/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.report.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.report.jooq.tables.interfaces.IIntelligenceStudySyncRecord;

import javax.annotation.Generated;


/**
 * 人工智能同步学习时长记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class IntelligenceStudySyncRecordEntity extends BaseEntity implements IIntelligenceStudySyncRecord {

    private static final long serialVersionUID = 1L;

    private String  serverIp;
    private String  param;
    private Integer result;
    private String  returnMsg;
    private Integer callSize;
    private Integer status;
    private Long    updateTime;

    public IntelligenceStudySyncRecordEntity() {}

    public IntelligenceStudySyncRecordEntity(IntelligenceStudySyncRecordEntity value) {
        this.serverIp = value.serverIp;
        this.param = value.param;
        this.result = value.result;
        this.returnMsg = value.returnMsg;
        this.callSize = value.callSize;
        this.status = value.status;
        this.updateTime = value.updateTime;
    }

    public IntelligenceStudySyncRecordEntity(
        String  id,
        String  serverIp,
        String  param,
        Integer result,
        String  returnMsg,
        Integer callSize,
        Integer status,
        Long    createTime,
        Long    updateTime
    ) {
        super.setId(id);
        this.serverIp = serverIp;
        this.param = param;
        this.result = result;
        this.returnMsg = returnMsg;
        this.callSize = callSize;
        this.status = status;
        super.setCreateTime(createTime);
        this.updateTime = updateTime;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getServerIp() {
        return this.serverIp;
    }

    @Override
    public void setServerIp(String serverIp) {
        this.serverIp = serverIp;
    }

    @Override
    public String getParam() {
        return this.param;
    }

    @Override
    public void setParam(String param) {
        this.param = param;
    }

    @Override
    public Integer getResult() {
        return this.result;
    }

    @Override
    public void setResult(Integer result) {
        this.result = result;
    }

    @Override
    public String getReturnMsg() {
        return this.returnMsg;
    }

    @Override
    public void setReturnMsg(String returnMsg) {
        this.returnMsg = returnMsg;
    }

    @Override
    public Integer getCallSize() {
        return this.callSize;
    }

    @Override
    public void setCallSize(Integer callSize) {
        this.callSize = callSize;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getUpdateTime() {
        return this.updateTime;
    }

    @Override
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("IntelligenceStudySyncRecordEntity (");

        sb.append(getId());
        sb.append(", ").append(serverIp);
        sb.append(", ").append(param);
        sb.append(", ").append(result);
        sb.append(", ").append(returnMsg);
        sb.append(", ").append(callSize);
        sb.append(", ").append(status);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(updateTime);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IIntelligenceStudySyncRecord from) {
        setId(from.getId());
        setServerIp(from.getServerIp());
        setParam(from.getParam());
        setResult(from.getResult());
        setReturnMsg(from.getReturnMsg());
        setCallSize(from.getCallSize());
        setStatus(from.getStatus());
        setCreateTime(from.getCreateTime());
        setUpdateTime(from.getUpdateTime());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IIntelligenceStudySyncRecord> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends IntelligenceStudySyncRecordEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.report.jooq.tables.records.IntelligenceStudySyncRecordRecord r = new com.zxy.product.report.jooq.tables.records.IntelligenceStudySyncRecordRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.ID, record.getValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.SERVER_IP.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.SERVER_IP, record.getValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.SERVER_IP));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.PARAM.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.PARAM, record.getValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.PARAM));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.RESULT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.RESULT, record.getValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.RESULT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.RETURN_MSG.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.RETURN_MSG, record.getValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.RETURN_MSG));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.CALL_SIZE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.CALL_SIZE, record.getValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.CALL_SIZE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.STATUS, record.getValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.CREATE_TIME, record.getValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.UPDATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.UPDATE_TIME, record.getValue(com.zxy.product.report.jooq.tables.IntelligenceStudySyncRecord.INTELLIGENCE_STUDY_SYNC_RECORD.UPDATE_TIME));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
