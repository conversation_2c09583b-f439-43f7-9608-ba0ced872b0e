package com.zxy.product.course.web.audit;

import java.lang.annotation.*;

@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
@Repeatable(ImAudits.class)
public @interface ImAudit {

    boolean before() default false;

    String module() default "";

    String subModule() default "";

    Action action();

    String fisrtAction() default "";

    String secondAction() default "";

    String desc();

    String[] params() default {};

    String[] keys() default {};

    String[] ids() default {};

    String[] jsons() default {};

    String businessType() default "";

    String businessValue() default "";

    enum Action {
        /**
         * 新增
         */
        INSERT(1),
        /**
         * 编辑
         */
        UPDATE(2),
        /**
         * 删除
         */
        DELETE(3),
        /**
         * 导出
         */
        EXPORT(4),
        /**
         * 导入
         */
        IMPORT(5),
        /**
         * 发布
         */
        PUBLISH(6),
        /**
         * 复制
         */
        COPY(7),
        /**
         * 管理
         */
        MANAGE(8),
        /**
         * 评卷
         */
        MARK_PAPER(9),
        /**
         * 取消发布
         */
        UNPUBLISH(10),
        /**
         * 审核
         */
        AUDIT(11),
        /**
         * 下载
         */
        DOWNLOAD(12),
        /**
         * 查询
         */
        FIND(13),

        /**
         * 预览,仅内部使用
         */
        PREVIEW(14)
        ;

        private final int code;

        Action(int code){
            this.code = code;
        }

        @Override
        public String toString() {
            return String.valueOf(this.code);
        }

    }
}
