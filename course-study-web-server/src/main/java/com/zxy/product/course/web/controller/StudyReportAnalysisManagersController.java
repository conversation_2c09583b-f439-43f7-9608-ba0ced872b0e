package com.zxy.product.course.web.controller;

import com.alibaba.fastjson.JSONException;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.StudyReportAnalysisManagersService;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.StudyReportAnalysisManagers;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

import static com.alibaba.fastjson.JSON.parseArray;

/**
 * 学情分析报告-管理员 控制器
 */
@Controller
@RequestMapping("/study-report-analysis")
public class StudyReportAnalysisManagersController {

    private StudyReportAnalysisManagersService service;

    @Autowired
    public void setService(StudyReportAnalysisManagersService service) {
        this.service = service;
    }

    @RequestMapping(method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("id, courseId, memberId, viewScope, createTime")
    @JSON("member.(id, name, fullName, status)")
    @JSON("member.organization.(id, name, companyOrganizationId, companyOrganizationName)")
    @JSON("member.companyOrganization.(id, name)")
    public StudyReportAnalysisManagers getById(RequestContext context) {
        return service.getById(context.getString("id"));
    }

    @RequestMapping(value = "/current", method = RequestMethod.GET)
    @Permitted
    @Param(name = "courseId", required = true)
    @Param(name = "memberId", required = true)
    @JSON("id, courseId, viewScope, createTime")
    @JSON("member.(id, name, fullName, status)")
    @JSON("member.organization.(id, name, companyOrganizationId, companyOrganizationName)")
    @JSON("member.companyOrganization.(id, name)")
    public StudyReportAnalysisManagers findByCourseIdAndMemberId(RequestContext context) {
        return service.findByCourseIdAndMemberId(context.getString("courseId"), context.getString("memberId")).orElse(new StudyReportAnalysisManagers());
    }

    @RequestMapping(value = "/collects", method = RequestMethod.GET)
    @Permitted
    @Param(name = "courseId", required = true)
    @Param(name = "memberName")
    @Param(name = "memberFullName")
    @JSON("id, courseId, viewScope, createTime")
    @JSON("member.(id, name, fullName, status)")
    @JSON("member.organization.(id, name, companyOrganizationId, companyOrganizationName)")
    @JSON("member.companyOrganization.(id, name)")
    public List<StudyReportAnalysisManagers> findByCourseId(RequestContext context) {
        return service.findByCourseId(
                context.getString("courseId"),
                context.getOptionalString("memberName"),
                context.getOptionalString("memberFullName")
        );
    }

    @RequestMapping(method = RequestMethod.POST)
    @Permitted
    @Param(name = "courseId", required = true)
    @Param(name = "memberId", required = true)
    @Param(name = "viewScope", required = true, type = Integer.class)
    @JSON("id")
    public StudyReportAnalysisManagers save(RequestContext context) {
        return service.save(
                context.getString("courseId"),
                context.getString("memberId"),
                context.getInteger("viewScope")
        );
    }

    @RequestMapping(method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "viewScope", required = true, type = Integer.class)
    @JSON("id, courseId, memberId, viewScope, createTime")
    public StudyReportAnalysisManagers update(RequestContext context) {
        StudyReportAnalysisManagers entity = service.getById(context.getString("id"));
        if(Objects.nonNull(entity)) {
            entity.setViewScope(context.getInteger("viewScope"));
            return service.update(entity);
        }
        return null;
    }

    @RequestMapping(method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "ids", required = true)
    @JSON("*")
    public int delete(RequestContext context) {
        return service.delete(Arrays.asList(context.getString("ids").split(",")));
    }

    @RequestMapping(value = "/changes", method = RequestMethod.POST)
    @Permitted
    @Param(name = "courseId", required = true)
    @Param(name = "studyReportAnalysisManagers", required = true)
    @JSON("id, courseId, memberId, viewScope, createTime")
    public List<StudyReportAnalysisManagers> saveOrUpdate(RequestContext context) {
        List<StudyReportAnalysisManagers> studyReportAnalysisManagers = parseStudyReportAnalysisManagers(context.getOptionalString("studyReportAnalysisManagers"));
        if(studyReportAnalysisManagers.isEmpty()) {
           return new ArrayList<>();
        }
        return service.saveOrUpdate(context.getString("courseId"), studyReportAnalysisManagers);
    }

    private List<StudyReportAnalysisManagers> parseStudyReportAnalysisManagers(Optional<String> managersJsonOptional) {
        return managersJsonOptional
                .map(managersJsonStr -> {
                    try {
                        return parseArray(managersJsonStr, StudyReportAnalysisManagers.class);
                    } catch (JSONException e) {
                        return new ArrayList<StudyReportAnalysisManagers>();
                    }
                })
                .orElse(new ArrayList<>());
    }

    @RequestMapping(value = "/verify", method = RequestMethod.GET)
    @Permitted
    @JSON("*")
    private Map<String, Object> permissionCheck(Subject<Member> subject) {
        return ImmutableMap.of("state", service.verifyManager(subject.getCurrentUserId()));
    }

    @RequestMapping(value = "/study-report-analysis", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")
    @JSON("recordCount")
    @JSON("items.(id, code, name, beginDate, endDate, cover, coverPath, releaseTime)")
    @Permitted
    private PagedResult<CourseInfo> findSubjectStudyReportAnalysisPage(RequestContext context, Subject<Member> subject) {
        return service.findSubjectStudyReportAnalysisPage(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptionalString("name"),
                Optional.ofNullable(subject.getCurrentUserId())
        );
    }


    @RequestMapping(value = "/basic", method = RequestMethod.GET)
    @Param(name = "courseId", required = true)
    @Param(name = "courseName")
    @Permitted
    @JSON("id, courseId, courseName, viewScope, createTime")
    @JSON("viewScopeOrganization.(id, name, parentId, depth)")
    @JSON("sponsoringOrganization.(id, name)")
    @JSON("member.(name, fullName)")
    @JSON("member.organization.(id, name)")
    private StudyReportAnalysisManagers getStudyReportAnalysisBasic(RequestContext context, Subject<Member> subject) {
        return service.getStudyReportAnalysisBasic(
                context.getString("courseId"),
                subject.getCurrentUserId()
        );
    }




}
