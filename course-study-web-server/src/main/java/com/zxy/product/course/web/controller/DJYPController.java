package com.zxy.product.course.web.controller;

import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.ThematicService;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.web.controller.party.Result;
import com.zxy.product.course.web.controller.party.ResultCodeEnum;
import com.zxy.product.exam.api.CertificateRecordService;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.examstu.api.ExamRecordService;
import com.zxy.product.human.api.MemberService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;
import org.springframework.util.DigestUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import sun.misc.BASE64Encoder;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * @Author: ligaoqiang
 * @Date: 2025/4/23 22:07
 * @Description: 党建云屏 - 三方调用
 */
@Controller
@RequestMapping("/djyp")
public class DJYPController implements EnvironmentAware {

    private static Logger logger = LoggerFactory.getLogger(DJYPController.class);

    private String secret;
    private String djypSubjectId;
    private String thematicId;
    private String djypExamId;

    private static final String AES_KEY = "uAEodLPmqUa2WACc";

    private ThematicService thematicService;

    private CourseInfoService courseInfoService;

    private CertificateRecordService certificateRecordService;

    private MemberService memberService;

    private com.zxy.product.examstu.api.ExamRecordService examRecordStuService;

    @Override
    public void setEnvironment(Environment environment) {
        secret = environment.getProperty("secret.key", "a19de0eb80013fb468fd2ee0cc672211");
        djypSubjectId = environment.getProperty("djyp.subject.id", "f3bcc57f-a3e4-4176-8505-e05cbca9a0fa");
        thematicId = environment.getProperty("djyp.thematic.id", "18d35a16-0d53-11ea-ae76-0050568d3015");
        djypExamId = environment.getProperty("djyp.exam.id", "bbfd30e9-2fe4-42b2-a67c-4715191194a4");
    }

    @Autowired
    public void setThematicService(ThematicService thematicService) {
        this.thematicService = thematicService;
    }

    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setCertificateRecordService(CertificateRecordService certificateRecordService) {
        this.certificateRecordService = certificateRecordService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setExamRecordStuService(ExamRecordService examRecordStuService) {
        this.examRecordStuService = examRecordStuService;
    }

    /**
     * 党建云屏调用，获取制定专题下的课程、考试信息
     * */
    @RequestMapping(value = "/info", method = RequestMethod.GET)
    @Param(name = "ihrCord", required = true, value = "ihr员工编码")
    @Param(name = "nonce", required = true, value = "随机字符串，对接方生成，每次调用不一样")
    @Param(name = "timestamp", required = true, value = "时间戳，例如1600157524461")
    @Param(name = "sign", required = true)
    @JSON("code,msg")
    @JSON("data.(courseSum, courseFinishNumber, courseProgressBar, oldCertificateNumber, oldCertificationTime," +
            "newCertificateNumber, newCertificationTime, dashBoard, dashBoardTwo, name, finishTimeMax, alreadyExamNumber," +
            "passOrNot,examPassTime)")
    @JSON("data.course.(channelId,channelTitle,channelOrder)")
    @JSON("data.course.list.(courseName,finishStatus,courseId,order)")
    public Result getDJYPInfo(RequestContext context) {
        // 1.验证签名
        if (!this.verify(context,  "ihrCord", "nonce", "timestamp")) {
            return Result.failure(ResultCodeEnum.SIGN_ERROR);
        }

        String ihrCord = context.getString("ihrCord");

        //根据ihrCode查询学员数据
        List<Member> member = thematicService.getMemberByIhrCode(ihrCord);

        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(member)){
            return Result.failure(ResultCodeEnum.PARAM_ERROR);
        }

        List<CourseChapter> courseChapters = courseInfoService.findCourseChapterByCourseId(djypSubjectId);
        //该专题下所有课程id
        List<String> allCourseIds = new ArrayList<>();
        //必修课程id
        List<String> requiredCourseIds = new ArrayList<>();
        //非必修课id
        List<String> noRequiredCourseIds = new ArrayList<>();

        courseChapters.parallelStream().flatMap(chapter -> chapter.getCourseChapterSections().stream())
                .forEach(section ->{
                    allCourseIds.add(section.getResourceId());
                    if (section.getRequired().equals(CourseChapterSection.IS_REQUIRED)){
                        requiredCourseIds.add(section.getResourceId());
                    }else {
                        noRequiredCourseIds.add(section.getResourceId());
                    }
                });

        Integer finishCount = 0;
        String memberId = member.get(0).getId();
        String fullName = member.get(0).getFullName();
        List<CourseStudyProgress> allCourseStudyProgresses = new ArrayList<>();

        //处理党建多账号 ，如果根据ihrCode查出来多个账号，则取finishCount最大(必修课完成数量最多)的账号
        for (int i = 0; i < member.size(); i++) {
            // 查询专题下所有课程, 该人员学了多少（courseStudyProgress下有数据）的课程
            List<CourseStudyProgress> courseStudyProgresses = thematicService.findCourseStudyProgress(memberId, allCourseIds);

            //过滤出来所学课程中已完成必修课的数量
            int currentFinishCount = courseStudyProgresses.stream().filter(courseStudyProgress -> requiredCourseIds.contains(courseStudyProgress.getCourseId()) &&
                    (courseStudyProgress.getFinishStatus().equals(CourseStudyProgress.FINISH_STATUS_FINISH) || courseStudyProgress.getFinishStatus().equals(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS))).mapToInt(csp -> 1).sum();

            //如果已完成必修课数量不为null,则和之前账号中的已完成必修课数量进行对比，取已完成必修课数量最大的账号
            if (!com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(courseStudyProgresses) && currentFinishCount > finishCount) {
                finishCount = currentFinishCount;
                memberId = member.get(i).getId();
                allCourseStudyProgresses = courseStudyProgresses;
            }
        }

        String courseProgressBar;
        String passOrNot = "未考试";

        //老党支部书记
        List<ThematicMember> thematicMember = thematicService.findMemberCertificate(memberId, thematicId);
        //新党支部书记
        List<com.zxy.product.exam.entity.CertificateRecord> memberCertificate = certificateRecordService.findMemberCertificate(memberId, djypExamId);
        Integer examRegion = memberService.findExamRegion(memberId);
        //获取该学员的考试状态
        List<com.zxy.product.exam.entity.ExamRecord> memberExamRecord = examRecordStuService.getMemberExamRecordScore(examRegion, memberId, djypExamId);

        // 该学员已经参加的考试次数 不包含考试中和未开始
        long hasExamNum = memberExamRecord.stream().filter(examRecord -> examRecord.getStatus() != null
                && examRecord.getStatus() != ExamRecord.STATUS_TO_BE_STARTED
                && examRecord.getStatus() != ExamRecord.STATUS_DOING).count();

        // 该学员考试是否通过
        boolean isPass = memberExamRecord.stream().map(com.zxy.product.exam.entity.ExamRecord::getStatus).collect(Collectors.toList())
                                              .contains(ExamRecord.STATUS_PASS);

        double examSum = 1;

        //获取老支部书记证书编号
        String oldCertificateNumber = null;
        Long oldCertificationTime = null;
        if(!com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(thematicMember)) {
            oldCertificateNumber = thematicMember.get(0).getQualifiedNumber();
            oldCertificationTime = thematicMember.get(0).getQualifiedTime();
        }

        double certificateNumber = 0;
        Long newCertificationTime = null;
        String newCertificateNumber = null;
        String examPassTime = null;

        //获取新支部书记证书编号
        if(!com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(memberCertificate)) {
            certificateNumber = 1;
            newCertificateNumber = memberCertificate.get(0).getNum();
            newCertificationTime = memberCertificate.get(0).getIssueTime();
        }
        if (!com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(memberCertificate)
                && memberCertificate.get(0) != null
                && memberCertificate.get(0).getPassTime() != null) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            examPassTime = sdf.format(new Date(memberCertificate.get(0).getPassTime()));
        }
        DecimalFormat df = new DecimalFormat("######0.00");
        Thematic thematic = new Thematic();

        double courseSum =finishCount;

        int requiredCourseNum = requiredCourseIds.size();

        // 计算新党支部书记仪表盘
        thematic.setDashBoard( calcPercentage(finishCount + certificateNumber, requiredCourseNum + examSum));
        // 计算老党支部书记仪表盘
        thematic.setDashBoardTwo( calcPercentage(finishCount + certificateNumber, courseSum));

        //最后一门课完成时间
        CourseStudyProgress finishTimeMax = new CourseStudyProgress();
        if (org.springframework.util.StringUtils.isEmpty(oldCertificateNumber)){
            finishTimeMax = allCourseStudyProgresses.stream().filter(courseStudyProgress->courseStudyProgress.getFinishTime() != null).max(Comparator.comparing(CourseStudyProgress::getFinishTime))
                                               .orElse(allCourseStudyProgresses.stream().filter(courseStudyProgress->courseStudyProgress.getLastAccessTime() != null).max(Comparator.comparing(CourseStudyProgress::getLastAccessTime))
                                                                          .orElse(new CourseStudyProgress()));
        }

        if (hasExamNum > 0) {
            passOrNot = isPass ? "及格" : "不及格";
        }

        List<Map<String, Object>> courseFinishStatusList = new ArrayList<>(allCourseIds.size());

        Map<String, Integer> studiedCourseStatusMap = allCourseStudyProgresses.stream()
                                                                              .collect(Collectors.toMap(
                                                                                      CourseStudyProgress::getCourseId,
                                                                                      CourseStudyProgress::getFinishStatus,
                                                                                      (existing, replacement) -> existing));


        List<Map<String, Object>> courseStructure = courseChapters.stream().map(chapter -> {
            Map<String, Object> chapterMap = new LinkedHashMap<>();
            // 模块id、模块名称、模块顺序
            chapterMap.put("channelId", chapter.getId());
            chapterMap.put("channelTitle", chapter.getName());
            chapterMap.put("channelOrder", chapter.getSequence());

            List<Map<String, Object>> sectionList = chapter.getCourseChapterSections().stream().map(section -> {

                Map<String, Object> sectionMap = new HashMap<>();
                // 课程id、课程名称、课程完成状态、课程顺序
                sectionMap.put("courseName", section.getName());
                sectionMap.put("courseId", section.getResourceId());
                sectionMap.put("finishStatus",
                              studiedCourseStatusMap.getOrDefault(
                                       section.getResourceId(),
                                       CourseStudyProgress.FINISH_STATUS_DEFAULT));
                sectionMap.put("order", section.getSequence());

                return sectionMap;

            }).collect(Collectors.toList());

            chapterMap.put("list", sectionList);

            return chapterMap;
        }).collect(Collectors.toList());

        Map<String, Object> map = new HashMap<>();
        map.put("course",courseStructure);
        courseProgressBar = requiredCourseIds.isEmpty() ?
                "0.00%" :
                df.format(finishCount * 100.0 / requiredCourseNum) + "%";
        //  课程总数 = 必修课总数 + 选修课总数
        map.put("courseSum", allCourseIds.size());
        //  课程完成数量 = 必修课完成数量
        map.put("courseFinishNumber", finishCount);
        //  课程进度条 = 已完成必修课数量为0 则为0。否则，课程进度条 = 已完成必修课数量 / 必修课总数
        map.put("courseProgressBar", finishCount == 0 ? (0 + "0%") : courseProgressBar);
        //  老党支部书记证书编号
        map.put("oldCertificateNumber", oldCertificateNumber);
        //  老党支部书记获证时间
        map.put("oldCertificationTime", oldCertificationTime);
        //  新党支部书记证书编号
        map.put("newCertificateNumber", newCertificateNumber);
        // 新党支部书记获证时间
        map.put("newCertificationTime", newCertificationTime);
        // 新党支部书记仪表盘
        map.put("dashBoard", thematic.getDashBoard());
        // 老党支部书记仪表盘
        map.put("dashBoardTwo", thematic.getDashBoardTwo());
        // 人员姓名
        map.put("name", getAesEncryptParam(Optional.ofNullable(fullName)));
        // 最后一门课程完成时间（如果没有课程完成，则返回最后一次访问时间，如果两者皆无则为null）
        map.put("finishTimeMax", finishTimeMax.getFinishTime() == null ? finishTimeMax.getLastAccessTime() : finishTimeMax.getFinishTime());
        // 该学员已考试次数
        map.put("alreadyExamNumber", hasExamNum);
        // 该学员的考试是否及格过
        map.put("passOrNot", passOrNot);
        // 考试通过时间
        map.put("examPassTime", examPassTime);

        return Result.success(map);
    }

    private String calcPercentage(double numerator, double denominator) {
        return denominator == 0 ? "0.00%" :
                new DecimalFormat("0.00%").format(numerator / denominator);
    }
    private boolean verify(RequestContext context, String... keys) {
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            sb.append(key).append("=").append(context.getString(key)).append("&");
        }
        sb.append("secret=").append(secret);
        String _sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes()).toUpperCase();
        return context.getString("sign").equals(_sign);
    }

    private String getAesEncryptParam(Optional<String> param) {
        if (param.isPresent()) {
            try {
                return aesEncrypt(param.get(), AES_KEY);
            } catch (Exception e) {
                logger.error("加密失败", e);
                return null;
            }
        }
        return null;
    }

    public static String aesEncrypt(String content, String encryptKey) throws Exception {
        String result = (new BASE64Encoder()).encode(aesEncryptToBytes(content, encryptKey));
        return result.replace("\n", "");

    }

    private static byte[] aesEncryptToBytes(String content, String encryptKey) throws Exception {
        KeyGenerator kgen = KeyGenerator.getInstance("AES");
        kgen.init(128);
        Cipher cipher = Cipher.getInstance("AES/ECB/PKCS5Padding");
        cipher.init(1, new SecretKeySpec(encryptKey.getBytes(), "AES"));
        return cipher.doFinal(content.getBytes(StandardCharsets.UTF_8));
    }
}
