package com.zxy.product.course.web.kit;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.Validator;
import com.zxy.common.office.excel.export.Sheet;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelSheet;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.office.excel.support.DefaultReader;
import com.zxy.common.office.excel.support.DefaultResult;
import com.zxy.common.office.excel.support.DefaultRow;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.product.course.api.subAuthenticated.SubAuthenticatedService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.SubAuthenticated;
import com.zxy.product.course.entity.SubAuthenticatedCertificateRecord;
import com.zxy.product.course.entity.SubAuthenticatedContentConfigure;
import com.zxy.product.course.entity.SubAuthenticatedDimension;
import com.zxy.product.course.entity.SubAuthenticatedMemberDimension;
import com.zxy.product.course.entity.SubAuthenticatedTmp;
import com.zxy.product.course.jooq.tables.pojos.MemberEntity;
import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedContentConfigureEntity;
import com.zxy.product.course.web.converter.MultipartFileConverter;
import com.zxy.product.course.web.util.BrowserUtil;
import com.zxy.product.course.web.util.ExcelCheckTileUtil;
import com.zxy.product.exam.api.AudienceObjectService;
import com.zxy.product.exam.api.CertificateRecordService;
import com.zxy.product.exam.api.StrongBaseService;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.jooq.tables.pojos.ExamEntity;
import com.zxy.product.human.api.ExportTaskService;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.entity.Attachment;
import com.zxy.product.human.entity.ExportTask;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * @Classname SubAuthenticatedController
 * @Description
 * @Date 2023/3/6
 * @Created by futzh
 */
@Component
public class SubAuthenticatedKit {
    private static final Logger LOGGER = LoggerFactory.getLogger(SubAuthenticatedKit.class);
    /**
     * 防止重复点击import 的key
     */
    public static final String IMPORT_REDIS_KEY_PREFIX = "safe-import";
    private static final String[] TITLE = {"姓名","员工编号"};
    @Resource
    private SubAuthenticatedService subAuthenticatedService;
    @Resource
    private ExportTaskService exportTaskService;
    @Resource
    private AttachmentResolver attachmentResolver;
    @Resource
    private FileService fileService;
    @Resource
    private Cache cache;
    @Resource
    private AudienceObjectService examAudienceObjectService;
    @Resource
    private StrongBaseService strongBaseService;
    @Resource
    private CertificateRecordService examCertificationRecordService;

    public List<SubAuthenticatedContentConfigure> parseContentConfigures(Optional<String> contentConfigures) {
        List<SubAuthenticatedContentConfigure> contentConfigureList = new ArrayList<>();
        if (!contentConfigures.isPresent())
            return contentConfigureList;
        String configures = contentConfigures.get();
        contentConfigureList = com.alibaba.fastjson.JSON.parseArray(configures, SubAuthenticatedContentConfigure.class);
        return contentConfigureList;
    }


    public List<SubAuthenticatedMemberDimension> checkRowAndInitData(DefaultResult errorResult, Reader.Row row, Map<String, Member> memberMap, List<String> titleCodes, Map<String, String> dimensionMapping, Map<String, String> permissionsMapping) {

        // 检查用户编码
        String memberCode = this.get(row, SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_CODE);
        if (!memberMap.containsKey(memberCode)) {
            DefaultRow defaultRow = ((DefaultRow) row);
            defaultRow. addError(new Validator.DataError(row.getIndex(), SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_CODE, ErrorCode.TheEmployeeNumberDoesNotExist));
            errorResult.addRow(defaultRow);
            return null;
        }
        // 检查当前被导入的用户组织是否在导入人的授权内
        if (!permissionsMapping.containsKey(memberMap.get(memberCode).getOrganizationId())) {
            DefaultRow defaultRow = ((DefaultRow) row);
            // 随便写的 column 值 因为不存在与模版中
            defaultRow.addError(new Validator.DataError(row.getIndex(), SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_CODE, ErrorCode.YouDoNotHavePermissionToImportResource));
            errorResult.addRow(defaultRow);
            return null;
        }
        String memberName = this.get(row, SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_NAME);
        if (Objects.nonNull(memberMap.get(memberCode)) && !Optional.ofNullable(memberMap.get(memberCode).getFullName()).orElse("").equals(memberName)) {
            DefaultRow defaultRow = ((DefaultRow) row);
            defaultRow.addError(new Validator.DataError(row.getIndex(), SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_NAME, ErrorCode.TheEmployeeNumberOrNameIsIncorrect));
            errorResult.addRow(defaultRow);
            return null;
        }

        return dimensionNameValidatorAndInitData(errorResult, row, memberMap.get(memberCode).getId(), titleCodes, dimensionMapping);


    }

    private List<SubAuthenticatedMemberDimension> dimensionNameValidatorAndInitData(DefaultResult errorResult, Reader.Row row, String memberId, List<String> titleCodes, Map<String, String> dimensionMapping) {
        List<SubAuthenticatedMemberDimension> result = new ArrayList<>();
        // 固定的最多10个维度 所以这里写死 因为2023年3月12的我 认为一个个写出来好蠢
        for (int i = 2; i < dimensionMapping.keySet().size() + 2; i++) {
            String value = this.get(row, i);
            boolean isNonNull = Objects.nonNull(value);
            if (isNonNull && !(value.equals("是") || value.equals("否"))) {
                DefaultRow defaultRow = ((DefaultRow) row);
                defaultRow.addError(new Validator.DataError(row.getIndex(), i, ErrorCode.TheAuthenticationDimensionStatusIsIncorrect));
                errorResult.addRow(defaultRow);
                return null;
            }
            if (isNonNull) {
                String dimensionId = dimensionMapping.get(titleCodes.get(i - 2));
                SubAuthenticatedMemberDimension subAuthenticatedMemberDimension = new SubAuthenticatedMemberDimension();
                subAuthenticatedMemberDimension.forInsert();
                subAuthenticatedMemberDimension.setDimensionId(dimensionId);
                subAuthenticatedMemberDimension.setMemberId(memberId);
                subAuthenticatedMemberDimension.setIsLighted(value.equals("是") ? SubAuthenticatedMemberDimension.IS_LIGHTED : SubAuthenticatedMemberDimension.NOT_LIGHTED);
                result.add(subAuthenticatedMemberDimension);
            }
        }
        return result;
    }


    public List<SubAuthenticatedMemberDimension> getSubAuthenticatedMemberDimensions(DefaultResult errorResult, Reader.Result result, Map<String, Member> memberMap, Map<String, String> dimensionMapping, Map<String, String> permissionsMapping) {
        // 获取员工编号和真实姓名
        List<String> titleCodes = initTitle(result, dimensionMapping);

        return result.map(row -> {
            if (row.getIndex() == 0) return null; // 跳过表头不处理

            return checkRowAndInitData(errorResult, row, memberMap, titleCodes, dimensionMapping,permissionsMapping);
        }).stream().filter(Objects::nonNull).flatMap(List::stream).collect(Collectors.toList());
    }

    private List<String> initTitle(Reader.Result result, Map<String, String> dimensionMapping) {
        Reader.Row titleRow = result.getCorrectRows().get(0);
        Set<String> keySet = dimensionMapping.keySet();
        List<String> codes = new LinkedList<>();
        for (int i = 2; i < keySet.size() + 2; i++) {
            String value = this.get(titleRow, i);
            if (Objects.isNull(value))
                break;
            String[] split = value.split("\\|");
            ErrorCode.EXCEL_ERROR.throwIf(split.length < 2);
            String dimensionCode = split[1].replace("\n点亮则填写“是”，不点亮则填写“否”，无需更新则不填写","");
            codes.add(dimensionCode);
        }
        ErrorCode.EXCEL_ERROR.throwIf(!keySet.containsAll(codes));
        return codes;
    }

    /*Reader.Row.get() 当index 不存在的时候会抛异常
     * 主动捕获异常
     * */
    private String get(Reader.Row row, int index) {
        String t = null;
        try {
            t = row.get(index, String.class);
        } catch (Exception ignore) {
        }
        return t;

    }

    public Reader getDimensionReaderInstance(int size) {
        DefaultReader defaultReader = new DefaultReader();
        defaultReader.setColumn(SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_NAME, String.class, (value, context, previous) -> true);
        defaultReader.setColumn(SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_CODE, String.class, (value, context, previous) -> true);
        for (int i = 2; i < size + 2; i++) {
            defaultReader.setColumn(i, String.class, (value, context, previous) -> true);
        }
        return defaultReader;
    }

    public Writer buildDimensionWriter(RequestContext context, List<SubAuthenticatedDimension> list) throws UnsupportedEncodingException {
        HttpServletResponse response = context.getResponse();
        String title = "维度模版";
        String attachmentName = title.concat(".xlsx");
        if (BrowserUtil.isMSBrowser(context.getRequest().getHeader("User-Agent"))) {
            attachmentName = URLEncoder.encode(attachmentName, "UTF-8");
        } else {
            attachmentName = new String(attachmentName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        }
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + attachmentName);
        Writer writer = new ExcelWriter();
        Sheet<Object> sheet = writer.sheet(title, new ArrayList<>());
        sheet.field("姓名", null);
        sheet.field("员工编号", null);
        list.sort(Comparator.comparingInt(SubAuthenticatedDimension::getOrder));
        for (SubAuthenticatedDimension item : list) {
            sheet.field(item.getName().concat("|").concat(item.getCode()).concat("\n点亮则填写“是”，不点亮则填写“否”，无需更新则不填写"), null);
        }
        return writer;
    }

    public List<SubAuthenticatedCertificateRecord> checkAndInitImportCertificate(DefaultResult errorResult, Reader.Result result, Map<String, Member> memberMap, String subAuthenticatedId, String currentUserId, Map<String, String> permissionsMapping) {
        List<Exam> examData = strongBaseService.findExamDataBySubId(subAuthenticatedId);
        Map<String, HashSet<String>> examAudienceMapping = initExamAudienceMapping(examData, memberMap);
        Map<String, HashSet<String>> examCertificateMapping = initExamCertificateMapping(examData, memberMap);
        List<String> existMemberCodes = new ArrayList<>();
        return result.map(row -> {
            // 检查用户编码
            String memberCode = row.get(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_CODE, String.class);
            if (existMemberCodes.contains(memberCode)){
                return null;
            }
            existMemberCodes.add(memberCode);
            if (!memberMap.containsKey(memberCode)) {
                DefaultRow defaultRow = (DefaultRow) row;
                defaultRow.addData(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_OTHER, ErrorCode.TheEmployeeNumberDoesNotExist.getDesc());
                errorResult.addRow(defaultRow);
                return null;
            }
            // 检查用户权限是否能导入当前条数据
            if (!permissionsMapping.containsKey(memberMap.get(memberCode).getOrganizationId())) {
                DefaultRow defaultRow = (DefaultRow) row;
                defaultRow.addData(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_OTHER, ErrorCode.YouDoNotHavePermissionToImportResource.getDesc());
                errorResult.addRow(defaultRow);
                return null;
            }
            // 检查用户名称

            String memberName = row.get(SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_NAME, String.class);
            if (Objects.nonNull(memberMap.get(memberCode)) && !Optional.ofNullable(memberMap.get(memberCode).getFullName()).orElse("").equals(memberName)) {
                DefaultRow defaultRow = (DefaultRow) row;
                defaultRow.addData(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_OTHER, ErrorCode.TheEmployeeNumberOrNameIsIncorrect.getDesc());

                errorResult.addRow(defaultRow);
                return null;
            }
            //  前提是有学习模块配置
            //  若新增/编辑子认证-填写信息 内“考培分离”选择“否”：则校验该员工在“学习管理”内的学习状态是否为“已完成”，若为未完成则该行错误提示：学习状态为未完成；若为空则错误提示：学习状态不存在
                SubAuthenticated subAuthenticated = subAuthenticatedService.findSubAuthenticated(subAuthenticatedId);
            boolean containStudyContent = subAuthenticated.getContentConfigureList().stream().map(SubAuthenticatedContentConfigureEntity::getContentType).collect(Collectors.toList()).contains(SubAuthenticatedContentConfigure.CONTENT_TYPE_STUDY);
            if (containStudyContent && SubAuthenticated.SPLIT_TRAINING_FLAG_NO.equals(subAuthenticated.getSplitTrainingFlag())) {
                Map<Integer, List<SubAuthenticatedContentConfigure>> progressDetail = Optional.ofNullable(subAuthenticatedService.findProgressDetail(subAuthenticatedId, memberMap.get(memberCode).getId())).orElse(new HashMap<>());
                DefaultRow defaultRow = (DefaultRow) row;
                Set<Integer> keySet = progressDetail.keySet();
                if (keySet.contains(0)) {
                    defaultRow.addData(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_OTHER, ErrorCode.TheLearningStatusDoesNotExist.getDesc());
                    errorResult.addRow(defaultRow);
                    return null;
                }
                if (keySet.contains(1)) {
                    defaultRow.addData(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_OTHER, ErrorCode.TheLearningStatusIsIncomplete.getDesc());
                    errorResult.addRow(defaultRow);
                    return null;
                }
            }
            Integer checkCertificateFlag = subAuthenticatedService.getCheckCertificateById(subAuthenticatedId);
            if (checkCertificateFlag.equals(SubAuthenticated.CHECK_CERTIFICATE_FLAG_YES)) {
                //  验证考试证书的发放完成状态   4.校验该员工在所有考试组内的所有在受众范围内的考试状态是否获得证书，若存在未获得证书的受众范围内的考试则该行错误提示：证书校验未通过；
                if (checkExamCertificate(examAudienceMapping, examCertificateMapping, memberMap.get(memberCode).getId(), row, errorResult)) {
                    return null;
                };
            }

            SubAuthenticatedCertificateRecord subAuthenticatedCertificateRecord = new SubAuthenticatedCertificateRecord();
            subAuthenticatedCertificateRecord.forInsert();
            subAuthenticatedCertificateRecord.setSubAuthenticatedId(subAuthenticatedId);
            subAuthenticatedCertificateRecord.setMemberId(memberMap.get(memberCode).getId());
            subAuthenticatedCertificateRecord.setOperatorId(currentUserId);
            subAuthenticatedCertificateRecord.setDeleteFlag(SubAuthenticatedCertificateRecord.NOT_DELETE);
            subAuthenticatedCertificateRecord.setImportTime(System.currentTimeMillis());
            subAuthenticatedCertificateRecord.setStatus(SubAuthenticatedCertificateRecord.STATUS_NO);
            return subAuthenticatedCertificateRecord;
        });
    }


    private boolean checkExamCertificate(Map<String, HashSet<String>> examAudienceMap, Map<String, HashSet<String>> examCertificateMapping, String memberId, Reader.Row row, DefaultResult errorResult) {

        boolean flag = Objects.isNull(examAudienceMap.get(memberId)) || examAudienceMap.get(memberId).isEmpty();
        // 没有受众的直接跳出 未完成所有的课程的也跳出
        if (Objects.isNull(examCertificateMapping.get(memberId)) || examCertificateMapping.get(memberId).isEmpty() || Objects.isNull(examAudienceMap.get(memberId)) ||examCertificateMapping.get(memberId).size() != examAudienceMap.get(memberId).size()){
            flag = true;
        }
        if (flag){
            DefaultRow defaultRow = (DefaultRow) row;
            defaultRow.addData(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_OTHER, ErrorCode.CertificateValidationFailed.getDesc());
            errorResult.addRow(defaultRow);
            return true;
        }
        return false;
    }

    private Map<String, HashSet<String>> initExamAudienceMapping(List<Exam> examData, Map<String, Member> memberMap) {
        Map<String, HashSet<String>> examAudienceMap = new HashMap<>();
        List<String> memberIds = memberMap.values().stream().map(MemberEntity::getId).collect(Collectors.toList());
        for (Exam exam : examData) {
            String examId = exam.getId();
            Map<String, String> audientMapCache = examAudienceObjectService.createAudientMapCache(examId, memberIds);
            audientMapCache.keySet().forEach(memberId -> {
                examAudienceMap.computeIfAbsent(memberId, k -> new HashSet<>()).add(examId);
            });
        }
        return examAudienceMap;
    }

    private Map<String, HashSet<String>> initExamCertificateMapping(List<Exam> examData, Map<String, Member> memberMap) {
        Map<String, HashSet<String>> examCertificationMap = new HashMap<>();
        Map<String, Object> examIdMap = examData.stream().collect(Collectors.toMap(ExamEntity::getId, v -> false));
        List<String> memberIds = memberMap.values().stream().map(MemberEntity::getId).collect(Collectors.toList());
        examCertificationRecordService.findByMemberIds(memberIds).stream().filter(x -> examIdMap.containsKey(x.getExamId()))
                .forEach(
                        record -> examCertificationMap.computeIfAbsent(record.getMemberId(), k -> new HashSet<>()).add(record.getExamId())
                );

        return examCertificationMap;
    }

    public Attachment certificateErrorWriter(DefaultResult errorResult, String currentUserId) throws IOException {
        ExcelWriter excelWriter = new ExcelWriter();
        if (errorResult.getCorrectRows().isEmpty()) {
            return null;
        }
        excelWriter.sheet("证书导入错误数据", errorResult.getCorrectRows())
                .field("姓名", x -> x.get(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_NAME, String.class))
                .field("员工编号", x -> x.get(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_CODE, String.class))
                .field("失败原因", x -> x.get(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_OTHER, String.class));
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        excelWriter.write(out);
        return uploadTempFile(new ByteArrayInputStream(out.toByteArray()),"证书导入错误数据.xlsx");
    }

    public Attachment dimensionErrorWriter(DefaultResult errorResult, List<SubAuthenticatedDimension> dimensionMapping) throws IOException {
        ExcelWriter excelWriter = new ExcelWriter();
        if (errorResult.getErrors().isEmpty()) {
            return new Attachment();
        }

        ExcelSheet<Reader.Row> sheet = excelWriter.sheet("维度导入错误数据", errorResult.getErrorRows());
        sheet.field("姓名", x -> x.get(SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_NAME, String.class))
                .field("员工编号", x -> x.get(SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_CODE, String.class));
        for (int i = 0; i < dimensionMapping.size(); i++) {
            int finalI = i;
            SubAuthenticatedDimension dimension = dimensionMapping.get(i);
            sheet.field(dimension.getName().concat("|").concat(dimension.getCode()), x -> x.get(finalI + 2, String.class));
        }
   /*     sheet.field("失败原因", x -> {
            ErrorCode code = ((ErrorCode) x.getErrors().get(0).getCode());
            return code.getDesc();
        });*/
        ByteArrayOutputStream out = new ByteArrayOutputStream();
        excelWriter.write(out);
        return uploadTempFile(new ByteArrayInputStream(out.toByteArray()),"维度导入错误数据.xlsx");
    }

    /**
     * 输出流转文件，再转输入流上传到FASTDFS
     *
     * @return
     * @throws IOException
     */
    private Attachment uploadTempFile(InputStream is,String fileName) throws IOException {
        MultipartFileConverter multipartFile = new MultipartFileConverter(fileName, is);
        com.zxy.common.restful.multipart.Attachment minAttachment = attachmentResolver.store(null, multipartFile, Optional.empty());
        return  fileService.insert(new String[] { minAttachment.getFilename() }, new String[] { minAttachment.getContentType() }, new String[] { minAttachment.getExtension() }, new String[] { minAttachment.getPath() }, new Long[] { minAttachment.getSize() }).get(0);
    }


    public void handleCertificateTmp(String subAuthenticatedId, String fileId, String currentUserId) {
        Optional<Attachment> excelAttachment = fileService.get(fileId);
        if (!excelAttachment.isPresent()) {
            throw new UnprocessableException(() -> 0, "文件不存在");
        }
        com.zxy.common.restful.multipart.Attachment multipart = new com.zxy.common.restful.multipart.Attachment();
        BeanUtils.copyProperties(excelAttachment.get(), multipart);

        if (!(multipart.getExtension().endsWith("xls") || multipart.getExtension().endsWith("xlsx"))) {
            throw new UnprocessableException(() -> 0, "文件后缀不正确");
        }
        Reader.Result result;
        ExcelCheckTileUtil.checkTile(attachmentResolver.resolveToRead(multipart), TITLE);
        try (InputStream is = attachmentResolver.resolveToRead(multipart)) {
            result = new DefaultReader()
                    .skipRows(1)
                    .setColumn(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_NAME, String.class, (value, c, previous) -> true)
                    .setColumn(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_CODE, String.class, (value, c, previous) -> true)
                    .read(is);
            if (!result.isCellMatched()) { // 那么就是模板不匹配
                throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
            }
            if(Objects.equals(result.getErrorRows().size(),0) && Objects.equals(result.getCorrectRows().size(),0)){
                throw new UnprocessableException(ErrorCode.ImportNullFile);
            }
            List<SubAuthenticatedTmp> members = new ArrayList<>();
            result.getCorrectRows().forEach(item->{
                SubAuthenticatedTmp dto = new SubAuthenticatedTmp();
                dto.forInsert();
                dto.setCode(item.get(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_CODE, String.class));
                dto.setName(item.get(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_NAME, String.class));
                dto.setDeleteFlag(SubAuthenticatedTmp.DELETE_FLAG_NO);
                dto.setCreator(currentUserId);
                dto.setFileId(fileId);
                dto.setSubAuthenticatedId(subAuthenticatedId);
                members.add(dto);
            });
            if(CollectionUtils.isNotEmpty(members)){
                subAuthenticatedService.addTmp(members);
            }
        } catch (IOException e) {
            LOGGER.warn("导入错误：message = {}", e.getMessage());
            throw new UnprocessableException(ErrorCode.EXCEL_NOTSUPPORT);
        }
    }

    @Async
    public void handleCertificateImport(String subAuthenticatedId, String fileId, String currentUserId, Map<String, String> permissionsMapping) throws IOException {
        Optional<Attachment> excelAttachment = fileService.get(fileId);
        if (!excelAttachment.isPresent()) {
            throw new UnprocessableException(() -> 0, "文件不存在");
        }
        com.zxy.common.restful.multipart.Attachment multipart = new com.zxy.common.restful.multipart.Attachment();
        BeanUtils.copyProperties(excelAttachment.get(), multipart);

        if (!(multipart.getExtension().endsWith("xls") || multipart.getExtension().endsWith("xlsx"))) {
            throw new UnprocessableException(() -> 0, "文件后缀不正确");

        }
        Reader.Result result;
        try (InputStream is = attachmentResolver.resolveToRead(multipart)) {
            result = new DefaultReader()
                    .skipRows(1)
                    .setColumn(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_NAME, String.class, (value, c, previous) -> true)
                    .setColumn(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_CODE, String.class, (value, c, previous) -> true)
                    .read(is);

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        List<String> memberCodes = result.getCorrectRows()
                .stream()
                .map(x -> x.get(SubAuthenticatedCertificateRecord.TEMPLATE_MEMBER_CODE, String.class))
                .filter(Objects::nonNull)
                .map(String::trim)
                .collect(Collectors.toList());

        Map<String, Member> memberMap = subAuthenticatedService.getMemberCodeNameMap(memberCodes);

        DefaultResult errorResult = new DefaultResult();
        ExportTask exportTask = new ExportTask();
        try {
            ExportTask importTask = new ExportTask();
            importTask.setBusinessType(ExportTask.BUSINESS_TYPE_SUB_AUTHENTICATED_CERTIFICATE_IMPORT);
            importTask.setAttachmentId(fileId);
            importTask.setMemberId(currentUserId);
            importTask.setDataLen(0);
            JSONObject extraParam = new JSONObject();
            extraParam.put("subAuthenticatedId",subAuthenticatedId);
            importTask.setExtraParam(extraParam.toJSONString());
            ExportTask it = exportTaskService.insertImport(importTask);
            exportTask = exportTaskService.get(it.getId());

            List<SubAuthenticatedCertificateRecord> subAuthenticatedCertificateRecords = this.checkAndInitImportCertificate(errorResult, result, memberMap, subAuthenticatedId, currentUserId,permissionsMapping);
            subAuthenticatedCertificateRecords = subAuthenticatedCertificateRecords.stream().filter(Objects::nonNull).collect(Collectors.toList());
            subAuthenticatedService.saveSubAuthenticatedCertificateRecord(subAuthenticatedCertificateRecords);

            // 返回解析失败的excel 数据
            Attachment errAttachment = this.certificateErrorWriter(errorResult, currentUserId);
            exportTask.setStatus(ExportTask.StatusQueue.complete.getCode());
            if (Objects.nonNull(errAttachment)) {
                exportTask.setAttachmentId(errAttachment.getId());
                exportTask.setFilePath(errAttachment.getPath());
                exportTask.setFailCount(errorResult.getCorrectRows().size());
                exportTask.setStatus(ExportTask.StatusQueue.section_complete.getCode());
            }
            exportTask.setSuccessCount(subAuthenticatedCertificateRecords.size());
            if (exportTask.getSuccessCount()==0){
                exportTask.setStatus(ExportTask.StatusQueue.fail.getCode());
            }
        } catch (Exception exception) {
            exportTask.setStatus(ExportTask.StatusQueue.fail.getCode());
            Arrays.asList(exception.getStackTrace()).forEach(e-> LOGGER.error(e.toString()));
        } finally {
            // 成功的不写异步文件
            exportTaskService.update(exportTask);
            cache.clear(IMPORT_REDIS_KEY_PREFIX + currentUserId);
        }
    }
}
