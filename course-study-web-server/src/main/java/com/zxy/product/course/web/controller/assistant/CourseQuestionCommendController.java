package com.zxy.product.course.web.controller.assistant;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.course.CourseQuestionCommendService;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.entity.CourseQuestionRecommend;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.web.util.BrowserUtil;
import com.zxy.product.course.web.util.DateUtil;
import com.zxy.product.course.web.util.ImportExcelUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/18:01
 * @Description:
 */
@Controller
@RequestMapping("/recommend")
public class CourseQuestionCommendController {

    private static final Logger log = LoggerFactory.getLogger(CourseQuestionCommendController.class);
    private CourseQuestionCommendService courseQuestionCommendService;


    @Autowired
    public void setCourseQuestionCommendService(CourseQuestionCommendService courseQuestionCommendService) {
        this.courseQuestionCommendService = courseQuestionCommendService;
    }


    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "question", required = true)
    @Param(name = "answer", required = true)
    @Param(name = "answerText", required = true)
    @Param(name = "recommend",type = Integer.class, required = true)
    @Permitted
    @JSON("*.*")
    public Map<String, Object> add(RequestContext context){
        courseQuestionCommendService.insert(setData(context, Optional.empty()));
        return ImmutableMap.of("result", "success");
    }

    private CourseQuestionRecommend setData(RequestContext context, Optional<String> id){
        CourseQuestionRecommend recommend = new CourseQuestionRecommend();
        if(id.isPresent()){
            recommend = courseQuestionCommendService.get(id.get());
        }else {
            recommend.forInsert();
            recommend.setStatus(CommonConstant.ZERO);
        }
        recommend.setModifyDate(System.currentTimeMillis());
        recommend.setQuestion(context.getString("question"));
        recommend.setAnswer(context.getString("answer"));
        recommend.setAnswerText(context.getString("answerText"));
        recommend.setRecommend(context.getInteger("recommend"));
        return recommend;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "question", required = true)
    @Param(name = "answer", required = true)
    @Param(name = "answerText", required = true)
    @Param(name = "recommend",type = Integer.class, required = true)
    @Permitted
    @JSON("*.*")
    public Map<String, Object> update(RequestContext context){
        CourseQuestionRecommend recommend = setData(context, context.getOptionalString("id"));
        courseQuestionCommendService.update(recommend);
        return ImmutableMap.of("result", "success");
    }


    @RequestMapping(value = "/sort/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "sort", required = true,type = Integer.class)
    @Permitted
    @JSON("*.*")
    public Map<String, Object> sort(RequestContext context){
        courseQuestionCommendService.sort( context.getString("id"), context.getInteger("sort"));
        return ImmutableMap.of("result", "success");
    }


    @RequestMapping(value = "/updateCommend/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "recommend",type = Integer.class,required = true)
    @Permitted
    @JSON("*.*")
    public Map<String, Object> updateCommend(RequestContext context){
        courseQuestionCommendService.updateRecommend( context.getString("id"), context.getInteger("recommend"));
        return ImmutableMap.of("result", "success");
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("id,question,answer,recommend,sort,modifyDate")
    public CourseQuestionRecommend getDetail(RequestContext context){
        return courseQuestionCommendService.get(context.getString("id"));
    }

    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "page",type = Integer.class,required = true)
    @Param(name = "pageSize",type = Integer.class,required = true)
    @Param(name = "question")
    @Param(name = "recommend",type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,question,answer,recommend,sort,modifyDate)")
    public PagedResult<CourseQuestionRecommend> findPage(RequestContext context){
        return courseQuestionCommendService.findPage(context.getInteger("page"),context.getInteger("pageSize"),
                context.getOptionalString("question"), context.getOptionalInteger("recommend"));
    }


    @RequestMapping(value = "/export",method = RequestMethod.GET)
    @Param(name = "question")
    @Param(name = "recommend",type = Integer.class)
    @Permitted
    @JSON("recordCount")
    @JSON("items.(id,question,answer,recommend,sort,modifyDate)")
    public void export(RequestContext context, Subject<Member> subject) throws Exception {
        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        String filename = "推荐问题";
        if (BrowserUtil.isMSBrowser(context.getRequest().getHeader("User-Agent"))) {
            filename = URLEncoder.encode(filename, "UTF-8");
        } else {
            filename = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
        }
        response.setHeader("Content-Disposition", "attachment;filename=" + filename + ".xlsx");
        List<CourseQuestionRecommend> items = new ArrayList<>();
        List<CourseQuestionRecommend> recommends = new ArrayList<>();
        int page = 1;
        do {
            PagedResult<CourseQuestionRecommend> result = courseQuestionCommendService.findPage(page, CommonConstant.TWO_HUNDRED,
                    context.getOptionalString("question"), context.getOptionalInteger("recommend"));
            if(result.getRecordCount() > 0){
                items = result.getItems();
                recommends.addAll(items);
            }
            page ++;
        }while (Objects.equals(CommonConstant.TWO_HUNDRED, items.size()));
        Writer writer = new ExcelWriter();
        writer.sheet("推荐问题列表", recommends)
                .indexColumn(Optional.of("序号"))
                .field("问题内容", CourseQuestionRecommend::getQuestion)
                .field("问题答案", CourseQuestionRecommend::getAnswerText)
                .field("是否为推荐问题", recommend-> CourseQuestionRecommend.getRecommendStr(recommend.getRecommend()))
                .field("更新时间", time -> DateUtil.dateLongToString(time.getModifyDate(), DateUtil.YYYY_MM_DD_HH_MM));
        String date = DateUtil.dateLongToString(System.currentTimeMillis(), DateUtil.YYYY_MM_DD);
        String content = (subject.getCurrentUser().getFullName() + "\n" + date);
        writer.write(response.getOutputStream(), workBook -> {
            try {
                ImportExcelUtil.addWatermark(workBook, workBook.getSheetAt(0), content);
            } catch (IOException e) {
                log.warn("水印打印出错 message={}", e.getMessage());
                throw new RuntimeException(e);
            }
        });
    }

}
