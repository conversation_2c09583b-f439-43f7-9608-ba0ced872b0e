package com.zxy.product.course.web.controller.home;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.BusinessEmergencyService;
import com.zxy.product.course.api.course.CourseInfoFrontService;
import com.zxy.product.course.api.home.CoursePolymerService;
import com.zxy.product.course.content.ResourceEnum;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.vo.*;
import com.zxy.product.course.web.config.SwitchConfig;
import com.zxy.product.course.web.controller.CourseInfoController;
import com.zxy.product.course.web.util.DateUtil;
import com.zxy.product.system.api.home.HomeSystemService;
import com.zxy.product.system.domain.vo.CustomizeVO;
import com.zxy.product.system.entity.HomeContentTree;
import com.zxy.product.system.entity.InternalSwitch;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zxy.product.course.content.ClientTypeEnum.PC;
import static com.zxy.product.course.content.ResourceEnum.*;
import static com.zxy.product.course.web.util.SecurePathCdnUtils.generateSecurePathCdn;
import static com.zxy.product.system.content.SwitchEnum.HotContent;
import static com.zxy.product.system.content.SwitchEnum.StudyCard;

/**
 * 十万——首页课程模块聚合控制器
 * <AUTHOR>
 * @date 2024年11月11日 15:30
 */
@Controller
@RequestMapping("/course-polymer")
public class CoursePolymerController {
    private static final Logger logger= LoggerFactory.getLogger(CoursePolymerController.class);
    private final  static String  DATA_KEY ="data";
    private final  static String  HOT_CONTENT ="hotContent";
    private Cache cache;
    private Cache hotContentCache;
    private HomeSystemService homeSystemService;
    private CourseInfoFrontService courseInfoFrontService;
    private CoursePolymerService coursePolymerService;
    private CourseInfoController courseInfoController;
    private Cache internalSwitchCache;
    private BusinessEmergencyService businessEmergencyService;

    @Autowired
    public void setCache(CacheService cache) { this.internalSwitchCache = cache.create("anti-corruption", InternalSwitch.KEY); }

    @Autowired
    public void setCache(Cache cache){this.cache=cache;}

    @Autowired
    public void setHotContentCache(CacheService cacheService){
        this.hotContentCache=cacheService.create("course-study-service", "course_study");
    }

    @Autowired
    private void setCourseInfoController(CourseInfoController courseInfoController){
        this.courseInfoController=courseInfoController;
    }

    @Autowired
    public void setCourseInfoFrontService(CourseInfoFrontService courseInfoFrontService){
        this.courseInfoFrontService=courseInfoFrontService;
    }

    @Autowired
    public void setCoursePolymerService(CoursePolymerService coursePolymerService){
        this.coursePolymerService=coursePolymerService;
    }

    @Autowired
    public void setHomeSystemService(HomeSystemService homeSystemService){
        this.homeSystemService=homeSystemService;
    }

    @Autowired
    public void setBusinessEmergencyService(BusinessEmergencyService businessEmergencyService) {
        this.businessEmergencyService = businessEmergencyService;
    }

    /**
     * 1班级 2直播 3考试 4调研 5mooc 6直播 7未知 8未知 9课程 10专题 11图片
     * 首页：聚合学习模块非配置接口
     * @param context 请求上下文
     * @param subject 用户容器
     * @return 学习聚合接口
     */
    @JSON("*.*.*")
    @JSON("studyCardMap.unfinished.(id,name)")
    @JSON("hotContentCollect.(*)")
    @Param(name = "clientType",required = true,type = Integer.class)
    @Param(name = "configId",required = true)
    @Param(name = "size",required = true,type = Integer.class)
    @Param(name = "businessType",required = true)
    @Param(name = "hotContentSize",required = true,type = Integer.class)
    @RequestMapping(value = "/do-polymer-course",method = RequestMethod.POST)
    public HomeCourseVO doPolymerCourse(RequestContext context, Subject<Member> subject){
        String memberId = subject.getCurrentUserId();
        Integer size = context.getInteger("size");
        String cfgId = context.getString("configId");
        Integer clientType = context.getInteger("clientType");
        String businessType = context.getString("businessType");
        Integer hotContentSize = context.getInteger("hotContentSize");
        HomeCourseVO homeCourseVO = new HomeCourseVO();
        Map<String, List<? extends CustomizeParentVO>> customizeMap = this.doCustomizeSelect(cfgId,clientType);
        Map<String, ClassifiedCourseVo> map = this.doClassifiedCoursesSelect(cfgId);
        homeCourseVO.setClassifiedCourses(map);
        homeCourseVO.setCourseCustomizeMap(customizeMap);
        Map<String, List<StudyCardVO>> studyCardMap = this.convertStudyCard(memberId,size);
        homeCourseVO.setStudyCardMap(studyCardMap);
        List<HotContentParentVO> contentParentCollect = this.hotContentSelect(hotContentSize, clientType, businessType);
        homeCourseVO.setHotContentCollect(contentParentCollect);
        return homeCourseVO;
    }

    /**
     * 分类课程
     */
    private Map<String, ClassifiedCourseVo> doClassifiedCoursesSelect(String cfgId) {
        String cacheKey = cfgId + "ClassCourses";
        String string = cache.get(cacheKey, () -> homeSystemService.doClassifiedCourses(cfgId), 5 * 60);
        if (StringUtils.isEmpty(string)){
            return Collections.emptyMap();
        }
        List<String> homeContentTrees = homeSystemService.specialClassifiedCoursesSelect(string);
        if (CollectionUtils.isEmpty(homeContentTrees)) {
            return Collections.emptyMap();
        }
        List<String> ids = homeContentTrees.stream().distinct().collect(Collectors.toList());

        List<ClassifiedCourseVo> ClassifiedCourseVos = coursePolymerService.doClassifiedCoursesCourse(new ArrayList<>(ids));
        Map<String, ClassifiedCourseVo> idToCourseInfoMap = ClassifiedCourseVos.stream()
                .collect(Collectors.toMap(ClassifiedCourseVo::getId, Function.identity(), (a, b) -> a));
        return homeContentTrees.stream()
                .filter(idToCourseInfoMap::containsKey)
                .collect(Collectors.toMap(Function.identity(), idToCourseInfoMap::get));
    }


    /**
     * 首页：学习卡片
     * @param memberId 当前登录用户Id
     * @param size 数据长度
     * @return 转化后学习卡片返回值
     */
    private Map<String,List<StudyCardVO>> convertStudyCard(String memberId,Integer size){
        Map<String,List<StudyCardVO>> studyCardMap=new HashMap<>(4);
        boolean flag = SwitchConfig.getSwitchStatus(StudyCard);
        if(!flag){
            Map<String, List<CourseInfo>> recentMap = courseInfoFrontService.getRecentStudyCourses(Collections.singletonList(memberId), size);
            for (Map.Entry<String, List<CourseInfo>> entry : recentMap.entrySet()){
                Optional.of(entry)
                        .filter(ew1->Objects.equals(ew1.getKey(),"unfinished"))
                        .ifPresent(ew2->{
                            List<StudyCardVO> studyCardCollect = ew2.getValue().parallelStream()
                                    .map(ew1 -> {
                                        StudyCardVO studyCardVO = new StudyCardVO();
                                        studyCardVO.setId(ew1.getId());
                                        studyCardVO.setName(ew1.getName());
                                        studyCardVO.setCoverPath(generateSecurePathCdn(ew1.getCoverPath()));
                                        return studyCardVO;
                                    }).collect(Collectors.toList());
                            studyCardMap.put(ew2.getKey(),studyCardCollect);
                        });
            }
        }
        return studyCardMap;
    }

    /**
     * 1班级 2直播 3考试 4调研 5mooc 6直播 7未知 8未知 9课程 10专题 11图片
     * 执行自定义查询，构建Map数据
     * @param configId 首页主配置Id
     * @param clientType 客户端类型 I PC 2 App
     * @return 自定义模块填充数据Map（find-by-ids的接口返回值）
     */
    private Map<String, List<? extends CustomizeParentVO>> doCustomizeSelect(String configId, Integer clientType){
        String cacheKey=configId+clientType+"coursePolymer";
        Map<String, List<CustomizeVO>> customizeMap = cache.get(cacheKey, () -> homeSystemService.customizeCourseSelect(configId, clientType), 5 * 60);
        Map<String, List<? extends CustomizeParentVO>> polymerMap=new HashMap<>(3);
        for (Map.Entry<String, List<CustomizeVO>> entry : customizeMap.entrySet()){
            logger.info("当前自定义模块数据key{},当前自定义模块数据Value{}",JSONObject.toJSONString(entry.getKey()),JSONObject.toJSONString(entry.getValue()));
            List<CustomizeParentVO> polymerCfgCollect= Lists.newArrayList();
            Map<String, SpecialVO> courseMap = entry.getValue()
                    .parallelStream()
                    .filter(ew1 -> Special.getCode().equals(ew1.getDataType()) || Course.getCode().equals(ew1.getDataType()))
                    .collect(Collectors.toMap(CustomizeVO::getDataId, this::convertSpecialVO));
            Optional.of(courseMap.keySet())
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(ew1->polymerCfgCollect.addAll(coursePolymerService.doCustomizeCourse(courseMap, clientType)));
            Map<String, SpecialVO> broadcastMap = entry.getValue()
                    .parallelStream()
                    .filter(ew1 -> LiveBroadcast2.getCode().equals(ew1.getDataType()) || LiveBroadcast6.getCode().equals(ew1.getDataType()))
                    .collect(Collectors.toMap(CustomizeVO::getDataId, this::convertSpecialVO));
            Optional.of(broadcastMap.keySet())
                    .filter(CollectionUtils::isNotEmpty)
                    .ifPresent(ew1-> polymerCfgCollect.addAll(coursePolymerService.doCustomizeBroadcast(broadcastMap,clientType)));
            this.specialPaddingImg(entry.getValue(),polymerCfgCollect,clientType);
            String key=entry.getKey().split(":")[1];
            polymerMap.put(key,polymerCfgCollect);
        }
        return polymerMap;
    }

    /**转化回显前端的特殊字段处理VO*/
    private SpecialVO convertSpecialVO(CustomizeVO customizeVO){
        SpecialVO specialVO = new SpecialVO();
        specialVO.setId(customizeVO.getDataId());
        specialVO.setImgPath(customizeVO.getUrl());
        return specialVO;
    }

    /**
     * 填充图片资源数据
     * @param customizeCollect 首页自定义模块填充的原始数据集合
     * @param polymerCfgCollect 聚合集合
     * @param clientType 客户端类型 1 PC 2 App
     */
    private void specialPaddingImg(List<CustomizeVO> customizeCollect,
                                                                List<CustomizeParentVO> polymerCfgCollect,Integer clientType){
        List<CustomizeVO> specialCollect = customizeCollect.parallelStream()
                .filter(ew1 -> ResourceEnum.Picture.getCode().equals(ew1.getDataType()))
                .collect(Collectors.toList());
        List<CustomizeParentVO> imgCollect = specialCollect.parallelStream()
                .map(ew1 -> this.doBuildParentVO(ew1, clientType))
                .collect(Collectors.toList());
        polymerCfgCollect.addAll(imgCollect);
    }

   /**根据客户端转化回显VO*/
    private CustomizeParentVO doBuildParentVO(CustomizeVO customizeVO,Integer clientType){
        return Optional.of(clientType)
                .filter(ew1->Objects.equals(PC.getCode(),ew1))
                .map(ew2->{
                    CustomizePcVO customizePcVO = new CustomizePcVO();
                    customizePcVO.setId(customizeVO.getDataId());
                    customizePcVO.setImagePath(customizeVO.getUrl());
                    return (CustomizeParentVO)customizePcVO;
                }).orElseGet(()->{
                    CustomizeAppVO customizeAppVO = new CustomizeAppVO();
                    customizeAppVO.setId(customizeVO.getDataId());
                    customizeAppVO.setCoverPath(customizeVO.getUrl());
                    return customizeAppVO;
                });
    }

    private List<HotContentParentVO> hotContentSelect(Integer hotContentSize, Integer clientType, String businessType){
        boolean flag = SwitchConfig.getSwitchStatus(HotContent);
        logger.info("获取flag数据{}",JSONObject.toJSONString(flag));
        return Optional.of(flag)
                .filter(ew1-> ew1)
                .map(ew1->hotContent(businessType,hotContentSize,clientType))
                .orElseGet(()->doHotContentSelect(clientType,businessType));
    }


    /**
     * 首页：热门内容
     * @param clientType 客户端类型1PC 2App
     * @param businessType 0课程 2专题
     * @return 热门内容信息
     */
    private List<HotContentParentVO> doHotContentSelect(Integer clientType, String businessType) {
        logger.info("开关关闭，进入正常业务逻辑分支");
        List<HotContentParentVO> parentCollect=Lists.newArrayList();
        String rootOrgId = "1";
        List<String> businessTypeCollect = Arrays.stream(businessType.split(",")).collect(Collectors.toList());
        businessTypeCollect.forEach(ew1->{
            String cacheKey = "hot-visits-key-" + rootOrgId + "-" + ew1 + "-" + clientType;
            List<HotContentParentVO> hotContentParent = hotContentCache.get(cacheKey, () -> this.doHotContentSelect(clientType, Integer.parseInt(ew1)));
            parentCollect.addAll(hotContentParent);
        });
        logger.info("热门内容查询完毕{}", JSONObject.toJSONString(parentCollect));
        return parentCollect;
    }

    private List<HotContentParentVO> hotContent(String businessType, Integer size, Integer clientType) {
        logger.info("开关打开，进入兜底业务分支");
        return internalSwitchCache.get(InternalSwitch.KEY + HOT_CONTENT + DATA_KEY + businessType, () -> {
            List<CourseInfo> hotContent = businessEmergencyService.findHotContent(Integer.valueOf(businessType), size);
            return doBuildHotContentParentVO(clientType, hotContent);
        }, DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
    }

    /**
     * 执行查询热门内容
     * @param clientType 客户端类型 1 PC 2 App
     * @param businessType 业务类型
     * @return 热门内容集合数据
     */
    public List<HotContentParentVO> doHotContentSelect(Integer clientType, Integer businessType) {
        String rootOrgId = "1";
        List<CourseInfo> hotVisitsCollect=courseInfoController.getCurrentHotVisitsLists(rootOrgId, businessType, clientType);
        return doBuildHotContentParentVO(clientType, hotVisitsCollect);
    }


    private List<HotContentParentVO> doBuildHotContentParentVO(Integer clientType, List<CourseInfo> hotVisitsCollect) {
        return Optional.ofNullable(clientType)
                .filter(ew1 -> Objects.equals(PC.getCode(), ew1))
                .map(ew2 -> hotVisitsCollect.parallelStream()
                        .map(ew3 -> {
                            HotContentPcVO hotContentPcVO = new HotContentPcVO();
                            hotContentPcVO.setId(ew3.getId());
                            hotContentPcVO.setBusinessType(ew3.getBusinessType());
                            hotContentPcVO.setName(ew3.getName());
                            hotContentPcVO.setVisits(ew3.getVisits());
                            return (HotContentParentVO) hotContentPcVO;
                        }).collect(Collectors.toList()))
                .orElseGet(() -> hotVisitsCollect.parallelStream()
                        .map(ew4 -> {
                            HotContentAppVO hotContentAppVO = new HotContentAppVO();
                            hotContentAppVO.setId(ew4.getId());
                            hotContentAppVO.setBusinessType(ew4.getBusinessType());
                            hotContentAppVO.setName(ew4.getName());
                            hotContentAppVO.setCoverPath(ew4.getCoverPath());
                            hotContentAppVO.setVisits(ew4.getVisits());
                            return hotContentAppVO;
                        }).collect(Collectors.toList())
                );
    }



    /**
     * 首页：扩展自定义模块简介查询（课程|专题）
     * @param context 请求上下文
     * @param subject 用户容器
     * @return 扩展自定义模块简介查询（课程|专题）数据集合
     */
    @JSON("*.*")
    @Param(name = "customizeIdStr",required = true)
    @Param(name = "sizeOpt",type = Integer.class)
    @Param(name = "selectIndex",required = true,type = Integer.class)
    @Param(name = "selectType",required = true,type = Integer.class)
    @RequestMapping(value = "/extend-course-ext",method = RequestMethod.POST)
    public Map<String, String> extendCourseExt(RequestContext context, Subject<Member> subject){
        subject.getCurrentUserId();
        Integer selectType = context.getInteger("selectType");
        Integer selectIndex = context.getInteger("selectIndex");
        Optional<Integer> sizeOpt = context.getOptionalInteger("sizeOpt");
        String customizeIdStr = context.getString("customizeIdStr");
        List<String> customizeIdCollect = Arrays.stream(customizeIdStr.split(",")).parallel().collect(Collectors.toList());
        return coursePolymerService.extendCourseExt(selectType,selectIndex,sizeOpt,customizeIdCollect);
    }


    /**
     * 首页：扩展自定义模块简介查询（直播）
     * @param context 请求上下文
     * @param subject 用户容器
     * @return 扩展自定义模块简介查询（直播）数据集合
     */
    @JSON("*.*")
    @Param(name = "customizeIdStr",required = true)
    @Param(name = "sizeOpt",type = Integer.class)
    @Param(name = "selectIndex",required = true,type = Integer.class)
    @Param(name = "selectType",required = true,type = Integer.class)
    @RequestMapping(value = "/extend-broadcast-ext",method = RequestMethod.POST)
    public Map<String,String> extendBroadcastExt(RequestContext context,Subject<Member> subject){
        subject.getCurrentUserId();
        Optional<Integer> sizeOpt = context.getOptionalInteger("sizeOpt");
        Integer selectType = context.getInteger("selectType");
        Integer selectIndex = context.getInteger("selectIndex");
        String customizeIdStr = context.getString("customizeIdStr");
        List<String> customizeIdCollect = Arrays.stream(customizeIdStr.split(",")).parallel().collect(Collectors.toList());
        return coursePolymerService.extendBroadcastExt(selectType,selectIndex,sizeOpt,customizeIdCollect);
    }
}