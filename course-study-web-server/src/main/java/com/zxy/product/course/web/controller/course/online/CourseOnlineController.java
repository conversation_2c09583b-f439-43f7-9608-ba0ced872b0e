package com.zxy.product.course.web.controller.course.online;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.content.FlowLimitEnum;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.vo.course.online.CourseMemory;
import com.zxy.product.course.vo.course.online.MsgParameter;
import com.zxy.product.course.web.config.CourseOnlineConfig;
import com.zxy.product.course.web.config.SwitchConfig;
import com.zxy.product.course.web.util.CourseFlowVerify;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.Map;

import static com.zxy.product.course.content.CourseLimitConstants.*;
import static com.zxy.product.course.content.CourseLimitConstants.CacheKeyModule;
import static com.zxy.product.course.content.MessageTypeContent.COURSE_DISTRIBUTE_RELEASE;
import static com.zxy.product.system.content.SwitchEnum.FlowLimitingSwitch;

/**
 * 限流限频课程在线控制器
 * <AUTHOR>
 * @date 2025年02月18日 14:10
 */
@Controller
@RequestMapping("/course-online")
public class CourseOnlineController {
    private static final Logger logger = LoggerFactory.getLogger( CourseOnlineController.class );
    private CourseFlowVerify courseFlowVerify;
    private MessageSender messageSender;
    private Cache courseOnlineCache;

    @Autowired
    public void setCourseFlowVerify( CourseFlowVerify courseFlowVerify ){ this.courseFlowVerify=courseFlowVerify; }

    @Autowired
    public void setMessageSender( MessageSender messageSender ){ this.messageSender=messageSender; }

    @Autowired
    public void setCourseOnlineCache( CacheService cacheService ){ this.courseOnlineCache=cacheService.create( CacheKeyApplication,CacheKeyModule ); }

    /**
     * 限流：前置校验是否超过阈值
     * @param requestContext 请求上下文
     * @param subject 用户容器
     * @return 出参
     */
    @JSON("status")
    @Param(name = "businessId",required = true)
    @RequestMapping(value = "/view",method = RequestMethod.GET)
    public Map<String,String> view( RequestContext requestContext, Subject<Member> subject){
        String memberId = subject.getCurrentUserId();
        String businessId = requestContext.getString("businessId");
        if(SwitchConfig.getSwitchStatus(FlowLimitingSwitch)){
            courseFlowVerify.verify( businessId, memberId );
            logger.info( "限流：校验是否超过阈值进入，课程{} 用户{}", businessId, memberId );
        }
        return ImmutableMap.of("status","true");
    }

    /**
     * 限流：后置释放（课程详情专用）
     * @param requestContext 请求上下文
     * @param subject 用户容器
     * @return 出参
     */
    @JSON("status")
    @Param(name = "businessId",required = true)
    @RequestMapping(value = "/release",method = RequestMethod.GET)
    public Map<String,String> release( RequestContext requestContext, Subject<Member> subject ){
        String memberId = subject.getCurrentUserId();
        String businessId = requestContext.getString("businessId");
        MsgParameter msgParameter = this.doMsgParameter(businessId, memberId);
        if(SwitchConfig.getSwitchStatus(FlowLimitingSwitch)){
            messageSender.send(COURSE_DISTRIBUTE_RELEASE,msgParameter);
        }
        return ImmutableMap.of("status","true");
    }

    /**
     * 组装MQ参数
     * @param businessId 业务Id
     * @param memberId 用户Id
     * @return MQ入参POJO
     */
    private MsgParameter doMsgParameter ( String businessId, String memberId){
        MsgParameter msgParameter = new MsgParameter();
        msgParameter.setBusinessId( businessId );
        msgParameter.setMemberId( memberId );
        CourseMemory courseMemory=CourseOnlineConfig.courseMemory;
        boolean flag = courseMemory.getCoreCourseCollect().contains(businessId);
        msgParameter.setCoreState(flag ? Core :Ordinary);
        return msgParameter;
    }

   /** 查询当前课程在线人数 */
    @JSON("coreMaxOnline")
    @JSON("ordinaryMaxOnline")
    @RequestMapping( value = "/do-cache-online", method = RequestMethod.GET)
    public Map<String, Long> doCacheOnline(){
        Map<String,Long> onlineMap = new HashMap<>(3);
        String coreCacheKey=CacheKeyCoreOnline+ FlowLimitEnum.CourseInfo.getBusinessType();
        Long coreOnline = courseOnlineCache.increment(coreCacheKey, 0L);
        onlineMap.put( "coreMaxOnline",coreOnline );
        String ordinaryCacheKey=CacheKeyOrdinaryOnline+ FlowLimitEnum.CourseInfo.getBusinessType();
        Long ordinaryMaxOnline = courseOnlineCache.increment( ordinaryCacheKey, 0L );
        onlineMap.put( "ordinaryMaxOnline", ordinaryMaxOnline );
        return onlineMap;
    }
}
