package com.zxy.product.course.web.controller.assistant;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.course.CourseQuestionCommendService;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.entity.CourseQuestionRecommend;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.web.util.BrowserUtil;
import com.zxy.product.course.web.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/18:01
 * @Description:
 */
@Controller
@RequestMapping("/front/recommend")
public class CourseFrontQuestionCommendController {



}
