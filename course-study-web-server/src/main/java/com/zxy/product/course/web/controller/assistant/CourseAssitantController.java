package com.zxy.product.course.web.controller.assistant;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Permitted;
import com.zxy.product.course.api.course.CourseKnowlegeService;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.dto.AttachmentDto;
import com.zxy.product.course.entity.CourseKnowledge;
import com.zxy.product.course.web.helper.UploadHelper;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.entity.Attachment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 * 学习助手-知识
 * @Author: xxh
 * @Date: 2025/05/26/18:20
 * @Description:
 */
@Controller
@RequestMapping("/assistant")
public class CourseAssitantController {

    private static Logger logger = LoggerFactory.getLogger(CourseAssitantController.class);

    private CourseKnowlegeService courseKnowlegeService;

    private FileService fileService;

    private AttachmentResolver attachmentResolver;

    @Value("${course.ai.file.path}")
    private String filePath;

    @Autowired
    public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
        this.attachmentResolver = attachmentResolver;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setCourseKnowlegeService(CourseKnowlegeService courseKnowlegeService) {
        this.courseKnowlegeService = courseKnowlegeService;
    }

    @Permitted
    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class)
    @Param(name = "pageSize", type = Integer.class)
    @Param(name = "name")
    @Param(name = "status", type = Integer.class)
    @Param(name = "shelveStartTime", type = Long.class)
    @Param(name = "shelveEndTime", type = Long.class)
    @JSON("recordCount")
    @JSON("items.(id, name, attachmentId, attachmentName, attachmentType,status,finshStatus,knowledge,shelveTime,modifyDate)")
    public PagedResult<CourseKnowledge> find(RequestContext context){
       return courseKnowlegeService.find(context.getInteger("page"),context.getInteger("pageSize"),
                context.getOptionalString("name"),context.getOptionalInteger("status"),
                context.getOptionalLong("shelveStartTime"), context.getOptionalLong("shelveEndTime"));
    }

    @Permitted
    @RequestMapping(value = "/{id}",method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("id, name, attachmentId, attachmentName, attachmentType,status,finshStatus,knowledge,shelveTime")
    public CourseKnowledge getDetail(RequestContext context){
       return courseKnowlegeService.getOptional(context.getString("id")).orElse(new CourseKnowledge());
    }

    @Permitted
    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "name", required = true)
    @Param(name = "attachment", required = true)
    @Param(name = "knowledge")
//   @FormFile(name="file", required = true, extension = "(?i)(xls|xlsx|doc|docx|ppt|pptx|zip|txt|rar|pdf)")
    @JSON("*.*")
    public Map<String, Object> add(RequestContext context){
        courseKnowlegeService.insert(setData(context, Optional.empty()));
        return ImmutableMap.of("result", "success");
    }


    private CourseKnowledge setData(RequestContext context, Optional<String> id){
        CourseKnowledge knowledge = new CourseKnowledge();
        if(id.isPresent()){
            Optional<CourseKnowledge> courseKnowledge = courseKnowlegeService.getOptional(id.get());
            if(!courseKnowledge.isPresent()){
                throw new UnprocessableException(ErrorCode.DJDataError);
            }
            knowledge = courseKnowledge.get();
        } else {
            knowledge.forInsert();
            knowledge.setDelete(CommonConstant.ZERO);
            knowledge.setStatus(CommonConstant.ZERO);
            knowledge.setFinshStatus(CommonConstant.ZERO);
        }
        knowledge.setModifyDate(System.currentTimeMillis());
        AttachmentDto attachment = com.alibaba.fastjson.JSON.parseObject(context.getString("attachment"), AttachmentDto.class);
        if(Objects.nonNull(attachment)){
            knowledge.setAttachmentId(attachment.getId());
            knowledge.setAttachmentName(attachment.getName());
            knowledge.setAttachmentType(attachment.getType());
        }
        knowledge.setKnowledge(context.getOptionalString("knowledge").orElse(null));
        knowledge.setName(context.getString("name"));
        return knowledge;
    }


    @Permitted
    @RequestMapping(value = "/{id}",method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "name", required = true)
    @Param(name = "attachment", required = true)
    @Param(name = "knowledge")
//    @FormFile(name="file", required = true, extension = "(?i)(xls|xlsx|doc|docx|ppt|pptx|zip|txt|rar|pdf)")
    @JSON("*.*")
    public Map<String, Object> update(RequestContext context){
        courseKnowlegeService.update(setData(context, context.getOptionalString("id")));
        return ImmutableMap.of("result", "success");
    }


    @Autowired
    private UploadHelper uploadHelper;

    @Permitted
    @RequestMapping(value = "/update-status/{id}",method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "status",type = Integer.class, required = true)
    @JSON("*.*")
    public Map<String, Object> publish(RequestContext context){
        Optional<CourseKnowledge> courseKnowledge = courseKnowlegeService.getOptional(context.getString("id"));
        Integer status = context.getInteger("status");
        try {
         if(courseKnowledge.isPresent() && Objects.equals(status, CourseKnowledge.STATUS_PUBLISH)){
                Optional<Attachment> attachment = fileService.get(courseKnowledge.get().getAttachmentId());
                if(attachment.isPresent()){
                    Attachment a = attachment.get();
//                    com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
//                    fastDFSAttachment.setPath(a.getPath());
//                    InputStream is = attachmentResolver.resolveToRead(fastDFSAttachment);
//                    logger.info("InputStream size ={}", is.available());
//                    MultipartFile multipartFile = HttpClientUtil.transferTo(is, "text/plain; charset=UTF-8", a.getFilename(), is.available());
//                    logger.info("multipartFile size ={}", multipartFile.getSize());
                    CourseKnowledge knowledge = uploadHelper.sendFile(a, courseKnowledge.get(), Optional.ofNullable(courseKnowledge.get().getAttachmentUpId()));
                    courseKnowlegeService.publish(context.getString("id"), status, knowledge);
                }
                return ImmutableMap.of("result", "success");
            }
            courseKnowlegeService.publish(context.getString("id"), status, courseKnowledge.get());
        }catch (Exception e) {
            logger.info("发布失败" + e.getMessage());
            throw new UnprocessableException(ErrorCode.uploadFileFail);
        }
        return ImmutableMap.of("result", "success");
    }

    @Permitted
    @RequestMapping(value = "/{id}",method = RequestMethod.DELETE)
    @Param(name = "id", required = true)
    @JSON("*.*")
    public Map<String, Object> delete(RequestContext context){
        courseKnowlegeService.delete(context.getString("id"));
        return ImmutableMap.of("result", "success");
    }


}
