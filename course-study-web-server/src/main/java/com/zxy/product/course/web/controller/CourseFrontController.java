package com.zxy.product.course.web.controller;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.AuthenticationException;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.security.support.oauth.AccessToken;
import com.zxy.common.restful.util.Encrypt;
import com.zxy.common.restful.validation.ValidationException;
import com.zxy.product.course.api.CourseInfoPerfService;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.CourseRegisterService;
import com.zxy.product.course.api.CourseStudyProgressService;
import com.zxy.product.course.api.KnowledgeService;
import com.zxy.product.course.api.MultidimensionalScoringService;
import com.zxy.product.course.api.SubjectAdvertisingService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.course.CourseProcessService;
import com.zxy.product.course.api.other.CourseExceptionService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.dto.CourseProgressCache;
import com.zxy.product.course.entity.BusinessTopic;
import com.zxy.product.course.entity.CourseChapter;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseNote;
import com.zxy.product.course.entity.CoursePhoto;
import com.zxy.product.course.entity.CourseRegister;
import com.zxy.product.course.entity.CourseScore;
import com.zxy.product.course.entity.CourseSectionProgressAttachment;
import com.zxy.product.course.entity.CourseSectionStudyLog;
import com.zxy.product.course.entity.CourseSectionStudyProgress;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.MultidimensionalScoring;
import com.zxy.product.course.entity.SubjectAdvertising;
import com.zxy.product.course.jooq.tables.pojos.CourseChapterSectionEntity;
import com.zxy.product.course.jooq.tables.pojos.CourseInfoEntity;
import com.zxy.product.course.mongodb.CourseStudyLog;
import com.zxy.product.course.util.CourseStudyException;
import com.zxy.product.course.vo.course.online.MsgParameter;
import com.zxy.product.course.web.aspectj.Behavior;
import com.zxy.product.course.web.audit.ImAudit;
import com.zxy.product.course.web.config.SwitchConfig;
import com.zxy.product.course.web.kit.ArchivedKit;
import com.zxy.product.course.web.util.CourseFlowVerify;
import com.zxy.product.exam.api.ExamService;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.api.ThirdPartyConfigService;
import com.zxy.product.human.api.plan.StudyPlanService;
import com.zxy.product.human.entity.ThirdPartyConfig;
import com.zxy.product.system.api.homeconfig.CourseVirtualSpaceService;
import com.zxy.product.system.api.homeconfig.HomeConfigService;
import com.zxy.product.system.api.internalswitch.InternalSwitchService;
import com.zxy.product.system.api.operation.IntegralDetailService;
import com.zxy.product.system.api.operation.IntegralService;
import com.zxy.product.system.api.setting.RuleConfigService;
import com.zxy.product.system.content.IntegralRuleConstant;
import com.zxy.product.system.content.SwitchEnum;
import com.zxy.product.system.entity.CourseVirtualSpace;
import com.zxy.product.system.entity.IntegralDetail;
import com.zxy.product.system.entity.IntegralResult;
import com.zxy.product.system.entity.InternalSwitch;
import com.zxy.product.system.entity.RuleConfig;
import com.zxy.product.system.result.ResultMsg;
import com.zxy.product.system.util.DateUtil;
import com.zxy.product.system.util.RuleConfigRedisKeyUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zxy.product.course.content.CourseLimitConstants.CacheKeyApplication;
import static com.zxy.product.course.content.CourseLimitConstants.CacheKeyModule;
import static com.zxy.product.course.content.MessageTypeContent.COURSE_DISTRIBUTE_SURVIVAL;
import static com.zxy.product.system.content.SwitchEnum.FlowLimitingSwitch;
import static java.util.stream.Collectors.toList;

@Controller
@RequestMapping("/course-front")
public class CourseFrontController implements EnvironmentAware {
    private static Logger logger = LoggerFactory.getLogger(CourseFrontController.class);
    public static final String OAUTH_PROVIDER_APPLICATION_NAME = "oauth-provider";
    public static final String OAUTH_PROVIDER_MODULE_NAME = "oauth";
    private CourseInfoService courseInfoService;
    private CourseStudyProgressService courseStudyProgressService;
    private CourseRegisterService courseRegisterService;
    private SubjectAdvertisingService subjectAdvertisingService;
    private CourseProcessService courseProcessService;
    private RuleConfigService ruleConfigService;
    private MessageSender messageSender;
    private Redis Redis;
    private CourseExceptionService courseExceptionService;
    private IntegralDetailService integralDetailService;
    private IntegralService integralService;
    private CourseCacheService courseCacheService;
    private String aesKey;
    private Cache cache;
    private Cache cacheSystem;
    private Cache chapterProgressCache;
    private Cache courseSectionCache;
    private ThirdPartyConfigService thirdPartyConfigService;
    private StudyPlanService studyPlanService;
    private MemberService memberService;
    private CourseFlowVerify courseFlowVerify;
    private Cache courseOnlineCache;
    private CourseInfoPerfService courseInfoPerfService;
    @Resource
    private KnowledgeService knowledgeService;
    @Resource
    private ExamService examService;

    private HomeConfigService homeConfigService;

    private static final Object LOCK = new Object();


    private CourseVirtualSpaceService courseVirtualSpaceService;


    private InternalSwitchService internalSwitchService;
    private Cache intelligentSearchCache;

    private final  static String  KEY ="intelligentSearch";

    @Autowired
    public void setHomeConfigService(HomeConfigService homeConfigService) {
        this.homeConfigService = homeConfigService;
    }

    @Autowired
    public void setInternalSwitchService(InternalSwitchService internalSwitchService) {
        this.internalSwitchService = internalSwitchService;
    }

    @Autowired
    public void setCourseVirtualSpaceService(CourseVirtualSpaceService courseVirtualSpaceService) {
        this.courseVirtualSpaceService = courseVirtualSpaceService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Resource
    private MultidimensionalScoringService multidimensionalScoringService;

    @Autowired
    public void setThirdPartyConfigService(ThirdPartyConfigService thirdPartyConfigService) {
        this.thirdPartyConfigService = thirdPartyConfigService;
    }

    @Autowired
    public void setCourseExceptionService(CourseExceptionService courseExceptionService) {
        this.courseExceptionService = courseExceptionService;
    }

    @Autowired
    public void setRedis(com.zxy.common.cache.redis.Redis redis) {
        Redis = redis;
    }

    @Autowired
    public void setRuleConfigService(RuleConfigService ruleConfigService) {
        this.ruleConfigService = ruleConfigService;
    }

    @Autowired
    public void setCourseProcessService(CourseProcessService courseProcessService) {
        this.courseProcessService = courseProcessService;
    }

    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setCourseStudyProgressService(CourseStudyProgressService courseStudyProgressService) {
        this.courseStudyProgressService = courseStudyProgressService;
    }

    @Autowired
    public void setCourseRegisterService(CourseRegisterService courseRegisterService) {
        this.courseRegisterService = courseRegisterService;
    }

    @Autowired
    public void setSubjectAdvertisingService(SubjectAdvertisingService subjectAdvertisingService) {
        this.subjectAdvertisingService = subjectAdvertisingService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setIntegralDetailService(IntegralDetailService integralDetailService) {
        this.integralDetailService = integralDetailService;
    }

    @Autowired
    public void setIntegralService(IntegralService integralService) {
        this.integralService = integralService;
    }

    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }

    @Autowired
    public void setStudyPlanService(StudyPlanService studyPlanService) {
        this.studyPlanService = studyPlanService;
    }

    @Override
    public void setEnvironment(Environment environment) {
        aesKey = environment.getProperty("aes.key", "d8cg8gVakEq9Agup");
    }

    @Autowired
    public void setCourseFlowVerify(CourseFlowVerify courseFlowVerify){
        this.courseFlowVerify=courseFlowVerify;
    }

    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create(OAUTH_PROVIDER_APPLICATION_NAME, OAUTH_PROVIDER_MODULE_NAME);
        this.intelligentSearchCache = cacheService.create("anti-corruption", InternalSwitch.KEY);
        this.chapterProgressCache = cacheService.create("course-front", "chapter-progress-data");
        this.courseSectionCache = cacheService.create("course-front", "course-section-data");
        this.cacheSystem = cacheService.create(RuleConfigRedisKeyUtil.getSpringApplicationName(), RuleConfigRedisKeyUtil.getModeName());
        this.courseOnlineCache=cacheService.create(CacheKeyApplication,CacheKeyModule);
    }

    @Autowired
    public void setCourseInfoPerfService(CourseInfoPerfService courseInfoPerfService) {
        this.courseInfoPerfService = courseInfoPerfService;
    }

    @RequestMapping(value = "/lastest-user", method = RequestMethod.GET)
    @Permitted
    @Param(name = "courseId", required = true)
    // 课程id
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @JSON("recordCount")
    @JSON("items.(*)")
    public PagedResult<Member> findLastestUser(RequestContext context, Subject<Member> subject) {
        return courseInfoService.findLastestUser(
                subject.getCurrentUserId(),
                context.getString("courseId"),
                context.getInteger("page"),
                context.getInteger("pageSize"));


    }

    /**
     * 查询课程列表,需分页,排序
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "orderBy", type = Integer.class)
    //0:上架时间 1:访问人数 2:评分
    @Param(name = "order", type = Integer.class)
    //1:asc 2:desc
    @Param(name = "companyType", type = Integer.class)
    // 1:集团 2:本公司 3:其它公司
    @Param(name = "searchContent")
    // 搜索内容,现在只匹配了:课程名称和描述
    @Param(name = "categoryId")
    //课程目录id
    @Param(name = "topicId")
    //话题id
    @Param(name = "publishClient", type = Integer.class)
    // 1: PC, 2: APP
    @Param(name = "type", type = Integer.class, required = true)
    // 0:课程 2:专题
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,releaseTime,courseTime,source,status,cover,coverPath,beginDate,endDate,description,studyMemberCount,finishStatus,visits,avgScore,url, descriptionText,integral)")
    public PagedResult<CourseInfo> findCourseInfoPage(RequestContext context, Subject<Member> subject) {
        return courseInfoService.find(subject.getCurrentUserId(),
                context.getInteger("page"),
                context.getInteger("pageSize"),
                context.getOptionalInteger("orderBy"),
                context.getOptionalInteger("order"),
                context.getOptionalString("searchContent"),
                context.getOptionalString("categoryId"),
                context.getOptionalString("topicId"),
                context.getInteger("type"),
                context.getOptionalInteger("publishClient"),
                context.getOptionalInteger("companyType"));
    }

    /**
     * 查询课程列表,需分页,排序
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/front", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "orderBy", type = Integer.class)
    //0:上架时间 1:访问人数 2:评分
    @Param(name = "order", type = Integer.class)
    //1:asc 2:desc
    @Param(name = "companyType", type = Integer.class)
    // 1:集团 2:本公司 3:其它公司
    @Param(name = "searchContent")
    // 搜索内容,现在只匹配了:课程名称和描述
    @Param(name = "categoryId")
    //课程目录id
    @Param(name = "topicId")
    //话题id
    @Param(name = "publishClient", type = Integer.class)
    // 1: PC, 2: APP
    @Param(name = "type", type = Integer.class, required = true)
    // 0:课程 2:专题
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,releaseTime,courseTime,source,status,cover,coverPath,beginDate,endDate,description,studyMemberCount,finishStatus,visits,avgScore,url, descriptionText,integral)")
    @JSON("more")
    public Map<String, Object> findCourseOrSubjectInfoPage(RequestContext context, Subject<Member> subject) {
        boolean cachedPage = SwitchConfig.getSwitchStatus(SwitchEnum.CourseAndSubjectSwitch);
        boolean cachedOrder = SwitchConfig.getSwitchStatus(SwitchEnum.CourseAndSubjectOrder);
        int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);

        return courseInfoPerfService.findCourseOrSubjectInfoPage(
                subject.getCurrentUserId(),
                context.getInteger("page"),
                context.getInteger("pageSize"),
                context.getOptionalInteger("orderBy"),
                context.getOptionalInteger("order"),
                context.getOptionalString("searchContent"),
                context.getOptionalString("categoryId"),
                context.getOptionalString("topicId"),
                context.getOptionalInteger("publishClient"),
                context.getOptionalInteger("companyType"),
                context.getInteger("type"),
                cachedPage,pageSwitch == 1,
                cachedOrder,
                subject.from()
        );
//        return courseInfoService.findMap(subject.getCurrentUserId(),
//                context.getInteger("page"),
//                context.getInteger("pageSize"),
//                context.getOptionalInteger("orderBy"),
//                context.getOptionalInteger("order"),
//                context.getOptionalString("searchContent"),
//                context.getOptionalString("categoryId"),
//                context.getOptionalString("topicId"),
//                context.getInteger("type"),
//                context.getOptionalInteger("publishClient"),
//                context.getOptionalInteger("companyType"),
//                cachedPage,pageSwitch == 1,
//                cachedOrder);
    }

    @RequestMapping(value = "/front-page-new", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "orderBy", type = Integer.class)
    //0:上架时间 1:访问人数 2:评分
    @Param(name = "order", type = Integer.class)
    //1:asc 2:desc
    @Param(name = "companyType", type = Integer.class)
    // 1:集团 2:本公司 3:其它公司
    @Param(name = "searchContent")
    // 搜索内容,现在只匹配了:课程名称和描述
    @Param(name = "categoryId")
    //课程目录id
    @Param(name = "topicId")
    //话题id
    @Param(name = "publishClient", type = Integer.class)
    // 1: PC, 2: APP
    @Param(name = "type", type = Integer.class, required = true)
    // 0:课程 2:专题
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,releaseTime,courseTime,source,status,cover,coverPath,beginDate,endDate,studyMemberCount,finishStatus,visits,avgScore,url, integral)")
    @JSON("more")
    public Map<String, Object> findCourseOrSubjectInfoPageNew(RequestContext context, Subject<Member> subject) {
        boolean cachedPage = SwitchConfig.getSwitchStatus(SwitchEnum.CourseAndSubjectSwitch);
        boolean cachedOrder = SwitchConfig.getSwitchStatus(SwitchEnum.CourseAndSubjectOrder);
        int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);

        return courseInfoPerfService.findCourseOrSubjectInfoPageNew(
                subject.getCurrentUserId(),
                context.getInteger("page"),
                context.getInteger("pageSize"),
                context.getOptionalInteger("orderBy"),
                context.getOptionalInteger("order"),
                context.getOptionalString("searchContent"),
                context.getOptionalString("categoryId"),
                context.getOptionalString("topicId"),
                context.getOptionalInteger("publishClient"),
                context.getOptionalInteger("companyType"),
                context.getInteger("type"),
                cachedPage,pageSwitch == 1,
                cachedOrder,
                subject.from()
        );
    }






    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @Param(name = "clickType")
    @Param(name = "contentType")
    @Param(name = "middlePage")
    @JSON("id,name,avgScore,studyMemberCount,courseTime,description,lecturer,credit,shelveTime,addType,cover,coverPath,learnSequence,publishClient,businessType, descriptionText,allCourseTime, descriptionApp,integral, relativeGenseeId,useVideoSpeed,switchHide,historyHide,switchMentor,captionFlag,organizationName,homeId,constructionType")
    @JSON("courseAttachments.(*)")
    @JSON("releaseOrg.(id,name)")
    @JSON("courseChapters.(*)")
    @JSON("courseChapters.courseChapterSections.(*)")
    @Behavior(contentID = "id", clickType = "clickType", pageSource = "/api/v1/course-study/course-front/info", contentType = "contentType")
    public CourseInfo getNew(RequestContext context, Subject<Member> subject) {
        String id = context.getString("id");
        String memberId = subject.getCurrentUserId();
        Integer from = subject.from();
        if (!context.getOptionalBoolean("middlePage").orElse(false)) {
            backToTheCourse(context.getRequest(), id, true, memberId);
        }
        CourseInfo frontNew = courseInfoService.getFrontNew2(id, memberId, from);
        this.addVisits(id, memberId);
        String organizationId = frontNew.getOrganizationId();
        //查询虚拟空间id
        frontNew.setHomeId(homeConfigService.getVirtualSpaceIdByOrganizationId(organizationId));
        return frontNew;
    }

    public void addVisits(String courseId, String memberId) {
        // 访问次数+1
        messageSender.send(MessageTypeContent.COURSE_STATISTICS_VISITS,
                MessageHeaderContent.ID, courseId, MessageHeaderContent.MEMBER_ID, memberId);
        messageSender.send(MessageTypeContent.COURSE_STATISTICS_STUDY_COUNT,
                MessageHeaderContent.ID, courseId, MessageHeaderContent.MEMBER_ID, memberId);
        // 专家工作室人气值统计
        messageSender.send(MessageTypeContent.BAR_STUDIO_POPULAR_COMPUTE, MessageHeaderContent.BUSINESS_ID, courseId);

    }

    /**
     * 判断用户是否购买某个课程
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/is-course-purchase", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("*")
    public JSONObject isPurchaseCourse(RequestContext context, Subject<Member> subject) {
        String id = context.getString("id");
        String memberId = subject.getCurrentUserId();
        IntegralDetail integralDetail = integralDetailService.findMemberByBusiness(memberId, IntegralRuleConstant.COURSE_SEE_DELETE, id);
        JSONObject json = new JSONObject();
        json.put("code", 200);
        json.put("message", "success");
        json.put("purchase", integralDetail == null || "".equals(integralDetail.getId()) ? false : true);
        return json;
    }


    /**
     * 消耗积分购买课程
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/course-purchase", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("*")
    public ResultMsg getPurchaseCourse(RequestContext context, Subject<Member> subject) throws Exception {
        String id = context.getOptionalString("id").orElse("");
        String memberId = subject.getCurrentUserId();
        IntegralDetail integralDetail = integralDetailService.findMemberByBusiness(memberId, IntegralRuleConstant.COURSE_SEE_DELETE, id);
        ResultMsg resultMsg = new ResultMsg();
        resultMsg.setCode("40003");
        resultMsg.setMsg("您已有权查看改视频，请勿重复购买");
        if (integralDetail == null) {
            Optional<IntegralResult> resultOptional = integralService.getIntegralResultByMemberId(memberId);
            CourseInfo course = courseInfoService.getCourseInfo(id);
            if (course == null || "".equals(course.getId())) {
                resultMsg.setCode("40001");
                resultMsg.setMsg("课程不存在");
                return resultMsg;
            }
            int integral = course.getIntegral() == null ? 0 : course.getIntegral();
            if ((integral > 0) && (!resultOptional.isPresent() || resultOptional.get().getTotalScore().subtract(new BigDecimal(integral)).signum() < 0)) {
//        		resultMsg.setCode("40002");
//        		resultMsg.setMsg("积分不足");
//        		return resultMsg;
                throw new UnprocessableException(ErrorCode.CourseNotEnoughIntegral);
            }
            if (integral != 0) {
                String details = course.getIntegral() > 0 ? "观看课程直接消耗积分" : "观看课程奖励积分";
                integralService.subtractIntegral(memberId, Double.valueOf(course.getIntegral() + ""), IntegralRuleConstant.COURSE_SEE_DELETE, IntegralService.BUSINESS_TYPE_COURSE, course.getId(), details, subject.getRootOrganizationId());
            }
            resultMsg.setCode("200");
            resultMsg.setMsg("课程购买成功");
        }
        return resultMsg;

    }

    @RequestMapping(value = "/preview/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("id,name,avgScore,studyMemberCount,courseTime,description,lecturer,credit,shelveTime,addType,cover,coverPath,learnSequence,publishClient,businessType, descriptionText,allCourseTime, descriptionApp,integral, relativeGenseeId,useVideoSpeed,switchHide")
    @JSON("courseAttachments.(*)")
    @JSON("releaseOrg.(id,name)")
    @JSON("courseChapters.(*)")
    @JSON("courseChapters.courseChapterSections.(*)")
    public CourseInfo preview(RequestContext context, Subject<Member> subject) {
        String id = context.getString("id");
        String memberId = subject.getCurrentUserId();
        return courseInfoService.getPerview(id, memberId);
    }

    @RequestMapping(value = "/course-progress", method = RequestMethod.POST)
    @Param(name = "ids", required = true)
    @JSON("*")
    public List<CourseSectionStudyProgress> progresses(RequestContext context, Subject<Member> subject) {
        return courseInfoService.selectProgress(subject.getCurrentUserId(),
                Arrays.asList(context.getString("ids").split(",")));
    }

    @RequestMapping(value = "/course-info-progress", method = RequestMethod.GET)
    @Param(name = "ids", required = true)
    @JSON("courseId, finishStatus")
    public List<CourseStudyProgress> courseInfoprogresses(RequestContext context, Subject<Member> subject) {
        return courseInfoService.selectCourseProgress(subject.getCurrentUserId(),
                Arrays.asList(context.getString("ids").split(",")));
    }


    @RequestMapping(value = "/hotTopicIds", method = RequestMethod.GET)
    @JSON("id")
    public List<Map<String, String>> hotTopicIds() {
        List<Map<String, String>> hotTopicIdCollect = cache.get(BusinessTopic.COURSE_HOT_TOPIC_IDS_CACHE_KEY, List.class);
        if(hotTopicIdCollect==null){
            synchronized (LOCK){
                hotTopicIdCollect=cache.get(BusinessTopic.COURSE_HOT_TOPIC_IDS_CACHE_KEY,List.class);
                if(hotTopicIdCollect==null){
                    hotTopicIdCollect=courseInfoService.hotTopicIds().stream().map(x -> ImmutableMap.of("id", x)).collect(Collectors.toList());
                    if(hotTopicIdCollect==null){
                        hotTopicIdCollect=Lists.newArrayList();
                    }
                    cache.set(BusinessTopic.COURSE_HOT_TOPIC_IDS_CACHE_KEY, hotTopicIdCollect,timeLeftInSeconds());
                }
            }
        }
        return hotTopicIdCollect;
    }

    private static int timeLeftInSeconds() {
        long now = System.currentTimeMillis();
        long today24 = DateUtil.getTimesnightByLong(now);
        return (int)(today24 - now) / 1000;
    }

    /**
     * 更新课程笔记
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/course-note/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", required = true)
    // note id
    @Param(name = "content", required = true)
    // 笔记内容
    @JSON("id")
    public CourseNote updateCourseNote(RequestContext context, Subject<Member> subject) {
        return courseInfoService.updateCourseNote(subject.getCurrentUserId(),
                context.getString("id"),
                context.getString("content"));
    }

    /**
     * 新增课程笔记
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/course-note", method = RequestMethod.POST)
    @Permitted
    @Param(name = "courseId", required = true)
    // 课程id
    @Param(name = "content", required = true)
    // 笔记内容
    @JSON("id")
    public CourseNote insertCourseNote(RequestContext context, Subject<Member> subject) {
        return courseInfoService.insertCourseNote(subject.getCurrentUserId(),
                context.getString("courseId"),
                context.getString("content"));

    }

    /**
     * 删除课程笔记
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/course-note/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", required = true)
    // 笔记内容
    @JSON("*")
    public Map<String, Object> deleteCourseNote(RequestContext context, Subject<Member> subject) {
        return ImmutableMap.of("id", courseInfoService.deleteCourseNote(context.getString("id")));
    }

    /**
     * 查询课程笔记列表
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/course-notes", method = RequestMethod.GET)
    @Permitted
    @Param(name = "courseId", required = true)
    // 课程id
    @JSON("id,courseId,content")
    public List<CourseNote> findCourseNotes(RequestContext context, Subject<Member> subject) {
        return courseInfoService.findCourseNotes(
                subject.getCurrentUserId(),
                context.getString("courseId"));

    }

    @RequestMapping(value = "/course-section-chapter/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @Param(name = "subjectName", required = true)
    @Param(name = "flag")
    @JSON("*")
    @ImAudit(module = "个人中心", subModule = "学情查询", action = ImAudit.Action.FIND, fisrtAction = "查询", desc = "导出学情分析报告《{0}》", params = {"subjectName"}, businessType = "flag", businessValue = "true")
    public List<CourseChapterSection> findCourseChapterById(RequestContext requestContext,Subject<Member> subject) {
        String id = requestContext.getString("id");
        return courseInfoService.findCourseChapterById(id, Optional.ofNullable(CourseChapterSection.SECTION_TYPE_EXAM));
    }

    /**
     * 根据ids查询考试相关基本信息
     * @param context
     * @return
     */
    @RequestMapping(value="/front/find-by-ids", method = RequestMethod.GET)
    @Param(name="ids", required = true)
    @Param(name = "subjectName", required = true)
    @Param(name = "flag")
    @JSON("id,name")
    @ImAudit(module = "个人中心", subModule = "学情查询", action = ImAudit.Action.FIND, fisrtAction = "查询", desc = "学情分析报告《{0}》", params = {"subjectName"}, businessType = "flag", businessValue = "true")
    public List<Exam> findByIds(RequestContext context) {
        return examService.getByIds(context.getString("ids").split(","));
    }

    /**
     * 用户自主注册
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/register", method = RequestMethod.POST)
    @Permitted
    @Param(name = "courseId", required = true)
    // 课程id
    @Param(name = "clickType")
    @Param(name = "contentType")
    @JSON("courseChapters.(*)")
    @JSON("courseAttachments.(*)")
    @JSON("releaseUser.(id,name)")
    @JSON("courseChapters.courseChapterSections.(*)")
    @JSON("courseChapters.courseChapterSections.progress.(*)")
    @JSON("courseScore.(*)")
    @JSON("studyProgress.(*)")
    @JSON("photos.(*)")
    @JSON("businessTopics.(*)")
    @JSON("advertisings.(*)")
    @JSON("textAreas.(*)")
    @JSON("id,name,organizationId,businessType,url,cover,beginDate,endDate,code,coverPath,description,descriptionText,publishClient,studyDays,versionId,styles,scoreMemberCount,avgScore,register,registerMemberCount,addType,descriptionApp,certificateId,isSign,existedManager,switchMentor,pcBannerPath,skinType")
    @Behavior(contentID = "courseId", clickType = "clickType", pageSource = "/api/v1/course-study/course-front/register", contentType = "contentType")
    public CourseInfo register(RequestContext context, Subject<Member> subject) {
        String courseId = context.getString("courseId");
        String currentUserId = subject.getCurrentUserId();
        Integer from = subject.from();
        backToTheCourse(context.getRequest(), courseId, false, currentUserId);

        String subjectDomain = context.getRequest().getServerName().toString() + "/subject";
        logger.info("register.subjectDomain: {}", subjectDomain);
        return courseRegisterService.register(from, currentUserId, context.getString("courseId"), subjectDomain);
    }


    @RequestMapping(value = "/registerStudy", method = RequestMethod.POST)
    @Param(name = "courseId", required = true)
    // 课程id
    @Param(name = "type", type = Integer.class)
    // 课程id
    @JSON("id,type,finishStatus,currentSectionId,finishTime")
    public CourseStudyProgress registerSelf(RequestContext context, Subject<Member> subject) {
        return courseRegisterService.registerStudy(subject.getCurrentUserId(),
                context.getString("courseId"),
                context.getOptionalInteger("type").orElse(CourseRegister.TYPE_SELF));
    }

    /**
     * 查询专题相册
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/course-photo", method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "id", required = true)
    @JSON("recordCount")
    @JSON("items.(*)")
    public PagedResult<CoursePhoto> findCoursePhotoPage(RequestContext context) {
        return courseInfoService.findPhotosPageBySubjectId(context.getString("id"), context.getInteger("page"), context.getInteger("pageSize"));
    }

    /**
     * 查询课程学习日志
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/study-log", method = RequestMethod.GET)
    @Permitted
    @Param(name = "sectionId", required = true)
    @JSON("*")
    public List<CourseSectionStudyLog> getSectionStudyLogs(RequestContext context, Subject<Member> subject) {
        return courseStudyProgressService.getSectionStudyLogs(subject.getCurrentUserId(), context.getString("sectionId"));
    }

    /**
     * 课程详情 音视频更新
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/video-progress", method = RequestMethod.POST)
//    @SecurityParam
    @Param(name = "logId", security = true)
    @Param(name = "studyTime", required = true, type = Integer.class, security = true)
    @Param(name = "resourceTotalTime", required = true, type = Integer.class, security = true)
    @Param(name = "lessonLocation", type = Integer.class, security = true)
    @Param(name = "organizationId", security = true)
    @Param(name = "flush", security = true)
    // 是否更新课程进度
    @Param(name = "remove", type = Integer.class, security = true)
    // 是否需要清空当前播放课程缓存信息 1:清理
    @JSON("*")
    public CourseSectionStudyProgress videoProgress(RequestContext context, Subject<Member> subject) {
        String orgId = context.getOptionalString("organizationId").orElse("1");

        Map<String, RuleConfig> ruleMap = ruleConfigService.findAll(orgId, Optional.of(RuleConfig.STATUS_ENABLED)).stream().collect(Collectors.toMap(RuleConfig::getKey, r -> r, (key1, key2) -> key1));
        int configProcess = Optional.ofNullable(ruleMap.get(RuleConfig.KEY.VIDEO_PROCESS_THRESHOLD.name())).map(r -> Integer.parseInt(r.getValue())).orElse(50);
        int configPlay = Optional.ofNullable(ruleMap.get(RuleConfig.KEY.VIDEO_PALY_THRESHOLD.name())).map(r -> Integer.parseInt(r.getValue())).orElse(50);
//        int configProcess = ruleConfigService.getByName(orgId,RuleConfig.KEY.VIDEO_PROCESS_THRESHOLD).map(x-> Integer.parseInt(x.getValue())).orElse(50);
//        int configPlay = ruleConfigService.getByName(orgId,RuleConfig.KEY.VIDEO_PALY_THRESHOLD).map(x-> Integer.parseInt(x.getValue())).orElse(50);
        String agent = context.getRequest().getHeader("User-Agent");

        Integer studyTime = context.getOptionalInteger("studyTime").orElse(null);
        String logId = context.getOptionalString("logId").orElse(null);
        logger.info("media音视频学习，logId={}", logId);
        // updated by wangdongyan 以防前台传的的参数不能转换为数字类型
        String recourseTime = context.getOptionalString("resourceTotalTime").orElse(null);
        if (studyTime == null || logId == null || recourseTime == null) {
            logger.info("studyTime: {}, logId: {}, resourceTotalTime: {},memberId: {},agent:{}",
                    studyTime, logId, recourseTime, subject.getCurrentUserId(), agent);
            return null;
        }
        if (!StringUtils.isNumeric(recourseTime)) {
            return null;
        }
        // add by wangdongyan 11-29 学习时长统计更新
        Integer configMaxTime = Optional.ofNullable(ruleMap.get(RuleConfig.KEY.PC_STUDY_TIME_COUNT_RULE.name())).map(r -> Integer.parseInt(r.getValue())).orElse(0);
//        Integer configMaxTime = ruleConfigService.getByName(orgId, RuleConfig.KEY.PC_STUDY_TIME_COUNT_RULE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        // 查询对应分表中的log数据
        String currentUserId = subject.getCurrentUserId();
        String path = courseCacheService.getPath(currentUserId);
//        TableImpl<?> table = courseCacheService.getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
        CourseStudyLog log = courseProcessService.findCourseSectionLog(logId, path);

        if (log == null) {
            throw new CourseStudyException(CourseExceptionService.REMARK_STUDY_LOG_NOEXISTS);
        }
        this.sendMsgSurvival(log.getCourseId(),currentUserId);
        //单窗口播放限制
        String key = "PC_ONLY_ONE_WINDOW_PLAY";
        Integer onlyOneWinPlayConfig = Optional.ofNullable(ruleMap.get(key)).map(r -> Integer.parseInt(r.getValue())).orElse(0);

        Integer play;
        //未启用配置,继续播放
        if (Objects.equals(onlyOneWinPlayConfig, 0)) {
            play = 1;
        } else {
            play = courseProcessService.onlyOneVideoPlayLimitLog(log.getId(), subject.getCurrentUserId());
        }

        if (log.getClientType() != CourseSectionStudyLog.CLIENT_TYPE_PC) {
            configMaxTime = Optional.ofNullable(ruleMap.get(RuleConfig.KEY.APP_STUDY_TIME_COUNT_RULE.name())).map(r -> Integer.parseInt(r.getValue())).orElse(0);
//        	configMaxTime = ruleConfigService.getByName(orgId, RuleConfig.KEY.APP_STUDY_TIME_COUNT_RULE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        }
        Integer resourceTotalTime = Integer.valueOf(recourseTime);
        try {
            CourseSectionStudyProgress result = courseProcessService.mediaProcess(
                    log,
                    studyTime,
                    resourceTotalTime,
                    context.getOptionalInteger("lessonLocation").orElse(0),
                    configProcess, configPlay, configMaxTime, path
            );
            if (context.getOptionalString("flush").isPresent()) {
                // updated by wangdongyan 优化时长使用
                if (result != null) {
                    logger.info("media类型的课件，是否更新的t_course_study_progress表呢，courseId={},memberId={}, totalTime={}", result.getCourseId(), result.getMemberId(), result.getStudyTotalTime());
                    courseProcessService.updateCourseStudyAsyn(
                            result.getCourseId(), result.getMemberId()
                    );
                }
            }
            //返回前端是否继续播放
            if (Objects.nonNull(result)) {
                result.setPlay(play);
            }
            return result;
        } catch (
                CourseStudyException e) {
            String ip = context.getRequest().getRemoteAddr();
//            courseExceptionService.save(subject.getCurrentUserId(), logId, studyTime, ip, e.getRemark());
            messageSender.send(MessageTypeContent.COURSE_EXCEPTION_INSTER,
                    MessageHeaderContent.MEMBER_ID, subject.getCurrentUserId(),
                    MessageHeaderContent.ID, logId,
                    MessageHeaderContent.STUDYTIME, studyTime + "",
                    MessageHeaderContent.IP, ip,
                    MessageHeaderContent.PARAMS, e.getRemark());
//            if(e.getRemark().equals(CourseExceptionService.REMARK_STUDY_OFTEN)) {
//                logOut(context);
//            }
//            throw e;
            return null;
        }
    }

    /**
     * 限流限频：发送MQ课程续命
     * @param courseId 课程Id
     * @param memberId 当前用户Id
     */
    private void sendMsgSurvival(String courseId, String memberId){
        if(SwitchConfig.getSwitchStatus(FlowLimitingSwitch)){
            logger.info( "课程续命课程id{},用户Id{}", courseId, memberId );
            MsgParameter msgParameter=new MsgParameter();
            msgParameter.setBusinessId(courseId);
            msgParameter.setMemberId(memberId);
            messageSender.send(COURSE_DISTRIBUTE_SURVIVAL,msgParameter);
        }
    }


    /**
     * 课程详情 音视频更新 （chrome浏览器关闭页面时使用）
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/video-progress-new", method = RequestMethod.POST)
    @Param(name = "logId")
    @Param(name = "studyTime", required = true)
    @Param(name = "resourceTotalTime", required = true)
    @Param(name = "lessonLocation")
    @Param(name = "organizationId")
    @Param(name = "flush")
    // 是否更新课程进度
    @Param(name = "remove")
    // 是否需要清空当前播放课程缓存信息 1:清理
    @JSON("*")
    public CourseSectionStudyProgress videoProgressNew(RequestContext context) {
        logger.info("chrome关闭调用新接口");
        AccessToken token = getAccessToken(context.getRequest(), true);
        if (token == null) {
            throw new AuthenticationException();
        }
        // add 2020-05-19 chrome 解密开始
        String orgId = Optional.ofNullable(this.getDecryptParam(context.getOptionalString("organizationId"))).orElse("1");
        String logId = this.getDecryptParam(context.getOptionalString("logId"));
        String studyTimeParam = this.getDecryptParam(context.getOptionalString("studyTime"));
        Integer studyTime = ObjectUtils.isEmpty(studyTimeParam) ? null : Integer.valueOf(studyTimeParam);
        String recourseTime = this.getDecryptParam(context.getOptionalString("resourceTotalTime"));
        String lessonLocationParam = this.getDecryptParam(context.getOptionalString("lessonLocation"));
        Integer lessonLocation = ObjectUtils.isEmpty(lessonLocationParam) ? 0 : Integer.valueOf(lessonLocationParam);
        String removeParam = this.getDecryptParam(context.getOptionalString("remove"));
        Integer remove = ObjectUtils.isEmpty(removeParam) ? 0 : Integer.valueOf(removeParam);
        // 解密结束

        String memberId = token.getUserId();

        // 音视频完成标准查询
        Map<String, RuleConfig> ruleMap = ruleConfigService.findAll(orgId, Optional.of(RuleConfig.STATUS_ENABLED)).stream().collect(Collectors.toMap(RuleConfig::getKey, r -> r, (key1, key2) -> key1));
        int configProcess = Optional.ofNullable(ruleMap.get(RuleConfig.KEY.VIDEO_PROCESS_THRESHOLD.name())).map(r -> Integer.parseInt(r.getValue())).orElse(50);
        int configPlay = Optional.ofNullable(ruleMap.get(RuleConfig.KEY.VIDEO_PALY_THRESHOLD.name())).map(r -> Integer.parseInt(r.getValue())).orElse(50);

        String agent = context.getRequest().getHeader("User-Agent");
        logger.info("chrome-media音视频学习，logId={}，studyTime={}", logId, studyTime);
        // updated by wangdongyan 以防前台传的的参数不能转换为数字类型
        if (studyTime == null || logId == null || recourseTime == null) {
            logger.info("studyTime: {}, logId: {}, resourceTotalTime: {},memberId: {},agent:{}",
                    studyTime, logId, recourseTime, memberId, agent);
            return null;
        }
        if (!StringUtils.isNumeric(recourseTime)) {
            return null;
        }
        // add by wangdongyan 11-29 学习时长统计更新
        Integer configMaxTime = Optional.ofNullable(ruleMap.get(RuleConfig.KEY.PC_STUDY_TIME_COUNT_RULE.name())).map(r -> Integer.parseInt(r.getValue())).orElse(0);
//        Integer configMaxTime = ruleConfigService.getByName(orgId, RuleConfig.KEY.PC_STUDY_TIME_COUNT_RULE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        // 查询对应分表中的log数据
        String path = courseCacheService.getPath(memberId);
//        TableImpl<?> table = courseCacheService.getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
        CourseStudyLog log = courseProcessService.findCourseSectionLog(logId, path);
        if (log == null) {
            throw new CourseStudyException(CourseExceptionService.REMARK_STUDY_LOG_NOEXISTS);
        }
        //单窗口播放限制
        String key = "PC_ONLY_ONE_WINDOW_PLAY";
        Integer onlyOneWinPlayConfig = Optional.ofNullable(ruleMap.get(key)).map(r -> Integer.parseInt(r.getValue())).orElse(0);

        Integer play;
        //未启用配置,继续播放
        if (Objects.equals(onlyOneWinPlayConfig, 0)) {
            play = 1;
        } else {
            play = courseProcessService.onlyOneVideoPlayLimitLog(log.getId(), memberId);
        }

        if (log.getClientType() != CourseSectionStudyLog.CLIENT_TYPE_PC) {
            configMaxTime = Optional.ofNullable(ruleMap.get(RuleConfig.KEY.APP_STUDY_TIME_COUNT_RULE.name())).map(r -> Integer.parseInt(r.getValue())).orElse(0);
//        	configMaxTime = ruleConfigService.getByName(orgId, RuleConfig.KEY.APP_STUDY_TIME_COUNT_RULE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        }
        Integer resourceTotalTime = Integer.valueOf(recourseTime);
        try {
            CourseSectionStudyProgress result = courseProcessService.mediaProcess(
                    log,
                    studyTime,
                    resourceTotalTime,
                    lessonLocation,
                    configProcess, configPlay, configMaxTime, path
            );
            if (context.getOptionalString("flush").isPresent()) {
                // updated by wangdongyan 优化时长使用
                if (result != null) {
                    logger.info("chrome--media类型的课件，是否更新的t_course_study_progress表呢，courseId={},memberId={}, totalTime={}", result.getCourseId(), result.getMemberId(), result.getStudyTotalTime());
                    courseProcessService.updateCourseStudyAsyn(
                            result.getCourseId(), result.getMemberId()
                    );
                }
            }
            //返回前端是否继续播放
            if (Objects.nonNull(result)) {
                result.setPlay(play);
            }
            return result;
        } catch (
                CourseStudyException e) {
            String ip = context.getRequest().getRemoteAddr();
//            courseExceptionService.save(subject.getCurrentUserId(), logId, studyTime, ip, e.getRemark());
            messageSender.send(MessageTypeContent.COURSE_EXCEPTION_INSTER,
                    MessageHeaderContent.MEMBER_ID, memberId,
                    MessageHeaderContent.ID, logId,
                    MessageHeaderContent.STUDYTIME, studyTime + "",
                    MessageHeaderContent.IP, ip,
                    MessageHeaderContent.PARAMS, e.getRemark());
//            if(e.getRemark().equals(CourseExceptionService.REMARK_STUDY_OFTEN)) {
//                logOut(context);
//            }
//            throw e;
            return null;
        }
    }

    /**
     * 音視頻關閉窗口清空緩存信息.
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/clear-video-cache", method = RequestMethod.GET)
    @Param(name = "id")
    @Param(name = "type")
    // 1:课程 0:章节
    @JSON("*")
    public JSONObject clearVideoCache(RequestContext context, Subject<Member> subject) {
        String id = context.getString("id");
        Integer type = context.getInteger("type");
        courseProcessService.onlyOneVideoPlayLimit(id, subject.getCurrentUserId(), 1, type);
        return null;
    }

    /**
     * 播放音視頻设置緩存信息.
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/set-video-cache", method = RequestMethod.GET)
    @Param(name = "id")
    // 章节ID
    @JSON("*")
    public JSONObject setVideoCache(RequestContext context, Subject<Member> subject) {
        String id = context.getString("id");
        courseProcessService.onlyOneVideoPlaySetCache(id, subject.getCurrentUserId());
        return null;
    }

    /**
     * 离线学习  批量学习
     *
     * @date 2017-12-06
     */
    @RequestMapping(value = "/batch-study-progress", method = RequestMethod.POST)
    @Param(name = "data", required = true)
    // data = [{courseId,sectionId,studyTime,resourcesTime,lessonLocation,createTime}]
    @Param(name = "organizationId", required = true)
    @JSON("success")
    public Object batchStudyProgress(RequestContext context, Subject<Member> subject) {
        String jsonData = context.getString("data");
        String memberId = subject.getCurrentUserId();
        String comments = "离线学习";
        String orgId = context.getOptionalString("organizationId").orElse("1");
        List<JSONObject> jsonObjects = com.alibaba.fastjson.JSON.parseArray(jsonData, JSONObject.class);
        List<CourseStudyLog> items = jsonObjects.stream().map(x -> {
            CourseStudyLog log = new CourseStudyLog();
            log.setCourseId(x.getString("courseId"));
            log.setSectionId(x.getString("sectionId"));
            log.setStudyTime(x.getInteger("studyTime"));
            log.setLessonLocation(x.getString("lessonLocation"));
            log.setCreateTime(x.getLong("createTime"));
            log.setResourceTotalTime(x.getInteger("resourcesTime"));
            log.setMemberId(memberId);
            log.setComments(comments);
            return log;
        }).collect(Collectors.toList());

        boolean flag = items.stream().anyMatch(x ->
                x.getCourseId() == null || x.getSectionId() == null
                        || x.getStudyTime() == null || x.getLessonLocation() == null || x.getCreateTime() == null || x.getResourceTotalTime() == null
        );
        if (flag) {
            throw new UnprocessableException(ErrorCode.DataViolation, "param is empty");
        }
        courseProcessService.batchStudyProgress(items, orgId);

        return ImmutableMap.of("success", items.size());
    }

    @RequestMapping(value = "/course-progress-update", method = RequestMethod.POST)
//    @SecurityParam
    @Param(name = "courseId", required = true, security = true)
    @JSON("success")
    public Object updateCourse(RequestContext context, Subject<Member> subject) {
        String courseId = context.getString("courseId");
        String memberId = subject.getCurrentUserId();
        String agent = context.getRequest().getHeader("User-Agent");
        // 暂时添加到异常表待分析
//        courseExceptionService.save(memberId,courseId,0,"keeley","更新课程"+agent);
        logger.info("异步请求时更新的，时长memberId={},courseId={}", subject.getCurrentUserId(), courseId);
        courseProcessService.updateCourseStudyAsyn(courseId, memberId);
        return ImmutableMap.of("success", true);
    }

    @RequestMapping(value = "/course-progress-update-async", method = RequestMethod.POST)
    //@SecurityParam
    @Param(name = "courseId", required = true)
    @JSON("success")
    public Object updateCourseAsync(RequestContext context) {
        logger.error("invoke course-progress-update-async");
        String courseId = null;
        try {
            courseId = Encrypt.Decrypt(context.getString("courseId"), aesKey);
            logger.error("courseId:" + courseId);
        } catch (
                Exception e) {
            logger.error("course-progress-update-async解密失败");
            return ImmutableMap.of("failure", true);
        }

        AccessToken token = getAccessToken(context.getRequest(), true);
        if (token == null) {
            throw new AuthenticationException();
        }
        String memberId = token.getUserId();
        logger.error("memberId:" + memberId);
        String agent = context.getRequest().getHeader("User-Agent");
        // 暂时添加到异常表待分析
//        courseExceptionService.save(memberId,courseId,0,"keeley","更新课程"+agent);
        logger.info("async异步请求时更新的，时长memberId={},courseId={}", token.getUserId(), courseId);
        courseProcessService.updateCourseStudyAsyn(courseId, memberId);
        return ImmutableMap.of("success", true);
    }

    /**
     * 课程详情 更新普通文档等
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/doc-progress", method = RequestMethod.POST)
    @Permitted
//    @SecurityParam
    @Param(name = "logId", security = true)
    @Param(name = "lessonLocation", type = String.class, security = true)
    @Param(name = "studyTime", type = Integer.class, security = true)
    // 学习时间
    @Param(name = "flush", security = true)
    // 是否更新课程进度
    @JSON("*")
    public CourseSectionStudyProgress docProgress(RequestContext context, Subject<Member> subject) {
        String memberId = subject.getCurrentUserId();
        String logId = context.getOptionalString("logId").orElse(null);
        logger.info("doc文档学习，logId={}", logId);
        if (logId == null) {
            String agent = context.getRequest().getHeader("User-Agent");
            logger.debug("docProgress logId is null currentId:{},agent:{}", subject.getCurrentUserId(), agent);
            return null;
        }
        Integer studyTime = context.getOptionalInteger("studyTime").orElse(0);
        // add by wangdongyan 11-29 学习时长统计更新
        RuleConfig PCStudyTimeCountRuleConfig =
                cacheSystem.get(RuleConfigRedisKeyUtil.getByNameRedisKey(RuleConfig.KEY.PC_STUDY_TIME_COUNT_RULE), RuleConfig.class);
        if(Objects.isNull(PCStudyTimeCountRuleConfig)) {
            PCStudyTimeCountRuleConfig = ruleConfigService.getByName(subject.getRootOrganizationId(), RuleConfig.KEY.PC_STUDY_TIME_COUNT_RULE).orElse(null);
        }
        Integer configMaxTime = Optional.ofNullable(PCStudyTimeCountRuleConfig).map(x-> Integer.parseInt(x.getValue())).orElse(0);

        // 查询对应分表中的log数据
        String currentUserId = subject.getCurrentUserId();
        String path = courseCacheService.getPath(currentUserId);
//        TableImpl<?> table = courseCacheService.getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
        CourseStudyLog log = courseProcessService.findCourseSectionLog(logId, path);
        if (log == null) {
            throw new CourseStudyException(CourseExceptionService.REMARK_STUDY_LOG_NOEXISTS);
        }
        this.sendMsgSurvival(log.getCourseId(),subject.getCurrentUserId());
        if (log.getClientType() != CourseSectionStudyLog.CLIENT_TYPE_PC) {
            RuleConfig APPStudyTimeCountRuleConfig =
                    cacheSystem.get(RuleConfigRedisKeyUtil.getByNameRedisKey(RuleConfig.KEY.APP_STUDY_TIME_COUNT_RULE), RuleConfig.class);
            if(Objects.isNull(APPStudyTimeCountRuleConfig)) {
                APPStudyTimeCountRuleConfig = ruleConfigService.getByName(subject.getRootOrganizationId(), RuleConfig.KEY.APP_STUDY_TIME_COUNT_RULE).orElse(null);
            }
        	configMaxTime = Optional.ofNullable(APPStudyTimeCountRuleConfig).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        }
        try {
            CourseSectionStudyProgress result = courseProcessService.docProcess(log,
                    context.getOptionalString("lessonLocation"),
                    context.getOptionalInteger("studyTime"),
                    configMaxTime,
                    path
            );
            if (context.getOptionalString("flush").isPresent()) {
                // updated by wangdongyan 优化时长使用
                if (result != null) {
                    logger.info("doc类型的课件的学习，是否更新的t_course_study_progress表呢，courseId={},memberId={}, totalTime={}", result.getCourseId(), result.getMemberId(), result.getStudyTotalTime());
                    courseProcessService.updateCourseStudyAsyn(
                            result.getCourseId(), result.getMemberId());
                }
            }
            return result;
        } catch (
                CourseStudyException e) {
            String ip = context.getRequest().getRemoteAddr();
//            courseExceptionService.save(memberId,logId,studyTime,ip,e.getRemark());
            messageSender.send(MessageTypeContent.COURSE_EXCEPTION_INSTER,
                    MessageHeaderContent.MEMBER_ID, memberId,
                    MessageHeaderContent.ID, logId,
                    MessageHeaderContent.STUDYTIME, studyTime + "",
                    MessageHeaderContent.IP, ip,
                    MessageHeaderContent.PARAMS, e.getRemark());
//            if(e.getRemark().equals(CourseExceptionService.REMARK_STUDY_OFTEN))
//                logOut(context);
//            throw e;
            return null;
        }

    }

    /**
     * 课程详情 更新普通文档等（注解无法解密，在解密拦截器中request实例无法转换对应的实体）
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/doc-progress-new", method = RequestMethod.POST)
    @Param(name = "logId")
    @Param(name = "lessonLocation", type = String.class)
    @Param(name = "studyTime")
    // 学习时间
    @Param(name = "flush")
    // 是否更新课程进度
    @JSON("*")
    public CourseSectionStudyProgress docProgressNew(RequestContext context) throws Exception {
        logger.info("chrome关闭调用新接口");
        AccessToken token = getAccessToken(context.getRequest(), true);
        if (token == null) {
            throw new AuthenticationException();
        }

        String memberId = token.getUserId();
        String logId = context.getOptionalString("logId").orElse(null);
        if (logId == null) {
            String agent = context.getRequest().getHeader("User-Agent");
            logger.debug("chrome--docProgress logId is null currentId:{},agent:{}", memberId, agent);
            return null;
        }

        // add 2020-05-19 chrome80版本解密前端传的加密参数
        logId = this.getDecryptParam(Optional.of(logId));
        logger.info("chrome--doc文档学习，logId={}", logId);
        String lessonLocation = this.getDecryptParam(context.getOptionalString("lessonLocation"));
        String studyTimeParam = this.getDecryptParam(context.getOptionalString("studyTime"));
        Optional<Integer> studyTime = ObjectUtils.isEmpty(studyTimeParam) ? Optional.empty() : Optional.of(Integer.valueOf(studyTimeParam));
        // chrome80版本解密结束

        // add by wangdongyan 11-29 学习时长统计更新
        RuleConfig PCStudyTimeCountRuleConfig =
                cacheSystem.get(RuleConfigRedisKeyUtil.getByNameRedisKey(RuleConfig.KEY.PC_STUDY_TIME_COUNT_RULE), RuleConfig.class);
        if(Objects.isNull(PCStudyTimeCountRuleConfig)) {
            PCStudyTimeCountRuleConfig = ruleConfigService.getByName("1", RuleConfig.KEY.PC_STUDY_TIME_COUNT_RULE).orElse(null);
        }
        Integer configMaxTime = Optional.ofNullable(PCStudyTimeCountRuleConfig).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        // 查询对应分表中的log数据
        String path = courseCacheService.getPath(memberId);
//        TableImpl<?> table = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
        CourseStudyLog log = courseProcessService.findCourseSectionLog(logId, path);

        if (log == null) {
            throw new CourseStudyException(CourseExceptionService.REMARK_STUDY_LOG_NOEXISTS);
        }
        if (log.getClientType() != CourseSectionStudyLog.CLIENT_TYPE_PC) {
            RuleConfig APPStudyTimeCountRuleConfig =
                    cacheSystem.get(RuleConfigRedisKeyUtil.getByNameRedisKey(RuleConfig.KEY.APP_STUDY_TIME_COUNT_RULE), RuleConfig.class);
            if(Objects.isNull(APPStudyTimeCountRuleConfig)) {
                APPStudyTimeCountRuleConfig = ruleConfigService.getByName("1", RuleConfig.KEY.APP_STUDY_TIME_COUNT_RULE).orElse(null);
            }
            configMaxTime = Optional.ofNullable(APPStudyTimeCountRuleConfig).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        }
        try {
            CourseSectionStudyProgress result = courseProcessService.docProcess(log,
                    Optional.ofNullable(lessonLocation),
                    studyTime,
                    configMaxTime,
                    path
            );
            if (context.getOptionalString("flush").isPresent()) {
                // updated by wangdongyan 优化时长使用
                if (result != null) {
                    logger.info("chrome--doc类型的课件的学习，是否更新的t_course_study_progress表呢，courseId={},memberId={}, totalTime={}", result.getCourseId(), result.getMemberId(), result.getStudyTotalTime());
                    courseProcessService.updateCourseStudyAsyn(
                            result.getCourseId(), result.getMemberId());
                }
            }
            return result;
        } catch (
                CourseStudyException e) {
            String ip = context.getRequest().getRemoteAddr();
//            courseExceptionService.save(memberId,logId,studyTime,ip,e.getRemark());
            messageSender.send(MessageTypeContent.COURSE_EXCEPTION_INSTER,
                    MessageHeaderContent.MEMBER_ID, memberId,
                    MessageHeaderContent.ID, logId,
                    MessageHeaderContent.STUDYTIME, studyTime.orElse(0) + "",
                    MessageHeaderContent.IP, ip,
                    MessageHeaderContent.PARAMS, e.getRemark());
//            if(e.getRemark().equals(CourseExceptionService.REMARK_STUDY_OFTEN))
//                logOut(context);
//            throw e;
            return null;
        }

    }

    /**
     * 专题URL进度更新 - 打开即完成
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/url-progress", method = RequestMethod.POST)
    @Permitted
    @Param(name = "sectionId", required = true)
    @Param(name = "clientType", required = true, type = Integer.class)
    @Param(name = "beginTime", required = true, type = Long.class)
    @Param(name = "lessonLocation", type = Integer.class)
    @JSON("*")
    public CourseSectionStudyProgress urlProgress(RequestContext context, Subject<Member> subject) {
        //获取重塑专区相关配置
        List<ThirdPartyConfig> thirdPartyConfigs = thirdPartyConfigService.findConfigList(ThirdPartyConfig.BUSINESS_CODE);
        List<String> domains = thirdPartyConfigs.stream().map(r -> r.getDomain()).collect(Collectors.toList());
        return courseStudyProgressService.updateUrlStudyProgress(
                subject.getCurrentUserId(),
                context.getString("sectionId"),
                context.getInteger("clientType"),
                context.getOptionalInteger("lessonLocation"),
                context.get("beginTime", Long.class),
                domains
        );
    }


    /**
     * 智能播报进度更新 - 打开即完成
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/broadcast-progress", method = RequestMethod.POST)
    @Permitted
    @Param(name = "sectionId", required = true)
    @Param(name = "clientType", required = true, type = Integer.class)
    @Param(name = "beginTime", required = true, type = Long.class)
    @Param(name = "lessonLocation", type = Integer.class)
    @JSON("*")
    public CourseSectionStudyProgress broadcastProgress(RequestContext context, Subject<Member> subject) {
        //获取重塑专区相关配置
        return courseStudyProgressService.updateBroadcastStudyProgress(
                subject.getCurrentUserId(),
                context.getString("sectionId"),
                context.getInteger("clientType"),
                context.getOptionalInteger("lessonLocation"),
                context.get("beginTime", Long.class)
        );
    }

    /**
     * 提交作业审核
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/submit-progress", method = RequestMethod.POST)
    @Permitted
    @Param(name = "sectionId", required = true)
    @Param(name = "clientType", required = true, type = Integer.class)
    @Param(name = "attachments")
    @JSON("*")
    public CourseSectionStudyProgress submitProgress(RequestContext context, Subject<Member> subject) {
        return courseStudyProgressService.submitProgress(
                context.getString("sectionId"),
                subject.getCurrentUserId(),
                context.getInteger("clientType"),
                parseAttachments(context.getOptionalString("attachments"))
        );
    }

    /**
     * 开始学习记录 作业学习时的记录
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/start-study", method = RequestMethod.POST)
    @Permitted
    @Param(name = "sectionId", required = true)
    @Param(name = "clientType", required = true, type = Integer.class)
    @JSON("*")
    public CourseSectionStudyProgress startStudy(RequestContext context, Subject<Member> subject) {
        String sectionId = context.getString("sectionId");
        // 自动回源
        Optional.ofNullable(courseCacheService.getSection(sectionId)).ifPresent(s -> {
            if (Objects.nonNull(s.getCourseId())) {
                ArchivedKit.autoBackToCourse(context.getRequest(), s.getCourseId(), true, subject.getCurrentUserId());
            }
        });
        return courseStudyProgressService.startStudy(
                sectionId,
                subject.getCurrentUserId(),
                context.getInteger("clientType")
        );
    }

    @RequestMapping(value = "/update-media-time", method = RequestMethod.POST)
    @Param(name = "sectionId", required = true)
    @Param(name = "duration", type = Integer.class, required = true)
    @JSON("*")
    public Object updateMediaTime(RequestContext context) {
        courseProcessService.updateMediaLength(
                context.getString("sectionId"),
                context.getInteger("duration")
        );
        return ImmutableMap.of("success", true);
    }

    /**
     * 根据id查询节信息
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/section/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("id,name,courseId,chapterId,sectionType,resourceId,referenceId")
    @JSON("studyTask.(*)")
    @JSON("studyTask.attachments.(*)")
    @JSON("progress.(*)")
    @JSON("progress.sectionAttachments.(*)")
    public CourseChapterSection getSectionByIds(RequestContext context, Subject<Member> subject) {
        return courseInfoService.getSectionByIds(context.getString("id"), subject.getCurrentUserId());
    }

    private Optional<List<CourseSectionProgressAttachment>> parseAttachments(Optional<String> sectionAttachments) {
        List<CourseSectionProgressAttachment> attachments = Lists.newArrayList();
        try {
            if (sectionAttachments.isPresent()) {
                attachments = com.alibaba.fastjson.JSON.parseArray(sectionAttachments.get(), CourseSectionProgressAttachment.class);
            }
        } catch (
                Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        return Optional.ofNullable(attachments);
    }

    private void logOut(RequestContext context) {
        String token = context.getRequest().getHeader("Authorization");
        System.out.println(token);
        if (token != null && token.indexOf("Bearer__") != -1) {
            token = token.split("Bearer__")[1];
            Redis.del("oauth-provider#oauth#" + token);
        }
    }

    /**
     * 业务评分接口
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/score", method = RequestMethod.POST)
    @Permitted
    @Param(name = "businessId", required = true)
    @Param(name = "businessType", required = true, type = Integer.class)
    @Param(name = "score", required = true, type = Integer.class)
    @JSON("avgScore")
    public Map<String, Integer> score(RequestContext context, Subject<Member> subject) {

        if (!(context.getInteger("score") != null && context.getInteger("score") >= 0 && context.getInteger("score") <= 10))
            throw new ValidationException(ErrorCode.ScoreError);

        Integer avgScore = courseInfoService.insertCourseScore(
                subject.getCurrentUserId(),
                context.getString("businessId"),
                context.getInteger("businessType"),
                context.getInteger("score"));
        return ImmutableMap.of("avgScore", avgScore);
    }

    /**
     * 查询专题广告
     *
     * @return
     */
    @RequestMapping(value = "/subject-news", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("*")
    @JSON("member.(*)")
    public SubjectAdvertising getSubjectAdvertising(RequestContext context, Subject<Member> subject) {
        return subjectAdvertisingService.get(context.getString("id"));
    }


    /**
     * 待办中查询当前用户的作业批阅
     *
     * @return
     */
    @RequestMapping(value = "/audit-works", method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "finishStatus", type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(*)")
    @JSON("items.courseInfo.(businessType)")
    @JSON("items.courseChapterSection.(name,resourceId)")
    @JSON("items.member.(fullName)")
    @JSON("items.member.organization.(name)")
    public PagedResult<CourseSectionStudyProgress> findWorksByAuditMemberId(RequestContext context, Subject<Member> subject) {
        return courseStudyProgressService.findWorksByAuditMemberId(context.getInteger("page"),
                context.getInteger("pageSize"),
                subject.getCurrentUserId(),
                context.getOptionalInteger("finishStatus"));
    }


    @RequestMapping(value = "/start-progress/{id}", method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "clientType", type = Integer.class, required = true)
    @Param(name = "createTime", type = Long.class)
    @JSON("id")
    public CourseStudyLog startProgress(RequestContext context, Subject<Member> subject) {
        String id = context.getString("id");
        Integer clientType = context.getInteger("clientType");
        String memberId = subject.getCurrentUserId();
        Long createTime = context.getOptional("createTime", Long.class).orElse(System.currentTimeMillis());
        try {
            String path = courseCacheService.getPath(memberId);
            // 自动回源
            Optional.ofNullable(courseSectionCache.get("start-progress-id#" + id, () -> courseCacheService.getCourseId(id), 60 * 2)).ifPresent(s -> {
                if (Objects.nonNull(s)) {
                    ArchivedKit.autoBackToCourse(context.getRequest(), s, true, subject.getCurrentUserId());
                }
            });

            return courseProcessService.startSectionLog(memberId, id, clientType, createTime, path);
        } catch (
                CourseStudyException e) {
            String ip = context.getRequest().getRemoteAddr();
//            courseExceptionService.save(memberId,id,null,ip,e.getRemark());
            messageSender.send(MessageTypeContent.COURSE_EXCEPTION_INSTER,
                    MessageHeaderContent.MEMBER_ID, memberId,
                    MessageHeaderContent.ID, id,
                    MessageHeaderContent.STUDYTIME, 0 + "",
                    MessageHeaderContent.IP, ip,
                    MessageHeaderContent.PARAMS, e.getRemark());
        } catch (
                RuntimeException ee) {
        }
        return null;
    }

    /**
     * 查询专题章节信息包含进度
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/chapter-progress", method = RequestMethod.GET)
    @Permitted
    @Param(name = "courseId", required = true)
    @Param(name = "versionId", required = true)
    @Param(name = "isRegister", type = Boolean.class)
    @JSON("*")
    @JSON("courseChapterSections.(*)")
    @JSON("courseChapterSections.progress.(*)")
    public List<CourseChapter> findSubjectChapterForFront(RequestContext context, Subject<Member> subject) {
        //获取重塑专区相关配置
        List<ThirdPartyConfig> thirdPartyConfigs = chapterProgressCache.get("thirdPartyConfig#"+ThirdPartyConfig.BUSINESS_CODE, () -> thirdPartyConfigService.findConfigList(ThirdPartyConfig.BUSINESS_CODE), 60 * 10);
        List<String> domains = thirdPartyConfigs.stream().map(r -> r.getDomain()).collect(Collectors.toList());
        String courseId = context.getString("courseId");
        List<CourseChapter> courseChapters = courseInfoService.findSubjectChapterNoContainReturn(courseId,
                subject.getCurrentUserId(), context.getString("versionId"),
                context.getOptional("isRegister", Boolean.class).orElse(false),
                domains
        );
        // 查询主题绑定的评分表
        Map<String, MultidimensionalScoring> scoringMap = multidimensionalScoringService.findScoringIdAndNameBySubjectId(courseId, Optional.empty()).stream()
                .filter(c -> Objects.nonNull(c.getTopicId())).collect(Collectors.toMap(MultidimensionalScoring::getTopicId, v -> v, (v1, v2) -> v2));
        // 查询有没有当前人的所有评分记录
        Map<String, String> presenceScoreMap = Optional.ofNullable(multidimensionalScoringService.presenceScore(courseId, subject.getCurrentUserId())).orElse(new HashMap<>());
        Set<String> keySet = presenceScoreMap.keySet();
        courseChapters.forEach(courseChapter -> {
            Optional<MultidimensionalScoring> multidimensionalScoringOpt = Optional.ofNullable(scoringMap.get(courseChapter.getId()));
            multidimensionalScoringOpt.ifPresent(multidimensionalScoring -> {
                courseChapter.setScoringId(multidimensionalScoring.getId());
                courseChapter.setScoringName(multidimensionalScoring.getName());
            });
            Optional.ofNullable(courseChapter.getCourseChapterSections()).orElse(new ArrayList<>()).forEach(courseChapterSection -> {
                String key = Optional.ofNullable(courseChapterSection.getResourceId()).orElse("").concat("_").concat(courseChapter.getId());
                courseChapterSection.setFinishStatus(Optional.ofNullable(courseChapterSection.getFinishStatus()).orElse(CourseStudyProgress.FINISH_STATUS_DEFAULT));
                if (keySet.contains(key))
                    courseChapterSection.setScoringSheetId(presenceScoreMap.get(key));
            });
        });
        return courseChapters;
    }

    @RequestMapping(value = "/chapter-progress-simple", method = RequestMethod.GET)
    @Permitted
    @Param(name = "courseId", required = true)
    @JSON("courseChapterSections.(id,name,coverPath,sectionType,resourceId,referenceId,url)")
    @JSON("courseChapterSections.progress.(id,progressPercentage,finishStatus)")
    public List<CourseChapter> findSubjectSimpleChapterForFront(RequestContext context, Subject<Member> subject) {
        List<CourseChapter> courseChapters = courseInfoService.findSubjectSimpleChapterForFront(
                context.getString("courseId"),
                subject.getCurrentUserId()
        );
        //  封面填充
        List<String> resourceIds = courseChapters
                .stream().map(CourseChapter::getCourseChapterSections)
                .filter(Objects::nonNull).flatMap(Collection::stream)
                .map(CourseChapterSectionEntity::getResourceId)
                .filter(Objects::nonNull)
                .collect(toList());
        Map<String, String> courseInfoCoverPath = courseInfoService.getMapCoverPaths(resourceIds);
        Map<String, String> knowledgeCoverPath = knowledgeService.findCoverByIds(resourceIds);
        Map<String, String> examCoverPath = examService.findCoverByIds(resourceIds);

        courseChapters.forEach(courseChapter -> {
            Optional.ofNullable(courseChapter.getCourseChapterSections()).orElse(new ArrayList<>())
                    .forEach(courseChapterSection -> {
                        String resourceId = courseChapterSection.getResourceId();
                        if (Objects.isNull(courseChapterSection.getCoverPath())) {
                            courseChapterSection.setCoverPath(courseInfoCoverPath.getOrDefault(resourceId, knowledgeCoverPath.getOrDefault(resourceId, examCoverPath.get(resourceId))));
                        }
                    });
        });
        return courseChapters;
    }


    @RequestMapping(value = "/study-map-chapter-progress", method = RequestMethod.GET)
    @Param(name = "courseId", required = true)
    @Param(name = "versionId", required = true)
    @Param(name = "isRegister", type = Boolean.class)
    @JSON("*")
    @JSON("courseChapterSections.(*)")
    @JSON("courseChapterSections.progress.(*)")
    public List<CourseChapter> findStudyMapChapterByCourseId(RequestContext context, Subject<Member> subject) {
        //获取重塑专区相关配置
        List<ThirdPartyConfig> thirdPartyConfigs = chapterProgressCache.get("thirdPartyConfig#"+ThirdPartyConfig.BUSINESS_CODE, () -> thirdPartyConfigService.findConfigList(ThirdPartyConfig.BUSINESS_CODE), 60 * 10);
        List<String> domains = thirdPartyConfigs.stream().map(r -> r.getDomain()).collect(Collectors.toList());
        String courseId = context.getString("courseId");
        List<CourseChapter> courseChapters = courseInfoService.findSubjectChapterForFront(courseId,
            subject.getCurrentUserId(), context.getString("versionId"),
            context.getOptional("isRegister", Boolean.class).orElse(false),
            domains
        );
        // 查询主题绑定的评分表
        Map<String, MultidimensionalScoring> scoringMap = multidimensionalScoringService.findScoringIdAndNameBySubjectId(courseId, Optional.empty()).stream()
                                                                                        .filter(c -> Objects.nonNull(c.getTopicId())).collect(Collectors.toMap(MultidimensionalScoring::getTopicId, v -> v, (v1, v2) -> v2));
        // 查询有没有当前人的所有评分记录
        Map<String, String> presenceScoreMap = Optional.ofNullable(multidimensionalScoringService.presenceScore(courseId, subject.getCurrentUserId())).orElse(new HashMap<>());
        Set<String> keySet = presenceScoreMap.keySet();
        courseChapters.forEach(courseChapter -> {
            Optional<MultidimensionalScoring> multidimensionalScoringOpt = Optional.ofNullable(scoringMap.get(courseChapter.getId()));
            multidimensionalScoringOpt.ifPresent(multidimensionalScoring -> {
                courseChapter.setScoringId(multidimensionalScoring.getId());
                courseChapter.setScoringName(multidimensionalScoring.getName());
            });
            Optional.ofNullable(courseChapter.getCourseChapterSections()).orElse(new ArrayList<>()).forEach(courseChapterSection -> {
                String key = Optional.ofNullable(courseChapterSection.getResourceId()).orElse("").concat("_").concat(courseChapter.getId());
                courseChapterSection.setFinishStatus(Optional.ofNullable(courseChapterSection.getFinishStatus()).orElse(CourseStudyProgress.FINISH_STATUS_DEFAULT));
                if (keySet.contains(key))
                    courseChapterSection.setScoringSheetId(presenceScoreMap.get(key));
            });
        });
        List<String> resourceId = courseChapters.stream().filter(a -> a.getCourseChapterSections() != null).flatMap(a -> a.getCourseChapterSections().stream()).map(CourseChapterSectionEntity::getResourceId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, String> pathMap = Optional.ofNullable(courseInfoService.getCoverPaths(resourceId)).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(CourseInfoEntity::getId,v->Optional.ofNullable(v.getCoverPath()).orElse("")));
        // 增加判断能力完成状态
        courseChapters.forEach(courseChapter -> {
            courseChapter.setAbilityCompleted(Optional.ofNullable(courseChapter.getCourseChapterSections()).orElse(new ArrayList<>())
                    .stream().allMatch(c -> c.getProgress() != null && Objects.equals(c.getProgress().getFinishStatus(), CourseStudyProgress.FINISH_STATUS_FINISH)) ? 1 : 0);
            // 填充资源封面
            Optional.ofNullable(courseChapter.getCourseChapterSections()).orElse(new ArrayList<>()).forEach(courseChapterSection -> {
                if (courseChapterSection.getResourceId() != null) {
                    courseChapterSection.setCoverPath(Optional.ofNullable(pathMap.get(courseChapterSection.getResourceId())).orElse(""));
                }
            });
        });

        return courseChapters;
    }

    @RequestMapping(value = "/find-app-study-map-chapter-progress", method = RequestMethod.GET)
    @Param(name = "chapterId", required = true)
    @Param(name = "courseId", required = true)
    @JSON("*")
    @JSON("courseChapterSections.(*)")
    @JSON("courseChapterSections.progress.(*)")
    public List<CourseChapter> findStudyMapChapterInfoByChapterId(RequestContext context, Subject<Member> subject) {
        //获取重塑专区相关配置
        List<ThirdPartyConfig> thirdPartyConfigs = chapterProgressCache.get("thirdPartyConfig#"+ThirdPartyConfig.BUSINESS_CODE, () -> thirdPartyConfigService.findConfigList(ThirdPartyConfig.BUSINESS_CODE), 60 * 10);
        List<String> domains = thirdPartyConfigs.stream().map(r -> r.getDomain()).collect(Collectors.toList());
        List<CourseChapter> courseChapters = courseInfoService.findStudyMapChapterForFrontApp(
                context.getString("courseId"),
                subject.getCurrentUserId(),
                domains, context.getString("chapterId")
        );

        courseChapters.forEach(courseChapter -> {

            Optional.ofNullable(courseChapter.getCourseChapterSections()).orElse(new ArrayList<>()).forEach(courseChapterSection -> {
                courseChapterSection.setFinishStatus(Optional.ofNullable(courseChapterSection.getFinishStatus()).orElse(CourseStudyProgress.FINISH_STATUS_DEFAULT));
            });
        });
        List<String> resourceId = courseChapters.stream().filter(a -> a.getCourseChapterSections() != null).flatMap(a -> a.getCourseChapterSections().stream()).map(CourseChapterSectionEntity::getResourceId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, String> pathMap = Optional.ofNullable(courseInfoService.getCoverPaths(resourceId)).orElse(new ArrayList<>()).stream().collect(Collectors.toMap(CourseInfoEntity::getId,v->Optional.ofNullable(v.getCoverPath()).orElse("")));
        // 增加判断能力完成状态
        courseChapters.forEach(courseChapter -> {
            courseChapter.setAbilityCompleted(Optional.ofNullable(courseChapter.getCourseChapterSections()).orElse(new ArrayList<>())
                    .stream().anyMatch(c -> c.getProgress() != null && !Objects.equals(c.getProgress().getFinishStatus(), CourseStudyProgress.FINISH_STATUS_FINISH)) ? 0 : 1);
            // 填充资源封面
            Optional.ofNullable(courseChapter.getCourseChapterSections()).orElse(new ArrayList<>()).forEach(courseChapterSection -> {
                if (courseChapterSection.getResourceId() != null) {
                    courseChapterSection.setCoverPath(Optional.ofNullable(pathMap.get(courseChapterSection.getResourceId())).orElse(""));
                }
            });
        });

        return courseChapters;
    }


    /**
     * 分享数累计
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/count-share", method = RequestMethod.POST)
    @Permitted
    @Param(name = "courseId", required = true)
    @JSON("*")
    public Map<String, String> countShare(RequestContext context, Subject<Member> subject) {
        String courseId = context.getString("courseId");
        messageSender.send(MessageTypeContent.COURSE_STATISTICS_SHARE,
                MessageHeaderContent.ID, courseId,
                MessageHeaderContent.MEMBER_ID, subject.getCurrentUserId()
        );
        return ImmutableMap.of("courseId", courseId);
    }


    /**
     * 查询课程列表,需分页,排序,全局搜索查询，需查询非公开课程，新需求
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/full-search")
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "orderBy", type = Integer.class)
    //0:上架时间 1:访问人数 2:评分
    @Param(name = "order", type = Integer.class)
    //1:asc 2:desc
    @Param(name = "companyType", type = Integer.class)
    // 1:集团 2:本公司 3:其它公司
    @Param(name = "searchContent")
    // 搜索内容,现在只匹配了:课程名称和描述
    @Param(name = "categoryId")
    //课程目录id
    @Param(name = "topicId")
    //话题id
    @Param(name = "publishClient", type = Integer.class)
    // 1: PC, 2: APP
    @Param(name = "type", type = Integer.class, required = true)
    // 0:课程 2:专题
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,releaseTime,courseTime,source,status,cover,coverPath,beginDate,endDate,description,studyMemberCount,finishStatus,visits,avgScore,url,descriptionText)")
    public PagedResult<CourseInfo> findCourseInfoPageFullSearch(RequestContext context, Subject<Member> subject) {

        Optional<String> searchContentOptional = context.getOptionalString("searchContent");
        if (searchContentOptional.isPresent()){
            //查询开关是否开启,开启则搜索兜底返回null
            InternalSwitch internalSwitch = intelligentSearchCache.get(InternalSwitch.KEY + KEY, () -> internalSwitchService.findByType(InternalSwitch.intelligentSearch), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
            logger.info("查询搜索开关,实体:{}",internalSwitch);
            if (!ObjectUtils.isEmpty(internalSwitch) && Objects.equals(internalSwitch.getStatus(), InternalSwitch.switchStatusNO)) {
                logger.info("开关开启,返回null");
                return null;
            }
        }


        return courseInfoService.findFullSearch(subject.getCurrentUserId(),
                context.getInteger("page"),
                context.getInteger("pageSize"),
                context.getOptionalInteger("orderBy"),
                context.getOptionalInteger("order"),
                searchContentOptional,
                context.getOptionalString("categoryId"),
                context.getOptionalString("topicId"),
                context.getInteger("type"),
                context.getOptionalInteger("publishClient"),
                context.getOptionalInteger("companyType"));
    }

    /**
     * 查询课程列表,需分页,排序,全局搜索查询，需查询非公开课程，新需求，去除count查询
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/full-search-map")
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "orderBy", type = Integer.class)
    //0:上架时间 1:访问人数 2:评分
    @Param(name = "order", type = Integer.class)
    //1:asc 2:desc
    @Param(name = "companyType", type = Integer.class)
    // 1:集团 2:本公司 3:其它公司
    @Param(name = "searchContent")
    // 搜索内容,现在只匹配了:课程名称和描述
    @Param(name = "categoryId")
    //课程目录id
    @Param(name = "topicId")
    //话题id
    @Param(name = "publishClient", type = Integer.class)
    // 1: PC, 2: APP
    @Param(name = "type", type = Integer.class, required = true)
    // 0:课程 2:专题
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,releaseTime,courseTime,source,status,cover,coverPath,beginDate,endDate,description,studyMemberCount,finishStatus,visits,avgScore,url,descriptionText)")
    @JSON("more")
    public Map<String, Object> findCourseInfoPageFullSearchMap(RequestContext context, Subject<Member> subject) {
        return courseInfoService.findFullSearchMap(subject.getCurrentUserId(),
                context.getInteger("page"),
                context.getInteger("pageSize"),
                context.getOptionalInteger("orderBy"),
                context.getOptionalInteger("order"),
                context.getOptionalString("searchContent"),
                context.getOptionalString("categoryId"),
                context.getOptionalString("topicId"),
                context.getInteger("type"),
                context.getOptionalInteger("publishClient"),
                context.getOptionalInteger("companyType"));
    }

    /**
     * 根据人员集合查找评分
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/find-score", method = RequestMethod.GET)
    @Permitted
    @Param(name = "memberIds", required = true)
    @Param(name = "businessId", required = true)
    @Param(name = "businessType", required = true, type = Integer.class)
    @JSON("id,memberId,businessId,businessType,score")
    public List<CourseScore> findScore(RequestContext context) {
        String[] memberIds = context.get("memberIds", String.class).split(",");
        return courseInfoService.findCourseScore(memberIds,
                context.get("businessId", String.class),
                context.get("businessType", Integer.class));
    }

    /**
     * 专题知识进度更新 - 打开即完成
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/knowledge-progress", method = RequestMethod.POST)
    @Permitted
    @Param(name = "resourceId", required = true, type = String.class)
    @Param(name = "beginTime", required = true, type = Long.class)
    @JSON("*")
    public Map<String, Boolean> knowledgeProgress(RequestContext context, Subject<Member> subject) {
        courseStudyProgressService.updateKnowledgeStudyProgressAsync(
                subject.getCurrentUserId(),
                context.getString("resourceId"),
                context.get("beginTime", Long.class)
        );
        return ImmutableMap.of("success", true);
    }

    /**
     * 添加缓存用于触发发送人-课-天消息的开关
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/trigger-course-day-message", method = RequestMethod.POST)
    @Permitted
    @Param(name = "flag", required = true, type = String.class)
    @JSON("*")
    public Map<String, Boolean> triggerCourseMemberDayMessage(RequestContext context, Subject<Member> subject) {
        courseCacheService.triggerCourseMemberDayMessage(context.get("flag", String.class));
        return ImmutableMap.of("success", true);
    }


    @RequestMapping(value = "/find-subject-chapter-sections", method = RequestMethod.GET)
    @Permitted
    @Param(name = "subjectId", required = true)
    @JSON("*")
    @JSON("courseChapterSections.(*)")
    public List<CourseChapter> findSubjectChapterSections(RequestContext context) {
        String subjectId = context.get("subjectId", String.class);
        return courseInfoService.findSubjectChapterSections(subjectId);
    }

    /**
     * 添加缓存用于触发全局搜索查询的接口
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/trigger-course-full-search", method = RequestMethod.POST)
    @Permitted
    @Param(name = "flag", required = true, type = String.class)
    @JSON("*")
    public Map<String, Boolean> triggerCourseFullSearchMessage(RequestContext context, Subject<Member> subject) {
        courseCacheService.triggerCourseFullSearch(context.get("flag", String.class));
        return ImmutableMap.of("success", true);
    }

    @RequestMapping(value = "/study-plan-recommend", method = RequestMethod.GET)
    @Permitted
    @Param(name = "type", required = true, type = Integer.class)
    @Param(name = "page", required = true, type = Integer.class)
    @Param(name = "pageSize", required = true, type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(id,name,businessType,cover,coverPath,addPlanMemberCount,studyPlanAdded,topicNames)")
    @JSON("items.courseChapterSections.(id,referenceId,name,sectionType,required)")
    public PagedResult<CourseInfo> findStudyPlanRecommend(RequestContext rc, Subject<Member> sub) {
        long now = System.currentTimeMillis();
        PagedResult<CourseInfo> recommendCourses = courseInfoService.findStudyPlanRecommend(rc.getInteger("page"), rc.getInteger("pageSize"), sub.getCurrentUserId(), rc.getInteger("type"));
        long now2 = System.currentTimeMillis();
        logger.debug("课程推送查询 用时:{}", now2 - now);
        List<String> businessIds = studyPlanService.findAllBusinessIdByMemberId(sub.getCurrentUserId(), rc.getOptionalInteger("type"), recommendCourses.getItems().stream().map(CourseInfo::getId).collect(Collectors.toList()));
        long now3 = System.currentTimeMillis();
        logger.debug("学习计划已添加查询 用时:{}", now3 - now2);
        recommendCourses.getItems().forEach(c -> {
            c.setStudyPlanAdded(businessIds.contains(c.getId()));
        });
        return recommendCourses;
    }


    private AccessToken getAccessToken(HttpServletRequest request, Boolean isSecurity) {
        String header = request.getHeader("Authorization");
        String token = org.springframework.util.StringUtils.hasText(header) ? header.substring("Bearer".length() + "__".length()) : request.getParameter("access_token");
        if (isSecurity) {
            try {
                token = Encrypt.Decrypt(token, aesKey);
            } catch (
                    Exception e) {
                return null;
            }
        }
        AccessToken accessToken = cache.get(token, AccessToken.class);
        return accessToken;
    }

    private String getDecryptParam(Optional<String> param) {
        if (param.isPresent()) {
            try {
                return Encrypt.Decrypt(param.get(), aesKey);
            } catch (
                    Exception e) {
                e.printStackTrace();
                return null;
            }
        }
        return null;
    }

    /**
     * 自动学习数据回源
     *
     * @param request
     * @param courseId
     * @param isCourse
     * @param memberId
     */
    private void backToTheCourse(HttpServletRequest request, String courseId, boolean isCourse, String memberId) {
//        try {
//            if (courseInfoService.checkArchived(memberId, courseId)) {
//                String authorization = request.getHeader("Authorization");
//                String protocol = "http://";
//                String host = request.getHeader("Host");
//                String url = isCourse ? "/api/v1/archived/my-course/course-back" : "/api/v1/archived/my-course/subject-back";
//
//                Map<String, String> params = new HashMap<>();
//                Map<String, String> header = new HashMap<>();
//                header.put("Authorization", authorization);
//                params.put(
//                        "ids", new JSONArray() {{
//                            add(courseId);
//                        }}.toJSONString()
//                );
//
//                String responseJson =
//                        HttpClientUtil.httpPost(String.join("",protocol, host, url), header, params);
//                logger.error("返回值 {}",responseJson);
//                if (StringUtils.isEmpty(responseJson) || !responseJson.contains("ok")) {
//                    logger.error("自动回源调用失败! 地址: {} 参数: {} 请求头: {}",String.join("",protocol, host, url), params, header);
//                    throw new UnprocessableException(ErrorCode.CourseNotAudience);
//                }
//            }
//        } catch (Exception e) {
//            e.printStackTrace();
//            logger.error("自动回源调用失败!");
//            throw new UnprocessableException(ErrorCode.CourseNotAudience);
//        }
        ArchivedKit.autoBackToCourse(request, courseId, isCourse, memberId);
    }


    /**
     * 查询课程列表,需分页,排序
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/find-course-virtual-space-list", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "orderBy", type = Integer.class)
    //0:上架时间 1:访问人数 2:评分
    @Param(name = "order", type = Integer.class)
    //1:asc 2:desc
    @Param(name = "showField", type = Integer.class)
    //0=都显示,1=不显示,2=显示评分,3=显示预览人数
    @Param(name = "companyType", type = Integer.class)
    //0:全部 1:集团 2:本公司 3:其它公司
    @Param(name = "searchContent")
    // 搜索内容,现在只匹配了:课程名称和描述
    @Param(name = "categoryId")
    //课程目录id
    @Param(name = "topicId")
    //话题id
    @Param(name = "publishClient", type = Integer.class)
    // 1: PC, 2: APP
    @Param(name = "type", type = Integer.class)
    // 0:课程
    @Param(name = "virtualSpacesId", required = true)
    // 虚拟空间id
    @Param(name = "virtualSpacesOrganizationId", required = true)
    // 虚拟空间归属id
    @Param(name = "isAdd")
    //1,追加资源
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,releaseTime,courseTime,source,status,cover,coverPath,beginDate,endDate,studyMemberCount,finishStatus,visits,avgScore,url,integral, businessType)")
    @JSON("more")
    public Map<String, Object> findCourseVirtualSpaceList(RequestContext context, Subject<Member> subject) {

        String currentUserId = subject.getCurrentUserId();

        Optional<Integer> type = context.getOptionalInteger("type");
        String virtualSpacesId = context.getString("virtualSpacesId");
        String virtualSpacesOrganizationId = context.getString("virtualSpacesOrganizationId");

        Optional<Integer> isAdd = context.getOptionalInteger("isAdd");


        List<String> businessId = courseVirtualSpaceService.findBusinessId(type, virtualSpacesId, isAdd);

        //查询禁用资源,然后notin
        List<String> byVirtualSpacesForbidden = courseVirtualSpaceService.findByVirtualSpacesStatus(virtualSpacesId, type, CourseVirtualSpace.STATUS_FORBIDDEN, isAdd);


        return courseInfoService.findCourseVirtualSpaceList(currentUserId,
                context.getInteger("page"),
                context.getInteger("pageSize"),
                context.getOptionalInteger("orderBy"),
                context.getOptionalInteger("order"),
                context.getOptionalString("searchContent"),
                context.getOptionalString("categoryId"),
                context.getOptionalString("topicId"),
                type,
                context.getOptionalInteger("publishClient"),
                context.getOptionalInteger("companyType"),
                context.getOptionalInteger("showField").orElse(0),
                businessId,
                isAdd,
                virtualSpacesId,
                virtualSpacesOrganizationId,
                byVirtualSpacesForbidden);
    }

    @RequestMapping(value = "/course-info-progress-cache", method = RequestMethod.GET)
    @Param(name = "ids", required = true)
    @Param(name = "type", required = true, type =  Integer.class)
    @JSON("courseId, finishStatus")
    public List<CourseProgressCache> courseInfoProgressesCache(RequestContext context, Subject<Member> subject) {
        String key = "explicitLearningStatus#" + subject.getCurrentUserId();
        String[] ids = context.getString("ids").split(",");
        List<String> notExistIds = new ArrayList<>();
        List<CourseProgressCache> resList = new ArrayList<>();
        for (String id : ids) {
            CourseProgressCache courseProgressCache = cache.get(key + id, CourseProgressCache.class);
            if (Objects.nonNull(courseProgressCache)) {
                resList.add(courseProgressCache);
            } else {
                notExistIds.add(id);
            }
        }

        String rootId = "1";
        Optional<String> configString = Optional.empty();
        Optional<RuleConfig> config = ruleConfigService.getByName(rootId, RuleConfig.KEY.EXPLICIT_LEARNING_STATUS);
        if (config.isPresent()){
            configString = Optional.ofNullable(config.get().getValue());
        }

        if (!notExistIds.isEmpty()){
            Map<String, Integer> map = Optional.ofNullable(courseInfoService.courseInfoProgressesCache2(subject.getCurrentUserId(), notExistIds, context.getInteger("type"), configString)).orElse(new HashMap<>());
            notExistIds.forEach(id->{
                Integer finishStatus = map.get(id);
                CourseProgressCache courseProgressCache = new CourseProgressCache();
                courseProgressCache.setCourseId(id);
                if (Objects.nonNull(finishStatus)){
                    courseProgressCache.setFinishStatus(map.get(id));
                }
                cache.set(key + courseProgressCache.getCourseId(), courseProgressCache, 5 * 60);
                resList.add(courseProgressCache);
            });
        }
        return resList;
    }

    @RequestMapping(value = "/get-required-section-progress", method = RequestMethod.GET)
    @Param(name = "courseId", required = true)
    @Param(name = "type", required = true, type =  Integer.class)
    @JSON("required, finish")
    public Map<String,Integer> getRequiredSectionProgress(RequestContext context, Subject<Member> subject) {
        String courseId = context.getString("courseId");
        if (!checkExplicitLearningStatus(context.getInteger("type"),courseId)){
            return null;
        }
        return courseInfoService.getRequiredSectionProgress(courseId, subject.getCurrentUserId());
    }
    @RequestMapping(value = "/get-required-section-progress-no-switch", method = RequestMethod.GET)
    @Param(name = "courseIds", required = true)
    @JSON("*")
    public Map<String,Integer> getRequiredSectionProgressNoSwitch(RequestContext context, Subject<Member> subject) {
        List<String> courseIds = new ArrayList<>(Arrays.asList(context.getString("courseIds").split(",")));
        return courseInfoService.getRequiredSectionProgress(courseIds, subject.getCurrentUserId());
    }
    @RequestMapping(value = "/get-new-required-section-id", method = RequestMethod.GET)
    @Param(name = "courseIds", required = true)
    @Param(name = "type", required = true, type =  Integer.class)
    @JSON("*.*.*")
    public Map<String,List<Map<String,Object>>> getNewRequiredSectionId(RequestContext context, Subject<Member> subject) {
        boolean switchStatus = SwitchConfig.getSwitchStatus(SwitchEnum.LearningStatus);
        String[]  courseIds = context.getString("courseIds").split(",");
        if (switchStatus){
            return Arrays.stream(courseIds).collect(Collectors.toMap(k -> k, v -> new ArrayList<>()));
        }
        String rootId = "1";
        Optional<String> configString = Optional.empty();

        Optional<RuleConfig> config = ruleConfigService.getByName(rootId, RuleConfig.KEY.EXPLICIT_LEARNING_STATUS);

        if (config.isPresent()){
            configString = Optional.ofNullable(config.get().getValue());
        }
        return courseInfoService.getNewRequiredSectionId2(Arrays.asList(courseIds), subject.getCurrentUserId(), context.getInteger("type"), configString);
    }
    @RequestMapping(value = "/get-front-subject-update-status", method = RequestMethod.GET)
    @Param(name = "courseIds", required = true)
    @Param(name = "type", required = true, type =  Integer.class)
    @JSON("*")
    public List<String> getNewRequiredList(RequestContext context, Subject<Member> subject) {
        boolean switchStatus = SwitchConfig.getSwitchStatus(SwitchEnum.LearningStatus);
        String[]  courseIds = context.getString("courseIds").split(",");
        if (switchStatus){
            return new ArrayList<>();
        }
        String rootId = "1";
        Optional<String> configString = Optional.empty();

        Optional<RuleConfig> config = ruleConfigService.getByName(rootId, RuleConfig.KEY.EXPLICIT_LEARNING_STATUS);

        if (config.isPresent()){
            configString = Optional.ofNullable(config.get().getValue());
        }
        return courseInfoService.getNewRequiredList(Arrays.asList(courseIds), subject.getCurrentUserId(), context.getInteger("type"), configString);
    }

    @JSON("*")
    @Param(name = "courseIds", required = true)
    @RequestMapping(value = "/switch-mentor", method = RequestMethod.GET)
    public Map<String,Integer> getSwitchMentor(RequestContext context) {
        return courseInfoService.getSwitchMentor(context.getString("courseIds"));
    }

    private Boolean checkExplicitLearningStatus(Integer type,String businessId){
        String rootId = "1";
        Integer enableStatus = 1;
        Integer subject;
        Integer course;
        Optional<CourseInfo> courseInfo = Optional.ofNullable(courseInfoService.getCourseInfo(businessId));
        if (courseInfo.isPresent() && Objects.nonNull(courseInfo.get().getExplicitLearningStatus())) {
            // subject course 没啥特殊含义就是兼容代码 写成了这个样子
            subject = courseInfo.get().getExplicitLearningStatus();
            course = subject;
        }else {
            Optional<RuleConfig> config = ruleConfigService.getByName(rootId, RuleConfig.KEY.EXPLICIT_LEARNING_STATUS);
            if (!config.isPresent()) {
                return false;
            }
            JSONObject configObject = JSONObject.parseObject(config.get().getValue());
            subject = configObject.getInteger("subject");
            course = configObject.getInteger("course");
        }
        if (CourseInfo.BUSINESS_TYPE_SUBJECT.equals(type)) {
            return enableStatus.equals(subject);
        }else {
            return enableStatus.equals(course);
        }
    }

    private Map<String, Boolean> checkExplicitLearningStatus2(Integer type, List<String> businessId) {
        Map<String, Boolean> resMap = new HashMap<>();
        String rootId = "1";
        Integer enableStatus = 1;
        Integer subject = 0;
        Integer course = 0;
        Map<String, Integer> learningStatusMap = courseInfoService.getExplicitLearningStatus(businessId);
        Optional<RuleConfig> config = ruleConfigService.getByName(rootId, RuleConfig.KEY.EXPLICIT_LEARNING_STATUS);
        if (config.isPresent()) {
            JSONObject configObject = JSONObject.parseObject(config.get().getValue());
            subject = configObject.getInteger("subject");
            course = configObject.getInteger("course");
        }
        for (String id : businessId) {
            if (!learningStatusMap.isEmpty() && Objects.nonNull(learningStatusMap.get(id))) {
                subject = learningStatusMap.get(id);
                course = subject;
            }
            Boolean status = CourseInfo.BUSINESS_TYPE_SUBJECT.equals(type) ? enableStatus.equals(subject) : enableStatus.equals(course);
            resMap.put(id, status);
        }
        return resMap;
    }
}


