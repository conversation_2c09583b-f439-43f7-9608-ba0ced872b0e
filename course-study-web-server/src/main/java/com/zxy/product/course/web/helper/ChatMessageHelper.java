package com.zxy.product.course.web.helper;

import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.util.HttpClientUtil;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/14:13
 * @Description:
 */
@Component
public class ChatMessageHelper {

    private static Logger logger = LoggerFactory.getLogger(ChatMessageHelper.class);

    @Value("${course.model.url}")
    private String url;
    @Value("${course.model.dataset-id}")
    private String datasetId;
    @Value("${course.model.app-api-key}")
    private String appApiKey;
    @Value("${course.model.dataset-api-key}")
    private String datasetApiKey;
    @Value("${course.model.interface.chat-messages}")
    private String chatMessage;
    @Value("${course.model.interface.chat-stop}")
    private String chatStop;
    @Value("${course.model.interface.chat-list}")
    private String chatList;
    @Value("${course.model.interface.history-chat-list}")
    private String historyChatList;

    private RestTemplate restTemplate;

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate){
        this.restTemplate=restTemplate;
    }


    public String sendChatMesage(String question, String memberId, Optional<String> conversationId){
        HashMap<String, Object> data = new HashMap();
        data.put("inputs", new Object());
        data.put("query", question);
        data.put("response_mode","streaming");
        data.put("user", memberId);
        data.put("conversation_id", conversationId.orElse(CommonConstant.EMPTY));
//        HashMap<String, String> map = new HashMap();
//        map.put("data", JSON.toJSONString(data));
        HashMap<String, String> heards = setHeard(true);
        String urls = url + chatMessage;
        logger.info("request url= {}, params ={}, heard = {}",url,data, heards);
        String response = HttpClientUtil.httpPostCustomizeV2(urls, heards, JSON.toJSONString(data));
        logger.info("response = {}" ,response);
        return response;

    }


    /**
     * 处理第三方的响应流数据
     * @param response 第三方响应结果集
     * @return 流处理后的JSON数据
     */
    private static ChatResponseDto processAiStream(String response){
        logger.info("处理HTTP成研AI问答的响应流数据开始,响应{}",response);
        int startIndex = response.indexOf("data: {");
        response=response.substring(startIndex+5);
        logger.info("处理后的数据 {}",response);
        ObjectMapper mapper = new ObjectMapper();
        ChatResponseDto obj = new ChatResponseDto();
        try {
            //反序列化时,遇到未知属性会不会报错
            //true - 遇到没有的属性就报错 false - 没有的属性不会管，不会报错
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
            //如果是空对象的时候,不抛异常
            mapper.configure(SerializationFeature.FAIL_ON_EMPTY_BEANS, false);
            mapper.configure(JsonParser.Feature.ALLOW_UNQUOTED_CONTROL_CHARS, true);
            obj = mapper.readValue(response, ChatResponseDto.class);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return obj;
    }


    public ChatResponseDto sendChatMesageV2(String question, String memberId, Optional<String> conversationId){
        HashMap<String, Object> data = new HashMap();
        data.put("inputs", new Object());
        data.put("query", question);
        data.put("response_mode","streaming");
        data.put("user", memberId);
        data.put("conversation_id", conversationId.orElse(CommonConstant.EMPTY));
        HttpHeaders header = new HttpHeaders();
        MediaType mediaType = MediaType.parseMediaType(String.valueOf(MediaType.APPLICATION_JSON));
        header.setContentType(mediaType);
        header.add("Authorization", appApiKey);
        String urls = url + chatMessage;
        String responseBody = JSON.toJSONString(data);
        HttpEntity<String> httpEntity = new HttpEntity<>(responseBody, header);
        HttpClient httpClient = HttpClients.createDefault();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
        requestFactory.setConnectTimeout(5000);
        requestFactory.setReadTimeout(5000);
        requestFactory.setConnectionRequestTimeout(5000);
        restTemplate.setRequestFactory(requestFactory);
        ResponseEntity<String> response = restTemplate.exchange(urls, HttpMethod.POST, httpEntity, String.class);
        logger.info("HTTP成研AI问答原始出参{}", JSON.toJSONString(response));
        return Optional.ofNullable(response)
                .filter(ew1-> Objects.equals(200,ew1.getStatusCodeValue()))
                .map(ew2->this.processAiStream(ew2.getBody()))
                .orElseThrow(()->new UnprocessableException(ErrorCode.ModelMentorTutorAIQANetworkFailure));

    }




    private HashMap<String, String> setHeard(Boolean isJson){
        HashMap<String, String> heard = new HashMap();
        heard.put("Authorization", appApiKey);
        if(isJson){
            heard.put("Content-Type", "application/json");
        }
        return heard;
    }

    public String sendChatStop(String memberId, String taskId){
        HashMap<String, String> heards = setHeard(true);
        HashMap<String, String> data = new HashMap();
        data.put("user", memberId);
        HashMap<String, String> map = new HashMap();
        map.put("data", JSON.toJSONString(data));
        logger.info("request = {}" ,map);
        String response = HttpClientUtil.httpPostCustomizeV2(url + String.format(chatStop,taskId), heards, JSON.toJSONString(data));
        logger.info("response = {}" ,response);
        return response;
    }

    public String getChatList(String memberId, Integer limit, String sortBy){
        HashMap<String, String> heards = setHeard(false);
        String response = HttpClientUtil.httpGetCustomize(url + String.format(chatList,memberId, limit, sortBy), heards, null,HttpClientUtil.RequestConfigType.TIMEOUT_10000);
        logger.info("response = {}" ,response);
        return response;
    }


    public String getHistoryChatList(String memberId, Integer limit, String conversationId){
        HashMap<String, String> heards = setHeard(false);
        String response = HttpClientUtil.httpGetCustomize(url + String.format(historyChatList,memberId,conversationId,limit), heards, null,HttpClientUtil.RequestConfigType.TIMEOUT_10000);
        logger.info("response = {}" ,response);
        return response;
    }





}
