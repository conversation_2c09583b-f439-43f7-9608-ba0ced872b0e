package com.zxy.product.course.web.controller.subAutenticated;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.encrypt.SimpleDigest;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.support.DefaultResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.annotation.Params;
import com.zxy.common.restful.annotation.Validation;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.subAuthenticated.SubAuthenticatedService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedResourceAuditRecordEntity;
import com.zxy.product.course.util.DesensitizationUtil;
import com.zxy.product.course.util.EncryptUtil;
import com.zxy.product.course.web.kit.CourseInfoAdminKit;
import com.zxy.product.course.web.kit.SubAuthenticatedKit;
import com.zxy.product.course.web.util.Assert;
import com.zxy.product.course.web.util.BrowserUtil;
import com.zxy.product.course.web.util.FileUtil;
import com.zxy.product.exam.api.StrongBaseService;
import com.zxy.product.human.api.ExportTaskService;
import com.zxy.product.course.web.validation.LengthRangeValidation;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.entity.ExportTask;
import com.zxy.product.human.entity.Attachment;
import com.zxy.product.human.jooq.tables.pojos.AttachmentEntity;
import com.zxy.product.system.api.permission.GrantService;
import org.apache.http.client.utils.DateUtils;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.InputStreamResource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.util.*;
import java.util.stream.Collectors;

import static java.util.stream.Collectors.groupingBy;

/**
 * @Classname SubAuthenticatedController
 * @Description
 * @Date 2023/3/6
 * @Created by futzh
 */
@Controller
@RequestMapping("/sub-authenticated")
public class SubAuthenticatedController {
    private static final Logger LOGGER = LoggerFactory.getLogger(SubAuthenticatedController.class);
    private static final String URI = "auth-management/auth-child-management"; // TODO 没定
    private SubAuthenticatedKit subAuthenticatedKit;
    private SubAuthenticatedService subAuthenticatedService;
    private StrongBaseService strongBaseService;
    private Cache cache;
    private Redis redis;
    private RedissonClient redissonClient;
    private ExportTaskService exportTaskService;
    private MessageSender messageSender;
    private GrantService grantService;
    private static final String CODE_CACHE_KEY = "course-subAuthenticated-code";
    private static final String CODE_PREFIX = "wd";
    public static final String GENERATE_CODE_LOCK = "course-subAuthenticated-generate-code-lock";
    public static final String REGISTER_LOCK = "course-subAuthenticated-register-lock";
    /**
     * 防止重复点击导出的key
     */
    public static final String EXPORT_REDIS_KEY_PREFIX = "safe-export";

    /**
     * 防止重复点击import 的key
     */
    public static final String IMPORT_REDIS_KEY_PREFIX = "safe-import";
    /**
     * 防重复点击间隔时间(毫秒)
     */
    private static final long REPEAT_TIME = 1000;

    @Autowired
    public void setExportTaskService(ExportTaskService exportTaskService) {
        this.exportTaskService = exportTaskService;
    }

    @Autowired
    public void setStrongBaseService(StrongBaseService strongBaseService) {
        this.strongBaseService = strongBaseService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }

    @Autowired
    public void setCache(Cache cache) {
        this.cache = cache;
    }

    @Resource
    private CourseInfoAdminKit courseInfoAdminKit;
    @Resource
    private FileService fileService;
    @Resource
    private AttachmentResolver attachmentResolver;

    @Autowired
    public void setGrantService(GrantService grantService) {
        this.grantService = grantService;
    }


    @Autowired
    public void setSubAuthenticatedKit(SubAuthenticatedKit subAuthenticatedKit) {
        this.subAuthenticatedKit = subAuthenticatedKit;
    }

    @Autowired
    public void setSubAuthenticatedService(SubAuthenticatedService subAuthenticatedService) {
        this.subAuthenticatedService = subAuthenticatedService;
    }

    /**
     * 新增子认证
     *
     * @param context
     * @return
     */
    @RequestMapping(method = RequestMethod.POST)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    @Param(name = "contentConfigures") //认证专区配置内容
    @Param(name = "name", type = String.class) // 专区名称
    @Param(name = "certificateId", type = String.class) // 证书id
    @Param(name = "organizationId", type = String.class) // 归属部门id
    @Param(name = "splitTrainingFlag", type = String.class) // 考培分离标志
    @Param(name = "checkCertificateFlag", type = Integer.class, value = "是否校验考试证书，0=否，1=是")
    @Param(name = "manageIgnoreFlag", type = String.class) //  管理忽略归属部门标志
    @Param(name = "authenticatedLevel", required = true,type = Integer.class)
    @JSON("id")
    public Object save(RequestContext context) {
        List<SubAuthenticatedContentConfigure> contentConfigureList = subAuthenticatedKit.parseContentConfigures(context.getOptionalString("contentConfigures"));
        contentConfigureList = validateContentConfigureList(contentConfigureList, Optional.empty());
        List<SubAuthenticatedContentConfigure> examConfigureList = null;
        Optional<String> name = context.getOptionalString("name");
        Optional<String> certificateId = context.getOptionalString("certificateId");
        Optional<String> organizationId = context.getOptionalString("organizationId");
        Optional<Integer> splitTrainingFlag = context.getOptionalInteger("splitTrainingFlag");
        Optional<String> manageIgnoreFlag = context.getOptionalString("manageIgnoreFlag");
        Optional<Integer> authenticatedLevel = context.getOptionalInteger("authenticatedLevel");
        Optional<Integer> checkCertificateFlag = context.getOptionalInteger("checkCertificateFlag");

        if (!ObjectUtils.isEmpty(contentConfigureList)) {
            examConfigureList = contentConfigureList.stream().filter(r -> SubAuthenticatedContentConfigure.CONTENT_TYPE_EXAM.equals(r.getContentType())).collect(Collectors.toList());
            contentConfigureList = contentConfigureList.stream().filter(r -> !SubAuthenticatedContentConfigure.CONTENT_TYPE_EXAM.equals(r.getContentType())).collect(Collectors.toList());
        }
        String subAuthenticatedId = subAuthenticatedService.save(contentConfigureList, name, certificateId, organizationId, splitTrainingFlag, manageIgnoreFlag,authenticatedLevel, checkCertificateFlag);
        saveExamGroup(examConfigureList, subAuthenticatedId);
        return ImmutableMap.of("id", subAuthenticatedId);
    }

    private void saveExamGroup(List<SubAuthenticatedContentConfigure> examConfigureList, String subAuthenticatedId) {
        if (!ObjectUtils.isEmpty(examConfigureList)) {
            List<SubAuthenticatedExamGroup> examGroups = new ArrayList<>();
            examConfigureList.forEach(subAuthenticatedContentConfigure -> {
                //构建考试组
                if (SubAuthenticatedContentConfigure.CONTENT_TYPE_EXAM.equals(subAuthenticatedContentConfigure.getContentType())) {
                    //处理配置表
                    SubAuthenticatedContentConfigure contentConfigure = subAuthenticatedService.insertContentConfigure(subAuthenticatedContentConfigure, subAuthenticatedId);
                    List<SubAuthenticatedExamGroup> examGroupList = subAuthenticatedContentConfigure.getSubAuthenticatedExamGroups();
                    examGroupList.forEach(examGroup -> {
                        examGroup.forInsert();
                        examGroup.setExamTimes(subAuthenticatedContentConfigure.getExamTimes());
                        examGroup.setSubAuthenticatedId(subAuthenticatedId);
                        examGroup.setExamGroupId(contentConfigure.getContentId());
                        examGroup.setExamRegistrationFrequency(subAuthenticatedContentConfigure.getExamRegistrationFrequency());
                    });
                    examGroups.addAll(examGroupList);
                }
            });
            examGroups.forEach(examGroup -> {
                com.zxy.product.exam.entity.SubAuthenticatedExamGroup subAuthenticatedExamGroup = new com.zxy.product.exam.entity.SubAuthenticatedExamGroup();
                BeanUtils.copyProperties(examGroup, subAuthenticatedExamGroup);
                strongBaseService.insert(subAuthenticatedExamGroup);
            });
        }
    }

    /**
     * 编辑子认证
     *
     * @param context
     * @return
     */
    @RequestMapping(method = RequestMethod.PUT)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    @Param(name = "id", required = true)
    @Param(name = "contentConfigures") //认证专区配置内容
    @Param(name = "name", required = true) // 专区名称
    @Param(name = "certificateId") // 证书id
    @Param(name = "organizationId", required = true) // 归属部门id
    @Param(name = "splitTrainingFlag", type = Integer.class) // 考培分离标志
    @Param(name = "checkCertificateFlag", type = Integer.class, value = "是否校验考试证书，0=否，1=是")
    @Param(name = "manageIgnoreFlag", type = String.class) //  管理忽略归属部门标志
    @Param(name = "authenticatedLevel", type = Integer.class, required = true) // 考培分离标志
    @JSON("id")
    public Object update(RequestContext context) {
        String id = context.getString("id");
        List<SubAuthenticatedContentConfigure> contentConfigureList = subAuthenticatedKit.parseContentConfigures(context.getOptionalString("contentConfigures"));
        contentConfigureList = validateContentConfigureList(contentConfigureList, Optional.of(id));
        List<SubAuthenticatedContentConfigure> examConfigureList = null;

        Optional<String> name = context.getOptionalString("name");
        Optional<String> certificateId = context.getOptionalString("certificateId");
        Optional<String> organizationId = context.getOptionalString("organizationId");
        Optional<Integer> splitTrainingFlag = context.getOptionalInteger("splitTrainingFlag");
        Optional<String> manageIgnoreFlag = context.getOptionalString("manageIgnoreFlag");
        Optional<Integer> authenticatedLevel = context.getOptionalInteger("authenticatedLevel");
        Optional<Integer> checkCertificateFlag = context.getOptionalInteger("checkCertificateFlag");
        if (!ObjectUtils.isEmpty(contentConfigureList)) {
            examConfigureList = contentConfigureList.stream().filter(r -> SubAuthenticatedContentConfigure.CONTENT_TYPE_EXAM.equals(r.getContentType())).collect(Collectors.toList());
            contentConfigureList = contentConfigureList.stream().filter(r -> !SubAuthenticatedContentConfigure.CONTENT_TYPE_EXAM.equals(r.getContentType())).collect(Collectors.toList());
        }
        String subId = subAuthenticatedService.update(id, contentConfigureList, name, certificateId, organizationId, splitTrainingFlag, manageIgnoreFlag,authenticatedLevel, checkCertificateFlag);
        updateExamGroup(examConfigureList, subId);
        return ImmutableMap.of("id", subId);
    }

    private void updateExamGroup(List<SubAuthenticatedContentConfigure> examConfigureList, String subId) {
        deleteExamGroup(subId);
        saveExamGroup(examConfigureList, subId);
    }

    private void deleteExamGroup(String subId) {
        strongBaseService.deleteBySubId(subId);
    }

    /**
     * 管理端查询子认证详情
     *
     * @param context
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    @Param(name = "id", required = true)
    @JSON("name,certificateId,organizationId,splitTrainingFlag,organizationName,manageIgnoreFlag,authenticatedLevel,checkCertificate")
    @JSON("contentConfigureList.(contentId,contentName,contentType,examTimes,id,order,studyCover,studyCoverPath,subAuthenticatedId,isHide,examRegistrationFrequency)")
    @JSON("contentConfigureList.subAuthenticatedDimensions.(code,name,order,subAuthenticatedId,lockCoverId,lockCoverPath,unlockCoverId,unlockCoverPath,id)")
    @JSON("contentConfigureList.subAuthenticatedStudyOnlines.(businessId,businessName,businessType,id,order,studyGroupId,subAuthenticatedId)")
    @JSON("contentConfigureList.subAuthenticatedExamGroups.(businessId,businessName,businessType,order)")
    public SubAuthenticated findSubAuthenticated(RequestContext context) {
        SubAuthenticated subAuthenticated = subAuthenticatedService.findSubAuthenticated(context.getString("id"));
        // 查询考试组
        findSubAuthenticatedExamGroups(subAuthenticated);
        return subAuthenticated;
    }

    private void findSubAuthenticatedExamGroups(SubAuthenticated subAuthenticated) {
        List<SubAuthenticatedContentConfigure> contentConfigureList = subAuthenticated.getContentConfigureList();
        if (!ObjectUtils.isEmpty(contentConfigureList)) {
            List<SubAuthenticatedContentConfigure> examGroups = contentConfigureList.stream().filter(r -> SubAuthenticatedContentConfigure.CONTENT_TYPE_EXAM.equals(r.getContentType()) && !ObjectUtils.isEmpty(r.getContentId())).collect(Collectors.toList());
            if (!ObjectUtils.isEmpty(examGroups)) {
                List<String> contentIds = examGroups.stream().map(SubAuthenticatedContentConfigure::getContentId).collect(Collectors.toList());
                Map<String, List<com.zxy.product.exam.entity.SubAuthenticatedExamGroup>> examGroupMap = strongBaseService.findByGroups(contentIds).stream().collect(groupingBy(com.zxy.product.exam.entity.SubAuthenticatedExamGroup::getExamGroupId));
                examGroups.forEach(config ->  {
                    List<com.zxy.product.exam.entity.SubAuthenticatedExamGroup> subAuthenticatedExamGroups = examGroupMap.get(config.getContentId());
                    if (!ObjectUtils.isEmpty(subAuthenticatedExamGroups)) {
                        List<SubAuthenticatedExamGroup> exams = subAuthenticatedExamGroups.stream().map(r -> {
                            SubAuthenticatedExamGroup subAuthenticatedExamGroup = new SubAuthenticatedExamGroup();
                            BeanUtils.copyProperties(r, subAuthenticatedExamGroup);
                            return subAuthenticatedExamGroup;
                        }).sorted(Comparator.comparing(SubAuthenticatedExamGroup::getOrder)).collect(Collectors.toList());
                        config.setSubAuthenticatedExamGroups(exams);
                    }
                });
            }
        }
    }

    /**
     * 子认证发布/取消发布接口
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/publish", method = RequestMethod.PUT)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    @Param(name = "id", required = true)
    @Param(name = "publish", required = true, type = Integer.class)
    @JSON("*")
    public Boolean publish(RequestContext context, Subject<Member> subject) {
        String id = context.getString("id");
        Integer publish = context.getInteger("publish");
        Assert.assertTrue(Arrays.asList(SubAuthenticated.PUBLISH_NO, SubAuthenticated.PUBLISH_YES).contains(publish), ErrorCode.SubAuthenticatedPublishError);
        Boolean published = subAuthenticatedService.publish(id, publish, subject.getCurrentUserId());
        // 修改考试组的发布状态
        strongBaseService.updateGroupPublish(id, publish);
        return published;
    }

    /**
     * 子认证删除接口
     *
     * @param context
     * @return
     */
    @RequestMapping(method = RequestMethod.DELETE)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    @Param(name = "id", required = true)
    @JSON("*")
    public Boolean delete(RequestContext context) {
        String id = context.getString("id");
        // 删除考试组
        deleteExamGroup(id);
        return subAuthenticatedService.delete(id);
    }

    /**
     * 子认证-认证维度编码生成接口
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/generate-code", method = RequestMethod.GET)
    @Permitted
    @Params
    @JSON("code")
    public Object generateCode(RequestContext context) {
        return ImmutableMap.of("code", getCode());
    }

    private String getCode() {
        RLock lock = redissonClient.getLock(GENERATE_CODE_LOCK);
        String dateStr = DateUtils.formatDate(new Date(), "yyyyMMdd");
        Long firstIndex = Long.valueOf(dateStr + "001");
        String key = CODE_CACHE_KEY + "-" + dateStr;
        String resultCode = "";
        try {
            if (lock.tryLock()) {
                Long index = redis.process(x -> {
                    Long notExists = x.setnx(key, firstIndex + "");
                    if (notExists == 1) {
                        x.expire(key, 24 * 60 * 60);
                        return Long.valueOf(x.get(key));
                    }
                    return x.incr(key);
                });
                resultCode = CODE_PREFIX + index;
            }
        } finally {
            lock.unlock();
        }
        return StringUtils.isEmpty(resultCode) ? CODE_PREFIX + firstIndex : resultCode;
    }

    /**
     * 子认证-管理页面详情接口
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/admin-detail", method = RequestMethod.GET)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    @Param(name = "id", required = true)
    @JSON("*.*")
    public Object getAdminDetail(RequestContext context) {
        String id = context.getString("id");
        SubAuthenticated result = subAuthenticatedService.findAdminDetail(id);
        Assert.assertTrue(result != null, ErrorCode.SubAuthenticatedNotExistError);
        return result;
    }

    /**
     * 子认证-管理页面学习进度列表接口
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/admin-progress", method = RequestMethod.GET)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "memberName")
    @Param(name = "memberReadName")
    @Param(name = "uri",required = true)
    @Param(name = "organizationId")
    @Param(name = "contain", type = Integer.class)
    @Param(name = "registerBeginDate", type = Long.class)
    @Param(name = "registerEndDate", type = Long.class)
    @JSON("recordCount")
    @JSON("items.(*)")
    public PagedResult<SubAuthenticatedProgress> findAdminProgress(RequestContext context, Subject<Member> subject) {
        Optional<String> organizationId = context.getOptionalString("organizationId");
        Map<String, Set<String>> grantOrganizationMap = grantService.findGrantOrganizationByUri(
                subject.getCurrentUserId(), context.get("uri", String.class), organizationId.isPresent() ?
                        organizationId.get() : subject.getRootOrganizationId());
        PagedResult<SubAuthenticatedProgress> page = subAuthenticatedService.page(context.get("subAuthenticatedId", String.class), context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptional("memberName", String.class), context.getOptional("memberReadName", String.class),
                organizationId,
                context.getOptionalInteger("contain"),
                context.getOptional("registerBeginDate", Long.class),
                context.getOptional("registerEndDate", Long.class),
                grantOrganizationMap);
        page.getItems().forEach(r -> {
            r.setMemberFullName(EncryptUtil.aesEncrypt(r.getMemberFullName(), null));
            r.setMemberName(DesensitizationUtil.desensitizeEmployeeId(r.getMemberName()));
        });
        return page;
    }

    /**
     * 子认证-列表接口
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/page-list", method = RequestMethod.GET)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "subAuthenticatedName")
    @Param(name = "subAuthenticatedCode")
    @Param(name = "organizationId")
    @Param(name = "contain", type = Integer.class)    //是否包含子级【0：不包含；1：包含】
    @Param(name = "uri", required = true)
    @Param(name = "isPublish", type = Integer.class)
    @Param(name = "manageFlag", type = Integer.class, required = true)
    @Param(name = "firstPublishBeginDate", type = Long.class)
    @Param(name = "firstPublishEndDate", type = Long.class)
    @Param(name = "lastPublishBeginDate", type = Long.class)
    @Param(name = "lastPublishEndDate", type = Long.class)
    @Param(name = "authenticatedLevel",type = Integer.class)
    @JSON("recordCount")
    @JSON("items.(name,code,id,isPublish,firstPublishTime,publishTime,organizationName,manageIgnoreFlag,authenticatedLevel)")
    public PagedResult<SubAuthenticated> pageList(RequestContext context, Subject<Member> memberSubject) {
        Optional<String> organizationId = context.getOptionalString("organizationId").isPresent() ?
                context.getOptionalString("organizationId") : Optional.of(memberSubject.getRootOrganizationId());
        List<String> grantOrganizationIds = null;
        if (context.getOptionalInteger("contain").orElse(0) == 0) {
            grantOrganizationIds = courseInfoAdminKit.findGrantTopOrganizationIds(memberSubject.getCurrentUserId(), organizationId, context.getOptionalString("uri").orElse(CourseInfo.URI));
        } else {
            grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(memberSubject.getCurrentUserId(), organizationId, context.getOptionalString("uri").orElse(CourseInfo.URI));
        }
        return subAuthenticatedService.pageList(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptional("subAuthenticatedName", String.class),
                context.getOptional("subAuthenticatedCode", String.class),
                grantOrganizationIds,
                context.getOptional("isPublish", Integer.class),
                context.getInteger("manageFlag"),
                context.getOptional("firstPublishBeginDate", Long.class),
                context.getOptional("firstPublishEndDate", Long.class),
                context.getOptional("lastPublishBeginDate", Long.class),
                context.getOptional("lastPublishEndDate", Long.class),
                context.getOptional("authenticatedLevel", Integer.class)
        );
    }

    /**
     * 子认证-管理页面学习进度点击详情接口
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/admin-progress-detail", method = RequestMethod.GET)
    @Permitted
    @Param(name = "memberId", required = true)
    @Param(name = "subAuthenticatedId", required = true)
    @JSON("*.contentId,contentName,contentType,order,totalGroupStatus")
    @JSON("*.subAuthenticatedStudyOnlines.(businessId,businessName,businessType,order,studyGroupId,subAuthenticatedId,beginTime,studyTotalTime,status)")
    public Map<Integer, List<SubAuthenticatedContentConfigure>> findAdminProgressDetail(RequestContext context) {
        return subAuthenticatedService.findProgressDetail(context.get("subAuthenticatedId", String.class), context.get("memberId", String.class));
    }

    /**
     * 子认证学习管理导出
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/admin-progress-export", method = RequestMethod.POST)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    @Param(name = "businessType", type = Integer.class, required = true)
    @Param(name = "queryParas", required = true)
    @JSON("id,memberId,businessType,fileSize,createTime,queryParas,signature,status,dataLen,filePath,attachmentId,extraParam")
    public ExportTask insertExportTask(RequestContext context, Subject<Member> subject) {
        String redisKey = EXPORT_REDIS_KEY_PREFIX + subject.getCurrentUserId();
        String value = cache.get(redisKey, String.class);
        if (!StringUtils.isEmpty(value)) {
            throw new UnprocessableException(ErrorCode.SubAuthenticatedIsExporting);
        }
        Integer businessType = context.get("businessType", Integer.class);
        String queryParas = context.getString("queryParas");
        Optional<String> signature = Optional.of(SimpleDigest.hashPassword(queryParas));
        ExportTask exportTask = exportTaskService.insertAsynchronousExportTask(subject.getCurrentUserId(), businessType, queryParas,
                signature, Optional.empty(), Optional.empty());
        // 发送异步导出消息
        messageSender.send(MessageTypeContent.SUB_AUTHENTICATED_STUDY_EXPORT,
                MessageHeaderContent.ID, exportTask.getId(),
                MessageHeaderContent.QUERY_PARAS, queryParas,
                MessageHeaderContent.MEMBER_ID, subject.getCurrentUserId(),
                MessageHeaderContent.ORGANIZATION_ID, subject.getRootOrganizationId()
        );
        cache.set(EXPORT_REDIS_KEY_PREFIX + subject.getCurrentUserId(),1, 5 * 60);
        return exportTask;
    }


    /**
     * 子认证-学员点击学习组封面注册
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/sub-authenticated-register", method = RequestMethod.POST)
    @Param(name = "subAuthenticatedId", required = true)
    @JSON("result")
    public Boolean register(RequestContext context, Subject<Member> subject) {
        RLock lock = redissonClient.getLock(REGISTER_LOCK);
        boolean result = true;
        try {
            if (lock.tryLock()) {
                result = subAuthenticatedService.register(context.get("subAuthenticatedId", String.class), subject.getCurrentUserId());
            }
        } catch (Exception e) {
            throw new UnprocessableException(ErrorCode.SubAuthenticatedIsRegistering);
        } finally {
            lock.unlock();
        }
        return result;
    }

    /**
     * 子认证-学员端-子认证列表接口
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/student-subAuthenticated-list", method = RequestMethod.GET)
    @Param(name = "groupId", required = true)//分组id
    @JSON("name,id,certificatedRecordId,certificateId,certificatedExistFlag,subCertificatedPublish, subCertificatedRecordId")
    @JSON("memberDimensions.subAuthenticatedDimension.(id,name,code,unlockCoverPath,lockCoverPath,order)")
    @JSON("memberDimensions.(isLighted)")
    @JSON("contentConfigureList.(contentId,contentName,contentType,order,studyCoverPath,examCertificatedRecordId,isHide)")
    public List<SubAuthenticated> studentSubAuthList(RequestContext context, Subject<Member> subject) {
        List<SubAuthenticated> subAuthenticatedList = subAuthenticatedService.studentSubAuthList(context.get("groupId", String.class), subject.getCurrentUserId());
        if (CollectionUtils.isNotEmpty(subAuthenticatedList)) {
            List<String> subIds = new ArrayList<>();
            subAuthenticatedList.forEach(subAuthenticated -> {
                List<SubAuthenticatedContentConfigure> contentConfigureList = subAuthenticated.getContentConfigureList();
                if (CollectionUtils.isNotEmpty(contentConfigureList)) {
                    List<String> subId = contentConfigureList.stream().filter(config -> SubAuthenticatedContentConfigure.CONTENT_TYPE_EXAM == config.getContentType())
                            .map(SubAuthenticatedContentConfigure::getSubAuthenticatedId).distinct().collect(Collectors.toList());
                    if (CollectionUtils.isNotEmpty(subId)) { subIds.addAll(subId); }
                }
            });
            if (CollectionUtils.isNotEmpty(subIds)) {
                //查询每个考试组对应的最高成绩的考试证书
                Map<String, String> result = strongBaseService.findTopScoreCertificate(subIds, subject.getCurrentUserId());
                subAuthenticatedList.forEach(subAuthenticated -> {
                    List<SubAuthenticatedContentConfigure> contentConfigureList = subAuthenticated.getContentConfigureList();
                    if (CollectionUtils.isNotEmpty(contentConfigureList)) {
                        contentConfigureList.forEach(config -> config.setExamCertificatedRecordId(result.get(config.getContentId())));
                    }
                });
            }
        }
        return subAuthenticatedList;
    }


    /**
     * 子认证-学员端-在线学习组内课程专题列表查询接口
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/study-list", method = RequestMethod.GET)
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "studyGroupId", required = true)
    @JSON("order,businessName,businessId,businessType,subAuthenticatedId,studyGroupId")
    public List<SubAuthenticatedStudyOnline> findStudyList(RequestContext context, Subject<Member> subject) {
        return subAuthenticatedService.findStudyList(context.get("subAuthenticatedId", String.class), context.get("studyGroupId", String.class)
        ,subject.getCurrentUserId());
    }

    /**
     * 校验必填项
     *
     * @param contentConfigureList
     */
    private List<SubAuthenticatedContentConfigure> validateContentConfigureList(List<SubAuthenticatedContentConfigure> contentConfigureList, Optional<String> id) {
        if (!ObjectUtils.isEmpty(contentConfigureList)) {
            List<String> examIds = new ArrayList<>();
            contentConfigureList.forEach(configure -> {
                //配置类型必填
                Assert.assertTrue(configure.getContentType() != null && Arrays.asList(SubAuthenticatedContentConfigure.CONTENT_TYPE_STUDY,
                        SubAuthenticatedContentConfigure.CONTENT_TYPE_EXAM, SubAuthenticatedContentConfigure.CONTENT_TYPE_DIMENSION,
                        SubAuthenticatedContentConfigure.CONTENT_TYPE_RECOURSE, SubAuthenticatedContentConfigure.CONTENT_TYPE_ACCUSE,
                        SubAuthenticatedContentConfigure.CONTENT_TYPE_BLANK).contains(configure.getContentType()), ErrorCode.SubAuthenticatedConfigContentTypeIsNull);
                //配置列表的order必填
                Assert.assertTrue(configure.getOrder() != null, ErrorCode.SubAuthenticatedConfigOrderIsNull);
                //名称必填
                Assert.assertTrue(configure.getContentName() != null, ErrorCode.SubAuthenticatedConfigContentNameIsNull);
                //维度，资源，举证，占位等设置默认名称
                configure.setContentName(SubAuthenticatedContentConfigure.CONTENT_TYPE_DIMENSION.equals(configure.getContentType()) ? SubAuthenticatedContentConfigure.CONTENT_NAME_DIMENSION : configure.getContentName());
                configure.setContentName(SubAuthenticatedContentConfigure.CONTENT_TYPE_RECOURSE.equals(configure.getContentType()) ? SubAuthenticatedContentConfigure.CONTENT_NAME_RECOURSE : configure.getContentName());
                configure.setContentName(SubAuthenticatedContentConfigure.CONTENT_TYPE_ACCUSE.equals(configure.getContentType()) ? SubAuthenticatedContentConfigure.CONTENT_NAME_ACCUSE : configure.getContentName());
                configure.setContentName(SubAuthenticatedContentConfigure.CONTENT_TYPE_BLANK.equals(configure.getContentType()) ? SubAuthenticatedContentConfigure.CONTENT_NAME_BLANK : configure.getContentName());
                //校验维度内容必填项
                if (!ObjectUtils.isEmpty(configure.getSubAuthenticatedDimensions())) {
                    configure.getSubAuthenticatedDimensions().forEach(dimension -> {
                        Assert.assertTrue(!StringUtils.isEmpty(dimension.getName()), ErrorCode.SubAuthenticatedConfigDimensionNameIsNull);
                        Assert.assertTrue(!StringUtils.isEmpty(dimension.getCode()), ErrorCode.SubAuthenticatedConfigDimensionCodeIsNull);
                        Assert.assertTrue(dimension.getOrder() != null, ErrorCode.SubAuthenticatedConfigOrderIsNull);
                    });
                }
                if (SubAuthenticatedContentConfigure.CONTENT_TYPE_STUDY.equals(configure.getContentType())&&ObjectUtils.isEmpty(configure.getSubAuthenticatedStudyOnlines())) {
                    throw new UnprocessableException(ErrorCode.SubAuthenticatedOnlineIsNull);
                }
                if (SubAuthenticatedContentConfigure.CONTENT_TYPE_DIMENSION.equals(configure.getContentType())&&ObjectUtils.isEmpty(configure.getSubAuthenticatedDimensions())) {
                    throw new UnprocessableException(ErrorCode.SubAuthenticatedDimensionIsNull);
                }
                //学习组
                if (!ObjectUtils.isEmpty(configure.getSubAuthenticatedStudyOnlines())) {
                    configure.getSubAuthenticatedStudyOnlines().forEach(online -> {
                        Assert.assertTrue(!StringUtils.isEmpty(online.getBusinessId()), ErrorCode.SubAuthenticatedConfigBusinessIdIsNull);
                        Assert.assertTrue(online.getBusinessType() != null && Arrays.asList(SubAuthenticatedStudyOnline.BUSINESS_TYPE_COURSE,
                                SubAuthenticatedStudyOnline.BUSINESS_TYPE_SUBJECT).contains(online.getBusinessType()), ErrorCode.SubAuthenticatedConfigBusinessTypeIsNull);
                        Assert.assertTrue(online.getOrder() != null, ErrorCode.SubAuthenticatedConfigOrderIsNull);
                    });
                }
                //考试组
                if (!ObjectUtils.isEmpty(configure.getSubAuthenticatedExamGroups())) {
                    List<String> businessIds = configure.getSubAuthenticatedExamGroups().stream().map(SubAuthenticatedExamGroup::getBusinessId).collect(Collectors.toList());
                    List<String> distinctBusinessIds = configure.getSubAuthenticatedExamGroups().stream().map(SubAuthenticatedExamGroup::getBusinessId).distinct().collect(Collectors.toList());
                    examIds.addAll(businessIds);
                    // 验证这个考试组内是否包含重复考试
                    Assert.assertTrue(businessIds.size() == distinctBusinessIds.size(), ErrorCode.HaveRepeatedExamsInTheExaminationGroup);
                    configure.getSubAuthenticatedExamGroups().forEach(exam -> {
                        Assert.assertTrue(!StringUtils.isEmpty(exam.getBusinessId()), ErrorCode.SubAuthenticatedConfigBusinessIdIsNull);
                        Assert.assertTrue(!StringUtils.isEmpty(exam.getBusinessName()), ErrorCode.SubAuthenticatedConfigBusinessNameIsNull);
                        Assert.assertTrue(exam.getBusinessType() != null && Arrays.asList(SubAuthenticatedStudyOnline.BUSINESS_TYPE_MOCK_EXAM,
                                SubAuthenticatedStudyOnline.BUSINESS_TYPE_AUTH_EXAM).contains(exam.getBusinessType()), ErrorCode.SubAuthenticatedConfigBusinessTypeIsNull);
                        Assert.assertTrue(exam.getOrder() != null, ErrorCode.SubAuthenticatedConfigOrderIsNull);
                    });
                }
            });
            // 验证整个子认证内是否包含重复考试
            List<String> distinctExamIds = examIds.stream().distinct().collect(Collectors.toList());
            Assert.assertTrue(examIds.size() == distinctExamIds.size(), ErrorCode.HaveRepeatedExamsInTheSubAuthenticated);
            // 验证整个子认证内的考试是否被其他子认证引用
            if (CollectionUtils.isNotEmpty(distinctExamIds)) {
                Assert.assertTrue(!strongBaseService.examGroupBeUsed(id, distinctExamIds),ErrorCode.ExamsInSubCertificationsHaveBeenReferencedByOtherSubCertifications);
            }
        }
        return contentConfigureList;
    }

    @JSON("*")
    @Param(name = "type", required = true, type = Integer.class)
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "attachmentId", required = true)
    @RequestMapping(value = "/upload-materials", method = RequestMethod.POST)
    public SubAuthenticatedResourceAuditRecord uploadMaterials(RequestContext context, Subject<Member> member) {
        return subAuthenticatedService.saveMaterials(
                context.getInteger("type"),
                context.getString("subAuthenticatedId"),
                context.getString("attachmentId"),
                member.getCurrentUserId()
        );
    }

    /**
     * 查询单个人上传的记录
     * ，用来显示是否已经上传过
     *
     * @param context
     * @return
     */
    @JSON("*.*")
    @Param(name = "subAuthenticatedId", required = true)
    @RequestMapping(value = "/find-material", method = RequestMethod.GET)
    public List<SubAuthenticatedResourceAuditRecord> findMaterial(RequestContext context, Subject<Member> member) {
        List<SubAuthenticatedResourceAuditRecord> materials = subAuthenticatedService.findMaterial(context.getString("subAuthenticatedId"), member.getCurrentUserId());
        // 组装附件名称
        List<String> attachmentId = materials.stream().map(SubAuthenticatedResourceAuditRecordEntity::getAttachementId).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, String> attachMap = Optional.ofNullable(fileService.findByIds(attachmentId)).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(AttachmentEntity::getId, AttachmentEntity::getFilename, (v1, v2) -> v1));
        for(SubAuthenticatedResourceAuditRecord material : materials) {
            String id = material.getAttachementId();
            material.setAttachmentName(attachMap.get(id));
        }
        return materials;
    }

    @JSON("*.*")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "type", required = true, type = Integer.class)
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "name") // 姓名
    @Param(name = "uri")
    @Param(name = "code") // 员工编号
    @Param(name = "organizationId") // 归属部门
    @Param(name = "contain", type = Integer.class) // 是否包含子级【0：不包含；1：包含】,配合组织使用
    @Param(name = "beginDate" , type = Long.class)
    @Param(name = "endDate" , type = Long.class)
    @RequestMapping(value = "/find-materials", method = RequestMethod.GET)
    public PagedResult<Map<String, String>> findMaterials(RequestContext context, Subject<Member> member) {
        Optional<String> organizationId = context.getOptionalString("organizationId");
        String currentUserId = member.getCurrentUserId();
        List<String> grantOrganizationIds = getOrg(context, organizationId, currentUserId);
        PagedResult<Map<String, String>> materials = subAuthenticatedService.findMaterials(context.getInteger("type"),
                context.getString("subAuthenticatedId"),
                context.getOptionalString("name"), context.getOptionalString("code"), grantOrganizationIds,
                context.getInteger("page"), context.getInteger("pageSize"),
                context.getOptionalLong("beginDate"),
                context.getOptionalLong("endDate")
        );
        // 组装附件名称
        List<String> attachmentId = materials.getItems().stream().map(x -> x.get("attachmentId")).filter(Objects::nonNull).collect(Collectors.toList());
        Map<String, String> attachMap = Optional.ofNullable(fileService.findByIds(attachmentId)).orElse(new ArrayList<>())
                .stream().collect(Collectors.toMap(AttachmentEntity::getId, AttachmentEntity::getFilename, (v1, v2) -> v1));
        for(Map<String, String> material : materials.getItems()) {
            String id = material.get("attachmentId");
            material.put("attachmentName", attachMap.get(id));
            material.put("fullName",EncryptUtil.aesEncrypt(material.get("fullName"), null));
            material.put("memberName",DesensitizationUtil.desensitizeEmployeeId(material.get("memberName")));
        }
        return materials;
    }

    /**
     * 后台-批量下载审核材料(copy exam)
     *
     * @param context
     * @return
     */
    @Param(name = "ids", required = true)
    @RequestMapping(value = "/export-materials-files", method = RequestMethod.POST)
    public Object exportMaterials(RequestContext context) {
        List<String> ids = Arrays.asList(context.getString("ids").split(","));
        List<SubAuthenticatedResourceAuditRecord> listAudit = subAuthenticatedService.findMaterialsByIds(ids);
        List<String> list = listAudit.stream()
                .map(SubAuthenticatedResourceAuditRecordEntity::getAttachementId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        if (listAudit.isEmpty()){
            return null;
        }
        String zipName = "\u8d44\u6599\u5ba1\u6838";
        if (listAudit.get(0).getType().equals(SubAuthenticatedResourceAuditRecord.EVIDENCE)) {
            zipName = "\u4e3e\u8bc1\u6750\u6599";
        }
        File tempDir = null;
        try {
            tempDir = Files.createTempDirectory(String.valueOf(System.nanoTime())).toFile();
            handleFiles(tempDir, list);
            FileUtil.zipDirWithName(tempDir.getAbsolutePath(), zipName);
            return writeResponse(context, tempDir, zipName);
        } catch (Exception e) {
            LOGGER.error("生成文件失败！", e);
            return null;
        } finally {
            if (tempDir != null) {
                FileUtil.cleanUp(tempDir);
                FileUtil.cleanUp(new File(tempDir.getAbsolutePath() + SubAuthenticatedResourceAuditRecord.ZIP_EXT));
            }
        }
    }

    /**
     * 返回的结构有好多层 希望前端还好
     *
     * @param context
     * @return
     */
    @JSON("*.*.*.*")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "status", type = Integer.class) // 点亮状态 0:代表全部点亮 1:未全部点亮 (和数据库的状态相反)
    @Param(name = "uri")
    @Param(name = "name") // 姓名
    @Param(name = "code") // 员工编号
    @Param(name = "organizationId") // 归属部门
    @Param(name = "contain", type = Integer.class) // 是否包含子级【0：不包含；1：包含】,配合组织使用
    @Param(name = "subAuthenticatedId", required = true)
    @RequestMapping(value = "/query-dimension", method = RequestMethod.GET)
    public PagedResult<Map<String, Object>> queryDimension(RequestContext context, Subject<Member> member) {

        Optional<String> organizationId = context.getOptionalString("organizationId");
        String currentUserId = member.getCurrentUserId();
        List<String> grantOrganizationIds = getOrg(context, organizationId, currentUserId);
        return subAuthenticatedService.getDimensions(context.getOptionalInteger("status"), context.getOptionalString("name"),
                context.getOptionalString("code"), context.getString("subAuthenticatedId"), grantOrganizationIds,
                context.getInteger("page"), context.getInteger("pageSize"));
    }

    /**
     * 获取dimension 的元数据
     */
    @JSON("*")
    @Param(name = "uri")
    @Param(name = "subAuthenticatedId", required = true)
    @RequestMapping(value = "/query-dimension-items", method = RequestMethod.GET)
    @Permitted
    public List<SubAuthenticatedDimension> queryDimensionItem(RequestContext context) {
        List<SubAuthenticatedDimension> list = subAuthenticatedService.getDimensionsBySubId(context.getString("subAuthenticatedId"));
        list.sort(Comparator.comparingInt(SubAuthenticatedDimension::getOrder));
        return list;
    }

    /**
     * 维度管理-编辑
     *
     * @param context
     * @return
     */
    @JSON("*")
    @Param(name = "data", required = true)
    @Param(name = "memberId", required = true)
    @Param(name = "subAuthenticatedId", required = true)
    @ResponseBody
    @RequestMapping(value = "/edit-dimension", method = RequestMethod.PUT)
    @Permitted
    public void editDimension(RequestContext context) {
        String data = context.getString("data");
        String memberId = context.getString("memberId");
        String subAuthenticatedId = context.getString("subAuthenticatedId");
        Optional.ofNullable(JSONArray.parseArray(data, Map.class)).ifPresent(list -> {
            subAuthenticatedService.upsetDimensionById(list, memberId, subAuthenticatedId);
        });
    }

    /**
     * 维度管理-编辑回显数据
     *
     * @param context
     * @return
     */
    @JSON("id,isLighted")
    @JSON("subAuthenticatedDimension.*")
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "memberId", required = true)
    @RequestMapping(value = "/member-dimension", method = RequestMethod.GET)
    @Permitted
    public List<SubAuthenticatedMemberDimension> queryMemberDimension(RequestContext context) {
        return subAuthenticatedService.queryMemberDimension(context.getString("memberId"), context.getString("subAuthenticatedId"));
    }

    /**
     * 维度管理——导入数据
     */
    @JSON("*.*.*")
    @Param(name = "uri", required = true)
    @Param(name = "subAuthenticatedId", required = true)
    @RequestMapping(value = "/import-dimension", method = RequestMethod.POST)
    public Map<String, Object> importDimension(@RequestParam("file") MultipartFile file, RequestContext context,Subject<Member> subject) throws IOException {

        String subAuthenticatedId = context.getString("subAuthenticatedId");
        String fileName = file.getOriginalFilename();
        if (!(fileName.endsWith(".xls") || fileName.endsWith(".xlsx"))) {
            return null;
        }
        List<SubAuthenticatedDimension> dimensions = subAuthenticatedService.getDimensionsBySubId(subAuthenticatedId);
        Map<String, String> dimensionMapping = dimensions
                .stream().collect(Collectors.toMap(SubAuthenticatedDimension::getCode, SubAuthenticatedDimension::getId, (v1, v2) -> v1,LinkedHashMap::new));
        // 没有维度的不能导入
        ErrorCode.ImportNullFile.throwIf(dimensionMapping.keySet().size() == 0);

        InputStream inputStream = file.getInputStream();
        Reader.Result result = subAuthenticatedKit.getDimensionReaderInstance(dimensionMapping.keySet().size()).read(inputStream);

        // 文档不能为空
        ErrorCode.ImportNullFile.throwIf(result.getCorrectRows().size() <= 1);
        ErrorCode.EXCEL_ERROR.throwIf(!result.isCellMatched());
        Map<String,String> permissionsMapping = this.getOrg(context, Optional.empty(), subject.getCurrentUserId(), Optional.of(1)).stream().collect(Collectors.toMap(k -> k, v -> v,(v1,v2)->null));


        try {
            // 校验并初始化数据
            List<String> memberCodes = result.getCorrectRows()
                    .stream().skip(1)
                    .map(x -> x.get(SubAuthenticatedMemberDimension.TEMPLATE_MEMBER_CODE, String.class))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            Map<String, Member> memberMap = subAuthenticatedService.getMemberCodeNameMap(memberCodes);
            DefaultResult errorResult = new DefaultResult();

            List<SubAuthenticatedMemberDimension> collect = subAuthenticatedKit.getSubAuthenticatedMemberDimensions(errorResult, result, memberMap, dimensionMapping,permissionsMapping);
            subAuthenticatedService.upsetSubAuthenticatedMemberDimension(collect);
            //  生成错误模版并写入文件返回
            Attachment errorAttachment = subAuthenticatedKit.dimensionErrorWriter(errorResult, dimensions);
            // 返回解析失败的excel 数据
            return new HashMap<String, Object>() {{
                put("errors", errorResult.getErrors().stream().map(x -> new ExcelErrorCode(x.getRow(), x.getColumn(), x.getCode().getCode())).collect(Collectors.toList()));
                put("errorFileId", errorAttachment.getId());
                put("successCount", collect.size());
                put("failCount", errorResult.getErrors().size());
            }};
        } catch (Exception igneous) {
            LOGGER.error(igneous.getMessage());
            ErrorCode.EXCEL_ERROR.throwIf(true);
        }
        return null;

    }

    /**
     * 维度管理——导出模版
     */
    @JSON("*.*.*")
    @Param(name = "subAuthenticatedId", required = true)
    @Permitted
    @RequestMapping(value = "/export-dimension-template", method = RequestMethod.GET)
    public void exportDimensionTemplate(RequestContext context) throws IOException {
        List<SubAuthenticatedDimension> list = subAuthenticatedService.getDimensionsBySubId(context.getString("subAuthenticatedId"));
        Writer writer = subAuthenticatedKit.buildDimensionWriter(context, list);
        writer.write(context.getResponse().getOutputStream());
    }

    /**
     * 查询子认证下的证书列表
     *
     * @param context
     * @param member
     * @return
     */
    @JSON("*.*")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "uri")
    @Param(name = "name") // 姓名
    @Param(name = "code") // 员工编号
    @Param(name = "organizationId") // 归属部门
    @Param(name = "contain", type = Integer.class) // 是否包含子级【0：不包含；1：包含】,配合组织使用
    @Param(name = "subAuthenticatedId", required = true)
    @RequestMapping(value = "/query-certificate", method = RequestMethod.GET)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    public PagedResult<Map<String, Object>> queryCertificate(RequestContext context, Subject<Member> member) {

        Optional<String> organizationId = context.getOptionalString("organizationId");
        String currentUserId = member.getCurrentUserId();
        List<String> grantOrganizationIds = getOrg(context, organizationId, currentUserId);
        PagedResult<Map<String, Object>> certificate = subAuthenticatedService.getCertificate(context.getOptionalString("name"), context.getOptionalString("code"), context.getString("subAuthenticatedId"), grantOrganizationIds,
                context.getInteger("page"), context.getInteger("pageSize"));
        if (!org.springframework.util.CollectionUtils.isEmpty(certificate.getItems())) {
            certificate.getItems().forEach(r -> {
                r.put("name", EncryptUtil.aesEncrypt(String.valueOf(r.get("name")), null));
                r.put("code", DesensitizationUtil.desensitizeEmployeeId(String.valueOf(r.get("code"))));
            });
        }
        return certificate;
    }

    /**
     * @param context
     * @param member
     */
    @Param(name = "subAuthenticatedRecordId", required = true)
    @RequestMapping(value = "/update-certificate", method = RequestMethod.PUT)
    @JSON("*")
    public Map<String, String> updateCertificate(RequestContext context, Subject<Member> member) {
        subAuthenticatedService.updateCertificate(context.getString("subAuthenticatedRecordId"));
        return ImmutableMap.of("result", "success");
    }

    /**
     * @param context
     * @param member
     */
    @ResponseBody
    @Param(name = "cerId", required = true)
    @Param(name = "fileId")
    @Param(name = "reasonForDeletion", required = true, validation = {@Validation(value = LengthRangeValidation.NAME, options = "0,50")})
    @RequestMapping(value = "/delete-certificate", method = RequestMethod.POST)
    public void removeCertificate(RequestContext context, Subject<Member> member) {
        subAuthenticatedService.deleteCertificate(context.getString("cerId"), context.getString("reasonForDeletion"), context.getOptionalString("fileId"), member.getCurrentUserId(), true);
    }


    /**
     * 删除证书记录
     *
     * @param context
     * @param member
     * @return
     */
    @JSON("*.*")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "uri")
    @Param(name = "name") // 姓名
    @Param(name = "code") // 员工编号
    @Param(name = "organizationId") // 归属部门
    @Param(name = "contain", type = Integer.class) // 是否包含子级【0：不包含；1：包含】,配合组织使用
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "issuanceOfCertificatesBeginDate", type = Long.class)
    @Param(name = "issuanceOfCertificatesEndDate" , type = Long.class)
    @Param(name = "deleteBeginDate" , type = Long.class)
    @Param(name = "deleteEndDate" , type = Long.class)
    @RequestMapping(value = "/query-delete-certificate", method = RequestMethod.GET)
    @Permitted(perms = {"auth-management/auth-child-management","auth-management/auth-region-management"})
    public PagedResult<Map<String, String>> queryRemoveCertificate(RequestContext context, Subject<Member> member) {
        Optional<String> organizationId = context.getOptionalString("organizationId");
        String currentUserId = member.getCurrentUserId();
        List<String> grantOrganizationIds = getOrg(context, organizationId, currentUserId);
        PagedResult<Map<String, String>> mapList = subAuthenticatedService.getDeleteCertificate(
                context.getOptionalString("name"),
                context.getOptionalString("code"),
                context.getString("subAuthenticatedId"),
                grantOrganizationIds,
                context.getOptionalLong("issuanceOfCertificatesBeginDate"),
                context.getOptionalLong("issuanceOfCertificatesEndDate"),
                context.getOptionalLong("deleteBeginDate"),
                context.getOptionalLong("deleteEndDate"),
                context.getInteger("page"), context.getInteger("pageSize")
        );
        List<String> attachmentIds = mapList.getItems().stream().map(x -> x.get("attachmentId")).collect(Collectors.toList());
        Map<String, String> fileNameMap = fileService.findByIds(attachmentIds).stream().collect(Collectors.toMap(Attachment::getId, Attachment::getFilename, (v1, v2) -> v1));
        mapList.getItems().forEach(x -> x.put("attachmentName", fileNameMap.get(x.get("attachmentId"))));
        return mapList;
    }




    /**
     * 展示导入信息-临时表
     *
     * @param context request根
     * @param subject 授权对象
     */
    @JSON("result")
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "fileId", required = true)
    @RequestMapping(value = "/import-certificate-tmp", method = RequestMethod.GET)
    public Map<String, String> importTmpCertificate(RequestContext context, Subject<Member> subject) {
        String subAuthenticatedId = context.getString("subAuthenticatedId");
        subAuthenticatedKit.handleCertificateTmp(subAuthenticatedId, context.getString("fileId"), subject.getCurrentUserId());
        return ImmutableMap.of("result", "success");
    }

    /**
     * 展示导入信息-临时表
     *
     * @param context request根
     * @param subject 授权对象
     */
    @JSON("*.*.*")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "fileId", required = true)
    @RequestMapping(value = "/find-certificate-tmp", method = RequestMethod.GET)
    public PagedResult<SubAuthenticatedTmp> findTmpCertificate(RequestContext context, Subject<Member> subject) throws IOException {
       return subAuthenticatedService.findTmp(context.getInteger("page"), context.getInteger("pageSize"), context.getString("fileId"),
                context.getString("subAuthenticatedId"), subject.getCurrentUserId());

    }

    /**
     * 删除导入信息-临时表
     *
     * @param context request根
     * @param subject 授权对象
     */
    @JSON("result")
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "fileId", required = true)
    @RequestMapping(value = "/delete-certificate-tmp", method = RequestMethod.DELETE)
    public Map<String, String> deleteTmpCertificate(RequestContext context, Subject<Member> subject) throws IOException {
        subAuthenticatedService.deleteTmp(context.getString("fileId"),
                context.getString("subAuthenticatedId"), subject.getCurrentUserId());
        return ImmutableMap.of("result", "success");
    }
    /**
     * 导入子认证的证书
     *
     * @param context request根
     * @param subject 授权对象
     */
    @ResponseBody
    @JSON("*.*.*")
    @Param(name = "subAuthenticatedId", required = true)
    @Param(name = "fileId", required = true)
    @RequestMapping(value = "/import-certificate", method = RequestMethod.POST)
    public void importCertificate(RequestContext context, Subject<Member> subject) throws IOException {
        String subAuthenticatedId = context.getString("subAuthenticatedId");
        String redisKey = IMPORT_REDIS_KEY_PREFIX + subject.getCurrentUserId();
        String value = cache.get(redisKey, String.class);
        // TODO
//        if (!StringUtils.isEmpty(value)) {
//            throw new UnprocessableException(ErrorCode.CertificateImporting);
//        }
//        cache.set(IMPORT_REDIS_KEY_PREFIX + subject.getCurrentUserId(), 5 * 60);
        Map<String, String> permissionsMapping = getOrg(context, Optional.empty(), subject.getCurrentUserId(), Optional.of(1)).stream().collect(Collectors.toMap(k->k,v->v,(v1,v2)->v1));
        subAuthenticatedKit.handleCertificateImport(subAuthenticatedId, context.getString("fileId"), subject.getCurrentUserId(),permissionsMapping);
    }

    /**
     * 生成文件 工具类中引入service 不合适 所以放这里
     */
    private void handleFiles(File tempDir, List<String> fileIds) {
        List<String> fileNameList = new ArrayList<>();
        for(String fileId : fileIds) {
            fileService.get(fileId).ifPresent(file -> {
                com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
                fastDFSAttachment.setPath(file.getPath());
                byte[] bytes = attachmentResolver.downloadFile(fastDFSAttachment);
                String fileName = file.getFilename();
                int lastDotIndex = fileName.lastIndexOf('.');
                String extension = "";
                if (lastDotIndex!= -1){
                    extension = fileName.substring(lastDotIndex);
                    fileName = fileName.replace(extension, "");
                }
                while (fileNameList.contains(fileName)){
                    fileName = fileName.concat(getRandom());
                }
                fileNameList.add(fileName);
                if (bytes != null) {
                    FileUtil.convertByte(bytes, tempDir + File.separator + fileName.concat(extension));
                }
            });
        }

    }
    public static String getRandom() {
        Random random = new Random();
        int randomNumber = random.nextInt(9000) + 1000; // 生成1000到9999之间的随机数
        return String.format("%04d", randomNumber);
    }

    private Object writeResponse(RequestContext rc, File tempDir, String name) throws IOException {
        String zipExt = ".zip";
        String fileName = tempDir.getParentFile().getAbsolutePath() + File.separator + name;
        BrowserUtil.setHttpServletResponse(rc, name + zipExt);
        try (FileInputStream in = new FileInputStream(fileName + zipExt);
             OutputStream out = rc.getResponse().getOutputStream()) {
            byte[] buffer = new byte[102400];
            int len;
            while ((len = in.read(buffer)) > 0) {
                out.write(buffer, 0, len);
            }
            rc.getResponse().setContentLength(new BigDecimal(in.getChannel().size()).intValue());
            InputStreamResource resource = new InputStreamResource(new ByteArrayInputStream(buffer));
            return new ResponseEntity<>(resource, new HttpHeaders(), HttpStatus.OK);
        }
    }

    private List<String> getOrg(RequestContext context, Optional<String> organizationId, String currentUserId, Optional<Integer> contain) {
        List<String> grantOrganizationIds;
        if (contain.orElse(0) == 0) {
            grantOrganizationIds = courseInfoAdminKit.findGrantTopOrganizationIds(currentUserId, organizationId, context.getOptionalString("uri").orElse(URI));
        } else {
            grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(currentUserId, organizationId, context.getOptionalString("uri").orElse(URI));
        }
        return grantOrganizationIds;
    }

    private List<String> getOrg(RequestContext context, Optional<String> organizationId, String currentUserId) {
        return getOrg(context, organizationId, currentUserId, context.getOptionalInteger("contain"));
    }

}

class ExcelErrorCode {
    private int row = -1;
    private int column = -1;
    private int code = -1;

    public ExcelErrorCode() {
    }

    public ExcelErrorCode(int row, int column, int code) {
        this.row = row;
        this.column = column;
        this.code = code;
    }

    public int getRow() {
        return row;
    }

    public void setRow(int row) {
        this.row = row;
    }

    public int getColumn() {
        return column;
    }

    public void setColumn(int column) {
        this.column = column;
    }

    public int getCode() {
        return code;
    }

    public void setCode(int code) {
        this.code = code;
    }
}
