package com.zxy.product.course.web.controller.studyMap;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.ability.AbilityService;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.web.kit.CourseInfoAdminKit;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.api.permission.OrganizationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import com.zxy.product.course.entity.Ability;
import com.zxy.product.course.entity.AbilityBusiness;

import java.util.*;

/**
 * 学习地图
 */
@Controller
@RequestMapping("/ability")
public class AbilityController {

    private static final Logger LOGGER = LoggerFactory.getLogger(AbilityController.class);

    private AbilityService abilityService;
    private CourseInfoAdminKit courseInfoAdminKit;
    private GrantService grantService;
    private OrganizationService organizationService;


    @Autowired
    public void setCourseInfoAdminKit(CourseInfoAdminKit courseInfoAdminKit) {
        this.courseInfoAdminKit = courseInfoAdminKit;
    }

    @Autowired
    public void setAbilityService(AbilityService abilityService) {
        this.abilityService = abilityService;
    }


    @Autowired
    public void setGrantService(GrantService grantService) {
        this.grantService = grantService;
    }
    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    /**
     * 新增能力
     * 会同步更新、新增、删除内容
     */
    @RequestMapping(value = "/add", method = RequestMethod.POST)
    @Param(name = "name", value = "能力名称", required = true)
    @Param(name = "code", value = "能力编码", required = true)
    @Param(name = "organizationId", value = "能力归属部门", required = true)
    @Param(name = "description", value = "能力描述")
    @Param(name = "type", value = "来源 0:能力管理  1：学习地图")
    @Param(name = "status", value = "状态：0-未发布 1-已发布 2-取消发布", type = Integer.class)
    @Param(name = "businesses", value = "配置的内容列表，不传不更新内容，JSON数组：[{\"businessId\":\"资源id\",\"businessType\":\"资源类型 16-课程 10-专题\",\"isRequire\":\"0-选修 1-必修\",\"sequence\":\"序号\"}]")
    @Param(name = "abilityCategory", value = "能力管理大类")
    @Param(name = "abilitySubCategory", value = "能力管理小类")
    @Param(name = "level", value = "级别")
    @JSON("id,name,code,organizationId,status,description,level,abilityCategory,abilitySubCategory")
    @JSON("business.(id,businessId,businessType,sequence,isRequire)")
    public Ability insert(RequestContext context, Subject<Member> subject) {
        Optional<List<AbilityBusiness>> businesses = context.getOptionalString("businesses").map(t -> JSONObject.parseArray(t, AbilityBusiness.class));
        return abilityService.insert(
                context.getString("name"),
                context.getString("code"),
                context.getString("organizationId"),
                Optional.of(subject.getRootOrganizationId()),
                context.getOptionalString("description"),
                context.getOptionalInteger("status"),
                context.getOptionalInteger("type"),
                businesses,
                context.getOptionalInteger("abilityCategory"),
                context.getOptionalString("abilitySubCategory"),
                context.getOptionalInteger("level")
        );
    }

    /**
     * 新增能力
     * 会同步更新、新增、删除内容
     */
    @RequestMapping(value = "/batchAdd", method = RequestMethod.POST)
    @Param(name = "abilityJson", value = "能力json数组", required = true)
    @JSON("*")
    public List<Ability> batchAdd(RequestContext context, Subject<Member> subject) {
        return abilityService.batchAdd(
                context.getString("abilityJson")
        );
    }

    /**
     * 编辑能力
     *
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Param(name = "id", value = "能力主键id", required = true)
    @Param(name = "name", value = "能力名称")
    @Param(name = "code", value = "能力编码")
    @Param(name = "organizationId", value = "能力归属部门")
    @Param(name = "description", value = "能力描述")
    @Param(name = "status", value = "状态：0-未发布 1-已发布 2-取消发布", type = Integer.class)
    @Param(name = "businesses", value = "配置的内容列表，不传不更新内容，JSON数组：[{\"id\":\"内容主键\",\"businessId\":\"资源id\",\"businessType\":\"资源类型 1-课程 2-专题\",\"isRequire\":\"0-选修 1-必修\",\"sequence\":\"序号\"}]")
    @Param(name = "abilityCategory", value = "能力管理大类")
    @Param(name = "abilitySubCategory", value = "能力管理小类")
    @JSON("id,name,code,organizationId,status,description,level,abilityCategory,abilitySubCategory")
    @JSON("businesses.(id,businessId,businessType,sequence,isRequire)")
    @Param(name = "level", value = "级别")
    public Ability update(RequestContext context) {
        Optional<List<AbilityBusiness>> businesses = context.getOptionalString("businesses").map(t -> JSONObject.parseArray(t, AbilityBusiness.class));
        return abilityService.update(
                context.getString("id"),
                context.getOptionalString("name"),
                context.getOptionalString("code"),
                context.getOptionalString("organizationId"),
                context.getOptionalString("description"),
                context.getOptionalInteger("status"),
                businesses,
                context.getOptionalInteger("abilityCategory"),
                context.getOptionalString("abilitySubCategory"),
                context.getOptionalInteger("level")
        );
    }

    /**
     * 删除能力
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Param(name = "id", value = "能力主键id", required = true)
    @JSON("*")
    public Integer delete(RequestContext context) {
        return abilityService.delete(context.getString("id"));
    }

    /**
     * 能力列表
     */
    @RequestMapping(value = "/find-page", method = RequestMethod.GET)
    @Param(name = "page", required = true, type = Integer.class)
    @Param(name = "pageSize", required = true, type = Integer.class)
    @Param(name = "name", value = "能力名称")
    @Param(name = "level", value = "能力级别")
    @Param(name = "code", value = "能力编码")
    @Param(name = "contain", value = "是否包含子部门")
    @Param(name = "organizationId", value = "能力归属部门")
    @Param(name = "organizationPath",value = "部门path")
    @Param(name = "status", value = "状态：0-未发布 1-已发布 2-取消发布", type = Integer.class)
    @Param(name = "abilityCategory", value = "能力管理大类")
    @Param(name = "abilitySubCategory", value = "能力管理小类")
    @JSON("items.(id,name,code,organizationId,status,description,level,abilityCategory,abilitySubCategory)")
    @JSON("items.organization.(id,name,path)")
    @JSON("recordCount")
    @Permitted()
    public PagedResult<Ability> findPage(RequestContext context, Subject<Member> subject) {
        Optional<String> organizationId = context.getOptionalString("organizationId");
        Map<String, Set<String>> grantOrganizationPathMap =
                grantService.findGrantOrganizationByUri(subject.getCurrentUserId(), context.getOptionalString("uri").orElse(null), subject.getRootOrganizationId());
        Integer contain = context.getOptionalInteger("contain").orElse(0);

        return abilityService.findPageNew(
                context.getInteger("page"),
                context.getInteger("pageSize"),
                context.getOptionalString("organizationPath"),
                context.getOptionalString("name"),
                context.getOptionalString("code"),
                context.getOptionalInteger("status"),
                organizationId,
                context.getOptionalInteger("level"),
                grantOrganizationPathMap,
                contain,
                context.getOptionalBoolean(Ability.LAST_PAGE).orElse(Boolean.FALSE),
                context.getOptionalInteger("abilityCategory"),
                context.getOptionalString("abilitySubCategory")
        );
    }


    /**
     * 学习地图选择能力列表
     */
    @RequestMapping(value = "/find-map-page", method = RequestMethod.GET)
    @Param(name = "page", required = true, type = Integer.class)
    @Param(name = "pageSize", required = true, type = Integer.class)
    @Param(name = "name", value = "能力名称")
    @Param(name = "code", value = "能力编码")
    @Param(name = "level", value = "级别")
    @Param(name = "selectIds", value = "已选择的能力id集合")
    @Param(name = "abilityCategory", value = "能力管理大类")
    @Param(name = "abilitySubCategory", value = "能力管理小类")
    @JSON("items.(id,name,code,organizationId,status,level,description,abilityCategory,abilitySubCategory)")
    @JSON("recordCount")
    @Permitted()
    public PagedResult<Ability> find(RequestContext context, Subject<Member> subject) {
        Set<String> organizationIds = new HashSet<>();
        organizationIds.add(subject.getCurrentUser().getOrganizationId());
        organizationIds.add("");
        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));
        organizationIds.addAll(courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(), Optional.empty(), context.getOptionalString("uri").orElse("course-study/studyMap-info")));

        return abilityService.findPage(
                context.getInteger("page"),
                context.getInteger("pageSize"),
                context.getOptionalString("name"),
                context.getOptionalString("code"),
                context.getOptionalInteger("status"),
                organizationIds,
                selectIds,
                context.getOptionalInteger("level"),
                context.getOptionalInteger("abilityCategory"),
                context.getOptionalString("abilitySubCategory")
        );
    }

    /**
     * 获取多个能力关联的内容
     */
    @RequestMapping(value = "/getByIds", method = RequestMethod.GET)
    @Permitted
    @Param(name = "abilityIds", required = true, value = "能力id集合")
    @JSON("id,name,code,organizationId,status,description,businesses")
    @JSON("businesses.(id,name,abilityId,businessId,businessType,sequence,isRequire,businessName,name,sectionType,required)")
    public List<Ability> getByIds(RequestContext context, Subject<Member> subject) {
        String abilityIds = context.getOptionalString("abilityIds").orElse("");
        return abilityService.getByIds(abilityIds);
    }

    /**
     * 能力详情
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Param(name = "id", value = "能力id", required = true)
    @JSON("id,name,code,organizationId,status,description,level,abilityCategory,abilitySubCategory")
    @JSON("organization.(id,name,path)")
    @JSON("businesses.(id,name,abilityId,businessId,businessType,sequence,isRequire,businessName)")
    @Permitted()
    public Ability get(RequestContext context) {
        return abilityService.get(context.getString("id"));
    }

    /**
     * 能力发布&取消发布
     */
    @RequestMapping(value = "/edit-status/{id}", method = RequestMethod.PUT)
    @Param(name = "id", value = "能力id", required = true)
    @Param(name = "status", value = "状态：0-未发布 1-已发布 2-取消发布", type = Integer.class, required = true)
    @JSON("id,name,code,organizationId,status,description")
    public Ability changeStatus(RequestContext context) {
        return abilityService.changeStatus(context.getString("id"), context.getInteger("status"));
    }

    /**
     * 管理端-能力配置 能力启用/禁用
     */
//    @RequestMapping(value = "/batch/edit-status", method = RequestMethod.PUT)
//    @Param(name = "ids", value = "能力id集,多个id ',' 分隔", required = true)
//    @Param(name = "status", value = "状态：1-发布 2-取消发布", type = Integer.class, required = true)
//    @JSON("successCount,failCount,failIds,failMsg")
//    public Map<String, String> batchChangeStatus(RequestContext context) {
//        return abilityService.batchChangeStatus(Arrays.asList(context.getString("ids").split(CommonConstant.SEPARATOR_COMMA)), context.getInteger("status"));
//    }

    /**
     * 查询能力编码是否已被使用
     *
     * @return true-已使用 false-未被使用
     */
    @RequestMapping(value = "/code-used", method = RequestMethod.GET)
    @Param(name = "id", value = "能力主键id")
    @Param(name = "code", value = "能力编码", required = true)
    @JSON("result")
    @Permitted()
    public ImmutableMap<String, Object> codeUsed(RequestContext context, Subject<Member> subject) {
        return ImmutableMap.of("result", abilityService.codeUsed(context.getOptionalString("id"), Optional.of(subject.getRootOrganizationId()), context.getString("code")));
    }

    /**
     * 查询能力管理大类
     * @param subject
     * @return
     */
    @RequestMapping(value = "/find-ability-category", method = RequestMethod.GET)
    @JSON("*")
    public Map<Integer, String> findAbilityCategory(Subject<Member> subject) {
        return Ability.abilityCategoryMap;
    }

    /**
     * 复制能力
     */
    @RequestMapping(value = "/copy", method = RequestMethod.POST)
    @Param(name = "abilityId", value = "一级能力Id", required = true)
    @Permitted()
    @JSON("id,name,code,organizationId,status,description,level,abilityCategory,abilitySubCategory")
    @JSON("business.(id,businessId,businessType,sequence,isRequire)")
    public Ability copyAbility(RequestContext context, Subject<Member> subject) {
        String rootId = organizationService.findMaxGrantOriganization(subject.getCurrentUserId(), Ability.ABILITY_TWO_LEVEL_URI).getId();
        return abilityService.copyOneLevelAbility(context.getString("abilityId"),rootId);
    }


    /**
     * 新增的能力检查新增后是几级的
     */
    @RequestMapping(value = "/check-add-level", method = RequestMethod.GET)
    @JSON("level")
    public Map<String, Integer> checkAddLevel(Subject<Member> subject) {
        int level = subject.getRootOrganizationId().equals(subject.getCurrentUser().getOrganizationId())?Ability.ABILITY_LEVEL_ONE:Ability.ABILITY_LEVEL_TWO;
        return ImmutableMap.of("level", level);
    }

    /**
     * 二级能力列表
     */
    @RequestMapping(value = "/find-level-two-page", method = RequestMethod.GET)
    @Param(name = "page", required = true, type = Integer.class)
    @Param(name = "pageSize", required = true, type = Integer.class)
    @Param(name = "name", value = "能力名称")
    @Param(name = "level", value = "能力级别")
    @Param(name = "code", value = "能力编码")
    @Param(name = "contain", value = "是否包含子部门")
    @Param(name = "organizationId", value = "能力归属部门")
    @Param(name = "organizationPath",value = "部门path")
    @Param(name = "status", value = "状态：0-未发布 1-已发布 2-取消发布", type = Integer.class)
    @Param(name = "abilityCategory", value = "能力管理大类")
    @Param(name = "abilitySubCategory", value = "能力管理小类")
    @JSON("items.(id,name,code,organizationId,status,description,level,abilityCategory,abilitySubCategory)")
    @JSON("items.organization.(id,name,path)")
    @JSON("recordCount")
    @Permitted()
    public PagedResult<Ability> findLevelTwoPage(RequestContext context, Subject<Member> subject) {
        Optional<String> organizationId = context.getOptionalString("organizationId");
        Map<String, Set<String>> grantOrganizationPathMap =
                grantService.findGrantOrganizationByUri(subject.getCurrentUserId(), context.getOptionalString("uri").orElse(null), subject.getRootOrganizationId());
        Integer contain = context.getOptionalInteger("contain").orElse(0);

        return abilityService.findLevelTwoPage(
                context.getInteger("page"),
                context.getInteger("pageSize"),
                context.getOptionalString("organizationPath"),
                context.getOptionalString("name"),
                context.getOptionalString("code"),
                context.getOptionalInteger("status"),
                organizationId,
                context.getOptionalInteger("level"),
                grantOrganizationPathMap,
                contain,
                context.getOptionalBoolean(Ability.LAST_PAGE).orElse(Boolean.FALSE),
                context.getOptionalInteger("abilityCategory"),
                context.getOptionalString("abilitySubCategory")

        );
    }

}
