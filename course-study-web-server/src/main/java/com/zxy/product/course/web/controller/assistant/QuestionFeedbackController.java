package com.zxy.product.course.web.controller.assistant;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.course.QuestionFeedbackService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.entity.CourseFeedback;
import com.zxy.product.course.entity.Member;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/29/9:30
 * @Description:
 */
@Controller
@RequestMapping("/feedback")
public class QuestionFeedbackController {


    private QuestionFeedbackService questionFeedbackService;

    @Autowired
    public void setQuestionFeedbackService(QuestionFeedbackService questionFeedbackService) {
        this.questionFeedbackService = questionFeedbackService;
    }


    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "questionContent", required = true)
    @Param(name = "answerContent", required = true)
    @Param(name = "questionId", required = true)
    @Param(name = "like", type = Integer.class)
    @Param(name = "feedbackContent")
    @Param(name = "feedbackType",type = Integer.class)
    // @Permitted
    @JSON("*.*")
    public Map<String, Object> add(RequestContext context, Subject<Member> subject){
        Map<String, Object> map = new HashMap<>();
        CourseFeedback feedback = setData(context, subject, Optional.empty());
        questionFeedbackService.add(feedback);
        map.put("result", "success");
        map.put("id", feedback.getId());
        return map;
    }

    @RequestMapping(value = "/{id}",method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "questionContent", required = true)
    @Param(name = "answerContent", required = true)
    @Param(name = "questionId", required = true)
    @Param(name = "like", type = Integer.class)
    @Param(name = "feedbackContent")
    @Param(name = "feedbackType",type = Integer.class)
    // @Permitted
    @JSON("*.*")
    public Map<String, Object> update(RequestContext context, Subject<Member> subject){
        questionFeedbackService.update(setData(context,subject, Optional.ofNullable(context.getString("id"))));
        return ImmutableMap.of("result", "success");
    }

    private CourseFeedback setData(RequestContext context, Subject<Member> subject, Optional<String> id){
        CourseFeedback feedback = new CourseFeedback();
        if(id.isPresent()){
            Optional<CourseFeedback> optional = questionFeedbackService.getOptional(id.get());
            if (!optional.isPresent()){
                throw new UnprocessableException(ErrorCode.entityIsNull);
            } else if (Objects.nonNull(optional.get().getFeedbackContent())){
                throw new UnprocessableException(ErrorCode.entityForbbidUpdate);
            }
            feedback = optional.get();
        }else{
            feedback.forInsert();
        }
        feedback.setAnswerContent(context.getString("answerContent"));
        feedback.setModifyDate(System.currentTimeMillis());
        feedback.setQuestionId(context.getString("questionId"));
        feedback.setQuestionContent(context.getString("questionContent"));
        feedback.setLike(context.getOptionalInteger("like").orElse(null));
        feedback.setFeedbackContent(context.getOptionalString("feedbackContent").orElse(null));
        feedback.setFeedbackType(context.getOptionalInteger("feedbackType").orElse(null));
        feedback.setCreateMemberId(subject.getCurrentUserId());
        return feedback;
    }

    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize",type = Integer.class, required = true)
    @Param(name = "questionContent")
    @Param(name = "name")
    @Param(name = "fullName")
    @Permitted
    @JSON("recordCount")
    @JSON("items.(id,questionContent,feedbackContent,feedbackType,like,modifyDate, answerContent")
    @JSON("items.member.(id,name,fullName)")
    public PagedResult<CourseFeedback> findPage(RequestContext context, Subject<Member> subject){
       return questionFeedbackService.findPage(context.getInteger("page"), context.getInteger("pageSize"),
                context.getOptionalString("questionContent"), context.getOptionalString("name"),context.getOptionalString("fullName"));
    }



    @RequestMapping(value = "/{id}",method = RequestMethod.GET)
    @Param(name = "id",  required = true)
    @JSON("id,questionContent,feedbackContent,feedbackType,like,modifyDate,answerContent")
    @JSON("member.(id,name,fullName)")
    public CourseFeedback getOptional(RequestContext context, Subject<Member> subject){
        return questionFeedbackService.getOptional(context.getString("id")).orElse(new CourseFeedback());
    }

}
