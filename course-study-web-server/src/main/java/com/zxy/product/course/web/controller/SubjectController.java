package com.zxy.product.course.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.CachedResult;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.validation.ValidationException;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.CourseRegisterService;
import com.zxy.product.course.api.KnowledgeGraphService;
import com.zxy.product.course.api.certificate.CertificateRecordService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.subject.SubjectService;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.dto.knowledge.graph.SynchronousNodeDTO;
import com.zxy.product.course.entity.AudienceItem;
import com.zxy.product.course.entity.BusinessTopic;
import com.zxy.product.course.entity.CourseAttachment;
import com.zxy.product.course.entity.CourseChapter;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CoursePhoto;
import com.zxy.product.course.entity.CourseShelves;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.StudyReportAnalysisManagers;
import com.zxy.product.course.entity.SubjectAdvertising;
import com.zxy.product.course.entity.SubjectTextArea;
import com.zxy.product.course.entity.SubjectTopicManager;
import com.zxy.product.course.util.StringUtils;
import com.zxy.product.course.web.kit.AudienceKit;
import com.zxy.product.course.web.kit.CourseInfoAdminKit;
import com.zxy.product.course.web.kit.HomeCertifyKit;
import com.zxy.product.system.api.homeconfig.CourseVirtualSpaceService;
import com.zxy.product.system.api.internalswitch.InternalSwitchService;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.api.permission.OrganizationService;
import com.zxy.product.system.entity.CourseVirtualSpace;
import com.zxy.product.system.entity.InternalSwitch;
import com.zxy.product.system.util.DateUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.zxy.product.course.content.MessageTypeContent.GRAPH_ZONE_NODE;

/**
 * 专题信息
 * Created by TJ on 2017/9/6.
 */
@Controller
@RequestMapping("/subject")
public class SubjectController {

    private SubjectService subjectService;
    private CourseInfoAdminKit courseInfoAdminKit;
    private AudienceKit audienceKit;
    private GrantService grantService;
    private OrganizationService organizationService;
    public static final String URI = "course-study/subject-info";
    private CourseCacheService courseCacheService;
    private static final Object LOCK = new Object();

    private CourseInfoService courseInfoService;

    private Cache cache;
    private Cache internalSwitchCache;
    private CertificateRecordService certificateRecordService;


    private CourseVirtualSpaceService courseVirtualSpaceService;

    private com.zxy.product.course.api.OrganizationService courseOrganizationService;

    private  final  static String  INTERNAL_ORGANIZATION_ID = "10000001";
    private  final  static String  KEY = "PublicationRestriction";
    private static final Pattern JS_PROTOCOL_PATTERN = Pattern.compile("^javascript:", Pattern.CASE_INSENSITIVE);

    private InternalSwitchService internalSwitchService;

    private CourseRegisterService courseRegisterService;
    @Resource
    private HomeCertifyKit homeCertifyKit;


    @Autowired
    public void setInternalSwitchService(InternalSwitchService internalSwitchService) {
        this.internalSwitchService = internalSwitchService;
    }

    @Autowired
    public void setCourseOrganizationService(com.zxy.product.course.api.OrganizationService courseOrganizationService) {
        this.courseOrganizationService = courseOrganizationService;
    }

    @Autowired
    public void setCourseVirtualSpaceService(CourseVirtualSpaceService courseVirtualSpaceService) {
        this.courseVirtualSpaceService = courseVirtualSpaceService;
    }

    @Autowired
    public void setSubjectService(SubjectService subjectService) {
        this.subjectService = subjectService;
    }

    @Autowired
    public void setCourseInfoAdminKit(CourseInfoAdminKit courseInfoAdminKit) {
        this.courseInfoAdminKit = courseInfoAdminKit;
    }
    @Autowired
    public void setAudienceKit(AudienceKit audienceKit) {
        this.audienceKit = audienceKit;
    }

    @Autowired
    public void setGrantService(GrantService grantService) {
		this.grantService = grantService;
	}
    @Autowired
	public void setOrganizationService(OrganizationService organizationService) {
		this.organizationService = organizationService;
	}

	@Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }

	@Autowired
    public void setCacheService(CacheService cacheService) {
        this.cache = cacheService.create("course-study-async-task-service", "subject_progress_sub_repair");
        this.internalSwitchCache = cacheService.create("anti-corruption", InternalSwitch.KEY);
    }
    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setCertificateRecordService(CertificateRecordService certificateRecordService) {
        this.certificateRecordService = certificateRecordService;
    }

    private KnowledgeGraphService knowledgeGraphService;

    @Autowired
    public void setKnowledgeGraphService(KnowledgeGraphService knowledgeGraphService){
        this.knowledgeGraphService=knowledgeGraphService;
    }

    private MessageSender messageSender;

    @Autowired
    public void setMessageSender(MessageSender messageSender){
        this.messageSender=messageSender;
    }

    @Autowired
    public void setCourseRegisterService(CourseRegisterService courseRegisterService) {
        this.courseRegisterService = courseRegisterService;
    }

    /**
     * 查询专题列表
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "code")
    @Param(name = "status", type = Integer.class)
    @Param(name = "releaseUserId") // 发布人
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "beginBeginDate", type = Long.class)
    @Param(name = "beginEndDate", type = Long.class)
    @Param(name = "endBeginDate", type = Long.class)
    @Param(name = "endEndDate", type = Long.class)
    @Param(name = "shelveBeginTime", type = Long.class)
    @Param(name = "shelveEndTime", type = Long.class)
    @Param(name = "subjectType", type = Integer.class) // 专题类型，1-普通；2-个性化
    @Param(name = "businessType", type = Integer.class)
    @Param(name = "isParty",type = Integer.class) //是否是党建课程 【0：不是；1：是】
    @Param(name = "selectIds", type = String.class)
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】
    @Param(name = "uri")
    @Param(name = "subjectTypeSelector",type = Integer.class)
    @Permitted
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,status,beginDate,endDate,cover,coverPath,shelveTime,code,publishClient,url, styles,subjectType,returnFlag)")
    public PagedResult<CourseInfo> findPage(RequestContext context, Subject<Member> subject) {
        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));
        List<String> grantOrganizationIds = null;
        if (context.getOptionalInteger("contain").orElse(0) == 0) {
        	grantOrganizationIds = courseInfoAdminKit.findGrantTopOrganizationIds(subject.getCurrentUserId(),context.getOptionalString("organizationId"),context.getOptionalString("uri").orElse("course-study/subject-info"));
		}else {
			grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(),context.getOptionalString("organizationId"),context.getOptionalString("uri").orElse("course-study/subject-info"));
		}
        PagedResult<CourseInfo> result = subjectService.find(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                subject.getCurrentUserId(),
                context.getOptionalString("name"),
                context.getOptionalString("organizationId"),
                context.getOptionalString("code"),
                context.getOptionalInteger("status"),
                context.getOptionalString("releaseUserId"),
                context.getOptionalInteger("publishClient"),
                context.getOptional("subjectType", Integer.class),
                context.getOptional("beginBeginDate", Long.class),
                context.getOptional("beginEndDate", Long.class),
                context.getOptional("endBeginDate", Long.class),
                context.getOptional("endEndDate", Long.class),
                context.getOptional("shelveBeginTime", Long.class),
                context.getOptional("shelveEndTime", Long.class),
                context.getOptionalInteger("isParty"),
                selectIds,
                grantOrganizationIds,
                context.getOptionalInteger("businessType"),
                context.getOptionalInteger("subjectTypeSelector")
        );
        if(result.getRecordCount() > 0){
            List<CourseInfo> items = result.getItems();
            //查询专题中含有退库的数据
            List<CourseInfo> disableCourse = subjectService.getDisableCourse(items.stream().map(CourseInfo::getId).collect(Collectors.toList()));
            //根据专题id分组
            Map<String, List<CourseInfo>> map = disableCourse.stream().collect(Collectors.groupingBy(CourseInfo::getId));
            items.forEach(item->{
                List<CourseInfo> courseInfos = map.get(item.getId());
                Integer flag = CommonConstant.ZERO;
                if(!CollectionUtils.isEmpty(courseInfos)){
                    //节中有任何一个课程为退库，则专题存在退库数据
                    if(courseInfos.stream().anyMatch(r -> (Objects.equals(r.getReturnFlag(), CourseInfo.STATUS_FIVE_SHELVES)) || Objects.equals(r.getReturnFlag(), CourseInfo.STATUS_THE_SHELVES))){
                        flag = CommonConstant.ONE;
                    }
                }
                item.setReturnFlag(flag);
            });
        }
        return  result;
    }

    /**
     * 获取专题详情
     * @param context
     * @return
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("*")
    @JSON("organization.(id,name,depth,level)")
    @JSON("releaseOrg.(id,name)")
    @JSON("sponsoringOrganization.(id,name)")
    @JSON("releaseUser.(id,name,fullName)")
    @JSON("courseAttachments.(*)")
    @JSON("photos.(*)")
    @JSON("advertisings.(*)")
    @JSON("textAreas.(*)")
    @JSON("courseAbilities.(*)")
    @JSON("subjectTopicManager.*.*")
    public CourseInfo get(RequestContext context) {
        return subjectService.get(context.get("id", String.class));
    }


    /**
     * 专题新增
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.POST)
    @Permitted
    @Param(name = "name", required = true)
    @Param(name = "beginDate", type = Long.class)
    @Param(name = "endDate", type = Long.class)
    @Param(name = "description")
    @Param(name = "descriptionText")
    @Param(name = "releaseMemberId")
    @Param(name = "releaseOrgId")
    @Param(name = "organizationId", required = true)
    @Param(name = "code")
    @Param(name = "courseChapters")
    @Param(name = "courseAttachments")
    @Param(name = "audienceItems")
    @Param(name = "shelves")
    @Param(name = "studyDays", type = Integer.class) // 建议学习天数
    @Param(name = "publishClient", type = Integer.class, required = true) // 发布终端，0:
    @Param(name = "publishType", type = Integer.class) // 发布类型 0-定向(测试) 1-正式
    @Param(name = "cover") // 头像
    @Param(name = "coverPath") //头像路径
    @Param(name = "coverMaterialId") // 封面素材id
    @Param(name = "appBanner")
    @Param(name = "pcBanner")
    @Param(name = "pcBannerPath")
    @Param(name = "appBannerPath")
    @Param(name = "businessType")
    @Param(name = "styles") // 风格配置
    @Param(name = "topicIds") // 相关话题
    @Param(name = "advertisings") // 专题广告
    @Param(name = "photos") // 专题相册
    @Param(name = "textAreas") // 专题文字区域
    @Param(name = "url") // 个性化专题链接地址
    @Param(name = "type")//用于区分个性专题，普通专题，正向发布，定向发布
    @Param(name = "shareSub")//2018-01-30  专题新增是否分享下级使用
    @Param(name = "addType", type = Integer.class) // 添加类别 普通模式,章节模式
    @Param(name = "isPublic", type = Integer.class) // 非公开专题
    @Param(name = "descriptionApp", type = String.class) // 简介--app加粗换行样式 updated by wangdongyan
    @Param(name = "certificateId", type = String.class) // 专题配置的证书id
    @Param(name = "isSign", type = Integer.class) // 专题是否需要报名（新动能重塑培训新增功能）
    @Param(name = "managerItems") // 专题管理员
    @Param(name = "explicitLearningStatus",type = Integer.class)
    @Param(name = "learningSituation", defaultValue = "0", type = Integer.class) // 开启学情分析报告(0: 关、1:开)
    @Param(name = "studyReportAnalysisManagers") //
    @Param(name = "sponsoringOrg") // 主办部门
    @Param(name = "learnSequence",type = Integer.class)
    @Param(name = "certificateType",type = Integer.class)
    @Param(name = "certificateTime",type = Long.class)
    @Param(name = "subjectType",type = Integer.class)
    @Param(name = "subjectMisCode")
    @Param(name = "skinType", type = Integer.class)
    @JSON("id, name,description, scromType,createTime,supportApp,source,status,portalNum")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.INSERT, fisrtAction = "新增普通专题", desc = "新增普通专题《{0}》", params = {"name"}, businessType = "type", businessValue = "1")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.INSERT, fisrtAction = "新增个性专题", desc = "新增个性专题《{0}》", params = {"name"}, businessType = "type", businessValue = "2")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "正式发布", desc = "新增并正式发布普通专题《{0}》", params = {"name"}, businessType = "type", businessValue = "3")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "定向发布", desc = "新增并定向发布普通专题《{0}》", params = {"name"}, businessType = "type", businessValue = "4")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "正式发布", desc = "新增并正式发布个性专题《{0}》", params = {"name"}, businessType = "type", businessValue = "5")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "定向发布", desc = "新增并定向发布个性专题《{0}》", params = {"name"}, businessType = "type", businessValue = "6")
    public CourseInfo add(RequestContext context, Subject<Member> subject) {
        Optional<String> url = context.getOptionalString("url");
        url.ifPresent(urlStr -> {
            if(!isValidUrl(urlStr)){
                throw new UnprocessableException(ErrorCode.SubjectNotExists);
            }
        });
        Optional<CourseShelves> courseShelves = parseShelves(context.getOptionalString("shelves"));
        if (courseShelves.isPresent()) {
            issueJudgment(subject, context.getOptionalString("audienceItems"));
        }
        List<CourseChapter> courseChapters = courseInfoAdminKit.parseCourseChapters(context.getOptionalString("courseChapters"));
        // 判断章节中章节类型为专题的章节是否引用了课程
        Optional<Integer> businessType = context.getOptionalInteger("businessType");
        if (!ObjectUtils.isEmpty(courseChapters) && businessType.isPresent() && !Objects.equals(businessType.get(), CourseInfo.BUSINESS_TYPE_STUDY_MAP)) {
            courseInfoAdminKit.validationSubjectChapter(courseChapters,Optional.ofNullable(null));
        }
        List<CourseAttachment> courseAttachments = courseInfoAdminKit.parseCourseAttachment(context.getOptionalString("courseAttachments"));
        List<AudienceItem> audienceItems = audienceKit.parseAudienceItems(context.getOptionalString("audienceItems"));
        List<SubjectTopicManager> managerItems = audienceKit.parseManagerItems(context.getOptionalString("managerItems"));
        if (courseShelves.isPresent()) {
            validationSubject2(audienceItems, courseChapters, courseAttachments, context.getOptionalString("url"), context.getOptionalString("styles"), context.getOptionalString("certificateId"), context.getOptionalInteger("publishType"), context.getOptionalInteger("type"), businessType);
        }
        List<SubjectAdvertising> advertisingList = parseAdvertising(context.getOptionalString("advertisings"));
        List<CoursePhoto> photos = parsePhotos(context.getOptionalString("photos"));
        List<SubjectTextArea> textAreas = parseTextArea(context.getOptionalString("textAreas"));

        // 【CMU-学情分析】
        List<StudyReportAnalysisManagers> studyReportAnalysisManagers = parseStudyReportAnalysisManagers(context.getOptionalString("studyReportAnalysisManagers"));

        CourseInfo courseInfo = subjectService.add(
                context.get("name", String.class),
                subject.getCurrentUserId(),
                context.getInteger("publishClient"),
                context.getString("organizationId"),
                context.getOptionalInteger("publishType"),
                context.getOptional("beginDate", Long.class),
                context.getOptional("endDate", Long.class),
                context.getOptionalString("description"),
                context.getOptionalString("descriptionText"),
                context.getOptionalString("releaseMemberId"),
                context.getOptionalString("releaseOrgId"),
                context.getOptionalString("code"),
                context.getOptionalInteger("studyDays"),
                context.getOptionalString("cover"),
                context.getOptionalString("coverPath"),
                context.getOptionalString("coverMaterialId"),
                courseChapters,
                courseAttachments,
                audienceItems,
                courseShelves,
                context.getOptionalString("styles"),
                context.getOptionalString("topicIds"),
                advertisingList,
                photos,
                textAreas,
                context.getOptionalString("url"),
                context.getOptionalInteger("shareSub"),
                context.getOptionalInteger("addType"),
                context.getOptionalInteger("isPublic"),
                context.getOptionalString("descriptionApp"),
                context.getOptionalString("certificateId"),
                context.getOptionalInteger("isSign"),
                managerItems,
                context.getOptionalInteger("explicitLearningStatus"),
                businessType,
                context.getOptionalInteger("learnSequence"),
                context.getOptionalInteger("learningSituation"),
                studyReportAnalysisManagers,
                context.getOptionalString("sponsoringOrg"),
                context.getOptionalString("subjectMisCode"),
                context.getOptionalInteger("subjectType"),
                context.getOptionalLong("certificateTime"),
                context.getOptionalInteger("certificateType"),
                context.getOptionalInteger("skinType"),
                context.getOptionalString("pcBanner"),
                context.getOptionalString("pcBannerPath")
        );

        if (courseShelves.isPresent()) {
            senderCourse(courseChapters);
        }
        courseShelves.flatMap(s -> context.getOptionalInteger("publishType")).ifPresent(type -> {
            if (type == CourseInfo.PUBLISH_TYPE_FORMAL) {
                // 是否为首页配置PC端外部认证专题,是的话重新生成首页信息
                homeCertifyKit.certifySubjectHomeReset(courseInfo.getId());
            }
        });
        return courseInfo;
    }

    /**
     * 发布判断,是否是中国移动或者内部组织,开关是否开启,是否再时间内
     * @param subject
     * @param audienceItems
     */
    private void issueJudgment(Subject<Member> subject, Optional<String> audienceItems) {
        //判断内部开关是否开启
        InternalSwitch internalSwitch = internalSwitchCache.get(InternalSwitch.KEY + KEY, () -> internalSwitchService.findByType(InternalSwitch.PUBLICATION_RESTRICTION), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
        if (!ObjectUtils.isEmpty(internalSwitch) && Objects.equals(internalSwitch.getStatus(), InternalSwitch.switchStatusNO)) {
            //白天不允许发布/时间使用逗号隔开,7,18
            List<String> date = Arrays.asList(internalSwitch.getDescribe().split(","));
            //判断是否是再限制的时间内
            publicationRestriction(
                    audienceItems,
                    subject.getRootOrganizationId(),
                    Integer.parseInt(date.get(1)),
                    Integer.parseInt(date.get(2))
            );
        }
    }


    /**
     * 专题修改
     * @param context
     * @return
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "name", required = true) // 专题名称
    @Param(name = "publishClient", type = Integer.class, required = true) // 发布终端，0:全部, 1: PC, 2: APP
    @Param(name = "publishType", type = Integer.class) // 发布类型 0-定向(测试) 1-正式
    @Param(name = "beginDate", type = Long.class) // 开始时间
    @Param(name = "endDate", type = Long.class) // 结束时间
    @Param(name = "description") // 专题简介
    @Param(name = "descriptionText") // 专题简介-纯文本格式
    @Param(name = "releaseMemberId") // 发布人
    @Param(name = "releaseOrgId") // 发布部门
    @Param(name = "organizationId") // 所属部门
    @Param(name = "code") // 专题编码
    @Param(name = "courseChapters") // 专题章节
    @Param(name = "courseAttachments") // 专题附件
    @Param(name = "audienceItems") // 专题受众对象
    @Param(name = "shelves") // 上架(发布)信息
    @Param(name = "studyDays", type = Integer.class) // 建议学习天数
    @Param(name = "cover") // 头像
    @Param(name = "coverPath") // 头像
    @Param(name = "coverMaterialId") // 封面素材id
    @Param(name = "appBanner")
    @Param(name = "pcBanner")
    @Param(name = "pcBannerPath")
    @Param(name = "appBannerPath")
    @Param(name = "styles") // 风格配置
    @Param(name = "topicIds") // 相关话题
    @Param(name = "advertisings") // 专题广告
    @Param(name = "photos") // 专题相册
    @Param(name = "textAreas") // 专题文字区域
    @Param(name = "url") // 个性化专题链接地址
    @Param(name = "status", type = Integer.class)
    @Param(name = "type")//用于区分审计功能
    @Param(name = "businessType")
    @Param(name = "shareSub")//2018-01-30  专题新增是否分享下级使用
    @Param(name = "addType", type = Integer.class) // 添加类别 普通模式,章节模式
    @Param(name = "isPublic", type = Integer.class) // 非公开专题
    @Param(name = "descriptionApp", type = String.class) // 简介--app加粗换行样式 updated by wangdongyan
    @Param(name = "certificateId", type = String.class) // 专题配置的证书id
    @Param(name = "isSign", type = Integer.class) // 专题是否需要报名（新动能重塑培训新增功能）
    @Param(name = "managerItems") // 专题管理员
    @Param(name = "explicitLearningStatus",type = Integer.class)
    @Param(name = "learningSituation", defaultValue = "0", type = Integer.class) // 开启学情分析报告(0: 关、1:开)
    @Param(name = "studyReportAnalysisManagers") //
    @Param(name = "sponsoringOrg") // 主办部门
    @Param(name = "learnSequence",type = Integer.class)
    @Param(name = "subjectType",type = Integer.class)
    @Param(name = "subjectMisCode")
    @Param(name = "certificateType",type = Integer.class)
    @Param(name = "certificateTime",type = Long.class)
    @Param(name = "skinType",type = Integer.class)
    @JSON("id")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.UPDATE, fisrtAction = "修改", desc = "修改专题《{0}》", params = {"name"}, businessType = "type", businessValue = "1")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.UPDATE, fisrtAction = "修改", desc = "修改专题《{0}》", params = {"name"}, businessType = "type", businessValue = "2")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "正式发布", desc = "修改并正式发布普通专题《{0}》", params = {"name"}, businessType = "type", businessValue = "3")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "定向发布", desc = "修改并定向发布普通专题《{0}》", params = {"name"}, businessType = "type", businessValue = "4")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "正式发布", desc = "修改并正式发布个性专题《{0}》", params = {"name"}, businessType = "type", businessValue = "5")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "定向发布", desc = "修改并定向发布个性专题《{0}》", params = {"name"}, businessType = "type", businessValue = "6")
    public CourseInfo update(RequestContext context, Subject<Member> memberSubject) {
        /*Map<String, String> tagMap = this.queryExistRelation(context.getString("id"));*/
        Optional<String> url = context.getOptionalString("url");
        url.ifPresent(urlStr -> {
            if(!isValidUrl(urlStr)){
                throw new UnprocessableException(ErrorCode.SubjectNotExists);
            }
        });
        boolean isPublish = context.getOptionalInteger("status").map(s -> s == CourseInfo.STATUS_SHELVES || s == CourseInfo.STATUS_THE_TEST || s == CourseInfo.STATUS_THE_SHELVES).orElse(false);
        Optional<CourseShelves> courseShelves = parseShelves(context.getOptionalString("shelves"));
        if (courseShelves.isPresent() || isPublish) {
            issueJudgment(memberSubject, context.getOptionalString("audienceItems"));
        }

        Integer subjectStatus = courseCacheService.getSubjectPublishStatus(context.get("id", String.class));
        if (null != subjectStatus && subjectStatus.equals(CourseInfo.STATUS_IN_SHELVES)) {
            throw new UnprocessableException(ErrorCode.CourseStatusIsAnnouncing);
        }
        if (!context.getOptionalString("certificateId").isPresent()) {
            // 查询专题是否发放证书，如果已发放则不能删除证书
            Optional<String> certificateRecordId = certificateRecordService.findWhetherIssueCertificateByBusniessId(context.get("id", String.class));
            if (certificateRecordId.isPresent()) {
                throw new UnprocessableException(ErrorCode.SubjectCertificateHasBeenIssuedCannotBeDeleted);
            }
        }
        List<CourseChapter> courseChapters = courseInfoAdminKit.parseCourseChapters(context.getOptional("courseChapters", String.class));
        // 判断章节中章节类型为专题的章节是否引用了课程
        Optional<Integer> businessType = context.getOptionalInteger("businessType");
        if (businessType.isPresent() && Objects.equals(businessType.get(), CourseInfo.BUSINESS_TYPE_STUDY_MAP) && courseShelves.isPresent()){
            ErrorCode.CanNotFindStagesElements.throwIf(ObjectUtils.isEmpty(courseChapters));
        }

        if (!ObjectUtils.isEmpty(courseChapters) && businessType.isPresent() && !Objects.equals(businessType.get(), CourseInfo.BUSINESS_TYPE_STUDY_MAP)) {
            courseInfoAdminKit.validationSubjectChapter(courseChapters,Optional.of(context.get("id", String.class)));
        }
        List<CourseAttachment> courseAttachments = courseInfoAdminKit.parseCourseAttachment(context.getOptional("courseAttachments", String.class));
        List<AudienceItem> audienceItems = audienceKit.parseAudienceItems(context.getOptionalString("audienceItems"));
        List<SubjectTopicManager> managerItems = audienceKit.parseManagerItems(context.getOptionalString("managerItems"));
        // 发布时||已发布状态下点击保存，加入校验
        if (courseShelves.isPresent() || isPublish) {
            validationSubject2(audienceItems, courseChapters, courseAttachments, context.getOptionalString("url"), context.getOptionalString("styles"), context.getOptionalString("certificateId"), context.getOptionalInteger("publishType"),context.getOptionalInteger("type"),
                businessType);
        }
        List<SubjectAdvertising> advertisingList = parseAdvertising(context.getOptionalString("advertisings"));
        List<CoursePhoto> photos = parsePhotos(context.getOptionalString("photos"));
        List<SubjectTextArea> textAreas = parseTextArea(context.getOptionalString("textAreas"));

        // 【CMU-学情分析】
        List<StudyReportAnalysisManagers> studyReportAnalysisManagers = parseStudyReportAnalysisManagers(context.getOptionalString("studyReportAnalysisManagers")).stream()
                .peek(manager -> manager.setCourseId(context.get("id", String.class)))
                .collect(Collectors.toList());


        CourseInfo courseInfo = subjectService.update(
                context.get("id", String.class),
                memberSubject.getCurrentUserId(),
                context.getInteger("publishClient"),
                context.getOptionalInteger("publishType"),
                context.getOptional("name", String.class),
                context.getOptional("beginDate", Long.class),
                context.getOptional("endDate", Long.class),
                context.getOptional("description", String.class),
                context.getOptional("descriptionText", String.class),
                context.getOptional("releaseMemberId", String.class),
                context.getOptional("releaseOrgId", String.class),
                context.getOptional("organizationId", String.class),
                context.getOptional("code", String.class),
                context.getOptional("learnSequence", Integer.class),
                context.getOptional("studyDays", Integer.class),
                context.getOptionalString("cover"),
                context.getOptionalString("coverPath"),
                context.getOptionalString("coverMaterialId"),
                context.getOptionalInteger("shareSub"),
                courseChapters,
                courseAttachments,
                audienceItems,
                courseShelves,
                context.getOptionalString("styles"),
                context.getOptionalString("topicIds"),
                advertisingList,
                photos,
                textAreas,
                context.getOptionalString("url"),
                context.getOptionalInteger("addType"),
                context.getOptionalInteger("isPublic"),
                context.getOptionalString("descriptionApp"),
                context.getOptionalString("certificateId"),
                context.getOptionalInteger("isSign"),
                managerItems,
                context.getOptionalInteger("explicitLearningStatus"),
                context.getOptionalInteger("learningSituation"),
                studyReportAnalysisManagers,
                context.getOptionalString("sponsoringOrg"),
                context.getOptionalString("subjectMisCode"),
                context.getOptionalInteger("subjectType"),
                context.getOptionalLong("certificateTime"),
                context.getOptionalInteger("certificateType"),
                context.getOptionalInteger("skinType"),
                context.getOptionalString("pcBanner"),
                context.getOptionalString("pcBannerPath")
        );
        /*if(!Objects.isNull(tagMap)){
            this.sendMqSynchronousGraph(tagMap,context.getString("id"));
        }*/
        if (courseShelves.isPresent() || isPublish) {
            senderCourse(courseChapters);
        }

        //专题的修改埋点-同步ihr
        boolean isEdit = context.getOptionalInteger("type").map(s -> s == 1 || s == 2).orElse(false);
        if (isEdit){
            //专题培训班埋点-修改,关联的培训班数据同步
            messageSender.send(com.zxy.product.course.content.MessageTypeContent.IHR_TOPIC_TRAIN_SYNC,
                    MessageHeaderContent.OPERATION_STATE,CourseInfo.OPERATION_STATE_MODIFY,
                    MessageHeaderContent.TOPIC_ID, context.get("id", String.class));
        }
        return courseInfo;
    }

    /**
     * 校验输入的字符串是否为有效的URL
     */
    private  boolean isValidUrl(String url) {
        if (url == null) {
            return false;
        }
        // 检查是否包含javascript伪协议
        if (JS_PROTOCOL_PATTERN.matcher(url.trim()).lookingAt()) {
            return false;
        }
        return true;
    }

    private void senderCourse(List<CourseChapter> courseChapters) {
        if (!CollectionUtils.isEmpty(courseChapters)) {
            List<String> courseIds = courseChapters.stream()
                    .filter(x -> x.getCourseChapterSections() != null) // 检查是否为 null
                    .flatMap(x -> x.getCourseChapterSections().stream())
                    .filter(s -> null != s.getSectionType() && CourseChapterSection.SECTION_TYPE_COURSE == s.getSectionType())
                    .map(CourseChapterSection::getResourceId)
                    .filter(org.springframework.util.StringUtils::hasText)
                    .collect(Collectors.toList());
            //发送搜索监听,更新课程的引用专题
            if (!CollectionUtils.isEmpty(courseIds)) {
                courseIds.forEach(r -> messageSender.send(MessageTypeContent.COURSE_INFO_UPDATE, MessageHeaderContent.ID, r));
            }
        }
    }

    /**
     * 查询已存在的专题图谱关系Map
     *
     * @param subjectId 专题id
     * @return 满足图谱专题更新信息Map
     */
    private Map<String,String> queryExistRelation(String subjectId){
        List<String> subjectIdCollect = subjectService.checkSubjectSynchronous(subjectId);
        return Optional.ofNullable(subjectIdCollect)
                .filter(com.alibaba.dubbo.common.utils.CollectionUtils::isNotEmpty)
                .map(ew1 -> knowledgeGraphService.getGraphNodeTag(ew1.get(0)))
                .orElse(null);
    }

    /**
     * 查询已存在的专题图谱关系Map
     *
     * @param relationMap 专题图谱更新信息Map
     * @param subjectId 专题Id
     */
    private void sendMqSynchronousGraph(Map<String,String> relationMap,String subjectId){
        SynchronousNodeDTO synchronousNodeDTO = new SynchronousNodeDTO();
        synchronousNodeDTO.setNodeId(subjectId);
        synchronousNodeDTO.setNodeType("subject");
        synchronousNodeDTO.setGraphRelationCollectJson(relationMap.get("existRelationCollect"));
        messageSender.send(
                GRAPH_ZONE_NODE,
                MessageHeaderContent.CONTENT, JSONObject.toJSONString(synchronousNodeDTO)
        );
    }

    /**
     * 是否是全员,是否再限制时间内
     * @param optionalAudienceItems
     * @param rootOrgId
     * @param minDate
     * @param maxDate
     */
    private  void  publicationRestriction(Optional<String> optionalAudienceItems, String rootOrgId, int minDate, int maxDate){
        List<AudienceItem> audienceItems;
            audienceItems = audienceKit.parseAudienceItems(optionalAudienceItems);
            for (AudienceItem audienceItem : audienceItems) {
                if (audienceItem.getJoinId().equals(rootOrgId) || audienceItem.getJoinId().equals(INTERNAL_ORGANIZATION_ID)){
                    int hours = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
                    if (hours >= minDate && hours <= maxDate ){
                        throw new UnprocessableException(ErrorCode.PublicationRestriction);
                    }
                }
            }
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "subjectName", type = String.class)
    @JSON("*")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.DELETE, fisrtAction = "删除", desc = "删除专题《{0}》", params = {"subjectName"})
    public int delete(RequestContext context,Subject<Member> memberSubject) {
        return subjectService.delete(context.get("id", String.class),memberSubject.getCurrentUserId());
    }


    /**
     * 根据ids查询专题基本信息
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/basic-by-ids", method = RequestMethod.GET)
    @Param(name = "ids", type = String.class, required =true)
    @JSON("*")
    @JSON("organization.(id,name)")
    public List<CourseInfo> findBasicByIds(RequestContext requestContext) {
        return subjectService.findBasicByIds(Arrays.asList(requestContext.getString("ids").split(",")));
    }

    @RequestMapping(value = "/hotTopicIds", method = RequestMethod.GET)
    @JSON("id")
    public List<Map<String,String>> hotTopicIds() {
        List<Map<String,String>> hotTopicIdCollect = cache.get(BusinessTopic.SUBJECT_HOT_TOPIC_IDS_CACHE_KEY, List.class);
        if(hotTopicIdCollect==null){
            synchronized (LOCK){
                hotTopicIdCollect=cache.get(BusinessTopic.SUBJECT_HOT_TOPIC_IDS_CACHE_KEY, List.class);
                if(hotTopicIdCollect==null){
                    hotTopicIdCollect=subjectService.hotTopicIds()
                            .stream().map(x -> ImmutableMap.of("id", x)).collect(Collectors.toList());
                    if(hotTopicIdCollect==null){
                        hotTopicIdCollect=Lists.newArrayList();
                    }
                    cache.set(BusinessTopic.SUBJECT_HOT_TOPIC_IDS_CACHE_KEY,hotTopicIdCollect,DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
                }
            }
        }
        return hotTopicIdCollect;
    }

    /**
     * 专题选择器
     * @param context
     * @param memberSubject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/select")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "subjectType", type = Integer.class) // 专题类型，1-普通；2-个性化
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "selectIds")
    @Param(name = "uri")
    @Param(name = "shelveBeginTime", type = String.class)
    @Param(name = "shelveEndTime", type = String.class)
    @Param(name = "courseStatus")//课程状态
    @Param(name = "isParty",type = Integer.class) //是否是党建课程 【0：不是；1：是】
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,status,beginDate,endDate,cover,coverPath,shelveTime,code,publishClient,url, styles)")
    @JSON("items.organization.(id,name)")
    public PagedResult<CourseInfo> findSelect(RequestContext context, Subject<Member> memberSubject) {
        Optional<List<Integer>> courseStatus = context.getOptionalString("courseStatus").map(s-> Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));
        Optional<String> organizationId = context.getOptionalString("organizationId");
        String uri = context.getOptionalString("uri").orElse(CourseInfo.URI);
        List<String> grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(memberSubject.getCurrentUserId(),organizationId, uri);

//        String rootId = organizationService.findMaxGrantOriganization(memberSubject.getCurrentUserId(), uri).getId();
        Optional<List<String>> parentIds = Optional.empty();
        if (!organizationId.isPresent()) {
        	parentIds = Optional.of(grantService.findGrantedOrganizationWithParent(memberSubject.getCurrentUserId(), uri, null).stream().map(a -> a.getId()).collect(Collectors.toList()));

        }
//            List<String> parentOrganizationIds = grantService.findGrantedOrganizationWithParent(memberSubject.getCurrentUserId(), uri, organizationId.orElse(rootId)).stream().map(a -> a.getId()).collect(Collectors.toList());
//            parentIds = Optional.of(parentOrganizationIds.stream()
//                    .filter(x->!grantOrganizationIds.contains(x)).collect(Collectors.toList()));
//        }

//        List<String> parentOrganizationIds = grantService.findGrantedOrganizationWithParent(memberSubject.getCurrentUserId(), uri, organizationId.orElse(rootId)).stream().map(a -> a.getId()).collect(Collectors.toList());
//        parentOrganizationIds = parentOrganizationIds.stream()
        return subjectService.findSelect(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptionalInteger("publishClient"),
                context.getOptionalString("name"),
                context.getOptionalInteger("subjectType"),
                context.getOptionalInteger("isParty"),
                grantOrganizationIds,
                parentIds,
                selectIds,
                StringUtils.dateString2OptionalLong(context.getOptional("shelveBeginTime",String.class)),
                StringUtils.dateString2OptionalLongMore(context.getOptional("shelveEndTime", String.class)),
                courseStatus
                );
    }


    /**
     * 专题选择器（为专题套专题准备不涉及权限）
     * @param context
     * @param memberSubject
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/selector")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "subjectType", type = Integer.class) // 专题类型，1-普通；2-个性化
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "selectIds")
    @Param(name = "shelveBeginTime", type = String.class)
    @Param(name = "shelveEndTime", type = String.class)
    @Param(name = "courseStatus")//课程状态
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,status,beginDate,endDate,cover,coverPath,shelveTime,code,publishClient,url, styles)")
    @JSON("items.organization.(id,name)")
    public PagedResult<CourseInfo> findSelectorForSubject(RequestContext context, Subject<Member> memberSubject) {
        Optional<List<Integer>> courseStatus = context.getOptionalString("courseStatus").map(s-> Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));
        return subjectService.findSelectorSubject(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptionalInteger("publishClient"),
                context.getOptionalString("name"),
                context.getOptionalInteger("subjectType"),
                selectIds,
                StringUtils.dateString2OptionalLong(context.getOptional("shelveBeginTime",String.class)),
                StringUtils.dateString2OptionalLongMore(context.getOptional("shelveEndTime", String.class)),
                context.getOptionalString("organizationId"),
                courseStatus);
    }



    /**
     * 查询已经被引用用的专题id
     * @param context
     * @param memberSubject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/find-referenced-subjectIds")
    @Param(name = "subjectId", type = String.class)
    @JSON("*")
    public List<String> findReferencedSubjectIds(RequestContext context, Subject<Member> memberSubject) {
        Optional<String> subjectId = context.getOptionalString("subjectId");
        return subjectService.findReferencedSubjectIds(context.getOptionalString("subjectId"));
    }


    /**
     * 查询专题的封面等信息
     * @param context
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/front/find-by-ids")
    @Param(name = "ids", required = true)
    @JSON("*.*")
    @CachedResult(params = {"ids"}, key = "subject-front-find-by-ids", expired = 60 * 5)
    public List<CourseInfo> findBySubjectIds(RequestContext context) {
        return subjectService.findBySubjectIds(Arrays.asList(context.getString("ids").split(",")));
    }

    /**
     * 查询该专题是否被引用
     * @param context
     * @param memberSubject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/find-is-referenced/{id}")
    @Param(name = "id", required = true)
    @JSON("*.*")
    public Map<String, String> findSubjectIsReferenced(RequestContext context, Subject<Member> memberSubject) {
        return ImmutableMap.of("subjectId", Optional.ofNullable(subjectService.findSubjectIsReferenced(context.getString("id"))).orElse(""));
    }

    /**
     * 修复subject-sub-new设置redis缓存，与course-study-async-task-service中的SubjectProgressSubUpdateRepair配套使用
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = "/set-subject-sub-repair-redis")
    @Param(name = "tableName", required = true)
    @Param(name = "value", required = true)
    @JSON("*.*")
    public Integer setSubjectRepairValue(RequestContext context, Subject<Member> subject) {
    	cache.set(context.getString("tableName"), context.getString("value"));
        return 1;
    }

    /**
     * 【专题定制化统一入口】首页banner-查询用户对应权限的专题
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = "/find-member-role")
    @Param(name = "courseIds", required = true)
    @JSON("*.*")
    public Map<String, String> findMemberRole(RequestContext context, Subject<Member> subject){
        return ImmutableMap.of("id",
                subjectService.findMemberRole(Arrays.asList(context.getString("courseIds").split(",")),
                subject.getCurrentUserId()));

    }

    /***
     * 转换上架(发布)信息
     *
     * @param shelves
     * @return
     */
    private Optional<CourseShelves> parseShelves(Optional<String> shelves) {
        Optional<CourseShelves> courseShelves;
        try {
            courseShelves = shelves.map(s -> {
                CourseShelves cs = com.alibaba.fastjson.JSON.parseObject(s, CourseShelves.class);
                return cs;
            });
        } catch (Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        courseShelves.ifPresent(cs -> {
            int noticeUser = cs.getNoticeUser() != null ? cs.getNoticeUser().intValue() : CourseShelves.NOTICE_NO;
            if (noticeUser == CourseShelves.NOTICE_YES
                    && com.alibaba.dubbo.common.utils.StringUtils.isEmpty(cs.getNoticeUserContent())) {
                throw new ValidationException(ErrorCode.NoticeTextNotNull);
            }
            cs.setRule(cs.getRule() != null ? cs.getRule() : CourseShelves.RULE_NOT_AFFECT);
        });
        return courseShelves;
    }

    private List<StudyReportAnalysisManagers> parseStudyReportAnalysisManagers(Optional<String> managersJsonOptional) {
        return managersJsonOptional
                .map(managersJsonStr -> {
                    try {
                        return com.alibaba.fastjson.JSON.parseArray(managersJsonStr, StudyReportAnalysisManagers.class);
                    } catch (com.alibaba.fastjson.JSONException e) {
                        return new ArrayList<StudyReportAnalysisManagers>();
                    }
                })
                .orElse(new ArrayList<>());
    }

    /***
     * 转换相册照片
     *
     * @param photos
     * @return
     */
    private List<CoursePhoto> parsePhotos(Optional<String> photos) {
        List<CoursePhoto> photoList = Lists.newArrayList();
        try {
            if (photos.isPresent()) {
                photoList = com.alibaba.fastjson.JSON.parseArray(photos.get(), CoursePhoto.class);
            }
        } catch (Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        return photoList;
    }

    /**
     * 校验专题信息是否完整
     *
     * @return
     */
    private void validationSubject(List<AudienceItem> audienceItems, List<CourseChapter> courseChapters, List<CourseAttachment> courseAttachments, Optional<String> subjectUrl, Optional<String> styles) {
        if (audienceItems == null || audienceItems.isEmpty()) {
            throw new ValidationException(ErrorCode.AudienceItemsNotNull);
        }

        if (!subjectUrl.isPresent()) {
            courseInfoAdminKit.validationChapter(courseChapters, CourseInfo.BUSINESS_TYPE_SUBJECT);
            if (!styles.isPresent()) {
                throw new ValidationException(ErrorCode.SubjectStyleNotNull);
            }
        }
        if(courseAttachments != null) {
            courseInfoAdminKit.validationAttachments(courseAttachments);
        }
    }

    /**
     * 转换专题广告
     *
     * @param advertisings
     * @return
     */
    private List<SubjectAdvertising> parseAdvertising(Optional<String> advertisings) {
        List<SubjectAdvertising> advertisingList = Lists.newArrayList();
        try {
            if (advertisings.isPresent()) {
                advertisingList = com.alibaba.fastjson.JSON.parseArray(advertisings.get(), SubjectAdvertising.class);
            }
        } catch (Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        return advertisingList;
    }

    /**
     * 转换专题文字区域
     *
     * @param textAreas
     * @return
     */
    private List<SubjectTextArea> parseTextArea(Optional<String> textAreas) {
        List<SubjectTextArea> textAreaList = Lists.newArrayList();
        try {
            if (textAreas.isPresent()) {
                textAreaList = com.alibaba.fastjson.JSON.parseArray(textAreas.get(), SubjectTextArea.class);
            }
        } catch (Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        return textAreaList;
    }

    /**
     * 校验专题信息是否完整，专题添加证书后验证规则改变，配置证书时必须有一个必修章节，其他没有配置证书时不需要验证
     *
     * @return
     */
    private void validationSubject2(List<AudienceItem> audienceItems, List<CourseChapter> courseChapters, List<CourseAttachment> courseAttachments, Optional<String> subjectUrl,
                                    Optional<String> styles, Optional<String> certificateId, Optional<Integer> publishType, Optional<Integer> type,
                                    Optional<Integer> businessType) {
        if (audienceItems == null || audienceItems.isEmpty()) {
            throw new ValidationException(ErrorCode.AudienceItemsNotNull);
        }

        if (!businessType.isPresent()) {
            // 如果配置证书则需验证是否有一个必修章节（与课程验证一样）
            if (!subjectUrl.isPresent()) {
                if (certificateId.isPresent()) {
                    courseInfoAdminKit.validationChapter(courseChapters, CourseInfo.BUSINESS_TYPE_COURSE);
                } else {
                    courseInfoAdminKit.validationChapter(courseChapters, CourseInfo.BUSINESS_TYPE_SUBJECT);
                }
                if (!styles.isPresent()) {
                    throw new ValidationException(ErrorCode.SubjectStyleNotNull);
                }
            }
        }

        if(courseAttachments != null) {
            courseInfoAdminKit.validationAttachments(courseAttachments);
        }

        /**
         * 1.判断上架类型为正式发布，
         * 2.判断专题内课程状态，
         * 3.若有不是已发布状态课程则抛异常
         */
        publishType.ifPresent(t -> {
            if (t == CourseInfo.PUBLISH_TYPE_FORMAL) {
                Integer l = type.orElse(0);
                if (l.equals(3)||l.equals(5))
                validateCourses(courseChapters);
            }
        });

    }
    /**
     * 验证课程中是否包含未发布的课程活资源
     * @param courseChapters
     */
    private void validateCourses(List<CourseChapter> courseChapters) {
        List<String> sectionIds = null;
        if (!CollectionUtils.isEmpty(courseChapters)) {
            sectionIds = courseChapters.stream().filter(chapter -> !CollectionUtils.isEmpty(chapter.getCourseChapterSections()))
                    .flatMap(chapter -> chapter.getCourseChapterSections().stream())
                    .map(CourseChapterSection::getResourceId)
                    .collect(Collectors.toList());
            //未发布课程的课程数量
            Long num = courseInfoService.getChapterSectionStatus(Optional.ofNullable(sectionIds));
            if (Objects.nonNull(num) && num != 0) {
                throw new ValidationException(ErrorCode.CourseInfoNotInShelvesOrIsNull);
            }
        }
    }



    /**
     * 查询专题列表
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/find-subject-virtual-space", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "virtualSpacesOrganizationId" ,required = true) //空间归属部门id
    @Param(name = "virtualSpacesId" ,required = true) //空间id
    @Param(name = "businessType",type=Integer.class ,required = true)//专题类型,0=课程,1=考试,2=专题
    @Param(name = "name")
    @Param(name = "organizationId")    //归属部门,条件
    @Param(name = "status", type = Integer.class)
    @Param(name = "shelveBeginTime")
    @Param(name = "shelveEndTime")
    @Param(name = "uri")
    @Param(name = "virtualSpacesStatus" ,type = Integer.class) //空资源状态
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】
    @Param(name = "subjectType") //专题类型 1=普通,2=个性化
    @Param(name = "ids") //精选内容过滤
    @JSON("recordCount")
    @JSON("items.(id,name,code,status,shelveTime,publishClient,url,styles,courseVirtualSpacesStatus,cover,coverPath)")
    @JSON("items.organization.(id,name)")
    public PagedResult<CourseInfo> findSubjectVirtualSpace(RequestContext context ,Subject<Member> subject) {
       String currentUserId = subject.getCurrentUserId();

        String virtualSpacesOrganizationId = context.getString("virtualSpacesOrganizationId");
        List<String> grantOrganizationIds = null;
        List<String> virtualSpacesGrantOrganizationIds = null;
        //资源状态条件
        Optional<Integer> virtualSpacesStatus = context.getOptionalInteger("virtualSpacesStatus");
        List<String> spacesStatus = Lists.newArrayList();
        List<String> spacesStatusAddTo = Lists.newArrayList();


        String virtualSpacesId = context.getString("virtualSpacesId");
        Integer businessType = context.getInteger("businessType");
        Optional<String> organizationId = context.getOptionalString("organizationId");
        List<String> superiorIds = Lists.newArrayList();

        if (organizationId.isPresent()) {
            //查询归属条件的子级部门,组织深度截至级别2
            superiorIds = courseOrganizationService.findSuperiorOrgId(organizationId.get());
            //查询当前虚拟空间的子级部门
            grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(currentUserId, Optional.of(virtualSpacesOrganizationId), context.getString("uri"));

            //选择了以外的部门
            if (!superiorIds.contains(organizationId.get()) && !grantOrganizationIds.contains(organizationId.get())) {
                return PagedResult.create(0, new ArrayList<>());
            }

            if (!organizationId.get().equals(virtualSpacesOrganizationId)  && ObjectUtils.isEmpty(superiorIds)) {
                //等于null,查询的是三级及以下
                grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(currentUserId, organizationId, context.getString("uri"));
            }
        } else {
            //默认查询空间归属的资源  包含子级别
            virtualSpacesGrantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(currentUserId, Optional.of(virtualSpacesOrganizationId), context.getString("uri"));
            superiorIds = courseOrganizationService.findSuperiorOrgId(virtualSpacesOrganizationId);
        }


        //查询禁用
        if (virtualSpacesStatus.isPresent() && Objects.equals(CourseVirtualSpace.STATUS_FORBIDDEN, virtualSpacesStatus.get())) {
            spacesStatus = courseVirtualSpaceService.findByVirtualSpacesStatus(virtualSpacesId, businessType, virtualSpacesStatus.get(), Optional.empty());
            if (ObjectUtils.isEmpty(spacesStatus)) {
                return PagedResult.create(0, new ArrayList<>());
            }
            return findSubjectVirtualSpaceForbidden(context, subject, virtualSpacesId, spacesStatus, virtualSpacesStatus, grantOrganizationIds);

        }

        //查询启用
        if (virtualSpacesStatus.isPresent() && Objects.equals(CourseVirtualSpace.STATUS_ENABLED, virtualSpacesStatus.get())) {
            //查询资源禁用 not in
            spacesStatus = (courseVirtualSpaceService.findByVirtualSpacesStatus(virtualSpacesId, businessType, CourseVirtualSpace.STATUS_FORBIDDEN, Optional.of(CourseInfo.TYPE_INSIDE)));
        }
        Optional<String> optionalString = context.getOptionalString("ids");
        if (optionalString.isPresent()){
            spacesStatus.addAll(Arrays.asList(optionalString.get().split(",")));
        }

        //查询添加上级分享启用 in
        spacesStatusAddTo = courseVirtualSpaceService.findByVirtualSpacesId(virtualSpacesId, businessType, virtualSpacesStatus);
        if (!ObjectUtils.isEmpty(spacesStatus) && !ObjectUtils.isEmpty(spacesStatusAddTo)){
            spacesStatusAddTo.removeAll(spacesStatus);
        }


        spacesStatusAddTo = additionalResources(organizationId, superiorIds, spacesStatusAddTo, context.getOptionalInteger("status"),context.getOptionalInteger("subjectType"), context.getOptionalString("name"), StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginTime")), StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndTime")));

        PagedResult<CourseInfo> subjectVirtualSpace = findSubjectVirtualSpace(context,subject,grantOrganizationIds,virtualSpacesId,virtualSpacesOrganizationId,virtualSpacesGrantOrganizationIds,spacesStatus,spacesStatusAddTo,virtualSpacesStatus);
        //查询专题在空间的状态
        findSubjectVirtualSpacesStatus(subjectVirtualSpace.getItems(), virtualSpacesId, businessType);
        return subjectVirtualSpace;
    }


    /**
     * 排除归属组织外的资源
     *
     * @param organizationId
     * @param superiorIds
     * @param spacesStatusAddTo
     * @param status
     * @param name
     * @return
     */
    private   List<String>  additionalResources(Optional<String> organizationId, List<String> superiorIds, List<String> spacesStatusAddTo, Optional<Integer> status, Optional<Integer> subjectType, Optional<String> name, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate) {
        return  courseInfoService.findVirtualSpacesAndOrgId(spacesStatusAddTo, status,superiorIds, subjectType, name, organizationId, shelveBeginDate,shelveEndDate);
    }

    /**
     * 查询启用
     * @param context
     * @param subject
     * @param grantOrganizationIds
     * @param virtualSpacesId
     * @param virtualSpacesOrganizationId
     * @param virtualSpacesGrantOrganizationIds
     * @param spacesStatus
     * @param spacesStatusAddTo
     * @param virtualSpacesStatus
     * @return
     */
    private PagedResult<CourseInfo> findSubjectVirtualSpace(RequestContext context,
                                                            Subject<Member> subject,
                                                            List<String> grantOrganizationIds,
                                                            String virtualSpacesId,
                                                            String virtualSpacesOrganizationId,
                                                            List<String> virtualSpacesGrantOrganizationIds,
                                                            List<String> spacesStatus,
                                                            List<String> spacesStatusAddTo,
                                                            Optional<Integer> virtualSpacesStatus) {
        return subjectService.findSubjectVirtualSpace(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                subject.getCurrentUserId(),
                context.getOptionalString("name"),
                context.getOptionalString("organizationId"),
                context.getOptionalInteger("status"),
                StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginTime")),
                StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndTime")),
                grantOrganizationIds,
                virtualSpacesId,
                virtualSpacesOrganizationId,
                virtualSpacesGrantOrganizationIds,
                spacesStatus,
                spacesStatusAddTo,
                virtualSpacesStatus,
                context.getOptionalInteger("subjectType")
        );
    }


    /**
     * 查询禁用
     * @param context
     * @param subject
     * @param virtualSpacesId
     * @param spacesStatus
     * @param virtualSpacesStatus
     * @param grantOrganizationIds
     * @return
     */
    private PagedResult<CourseInfo> findSubjectVirtualSpaceForbidden(RequestContext context,
                                                                     Subject<Member> subject,
                                                                     String virtualSpacesId,
                                                                     List<String> spacesStatus,
                                                                     Optional<Integer> virtualSpacesStatus,
                                                                     List<String> grantOrganizationIds){
        return subjectService.findSubjectVirtualSpaceForbidden(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                subject.getCurrentUserId(),
                context.getOptionalString("name"),
                context.getOptionalString("organizationId"),
                context.getOptionalInteger("status"),
                StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginDate")),
                StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndDate")),
                virtualSpacesId,
                spacesStatus,
                virtualSpacesStatus,
                grantOrganizationIds,
                context.getOptionalInteger("subjectType"));
    }


    /**
     *  查询上级分享的专题
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/find-superior-subject" , method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")  //课程名
    @Param(name = "categoryId")   //课程目录id
    @Param(name = "shelveBeginDate", type = String.class)  //发布时间
    @Param(name = "shelveEndDate", type = String.class)
    @Param(name = "allCategory") //1: 查询主副所有序列 0:或者没有只查询主序列
    @Param(name = "businessType" ,required = true) //0=课程 2=专题
    @Param(name = "status" ,type = Integer.class) //课程状态
    @Param(name = "virtualSpacesId" ,required = true) //空间id
    @Param(name = "virtualSpacesOrganizationId" ,required = true) //空间归属部门id
    @Param(name = "subjectType") //专题类型 1=普通,2=个性化
    @JSON("recordCount")
    @JSON("items.(id,name,code,status,shelveTime,publishClient,url,styles)")
    @JSON("items.organization.(id,name)")
    @Permitted
    public PagedResult<CourseInfo>  findSuperiorCourse(RequestContext requestContext){

        String virtualSpacesId = requestContext.getString("virtualSpacesId");
        Integer businessType = requestContext.getInteger("businessType");
        //查询已经添加的上级资源id
        List<String> byVirtualSpacesId = courseVirtualSpaceService.findByVirtualSpacesId(virtualSpacesId, businessType,Optional.empty());


        return  subjectService.findSuperiorSubject(
                byVirtualSpacesId,
                requestContext.getInteger("page"),
                requestContext.getInteger("pageSize"),
                requestContext.getOptionalString("name"),
                requestContext.getOptionalString("categoryId"),
                StringUtils.dateString2OptionalLong(requestContext.getOptionalString("shelveBeginDate")),
                StringUtils.dateString2OptionalLongMore(requestContext.getOptionalString("shelveEndDate")),
                requestContext.getOptionalInteger("allCategory"),
                businessType,
                virtualSpacesId,
                requestContext.getOptionalInteger("status"),
                requestContext.getString("virtualSpacesOrganizationId"),
                requestContext.getOptionalInteger("subjectType")
        );
    }



    /**
     *   导出虚拟空间专题列表
     */
    @RequestMapping(value = "/download-subject" ,method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "virtualSpacesOrganizationId" ,required = true) //空间归属部门id
    @Param(name = "virtualSpacesId" ,required = true) //空间id
    @Param(name = "businessType",type = Integer.class ,required = true)//专题类型,0=课程,1=考试,2=专题
    @Param(name = "name")
    @Param(name = "organizationId")    //归属部门,条件
    @Param(name = "status", type = Integer.class)
    @Param(name = "shelveBeginTime")
    @Param(name = "shelveEndTime")
    @Param(name = "uri")
    @Param(name = "virtualSpacesStatus" ,type = Integer.class) //空资源状态
    @Param(name = "contain",type = Integer.class)	//是否包含子级【0：不包含；1：包含】
    @Param(name = "subjectType") //专题类型 1=普通,2=个性化
    @Permitted
    public void downloadCourse(RequestContext requestContext,Subject<Member> subject) {


        String virtualSpacesOrganizationId = requestContext.getString(
                "virtualSpacesOrganizationId");

        List<String> grantOrganizationIds = null;
        List<String> virtualSpacesGrantOrganizationIds = null;

        //资源状态条件
        Optional<Integer> virtualSpacesStatus = requestContext.getOptionalInteger("virtualSpacesStatus");
        List<String> spacesStatus = Lists.newArrayList();
        List<String> spacesStatusAddTo = Lists.newArrayList();


        String virtualSpacesId = requestContext.getString("virtualSpacesId");
        Integer businessType = requestContext.getInteger("businessType");
        Optional<String> organizationId = requestContext.getOptionalString("organizationId");


        List<CourseInfo> subjectList = Lists.newArrayList();


        List<String> superiorIds = Lists.newArrayList();



        if (organizationId.isPresent()) {
            //查询归属条件的子级部门,组织深度截至级别2
            superiorIds = courseOrganizationService.findSuperiorOrgId(organizationId.get());
            //查询当前虚拟空间的子级部门
            grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(), Optional.of(virtualSpacesOrganizationId), requestContext.getString("uri"));

            //选择了以外的部门
            if (!superiorIds.contains(organizationId.get()) && !grantOrganizationIds.contains(organizationId.get())) {
                downloadSubject(requestContext,subjectList);
            }
            if (!organizationId.get().equals(virtualSpacesOrganizationId)  && ObjectUtils.isEmpty(superiorIds)) {
                //等于null,查询的是三级及以下
                grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(), organizationId, requestContext.getString("uri"));
            }
        } else {
            //默认查询空间归属的资源  包含子级别
            virtualSpacesGrantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(), Optional.of(virtualSpacesOrganizationId), requestContext.getString("uri"));
            superiorIds = courseOrganizationService.findSuperiorOrgId(virtualSpacesOrganizationId);
        }
        //查询禁用
        if (virtualSpacesStatus.isPresent() && Objects.equals(CourseVirtualSpace.STATUS_FORBIDDEN, virtualSpacesStatus.get())) {
            spacesStatus = courseVirtualSpaceService.findByVirtualSpacesStatus(virtualSpacesId, businessType, virtualSpacesStatus.get(), Optional.empty());
            if (!ObjectUtils.isEmpty(spacesStatus)) {
                subjectList = findSubjectVirtualSpaceForbidden(requestContext, null, virtualSpacesId, spacesStatus, virtualSpacesStatus, grantOrganizationIds).getItems();
            }
            downloadSubject(requestContext,subjectList);
        }

        //查询启用
        if (virtualSpacesStatus.isPresent() && Objects.equals(CourseVirtualSpace.STATUS_ENABLED, virtualSpacesStatus.get())) {
            //查询资源禁用 not in
            spacesStatus = courseVirtualSpaceService.findByVirtualSpacesStatus(virtualSpacesId, businessType, CourseVirtualSpace.STATUS_FORBIDDEN, Optional.of(CourseInfo.TYPE_INSIDE));
        }
        //查询添加上级分享启用 in
        spacesStatusAddTo = courseVirtualSpaceService.findByVirtualSpacesId(virtualSpacesId, businessType, virtualSpacesStatus);
        if (!ObjectUtils.isEmpty(spacesStatus) && !ObjectUtils.isEmpty(spacesStatusAddTo)){
            spacesStatusAddTo.removeAll(spacesStatus);
        }
        spacesStatusAddTo = additionalResources(organizationId, superiorIds, spacesStatusAddTo, requestContext.getOptionalInteger("status"),requestContext.getOptionalInteger("subjectType"), requestContext.getOptionalString("name"), StringUtils.dateString2OptionalLong(requestContext.getOptionalString("shelveBeginTime")), StringUtils.dateString2OptionalLongMore(requestContext.getOptionalString("shelveEndTime")));

        subjectList = findSubjectVirtualSpace(requestContext,subject,grantOrganizationIds,virtualSpacesId,virtualSpacesOrganizationId,virtualSpacesGrantOrganizationIds,spacesStatus,spacesStatusAddTo,virtualSpacesStatus).getItems();
        //查询专题在空间的状态
        findSubjectVirtualSpacesStatus(subjectList, virtualSpacesId, businessType);
        downloadSubject(requestContext,subjectList);

    }

    /**
     * 专题计划-新增关联列表
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/plan-subject", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "status", type = Integer.class)
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "shelveBeginTime", type = Long.class)
    @Param(name = "shelveEndTime", type = Long.class)
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】
    @Param(name = "uri")
    @Param(name = "planId",required = true)
    @Permitted
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,status,beginDate,endDate,cover,coverPath,shelveTime,code,publishClient,url, styles)")
    @JSON("items.organization.(id,name)")
    public PagedResult<CourseInfo> findSubjectPage(RequestContext context, Subject<Member> subject) {
        List<String> grantOrganizationIds = null;
        if (context.getOptionalInteger("contain").orElse(0) == 0) {
            grantOrganizationIds = courseInfoAdminKit.findGrantTopOrganizationIds(subject.getCurrentUserId(),context.getOptionalString("organizationId"),context.getOptionalString("uri").orElse("course-study/subject-info"));
        }else {
            grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(),context.getOptionalString("organizationId"),context.getOptionalString("uri").orElse("course-study/subject-info"));
        }
        return subjectService.findSubjectPage(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptionalString("name"),
                context.getOptionalString("organizationId"),
                context.getOptionalInteger("status"),
                context.getOptionalInteger("publishClient"),
                context.getOptional("shelveBeginTime", Long.class),
                context.getOptional("shelveEndTime", Long.class),
                grantOrganizationIds,
                context.getString("planId"));
    }

    /**
     * 专题计划-关联专题列表
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/related-subject", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "code")
    @Param(name = "status", type = Integer.class)
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "beginBeginDate", type = Long.class)
    @Param(name = "beginEndDate", type = Long.class)
    @Param(name = "endBeginDate", type = Long.class)
    @Param(name = "endEndDate", type = Long.class)
    @Param(name = "shelveBeginTime", type = Long.class)
    @Param(name = "shelveEndTime", type = Long.class)
    @Param(name = "subjectType", type = Integer.class) // 专题类型，1-普通；2-个性化
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】
    @Param(name = "uri")
    @Param(name = "planId",required = true)
    @Permitted
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,status,beginDate,endDate,cover,coverPath,shelveTime,code,publishClient,url, styles)")
    @JSON("items.organization.(id,name)")
    public PagedResult<CourseInfo> findRelatedSubjectPage(RequestContext context, Subject<Member> subject) {

        return subjectService.findRelatedSubjectPage(context.get("page", Integer.class),
                                                     context.get("pageSize", Integer.class),
                                                     context.getOptionalString("name"),
                                                     context.getOptionalString("organizationId"),
                                                     context.getOptionalString("code"),
                                                     context.getOptionalInteger("status"),
                                                     context.getOptionalInteger("publishClient"),
                                                     context.getOptional("beginBeginDate", Long.class),
                                                     context.getOptional("beginEndDate", Long.class),
                                                     context.getOptional("endBeginDate", Long.class),
                                                     context.getOptional("endEndDate", Long.class),
                                                     context.getOptional("shelveBeginTime", Long.class),
                                                     context.getOptional("shelveEndTime", Long.class),
                                                     context.getString("planId"));
    }


    /**
     * 专题导出
     * @param requestContext
     * @param subjectList
     */
    private  void downloadSubject(RequestContext requestContext, List<CourseInfo> subjectList){

        HttpServletResponse response = requestContext.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        try {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String("虚拟空间资源池-专题资源列表".getBytes("gb2312"), StandardCharsets.ISO_8859_1) + ".xlsx");
        } catch (
                UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        if (!ObjectUtils.isEmpty(subjectList)){
            Writer writer = new ExcelWriter();
            writer.sheet("在线专题导出信息", subjectList)
                    .field("专题名称", CourseInfo::getName)
                    .field("专题编码", CourseInfo::getCode)
                    .field("归属部门",courseInfo -> courseInfo.getOrganization().getName())
                    .field("专题类型", courseInfo -> {
                        if (ObjectUtils.isEmpty(courseInfo.getUrl())){
                            return "普通";
                        }
                        return "个性";
                    })
                    .field("专题状态", courseInfo -> {
                        switch (courseInfo.getStatus()){
                            case CourseInfo.STATUS_SHELVES:
                                return "已发布";
                            case CourseInfo.STATUS_NO:
                                return "未发布";
                            case CourseInfo.STATUS_THE_SHELVES:
                                return "已下架";
                            case CourseInfo.STATUS_THE_TEST:
                                return "测试中";
                            case CourseInfo.STATUS_IN_SHELVES:
                                return "发布中";
                            case CourseInfo.STATUS_FIVE_SHELVES:
                                return "退库";
                            case CourseInfo.STATUS_APPROVE:
                                return "审核";
                            default:
                                return "";
                        }
                    })
                    .field("适用终端", courseInfo -> {
                        switch (courseInfo.getPublishClient()){
                            case CourseInfo.PUBLISH_CLIENT_ALL:
                                return "PC&APP";
                            case CourseInfo.PUBLISH_CLIENT_PC:
                                return "PC";
                            case CourseInfo.PUBLIST_CLIENT_APP:
                                return "APP";
                            default:
                                return "";
                        }
                    })
                    .field("资源空间状态", r->{
                        if (Objects.equals(r.getCourseVirtualSpacesStatus(),CourseInfo.STATUS_FORBIDDEN)){
                            return "禁用";
                        }else {
                            return "启用";
                        }
                    })
                    .field("首次发布时间", r->{
                        if (ObjectUtils.isEmpty(r.getShelveTime())){
                            return "";
                        }else{
                            return DateUtil.format(r.getShelveTime());
                        }
                    });
            try {
                writer.write(response.getOutputStream());
            } catch (
                    IOException e) {
                throw new RuntimeException(e);
            }
        }
    }


    /**
     * 查询空间资源状态
     *
     * @param courseList
     * @param virtualSpacesId
     * @param businessType
     */
    private void findSubjectVirtualSpacesStatus(List<CourseInfo> courseList, String virtualSpacesId, Integer businessType){
        //查询课程再空间的状态
        if (!ObjectUtils.isEmpty(courseList)) {
            List<String> ids = courseList.stream().map(CourseInfo::getId).collect(Collectors.toList());
            Map<String, Integer> integerMap = courseVirtualSpaceService.findBusinessIdStatus(
                    ids,
                    virtualSpacesId,
                    businessType
            );
            courseList.forEach(r -> r.setCourseVirtualSpacesStatus(ObjectUtils.isEmpty(integerMap.get(r.getId())) ? CourseVirtualSpace.STATUS_ENABLED : integerMap.get(r.getId())));
        }
    }

    /**
     * 校验调用
     * @return
     */
    @RequestMapping(value = "/existedManager", method = RequestMethod.GET)
    @Param(name = "subjectId", required = true)
    @JSON("*")
    public boolean existedManager(RequestContext context, Subject<Member> subject){
       return courseRegisterService.existedManager(context.getString("subjectId"), subject.getCurrentUserId());
    }

    /**
     * 获取专题简介
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/get-subject-text", method = RequestMethod.GET)
    @Param(name = "subjectIds", required = true)// 专题id
    @JSON("*.*")
    public Map<String, String> getSubjectText(RequestContext context) {
        return subjectService.getSubjectText(Arrays.stream(context.getString("subjectIds").split(",")).collect(Collectors.toList()));
    }

    /**
     * 获取专题简介
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/add-audit", method = RequestMethod.GET)
    @Param(name = "subjectId", required = true)// 专题id
    @Param(name = "subjectName")
    @JSON("*.*")
    @Audit(module = "个人中心", subModule = "学情分析查询", action = Audit.Action.EXPORT, fisrtAction = "导出", desc = "导出学情分析报告《{0}》", params = {"subjectName"})
    public Map<String, String> addAduit(RequestContext context) {
        CourseInfo info = subjectService.get(context.getString("subjectId"));
        if(Objects.nonNull(info)){
            return ImmutableMap.of("result", "success");
        }
        return ImmutableMap.of("result", "fail");
    }

}
