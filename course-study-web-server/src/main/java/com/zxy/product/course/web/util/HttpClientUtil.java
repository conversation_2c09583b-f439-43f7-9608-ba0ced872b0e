package com.zxy.product.course.web.util;


import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.product.course.content.ErrorCode;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Base64;
import java.util.Iterator;
import java.util.Map;
import java.util.Set;

public class HttpClientUtil {

    private static final Logger logger = LoggerFactory.getLogger(HttpClientUtil.class);
    private static final CloseableHttpClient client;
    public static final String UTF8 = "UTF-8";
    public static final String GBK = "GBK";
    public static final String GB2312 = "GB2312";

    public HttpClientUtil() {
    }

    /**
     * 重写MultipartFile实现， 将文件流转换成MultipartFile
     */
    public static MultipartFile transferTo(InputStream inputStream, String contentType, String filename, int size) {
        return new MultipartFile() {

            @Override
            public String getName() {
                return filename;
            }

            @Override
            public String getOriginalFilename() {
                if (filename == null) {
                    // Should never happen.
                    return "";
                }

                // Check for Unix-style path
                int unixSep = filename.lastIndexOf('/');
                // Check for Windows-style path
                int winSep = filename.lastIndexOf("\\");
                // Cut off at latest possible point
                int pos = (winSep > unixSep ? winSep : unixSep);
                if (pos != -1) {
                    // Any sort of path separator found...
                    return filename.substring(pos + 1);
                } else {
                    // A plain name
                    return filename;
                }
            }

            @Override
            public String getContentType() {
                return contentType;
            }

            @Override
            public boolean isEmpty() {
                return size == 0;
            }

            @Override
            public long getSize() {
                return size;
            }

            @Override
            public byte[] getBytes() {
                return new byte[0];
            }

            @Override
            public InputStream getInputStream() {
                return inputStream;
            }

            @Override
            public void transferTo(File dest) throws IOException, IllegalStateException {
                if (dest.isAbsolute() && !dest.exists()) {
                    FileCopyUtils.copy(this.getInputStream(), Files.newOutputStream(dest.toPath()));
                }
            }
        };
    }

    private static String chineseToBase64Ascii(String filename) {
        return Base64.getEncoder()
                .encodeToString(filename.getBytes(StandardCharsets.UTF_8));
    }

    public static String uploadFileV2(String url, MultipartFile multipartFile, Map<String, String> headers, String jsonStr){
        // 创建HttpPost对象，并设置URL
        HttpPost httpPost = new HttpPost(url);
        Set<String> e;
        Iterator<String> ite;
        String headerKey;
        try {
            if(headers != null) {
                e = headers.keySet();
                ite = e.iterator();
                while(ite.hasNext()) {
                    headerKey = ite.next();
                    httpPost.addHeader(headerKey, headers.get(headerKey));
                }
            }
            // 使用MultipartEntityBuilder构建请求体
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setCharset(StandardCharsets.UTF_8);
            builder.addTextBody("data", jsonStr, ContentType.APPLICATION_JSON); // 添加普通参数
            logger.info("fileName={}", multipartFile.getOriginalFilename());
            String fileName = chineseToBase64Ascii(multipartFile.getOriginalFilename());
            logger.info("fileName end={}", fileName);
            builder.addBinaryBody(
                    "file",
                    multipartFile.getInputStream(),
                    ContentType.APPLICATION_OCTET_STREAM,
                    fileName
            );
            HttpEntity multipart = builder.build();
            // 将构建的实体设置到HttpPost中
            httpPost.setEntity(multipart);
            // 执行请求并获取响应
            CloseableHttpResponse response = client.execute(httpPost);
            try {
                // 获取响应内容
                String responseBody = EntityUtils.toString(response.getEntity());
                return responseBody;
            } finally {
                response.close();
            }
        } catch (IOException es) {
            logger.error("上传文件失败 message={}", es.getMessage());
            throw new UnprocessableException(ErrorCode.uploadFileFail);
        }
    }

    static {
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
        connManager.setMaxTotal(100);
        connManager.setDefaultMaxPerRoute(10);
        client = HttpClients.custom().setConnectionManager(connManager).build();
    }


    public static enum RequestConfigType {
        TIMEOUT_60000('\uea60', '\uea60', '\uea60'),
        TIMEOUT_10000(10000, 10000, 10000),
        TIMEOUT_3000(3000, 3000, 3000),
        TIMEOUT_2000(2000, 2000, 2000),
        TIMEOUT_500(500, 500, 500);

        private RequestConfig requestConfig;

        private RequestConfigType(int socketTimeout, int connectTimeout, int requestTimeout) {
            this.requestConfig = RequestConfig.custom().setSocketTimeout(socketTimeout).setConnectTimeout(connectTimeout).setConnectionRequestTimeout(requestTimeout).build();
        }
    }
}