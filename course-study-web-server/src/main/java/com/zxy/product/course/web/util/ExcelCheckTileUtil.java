package com.zxy.product.course.web.util;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.product.course.content.ErrorCode;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;

import java.io.InputStream;
import java.util.Objects;

/**
 * @Auther: xxh
 * @Date: 2025/8/13 - 08 - 13 - 14:51
 * @Description: com.zxy.product.course.web.util
 * @version: 1.0
 */
public class ExcelCheckTileUtil {

    private static Logger logger = Logger.getLogger(ExcelCheckTileUtil.class);

    public static void checkTile(InputStream inputStream, String[] expectedHeaders) {
        //校验模板表头是否一致
        boolean result = validateHeader(inputStream, expectedHeaders);
        //如果不一致则抛出异常（文件模板不正确）
        if (!result) {
            throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
        }
    }

    //校验模板的方法
    public static boolean validateHeader(InputStream inputStream, String[] expectedHeaders) {
        try {
            Workbook workbook = WorkbookFactory.create(inputStream);
            Sheet sheet = workbook.getSheetAt(0);
            Row headerRow = sheet.getRow(0);
            if (headerRow == null || headerRow.getPhysicalNumberOfCells() != expectedHeaders.length) {
                return false;
            }
            for (int i = 0; i < expectedHeaders.length; i++) {
                Cell cell = headerRow.getCell(i);
                if (Objects.isNull(cell)|| !Objects.equals(cell.getStringCellValue(),expectedHeaders[i])) {
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            logger.warn("模板错误 orgainTile= " + StringUtils.join(expectedHeaders));
            logger.warn("模板错误message= " + e.getMessage());
            return false;
        }
    }
}