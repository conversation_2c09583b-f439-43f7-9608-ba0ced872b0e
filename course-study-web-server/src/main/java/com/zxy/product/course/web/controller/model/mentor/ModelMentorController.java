package com.zxy.product.course.web.controller.model.mentor;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.support.DefaultReader;
import com.zxy.common.office.excel.support.validator.RequiredValidator;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.model.mentor.ModelMentorService;
import com.zxy.product.course.content.model.mentor.QuestionTypeEnum;
import com.zxy.product.course.dto.model.mentor.*;
import com.zxy.product.course.entity.*;
import com.zxy.product.human.api.FileService;
import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.util.UriComponentsBuilder;

import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import static com.zxy.product.course.content.ErrorCode.TheLikesAndDislikesHaveExceededTheOperationThreshold;
import static com.zxy.product.course.entity.CourseMainNote.HAS_RELEASE;
import static com.zxy.product.course.util.model.mentor.DatasetProcessing.dataQueryValidator;


/**
 * 数智导师控制器
 * <AUTHOR>
 * @date 2024年05月08日 16:10
 */
@Controller
@RequestMapping("/model-mentor")
public class ModelMentorController {
    private static final Logger logger= LoggerFactory.getLogger(ModelMentorController.class);

    @Value("${ai.mentor.synchronize.synchronizeNoteApi}")
    private String synchronizeNoteApi;

    /**学员端模型提问反馈阈值*/
    private final Integer feedbackThresholds=4;
    private Cache cache;
    private RestTemplate restTemplate;
    private ModelMentorService mentorService;
    private FileService fileService;
    private AttachmentResolver attachmentResolver;
    //成盐运维
    private final static String USER_ID ="29b12bf4-e30f-4122-b7c4-d8dc2278b787";

    @Autowired
    public void setRestTemplate(RestTemplate restTemplate) { this.restTemplate = restTemplate; }

    @Autowired
    public void setCache(CacheService cacheService){
        String feedbackThresholds = "feedback#thresholds";
        this.cache=cacheService.create(feedbackThresholds);
    }

    @Autowired
    public void setMentorService(ModelMentorService mentorService){
        this.mentorService=mentorService;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
        this.attachmentResolver = attachmentResolver;
    }

    /**
     * 管理端管理员修改预设问题
     * @param requestContext 请求上下文
     * @return 数智导师管理员修改预设问题
     */
    @JSON("id,resourceId,presetQuestion,presetAnswer,adjustType,adjustReason")
    @Param(name = "id",required = true)
    @Param(name = "presetQuestion",required = true)
    @Param(name = "presetAnswer",required = true)
    @Param(name = "adjustType",type = Integer.class)
    @Param(name = "adjustReason")
    @Permitted(perms="course-study/management")
    @RequestMapping(value = "/edit-preset-example",method = RequestMethod.POST)
    public AiPresetExample editPresetExample(RequestContext requestContext){
        PresetExampleDTO exampleDTO=new PresetExampleDTO();
        exampleDTO.setId(requestContext.getString("id"));
        exampleDTO.setPresetQuestion(requestContext.getString("presetQuestion"));
        exampleDTO.setPresetAnswer(requestContext.getString("presetAnswer"));
        exampleDTO.setAdjustType(requestContext.getInteger("adjustType"));
        Optional<String> adjustReason = requestContext.getOptional("adjustReason", String.class);
        exampleDTO.setAdjustReason(adjustReason.orElse(""));
        return mentorService.editPresetExample(exampleDTO);
    }

    /**
     * 管理端管理员添加预设问题（只能添加通用问题）
     * @param requestContext 请求上下文
     * @param requestContext resourceType 业务类型，0-课程；2-专题
     * @return 数智导师管理员修改预设问题
     */
    @JSON("status")
    @Param(name = "presetQuestion",required = true)
    @Param(name = "presetAnswer",required = true)
    @Param(name = "resourceType",type = Integer.class)
    @Permitted
    @RequestMapping(value = "/save-preset-example",method = RequestMethod.POST)
    public Map<String,String> addPresetExample(RequestContext requestContext){
        String presetQuestion = requestContext.getString("presetQuestion");
        String presetAnswer = requestContext.getString("presetAnswer");
        Optional<Integer> resourceType = requestContext.getOptionalInteger("resourceType");
        mentorService.addPresetExample(presetQuestion,presetAnswer,resourceType);
        return ImmutableMap.of("status","SUCCESS");
    }

    /**
     * 管理端管理员删除预设问题（只能删除通用问题）
     * @param requestContext 请求上下文
     * @return 数智导师管理员修改预设问题
     */
    @JSON("status")
    @Param(name = "id",required = true)
    @Permitted
    @RequestMapping(value = "/del-preset-example",method = RequestMethod.GET)
    public Map<String,String> delPresetExample(RequestContext requestContext){
        String id = requestContext.getString("id");
        mentorService.delPresetExample(id);
        return ImmutableMap.of("status","SUCCESS");
    }

    /**
     * 管理端查询资源预设问题详情
     * @param requestContext 请求上下文
     * @return 数智导师根据查询资源预设问题详情
     */
    @JSON("id,resourceId,presetQuestion,presetAnswer,adjustType,adjustReason,questionType,resourceName,resourceType")
    @Param(name = "id",required = true)
    @Permitted(perms="course-study/management")
    @RequestMapping(value = "/single-preset-example",method = RequestMethod.POST)
    public AiPresetExample singlePresetExample(RequestContext requestContext){
        String id = requestContext.getString("id");
        return mentorService.singlePresetExample(id);
    }

    /**
     * 学员端数智导师猜你想问
     * @param requestContext 请求上下文
     * @return 数智导师用户猜你想问
     */
    @JSON("id,resourceId,presetQuestion")
    @Param(name = "resourceId",required = true)
    @Param(name = "operationType",required = true,type = Integer.class)
    @Param(name = "resourceType",type = Integer.class) // 0:课程 2:专题
    @Permitted
    @RequestMapping(value = "/preset-may-want-ask",method = RequestMethod.POST)
    public List<AiPresetExample> presetMayWantAsk(RequestContext requestContext){
        String resourceId = requestContext.getString("resourceId");
        Integer operationType = requestContext.getInteger("operationType");
        Optional<Integer> resourceType = requestContext.getOptionalInteger("resourceType");
        return mentorService.presetMayWantAsk(resourceId,operationType,resourceType);
    }

    /**
     * 管理端数智导师预设问题PAGE数据
     * @param requestContext 请求上下文
     * @return 数智导师预设问题PAGE页数据
     */
    @JSON("recordCount")
    @JSON("items.(id,presetQuestion,recourseType,resourceName,updateTime,questionType)")
    @JSON("items.organization.(id,name)")
    @Param(name = "page",required = true,type = Integer.class)
    @Param(name = "pageSize",required = true,type = Integer.class)
    @Param(name = "presetQuestion")
    @Param(name = "resourceName")
    @Param(name = "questionType",type = Integer.class)
    @Param(name = "resourceType",type = Integer.class)
    @Param(name = "resourceNameOrder",type = Integer.class)
    @Param(name = "updateTimeOrder",type = Integer.class)
    @Permitted(perms="course-study/management")
    @RequestMapping(value = "/preset-example-page",method = RequestMethod.POST)
    public PagedResult<AiPresetExample> presetExamplePage(RequestContext requestContext){
        Integer pageNum = requestContext.getInteger("page");
        Integer pageSize = requestContext.getInteger("pageSize");
        Optional<String> presetQuestion = requestContext.getOptional("presetQuestion", String.class);
        Optional<String> resourceName = requestContext.getOptional("resourceName", String.class);
        Optional<Integer> resourceType = requestContext.getOptional("resourceType", Integer.class);
        Optional<Integer> questionType = requestContext.getOptional("questionType", Integer.class);
        Optional<Integer> resourceNameOrder = requestContext.getOptional("resourceNameOrder", Integer.class);
        Optional<Integer> updateTimeOrder = requestContext.getOptional("updateTimeOrder", Integer.class);
        if(updateTimeOrder.isPresent() ){
            return mentorService.examplePage(pageNum,pageSize,presetQuestion,resourceName,resourceType,questionType,Optional.empty(),updateTimeOrder,0);
        }
        if(resourceNameOrder.isPresent() && resourceNameOrder.get()==0){
            return mentorService.examplePage(pageNum,pageSize,presetQuestion,resourceName,resourceType,questionType,Optional.empty(),Optional.empty(),1);
        }
        if(resourceNameOrder.isPresent() && resourceNameOrder.get()==1){
            return mentorService.examplePage(pageNum,pageSize,presetQuestion,resourceName,resourceType,questionType,resourceNameOrder,Optional.empty(),0);
        }
        return null;
    }

    /**
     * 管理端数智导师反馈Page数据
     * @param requestContext 请求上下文
     * @return 数智导师反馈Page数据
     */
    @JSON("recordCount")
    @JSON("items.(id,memberName,question,resourceName,resourceType,experience,feedbackType,feedbackSubstance,createTime,updateTime)")
    @Param(name = "presetQuestion")
    @Param(name = "resourceName")
    @Param(name = "resourceType",type = Integer.class)
    @Param(name = "page",type = Integer.class,required = true)
    @Param(name = "pageSize",type = Integer.class,required = true)
    @Permitted(perms = "course-study/management")
    @RequestMapping(value = "/feedback-page",method = RequestMethod.POST)
    public PagedResult<AiFeedback> feedbackPage(RequestContext requestContext){
        Integer pageNum = requestContext.get("page", Integer.class);
        Integer pageSize = requestContext.get("pageSize", Integer.class);
        Optional<String> question = requestContext.getOptional("presetQuestion", String.class);
        Optional<String> resourceName = requestContext.getOptional("resourceName", String.class);
        Optional<Integer> resourceType = requestContext.getOptional("resourceType", Integer.class);
        return mentorService.feedbackPage(pageNum,pageSize,question,resourceName,resourceType);
    }

    /**
     * 管理端查询数智导师反馈详情
     * @param requestContext 请求上下文
     * @return 数智导师反馈详情
     */
    @JSON("id,resourceType,resourceName,memberName,updateTime,createTime,question,answer,experience,feedbackType,feedbackSubstance")
    @Param(name = "id",required = true)
    @Permitted(perms = {"course-study/management-feedback","course-study/management"})
    @RequestMapping(value = "/single-feedback",method = RequestMethod.POST)
    public AiFeedback singleFeedback(RequestContext requestContext){
        return mentorService.singleFeedback(requestContext.getString("id"));
    }

    /**
     * 学员端用户Ai提问
     * @param subject 用户容器
     * @param requestContext 请求上下文
     * @return 用户Ai提问后添加的数智导师VO
     */
    @JSON("id,resourceId,memberId,questionId,answerId,feedbackId,createTime")
    @Param(name = "question",required = true)
    @Param(name = "answer",required = true)
    @Param(name = "resourceId",required = true)
    @Param(name = "resourceType",required = true,type = Integer.class)
    @RequestMapping(value = "/mentor-member-ask",method = RequestMethod.POST)
    public AiMentor mentorMemberAsk(
            Subject<Member> subject, RequestContext requestContext
    ){
        MentorDTO mentorDTO=new MentorDTO();
        mentorDTO.setMemberId(subject.getCurrentUserId());
        mentorDTO.setQuestion(requestContext.getString("question"));
        mentorDTO.setAnswer(requestContext.getString("answer"));
        mentorDTO.setResourceId(requestContext.getString("resourceId"));
        mentorDTO.setResourceType(requestContext.getInteger("resourceType"));
        return mentorService.mentorMemberAsk(mentorDTO);
    }

    /**
     * 学员端用户重新生成答案
     * @param requestContext 请求上下文
     * @return 重新生成答案的数智导师VO
     */
    @JSON("id,resourceId,memberId,questionId,answerId,feedbackId,createTime")
    @Param(name = "mentorId",required = true)
    @Param(name = "answerId",required = true)
    @Param(name = "answer",required = true)
    @RequestMapping(value = "/member-again-ask",method = RequestMethod.POST)
    public AiMentor memberAgainAsk(RequestContext requestContext){
        EditAnswerDTO editAnswerDTO=new EditAnswerDTO();
        editAnswerDTO.setMentorId(requestContext.getString("mentorId"));
        editAnswerDTO.setAnswerId(requestContext.getString("answerId"));
        editAnswerDTO.setAnswer(requestContext.getString("answer"));
        return mentorService.memberAgainAsk(editAnswerDTO);
    }

    /**
     * 数智导师校验用户反馈次数
     * @param feedbackValue 原子操作用户反馈次数
     */
    private void checkThresholds(Long feedbackValue){
        Predicate<Long> numPredicate = ew1 -> Objects.isNull(ew1)||ew1 < feedbackThresholds;
        dataQueryValidator(feedbackValue, TheLikesAndDislikesHaveExceededTheOperationThreshold, numPredicate);
    }

    /**
     * 学员端用户针对AI回答反馈
     * @param subject 用户容器
     * @param requestContext 请求上下文
     * @return 返回的数智导师VO
     */
    @JSON("id,resourceId,memberId,questionId,answerId,feedbackId,createTime")
    @Param(name = "mentorId",required = true)
    @Param(name = "feedbackType",type = Integer.class)
    @Param(name = "feedbackSubstance")
    @Param(name = "experience",type = Integer.class,required = true)
    @RequestMapping(value = "/mentor-member-feedback",method = RequestMethod.POST)
    public AiMentor mentorMemberFeedback(
            Subject<Member> subject,RequestContext requestContext
    ){
        String mentorId = requestContext.getString("mentorId");
        String memberId = subject.getCurrentUserId();
        Long feedbackValue = cache.increment(memberId + "#" + mentorId);
        cache.expire(memberId + "#" + mentorId,60*60*2);
        this.checkThresholds(feedbackValue);
        FeedbackDTO feedbackDTO = this.buildFeedbackDTO(requestContext);
        feedbackDTO.setMentorId(mentorId);
        return mentorService.mentorMemberFeedback(feedbackDTO);
    }

    /**
     * 学员端用户自行修改AI回答反馈
     * @param requestContext 请求上下文
     * @return 返回的数智导师VO
     */
    @JSON("id,resourceId,memberId,questionId,answerId,feedbackId,createTime")
    @Param(name = "id",required = true)
    @Param(name = "operationType",type = Integer.class,required = true)
    @Param(name = "mentorId",required = true)
    @Param(name = "feedbackType",type = Integer.class)
    @Param(name = "feedbackSubstance")
    @Param(name = "experience",type = Integer.class)
    @RequestMapping(value = "/mentor-edit-feedback",method = RequestMethod.POST)
    public AiMentor mentorEditFeedback(Subject<Member> subject,RequestContext requestContext){
        String memberId = subject.getCurrentUserId();
        String mentorId = requestContext.getString("mentorId");
        Long feedbackValue = cache.increment(memberId + "#" + mentorId);
        cache.expire(memberId + "#" + mentorId,60*60*2);
        this.checkThresholds(feedbackValue);
        FeedbackDTO feedbackDTO = this.buildFeedbackDTO(requestContext);
        feedbackDTO.setMentorId(mentorId);
        feedbackDTO.setId(requestContext.getString("id"));
        feedbackDTO.setOperationType(requestContext.getInteger("operationType"));
        return mentorService.mentorEditFeedback(feedbackDTO);
    }

    /**
     * 构建用户AI提问反馈DTO
     * @param requestContext 请求上下文
     * @return 用户AI提问反馈DTO
     */
    private FeedbackDTO buildFeedbackDTO(RequestContext requestContext){
        FeedbackDTO feedbackDTO = new FeedbackDTO();
        Optional<Integer> feedbackType = requestContext.getOptional("feedbackType", Integer.class);
        feedbackType.ifPresent(feedbackDTO::setFeedbackType);
        Optional<String> substance = requestContext.getOptional("feedbackSubstance", String.class);
        substance.ifPresent(feedbackDTO::setFeedbackSubstance);
        Optional<Integer> experience = requestContext.getOptional("experience", Integer.class);
        experience.ifPresent(feedbackDTO::setExperience);
        return feedbackDTO;
    }

    /**
     * 学员端用户AI聊天记录
     * @param subject 用户容器
     * @param requestContext 请求上下文
     * @return 用户AI聊天记录（15条一问一答，也是就是30条记录）
     */
    @JSON("mentorId,feedbackId,question,answer,answerId,experience,createTime")
    @Param(name = "resourceId",required = true)
    @RequestMapping(value = "/mentor-chat-box-collect",method = RequestMethod.POST)
    public List<AiMentor> mentorChatBoxCollect(
            Subject<Member> subject, RequestContext requestContext
    ){
        ChatQueryDTO chatQueryDTO = new ChatQueryDTO();
        chatQueryDTO.setMemberId(subject.getCurrentUserId());
        chatQueryDTO.setResourceId(requestContext.getString("resourceId"));
        return mentorService.mentorChatBoxCollect(chatQueryDTO);
    }

    /**
     * 数智导师V2：管理端查询课件笔记Page列表页
     * @param context 请求上下文
     * @return 管理端课件笔记Page列表页
     */
    @JSON("recordCount")
    @JSON("items.(id,summaryContent,submitTime,isTop,likeNum,courseName,sectionName,submitName,updateTime)")
    @Param(name = "summaryContent")
    @Param(name = "courseName")
    @Param(name = "pageSize",type = Integer.class,required = true)
    @Param(name = "page",type = Integer.class,required = true)
    @Permitted
    @RequestMapping(value = "/courseware-note-page",method = RequestMethod.POST)
    public PagedResult<CoursewareNote> coursewareNotePage(RequestContext context){
        Integer page = context.getInteger("page");
        Integer pageSize = context.getInteger("pageSize");
        Optional<String> courseNameOpt = context.getOptionalString("courseName");
        Optional<String> summaryContentOpt = context.getOptionalString("summaryContent");
        return mentorService.coursewareNotePage(page,pageSize,courseNameOpt,summaryContentOpt);
    }

    /**
     * 数智导师V2：管理端查询课件笔记详情
     * @param context 请求上下文
     * @return 管理端查询课件笔记详情
     */
    @JSON("noteVersionCollect.(id,essential,content,versionNoteId,createTime)")
    @JSON("id,orgName,courseId,sectionId,submitTime,submitName,summaryContent")
    @Param(name = "id",required = true)
    @Permitted
    @RequestMapping(value = "/single-courseware-note",method = RequestMethod.GET)
    public CoursewareNote singleCoursewareNote(RequestContext context){
        return mentorService.singleCoursewareNote(context.getString("id"));
    }

    /**
     * 数智导师V2：管理端查询课件笔记版本集合 | 查询课件笔记版本当前回显状态
     * @param context 请求上下文
     * @return 管理端查询课件笔记版本集合 | 查询课件笔记版本当前回显状态
     */
    @JSON("currentVersion.(id,courseId,sectionId,currentStatus,version)")
    @JSON("noteVersionCollect.(id,courseId,sectionId,currentStatus,version)")
    @Param(name = "sectionId",required = true)
    @Param(name = "courseId",required = true)
    @Permitted
    @RequestMapping(value = "/current-ware-note",method = RequestMethod.POST)
    public Map<String,Object> currentWareNote(RequestContext context){
        String courseId = context.getString("courseId");
        String sectionId = context.getString("sectionId");
        return mentorService.currentWareNote(courseId,sectionId);
    }

    /**
     * 数智导师V2：管理端切换课件笔记|送审课件笔记
     * @param context 请求上下文
     * @return 管理端切换课件笔记|送审课件笔记出参
     */
    @JSON("*")
    @Param(name = "wareNoteId",required = true)
    @Permitted
    @RequestMapping(value = "/switch-ware-note",method = RequestMethod.GET)
    public Map<String, String> switchWareNote(RequestContext context){
        return mentorService.switchWareNote(context.getString("wareNoteId"));
    }

    /**
     * 数智导师V2：管理端审核课件笔记
     * @param context 请求上下文
     * @param subject 用户容器
     * @return 出参
     */
    @JSON("*")
    @Param(name = "auditOpinion")
    @Param(name = "auditStatus",type = Integer.class, required = true)
    @Param(name = "id",required = true)
    @Permitted
    @RequestMapping(value = "/audit-ware-note",method = RequestMethod.POST)
    public Map<String,String> auditWareNote(RequestContext context,Subject<Member>subject){
        String memberId = subject.getCurrentUserId();
        String id = context.getString("id");
        Integer auditStatus = context.getInteger("auditStatus");
        Optional<String> auditOpinionOpt = context.getOptionalString("auditOpinion");
        return mentorService.auditWareNote(id,memberId,auditStatus,auditOpinionOpt);
    }

    /**
     * 数智导师V2：管理端修正课件笔记
     * @param context 请求上下文
     * @param subject 用户容器
     * @return 出参
     */
    @JSON("*")
    @Param(name = "noteVersionJson",required = true)
    @Param(name = "summaryContent",required = true)
    @Param(name = "id",required = true)
    @Permitted
    @RequestMapping(value = "/edit-courseware-note",method = RequestMethod.POST)
    public Map<String,String> editCoursewareNote(RequestContext context,Subject<Member>subject){
        String memberId = subject.getCurrentUserId();
        String id = context.getString("id");
        String summaryContent = context.getString("summaryContent");
        String noteVersionJson = context.getString("noteVersionJson");
        return mentorService.editCoursewareNote(id,memberId,summaryContent,noteVersionJson);
    }

    /**
     * 数智导师V2：管理端课件笔记审核Page列表数据
     * @param context 请求上下文
     * @return 课件笔记审核Page列表数据
     */
    @JSON("recordCount")
    @JSON("items.(id,courseName,status,summaryContent,submitTime,auditStatus,auditMemberName,orgName,sectionName,auditTime)")
    @Param(name = "summaryContent")
    @Param(name = "courseName")
    @Param(name = "pageSize",type = Integer.class,required = true)
    @Param(name = "page",type = Integer.class,required = true)
    @Permitted
    @RequestMapping(value = "/ware-note-audit-page",method = RequestMethod.POST)
    public PagedResult<CoursewareNoteAudit> wareNoteAuditPage(RequestContext context){
        Integer page = context.getInteger("page");
        Integer pageSize = context.getInteger("pageSize");
        Optional<String> courseNameOpt = context.getOptionalString("courseName");
        Optional<String> summaryContentOpt = context.getOptionalString("summaryContent");
        return mentorService.wareNoteAuditPage(page,pageSize,courseNameOpt,summaryContentOpt);
    }

    /**
     * 数智导师V2：管理端根据Id查询课件笔记审核详情
     * @param context 请求上下文
     * @return 管理端课件笔记审核详情数据
     */
    @JSON("id,courseName,status,summaryContent,submitTime,auditStatus,auditMemberName,orgName,auditOpinion,sectionName,submitMemberName")
    @JSON("noteVersionCollect.(essential,content,)")
    @Param(name = "id",required = true)
    @Permitted
    @RequestMapping(value = "/single-ware-note-audit",method = RequestMethod.GET)
    public CoursewareNoteAudit singleWareNoteAudit(RequestContext context){
        return mentorService.singleWareNoteAudit(context.getString("id"));
    }

    /**
     * 数智导师V2：管理端课程笔记Page列表页
     * @param context 请求上下文
     * @return 管理端课程笔记Page列表页
     */
    @JSON("recordCount")
    @JSON("items.(id,courseName,orgName,summaryContent,updateTime,status,courseId,orgId,businessType,releaseStatus,firstReleaseTime)")
    @Param(name = "summaryContent")
    @Param(name = "courseName")
    @Param(name = "businessType",type = Integer.class)
    @Param(name = "pageSize",type = Integer.class,required = true)
    @Param(name = "page",type = Integer.class,required = true)
    @Permitted
    @RequestMapping(value = "/course-main-note-page",method = RequestMethod.POST)
    public PagedResult<CourseMainNote> courseMainNotePage(RequestContext context){
        Integer page = context.getInteger("page");
        Integer pageSize = context.getInteger("pageSize");
        Optional<String> courseNameOpt = context.getOptionalString("courseName");
        Optional<String> summaryContentOpt = context.getOptionalString("summaryContent");
        Optional<Integer> businessTypeOpt = context.getOptionalInteger("businessType");
        return mentorService.courseMainNotePage(page,pageSize,courseNameOpt,summaryContentOpt,businessTypeOpt);
    }


    /**
     * 数智导师V2.1：管理端数智导师内容Page列表页
     * @param context 请求上下文
     */
    @JSON("recordCount")
    @JSON("items.(id,courseName,businessType,orgName,status,releaseMemberName,updateTime,courseId,orgId,switchMentor,releaseStatus)")
    @Param(name = "courseName")
    @Param(name = "organizationId")
    @Param(name = "businessType",type = Integer.class)
    @Param(name = "switchMentor",type = Integer.class)
    @Param(name = "pageSize",type = Integer.class,required = true)
    @Param(name = "page",type = Integer.class,required = true)
    @Permitted
    @RequestMapping(value = "/course-main-note-content-page",method = RequestMethod.POST)
    public PagedResult<CourseMainNote> courseMainNoteContentPage(RequestContext context){
        Integer page = context.getInteger("page");
        Integer pageSize = context.getInteger("pageSize");

        Optional<String> courseNameOpt = context.getOptionalString("courseName");
        Optional<String> organizationId = context.getOptionalString("organizationId");
        Optional<Integer> businessTypeOpt = context.getOptionalInteger("businessType");
        Optional<Integer> switchMentorOpt = context.getOptionalInteger("switchMentor");
        return mentorService.courseMainNoteContentPage(page,pageSize,courseNameOpt,organizationId,businessTypeOpt,switchMentorOpt);
    }

    /**
     * 数智导师V2.1：管理端数智导师内容开启/关闭
     */
    @Permitted
    @ResponseBody
    @Param(name = "type", type = Integer.class)
    @Param(name = "status", type = Integer.class)
    @Param(name = "courseId")
    @RequestMapping(value = "/course-main-note-edit-mentor-switch", method = RequestMethod.POST)
    public void editSwitchMentor(RequestContext context) {
        Integer type = context.getInteger("type");
        Integer status = context.getInteger("status");
        String courseId = context.getString("courseId");
        mentorService.editSwitchMentor(courseId,status,type);
    }


    /**
     * 数智导师V2：管理端根据Id查询课程笔记详情
     * @param context 请求上下文
     * @return 管理端根据Id查询课程笔记详情
     */
    @JSON("id,courseId,orgName,submitTime,submitName,summaryContent")
    @JSON("mainNoteVersionCollect.(id,essential,content,versionMainNoteId,createTime)")
    @Param(name = "id",required = true)
    @Permitted
    @RequestMapping(value = "/single-course-main-note",method = RequestMethod.GET)
    public CourseMainNote singleCourseMainNote(RequestContext context){
        return mentorService.singleCourseMainNote(context.getString("id"));
    }

    /**
     * 数智导师V2：管理端查询课程笔记回显版本以及版本信息集合
     * @param context 请求上下文
     * @return  管理端查询课程笔记回显版本以及版本信息集合
     */
    @JSON("currentVersion.(id,courseId,currentStatus,version)")
    @JSON("noteVersionCollect.(id,courseId,currentStatus,version)")
    @Param(name = "courseId",required = true)
    @Permitted
    @RequestMapping(value = "/current-main-note",method = RequestMethod.GET)
    public Map<String, Object> currentMainNote(RequestContext context){
        return mentorService.currentMainNote(context.getString("courseId"));
    }

    /**
     * 数智导师V2：管理端切换课程笔记版本|送审
     * @param context 请求上下文
     * @return 管理端切换课程笔记版本|送审
     */
    @JSON("*")
    @Param(name = "mainNoteId",required = true)
    @Param(name = "currentMainNoteId",required = true)
    @Permitted
    @RequestMapping(value = "/switch-main-note",method = RequestMethod.GET)
    public Map<String, String> switchMainNote(RequestContext context){
        return mentorService.switchMainNote(context.getString("mainNoteId"), context.getString("currentMainNoteId"));
    }

    /**
     * 数智导师V2：管理端审核课程笔记
     * @param context 请求上下文
     * @param subject 用户容器
     * @return 审核课程笔记后出参
     */
    @JSON("*")
    @Param(name = "auditOpinion")
    @Param(name = "auditStatus",type = Integer.class,required = true)
    @Param(name = "id",required = true)
    @Permitted
    @RequestMapping(value = "/audit-main-note",method = RequestMethod.POST)
    public Map<String,String> auditMainNote(RequestContext context,Subject<Member>subject){
        String memberId = subject.getCurrentUserId();
        String id = context.getString("id");
        Integer auditStatus = context.getInteger("auditStatus");
        Optional<String> auditOpinionOpt = context.getOptionalString("auditOpinion");
        return mentorService.auditMainNote(id,memberId,auditStatus,auditOpinionOpt);
    }

    /**
     * 数智导师V2：管理端修正课程笔记
     * @param context 请求上下文
     * @param subject 用户容器
     * @return 修正课件笔记
     */
    @JSON("*")
    @Param(name = "mainVersionJson",required = true)
    @Param(name = "summaryContent",required = true)
    @Param(name = "id",required = true)
    @Permitted
    @RequestMapping(value = "/edit-course-main-note",method = RequestMethod.POST)
    public Map<String,String> editCourseMainNote(RequestContext context,Subject<Member>subject){
        String memberId = subject.getCurrentUserId();
        String id = context.getString("id");
        String summaryContent = context.getString("summaryContent");
        String mainVersionJson = context.getString("mainVersionJson");
        return mentorService.editCourseMainNote(id,memberId,summaryContent,mainVersionJson);
    }

    /**
     * 数智导师V2：管理端课程笔记审核Page列表数据
     * @param context 请求上下文
     * @return 管理端课程笔记审核Page列表数据
     */
    @JSON("recordCount")
    @JSON("items.(id,courseName,orgName,status,submitTime,auditStatus,summaryContent,auditMemberName,auditTime)")
    @Param(name = "summaryContent")
    @Param(name = "courseName")
    @Param(name = "pageSize",required = true,type = Integer.class)
    @Param(name = "page",required = true,type = Integer.class)
    @Permitted
    @RequestMapping(value = "/main-note-audit-page",method = RequestMethod.POST)
    public PagedResult<CourseMainNoteAudit> mainNoteAuditPage(RequestContext context){
        Integer page = context.getInteger("page");
        Integer pageSize = context.getInteger("pageSize");
        Optional<String> courseNameOpt = context.getOptionalString("courseName");
        Optional<String> summaryContentOpt = context.getOptionalString("summaryContent");
        return mentorService.mainNoteAuditPage(page,pageSize,courseNameOpt,summaryContentOpt);
    }

    /**
     * 数智导师V2：管理端根据Id查询课程笔记审核详情
     * @param context 请求上下文
     * @return 根据Id查询课程笔记审核详情
     */
    @JSON("id,courseName,orgName,status,submitTime,auditStatus,summaryContent,auditMemberName,submitMemberName")
    @JSON("mainNoteCollect.(essential,content)")
    @Param(name = "id",required = true)
    @Permitted
    @RequestMapping(value = "/single-main-note-audit",method = RequestMethod.GET)
    public CourseMainNoteAudit singleMainNoteAudit(RequestContext context){
        return mentorService.singleMainNoteAudit(context.getString("id"));
    }

    /**
     * 数智导师V2：学员端查询课件笔记
     * @param context 请求上下文
     * @return 课件官方笔记
     */
    @JSON("content,isTitle,courseId,sectionId")
    @Param(name = "sectionId",required = true)
    @Param(name = "courseId",required = true)
    @RequestMapping(value = "/smart-note-course",method = RequestMethod.POST)
    public List<SmartNoteDTO.Content> smartNoteOfCourse(RequestContext context,Subject<Member> subject){
        subject.getCurrentUserId();
        String courseId = context.getString("courseId");
        String sectionId = context.getString("sectionId");
        return mentorService.smartNoteOfCourse(courseId,sectionId);
    }

    /**
     * 数智导师V2：学员端查询课程笔记
     * @param context 请求上下文
     * @return 课程摘要And知识点内容集合
     */
    @JSON("id,summaryContent")
    @JSON("mainNoteVersionCollect.(essential,content)")
    @Param(name = "courseId",required = true)
    @RequestMapping(value = "/main-note-course",method = RequestMethod.GET)
    public CourseMainNote mainNoteOfCourse(RequestContext context,Subject<Member> subject){
        subject.getCurrentUserId();
        String courseId = context.getString("courseId");
        return mentorService.mainNoteOfCourse(courseId);
    }


    @JSON("synchronizeStatus")
    @Param(name = "itemid",required = true)
    @Param(name = "noduleid")
    @Param(name = "type",required = true)
    @RequestMapping(value = "/synchronize-note",method = RequestMethod.POST)
    public Map<String,String> synchronizeNote(RequestContext context, Subject<Member> subject){
        String memberId = subject.getCurrentUserId();
        String id = String.valueOf(UUID.randomUUID());
        String itemId = context.getString("itemid");
        String type = context.getString("type");
        Optional<String> noduleIdOpt = context.getOptionalString("noduleid");
        this.synchronizeNote(id,itemId,type,noduleIdOpt,memberId);
        return ImmutableMap.of("synchronizeStatus","SUCCESS");
    }


    @Permitted
    @ResponseBody
    @Param(name = "courseMainNoteId", required = true)
    @Param(name = "status", required = true, type = Integer.class)
    @RequestMapping(value = "/course-main-note-release-status-edit", method = RequestMethod.POST)
    public void release(RequestContext context,Subject<Member> memberSubject) {
        Integer status = context.getInteger("status");
        if (!(CourseMainNote.HAS_RELEASE.equals(status) || CourseMainNote.CANCEL_RELEASE.equals(status))) {
            throw new UnsupportedOperationException("发布状态异常");
        }
        mentorService.editReleaseStatus(context.getString("courseMainNoteId"), status, memberSubject.getCurrentUserId());
    }

    /**
     * 数智导师V2：拉取课件|课程|猜你想问笔记信息
     * @param id 三方请求链路Id
     * @param  itemId 课程Id
     * @param type 0课程 1小节
     * @param noduleIdOpt  小节Id（非必填）
     */
    private void synchronizeNote(String id, String itemId, String type, Optional<String> noduleIdOpt, String memberId) {
        UriComponentsBuilder builder = UriComponentsBuilder.fromHttpUrl(synchronizeNoteApi)
                .queryParam("id", id)
                .queryParam("itemid", itemId)
                .queryParam("type", type);
        noduleIdOpt.ifPresent(ew0 -> builder.queryParam("noduleid", ew0));
        Map<String, String> paramMap = ImmutableMap.of("id", id, "itemid", itemId, "type", type);
        noduleIdOpt.ifPresent(ew1 -> paramMap.put("noduleid", ew1));
        logger.info("数智导师V2：拉取课件笔记|课程笔记|猜你想问信息开始，入参{}", JSONObject.toJSONString(paramMap));
        HttpHeaders header = new HttpHeaders();
        MediaType mediaType = MediaType.parseMediaType(String.valueOf(MediaType.APPLICATION_JSON));
        header.setContentType(mediaType);
        HttpEntity<String> httpEntity = new HttpEntity<>(header);
        HttpClient httpClient = HttpClients.createDefault();
        HttpComponentsClientHttpRequestFactory requestFactory = new HttpComponentsClientHttpRequestFactory(httpClient);
        requestFactory.setConnectTimeout(8000);
        requestFactory.setReadTimeout(8000);
        requestFactory.setConnectionRequestTimeout(8000);
        restTemplate.setRequestFactory(requestFactory);
        logger.info("数智导师V2：拉取课件笔记|课程笔记|猜你想问信息API{}",JSONObject.toJSONString(String.valueOf(builder.build().encode().toUri())));
        ResponseEntity<String> response = restTemplate.exchange(String.valueOf(builder.build().encode().toUri()), HttpMethod.GET, httpEntity, String.class, paramMap);
        logger.info("数智导师V2：拉取课件笔记|课程笔记|猜你想问信息响应结果集{}",response.getBody());
        SyncDTO syncDTO = JSONObject.parseObject(response.getBody(), SyncDTO.class);
        logger.info("数智导师V2：拉取课件笔记|课程笔记|猜你想问信息结束，出参{}", JSONObject.toJSONString(syncDTO));
        this.doDistributePresetExample(syncDTO,itemId);
        this.doDistributeMainNote(syncDTO,itemId,memberId);
        this.doDistributeWareNote(syncDTO,itemId,memberId);
    }

    /**
     * 执行分发落盘任务——课程预设问题
     * @param syncDTO 九天原始报文
     * @param courseId 课程Id
     */
    private void doDistributePresetExample(SyncDTO syncDTO,String courseId){
        Optional.ofNullable(syncDTO)
                .filter(ew1->Objects.equals(200,ew1.getCode()))
                .filter(ew2->CollectionUtils.isNotEmpty(ew2.getData().getResultList()))
                .filter(ew3->!Objects.isNull(ew3.getData().getResultList().get(0)))
                .ifPresent(ew4->{
                    SyncResultDTO syncResultDTO = ew4.getData().getResultList().get(0);
                    List<SyncQuestionDTO> guessAsk = syncResultDTO.getGuessAsk();
                    logger.info("课程预设问题{}",JSONObject.toJSONString(guessAsk));
                    mentorService.synchronizePresetExample(JSONObject.toJSONString(guessAsk),courseId,ew4.getData().getType());
                });
    }

    /**
     * 执行分发落盘任务——课程笔记|课程知识点信息
     * @param syncDTO 九天原始报文
     * @param courseId 课程Id
     * @param memberId 用户Id
     */
    private void doDistributeMainNote(SyncDTO syncDTO,String courseId, String memberId){
        Optional.ofNullable(syncDTO)
                .filter(ew1->Objects.equals(200,ew1.getCode()))
                .filter(ew2->CollectionUtils.isNotEmpty(ew2.getData().getResultList()))
                .filter(ew3->!Objects.isNull(ew3.getData().getResultList().get(0)))
                .ifPresent(ew4->{
                    SyncResultDTO syncResultDTO = ew4.getData().getResultList().get(0);
                    logger.info("课程笔记|知识点信息集合{}",JSONObject.toJSONString(syncResultDTO));
                    mentorService.synchronizeMainNote(JSONObject.toJSONString(syncResultDTO),courseId,memberId);
                });
    }

    /**
     * 执行分发落盘任务——课件笔记|课件知识点信息
     * @param syncDTO 九天原始报文
     * @param courseId 课程Id
     * @param memberId 用户Id
     */
    private void doDistributeWareNote(SyncDTO syncDTO,String courseId, String memberId){
        List<ConvertDTO> convertCollect = mentorService.convertPrimaryKey(courseId);
        Optional.ofNullable(syncDTO)
                .filter(ew1->Objects.equals(200,ew1.getCode()))
                .filter(ew2->CollectionUtils.isNotEmpty(ew2.getData().getResultList()))
                .ifPresent(ew3->{
                    List<SyncResultDTO> resultCollect = ew3.getData().getResultList();
                    List<SyncResultDTO> syncResultCollect = resultCollect.subList(1, resultCollect.size());
                    syncResultCollect.forEach(ew4->{
                        String noduleId = ew4.getNoduleid();
                        String sectionId = convertCollect.get(Integer.parseInt(noduleId) - 1).getSectionId();
                        mentorService.synchronizeWareNote(JSONObject.toJSONString(ew4),courseId,sectionId,memberId);
                    });
                });
    }

    /**
     * 内部使用,手动导入接口
     */
    @RequestMapping(value = "/import-data",method = RequestMethod.POST)
    @Param(name = "fileId", type = String.class, required = true)
    @JSON("*")
    public ImmutableMap<String, String> importData(RequestContext requestContext) {
        Optional<com.zxy.product.human.entity.Attachment> attachmentOptional = fileService.get(requestContext.getString("fileId"));
        return attachmentOptional.map(attachment -> {
            try {
                Reader reader = new DefaultReader()
                        .skipRows(1)
                        //课程id
                        .setColumn(0, String.class, new RequiredValidator<>())
                        //json信息
                        .setColumn(1, String.class, new RequiredValidator<>());
                com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
                fastDFSAttachment.setPath(attachment.getPath());
                InputStream inputStream = attachmentResolver.resolveToRead(fastDFSAttachment);
                Reader.Result result = reader.read(inputStream);
                // key :课程id
                // value :大json
                logger.info("表格对象：{}",JSONObject.toJSONString(result));
                logger.info("流数据：{}",JSONObject.toJSONString(result.getCorrectRows()));
                Map<String, String> map = result.getCorrectRows()
                        .stream()
                        .collect(Collectors.toMap(
                                row -> row.get(0, String.class),
                                row -> row.get(1, String.class)
                        ));

                logger.info("表格数据：{}",JSONObject.toJSONString(map));
                map.forEach((courseId, json) -> {
                    JSONObject jsonObject = JSONObject.parseObject(json);
                    String abstractText = Optional.ofNullable(jsonObject.getJSONObject("data"))
                            .map(data -> data.getJSONArray("resultList"))
                            .filter(resultList -> !resultList.isEmpty())
                            .map(resultList -> resultList.getJSONObject(0))
                            .map(result1 -> result1.getString("abstractText"))
                            .orElse("");

                    //todo 查main
                    mentorService.findCourseMainNoteByCourseId(courseId).ifPresent(mainNote -> deleteAll(mainNote.getId()));

                    // 课程摘要
                    CourseMainNote courseMainNote = new CourseMainNote();
                    String uuid = UUID.randomUUID().toString();
                    courseMainNote.setId(uuid);
                    courseMainNote.setCreateTime(System.currentTimeMillis());
                    courseMainNote.setCourseId(courseId);
                    courseMainNote.setSummaryContent(abstractText);
                    courseMainNote.setVersion(buildVersion());
                    courseMainNote.setSubmitUserId(USER_ID);
                    courseMainNote.setSubmitTime(System.currentTimeMillis());
                    courseMainNote.setCurrentStatus(CourseMainNote.CURRENT_VERSION);
                    logger.info("课程摘要：{}",JSONObject.toJSONString(courseMainNote));

                    // 问答
                    List<AiPresetExample> aiPresetExampleList = Optional.ofNullable(jsonObject.getJSONObject("data"))
                            .map(data -> data.getJSONArray("resultList"))
                            .filter(resultList -> !resultList.isEmpty())
                            .map(resultList -> resultList.getJSONObject(0))
                            .map(result1 -> result1.getJSONArray("guessAsk"))
                            .map(guessAskList -> guessAskList.stream()
                                    .map(ew0 -> {
                                        JSONObject parseObject = JSONObject.parseObject(ew0.toString());
                                        AiPresetExample example = new AiPresetExample();
                                        example.forInsert();
                                        example.setResourceId(courseId);
                                        example.setPresetQuestion(parseObject.getString("question"));
                                        example.setPresetAnswer(parseObject.getString("answer"));
                                        example.setQuestionType(QuestionTypeEnum.General.getCode());
                                        return example;
                                    })
                                    .collect(Collectors.toList()))
                            .orElse(Collections.emptyList());
                    logger.info("问答：{}",JSONObject.toJSONString(aiPresetExampleList));

                    //知识点
                    List<CourseMainNoteVersion> courseMainNoteVersionList = Optional.ofNullable(jsonObject.getJSONObject("data"))
                            .map(data -> data.getJSONArray("resultList"))
                            .filter(resultList -> !resultList.isEmpty())
                            .map(resultList -> resultList.getJSONObject(0))
                            .map(result1 -> result1.getJSONArray("knowledgePoint"))
                            .map(knowledgePointList -> knowledgePointList.stream()
                                    .map(ew0 -> {
                                        JSONObject parseObject = JSONObject.parseObject(ew0.toString());
                                        CourseMainNoteVersion courseMainNoteVersion = new CourseMainNoteVersion();
                                        courseMainNoteVersion.forInsert();
                                        courseMainNoteVersion.setVersionMainNoteId(uuid);
                                        courseMainNoteVersion.setContent(parseObject.getString("substance"));
                                        courseMainNoteVersion.setEssential(parseObject.getString("title"));
                                        return courseMainNoteVersion;
                                    })
                                    .collect(Collectors.toList()))
                            .orElse(Collections.emptyList());
                    logger.info("知识点：{}",JSONObject.toJSONString(courseMainNoteVersionList));

                    mentorService.insertAiPresetExampleList(aiPresetExampleList);
                    mentorService.insertCourseMainNote(courseMainNote);
                    mentorService.insertCourseMainNoteVersionList(courseMainNoteVersionList);
                });

                return ImmutableMap.<String, String>builder().put("success", "导入成功").build();
            } catch (Exception e) {
                logger.error("手动接口异常", e);
                return ImmutableMap.<String, String>builder().put("error", e.getMessage()).build();
            }
        }).orElseGet(() -> ImmutableMap.<String, String>builder().put("error", "文件为空").build());
    }

    private void deleteAll(String id) {
        mentorService.deleteCourseMainNote(id);
        mentorService.deleteCourseMainNoteVersion(id);
     //   mentorService.deleteAiPresetExample(id);
    }

    private String buildVersion(){
        LocalDateTime now = LocalDateTime.now();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMddHHmmss");
        String suffix = now.format(formatter);
        int prefix = new Random().nextInt(10000);
        return suffix+"-"+prefix;
    }

    /**
     * 根据专题id查询是否有发布的摘要
     */
    @RequestMapping(value = "/subject-summary-status",method = RequestMethod.GET)
    @Param(name = "subjectId", required = true)
    @JSON("status")
    public ImmutableMap<String, Object> getSubjectSummaryStatus(RequestContext requestContext){
        return ImmutableMap.of("status", HAS_RELEASE.equals(mentorService.getSubjectSummaryStatus(requestContext.getString("subjectId"))));
    }

    /**
     * 课程ids，查询是否有摘要
     */
    @RequestMapping(value = "/get-subject-summary", method = RequestMethod.POST)
    @Param(name = "courseIds", required = true)
    @JSON("status")
    public ImmutableMap<String, Object> getSubjectSummary(RequestContext requestContext){
        Integer count = mentorService.getSubjectSummary(requestContext.getString("courseIds"));
        return ImmutableMap.of("status", count != 0);
    }

}
