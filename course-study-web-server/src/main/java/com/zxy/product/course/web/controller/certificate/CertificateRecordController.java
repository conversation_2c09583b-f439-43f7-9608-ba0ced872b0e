package com.zxy.product.course.web.controller.certificate;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.Validator;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.office.excel.support.DefaultReader;
import com.zxy.common.office.excel.support.validator.RequiredValidator;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.certificate.CertificateNormalCodeGenerator;
import com.zxy.product.course.api.certificate.CertificateRecordService;
import com.zxy.product.course.api.remodeling.RemodelingTrainService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.jooq.tables.pojos.MemberEntity;
import com.zxy.product.course.jooq.tables.pojos.RemodelingRoleDetailEntity;
import com.zxy.product.course.util.DesensitizationUtil;
import com.zxy.product.course.web.util.BrowserUtil;
import com.zxy.product.course.web.util.DateUtil;
import com.zxy.product.course.web.util.UploadUtil;
import com.zxy.product.exam.api.ExamService;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.entity.Attachment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Controller;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static com.zxy.product.system.util.StringUtils.dateString2OptionalLong;
import static com.zxy.product.system.util.StringUtils.dateString2OptionalLongMore;

@Controller
@RequestMapping("/certificate-record")
public class CertificateRecordController {
    private static final Logger logger = LoggerFactory.getLogger(CertificateRecordController.class);

    private CertificateRecordService certificateRecordService;
    private FileService fileService;
    private AttachmentResolver attachmentResolver;

    private CertificateNormalCodeGenerator normalCodeGenerator;
    private RemodelingTrainService remodelingTrainService;
    private ExamService examService;
    private MessageSender messageSender;

    @Autowired
    public void setExamService(ExamService examService) {
        this.examService = examService;
    }

    @Autowired
    public void setNormalCodeGenerator(CertificateNormalCodeGenerator normalCodeGenerator) {
        this.normalCodeGenerator = normalCodeGenerator;
    }

    @Autowired
    public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
        this.attachmentResolver = attachmentResolver;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setCertificateRecordService(CertificateRecordService certificateRecordService) {
        this.certificateRecordService = certificateRecordService;
    }
    @Autowired
    public void setRemodelingTrainService(RemodelingTrainService remodelingTrainService) {
        this.remodelingTrainService = remodelingTrainService;
    }
    /**
     * 查询获得证书的记录信息
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "businessId", required = true)
    @JSON("*.*")
    public Map<String, Object> findCertificateInfo(RequestContext context, Subject<Member> subject){
        return certificateRecordService.getRecordByBusinessIdAndMemberId(context.getString("businessId"), subject.getCurrentUserId());
    }

    @RequestMapping(method = RequestMethod.GET, value = "/find-template-type")
    @Permitted
    @Param(name = "certificateId", type = String.class, required = true)
    @JSON("*.*")
    public Map<String, Object> findCertificateTemplateType(RequestContext context) {
        String certificateId = context.getString("certificateId");
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("type", 2);
        // 需要特殊处理的证书id
        if (certificateId.equals("89fd0b7f-462a-4d0e-aee5-0511ed3b2e16")) {
            resultMap.put("type", 1);
        }
        return resultMap;
    }

    /**
     * 是否拥有证书
     * @return
     */
    @RequestMapping(value = "/whether-own-certificate",method = RequestMethod.GET)
    @Param(name = "businessId", type = String.class, required = true)
    @JSON("*.*")
    public ImmutableMap<String,Object> whetherHavCertificate(RequestContext requestContext,Subject<Member> subject){
        String businessId = requestContext.getString("businessId");
        return ImmutableMap.of("isOk",certificateRecordService.whetherHavCertificate(businessId,subject.getCurrentUserId()));
    }

    /**
     * 获得证书排名和领先百分比
     * @return
     */
    @RequestMapping(value = "/get-certificate-order", method = RequestMethod.GET)
    @Param(name = "businessId", type = String.class,required = true)
    @JSON("*.*")
    public Map<String, Object> getCertificateOrder(RequestContext context,Subject<Member> subject){
        return certificateRecordService.getCertificateOrder(
                context.getString("businessId"),
                subject.getCurrentUser());
    }

    /** 下载导入证书模板 */
    @RequestMapping(value = "/export" , method = RequestMethod.GET)
    @Param()
    @JSON("*")
    public void exportTemplate(RequestContext requestContext) throws IOException {
        HttpServletResponse response = BrowserUtil.fileDownloadResponse(requestContext, "重塑专区手动发放证书模板.xlsx");
        Writer writer = new ExcelWriter();
        // 导入模板
        writer.sheet("重塑专区手动发放证书模板", new ArrayList<>())
                .field(CertificateRecord.TEMPLATE_MEMBER_NAME_CN, null)
                .field(CertificateRecord.TEMPLATE_SUBJECT_CODE_CN, null);
        writer.write(response.getOutputStream());
    }

    /** 下载导入证书模板 */
    @RequestMapping(value = "/export-qj" , method = RequestMethod.GET)
    @Param()
    @JSON("*")
    public void exportTemplateQJ(RequestContext requestContext) throws IOException {
        HttpServletResponse response = BrowserUtil.fileDownloadResponse(requestContext, "强基专区手动发放证书模板.xlsx");
        Writer writer = new ExcelWriter();
        // 导入模板
        writer.sheet("强基专区手动发放证书模板", new ArrayList<>())
                .field(CertificateRecord.TEMPLATE_MEMBER_NAME_CN, null)
                .field(CertificateRecord.TEMPLATE_SUBJECT_CODE_CN, null)
                .field(CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_CN, null);
        writer.write(response.getOutputStream());
    }

    /** 证书发放导入*/
    @RequestMapping(value = "/import", method = RequestMethod.POST)
    @Param(name="fileId", required=true)
    @JSON("successCount,failCount,data,errorFileId")
    @JSON("errors.(column,row)")
    @JSON("errors.code.(code)")
    @Permitted
    @Audit(module = "运营管理", subModule = "重塑证书发放", action = Audit.Action.IMPORT, fisrtAction = "导入证书", desc = "导入重塑专区证书")
    public Map<String, Object> importData(RequestContext requestContext) {
        Optional<Attachment> attachment = fileService.get(requestContext.getString("fileId"));
        Map<String, Object> m = attachment.map(t -> {
            Reader reader = new DefaultReader()
                    .skipRows(0)
                    //网大员工编号
                    .setColumn(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class, new RequiredValidator<>())
                    //专题编码
                    .setColumn(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class, new RequiredValidator<>());

            com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
            fastDFSAttachment.setPath(t.getPath());
            InputStream inputStream = attachmentResolver.resolveToRead(fastDFSAttachment);
            Map<String, Object> resultMap = new HashMap<>();
            try {
                Reader.Result result = reader.read(inputStream);

                // 判断是否超出每次允许导入的最大数量:暂定5000
                if ((result.getCorrectRows().size() + result.getErrorRows().size()) >= OfflineCourseQuestionnaireChapter.TEMPLATE_DATA_LIMIT) {
                    throw new UnprocessableException(ErrorCode.DJYPExceedMax);
                }

                if (!result.isCellMatched()) { // 那么就是模板不匹配
                    throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
                }

                if (result.getCorrectRows().size() == 1 && result.getErrorRows().size() == 0){
                    throw new UnprocessableException(ErrorCode.ImportNullFile);
                }

                if (CollectionUtils.isNotEmpty(result.getCorrectRows())
                        && result.getCorrectRows().get(0)!=null
                        && result.getCorrectRows().get(0).get(0,String.class)!=null
                        && !CertificateRecord.TEMPLATE_MEMBER_NAME_CN.equals(result.getCorrectRows().get(0).get(0,String.class))) {
                    throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
                }
                if (CollectionUtils.isNotEmpty(result.getCorrectRows())
                        && result.getCorrectRows().get(0)!=null
                        && result.getCorrectRows().get(0).get(1,String.class)!=null
                        && !CertificateRecord.TEMPLATE_SUBJECT_CODE_CN.equals(result.getCorrectRows().get(0).get(1,String.class))) {
                    throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
                }

                // 将excel中的 数据先读取出来
                List<CertificateRecord> tempLists = readExcelResult(result);
                // 以导入填写的员工编号为基础条件查询人员
                List<String> memberNames = tempLists.stream().map(l -> l.getMemberName()).collect(Collectors.toList());
                List<Member> members = certificateRecordService.findByMemberNames(memberNames);
                List<String> memberIds = members.stream().map(me -> me.getId()).collect(Collectors.toList());
                List<CertificateRecord> existsRecords = certificateRecordService.findByMemberIds(memberIds);
                // 准备k-v数据用于校验，避免校验的时候循环读取集合
                Map<String, Member> memberMap = members.stream().filter(e -> e.getName() != null).collect(Collectors.toMap(Member::getName, e -> e, (t1, t2) -> t2));
                Map<String, List<CertificateRecord>> existsRecordsMap = existsRecords.stream().collect(Collectors.groupingBy(e -> getExistsKey(e.getMemberId(), e.getBusinessId())));

                List<String> codes = tempLists.stream().map(l -> l.getSubjectCode()).collect(Collectors.toList());
                // 查询专题的证书信息
                List<CourseInfo> courseInfoList = certificateRecordService.findSubjectCertificateByCodes(codes);
                Map<String, CourseInfo> courseInfoMap = courseInfoList.stream().filter(e -> e.getCode() != null).collect(Collectors.toMap(CourseInfo::getCode, e -> e, (t1, t2) -> t2));
                // 查询专题和学员已完成的数据
                List<String> courseIds = courseInfoList.stream().distinct().filter(e -> e.getCertificateId() != null).map(c -> c.getId()).collect(Collectors.toList());
                List<CourseStudyProgress> finishCourses = certificateRecordService.findFinishSubject(memberIds, courseIds);
                Map<String, List<CourseStudyProgress>> finishCoursesMap = finishCourses.stream().collect(Collectors.groupingBy(e -> getExistsKey(e.getMemberId(), e.getCourseId())));
                // 根据角色的等级处理证书的颁发时间（初级用导入时间，中高级用固定时间）
                List<RemodelingRoleDetail> remodelingRoleDetails = remodelingTrainService.findRoleDetailList(RemodelingRoleDetail.SORT_HOME_PAGE)
                        .values().stream().collect(ArrayList::new, ArrayList::addAll, ArrayList::addAll);
                Map<String, Integer> remodelingRoleLevelMap = remodelingRoleDetails.stream()
                        .collect(Collectors.toMap(RemodelingRoleDetailEntity::getSubjectId, RemodelingRoleDetailEntity::getRoleLevel, (t1, t2) -> t2));

                // 验证不通过的数据集合
                List<CertificateRecord> errorList = getResultErrors(result);
                List<CertificateRecord> errorCertificateRecords;
                // 验证通过需要新增的数据集合
                List<CertificateRecord> correctInsertList = new ArrayList<>();

                // 数据验证：先准备好需要的数据，纯JAVA逻辑校验业务数据，循环中不与数据库交互
                tempLists.forEach(tl ->
                        validateBusinessData(tl, memberMap, courseInfoMap, existsRecordsMap,finishCoursesMap, correctInsertList, errorList,remodelingRoleLevelMap)
                );
                // 批量新增成功数据
                certificateRecordService.insertRecordList(correctInsertList);

                // 返回数据
                resultMap.put("successCount", correctInsertList.size()); // 成功数量
                resultMap.put("failCount", errorList.size()); // 失败数量
                errorCertificateRecords = CollectionUtils.isNotEmpty(errorList)?errorList.stream().sorted(Comparator.comparing(CertificateRecord::getRow)).collect(Collectors.toList()):errorList;
                try {
                    // 失败记录下载文件id
                    resultMap.put("errorFileId", createErrorTempFile(errorCertificateRecords));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                resultMap.put("errors", createErrorList(errorCertificateRecords)); // 错误信息：行-列-错误编码
            } catch (IOException e) {
                e.printStackTrace();
                throw new UnprocessableException(ErrorCode.EXCEL_NOTSUPPORT); // 模板错误
            }
            return resultMap;
        }).get();

        return m;
    }

    /** 强基证书发放导入*/
    @RequestMapping(value = "/import-qj", method = RequestMethod.POST)
    @Param(name="fileId", required=true)
    @JSON("successCount,failCount,data,errorFileId")
    @JSON("errors.(column,row)")
    @JSON("errors.code.(code)")
    @Permitted
    @Audit(module = "运营管理", subModule = "强基证书发放", action = Audit.Action.IMPORT, fisrtAction = "导入证书", desc = "导入强基专区证书")
    public Map<String, Object> importDataQJ(RequestContext requestContext) {
        // 定义一个集合用来存放本次导入的所有的证书编号
        List<String> numList = new ArrayList<String>();
        Optional<Attachment> attachment = fileService.get(requestContext.getString("fileId"));
        Map<String, Object> m = attachment.map(t -> {
            Reader reader = new DefaultReader()
                    .skipRows(0)
                    //网大员工编号
                    .setColumn(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class, new RequiredValidator<>())
                    //专题编码
                    .setColumn(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class, new RequiredValidator<>())
                    //网大员工姓名
                    .setColumn(CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_COLUMN, String.class, new RequiredValidator<>());

            com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
            fastDFSAttachment.setPath(t.getPath());
            InputStream inputStream = attachmentResolver.resolveToRead(fastDFSAttachment);
            Map<String, Object> resultMap = new HashMap<>();
            try {
                Reader.Result result = reader.read(inputStream);

                // 判断是否超出每次允许导入的最大数量:暂定5000
                if ((result.getCorrectRows().size() + result.getErrorRows().size()) >= OfflineCourseQuestionnaireChapter.TEMPLATE_DATA_LIMIT) {
                    throw new UnprocessableException(ErrorCode.DJYPExceedMax);
                }

                if (!result.isCellMatched()) { // 那么就是模板不匹配
                    throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
                }

                if (result.getCorrectRows().size() == 1 && result.getErrorRows().size() == 0){
                    throw new UnprocessableException(ErrorCode.ImportNullFile);
                }

                if (CollectionUtils.isNotEmpty(result.getCorrectRows())
                        && result.getCorrectRows().get(0)!=null
                        && result.getCorrectRows().get(0).get(0,String.class)!=null
                        && !CertificateRecord.TEMPLATE_MEMBER_NAME_CN.equals(result.getCorrectRows().get(0).get(0,String.class))) {
                    throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
                }
                if (CollectionUtils.isNotEmpty(result.getCorrectRows())
                        && result.getCorrectRows().get(0)!=null
                        && result.getCorrectRows().get(0).get(1,String.class)!=null
                        && !CertificateRecord.TEMPLATE_SUBJECT_CODE_CN.equals(result.getCorrectRows().get(0).get(1,String.class))) {
                    throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
                }
                if (CollectionUtils.isNotEmpty(result.getCorrectRows())
                        && result.getCorrectRows().get(0)!=null
                        && result.getCorrectRows().get(0).get(2,String.class)!=null
                        && !CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_CN.equals(result.getCorrectRows().get(0).get(2,String.class))) {
                    throw new UnprocessableException(ErrorCode.EXCEL_ERROR);
                }

                // 将excel中的 数据先读取出来
                List<CertificateRecord> tempLists = readExcelResultQJ(result);
                // 以导入填写的员工编号为基础条件查询人员
                List<String> memberNames = tempLists.stream().map(l -> l.getMemberName()).collect(Collectors.toList());
                List<Member> members = certificateRecordService.findByMemberNames(memberNames);
                List<String> memberIds = members.stream().map(me -> me.getId()).collect(Collectors.toList());
                List<CertificateRecord> existsRecords = certificateRecordService.findByMemberIds(memberIds);
                // 准备k-v数据用于校验，避免校验的时候循环读取集合
                Map<String, Member> memberMap = members.stream().filter(e -> e.getName() != null).collect(Collectors.toMap(Member::getName, e -> e, (t1, t2) -> t2));
                Map<String, List<CertificateRecord>> existsRecordsMap = existsRecords.stream().collect(Collectors.groupingBy(e -> getExistsKey(e.getMemberId(), e.getBusinessId())));

                List<String> codes = tempLists.stream().map(l -> l.getSubjectCode()).collect(Collectors.toList());
                // 查询专题的证书信息
                List<CourseInfo> courseInfoList = certificateRecordService.findSubjectCertificateByCodes(codes);
                Map<String, CourseInfo> courseInfoMap = courseInfoList.stream().filter(e -> e.getCode() != null).collect(Collectors.toMap(CourseInfo::getCode, e -> e, (t1, t2) -> t2));
                // 查询专题受众
                List<String> courseIds = courseInfoList.stream().distinct().filter(e -> e.getCertificateId() != null).map(c -> c.getId()).collect(Collectors.toList());
                List<AudienceObject> audienceObjects = certificateRecordService.findByBusinessByMember(memberIds, courseIds, 2);
                Map<String, List<AudienceObject>> audienceObjectMap = audienceObjects.stream().collect(Collectors.groupingBy(e -> getExistsKey(e.getMemberId(), e.getBusinessId())));


                // 验证不通过的数据集合
                List<CertificateRecord> errorList = getResultErrorsQJ(result);
                List<CertificateRecord> errorCertificateRecords= new ArrayList<>();
                // 验证通过需要新增的数据集合
                List<CertificateRecord> correctInsertList = new ArrayList<>();

                // 数据验证：先准备好需要的数据，纯JAVA逻辑校验业务数据，循环中不与数据库交互
                tempLists.forEach(tl -> {
                    validateBusinessDataQJ(tl, memberMap, courseInfoMap, existsRecordsMap, correctInsertList, errorList, audienceObjectMap);
                });
                // 批量新增成功数据
                certificateRecordService.insertRecordList(correctInsertList);

                // 返回数据
                resultMap.put("successCount", correctInsertList.size()); // 成功数量
                resultMap.put("failCount", errorList.size()); // 失败数量
                errorCertificateRecords = CollectionUtils.isNotEmpty(errorList)?errorList.stream().sorted(Comparator.comparing(CertificateRecord::getRow)).collect(Collectors.toList()):errorList;
                try {
                    // 失败记录下载文件id
                    resultMap.put("errorFileId", createErrorTempFileQJ(errorCertificateRecords));
                } catch (IOException e) {
                    e.printStackTrace();
                }
                resultMap.put("errors", createErrorList(errorCertificateRecords)); // 错误信息：行-列-错误编码
            } catch (IOException e) {
                e.printStackTrace();
                throw new UnprocessableException(ErrorCode.EXCEL_NOTSUPPORT); // 模板错误
            }
            return resultMap;
        }).get();

        return m;
    }

    /** 错误信息：行-列-错误编码 */
    private List<Map<String, Object>> createErrorList(List<CertificateRecord> errorList) {
        List<Map<String, Object>> errors = new ArrayList<>();
        errorList.forEach(error -> {
            for(RowError e : error.getErrors() ){
                Map<String, Object> map = new HashMap<>();
                map.put("row", e.getRow());
                map.put("column", e.getColumn());
                map.put("code", e.getCode() == null ? "" : e.getCode());
                errors.add(map);
            }
        });
        return errors;
    }

    /** 生成导入的错误临时文件 */
    private String createErrorTempFile(List<CertificateRecord> errorList) throws IOException {
        if (CollectionUtils.isNotEmpty(errorList)) {
            Writer writer = new ExcelWriter();
            // 错误数据
            writer.sheet("导入有误的数据", errorList)
                    .field(CertificateRecord.TEMPLATE_MEMBER_NAME_CN, CertificateRecord::getMemberName)
                    .field(CertificateRecord.TEMPLATE_SUBJECT_CODE_CN, CertificateRecord::getSubjectCode);
            // 将临时文件写入文件系统
            return uploadTempFile(writer);
        }
        return null;
    }

    /** 生成导入的错误临时文件 */
    private String createErrorTempFileQJ(List<CertificateRecord> errorList) throws IOException {
        if (CollectionUtils.isNotEmpty(errorList)) {
            Writer writer = new ExcelWriter();
            // 错误数据
            writer.sheet("导入有误的数据", errorList)
                    .field(CertificateRecord.TEMPLATE_MEMBER_NAME_CN, CertificateRecord::getMemberName)
                    .field(CertificateRecord.TEMPLATE_SUBJECT_CODE_CN, CertificateRecord::getSubjectCode)
                    .field(CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_CN, CertificateRecord::getMemberFullName);
            // 将临时文件写入文件系统
            return uploadTempFile(writer);
        }
        return null;
    }
    /** 输出流转文件，再转输入流上传到文件系统，返回文件id  */
    private String uploadTempFile(Writer writer) throws IOException {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        writer.write(os);
        InputStream is = new ByteArrayInputStream(os.toByteArray());
        MultipartFile mfile = UploadUtil.transferTo(is, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", "导出错误数据.xlsx", is.available());
        com.zxy.common.restful.multipart.Attachment restfulAttachment = attachmentResolver.store(null, mfile, Optional.empty());
        String[] filename = new String[]{ restfulAttachment.getFilename() };
        String[] contentType = new String[]{ restfulAttachment.getContentType() };
        String[] path = new String[]{ restfulAttachment.getPath() };
        Long[] size = new Long[]{ restfulAttachment.getSize() };
        List<Attachment> result = fileService.insert(filename, contentType, new String[]{"xlsx"}, path, size);
        return result.get(0).getId();
    }

    /**
     * * 业务逻辑上的数据验证
     * 1 用户不存在
     * 2 专题不存在
     * 3 专题未配置证书
     * 4 该专题未完成学习
     * 5 证书重复
     */
    private void validateBusinessData(
            CertificateRecord tl,
            Map<String, Member> memberMap,
            Map<String, CourseInfo> courseInfoMap,
            Map<String, List<CertificateRecord>> existsRecordsMap,
            Map<String, List<CourseStudyProgress>> finishCoursesMap,
            List<CertificateRecord> correctInsertList,
            List<CertificateRecord> errorList,
            Map<String, Integer> remodelingRoleLevelMap){
        List<RowError> errors = tl.getErrors();
        String memberName = tl.getMemberName();
        String code = tl.getSubjectCode();
        // 通过员工编号查找员工
        Member targetMember = memberMap.getOrDefault(memberName,null);
        CourseInfo targetCourseInfo = courseInfoMap.getOrDefault(code,null);

        if ( targetMember == null ) {
            // 员工不存在
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, ErrorCode.CertificateMemberNotExists.getCode()));
        } else if (!Arrays.asList(CourseInfo.IMPORT_CERTIFICATE_SUBJECT_CODES).contains(code)) {
            // 专题不在需要手动发证的专题集里
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_SUBJECT_CODE, ErrorCode.SubjectNotExists.getCode()));
        }  else if (targetCourseInfo == null || targetCourseInfo.getCertificateId() == null || targetCourseInfo.getId() == null) {
            // 专题证书不存在
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_SUBJECT_CODE, ErrorCode.SubjectNotHaveCertificate.getCode()));
        }  else if (!CollectionUtils.isNotEmpty(finishCoursesMap.get(getExistsKey(targetMember.getId(), targetCourseInfo.getId())))) {
            // 这个学员该专题未完成
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_SUBJECT_CODE, ErrorCode.SubjectNotFinish.getCode()));
        }  else if (CollectionUtils.isNotEmpty(existsRecordsMap.get(getExistsKey(targetMember.getId(), targetCourseInfo.getId())))) {
            // 证书重复
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_SUBJECT_CODE, ErrorCode.CertificateHaveExists.getCode()));
        }

        if (errors.size() == 0) {
            Long finishTime = finishCoursesMap.get(getExistsKey(targetMember.getId(), targetCourseInfo.getId())).get(0).getFinishTime();
            // 该条数据验证通过
            tl.setBusinessId(targetCourseInfo.getId());
            tl.setMemberId(targetMember.getId());
            tl.setFinishTime(finishTime == null ? System.currentTimeMillis() : finishTime);
            tl.setType(CertificateRecord.ISSUE_CERTIFICATE_TYPE_QUALIFIED);
            tl.setIssueTime("二〇二四年五月三十一日");
            tl.setBusinessType(CertificateRecord.BUSINESS_TYPE_SUBJECT);
            tl.setIssueAgency("中国移动网上人才发展中心");
            try {
                String num = normalCodeGenerator.getCodeForNormalSubject(CertificateRecord.BUSINESS_TYPE_SUBJECT, BusinessCertificate.CERTIFICATE_NUMBER_PREFIX_SUBJECT, com.zxy.product.course.util.StringUtils.stampToDate(finishTime, 0));
                tl.setCertificateNumber(num);
            } catch (Exception e) {
                logger.error("手动导入批量生成证书编码异常，需重新处理， subjecId={}, memberId={}", targetCourseInfo.getId(), targetMember.getId());
                e.printStackTrace();
            }
            tl.setAccessType(CertificateRecord.ACCESS_TYPE_IMPORT);
            correctInsertList.add(tl);
        } else {
            // 该条数据验证失败
            errorList.add(tl);
        }
    }

    /**
     * * 业务逻辑上的数据验证
     * 1 用户不存在
     * 2 专题不存在
     * 3 专题未配置证书
     * 4 该专题未完成学习
     * 5 证书重复
     */
    private void validateBusinessDataQJ(
            CertificateRecord tl,
            Map<String, Member> memberMap,
            Map<String, CourseInfo> courseInfoMap,
            Map<String, List<CertificateRecord>> existsRecordsMap,
            List<CertificateRecord> correctInsertList,
            List<CertificateRecord> errorList,
            Map<String, List<AudienceObject>> audienceObjectMap){
        List<RowError> errors = tl.getErrors();
        String memberName = tl.getMemberName();
        String code = tl.getSubjectCode();
        String memberFullName = tl.getMemberFullName();
        // 通过员工编号查找员工
        Member targetMember = memberMap.getOrDefault(memberName,null);
        CourseInfo targetCourseInfo = courseInfoMap.getOrDefault(code,null);

        if ( targetMember == null ) {
            // 员工不存在
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, ErrorCode.CertificateMemberNotExists.getCode()));
        } else if (!memberFullName.equals(targetMember.getFullName())) {
            // 员工姓名或者编码填写错误
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_COLUMN, ErrorCode.NameOrFullNameError.getCode()));
        } else if (!Arrays.asList(CourseInfo.IMPORT_CERTIFICATE_SUBJECT_CODES_QJ).contains(code)) {
            // 专题不在需要手动发证的专题集里
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_SUBJECT_CODE, ErrorCode.SubjectNotExists.getCode()));
        }  else if (targetCourseInfo == null || targetCourseInfo.getCertificateId() == null || targetCourseInfo.getId() == null) {
            // 专题证书不存在
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_SUBJECT_CODE, ErrorCode.SubjectNotHaveCertificate.getCode()));
        }  else if (CollectionUtils.isNotEmpty(existsRecordsMap.get(getExistsKey(targetMember.getId(), targetCourseInfo.getId())))) {
            // 证书重复
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_SUBJECT_CODE, ErrorCode.CertificateHaveExists.getCode()));
        } else if (!CollectionUtils.isNotEmpty(audienceObjectMap.get(getExistsKey(targetMember.getId(), targetCourseInfo.getId())))){
            // 不为该专题编码的受众
            errors.add(new RowError(tl.getRow(), CertificateRecord.TEMPLATE_SUBJECT_CODE, ErrorCode.NotForAFeatureAudience.getCode()));
        }

        if (errors.size() == 0) {
            long timeMillis = System.currentTimeMillis();
            // 该条数据验证通过
            tl.setBusinessId(targetCourseInfo.getId());
            tl.setMemberId(targetMember.getId());
            tl.setFinishTime(timeMillis);
            tl.setType(CertificateRecord.ISSUE_CERTIFICATE_TYPE_QUALIFIED);
            tl.setIssueTime(com.zxy.product.course.util.StringUtils.stringDate(System.currentTimeMillis()));
            tl.setBusinessType(CertificateRecord.BUSINESS_TYPE_SUBJECT);
            tl.setIssueAgency("中国移动网上人才发展中心");
            try {
                String num = normalCodeGenerator.getCodeForNormalSubject(CertificateRecord.BUSINESS_TYPE_SUBJECT, BusinessCertificate.CERTIFICATE_NUMBER_PREFIX_SUBJECT, com.zxy.product.course.util.StringUtils.stampToDate(timeMillis, 0));
                tl.setCertificateNumber(num);
            } catch (Exception e) {
                logger.error("手动导入批量生成证书编码异常，需重新处理， subjecId={}, memberId={}", targetCourseInfo.getId(), targetMember.getId());
                e.printStackTrace();
            }
            tl.setAccessType(CertificateRecord.ACCESS_TYPE_IMPORT);
            correctInsertList.add(tl);
        } else {
            // 该条数据验证失败
            errorList.add(tl);
        }
    }

    /** 将excel数据筛选后读取到List */
    private List<CertificateRecord> readExcelResult(Reader.Result result) {
        List<CertificateRecord> records = result.getCorrectRows().stream().skip(1).map(row ->{
            CertificateRecord record = new CertificateRecord();
            record.forInsert();
            record.setRow(row.getIndex());
            // 员工编号
            record.setMemberName(row.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class) != null
                    ? row.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class).trim()
                    : row.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class));
            // 专题编码
            record.setSubjectCode(row.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class) != null
                    ? row.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class).trim()
                    : row.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class));
            return record;
        }).collect(Collectors.toList());

        // 时间处理，保证导入顺序一致
        Long currentTimeMillis = System.currentTimeMillis();
        for (CertificateRecord record : records) {
            record.setCreateTime(currentTimeMillis --);
        }
        return records;
    }

    /** 将excel数据筛选后读取到List */
    private List<CertificateRecord> readExcelResultQJ(Reader.Result result) {
        List<CertificateRecord> records = result.getCorrectRows().stream().skip(1).map(row ->{
            CertificateRecord record = new CertificateRecord();
            record.forInsert();
            record.setRow(row.getIndex());
            // 员工编号
            record.setMemberName(row.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class) != null
                    ? row.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class).trim()
                    : row.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class));
            // 专题编码
            record.setSubjectCode(row.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class) != null
                    ? row.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class).trim()
                    : row.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class));
            // 员工姓名
            record.setMemberFullName(row.get(CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_COLUMN, String.class) != null
                    ? row.get(CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_COLUMN, String.class).trim()
                    : row.get(CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_COLUMN, String.class));
            return record;
        }).collect(Collectors.toList());

        // 时间处理，保证导入顺序一致
        Long currentTimeMillis = System.currentTimeMillis();
        for (CertificateRecord record : records) {
            record.setCreateTime(currentTimeMillis --);
        }
        return records;
    }

    /** 用来判断已存在的键 */
    private String getExistsKey(String memberId, String businessId) {
        return memberId + "-" + businessId;
    }

    /** reader校验规则中的校验失败记录 */
    private List<CertificateRecord> getResultErrors(Reader.Result result) {
        List<CertificateRecord> list = new ArrayList<>();
        List<Reader.Row> errorRows = result.getErrorRows();
        List<Validator.DataError> errors = result.getErrors();
        Map<Integer, List<Validator.DataError>> rowErrorsMap = errors.stream().collect(Collectors.groupingBy(e -> e.getRow()));
        if(errorRows != null && errorRows.size() > 0){
            for(int i = 0; i < errorRows.size(); i++ ){
                CertificateRecord record = new CertificateRecord();
                // 错误记录
                Reader.Row item = errorRows.get(i);
                List<Validator.DataError> dataErrors = rowErrorsMap.get(item.getIndex());
                List<RowError> rowErrors = dataErrors.stream().map(dataError ->{
                    RowError error = new RowError();
                    if(!StringUtils.isEmpty(dataError.getCode())){
                        error.setCode(dataError.getCode().getCode());
                    }
                    if(dataError.getColumn() !=-1){
                        error.setColumn(dataError.getColumn());
                    }
                    if(dataError.getRow() !=-1){
                        error.setRow(dataError.getRow());
                    }
                    return error;
                }).collect(Collectors.toList());

                record.setErrors(rowErrors);
                record.setRow(item.getIndex());
                record.setMemberName(item.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class) != null
                        ? item.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class).trim()
                        : item.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class));
                record.setSubjectCode(item.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class) != null
                        ? item.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class).trim()
                        : item.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class));
                list.add(record);
            }
        }
        return list;
    }

    /** reader校验规则中的校验失败记录 */
    private List<CertificateRecord> getResultErrorsQJ(Reader.Result result) {
        List<CertificateRecord> list = new ArrayList<>();
        List<Reader.Row> errorRows = result.getErrorRows();
        List<Validator.DataError> errors = result.getErrors();
        Map<Integer, List<Validator.DataError>> rowErrorsMap = errors.stream().collect(Collectors.groupingBy(e -> e.getRow()));
        if(errorRows != null && errorRows.size() > 0){
            for(int i = 0; i < errorRows.size(); i++ ){
                CertificateRecord record = new CertificateRecord();
                // 错误记录
                Reader.Row item = errorRows.get(i);
                List<Validator.DataError> dataErrors = rowErrorsMap.get(item.getIndex());
                List<RowError> rowErrors = dataErrors.stream().map(dataError ->{
                    RowError error = new RowError();
                    if(!StringUtils.isEmpty(dataError.getCode())){
                        error.setCode(dataError.getCode().getCode());
                    }
                    if(dataError.getColumn() !=-1){
                        error.setColumn(dataError.getColumn());
                    }
                    if(dataError.getRow() !=-1){
                        error.setRow(dataError.getRow());
                    }
                    return error;
                }).collect(Collectors.toList());

                record.setErrors(rowErrors);
                record.setRow(item.getIndex());
                record.setMemberName(item.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class) != null
                        ? item.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class).trim()
                        : item.get(CertificateRecord.TEMPLATE_MEMBER_NAME_COLUMN, String.class));
                record.setSubjectCode(item.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class) != null
                        ? item.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class).trim()
                        : item.get(CertificateRecord.TEMPLATE_SUBJECT_CODE, String.class));
                record.setMemberFullName(item.get(CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_COLUMN, String.class) != null
                        ? item.get(CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_COLUMN, String.class).trim()
                        : item.get(CertificateRecord.TEMPLATE_MEMBER_FULL_NAME_COLUMN, String.class));
                list.add(record);
            }
        }
        return list;
    }

    @RequestMapping(method = RequestMethod.GET, value = "/list")
    @Permitted(perms = {"operation/remodel-certificate","operation/manual-certificate"})
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "fullName") // 姓名
    @Param(name = "name") // 员工编号
    @Param(name = "courseName") // 专题名称
    @Param(name = "courseCode") // 专题编码
    @Param(name = "type", type = Integer.class) // 1强基 其他重塑
    @Param(name = "createBeginTime")
    @Param(name = "createEndTime")
    @JSON("recordCount")
    @JSON("items.(id,createTime)")
    @JSON("items.member.(name,fullName)")
    @JSON("items.courseInfo.(name,code)")
    public PagedResult<CertificateRecord> findSelect(RequestContext context) {

        int type = 0;
        if (context.getOptionalInteger("type").isPresent() && context.getOptionalInteger("type").get() == 1){
            type = 1;
        }

        return certificateRecordService.findSelect(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptionalString("fullName"),
                context.getOptionalString("name"),
                context.getOptionalString("courseName"),
                context.getOptionalString("courseCode"),
                type,
                dateString2OptionalLong(context.getOptionalString("createBeginTime")),
                dateString2OptionalLongMore(context.getOptionalString("createEndTime"))
        );
    }

    @Permitted
    @Param(name = "fullName") // 姓名
    @Param(name = "name") // 员工编号
    @Param(name = "courseName") // 专题名称
    @Param(name = "courseCode") // 专题编码
    @Param(name = "createBeginTime")
    @Param(name = "createEndTime")
    @RequestMapping(method = RequestMethod.GET, value = "/export-remodeling-certificate")
    public void exportRemodelingCertificateIn(RequestContext context) throws Exception {
        // 1. 调用查询的接口
        List<CertificateRecord> certificateRecordList = certificateRecordService.findSelect(
                context.getOptionalString("fullName"),
                context.getOptionalString("name"),
                context.getOptionalString("courseName"),
                context.getOptionalString("courseCode"),
                dateString2OptionalLong(context.getOptionalString("createBeginTime")),
                dateString2OptionalLongMore(context.getOptionalString("createEndTime"))).getItems();
        // 2. 生成到出的Excel
        HttpServletResponse response = context.getResponse();
        String title = "重塑证书发放";
        String attachmentName = title + ".xlsx";
        if (BrowserUtil.isMSBrowser(context.getRequest().getHeader(HttpHeaders.USER_AGENT))) {
            attachmentName = URLEncoder.encode(attachmentName, "UTF-8");
        } else {
            attachmentName = new String(attachmentName.getBytes(StandardCharsets.UTF_8), StandardCharsets.ISO_8859_1);
        }
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + attachmentName);
        Writer writer = new ExcelWriter();
        AtomicInteger number = new AtomicInteger(0);
        writer.sheet(title, certificateRecordList)
                .field("序号", c-> number.incrementAndGet())
                .field("姓名", c -> Optional.ofNullable(c.getMember()).map(MemberEntity::getFullName).orElse(""))
                .field("员工编号", c -> DesensitizationUtil.desensitizeEmployeeId(Optional.ofNullable(c.getMember()).map(MemberEntity::getName).orElse("")))
                .field("专题名称", c -> Optional.ofNullable(c.getCourseInfo()).map(CourseInfo::getName).orElse(""))
                .field("专题编码", c -> Optional.ofNullable(c.getCourseInfo()).map(CourseInfo::getCode).orElse(""))
                .field("导入时间", c -> Optional.ofNullable(c.getCreateTime())
                        .map(x -> DateUtil.dateLongToString(x, DateUtil.YYYY_MM_DD_HH_MM))
                        .orElse(""));
        ServletOutputStream outputStream = response.getOutputStream();
        writer.write(outputStream);
    }


    /**
     * 重塑专区手动发放证书（删除）
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/{id}" , method = RequestMethod.DELETE)
    @Param(name = "id", required = true)
    @Param(name = "memberName", required = true)
    @Param(name = "courseName", required = true)
    @JSON("*")
    @Permitted()
    @Audit(module = "运营管理", subModule = "重塑证书发放", action = Audit.Action.DELETE, fisrtAction = "删除", desc = "在手动发放重塑证书中删除学员{0}的{1}的证书",  params = {"memberName", "courseName"})
    public Map<String,Object> delete(RequestContext requestContext) {
        return ImmutableMap.of("count",certificateRecordService.deleteCertificateRecord(requestContext.get("id", String.class)));
    }

    /**
     * 强基专区手动发放证书（删除）
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/delete/{id}" , method = RequestMethod.DELETE)
    @Param(name = "id", required = true)
    @Param(name = "memberName", required = true)
    @Param(name = "courseName", required = true)
    @JSON("*")
    @Permitted()
    @Audit(module = "运营管理", subModule = "强基证书发放", action = Audit.Action.DELETE, fisrtAction = "删除", desc = "在手动发放强基证书中删除学员{0}的{1}的证书",  params = {"memberName", "courseName"})
    public Map<String,Object> deleteCertificateRecord(RequestContext requestContext) {
        return ImmutableMap.of("count",certificateRecordService.deleteCertificateRecord(requestContext.get("id", String.class)));
    }

    /**
     * 子认证专区查询证书
     * @param context
     * @return
     */
    @RequestMapping(method = RequestMethod.GET,value = "/find-sub-authenticated-certificate")
    @Permitted
    @Param(name = "businessId",type = String.class, required = true)
    @JSON("*.*")
    public Map<String, Object> findSubAuthenticatedCertificateInfo(RequestContext context, Subject<Member> subject){
        return certificateRecordService.getSubAuthByBusinessIdAndMemberId(context.getString("businessId"), subject.getCurrentUser());
    }


    /**
     * 个人中心-我的证书（专题）
     *
     * @param context 请求上下文
     * @param subject 用户容器
     * @return Page数据出参
     */
    @Param(name="page", type=Integer.class, required = true)
    @Param(name="pageSize", type=Integer.class, required = true)
    @Param(name="businessType", type=Integer.class,required = true)
    @JSON("recordCount")
    @JSON("items.courseInfo.(name)")
    @JSON("items.businessCertificate.(certificateId)")
    @JSON("items.(id,memberId,certificateNumber,businessId,businessType,type,finishTime,issueTime,issueAgency,accessType,modifyDate)")
    @RequestMapping(method = RequestMethod.GET,value = "/other-certificate-records")
    @Permitted()
    public PagedResult<CertificateRecord> otherCertificateRecords(RequestContext context,
                                                                  Subject<Member> subject){
        return certificateRecordService.otherCertificateRecords(
                context.get("page",Integer.class),
                context.get("pageSize",Integer.class),
                subject.getCurrentUserId(),
                context.getOptional("businessType",Integer.class),
                Optional.empty(),Optional.empty());
    }

    /**
     * 查询证书详情
     *
     * @param requestContext 请求上下文
     * @return 出参
     */
    @RequestMapping(value = "/single-certificate" , method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("id,name,examId,memberId,num,professionId,subProfessionId,equipmentTypeId,professionLevelId,score,scoreLevel,accessType,passStatus,issueTime,validDate,reason,createTime,isCurrent,templateId,passTime")
    @JSON("member.(id,name,fullName,identityNumber)")
    public CertificateRecord singleCertificate(RequestContext requestContext){
        return certificateRecordService.singleCertificate(
                requestContext.get("id",String.class)
        );
    }

    /**
     * 手动发送证书
     */
    @RequestMapping(value = "/manual-certificate" , method = RequestMethod.GET)
    @Param(name = "courseId", required = true)
    @JSON("*")
    public Map<String, String> manual(RequestContext requestContext, Subject<Member> subject){
        messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD_MANUAL, MessageHeaderContent.BUSINESS_ID, requestContext.getString("courseId"),
                MessageHeaderContent.MEMBER_ID, subject.getCurrentUserId());
        return ImmutableMap.of("result", "success");
    }

    /**
     * 修复证书发布时间
     */
    @RequestMapping(value = "/manual-fix" , method = RequestMethod.GET)
    @Param()
    @JSON("*")
    public Map<String, String> manualFix(RequestContext requestContext, Subject<Member> subject){
        messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD_FIX);
        return ImmutableMap.of("result", "success");
    }

    /**
     * 查询所有证书，学习助手使用
     */
    @RequestMapping(value = "/find-all" , method = RequestMethod.GET, produces = "application/json;charset=utf-8")
    @Permitted
    @Param(name="page", type=Integer.class, required = true)
    @Param(name="pageSize", type=Integer.class, required = true)
    @Param(name="startTime", type=Long.class)
    @Param(name="endTime", type=Long.class)
    @ResponseBody
    public String findAll(RequestContext rc, Subject<Member> subject){
        Integer page = rc.get("page", Integer.class);
        Integer pageSize = rc.get("pageSize", Integer.class);
        Optional<Long> startTime = rc.getOptionalLong("startTime");
        Optional<Long> endTime = rc.getOptionalLong("endTime");
        PagedResult coursePagedResult = certificateRecordService.otherCertificateRecords(
            page,
            pageSize,
            subject.getCurrentUserId(),
            Optional.of(1),
            startTime,
            endTime);
        int total = page * pageSize;
        if (coursePagedResult.getRecordCount() == 0) {
            page = 1;
        } else if (total >= coursePagedResult.getRecordCount()) {
            page = (total - coursePagedResult.getRecordCount()) / pageSize + 1;
        }
        PagedResult normalExamPagedResult = examService.findMyCertificateList(
                rc.getOptional("organizationId", String.class),
                rc.getOptional("status", Integer.class),
                page,
                pageSize,
                rc.getOptional("startTimeOrderBy", Integer.class),
                subject.getCurrentUserId(),
                1,
                rc.getOptional("examIds", String.class), startTime, endTime);
        if (coursePagedResult.getRecordCount() == 0) {
            page = 1;
        } else if (total >= normalExamPagedResult.getRecordCount() + coursePagedResult.getRecordCount()) {
            page = (total - coursePagedResult.getRecordCount() - normalExamPagedResult.getRecordCount()) / pageSize + 1;
        }
        PagedResult authExamPagedResult = examService.findMyCertificateList(
                rc.getOptional("organizationId", String.class),
                rc.getOptional("status", Integer.class),
                page,
                pageSize,
                rc.getOptional("startTimeOrderBy", Integer.class),
                subject.getCurrentUserId(),
                2,
                rc.getOptional("examIds", String.class), startTime, endTime);
        ArrayList result = new ArrayList();
        result.addAll(coursePagedResult.getItems());
        result.addAll(normalExamPagedResult.getItems());
        result.addAll(authExamPagedResult.getItems());
        return com.alibaba.fastjson.JSON.toJSONString(ImmutableMap.of(
                "recordCount", coursePagedResult.getRecordCount() + normalExamPagedResult.getRecordCount() + authExamPagedResult.getRecordCount(),
                "items", result.stream().map(e-> com.alibaba.fastjson.JSON.parseObject(com.alibaba.fastjson.JSON.toJSONString(e), HashMap.class))
                        .limit(pageSize).collect(Collectors.toList())));
    }

    @Qualifier("messageSender")
    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }
}
