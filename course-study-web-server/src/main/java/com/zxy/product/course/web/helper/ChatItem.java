package com.zxy.product.course.web.helper;

import com.zxy.product.course.entity.CourseFeedback;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/29/15:55
 * @Description:
 */
public class ChatItem implements Serializable {
    private static final long serialVersionUID = 9076087757315165893L;

    private String id;
    private String conversation_id;
    private String query;
    private String answer;
    private Integer created_at;

    private CourseFeedback courseFeedback;

    public CourseFeedback getCourseFeedback() {
        return courseFeedback;
    }

    public void setCourseFeedback(CourseFeedback courseFeedback) {
        this.courseFeedback = courseFeedback;
    }

    private Long time;

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getConversation_id() {
        return conversation_id;
    }

    public void setConversation_id(String conversation_id) {
        this.conversation_id = conversation_id;
    }

    public String getQuery() {
        return query;
    }

    public void setQuery(String query) {
        this.query = query;
    }

    public String getAnswer() {
        return answer;
    }

    public void setAnswer(String answer) {
        this.answer = answer;
    }

//    public Integer getCreated_at() {
//        return created_at;
//    }
//
//    public void setCreated_at(Integer created_at) {
//        this.created_at = created_at;
//    }
}
