package com.zxy.product.course.web.helper;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.restful.multipart.AttachmentResolver;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.dto.model.mentor.AiPreProcessingRule;
import com.zxy.product.course.dto.model.mentor.AiRuleDto;
import com.zxy.product.course.dto.model.mentor.AiSegmentationDto;
import com.zxy.product.course.dto.model.mentor.FileRuleDto;
import com.zxy.product.course.entity.CourseKnowledge;
import com.zxy.product.course.web.util.HttpClientUtil;
import com.zxy.product.human.entity.Attachment;
import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/06/18/15:32
 * @Description:
 */
@Component
public class UploadHelper {

    @Value("${course.model.url}")
    private String url;
    @Value("${course.model.dataset-id}")
    private String datasetId;
    @Value("${course.model.app-api-key}")
    private String appApiKey;
    @Value("${course.model.dataset-api-key}")
    private String datasetApiKey;
    @Value("${course.model.interface.create-file}")
    private String createFile;

    private static Logger logger = LoggerFactory.getLogger(UploadHelper.class);

    private AttachmentResolver attachmentResolver;

    @Autowired
    public void setAttachmentResolver(AttachmentResolver attachmentResolver) {
        this.attachmentResolver = attachmentResolver;
    }

    private static final CloseableHttpClient client;


    /**
     * 转换文件名（保留扩展名）
     * @param filename 原始文件名
     * @return 转换后的临时文件名称
     */
    public String convertFilename(String filename) {
        if (StringUtils.isBlank(filename)) {
            return "file_" + System.currentTimeMillis();
        }

        // 分离文件名和扩展名
        String extension = "";
        int dotIndex = filename.lastIndexOf('.');

        if (dotIndex > 0) {
            extension = filename.substring(dotIndex);
        }

        // 转换中文部分
        String changenName = String.valueOf(System.currentTimeMillis());


        return changenName + extension;
    }

    public  String uploadFileV2(String url, Attachment a, Map<String, String> headers, String jsonStr){
        com.zxy.common.restful.multipart.Attachment fastDFSAttachment = new com.zxy.common.restful.multipart.Attachment();
        fastDFSAttachment.setPath(a.getPath());
        InputStream is = attachmentResolver.resolveToRead(fastDFSAttachment);
        // 创建HttpPost对象，并设置URL
        HttpPost httpPost = new HttpPost(url);
        Set<String> e;
        Iterator<String> ite;
        String headerKey;
        try {
            if(headers != null) {
                e = headers.keySet();
                ite = e.iterator();
                while(ite.hasNext()) {
                    headerKey = ite.next();
                    httpPost.addHeader(headerKey, headers.get(headerKey));
                }
            }
            // 使用MultipartEntityBuilder构建请求体
            MultipartEntityBuilder builder = MultipartEntityBuilder.create();
            builder.setCharset(StandardCharsets.UTF_8);
            builder.setMode(HttpMultipartMode.RFC6532); // 使用RFC6532模式支持非ASCII字符
            builder.addTextBody("data", jsonStr, ContentType.APPLICATION_JSON); // 添加普通参数
            logger.info("fileName={}", a.getFilename());
            ContentType contentType = ContentType.create("application/octet-stream", "UTF-8");
//            String filename = convertFilename(a.getFilename());
            builder.addBinaryBody(
                    "file",
                    is,
                    contentType,
                    a.getFilename()
            );
            HttpEntity multipart = builder.build();
            // 将构建的实体设置到HttpPost中
            httpPost.setEntity(multipart);
            // 执行请求并获取响应
            CloseableHttpResponse response = client.execute(httpPost);
            try {
                // 获取响应内容
                String responseBody = EntityUtils.toString(response.getEntity());
                return responseBody;
            } finally {
                response.close();
            }
        } catch (IOException es) {
            logger.error("上传文件失败 message={}", es.getMessage());
            throw new UnprocessableException(ErrorCode.uploadFileFail);
        }
    }
    public CourseKnowledge sendFile(Attachment a, CourseKnowledge courseKnowledge, Optional<String> documemntId){
        FileRuleDto data = setRule();
        if(documemntId.isPresent()){
            data.setOriginal_document_id(documemntId.get());
        }
        String urls = url + String.format(createFile, datasetId);
        logger.info("params: urls = {}, data ={}", urls, JSON.toJSONString(data));
        HashMap<String, String> heard = new HashMap();
        heard.put("Authorization", datasetApiKey);
        String response = uploadFileV2(urls, a, heard, JSON.toJSONString(data));
        logger.warn("response={}", response);
        JSONObject jsonObject = JSON.parseObject(response);
        JSONObject document = jsonObject.getJSONObject("document");
        Boolean enabled = document.getBoolean("enabled");
        if(Objects.nonNull(document) && Objects.nonNull(enabled) && enabled){
            courseKnowledge.setAttachmentUpId(document.getString("id"));
            courseKnowledge.setAttachmentBatch(jsonObject.getString("batch"));
            if(Objects.nonNull(document.getString("indexing_status")) && Objects.equals(document.getString("indexing_status"), CourseKnowledge.INDEXING_STATUS_COMPLETED)){
                courseKnowledge.setFinshStatus(CourseKnowledge.FEEDBACK_STATUS_PUBLISH);
            }
        } else {
            String message = jsonObject.getString("message");
            String code = jsonObject.getString("code");
            logger.warn("上传文件错误：错误code={}，message={}", code, message);
            throw new UnprocessableException(ErrorCode.uploadFileFail);
        }
        return courseKnowledge;
    }



    private FileRuleDto setRule(){
        FileRuleDto data = new FileRuleDto();
        data.setDoc_form("text_model");
        data.setIndexing_technique("high_quality");
        AiRuleDto aiRuleDto = new AiRuleDto();
        List<AiPreProcessingRule> aiPreProcessingRules = new ArrayList<>();
        AiPreProcessingRule pRule = new AiPreProcessingRule();
        pRule.setId("remove_extra_spaces");
        pRule.setEnabled(true);
        AiPreProcessingRule pRule2 = new AiPreProcessingRule();
        pRule2.setId("remove_urls_emails");
        pRule2.setEnabled(false);
        AiSegmentationDto aiSegmentationDto = new AiSegmentationDto();
        aiSegmentationDto.setSeparator("###");
        aiSegmentationDto.setMax_tokens(2000);
        aiSegmentationDto.setChunk_overlap(100);
        aiPreProcessingRules.add(pRule);
        aiPreProcessingRules.add(pRule2);
        aiRuleDto.setPre_processing_rules(aiPreProcessingRules);
        aiRuleDto.setSegmentation(aiSegmentationDto);
        Map<String, Object> map = new HashMap<>();
        map.put("rules", aiRuleDto);
        map.put("mode", "custom");
        data.setProcess_rule(map);
        return data;
    }

    static {
        PoolingHttpClientConnectionManager connManager = new PoolingHttpClientConnectionManager();
        connManager.setMaxTotal(100);
        connManager.setDefaultMaxPerRoute(10);
        client = HttpClients.custom().setConnectionManager(connManager).build();
    }
}
