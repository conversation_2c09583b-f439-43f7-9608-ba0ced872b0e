package com.zxy.product.course.web.audit;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.security.SecurityManager;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.servlet.http.HttpServletRequest;
import java.util.Arrays;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Aspect
@Component
public class ControllerImAdvice {

    private final Logger LOGGER = LoggerFactory.getLogger(ControllerImAdvice.class);

    private static final int type = 88802;
    private static final String ORGANIZATION_ID = "organization_id";
    private static final String MODULE = "module";
    private static final String SUB_MODULE = "sub-module";
    private static final String ACTION = "action";
    private static final String FIRST_ACTION = "first-action";
    private static final String SECOND_ACTION = "second-action";
    private static final String MEMBER_FULL_NAME = "member_full_name";
    private static final String CURRENT_DATE = "current-date";
    private static final String DESC = "desc";
    private static final String DESC_REQUEST = "desc-request";
    private static final String DESC_REST_TEMPLATE_KEYS = "desc-rest-template-keys";
    private static final String DESC_REST_TEMPLATE_IDS = "desc-rest-template-ids";
    private static final String DESC_REST_TEMPLATE_JSONS = "desc-rest-template-jsons";
    private static final String USER_AGENT = "user-agent";
    private static final String IP = "ip";
    private static final String PAGE = "1";

    private String module;
    private MessageSender messageSender;
    private SecurityManager<?> securityManager;

    public void setModule(String module) {
        this.module = module;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setSecurityManager(SecurityManager<?> securityManager) {
        this.securityManager = securityManager;
    }



    @Pointcut("@annotation(com.zxy.product.course.web.audit.ImAudit) || @annotation(com.zxy.product.course.web.audit.ImAudits)")
    public void pointcut() {}

    @AfterReturning(pointcut = "pointcut()")
    public void doBefore(JoinPoint pjp) {
        LOGGER.info("切面触发");
        MethodSignature methodSignature = (MethodSignature) pjp.getSignature();
        ImAudit[] audits = methodSignature.getMethod().getAnnotationsByType(ImAudit.class);
        if (LOGGER.isDebugEnabled()) {
            LOGGER.debug(Arrays.toString(audits));
        }
        RequestContext context = Arrays.stream(pjp.getArgs()).filter(param -> param instanceof RequestContext).map(param -> (RequestContext) param).findFirst().get();
        ImAudit audit = filterAudit(audits, context);
        LOGGER.info("audit:{}", audit);
        if (audit == null) {
            return;
        }

        //是查询
        if (audit.action().equals(ImAudit.Action.FIND)) {
            Optional<String> page = context.getOptionalString("page");
            if (page.isPresent()) {
                    boolean count = false;
                    //获取参数名,只有大于一个参数才记录
                    String[] params = audit.params();
                    LOGGER.info("参数名:{}", Arrays.toString(params));
                    for (String param : params) {
                        Optional<String> optionalString = context.getOptionalString(param);
                        if (optionalString.isPresent()) {
                            LOGGER.info("参数名:{}值等于:{}", param, optionalString.get());
                            count = true;
                            break;
                        }
                    }
                    //记录
                    if (count) {
                        LOGGER.info("参数大于等于1");
                        process(audit, context, this.messageSender);
                    }
                return;
            }
        }
        process(audit, context, this.messageSender);
    }

    private void process(ImAudit audit, RequestContext requestContext, MessageSender messageSender) {
        try {
            messageSender.send(
                    type, ORGANIZATION_ID, securityManager.get(requestContext.getRequest()).getRootOrganizationId(),
                    MODULE, null != module ? module : audit.module(),
                    SUB_MODULE, audit.subModule(),
                    ACTION, audit.action().toString(),
                    FIRST_ACTION, audit.fisrtAction(),
                    SECOND_ACTION, audit.secondAction(),
                    MEMBER_FULL_NAME, JSONObject.parseObject(JSON.toJSONString(securityManager.get(requestContext.getRequest()).getCurrentUser())).getString("fullName"),
                    USER_AGENT, requestContext.getRequest().getHeader("User-Agent"),
                    IP, getIpAddr(requestContext.getRequest()),
                    CURRENT_DATE, String.valueOf(System.currentTimeMillis()),
                    DESC, audit.desc(),
                    //DESC_REQUEST, Arrays.stream(audit.params()).map(requestContext::get).map(String::valueOf).reduce((a, b) -> a + "==" + b).orElse(""),
                    DESC_REQUEST, Arrays.stream(audit.params()).map(requestContext::get).map(r-> StringUtils.isEmpty(r)? "null":r.toString()).map(r -> r.replace("#","-")).reduce((a, b) -> a + "==" + b).orElse(""),
                    DESC_REST_TEMPLATE_KEYS, Arrays.stream(audit.keys()).reduce((a, b) -> a + "==" + b).orElse(""),
                    DESC_REST_TEMPLATE_IDS, Arrays.stream(audit.ids()).map(requestContext::get).map(String::valueOf).reduce((a, b) -> a + "==" + b).orElse("") ,
                    DESC_REST_TEMPLATE_JSONS, Arrays.stream(audit.jsons()).reduce((a, b) -> a + "==" + b).orElse(""));
        } catch (Exception e) {
            LOGGER.error(e.getMessage());
            e.printStackTrace();
        }
    }

    private ImAudit filterAudit(ImAudit[] audits, RequestContext context) {
        /*if (audits.length > 1) {
            Map<String, ImAudit> auditMap = Stream.of(audits).collect(Collectors.toMap(ImAudit::businessValue, audit -> audit));
            LOGGER.info("businessType的值:{}",auditMap);
            String businessType = audits[0].businessType();
            if ("".equals(businessType)) {
                return audits[0];
            }
            String[] businessTypes = businessType.split(",");

            LOGGER.info("businessTypes:{}", Arrays.toString(businessTypes));
            String businessValue = Arrays.stream(businessTypes).map(context::get)
                    .map(Optional::ofNullable)
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .map(r-> StringUtils.isEmpty(r) ? "null" : r.toString()).reduce((a, b) -> a + "," + b).orElse("");
            return auditMap.get(businessValue) == null ? null : auditMap.get(businessValue);

        }
        return audits[0];*/
        Map<String, ImAudit> auditMap = Stream.of(audits).collect(Collectors.toMap(ImAudit::businessValue, audit -> audit));
        String businessType = audits[0].businessType();
        if ("".equals(businessType)) {
            return audits[0];
        }
        String[] businessTypes = businessType.split(",");
        LOGGER.info("businessTypes:{}", Arrays.toString(businessTypes));
        String businessValue = Arrays.stream(businessTypes).map(context::get)
                .map(Optional::ofNullable)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .map(String::valueOf).reduce((a, b) -> a + "," + b).orElse("");
        return auditMap.get(businessValue) == null ? null : auditMap.get(businessValue);
    }

    /**
     * 取得真实地址IP(优先取x-forwarded-for)
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        //此方式用于nginx服务器参数设置
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip.split(",")[0];
        }
        if (request.getHeader("X-Real-IP") != null) {
            return request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

}
