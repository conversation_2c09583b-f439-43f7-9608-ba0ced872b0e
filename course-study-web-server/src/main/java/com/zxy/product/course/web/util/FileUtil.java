package com.zxy.product.course.web.util;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.zxy.product.exam.entity.GridFile;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.POIXMLTextExtractor;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.openxml4j.exceptions.OpenXML4JException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.xmlbeans.XmlException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

public class FileUtil {

    private static final Logger LOGGER = LoggerFactory.getLogger(FileUtil.class);

    /**
     * 获取word的文本信息
     *
     * @param path 文件路径
     * @return
     */
    public static String differences(String path) throws IOException, OpenXML4JException, XmlException {
        String buffer = "";
        if (path.endsWith(".doc")) {
            FileInputStream is = new FileInputStream(path);
            WordExtractor ex = new WordExtractor(is);
            buffer = ex.getText();
            is.close();
        } else if (path.endsWith("docx")) {
            OPCPackage opcPackage = POIXMLDocument.openPackage(path);
            POIXMLTextExtractor extractor = new XWPFWordExtractor(opcPackage);
            buffer = extractor.getText();
            opcPackage.close();
        }
        buffer = buffer.replaceAll("\\s+", "");
        buffer = buffer.replaceAll("\r\n+", "");
        return buffer;
    }

    public static String readTextAli(String fileName) throws IOException {
        String test = getCharacter(new File(fileName));
        File fileDir = new File(fileName);
        BufferedReader in = new BufferedReader(new InputStreamReader(new FileInputStream(fileDir), test));
        String str;
        StringBuilder stringBuilder = new StringBuilder();
        while ((str = in.readLine()) != null) {
            stringBuilder.append(str);
        }
        in.close();
        return stringBuilder.toString();
    }

    /**
     * 方法中传入的参数分别为
     * filePath: 文件地址
     * fileName：文件名
     **/
    public static void fileToBytes(byte[] bytes, String filePath, String fileName) {
        BufferedOutputStream bos = null;
        FileOutputStream fos = null;
        File file = null;
        try {
            String path = "";
            if(!StringUtils.isBlank(fileName)){
               path = new StringBuilder().append(filePath).append(fileName).toString();
            }else {
                path = filePath;
            }
            file = new File(path);
            if (!file.exists()) {
                file.createNewFile();
            }
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);
            bos.write(bytes);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (bos != null) {
                try {
                    bos.close();
                } catch (IOException e) {
                }
            }
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                }
            }
        }
    }

    /**
     * 方法功能：将字节数组写入到新建文件中。
     *
     * @return boolean
     */
    public static boolean save2File(String fileName, byte[] bytes) {
        OutputStream fos = null;
        try {
            File file = new File(fileName);
            File parent = file.getParentFile();
            boolean bool;
            if ((!parent.exists()) &&
                    (!parent.mkdirs())) {
                return false;
            }
            fos = new FileOutputStream(file);
            fos.write(bytes);
            fos.flush();
            return true;
        } catch (FileNotFoundException e) {
            return false;
        } catch (IOException e) {
            File parent;
            return false;
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                }
            }
        }
    }

    /**
     * 获取pdf文本信息
     *
     * @param path
     * @return
     */
    public static String getPDFTxt(String path) throws IOException {
        //要读取的pdf文档位置
        String text = null;
        File file = new File(path);
        XSSFWorkbook workbook = new XSSFWorkbook();
        XSSFSheet sheet = workbook.createSheet();
        //加载pdf文件，创建PDDocument对象
        PDDocument document = PDDocument.load(file);
        //创建pdf文本获取对象PDFTextStripper
        PDFTextStripper pdfStripper = null;
        pdfStripper = new PDFTextStripper();
        //获取pdf中所有信息，text中包含的就是当前pdf文档中所有信息
        text = pdfStripper.getText(document);
        text = text.replaceAll("\\s+", "");
        text = text.replaceAll("\r\n+", "");
        return text;
    }

   //判断编码格式方法
   private static  String getCharacter(File sourceFile) {
       String charset = "GBK";
       byte[] first3Bytes = new byte[3];
       try {
           boolean checked = false;
           BufferedInputStream bis = new BufferedInputStream(new FileInputStream(sourceFile));
           bis.mark(0);
           int read = bis.read(first3Bytes, 0, 3);
           if (read == -1) {
               return charset; //文件编码为 ANSI
           } else if (first3Bytes[0] == (byte) 0xFF
                   && first3Bytes[1] == (byte) 0xFE) {
               charset = "UTF-16LE"; //文件编码为 Unicode
               checked = true;
           } else if (first3Bytes[0] == (byte) 0xFE
                   && first3Bytes[1] == (byte) 0xFF) {
               charset = "UTF-16BE"; //文件编码为 Unicode big endian
               checked = true;
           } else if (first3Bytes[0] == (byte) 0xEF
                   && first3Bytes[1] == (byte) 0xBB
                   && first3Bytes[2] == (byte) 0xBF) {
               charset = "UTF-8"; //文件编码为 UTF-8
               checked = true;
           }
           bis.reset();
           if (!checked) {
               int loc = 0;
               while ((read = bis.read()) != -1) {
                   loc++;
                   if (read >= 0xF0)
                       break;
                   if (0x80 <= read && read <= 0xBF) // 单独出现BF以下的，也算是GBK
                       break;
                   if (0xC0 <= read && read <= 0xDF) {
                       read = bis.read();
                       if (0x80 <= read && read <= 0xBF) // 双字节 (0xC0 - 0xDF)
                           // (0x80
                           // - 0xBF),也可能在GB编码内
                           continue;
                       else
                           break;
                   } else if (0xE0 <= read && read <= 0xEF) {
                       read = bis.read();
                       if (0x80 <= read && read <= 0xBF) {
                           read = bis.read();
                           if (0x80 <= read && read <= 0xBF) {
                               charset = "UTF-8";
                               break;
                           } else
                               break;
                       } else
                           break;
                   }
               }
           }
           bis.close();
       } catch (Exception e) {
           e.printStackTrace();
       }
       return charset;
   }
    public static void convertByte(byte[] bytes, String fileName) {
        if (bytes == null || bytes.length == 0) {
            return;
        }
        try (OutputStream os = Files.newOutputStream(Paths.get(fileName))) {
            os.write(bytes, 0, bytes.length);
        } catch (Exception e) {
            LOGGER.error("生成文件失败！", e);
        }
    }

    public static void zipDirWithName(String srcFilePath, String name) {
        File srcFile = new File(srcFilePath);
        if (srcFile.exists() && srcFile.isDirectory()) {
            String zipName = name + GridFile.ZIP_EXT;
            File targetFile = new File(srcFile.getParent(), zipName);
            cleanUp(targetFile);
            try (FileOutputStream fos = new FileOutputStream(targetFile);
                 ZipOutputStream zos = new ZipOutputStream(new BufferedOutputStream(fos))) {
                File[] subFiles = srcFile.listFiles();
                if (subFiles != null) {
                    for (File subFile : subFiles) {
                        addEntry(subFile, zos);
                    }
                }
            } catch (IOException e) {
                LOGGER.error("压缩包添加文件失败！", e);
            }
        }
    }

    public static void cleanUp(File targetFile) {
        if (!targetFile.exists()) {
            return;
        }
        if (targetFile.isDirectory()) {
            String[] childs = targetFile.list();
            for (String file : childs == null ? new String[0] : childs) {
                deleteFile(new File(targetFile, file));
            }
        }
        deleteFile(targetFile);
    }

    private static void deleteFile(File file) {
        if (!file.exists()) {
            return;
        }
        try {
            Path dirPath = Paths.get(file.toURI());
            Path currentPath = Paths.get(dirPath.toString());
            Files.delete(currentPath);
        } catch (IOException e) {
            LOGGER.error("删除文件失败！", e);
        }
    }

    /**
     * 添加文件
     *
     * @param source 要添加进zip的文件
     * @param zos    zip文件输出流
     */
    private static void addEntry(File source, ZipOutputStream zos)
            throws IOException {
        byte[] buffer = new byte[GridFile.BUFFER_SIZE];
        try (FileInputStream fis = new FileInputStream(source);
             BufferedInputStream bis = new BufferedInputStream(fis, buffer.length)) {
            int read;
            zos.putNextEntry(new ZipEntry(source.getName()));
            while ((read = bis.read(buffer, 0, buffer.length)) != -1) {
                zos.write(buffer, 0, read);
            }
            zos.closeEntry();
        }
    }
}
