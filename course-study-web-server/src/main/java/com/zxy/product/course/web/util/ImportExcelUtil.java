package com.zxy.product.course.web.util;

import com.itextpdf.text.Element;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfGState;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.office.excel.Reader;
import com.zxy.common.office.excel.Validator;
import com.zxy.common.restful.RequestContext;
import com.zxy.product.human.content.ErrorCode;
import com.zxy.product.human.entity.Organization;
import com.zxy.product.human.util.BrowserUtil;
import com.zxy.product.system.entity.DataPermission;
import org.apache.poi.ss.usermodel.ClientAnchor;
import org.apache.poi.ss.usermodel.Drawing;
import org.apache.poi.ss.usermodel.Picture;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFClientAnchor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletResponse;
import java.awt.*;
import java.awt.font.FontRenderContext;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.Serializable;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 导入ExcelUtil
 * <AUTHOR>
 *
 */
public class ImportExcelUtil implements Serializable{
	
	private static final long serialVersionUID = 1L;
	
	public static final String indexRegex = "^[0-9]*[1-9][0-9]*$";
	public static final String codeRegex = "^[A-Za-z0-9_]{1,15}";
	private static Logger logger = LoggerFactory.getLogger(ImportExcelUtil.class);
	
	/**
	 * 设置下载格式
	 * @param requestContext
	 * @param attachmentName
	 * @return
	 * @throws IOException
	 */
	public static HttpServletResponse setHttpServletResponse(RequestContext requestContext,String attachmentName) throws IOException{
		HttpServletResponse response = requestContext.getResponse();
		if (BrowserUtil.isMSBrowser(requestContext.getRequest().getHeader("User-Agent"))) {
			attachmentName = URLEncoder.encode(attachmentName, "UTF-8");
		} else {
			attachmentName = new String(attachmentName.getBytes("UTF-8"), "ISO-8859-1");
		}
		response.setContentType("application/octet-stream;charset=utf-8");
	    response.setHeader("Content-Disposition", "attachment;filename=" + attachmentName);
		return response;
	}
	
	/**
	 * 获取数据权限规则
	 * @param codeLevels 
	 * @return
	 */
	public static List<Integer> getPermissionCodeLevels(Optional<DataPermission> codeLevels){
		return codeLevels.map(d -> {
			List<Integer> levels = new ArrayList<Integer>();
			levels.add(Organization.LEVEL_HEAD);
			if (d.getOrgLevel() == DataPermission.LEVEL_CHECKED)
				levels.add(Organization.LEVEL_BRANCH);
			if (d.getDeptLevel() == DataPermission.LEVEL_CHECKED)
				levels.add(Organization.LEVEL_DEPARTMENT);
			return levels;
		}).orElse(Arrays.asList());
	}
	
	/**
	 * excel序号验证
	 * @return
	 */
	public static Validator<String> serialNumberValidator() {
        return (v, context, prev) -> {
        	if (null != v && !"".equals(v)) {
                if (!v.matches(indexRegex)) {
                    context.error(ErrorCode.OrderNotMatchs);
                    return false;
                }
                if(v.length() >= 7){
                    context.error(ErrorCode.OrderLengthNotMatchs);
                    return false;
                }
			}
			return true;
        };
    }
	
	/**
	 * 验证模板是否匹配   导入数量是否超出最大限制、文件内容为空
	 * @param result
	 */
	public static void validatorTemplate(Reader.Result result) {
		//step1 是否匹配模板
		if(!result.isCellMatched()) throw new UnprocessableException(ErrorCode.TemplateError);
        //step2 判断是否超出每次新增最大导入数量。  过滤excel空的行数
        List<Reader.Row> errorRows = result.getErrorRows();
        for(int i=errorRows.size()-1;i>=0;i--) {
        	if(errorRows.get(i).getIndex()>=5000) {
        		List<Validator.DataError> dataErrors = errorRows.get(i).getErrors();
        		if(dataErrors.size()>=7) {
        			errorRows.remove(i);
        		}
        	}
        }
        if ((result.getCorrectRows().size() + errorRows.size()) > 5000)
            throw new UnprocessableException(ErrorCode.ImprotExcelMaxError);
       
        if (result.getCorrectRows().isEmpty() && result.getErrorRows().isEmpty()) 
            throw new UnprocessableException(ErrorCode.RowNotMatchs);
	}
	
	/**step3 错误信息：行-列-错误编码 */
	public static List<Map<String, Object>> createErrorList(Reader.Result result) {
        List<Map<String, Object>> errors = new ArrayList<>();
        for(Validator.DataError e : result.getErrors() ){
            Map<String, Object> map = new HashMap<>();
            map.put("row", e.getRow());
            map.put("column", e.getColumn());
            map.put("code", e.getCode() == null ? "" : e.getCode().getCode());
            errors.add(map);
        }
        if(errors.size()>0) {
        	Collections.sort(errors, new Comparator<Map<String, Object>>(){
				@Override
				public int compare(Map<String, Object> row1, Map<String, Object> row2) {
					 return ((Integer) row1.get("row")).compareTo((Integer)row2.get("row"));
				}
        		
        	});
        }
        return errors;
    }

	/*
	 * 为Excel打上水印工具函数 请自行确保参数值，以保证水印图片之间不会覆盖。 在计算水印的位置的时候，并没有考虑到单元格合并的情况，请注意
	 *
	 * @param wb
	 *            Excel Workbook
	 * @param sheet
	 *            需要打水印的Excel
	 * @param waterRemarkPath
	 *            水印地址，classPath，目前只支持png格式的图片，
	 *            因为非png格式的图片打到Excel上后可能会有图片变红的问题，且不容易做出透明效果。
	 *            同时请注意传入的地址格式，应该为类似："\\excelTemplate\\test.png"
	 * @param startXCol
	 *            水印起始列
	 * @param startYRow
	 *            水印起始行
	 * @param betweenXCol
	 *            水印横向之间间隔多少列
	 * @param betweenYRow
	 *            水印纵向之间间隔多少行
	 * @param XCount
	 *            横向共有水印多少个
	 * @param YCount
	 *            纵向共有水印多少个
	 * @param waterRemarkWidth
	 *            水印图片宽度为多少列
	 * @param waterRemarkHeight
	 *            水印图片高度为多少行
	 * @throws IOException
	 */
	public static void putWaterRemarkToExcel(Workbook wb, Sheet sheet, String waterRemarkPath, int startXCol,
											 int startYRow, int betweenXCol, int betweenYRow, int XCount, int YCount, int waterRemarkWidth,
											 int waterRemarkHeight, String content) throws IOException {

		// 加载图片
		ByteArrayOutputStream byteArrayOut = new ByteArrayOutputStream();
		BufferedImage bufferImg = createWaterMark(content);
		ImageIO.write(bufferImg, "png", byteArrayOut);

		// 开始打水印
		Drawing drawing = sheet.createDrawingPatriarch();
//        Drawing drawingPatriarch = sheet.getDrawingPatriarch();
		// 按照共需打印多少行水印进行循环
		for (int yCount = 0; yCount < YCount; yCount++) {
			// 按照每行需要打印多少个水印进行循环
			for (int xCount = 0; xCount < XCount; xCount++) {
				// 创建水印图片位置
				int xIndexInteger = startXCol + (xCount * waterRemarkWidth) + (xCount * betweenXCol);
				int yIndexInteger = startYRow + (yCount * waterRemarkHeight) + (yCount * betweenYRow);
				/*
				 * 参数定义： 第一个参数是（x轴的开始节点）； 第二个参数是（是y轴的开始节点）； 第三个参数是（是x轴的结束节点）；
				 * 第四个参数是（是y轴的结束节点）； 第五个参数是（是从Excel的第几列开始插入图片，从0开始计数）；
				 * 第六个参数是（是从excel的第几行开始插入图片，从0开始计数）； 第七个参数是（图片宽度，共多少列）；
				 * 第8个参数是（图片高度，共多少行）；
				 */
				ClientAnchor anchor = drawing.createAnchor(0, 0, 0, 0, xIndexInteger,
						yIndexInteger, xIndexInteger + waterRemarkWidth, yIndexInteger + waterRemarkHeight);

				Picture pic = drawing.createPicture(anchor, wb.addPicture(byteArrayOut.toByteArray(), Workbook.PICTURE_TYPE_PNG));
				pic.resize();
			}
		}
	}

	public static void addWatermark(Workbook workbook, Sheet sheet, String watermarkText) throws IOException {
		// 1. 计算内容区域
		ContentArea contentArea = calculateContentArea(sheet);

		// 2. 创建水印图片
		BufferedImage watermarkImage = createMultiLineWaterMark(watermarkText, 16, new Color(74, 74, 75), -15, 1);
		ByteArrayOutputStream os = new ByteArrayOutputStream();
		ImageIO.write(watermarkImage, "png", os);
		byte[] bytes = os.toByteArray();
		int pictureIdx = workbook.addPicture(bytes, Workbook.PICTURE_TYPE_PNG);

		// 3. 添加水印到内容区域
		Drawing drawing = sheet.createDrawingPatriarch();

		// 计算水印在内容区域内的分布
		int watermarkWidth = 2;  // 列宽
		int watermarkHeight = 4; // 行高

		// 计算水印数量
		int xCount = (int) Math.ceil((double) contentArea.width / (watermarkWidth * 1.5));
		int yCount = (int) Math.ceil((double) contentArea.height / (watermarkHeight * 1.5));

		// 计算间隔
		int xSpacing = xCount > 1 ? (contentArea.width - watermarkWidth * xCount) / (xCount - 1) : 0;
		int ySpacing = yCount > 1 ? (contentArea.height - watermarkHeight * yCount) / (yCount - 1) : 0;

		// 确保最小间隔
		xSpacing = Math.max(xSpacing, watermarkWidth / 2);
		ySpacing = Math.max(ySpacing, watermarkHeight / 2);

		// 添加水印
		for (int y = 0; y < yCount; y++) {
			for (int x = 0; x < xCount; x++) {
				int col = contentArea.startCol + (x * (watermarkWidth + xSpacing));
				int row = contentArea.startRow + (y * (watermarkHeight + ySpacing));

				// 边界检查
				col = Math.min(col, contentArea.endCol - watermarkWidth);
				row = Math.min(row, contentArea.endRow - watermarkHeight);

				XSSFClientAnchor anchor = new XSSFClientAnchor();
				anchor.setCol1(col);
				anchor.setRow1(row);
				anchor.setCol2(col + watermarkWidth);
				anchor.setRow2(row + watermarkHeight);

				Picture picture = drawing.createPicture(anchor, pictureIdx);
				resizePicture(picture);
			}
		}
	}

	// 计算内容区域
	private static ContentArea calculateContentArea(Sheet sheet) {
		ContentArea area = new ContentArea();

		// 计算实际使用的行和列
		area.startRow = 0;
		area.startCol = 0;
		area.endRow = sheet.getLastRowNum();

		// 计算最大列数
		area.endCol = 0;
		for (int i = 0; i <= area.endRow; i++) {
			Row row = sheet.getRow(i);
			if (row != null) {
				area.endCol = Math.max(area.endCol, row.getLastCellNum());
			}
		}

		// 设置最小区域
		area.endRow = Math.max(area.endRow, 10);
		area.endCol = Math.max(area.endCol, 5);

		area.width = area.endCol - area.startCol;
		area.height = area.endRow - area.startRow;

		return area;
	}

	// 内容区域内部类
	private static class ContentArea {
		int startRow;
		int endRow;
		int startCol;
		int endCol;
		int width;
		int height;
	}


	/**
	 * 创建支持换行的水印图片
	 *
	 * @param content 水印文本(支持\n换行)
	 * @param fontSize 字体大小
	 * @param color 颜色
	 * @param rotate 旋转角度
	 * @param lineSpacing 行间距(像素)
	 * @return 水印图片
	 */
	private static BufferedImage createMultiLineWaterMark(String content, int fontSize, Color color,
														  double rotate, int lineSpacing) {
		// 分割文本行
		String[] lines = content.split("\n");
		List<String> wrappedLines = new ArrayList<>();

		// 初步分割换行符分隔的文本
		for (String line : lines) {
			wrappedLines.add(line);
		}

		// 图片宽度和高度(根据行数动态调整)
		int width = 200;
		int height = 100 + (wrappedLines.size() * (fontSize + lineSpacing));

		BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);
		Graphics2D g2d = image.createGraphics();

		// 设置透明背景和抗锯齿
		g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER, 0.4f));
		g2d.setRenderingHint(RenderingHints.KEY_TEXT_ANTIALIASING, RenderingHints.VALUE_TEXT_ANTIALIAS_ON);
		g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);

		// 设置字体和颜色
		Font font = new Font("Microsoft YaHei", Font.ITALIC, fontSize);
		g2d.setFont(font);
		g2d.setColor(color);

		// 旋转画布
		g2d.rotate(Math.toRadians(rotate), width / 2, height / 2);

		// 计算文本位置(居中)
		FontMetrics fontMetrics = g2d.getFontMetrics();
		int textHeight = fontMetrics.getHeight();
		int totalTextHeight = (textHeight + lineSpacing) * wrappedLines.size() - lineSpacing;
		int startY = (height - totalTextHeight) / 2 + fontMetrics.getAscent();

		// 绘制每一行文本
		for (int i = 0; i < wrappedLines.size(); i++) {
			String line = wrappedLines.get(i);
			int textWidth = fontMetrics.stringWidth(line);
			int x = (width - textWidth) / 2;
			int y = startY + i * (textHeight + lineSpacing);
			g2d.drawString(line, x, y);
		}

		g2d.dispose();
		return image;
	}

	/**
	 * 调整图片大小(兼容XSSF和HSSF)
	 */
	private static void resizePicture(Picture picture) {
		if (picture instanceof org.apache.poi.xssf.usermodel.XSSFPicture) {
			((org.apache.poi.xssf.usermodel.XSSFPicture) picture).resize();
		} else if (picture instanceof org.apache.poi.hssf.usermodel.HSSFPicture) {
			((org.apache.poi.hssf.usermodel.HSSFPicture) picture).resize();
		}
	}



	public static BufferedImage createWaterMark(String content) {
		Integer width = 1200;
		Integer height = 500;
		BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);// 获取bufferedImage对象
		String fontType = "黑体";
		Integer fontStyle = Font.BOLD;
		Integer fontSize = 48;
		Font font = new Font(fontType, fontStyle, fontSize);
		Graphics2D g2d = image.createGraphics(); // 获取Graphics2d对象
		image = g2d.getDeviceConfiguration().createCompatibleImage(width, height, Transparency.TRANSLUCENT);
		g2d.dispose();
		g2d = image.createGraphics();
		g2d.setColor(new Color(0, 0, 0, 90)); //设置字体颜色和透明度
		g2d.setStroke(new BasicStroke(1)); // 设置字体
		g2d.setFont(font); // 设置字体类型  加粗 大小
		g2d.rotate(Math.toRadians(18), (double) image.getWidth() / 2, (double) image.getHeight() / 2);//设置倾斜度
		FontRenderContext context = g2d.getFontRenderContext();
		Rectangle2D bounds = font.getStringBounds(content, context);
		double x = 0;
		double y = 180;
		double ascent = -bounds.getY();
		double baseY = y + ascent;
		// 写入水印文字原定高度过小，所以累计写水印，增加高度
		g2d.drawString(content, (int) x, (int) baseY);
		// 设置透明度
		g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
		// 释放对象
		g2d.dispose();
		return image;
	}

	public static void createWaterMark(String content, String path) throws IOException {
		Integer width = 300;
		Integer height = 200;
		BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);// 获取bufferedImage对象
		String fontType = "黑体";
		Integer fontStyle = Font.BOLD;
		Integer fontSize = 48;
		Font font = new Font(fontType, fontStyle, fontSize);
		Graphics2D g2d = image.createGraphics(); // 获取Graphics2d对象
		image = g2d.getDeviceConfiguration().createCompatibleImage(width, height, Transparency.TRANSLUCENT);
		g2d.dispose();
		g2d = image.createGraphics();
		g2d.setColor(new Color(0, 0, 0, 90)); //设置字体颜色和透明度
		g2d.setStroke(new BasicStroke(1)); // 设置字体
		g2d.setFont(font); // 设置字体类型  加粗 大小
		g2d.rotate(Math.toRadians(-10), (double) image.getWidth() / 2, (double) image.getHeight() / 2);//设置倾斜度
		FontRenderContext context = g2d.getFontRenderContext();
		Rectangle2D bounds = font.getStringBounds(content, context);
		double x = (width - bounds.getWidth()) / 2;
		double y = (height - bounds.getHeight()) / 2;
		double ascent = -bounds.getY();
		double baseY = y + ascent;
		// 写入水印文字原定高度过小，所以累计写水印，增加高度
		g2d.drawString(content, (int) x, (int) baseY);
		// 设置透明度
		g2d.setComposite(AlphaComposite.getInstance(AlphaComposite.SRC_OVER));
		// 释放对象
		g2d.dispose();
		ImageIO.write(image, "png", new File(path));
	}

//	public static byte[] addWatermarkToPDF(byte[] pdfBytes, String watermarkText) {
//		//try-with-resources关闭流对象
//		try (InputStream inputStream = new ByteArrayInputStream(pdfBytes);
//			 PDDocument document = PDDocument.load(inputStream)
//		) {
//			PDPageTree pages = document.getPages();
//			/* *   修改点1: 针对不确定的PDF大小，不确定的PDF页数，转成串行流，false代表串行 true代表并行 ，基于安全性这里考虑串行流
//			 * 		 流相比for循环是一种更有效的处理数据集的方法，它可以提高性能，而且占用更少的内存（但是这不是绝对的）
//			 * 		 还有一种比较复杂的方式，针对PDPageTree这个树对象进行深度与广度遍历，但这无疑会增加代码的复杂性，故不采用
//			 */
//			Stream<PDPage> stream = StreamSupport.stream(pages.spliterator(), false);
//			/*     修改点2: PDType0Font.load(document, fileInputStream,true) 末尾的boolean变量作用:
//			 * 		如果将embedSubset设置为true，则字体将被嵌入到PDF文件中。这意味着字体将被保存在PDF文件中，并且不会从外部字体文件中引用。
//			 * 		这可以提高PDF文件的兼容性，因为即使没有安装字体，也可以查看PDF文件。
//			 *      但与此同时也会增加PDF文件的大小。因此，在将isEmbed设置为true之前，请务必考虑PDF文件的大小。
//			 * 		如果将embedSubset设置为false，则字体将被引用到外部字体文件中，它不会导致PDF变大，但是它可能导致一些兼容性，
//			 * 		具体情况根据业务来斟酌
//			 */
//			FileInputStream fileInputStream = new FileInputStream("/opt/jdk1.8.0_101/jre/lib/fonts/fallback/simhei.ttf");
//			PDFont font = PDType0Font.load(document, fileInputStream, true);
//			stream.forEach(page -> {
//				try (PDPageContentStream contentStream = new PDPageContentStream(
//						document, page,
//						PDPageContentStream.AppendMode.APPEND,
//						true, true
//				)) {
//					/*  	修改点3：“contentStream.setFont( PDType0Font.load(document, new FileInputStream("/opt/jdk1.8.0_101/jre/lib/fonts/fallback/simhei.ttf"),true), 40);”
//					 *		这行代码其中的PDType0Font.load()方法会加载字体文件并创建字体对象。如果字体文件非常大，或者有大量字体对象需要加载，那么这行代码很可能会导致OOM
//					 */
//					contentStream.setFont(font, 40);
//					contentStream.setNonStrokingColor(Color.gray);
//					contentStream.beginText();
//					AffineTransform transform = new AffineTransform();
//					transform.shear(0, -1);
//					contentStream.transform(new Matrix(transform));
//					contentStream.newLineAtOffset(0, 810);
//					contentStream.showText(watermarkText);
//					logger.info("我是pdf水印内容:{}", watermarkText);
//					contentStream.endText();
//				} catch (IOException e) {
//					logger.error("PDF水印添加出现异常，尽快排查e", e);
//					e.printStackTrace();
//				}
//			});
//			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//			// 将修改后添加水印的 PDF 保存到 ByteArrayOutputStream流对象中
//			document.save(outputStream);
//			// 返回添加水印后的字节数组
//			return outputStream.toByteArray();
//		} catch (Exception e) {
//			logger.error("addWatermarkToPDF方法出现异常，尽快排查e", e);
//			e.printStackTrace();
//		}
//		return null;
//	}



	/**
	 * pdf添加水印
	 *
	 * @param waterMarkText  水印文字
	 * @param pdfFileBytes   pdf
	 */
	public static byte[] addWatermarkToPDF( byte[] pdfFileBytes, String waterMarkText) {
		try {
			// 原PDF文件
			PdfReader reader = new PdfReader(pdfFileBytes);
			// 输出的PDF文件内容
			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
			PdfStamper stamper = new PdfStamper(reader, outputStream);
			// 字体 来源于 itext-asian jar包
//			BaseFont baseFont = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H", true);
			String prefixFont = "/opt/jdk1.8.0_101/jre/lib/fonts/fallback/simhei.ttf";
			BaseFont baseFont = BaseFont.createFont(prefixFont, BaseFont.IDENTITY_H, BaseFont.NOT_EMBEDDED);
			PdfGState gs = new PdfGState();
			// 设置透明度
			gs.setFillOpacity(0.2f);
			gs.setStrokeOpacity(0.3f);

			int totalPage = reader.getNumberOfPages() + 1;
			for (int i = 1; i < totalPage; i++) {
				// 内容上层
				PdfContentByte content = stamper.getOverContent(i);
				content.beginText();
				// 字体添加透明度
				content.setGState(gs);
				// 添加字体大小等
				content.setFontAndSize(baseFont, 30);
				// 添加范围
				content.showTextAligned(Element.ALIGN_BOTTOM, Optional.ofNullable(waterMarkText).orElse(""), 100, 100, 45);
				content.endText();
			}
			// 关闭
			stamper.close();
			reader.close();
			return outputStream.toByteArray();
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}


//	public static byte[] addImageWatermark(byte[] pdfBytes, byte[] watermarkBytes) {
//		try {
//			PdfReader pdfReader = new PdfReader(pdfBytes);
//
//			ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
//			PdfStamper pdfStamper = new PdfStamper(pdfReader, outputStream);
//
//			Image watermarkImage = Image.getInstance(watermarkBytes);
//			int pageCount = pdfReader.getNumberOfPages();
//
//			for (int i = 1; i <= pageCount; i++) {
//				PdfContentByte content = pdfStamper.getUnderContent(i);
//				content.addImage(watermarkImage);
//			}
//
//			pdfStamper.close();
//			pdfReader.close();
//
//			return outputStream.toByteArray();
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//
//		return null;
//	}
}
