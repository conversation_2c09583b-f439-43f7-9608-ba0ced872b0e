package com.zxy.product.course.web.controller.assistant;


import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Subject;
import com.zxy.product.course.api.course.ChatTimeService;
import com.zxy.product.course.api.course.QuestionFeedbackService;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.entity.ChatTimeRecord;
import com.zxy.product.course.entity.CourseFeedback;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.web.helper.ChatInputDto;
import com.zxy.product.course.web.helper.ChatItem;
import com.zxy.product.course.web.helper.ChatMessageHelper;
import com.zxy.product.course.web.helper.PageResultDto;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/29/11:17
 * @Description:
 */
@Controller
@RequestMapping("/chat")
public class AssismentChatController {

    private static Logger logger = LoggerFactory.getLogger(AssismentChatController.class);

    private ChatMessageHelper chatMessageHelper;

    private ChatTimeService chatTimeService;

    private MessageSender messageSender;

    private QuestionFeedbackService questionFeedbackService;

    @Autowired
    public void setQuestionFeedbackService(QuestionFeedbackService questionFeedbackService) {
        this.questionFeedbackService = questionFeedbackService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setChatTimeService(ChatTimeService chatTimeService) {
        this.chatTimeService = chatTimeService;
    }

    @Autowired
    public void setChatMessageHelper(ChatMessageHelper chatMessageHelper) {
        this.chatMessageHelper = chatMessageHelper;
    }

    @Value("${course.model.url}")
    private String url;

    @Value("${course.model.interface.chat-messages}")
    private String chatMessage;

    @Value("${course.model.app-api-key}")
    private String appApiKey;

    ExecutorService executorService = Executors.newSingleThreadExecutor();


    private static Integer TIME_OUT = 3 * 10 * 60 * 1000;
    /**
     * 提出问题*
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "question", required = true)
    @Param(name = "conversationId")
    @JSON("*")
    @JSON("*.(*)")
    @JSON("*.*.*.*")
    public SseEmitter sendChatMessage(RequestContext context, Subject<Member> subject){
            HashMap<String, Object> data = new HashMap();
            SseEmitter emitter = new SseEmitter(Long.parseLong(TIME_OUT+""));
            data.put("inputs", new Object());
            data.put("query", context.getString("question"));
            data.put("response_mode","streaming");
            data.put("user", subject.getCurrentUserId());
            data.put("conversation_id", context.getOptionalString("conversationId").orElse(CommonConstant.EMPTY));
            String urls = url + chatMessage;
            executorService.execute(() -> {
                try (CloseableHttpClient client = HttpClients.createDefault()) {
                    HttpPost httpPost = new HttpPost(urls);
                    httpPost.setHeader(HttpHeaders.CONTENT_TYPE, "application/json");
                    httpPost.setHeader(HttpHeaders.ACCEPT, "text/event-stream");
                    httpPost.setHeader("Authorization", appApiKey);

                    RequestConfig requestConfig = RequestConfig.custom()
                            .setConnectTimeout(TIME_OUT)
                            .setConnectionRequestTimeout(TIME_OUT)
                            .setSocketTimeout(TIME_OUT)
                            .build();
                    httpPost.setConfig(requestConfig);

                    StringEntity entity = new StringEntity(JSONObject.toJSONString(data), "UTF-8");
                    httpPost.setEntity(entity);

                    logger.info("请求参数:{}", entity);
                    HttpResponse response = client.execute(httpPost);
                    InputStream content = response.getEntity().getContent();

                    try (BufferedReader reader = new BufferedReader(new InputStreamReader(content, StandardCharsets.UTF_8))) {
                        String line;
                        while ((line = reader.readLine()) != null) {
                            if (line.startsWith("data:")) {
                                String json = line.substring(5).trim();
                                logger.info("收到成研 SSE 数据：{}", json);
                                emitter.send(SseEmitter.event().data(json));
                            }
                        }
                        emitter.complete();
                    }
                } catch (Exception e) {
                    logger.error("SSE 推送出错", e);
                    emitter.completeWithError(e);
                }
            });
            return emitter;
    }


    @RequestMapping(value = "/statistic",method = RequestMethod.POST)
    @Param(name = "businessId")
    @Param(name = "businessType", type = Integer.class)
    @Param(name = "answerSize", type = Integer.class)
    @Param(name = "from")
    @Param(name = "description")
    @JSON("*.*")
    public Map<String, Object> sendMessage(RequestContext context, Subject<Member> subject){
        sendMessage(context.getOptionalString("businessId").orElse(subject.getRootOrganizationId()), context.getOptionalInteger("businessType").orElse(0),
                context.getOptionalInteger("answerSize").orElse(0), subject.getCurrentUserId(),subject.getRootOrganizationId(),
                context.getOptionalString("description").orElse(CommonConstant.EMPTY), context.getOptionalString("from").orElse(CommonConstant.EMPTY));
        return ImmutableMap.of("result","success");
    }

    private void sendMessage(String businessId, Integer businessType, Integer answer, String memberId, String organizationId, String description, String from){
        messageSender.send(MessageTypeContent.SYSTEM_CHAT_RESULT_CHANGE, MessageHeaderContent.INTEGRAL_MEMBER_ID, memberId,
                MessageHeaderContent.INTEGRAL_BUSINESS_ID, businessId, MessageHeaderContent.BUSINESS_TYPE, String.valueOf(businessType),
                MessageHeaderContent.TEXT_SIZE, String.valueOf(answer),
                MessageHeaderContent.ORGANIZATION_ID, organizationId,
                MessageHeaderContent.INTEGRAL_DESCRIPTION,description,
                MessageHeaderContent.FROM, from);
    }


    @RequestMapping(value = "/addTimes",method = RequestMethod.POST)
    @Param(name = "id", required = true)
    @Param(name = "time",type = Long.class,required = true)
    @JSON("*.*")
    public Map<String, Object> addTimes(RequestContext context, Subject<Member> subject){
        setData(context.getString("id"), context.getLong("time"));
        return ImmutableMap.of("result", "success");
    }

    /**
     * 添加时间差*
     * @param time
     * @param id
     */
    private void setData(String id,Long time){
        ChatTimeRecord record = new ChatTimeRecord();
        record.forInsert();
        record.setAnswerId(id);
        record.setTime(time);
        chatTimeService.add(record);
    }

    /**
     * 停止响应*
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/stop",method = RequestMethod.POST)
    @Param(name = "taskId", required = true)
    @JSON("*.*")
    public Map<String, Object> sendChatStop(RequestContext context, Subject<Member> subject){
        String response = chatMessageHelper.sendChatStop(subject.getCurrentUserId(), context.getString("taskId"));
        return com.alibaba.fastjson.JSON.parseObject(response, HashMap.class);
    }



    /**
     * 聊天记录*
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(method = RequestMethod.GET)
    @Param(name="limit", type = Integer.class)
    @Param(name="sortBy", type = String.class)
    @JSON("limit, has_more")
    @JSON("data.(id,name,status,created_at,updated_at,inputs)")
    @JSON("data.inputs.(book,myName)")
    public PageResultDto<ChatInputDto> getChatList(RequestContext context, Subject<Member> subject){
        String response = chatMessageHelper.getChatList(subject.getCurrentUserId(), context.getOptionalInteger("limit").orElse(CommonConstant.TWENTY),
                context.getOptionalString("sortBy").orElse("-updated_at"));
        return com.alibaba.fastjson.JSON.parseObject(response, PageResultDto.class);
    }


    /**
     * 历史聊天记录*
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/histroy",method = RequestMethod.GET)
    @Param(name="limit", type = Integer.class)
    @Param(name="conversationId", type = String.class, required = true)
    @JSON("limit, has_more")
    @JSON("data.(id,query,answer,created_at,conversation_id, time)")
    @JSON("data.courseFeedback.(id,feedbackContent,feedbackType,like)")
    public PageResultDto<ChatItem> getHistoryChatList(RequestContext context, Subject<Member> subject){
        String response = chatMessageHelper.getHistoryChatList(subject.getCurrentUserId(), context.getOptionalInteger("limit").orElse(CommonConstant.TWENTY),
                context.getString("conversationId"));
        PageResultDto<ChatItem> pageResultDto = com.alibaba.fastjson.JSON.parseObject(response, PageResultDto.class);
        List<ChatItem> chatItems = JSONObject.parseArray(JSONObject.toJSONString(pageResultDto.getData()), ChatItem.class);
        List<String> ids = chatItems.stream().map(ChatItem::getId).collect(Collectors.toList());
        List<CourseFeedback> feedbacks = questionFeedbackService.findList(ids);
        Map<String, Long> timeServiceMap = chatTimeService.findMap(ids);
        Map<String, CourseFeedback> feedbackMap = feedbacks.stream().collect(Collectors.toMap(CourseFeedback::getQuestionId, Function.identity(), (p, q) -> q));
        chatItems.forEach(item->{
            item.setCourseFeedback(feedbackMap.get(item.getId()));
            item.setTime(timeServiceMap.get(item.getId()));
        });
        pageResultDto.setData(chatItems);
        return pageResultDto;
    }

    @RequestMapping(value = "/getTime",method = RequestMethod.GET)
    @Param(name ="ids", required = true)
    @JSON("*")
    public Map<String, Long> getOtherData(RequestContext context){
        List<String> ids = Stream.of(context.getString("ids").split(",")).collect(Collectors.toList());
        return chatTimeService.findMap(ids);
    }


}
