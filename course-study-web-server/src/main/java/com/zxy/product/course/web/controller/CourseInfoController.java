package com.zxy.product.course.web.controller;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.office.excel.export.Sheet;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.CachedResult;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.annotation.Params;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.validation.ValidationException;
import com.zxy.product.askbar.api.StudioContentService;
import com.zxy.product.askbar.api.StudioService;
import com.zxy.product.askbar.entity.StudioContent;
import com.zxy.product.course.api.AudienceItemService;
import com.zxy.product.course.api.BusinessEmergencyService;
import com.zxy.product.course.api.BusinessTopicService;
import com.zxy.product.course.api.CaptionService;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.DjLogService;
import com.zxy.product.course.api.IntelligentBroadcastService;
import com.zxy.product.course.api.MultidimensionalScoringService;
import com.zxy.product.course.api.OrganizationService;
import com.zxy.product.course.api.ThematicMemberService;
import com.zxy.product.course.api.ThematicService;
import com.zxy.product.course.api.archived.CourseStudyProgressArchivedService;
import com.zxy.product.course.api.audience.AudienceKService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.AudienceItem;
import com.zxy.product.course.entity.BusinessEmergency;
import com.zxy.product.course.entity.BusinessTopic;
import com.zxy.product.course.entity.Caption;
import com.zxy.product.course.entity.CourseAbility;
import com.zxy.product.course.entity.CourseAttachment;
import com.zxy.product.course.entity.CourseChapter;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseInfoDjyp;
import com.zxy.product.course.entity.CoursePhoto;
import com.zxy.product.course.entity.CourseRedShipAuditDetail;
import com.zxy.product.course.entity.CourseShelves;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.CourseStudyProgressArchived;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.MultidimensionalScoring;
import com.zxy.product.course.entity.Organization;
import com.zxy.product.course.entity.SubjectAdvertising;
import com.zxy.product.course.entity.SubjectTextArea;
import com.zxy.product.course.entity.Thematic;
import com.zxy.product.course.entity.ThematicMember;
import com.zxy.product.course.util.StringUtils;
import com.zxy.product.course.web.controller.course.CourseInfoAdminController;
import com.zxy.product.course.web.controller.party.Result;
import com.zxy.product.course.web.controller.party.ResultCodeEnum;
import com.zxy.product.course.web.kit.CourseInfoAdminKit;
import com.zxy.product.course.web.kit.HomeCertifyKit;
import com.zxy.product.course.web.util.BrowserUtil;
import com.zxy.product.course.web.util.VerifyIhrCodeUtil;
import com.zxy.product.exam.api.CertificateRecordService;
import com.zxy.product.exam.api.ExamRecordService;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.exam.entity.ExamRecord;
import com.zxy.product.history.api.HistoryCourseStudyProgressService;
import com.zxy.product.human.api.FileService;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.api.MemberTopicService;
import com.zxy.product.human.api.position.MemberTagService;
import com.zxy.product.human.entity.Attachment;
import com.zxy.product.human.entity.MemberTopic;
import com.zxy.product.log.api.DisplayService;
import com.zxy.product.log.entity.ResourceVisit;
import com.zxy.product.system.api.internalswitch.InternalSwitchService;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.api.setting.RuleConfigService;
import com.zxy.product.system.api.setting.RuleRedShipConfigService;
import com.zxy.product.system.api.topic.TopicService;
import com.zxy.product.system.entity.InternalSwitch;
import com.zxy.product.system.entity.RuleConfig;
import com.zxy.product.system.entity.Topic;
import com.zxy.product.system.util.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.DigestUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.zxy.product.course.web.util.SecurePathCdnUtils.generateSecurePathCdn;

/**
 *
 * <AUTHOR>
 */
@Controller
@RequestMapping("/course-info")
public class CourseInfoController implements EnvironmentAware {

    private static final Logger LOGGER = LoggerFactory.getLogger(CourseInfoController.class);
	public static final String RELEATED_COURSE_KEY = "releated-course-key";
	public static final String HOME_PAGE_KEY = "course-front-home-page-find-by-ids";
	public static final String HOME_PAGE_DETAIL_KEY = "course-front-home-page-detail-find-by-ids";
    private  final  static String  INTERNAL_ORGANIZATION_ID = "10000001";
    private  final  static String  KEY = "PublicationRestriction";
    private InternalSwitchService internalSwitchService;
    private AudienceItemService audienceItemService;
    private String secret;
    private CourseInfoService courseInfoService;
    private FileService fileService;
    private OrganizationService organizationService;
    private TopicService topicService;
    private BusinessTopicService businessTopicService;
    private MemberTopicService memberTopicService;
    private CourseInfoAdminController adminController;
    private GrantService grantService;
    private CourseInfoAdminKit courseInfoAdminKit;
    private AudienceKService audienceKService;
    private CourseCacheService courseCacheService;
    private StudioService studioService;
    private StudioContentService studioContentService;
    private ThematicService thematicService;
    private ThematicMemberService thematicMemberService;
    private CertificateRecordService certificateRecordService;
    private ExamRecordService examRecordService;
    private com.zxy.product.examstu.api.ExamRecordService examRecordStuService;

    private RuleConfigService ruleConfigService;
    private IntelligentBroadcastService intelligentBroadcastService;
    private MemberTagService memberTagService;
    private DjLogService djLogService;
    private MessageSender messageSender;
    private RuleRedShipConfigService ruleRedShipConfigService;
    private MemberService memberService;
    @Resource
    private HomeCertifyKit homeCertifyKit;
    @Autowired
    public void setExamRecordStuService(com.zxy.product.examstu.api.ExamRecordService examRecordStuService) {
        this.examRecordStuService = examRecordStuService;
    }

    @Autowired
    public void setAudienceKService(AudienceKService audienceKService) {
        this.audienceKService = audienceKService;
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @Autowired
    public void setRuleRedShipConfigService(RuleRedShipConfigService ruleRedShipConfigService) {
        this.ruleRedShipConfigService = ruleRedShipConfigService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setIntelligentBroadcastService(IntelligentBroadcastService intelligentBroadcastService) {
        this.intelligentBroadcastService = intelligentBroadcastService;
    }

    @Autowired
    public void setAudienceItemService(AudienceItemService audienceItemService) {
        this.audienceItemService = audienceItemService;
    }

    @Autowired
    public void setRuleConfigService(RuleConfigService ruleConfigService) {
        this.ruleConfigService = ruleConfigService;
    }

    @Resource
    private MultidimensionalScoringService multidimensionalScoringService;

    @Resource
    private CourseStudyProgressArchivedService courseStudyProgressArchivedService;
    private HistoryCourseStudyProgressService historyCourseStudyProgressService;
    private static final String ROOT_ORGANIZATION_ID = "1";
    private static final Object HOT_VISIT_LOCK = new Object();
    private static final String EXAM_ID_DJ = "bbfd30e9-2fe4-42b2-a67c-4715191194a4";
    //热门内容更新 -- LJY 2022/06/13
    private DisplayService displayService;
    private static final String HOT_VISITS_SERVICE_SWITCH = "hot-visits-service-switch";
    private static final String HOT_VISITS_KEY_NEW = "hot-visits-key-1-";

    private  CaptionService captionService;

    private BusinessEmergencyService businessEmergencyService;

    private Cache internalSwitchCache;
    private Cache courseCache;

    private final  static String  COURSE_KEY ="relatedCourses";
    private final  static String  SUBJECT_KEY ="relatedSubject";
    private final  static String  DATA_KEY ="data";

    private final  static String  HOT_CONTENT ="hotContent";
    private final  static String  GUESS_YOU_LIKE ="guessYouLike";

    private static final String REDIS_RESOURCE_TOP3 = "behaviorAcquisitionRedis-resourceTop3";


    @Autowired
    public void setCaptionService(CaptionService captionService) {
        this.captionService = captionService;
    }

    @Autowired
    public void setCache(CacheService cache) {
        this.internalSwitchCache = cache.create("anti-corruption", InternalSwitch.KEY);
        this.courseCache = cache.create(HOME_PAGE_KEY);
    }

    @Autowired
    public void setInternalSwitchService(InternalSwitchService internalSwitchService) {
        this.internalSwitchService = internalSwitchService;
    }

    @Autowired
    public void setBusinessEmergencyService(BusinessEmergencyService businessEmergencyService) {
        this.businessEmergencyService = businessEmergencyService;
    }

    @Autowired
    public void setDisplayService(DisplayService displayService) {
        this.displayService = displayService;
    }

    @Value("${hot.visit.rank.cache.expire.time:600}")
    private Integer hotVistRankCacheExpireTime;

    @Autowired
    public void setStudioService(StudioService studioService) {
        this.studioService = studioService;
    }

    @Autowired
    public void setGrantService(GrantService grantService) {
		this.grantService = grantService;
	}

    private Cache cache;
    @Autowired
    public void setCache(Cache cache){this.cache = cache;}
    @Autowired
    public void setAdminController(CourseInfoAdminController adminController) {
        this.adminController = adminController;
    }

    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setTopicService(TopicService topicService) {
        this.topicService = topicService;
    }


    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setFileService(FileService fileService) {
        this.fileService = fileService;
    }

    @Autowired
    public void setBusinessTopicService(BusinessTopicService businessTopicService) {
        this.businessTopicService = businessTopicService;
    }

    @Autowired
    public void setMemberTopicService(MemberTopicService memberTopicService) {
        this.memberTopicService = memberTopicService;
    }

    @Autowired
    public void setCourseInfoAdminKit(CourseInfoAdminKit courseInfoAdminKit) {
        this.courseInfoAdminKit = courseInfoAdminKit;
    }

    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }

    @Autowired
    public void setThematicService(ThematicService thematicService) {
        this.thematicService = thematicService;
    }

    @Autowired
    public void setThematicMemberService(ThematicMemberService thematicMemberService) {
        this.thematicMemberService = thematicMemberService;
    }

    @Override
    public void setEnvironment(Environment environment) {
        secret = environment.getProperty("party.screen.secret", "a19de0eb80013fb468fd2ee0cc672211");
    }

    @Autowired
    public void setCertificateRecordService(CertificateRecordService certificateRecordService) {
        this.certificateRecordService = certificateRecordService;
    }

    @Autowired
    public void setExamRecordService(ExamRecordService examRecordService) {
        this.examRecordService = examRecordService;
    }

    @Autowired
    public void setStudioContentService(StudioContentService studioContentService) {
        this.studioContentService = studioContentService;
    }

    @Autowired
    public void setHistoryCourseStudyProgressService(HistoryCourseStudyProgressService historyCourseStudyProgressService) {
        this.historyCourseStudyProgressService = historyCourseStudyProgressService;
    }

    @Autowired
    public void setMemberTagService(MemberTagService memberTagService) {
        this.memberTagService = memberTagService;
    }

    @Autowired
    public void setDjLogService(DjLogService djLogService) {
        this.djLogService = djLogService;
    }

    @RequestMapping(value = "ids", method = RequestMethod.GET)
    @Param(name = "ids", required = true)
    @JSON("id,name,description,cover,coverPath,credit,courseTime,studyMemberCount,visits,publishClient,avgScore,url,status")
    @JSON("organization.(id,name)")
    public List<CourseInfo> searchByIds(RequestContext requestContext) {
        String ids = requestContext.getString("ids");
        return courseInfoService.findByIds(Arrays.asList(ids.split(",")));
    }

    @RequestMapping(value = "ids-study-filter", method = RequestMethod.GET)
    @Param(name = "ids", required = true)
    @JSON("id,name,description,cover,coverPath,credit,courseTime,studyMemberCount,visits,publishClient,avgScore")
    @JSON("organization.(id,name)")
    public List<CourseInfo> searchByIdsFilter(RequestContext requestContext, Subject<Member> memberSubject) {
        String ids = requestContext.getString("ids");
        return courseInfoService.findByIds(Arrays.asList(ids.split(",")), memberSubject.getCurrentUserId());
    }

    @RequestMapping(value = "/course-chapter/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("*")
    @JSON("courseChapterSections.(*)")
    @JSON("courseChapterSections.courseInfo.(id, code, status)")
    public List<CourseChapter> findCourseChapterByCourseId(RequestContext requestContext,Subject<Member> subject) {
        String id = requestContext.getString("id");
        List<CourseChapter> courseChapters = courseInfoService.findCourseChapterByCourseId(id);
        // 查询主题绑定的评分表
        Map<String, MultidimensionalScoring> scoringMap = multidimensionalScoringService.findScoringIdAndNameBySubjectId(id, Optional.empty()).stream()
                .filter(c -> Objects.nonNull(c.getTopicId())).collect(Collectors.toMap(MultidimensionalScoring::getTopicId, v -> v, (v1, v2) -> v2));
        courseChapters.forEach(courseChapter -> {
            Optional<MultidimensionalScoring> multidimensionalScoringOpt = Optional.ofNullable(scoringMap.get(courseChapter.getId()));
            multidimensionalScoringOpt.ifPresent(multidimensionalScoring->{
                courseChapter.setScoringId(multidimensionalScoring.getId());
                courseChapter.setScoringName(multidimensionalScoring.getName());
            });
        });
        return courseChapters;
    }

    @RequestMapping(value = "/study-map-chapter/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("*")
    @JSON("courseChapters.(*)")
    @JSON("courseChapters.courseChapterSections.(*)")
    public List<CourseAbility> findStudyMapChapterByCourseId(RequestContext requestContext) {
        String id = requestContext.getString("id");
        return courseInfoService.findCourseAbilityByCourseId(id);
    }

    /**
     * 红船查询课程课件信息接口（MP3、MP4、xls、xlsx、doc、docx、ppt、pptx、pdf、txt），并按照上传顺序展示
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/red-ship-course-chapter", method = RequestMethod.GET)
    @Permitted(perms = "course-study/course-info")
    @Param(name = "courseId", required = true)
    @JSON("courseChapter.(id,courseId,name,sequence)")
    @JSON("courseChapter.courseChapterSection.(id,name,sectionType,sequence,attachmentId)")
    @JSON("caption.(id,courseId,name,sequence)")
    @JSON("caption.courseChapterSection.(id,name,sectionType,sequence,attachmentId)")
    public Map<String, Object> findRedShipCourseChapterByCourseId(RequestContext requestContext) {
        String courseId = requestContext.getString("courseId");
        Map<String, Object> map = Maps.newHashMap();
        List<CourseChapter> courseChapters = courseInfoService.findRedShipCourseChapterByCourseId(courseId);
        List<String> attachmentIds = courseChapters.stream().map(cc -> cc.getCourseChapterSection().getAttachmentId()).collect(Collectors.toList());
        // 查询资源后缀
        List<String> ids = fileService.findByIds(attachmentIds).stream()
                .filter(r -> Arrays.asList(CourseRedShipAuditDetail.RED_SHIP_EXTENTION).contains(r.getExtention()))
                .map(Attachment::getId)
                .collect(Collectors.toList());
        map.put("courseChapter", com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(ids) ? new ArrayList<>() : courseChapters.stream().filter(c -> ids.contains(c.getCourseChapterSection().getAttachmentId())).collect(Collectors.toList()));
        //查询字幕
        List<String> captionAttachmentIds = captionService.findGeneratedAttachmentIds(courseId);
        map.put("caption", com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(captionAttachmentIds) ? new ArrayList<>() : courseChapters.stream().filter(c -> captionAttachmentIds.contains(c.getCourseChapterSection().getAttachmentId())).collect(Collectors.toList()));
        return map;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "flag",type = Integer.class)
    @JSON("*")
    @JSON("organization.(id,name)")
    @JSON("releaseOrg.(id,name)")
    @JSON("releaseUser.(id,name,fullName)")
    @JSON("developUser.(id,name,fullName)")
    @JSON("createUser.(id,name,fullName)")
    @JSON("category.(id,name)")
    @JSON("courseChapters.(*)")
    @JSON("courseAttachments.(*)")
    @JSON("courseChapters.courseChapterSections.(*)")
    @JSON("audienceItems.(*)")
    @JSON("photos.(*)")
    @JSON("businessTopics.(*)")
    @JSON("advertisings.(*)")
    @JSON("textAreas.(*)")
    public CourseInfo get(RequestContext context) {
        Optional<Integer> flag = context.getOptionalInteger("flag");
        CourseInfo courseInfo = courseInfoService.get(context.get("id", String.class));

        if (!flag.isPresent()) {
            List<CourseChapter> list=new ArrayList<>();
            List<CourseChapter> courseChapters = courseInfo.getCourseChapters();
            if (ObjectUtils.isEmpty(courseChapters)) return courseInfo;
            for (CourseChapter courseChapter : courseChapters) {
                List<CourseChapterSection> courseChapterSections = courseChapter.getCourseChapterSections();
                if (ObjectUtils.isEmpty(courseChapterSections))continue;
                List<CourseChapterSection> collect = courseChapterSections.stream().filter(o -> !Integer.valueOf(CourseInfo.SOURCE_CLOUD).equals(o.getIsCloud())).collect(Collectors.toList());
                courseChapter.setCourseChapterSections(collect);
                list.add(courseChapter);
            }
            courseInfo.setCourseChapters(list);
        }
        return courseInfo;
    }

    /**
     * 查询课程基本信息和附件，其他都不查
     * @param context
     * @return
     */
    @RequestMapping(value = "/course/{id}", method = RequestMethod.GET)
    @Permitted
    @Param(name = "id", required = true)
    @JSON("*")
    @JSON("organization.(id,name)")
    @JSON("releaseOrg.(id,name)")
    @JSON("releaseUser.(id,name,fullName)")
    @JSON("developUser.(id,name,fullName)")
    @JSON("createUser.(id,name,fullName)")
    @JSON("category.(id,name)")
    @JSON("courseAttachments.(*)")
    @JSON("relativeGensee.(id,subject)")
    @JSON("deputyCategories.(id,name)")
    public CourseInfo getCourse(RequestContext context) {
        CourseInfo courseInfo = courseInfoService.getCourse(context.get("id", String.class));
        if (courseInfo.getSource().equals(CourseInfo.SOURCE_STUDIO)) {
            Optional<StudioContent> studioContent = studioContentService.getByBusinessId(courseInfo.getId());
            if (studioContent.isPresent()) {
                courseInfo.setCourseType(studioContent.get().getCourseType());
            }
        }
        return courseInfo;
    }


    @RequestMapping(value = "/share/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("*")
    @JSON("releaseOrg.(id,name)")
    @JSON("courseChapters.(*)")
    @JSON("courseAttachments.(*)")
    @JSON("courseChapters.courseChapterSections.(*)")
    @JSON("photos.(*)")
    @JSON("businessTopics.(*)")
    @JSON("advertisings.(*)")
    @JSON("textAreas.(*)")
    public CourseInfo getShare(RequestContext context) {
        return courseInfoService.get(context.get("id", String.class));
    }

    @RequestMapping(method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "businessType", type = Integer.class) // 业务类型，0-课程；1-学习路径；2-专题
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "categoryId")
    @Param(name = "code")
    @Param(name = "status", type = Integer.class)
    @Param(name = "source", type = Integer.class)
    @Param(name = "releaseUserId") // 发布人
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "shelveBeginDate", type = String.class)
    @Param(name = "shelveEndDate", type = String.class)
    @Param(name = "beginBeginDate", type = Long.class)
    @Param(name = "beginEndDate", type = Long.class)
    @Param(name = "endBeginDate", type = Long.class)
    @Param(name = "endEndDate", type = Long.class)
    @Param(name = "shelveBeginTime", type = Long.class)
    @Param(name = "shelveEndTime", type = Long.class)
    @Param(name = "subjectType", type = Integer.class) // 专题类型，1-普通；2-个性化
    @Param(name = "selectIds", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,name,type,publishType,createTime,source,status,portalNum,courseHour,credit,beginDate,endDate,cover,coverPath,shelveTime,isPublic,code,publishClient,visits,url,description,styles, descriptionText)")
    @JSON("items.organization.(id,name)")
    @JSON("items.createUser.(id,name)")
    @JSON("items.releaseUser.(id,name)")
    @JSON("items.category.(id,name)")
    public PagedResult<CourseInfo> findPage(RequestContext context, Subject<Member> subject) {
        int businessType = context.getOptionalInteger("businessType").orElse(0);
        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));

        // 替换权限 调用系统权限
		List<String> GrantedOrganizationIds = grantService.findGrantedOrganization(
				subject.getCurrentUserId(), CourseInfoService.URI, Optional.empty(), Optional.empty(),
                Optional.empty(), Optional.empty(), context.getOptionalString("organizationId"), Optional.empty(),
                Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());

        return courseInfoService.find(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                subject.getCurrentUserId(),
                businessType,
                context.getOptionalString("name"),
                context.getOptionalString("organizationId"),
                context.getOptionalString("categoryId"),
                context.getOptionalString("code"),
                context.getOptionalInteger("status"),
                context.getOptionalInteger("source"),
                context.getOptionalString("releaseUserId"),
                context.getOptionalInteger("publishClient"),
                StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginDate")),
                StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndDate")),
                context.getOptional("subjectType", Integer.class),
                context.getOptional("beginBeginDate", Long.class),
                context.getOptional("beginEndDate", Long.class),
                context.getOptional("endBeginDate", Long.class),
                context.getOptional("endEndDate", Long.class),
                context.getOptional("shelveBeginTime", Long.class),
                context.getOptional("shelveEndTime", Long.class),
                selectIds,
                GrantedOrganizationIds
        );
    }

    @RequestMapping(method = RequestMethod.GET, value = "/select")
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "client", type = Integer.class) // 课程类型
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "category")
    @Param(name = "selectIds")
    @Param(name = "uri")
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,source,status,portalNum,organizationId,courseHour,credit,beginDate,endDate,cover,coverPath,shelveTime,isPublic,code,publishClient,visits,description)")
    @JSON("items.organization.(id,name)")
    @JSON("items.category.(id,name)")
    public PagedResult<CourseInfo> findSelect(RequestContext context, Subject<Member> subject) {
        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));

        // 替换权限 调用系统权限
		List<String> GrantedOrganizationIds = grantService.findGrantedOrganization(
				subject.getCurrentUserId(), CourseInfoService.URI, Optional.empty(), Optional.empty(),
                Optional.empty(), Optional.empty(), context.getOptionalString("organizationId"), Optional.empty(),
                Optional.empty()).stream().map(a -> a.getId()).collect(Collectors.toList());

        return courseInfoService.findSelect(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                subject.getCurrentUserId(),
                context.getOptionalInteger("client"),
                context.getOptionalString("name"),
                context.getOptionalString("organizationId"),
                context.getOptionalString("category"),
                context.getOptionalString("uri"),
                selectIds,
                GrantedOrganizationIds);
    }

    @RequestMapping(value = "/download", method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")
    @Param(name = "status", type = Integer.class)
    @Param(name = "createTime")
    @Param(name = "organizationId")
    @Param(name = "type", type = Integer.class) // 课程类型
    @Param(name = "itemScromType", type = Integer.class)
    @Param(name = "supportApp", type = Integer.class)
    @Param(name = "businessType", type = Integer.class) // 业务类型，0-课程；1-学习路径；2-专题
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.EXPORT, fisrtAction = "导出", desc = "导出课程详情")
    public void download(RequestContext context, Subject<Member> subject) throws IOException {
        String filename = "在线课程导出信息.xlsx";
        if (BrowserUtil.isMSBrowser(context.getRequest().getHeader("User-Agent"))) {
            filename = URLEncoder.encode(filename, "UTF-8");
        } else {
            filename = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
        }
        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        List<CourseInfo> list = this.adminController.pagedResult(context, subject).getItems();
        Writer writer = new ExcelWriter();

        List<Topic> topicMap = this.getTopicMap(BusinessTopic.BUSINESS_TYPE_COURSE);

        writer.sheet("在线课程导出信息", list)
                .indexColumn(Optional.empty())
                .field("课程名称", CourseInfo::getName)
                .field("课程编码", CourseInfo::getCode)
                .field("归属部门", courseInfo -> courseInfo.getOrganization().getName())
                .field("适用终端", CourseInfo::getPublishClient, "pc&app", "1", "pc", "2", "app")
                .field("分类/序列", courseInfo -> courseInfo.getCategory().getName())
                .field("课程状态", courseInfo -> courseInfo.getStatus(), "未发布", "1", "已发布", "2", "取消发布", "3", "测试中", "4", "已拒绝")
                .field("来源", courseInfo -> courseInfo.getSource(), "其他",
                        "0","面授培训", "1", "内部提供", "2", "外部采购", "3", "网大开发", "4","直播课堂","5","mooc","6","网络资源")
                .field("发布人", courseInfo -> courseInfo.getReleaseUser().getFullName())
                .field("首次发布时间", CourseInfo::getShelveTime, x -> StringUtils.long2ShortDate(x))
                .field("发布部门", courseInfo -> courseInfo.getReleaseOrg().getName())
                .field("讲师", CourseInfo::getLecturer)
                .field("来源明细", CourseInfo::getSourceDetail)
                .field("课程简介", CourseInfo::getDescription)
                .field("标签", courseInfo -> this.getTopicsByCourseId(courseInfo.getId(), topicMap),
                        x-> x.stream().map(Topic::getName).reduce((a,b)->a +","+ b).orElse(""));
        writer.write(response.getOutputStream());

    }


    @RequestMapping(value = "/export-update", method = RequestMethod.POST)
    @Permitted
    @Param(name = "ids", required = true)
    @Param(name = "fields", required = true)
    public void exportUpdate(RequestContext context) throws IOException {
        String ids = context.getString("ids");
        String fields = context.getString("fields");

        Map<String, Function<CourseInfo, ?>> map = new HashMap<>();
        map.put("courseHour", CourseInfo::getCourseHour);
        map.put("credit", CourseInfo::getCredit);
        map.put("beginDate", CourseInfo::getBeginDate);
        map.put("endDate", CourseInfo::getEndDate);
        map.put("price", CourseInfo::getPrice);
        map.put("description", CourseInfo::getDescription);

        List<CourseInfo> list = courseInfoService.findByIds(Arrays.asList(ids.split(",")));
        Writer writer = new ExcelWriter();
        Sheet<CourseInfo> c = writer.sheet("课程列表", list).field("课程Id(请不要修改)", CourseInfo::getId);
        for (String field : fields.split(",")) {
            if (map.containsKey(field)) {
                c.field(field, map.get(field));
            }
        }

        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=batchUpdate-course.xlsx");
        writer.write(response.getOutputStream());

    }

    @RequestMapping(value = "/{id}", method = RequestMethod.DELETE)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "courseName", type = String.class)
    @JSON("*")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.DELETE, fisrtAction = "删除", desc = "删除课程《{0}》", params = {"courseName"})
    public int delete(RequestContext context) {
        String id = context.get("id", String.class);
        return courseInfoService.delete(id,Optional.ofNullable(historyCourseStudyProgressService.totalStudyMember(id)).orElse(0));
    }

    @RequestMapping(method = RequestMethod.POST)
    @Permitted
    @Param(name = "name", required = true) // 课程名称
    @Param(name = "beginDate", type = Long.class) // 开始时间
    @Param(name = "endDate", type = Long.class) // 结束时间
    @Param(name = "courseHour", type = Integer.class) // 课时
    @Param(name = "credit", type = Integer.class) // 学分
    @Param(name = "courseTime", type = Integer.class) // 课程时长
    @Param(name = "courseSecond", type = Integer.class) // 课程时长秒
    @Param(name = "description") // 课程简介
    @Param(name = "descriptionText") // 课程简介-纯文本格式
    @Param(name = "releaseMemberId") // 发布人
    @Param(name = "releaseOrgId") // 发布部门
    @Param(name = "organizationId", required = true) // 所属部门
    @Param(name = "developUserId") // 开发人
    @Param(name = "developTime") // 开发时间
    @Param(name = "code") // 课程编码
    @Param(name = "certificateId") // 课程证书
    @Param(name = "price", type = Integer.class) // 定价
    @Param(name = "courseChapters") // 课程章节
    @Param(name = "courseAttachments") // 课程附件
    @Param(name = "categoryId") // 目录id
    @Param(name = "addType", type = Integer.class) // 添加类别 普通模式,章节模式
    @Param(name = "audienceItems") // 课程受众对象
    @Param(name = "learnSequence", type = Integer.class) // 课程学习顺序
    @Param(name = "shelves") // 上架(发布)信息
    @Param(name = "businessType", type = Integer.class) // 业务类型，0-课程；1-学习路径；2-专题
    @Param(name = "studyDays", type = Integer.class) // 建议学习天数
    @Param(name = "publishClient", type = Integer.class, required = true) // 发布终端，0:
    @Param(name = "publishType", type = Integer.class) // 发布类型 0-定向(测试) 1-正式
    @Param(name = "cover") // 头像
    @Param(name = "coverPath") //头像路径
    @Param(name = "styles") // 风格配置
    @Param(name = "topicIds") // 相关话题
    @Param(name = "source", type = Integer.class, required = true) // 课程来源明细
    @Param(name = "sourceDetail") // 课程来源明细
    @Param(name = "lecturer") // 讲师
    @Param(name = "shareSub", type = Integer.class) // 分享给下级
    @Param(name = "advertisings") // 专题广告
    @Param(name = "photos") // 专题相册
    @Param(name = "textAreas") // 专题文字区域
    @Param(name = "url") // 个性化专题链接地址
    @Param(name = "isPublic", type = Integer.class) // 非公开课程
    @JSON("id, name,description, scromType,createTime,supportApp,source,status,portalNum")
    public CourseInfo save(RequestContext context, Subject<Member> subject) {
        List<CourseChapter> courseChapters = parseCourseChapters(context.getOptionalString("courseChapters"));
        List<CourseAttachment> courseAttachments = parseCourseAttachment(context.getOptionalString("courseAttachments"));
        List<AudienceItem> audienceItems = parseAudienceItems(context.getOptionalString("audienceItems"));
        int businessType = context.getOptionalInteger("businessType").orElse(0);
        Optional<CourseShelves> courseShelves = parseShelves(context.getOptionalString("shelves"));
        if (courseShelves.isPresent()) {
            CourseInfo courseInfo = new CourseInfo();
            courseInfo.setCourseChapters(courseChapters);
            courseInfo.setAudienceItems(audienceItems);
            courseInfo.setBusinessType(businessType);
            courseInfo.setCourseAttachments(courseAttachments);
            courseInfo.setUrl(context.getOptionalString("url").orElse(null));
            courseInfo.setStyles(context.getOptionalString("styles").orElse(null));
            validationCourse(courseInfo);
        }
        List<SubjectAdvertising> advertisingList = parseAdvertising(context.getOptionalString("advertisings"));
        List<CoursePhoto> photos = parsePhotos(context.getOptionalString("photos"));
        List<SubjectTextArea> textAreas = parseTextArea(context.getOptionalString("textAreas"));

        CourseInfo info = courseInfoService.save(context.get("name", String.class), subject.getCurrentUserId(),
                businessType, context.getInteger("publishClient"), context.getString("organizationId"),
                context.getInteger("source"),
                context.getOptionalInteger("publishType"), context.getOptionalString("certificateId"),
                context.getOptional("beginDate", Long.class),
                context.getOptional("endDate", Long.class),
                context.getOptionalInteger("courseHour"), context.getOptionalInteger("credit"),
                context.getOptionalInteger("courseTime"), context.getOptionalString("description"),
                context.getOptionalString("descriptionText"), context.getOptionalString("releaseMemberId"),
                context.getOptionalString("releaseOrgId"), context.getOptionalString("developUserId"),
                StringUtils.dateString2OptionalLong(context.getOptionalString("developTime")),
                context.getOptionalString("code"), context.getOptionalInteger("price"),
                context.getOptional("courseSecond", Integer.class), context.getOptionalString("categoryId"),
                context.getOptional("addType", Integer.class), context.getOptional("learnSequence", Integer.class),
                context.getOptional("studyDays", Integer.class), context.getOptionalString("cover"),context.getOptionalString("coverPath"),
                context.getOptionalString("sourceDetail"), context.getOptionalString("lecturer"), context.getOptionalInteger("shareSub"),
                courseChapters, courseAttachments, audienceItems, courseShelves, context.getOptionalString("styles"),
                context.getOptionalString("topicIds"), context.getOptionalInteger("isPublic"),
                advertisingList, photos, textAreas, context.getOptionalString("url"));

        return info;
    }

    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "name", required = true) // 课程名称
    @Param(name = "publishClient", type = Integer.class, required = true) // 发布终端，0:
    // 全部,
    // 1:
    // PC,
    // 2:
    // APP
    @Param(name = "publishType", type = Integer.class) // 发布类型 0-定向(测试) 1-正式
    @Param(name = "beginDate") // 开始时间
    @Param(name = "endDate") // 结束时间
    @Param(name = "courseHour", type = Integer.class) // 课时
    @Param(name = "credit", type = Integer.class) // 学分
    @Param(name = "courseTime", type = Integer.class) // 课程时长分
    @Param(name = "courseSecond", type = Integer.class) // 课程时长秒
    @Param(name = "description") // 课程简介
    @Param(name = "descriptionText") // 课程简介-纯文本格式
    @Param(name = "releaseMemberId") // 发布人
    @Param(name = "releaseOrgId") // 发布部门
    @Param(name = "organizationId") // 所属部门
    @Param(name = "developUserId") // 开发人
    @Param(name = "developTime") // 开发时间
    @Param(name = "code") // 课程编码
    @Param(name = "certificateId") // 课程证书
    @Param(name = "price", type = Integer.class) // 定价
    @Param(name = "courseChapters") // 课程章节
    @Param(name = "courseAttachments") // 课程附件
    @Param(name = "categoryId") // 目录id
    @Param(name = "addType", type = Integer.class) // 添加类别 普通模式,章节模式
    @Param(name = "audienceItems") // 课程受众对象
    @Param(name = "learnSequence", type = Integer.class) // 课程学习顺序
    @Param(name = "shelves") // 上架(发布)信息
    @Param(name = "studyDays", type = Integer.class) // 建议学习天数
    @Param(name = "cover") // 头像
    @Param(name = "coverPath") //头像路径
    @Param(name = "styles") // 风格配置
    @Param(name = "topicIds") // 相关话题
    @Param(name = "source", type = Integer.class) // 课程来源明细
    @Param(name = "sourceDetail") // 课程来源明细
    @Param(name = "lecturer") // 讲师
    @Param(name = "shareSub", type = Integer.class) // 分享给下级
    @Param(name = "advertisings") // 专题广告
    @Param(name = "photos") // 专题相册
    @Param(name = "textAreas") // 专题文字区域
    @Param(name = "url") // 个性化专题链接地址
    @Param(name = "isPublic", type = Integer.class) // 非公开课程
    @JSON("name,scromType,createTime,supportApp,source,status,portalNum")
    public CourseInfo update(RequestContext context) {
        List<CourseChapter> courseChapters = parseCourseChapters(context.getOptional("courseChapters", String.class));
        List<CourseAttachment> courseAttachments = parseCourseAttachment(
                context.getOptional("courseAttachments", String.class));
        List<AudienceItem> audienceItems = parseAudienceItems(context.getOptionalString("audienceItems"));
        Optional<CourseShelves> courseShelves = parseShelves(context.getOptionalString("shelves"));
        if (courseShelves.isPresent()) {
            CourseInfo courseInfo = new CourseInfo();
            courseInfo.setCourseChapters(courseChapters);
            courseInfo.setAudienceItems(audienceItems);
            courseInfo.setBusinessType(CourseInfo.BUSINESS_TYPE_COURSE);
            courseInfo.setCourseAttachments(courseAttachments);
            validationCourse(courseInfo);
        }
        Optional<String> beginDate = context.getOptionalString("beginDate").map(begin -> begin.contains("-") ? begin : null);
        Optional<String> endDate = context.getOptionalString("endDate").map(end -> end.contains("-") ? end : null);
        Optional<String> developTime = context.getOptionalString("developTime").map(develop -> develop.contains("-") ? develop : null);
        List<SubjectAdvertising> advertisingList = parseAdvertising(context.getOptionalString("advertisings"));
        List<CoursePhoto> photos = parsePhotos(context.getOptionalString("photos"));
        List<SubjectTextArea> textAreas = parseTextArea(context.getOptionalString("textAreas"));

        CourseInfo info = courseInfoService.update(context.get("id", String.class), context.getInteger("publishClient"),
                context.getInteger("source"),
                context.getOptionalInteger("publishType"), context.getOptional("name", String.class),
                StringUtils.dateString2OptionalLong(beginDate),
                StringUtils.dateString2OptionalLong(endDate),
                context.getOptional("courseHour", Integer.class), context.getOptional("credit", Integer.class),
                context.getOptional("courseTime", Integer.class), context.getOptional("description", String.class),
                context.getOptional("descriptionText", String.class),
                context.getOptional("releaseMemberId", String.class), context.getOptional("releaseOrgId", String.class),
                context.getOptional("organizationId", String.class), context.getOptional("developUserId", String.class),
                StringUtils.dateString2OptionalLong(developTime),
                context.getOptional("code", String.class), context.getOptional("price", Integer.class),
                context.getOptional("courseSecond", Integer.class), context.getOptional("certificateId", String.class),
                context.getOptional("categoryId", String.class), context.getOptional("learnSequence", Integer.class),
                context.getOptionalInteger("addType"), context.getOptional("studyDays", Integer.class),
                context.getOptionalString("cover"),context.getOptionalString("coverPath"),
                context.getOptionalString("sourceDetail"), context.getOptionalString("lecturer"), context.getOptionalInteger("shareSub"),
                courseChapters, courseAttachments, audienceItems,
                courseShelves, context.getOptionalString("styles"), context.getOptionalString("topicIds"),
                context.getOptionalInteger("isPublic"),
                advertisingList, photos, textAreas, context.getOptionalString("url"));
        return info;
    }

    @RequestMapping(value = "/updateSubject/{id}", method = RequestMethod.POST)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "name", required = true) // 专题名称
    @Param(name = "publishClient", type = Integer.class, required = true) // 发布终端，0:全部, 1: PC, 2: APP
    @Param(name = "publishType", type = Integer.class) // 发布类型 0-定向(测试) 1-正式
    @Param(name = "beginDate", type = Long.class) // 开始时间
    @Param(name = "endDate", type = Long.class) // 结束时间
    @Param(name = "description") // 专题简介
    @Param(name = "descriptionText") // 专题简介-纯文本格式
    @Param(name = "releaseMemberId") // 发布人
    @Param(name = "releaseOrgId") // 发布部门
    @Param(name = "organizationId") // 所属部门
    @Param(name = "code") // 专题编码
    @Param(name = "courseChapters") // 专题章节
    @Param(name = "courseAttachments") // 专题附件
    @Param(name = "audienceItems") // 专题受众对象
    @Param(name = "learnSequence", type = Integer.class) // 专题学习顺序
    @Param(name = "shelves") // 上架(发布)信息
    @Param(name = "studyDays", type = Integer.class) // 建议学习天数
    @Param(name = "cover") // 头像
    @Param(name = "coverPath") // 头像
    @Param(name = "styles") // 风格配置
    @Param(name = "topicIds") // 相关话题
    @Param(name = "shareSub", type = Integer.class) // 分享给下级
    @Param(name = "advertisings") // 专题广告
    @Param(name = "photos") // 专题相册
    @Param(name = "textAreas") // 专题文字区域
    @Param(name = "url") // 个性化专题链接地址
    @JSON("id")
    public CourseInfo updateSubject(RequestContext context) {
        List<CourseChapter> courseChapters = parseCourseChapters(context.getOptional("courseChapters", String.class));
        List<CourseAttachment> courseAttachments = parseCourseAttachment(context.getOptional("courseAttachments", String.class));
        List<AudienceItem> audienceItems = parseAudienceItems(context.getOptionalString("audienceItems"));
        Optional<CourseShelves> courseShelves = parseShelves(context.getOptionalString("shelves"));
        if (courseShelves.isPresent()) {
            CourseInfo courseInfo = new CourseInfo();
            courseInfo.setCourseChapters(courseChapters);
            courseInfo.setAudienceItems(audienceItems);
            courseInfo.setBusinessType(CourseInfo.BUSINESS_TYPE_SUBJECT);
            courseInfo.setUrl(context.getOptionalString("url").orElse(null));
            courseInfo.setStyles(context.getOptionalString("styles").orElse(null));
            courseInfo.setCourseAttachments(courseAttachments);
            validationCourse(courseInfo);
        }
        List<SubjectAdvertising> advertisingList = parseAdvertising(context.getOptionalString("advertisings"));
        List<CoursePhoto> photos = parsePhotos(context.getOptionalString("photos"));
        List<SubjectTextArea> textAreas = parseTextArea(context.getOptionalString("textAreas"));

        return courseInfoService.updateSubject(context.get("id", String.class),
                context.getInteger("publishClient"),
                context.getOptionalInteger("publishType"),
                context.getOptional("name", String.class),
                context.getOptional("beginDate", Long.class),
                context.getOptional("endDate", Long.class),
                context.getOptional("description", String.class),
                context.getOptional("descriptionText", String.class),
                context.getOptional("releaseMemberId", String.class),
                context.getOptional("releaseOrgId", String.class),
                context.getOptional("organizationId", String.class),
                context.getOptional("code", String.class),
                context.getOptional("learnSequence", Integer.class),
                context.getOptional("studyDays", Integer.class),
                context.getOptionalString("cover"),
                context.getOptionalString("coverPath"),
                context.getOptionalInteger("shareSub"),
                courseChapters,
                courseAttachments,
                audienceItems,
                courseShelves,
                context.getOptionalString("styles"),
                context.getOptionalString("topicIds"),
                advertisingList,
                photos,
                textAreas,
                context.getOptionalString("url"));
    }

    @RequestMapping(value = "/submit-person", method = RequestMethod.GET)
    @Permitted
    @Param(name = "ids", required = true)
    @JSON("*")
    public Map<String, Integer> countSubmitPerson(RequestContext context,Subject<Member> subject) {
        return courseInfoService.countSubmitPerson(context.get("ids", String.class).split(","),subject.getCurrentUserId());
    }

    /**
     * 课程退库回库操作
     * @param context
     * @param memberSubject
     * @return
     */
    @RequestMapping(value = "/retreat", method = RequestMethod.POST)
    @Permitted
    @Param(name = "courseId", required = true)
    @Param(name = "courseName")
    @Param(name = "status", type = Integer.class, required = true)
    @JSON("*")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.UPDATE, fisrtAction = "退库", secondAction = "退库课程", desc = "对课程《{0}》进行了退库操作", params = {"courseName"}, businessType = "status", businessValue = "5")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.UPDATE, fisrtAction = "回库", desc = "对课程《{0}》进行了回库操作", params = {"courseName"}, businessType = "status", businessValue = "2")
    public CourseInfo retreat(RequestContext context, Subject<Member> memberSubject) {
        CourseInfo courseInfo = courseInfoService.get(context.getString("courseId"));
        return courseInfoService.editCourseRetreat(courseInfo, context.getInteger("status"),memberSubject.getCurrentUserId());
    }
    /**
     * 课程、专题上下架
     *
     * @param context
     * @return
     */
    @RequestMapping(value = "/shelves", method = RequestMethod.POST)
    @Permitted
    @Param(name = "courseId", required = true)
    @Param(name = "status", type = Integer.class, required = true)
    @Param(name = "shelves") // 发布、取消发布
    @Param(name = "businessType")//0-课程, 2-专题
    @Param(name = "constructionType", type = Integer.class)// 全员共建共享，0=不是，1=是
    @Param(name = "courseName")
    @Param(name = "flag", type = Integer.class, value = "取消发布调用")
    @JSON("*")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.UNPUBLISH, fisrtAction = "取消发布", desc = "取消发布课程《{0}》", ids = {"courseId"}, jsons = {"name"}, keys = {"course-info"}, businessType = "businessType,status,flag", businessValue = "0,2,1")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.PUBLISH, fisrtAction = "重新发布", desc = "重新发布课程《{0}》", ids = {"courseId"}, jsons = {"name"}, keys = {"course-info"}, businessType = "businessType,status,flag", businessValue = "0,1,1")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.UNPUBLISH, fisrtAction = "取消发布", desc = "取消发布专题《{0}》", ids = {"courseId"}, jsons = {"name"}, keys = {"course-info"}, businessType = "businessType,status,flag", businessValue = "2,2,1")
    @Audit(module = "课程管理", subModule = "学习专题", action = Audit.Action.PUBLISH, fisrtAction = "重新发布", desc = "重新发布专题《{0}》", ids = {"courseId"}, jsons = {"name"}, keys = {"course-info"}, businessType = "businessType,status,flag", businessValue = "2,1,1")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "重新发布", desc = "重新发布课程《{0}》（全员共建共享）", params = {"courseName"}, businessType = "type,constructionType,status,businessType", businessValue = "2,1,1,0")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.UPDATE, fisrtAction = "修改", secondAction = "修改课程", desc = "修改课程《{0}》（取消全员共享）", params = {"courseName"}, businessType = "constructionType,businessType", businessValue = "0,0")
    public CourseInfo shelves(RequestContext context, Subject<Member> memberSubject) {
        Integer status = context.getInteger("status");
        if (status == CourseInfo.STATUS_UNPUBLISHED){
            status = CourseInfo.STATUS_THE_SHELVES;
        }
        boolean isSubjectPublic = false;
        if (status.equals(CourseInfo.STATUS_SHELVES) || status.equals(CourseInfo.STATUS_THE_SHELVES)){
            issueJudgment(context.getString("courseId"), memberSubject.getRootOrganizationId());
        }
        Integer subjectStatus = courseCacheService.getSubjectPublishStatus(context.get("courseId", String.class));
        if (null != subjectStatus && subjectStatus.equals(CourseInfo.STATUS_IN_SHELVES)) {
            throw new UnprocessableException(ErrorCode.CourseStatusIsAnnouncing);
        }

        Optional<CourseShelves> courseShelves = parseShelves(context.getOptionalString("shelves"));
        CourseInfo courseInfo = courseInfoService.get(context.getString("courseId"));
        if (status == CourseInfo.STATUS_SHELVES) {
            validationCourse(courseInfo);
            // 如果是专题的话需要验证专题中的专题类型的章节
            if (CourseInfo.BUSINESS_TYPE_SUBJECT.equals(courseInfo.getBusinessType())) {
                courseInfoAdminKit.validationSubjectChapter(courseInfo.getCourseChapters(), Optional.of(courseInfo.getId()));
                //校验专题内课程资源是否都已发布
                List<CourseChapter> courseChapters = courseInfo.getCourseChapters();
                if (!CollectionUtils.isEmpty(courseChapters)) {
                    validateCourses(courseChapters);
                }
            }
            // 发布时如果专题配置证书，需要验证章节中是否含有必修章节
            if (!ObjectUtils.isEmpty(courseInfo.getCertificateId())) {
                courseInfoAdminKit.validationSubjectChapterSectionIsRequired(courseInfo.getCourseChapters());
            }
            if (Integer.valueOf(CourseInfo.SOURCE_STUDIO).equals(courseInfo.getSource()) && CourseInfo.BUSINESS_TYPE_COURSE == courseInfo.getBusinessType()){
                // 查询工作室状态
                studioService.getStudioByBusinessId(courseInfo.getId());
            }
            isSubjectPublic = true;

        }
        captionStatus(status, courseInfo, courseShelves);
        CourseInfo info = courseInfoService.editCourseShelves(courseInfo, status, courseShelves.orElse(new CourseShelves()), context.getOptionalInteger("businessType"), memberSubject.getCurrentUserId(), context.getOptionalInteger("constructionType"));

        if (Objects.equals(subjectStatus, CourseInfo.STATUS_THE_SHELVES) && Objects.equals(context.getInteger("businessType"), CourseInfo.BUSINESS_TYPE_COURSE)){
            List<String> ids = Lists.newArrayList();
            List<CourseChapter> courseChapterByCourseId = courseInfoService.findCourseChapterByCourseId(info.getId());
            courseChapterByCourseId.forEach(r -> r.getCourseChapterSections().forEach(o -> ids.add(o.getAttachmentId())));
            intelligentBroadcastService.updateByAttachmentIds(ids);
        }
        if (status.equals(CourseInfo.STATUS_SHELVES) && ruleRedShipConfigService.getRootOrganizationId(info.getOrganizationId())) {
            messageSender.send(com.zxy.product.course.content.MessageTypeContent.RED_SHIP_AUTO_AUDIT,
                    MessageHeaderContent.COURSE_ID, info.getId());
        }
        senderCourse(courseInfo.getCourseChapters());

        //专题培训班埋点-上架,关联的培训班数据同步
        if (status.equals(CourseInfo.STATUS_SHELVES) && CourseInfo.BUSINESS_TYPE_SUBJECT.equals(courseInfo.getBusinessType())) {

            messageSender.send(com.zxy.product.course.content.MessageTypeContent.IHR_TOPIC_TRAIN_SYNC,
                    MessageHeaderContent.OPERATION_STATE,CourseInfo.OPERATION_STATE_TOPIC_PLUBLISH,
                    MessageHeaderContent.TOPIC_ID, info.getId());
        }else if (status.equals(CourseInfo.STATUS_THE_SHELVES) && CourseInfo.BUSINESS_TYPE_SUBJECT.equals(courseInfo.getBusinessType())){
            //专题培训班埋点-下架,关联的培训班数据同步
            messageSender.send(com.zxy.product.course.content.MessageTypeContent.IHR_TOPIC_TRAIN_SYNC,
                    MessageHeaderContent.OPERATION_STATE,CourseInfo.OPERATION_STATE_TOPIC_UNPUBLISH,
                    MessageHeaderContent.TOPIC_ID, info.getId());

        }
        if (isSubjectPublic) {
                homeCertifyKit.certifySubjectHomeReset(courseInfo.getId());
        }

        Optional<Integer> constructionType = context.getOptionalInteger("constructionType");
        Optional<Integer> contextOptionalInteger = context.getOptionalInteger("businessType");
        if (constructionType.isPresent() && contextOptionalInteger.isPresent() && contextOptionalInteger.get().equals(CourseInfo.BUSINESS_TYPE_COURSE)){
            audienceKService.deleteAudienceConstructionType(context.getString( "courseId"));
        }
        return info;
    }

    private void senderCourse(List<CourseChapter> courseChapters) {
        if (!CollectionUtils.isEmpty(courseChapters)) {
            List<String> courseIds = courseChapters.stream()
                    .filter(x -> x.getCourseChapterSections() != null) // 检查是否为 null
                    .flatMap(x -> x.getCourseChapterSections().stream())
                    .filter(s -> null != s.getSectionType() && CourseChapterSection.SECTION_TYPE_COURSE == s.getSectionType())
                    .map(CourseChapterSection::getResourceId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
            //发送搜索监听,更新课程的引用专题
            if (!CollectionUtils.isEmpty(courseIds)) {
                courseIds.forEach(r -> messageSender.send(MessageTypeContent.COURSE_INFO_UPDATE, MessageHeaderContent.ID, r));
            }
        }
    }

    /**
     * 判断是否跟随发布
     * @param status  课程状态
     * @param course  课程详情
     * @param courseShelves 发布
     */
    private void captionStatus(Integer status, CourseInfo course, Optional<CourseShelves> courseShelves) {
        if (Objects.equals(course.getCaptionFlag(), CourseInfo.CAPTION_FLAG)) {
            if (status.equals(CourseInfo.STATUS_THE_SHELVES)) {
                if (CollectionUtils.isEmpty(captionService.findOtherStatus(course.getId()))) {
                    course.setCaptionOverallStatus(CourseInfo.CAPTION_OVERALL_STATUS_NO_NEED_TO_GENERATE);
                } else {
                    //字幕取消发布
                    captionService.updateCourseStatusAndTime(course.getId(), Caption.CAPTION_STATUS_UNPUBLISH);
                    course.setCaptionOverallStatus(updateCaptionOverallStatus(course.getId()));
                }
            } else if ((status.equals(CourseInfo.STATUS_SHELVES) || status.equals(CourseInfo.STATUS_THE_TEST)) && courseShelves.isPresent() && Objects.equals(course.getCaptionFollowRelease(), CourseInfo.CAPTION_FOLLOW_RELEASE_YES)) {
                //发布所有字幕
                List<Integer> statusAll = captionService.findOtherStatus(course.getId());
                if (!CollectionUtils.isEmpty(statusAll)) {
                    course.setCaptionOverallStatus(CourseInfo.CAPTION_OVERALL_STATUS_NO_NEED_TO_GENERATE);
                }
                if (!statusAll.contains(Caption.CAPTION_STATUS_ANALYSISING)
                        && !statusAll.contains(Caption.CAPTION_STATUS_FAILED)
                        && !statusAll.contains(Caption.CAPTION_STATUS_UPLOAD_FAILED)) {
                    captionService.updateCourseStatusAndTime(course.getId(), Caption.CAPTION_STATUS_PUBLISHED);
                    course.setCaptionOverallStatus(CourseInfo.CAPTION_OVERALL_STATUS_PUBLISH_ALL);
                }
            }
        }
    }

    /**
     * 发布判断,是否是中国移动或者内部组织,开关是否开启,是否再时间内
     */
    private void issueJudgment(String courseId, String rootOrgId) {
        //判断内部开关是否开启
        InternalSwitch internalSwitch = internalSwitchCache.get(InternalSwitch.KEY + KEY, () -> internalSwitchService.findByType(InternalSwitch.PUBLICATION_RESTRICTION), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
        if (!ObjectUtils.isEmpty(internalSwitch) && Objects.equals(internalSwitch.getStatus(), InternalSwitch.switchStatusNO)) {

            //查询是否是全员:受众=中国移动,内部组织
            Integer count = audienceItemService.findAllFullStaff(courseId, rootOrgId, INTERNAL_ORGANIZATION_ID);
            if (count != 0) {
                //白天不允许发布/时间使用逗号隔开,7,18
                List<String> date = Arrays.asList(internalSwitch.getDescribe().split(","));
                //判断是否是再限制的时间内
                publicationRestriction(
                        Integer.parseInt(date.get(1)),
                        Integer.parseInt(date.get(2))
                );
            }
        }
    }

    private void publicationRestriction(int minDate, int maxDate) {
        int hours = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
        if (hours >= minDate && hours <= maxDate) {
            throw new UnprocessableException(ErrorCode.PublicationRestriction);
        }
    }

    /**
     * 验证课程中是否包含未发布的课程活资源
     * @param courseChapters
     */
    private void validateCourses(List<CourseChapter> courseChapters) {
        List<String> sectionIds = null;
        if (!CollectionUtils.isEmpty(courseChapters)) {
            sectionIds = courseChapters.stream().filter(chapter -> !CollectionUtils.isEmpty(chapter.getCourseChapterSections()))
                    .flatMap(chapter -> chapter.getCourseChapterSections().stream())
                    .map(CourseChapterSection::getResourceId)
                    .collect(Collectors.toList());
            //未发布课程的课程数量
            Long num = courseInfoService.getChapterSectionStatus(Optional.ofNullable(sectionIds));
            if (Objects.nonNull(num) && num != 0) {
                throw new ValidationException(ErrorCode.CourseInfoNotInShelvesOrIsNull);
            }
        }
    }

    @RequestMapping(value = "/photo", method = RequestMethod.GET)
    @Permitted
    @Param(name = "subjectId", type = String.class, required = true)
    @JSON("id,name,attachmentId")
    public List<CoursePhoto> findPhotosBySubjectId(RequestContext context) {
        return courseInfoService.findPhotosBySubjectId(context.getString("subjectId"));
    }

    @RequestMapping(value = "/update-photo/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "name", required = true)
    @JSON("id,name,attachmentId")
    public CoursePhoto updatePhoto(RequestContext context) {
        return courseInfoService.updatePhoto(context.getString("id"), context.getString("name"));
    }

    @RequestMapping(value = "/photo", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "ids", type = String.class, required = true)
    @JSON("*")
    public List<String> deletePhotos(RequestContext context) {
        List<String> ids = com.alibaba.fastjson.JSON.parseArray(context.get("ids", String.class), String.class);
        return courseInfoService.deletePhotos(ids);
    }

    @RequestMapping(value = "/update-styles/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "styles", required = true)
    @JSON("*")
    public CourseInfo updateStyles(RequestContext context) {
        return courseInfoService.updateStyles(context.getString("id"), context.getString("styles"));
    }


    /**
     * 根据话题查询相关课程/专题
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/related", method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "topicIds", required = true)
    @Param(name = "categoryId")
    @Param(name = "businessType", type = Integer.class, required = true)
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "courseId", type = String.class)
    @Param(name = "tokenId", type = String.class)
    @JSON("currentPage")
    @JSON("items.(id,name,cover,coverPath,createTime,currentPage,url,studyMemberCount)")
    public Map<String, Object> getRelatedCourse(RequestContext context, Subject<Member> subject) {

        Integer businessType = context.getInteger("businessType");
        Integer pageSize = context.getInteger("pageSize");

        //判断内部开关是否开启
        Map<String, Object> dataMap = relatedSubject(pageSize, businessType, subject.getRootOrganizationId());
        if (!ObjectUtils.isEmpty(dataMap)) {
            return dataMap;
        }

    	//2019.02.28 为应用4月份反腐学习月，临时将相关课程规则改回之前的状态
        Integer page = context.getInteger("page");
        Optional<String> tokenIdOpt = context.getOptionalString("tokenId");

    /*    if(tokenIdOpt.isPresent()){
        	List<CourseInfo> cacheList = new ArrayList<>();
            String  tokenId = tokenIdOpt.get();
            if(!(cache.get(tokenId, List.class) != null && cache.get(tokenId, List.class).size()>0)){
                cacheList =  courseInfoService.getRelated(
                        context.getInteger("page"),
                        context.getInteger("pageSize"),
                        context.getInteger("businessType"),
                        subject.getCurrentUserId(),
                        context.getString("topicIds").split(","),
                        context.getOptionalString("categroyId"),
                        context.getOptionalString("courseId"),
                        context.getOptionalInteger("publishClient"));
                Random random = new Random();
                HashSet<CourseInfo> set = new HashSet<>();
                int cacheSize = cacheList.size();
                for(int j=0; j<cacheSize; j++ ){
                    int randNum = random.nextInt(cacheList.size());
                    boolean b = set.add(cacheList.get(randNum));
                    if(!b){
                        ++cacheSize;
                    }
                }
                cacheList.clear();
                cacheList.addAll(set);
                cache.set(tokenId, cacheList, DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
            }
            cacheList =  cache.get(tokenId, ()->courseInfoService.getRelated(
                    context.getInteger("page"),
                    context.getInteger("pageSize"),
                    context.getInteger("businessType"),
                    subject.getCurrentUserId(),
                    context.getString("topicIds").split(","),
                    context.getOptionalString("categroyId"),
                    context.getOptionalString("courseId"),
                    context.getOptionalInteger("publishClient")), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));

            if(cacheList != null && cacheList.size() > 0){
                if(page > ((cacheList.size() % pageSize==0) ? cacheList.size()/pageSize  : cacheList.size()/pageSize + 1)){
                    page = 1;
                }
                List<CourseInfo> resu = new ArrayList<>();
                for(int i=(page - 1) * pageSize; i<(page*pageSize>cacheList.size() ? cacheList.size() : page*pageSize ); i++){
                    resu.add(cacheList.get(i));
                }
                return ImmutableMap.of("currentPage", page, "items", resu);
            }
        }
*/
        List<CourseInfo> courseInfoList = courseInfoService.getRelatedPage(
                page,
                context.getInteger("pageSize"),
                context.getInteger("businessType"),
                subject.getCurrentUserId(),
                context.getString("topicIds").split(","),
                context.getOptionalString("categroyId"),
                context.getOptionalString("courseId"),
                context.getOptionalInteger("publishClient"));
        // 页码大于1，且查询null，重置页码到第一页
        if(page > 1 && (courseInfoList == null || courseInfoList.size() == 0)) {
            page = 1;
            courseInfoList = courseInfoService.getRelatedPage(
                    page,
                    context.getInteger("pageSize"),
                    context.getInteger("businessType"),
                    subject.getCurrentUserId(),
                    context.getString("topicIds").split(","),
                    context.getOptionalString("categroyId"),
                    context.getOptionalString("courseId"),
                    context.getOptionalInteger("publishClient"));
        }
        //-- 之前的
//    	Integer page = context.getInteger("page");
//        String topicIds = context.getString("topicIds");
//        String cacheKey = RELEATED_COURSE_KEY + "-" + topicIds + "#" + page + "#" + context.getInteger("pageSize");
//    	List<CourseInfo> courseInfoList = cache.get(cacheKey, ()->{
//    		return courseInfoService.getRelated(
//    				context.getInteger("page"),
//                    context.getInteger("pageSize"),
//                    context.getInteger("businessType"),
//                    subject.getCurrentUserId(),
//                    context.getString("topicIds").split(","),
//                    context.getOptionalString("categroyId"),
//                    context.getOptionalString("courseId"),
//                    context.getOptionalInteger("publishClient"));
//    	}, 60 * 60 * 2);
//        // 页码大于1，且查询null，重置页码到第一页
//        if(context.getInteger("page") > 1 && (courseInfoList == null || courseInfoList.size() == 0)) {
//        	page = 1;
//            cacheKey = RELEATED_COURSE_KEY + "-" + topicIds + "#" + page + "#" + context.getInteger("pageSize");
//            courseInfoList = cache.get(cacheKey, () -> {
//                return courseInfoService.getRelated(
//                        1,
//                        context.getInteger("pageSize"),
//                        context.getInteger("businessType"),
//                        subject.getCurrentUserId(),
//                        context.getString("topicIds").split(","),
//                        context.getOptionalString("categroyId"),
//                        context.getOptionalString("courseId"),
//                        context.getOptionalInteger("publishClient"));
//            }, 60 * 60 * 2);
//        }
        return ImmutableMap.of("currentPage", page, "items", courseInfoList);
//        Map<String, Object> result = new HashMap<>();
//        result.put("currentPage", page);
//        result.put("items", new ArrayList<CourseInfo>());
//        return result;
    }

    /**
     * 判断相关专题开关是否开启
     */
    private Map<String, Object> relatedSubject(Integer pageSize, Integer businessType, String rootOrganizationId) {
        //查询外部开关是否开启
        Optional<RuleConfig> optionalRuleConfig = ruleConfigService.getByName(rootOrganizationId, RuleConfig.KEY.RELATED_RESOURCES);
        if (optionalRuleConfig.isPresent() && Objects.equals(optionalRuleConfig.get().getValue(), RuleConfig.STATUS_ENABLED.toString())) {
            return reuse(businessType, pageSize);
        }

        //查询内部开关是否开启
        InternalSwitch internalSwitch = null;
        //查询的是专题开关
        if (Objects.equals(businessType, CourseInfo.BUSINESS_TYPE_SUBJECT)) {
            internalSwitch = internalSwitchCache.get(InternalSwitch.KEY + SUBJECT_KEY, () -> internalSwitchService.findByType(InternalSwitch.relatedSubject), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
            //课程开关
        } else if (Objects.equals(businessType, CourseInfo.BUSINESS_TYPE_COURSE)) {
            internalSwitch = internalSwitchCache.get(InternalSwitch.KEY + COURSE_KEY, () -> internalSwitchService.findByType(InternalSwitch.relatedCourses), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
        }

        LOGGER.info("相关课程或者专题开关开启,实体:{}", internalSwitch);
        //如果是开启则查询固定的数据
        if (!ObjectUtils.isEmpty(internalSwitch) && Objects.equals(internalSwitch.getStatus(), InternalSwitch.switchStatusNO)) {
            return reuse(businessType, pageSize);
        }
        return null;
    }

    private Map<String, Object> reuse(Integer businessType, Integer pageSize){
        LOGGER.info("相关课程或者专题开关开启,查询固定数据");
        int currentPage = 0;
        int type = businessType.equals(CourseInfo.BUSINESS_TYPE_COURSE) ? BusinessEmergency.BusinessTypeCourse : BusinessEmergency.BehaviorTypeSubject;
        List<CourseInfo> courseInfoList = internalSwitchCache.get(InternalSwitch.KEY + (businessType.equals(CourseInfo.BUSINESS_TYPE_COURSE) ? COURSE_KEY : SUBJECT_KEY) + DATA_KEY, () -> businessEmergencyService.findBusiness(currentPage, pageSize, type), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
        LOGGER.info("查询固定数据完毕,值:{}", courseInfoList);
        return ImmutableMap.of("currentPage", currentPage + 1, "items", courseInfoList);
    }

    private List<CourseChapter> parseCourseChapters(Optional<String> courseChaptersOptional) {
        List<CourseChapter> courseChapterList = new ArrayList<>();
        if (!courseChaptersOptional.isPresent())
            return courseChapterList;
        String courseChapters = courseChaptersOptional.get();

        courseChapterList = com.alibaba.fastjson.JSON.parseArray(courseChapters, CourseChapter.class);

        int[] idx = {0, 0};
        courseChapterList.forEach(courseChapter -> {
            courseChapter.setSequence(idx[0]++);
            Optional.ofNullable(courseChapter.getCourseChapterSections())
                    .ifPresent(sections -> sections.forEach(section -> section.setSequence(idx[1]++)));
        });

        return courseChapterList;

    }

    private List<CourseAttachment> parseCourseAttachment(Optional<String> courseAttachmentOptional) {
        List<CourseAttachment> attachmentList = new ArrayList<>();
        if (!courseAttachmentOptional.isPresent())
            return attachmentList;
        String courseAttachment = courseAttachmentOptional.get();

        List<JSONObject> jsonObjects = com.alibaba.fastjson.JSON.parseArray(courseAttachment, JSONObject.class);
        IntStream.range(0, jsonObjects.size()).forEach(i -> {
            CourseAttachment attachment = new CourseAttachment();
            attachment.setSequence(i);
            attachment.setSource(jsonObjects.get(i).getInteger("source"));
            attachment.setKnowledgeId(jsonObjects.get(i).getString("knowledgeId"));
//            if(com.alibaba.dubbo.common.utils.StringUtils.isEmpty(attachment.getKnowledgeId())) {
            attachment.setName(jsonObjects.get(i).getString("name"));
            attachment.setAttachmentId(jsonObjects.get(i).getString("attachmentId"));
            attachment.setAttachmentType(jsonObjects.get(i).getString("attachmentType"));
//            }
            attachmentList.add(attachment);
        });

        return attachmentList;
    }

    private List<AudienceItem> parseAudienceItems(Optional<String> optionalAudienceItems) {
        List<AudienceItem> audienceItems = Lists.newArrayList();
        optionalAudienceItems.ifPresent(x -> {
            List<JSONObject> itemList = com.alibaba.fastjson.JSON.parseArray(x, JSONObject.class);
            itemList.stream().forEach(r -> {
                AudienceItem ai = new AudienceItem();
                ai.setJoinId(r.getString("joinId"));
                ai.setJoinType(r.getInteger("joinType"));
                ai.setJoinName(r.getString("joinName"));

                audienceItems.add(ai);
            });
        });
        return audienceItems;
    }

    /***
     * 转换上架(发布)信息
     *
     * @param shelves
     * @return
     */
    private Optional<CourseShelves> parseShelves(Optional<String> shelves) {
        Optional<CourseShelves> courseShelves;
        try {
            courseShelves = shelves.map(s -> {
                CourseShelves cs = com.alibaba.fastjson.JSON.parseObject(s, CourseShelves.class);
                return cs;
            });
        } catch (Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        courseShelves.ifPresent(cs -> {
            int noticeUser = cs.getNoticeUser() != null ? cs.getNoticeUser().intValue() : CourseShelves.NOTICE_NO;
            int noticeManager = cs.getNoticeManager() != null ? cs.getNoticeManager().intValue()
                    : CourseShelves.NOTICE_NO;
            if (noticeUser == CourseShelves.NOTICE_YES
                    && com.alibaba.dubbo.common.utils.StringUtils.isEmpty(cs.getNoticeUserContent())) {
                throw new ValidationException(ErrorCode.NoticeTextNotNull);
            } else if (noticeManager == CourseShelves.NOTICE_YES
                    && com.alibaba.dubbo.common.utils.StringUtils.isEmpty(cs.getNoticeManagerContent())) {
                throw new ValidationException(ErrorCode.NoticeTextNotNull);
            }
            cs.setRule(cs.getRule() != null ? cs.getRule() : CourseShelves.RULE_NOT_AFFECT);
        });
        return courseShelves;
    }


    /***
     * 转换相册照片
     *
     * @param photos
     * @return
     */
    private List<CoursePhoto> parsePhotos(Optional<String> photos) {
        List<CoursePhoto> photoList = Lists.newArrayList();
        try {
            if (photos.isPresent()) {
                photoList = com.alibaba.fastjson.JSON.parseArray(photos.get(), CoursePhoto.class);
            }
        } catch (Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        return photoList;
    }

    /**
     * 校验课程、专题信息是否完整
     *
     * @param course
     * @return
     */
    private CourseInfo validationCourse(CourseInfo course) {
        int businessType = course.getBusinessType();
        String url = course.getUrl(); //个性化专题链接
        if (course.getAudienceItems() == null || course.getAudienceItems().isEmpty()) {
            throw new ValidationException(ErrorCode.AudienceItemsNotNull);
        }

        if (com.alibaba.dubbo.common.utils.StringUtils.isEmpty(url)) {
            if (course.getCourseChapters() == null || course.getCourseChapters().isEmpty()) {
                throw new ValidationException(ErrorCode.CanNotFindStagesElements);
            }

            List<CourseChapter> chapters = course.getCourseChapters();
            if (CourseInfo.BUSINESS_TYPE_SUBJECT == businessType) {
            	chapters = chapters.stream().filter(chapter -> chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()).collect(Collectors.toList());
            } else {
            	if(chapters.stream().filter(chapter -> chapter.getCourseChapterSections() == null || chapter.getCourseChapterSections().isEmpty()).count() > 0) {
                    throw new ValidationException(ErrorCode.CanNotFindStagesElements);
                }
            }

            chapters.stream().forEach(chapter -> {
                chapter.getCourseChapterSections().stream().forEach(section -> {
                    int sectionType = section.getSectionType() == null ? 0 : section.getSectionType().intValue();
                    Attachment attachment = fileService.get(section.getResourceId())
                            .orElse(new Attachment());
                    // 验证文档转换
                    if("pdf".equalsIgnoreCase(attachment.getExtention())) return;
                    if (sectionType == CourseChapterSection.SECTION_TYPE_DOC || sectionType == CourseChapterSection.SECTION_TYPE_VIDEO) {
                        if (attachment.getTranslateFlag() == null || !attachment.getTranslateFlag().equals(1)) {
                            throw new ValidationException(ErrorCode.DocumentNoTranslateSuccess);
                        }
                    }
                    // 验证scrom课件
                    if (sectionType == CourseChapterSection.SECTION_TYPE_SCROM) {
                        if (attachment.getTranslateFlag() == null || !attachment.getTranslateFlag().equals(1)) {
                            throw new ValidationException(ErrorCode.ScormNoTranslateSuccess);
                        }
                    }
                });
            });
        }
        if(course.getCourseAttachments() != null) {

            course.getCourseAttachments().stream().forEach(atta -> {
                int contentType = atta.getAttachmentType() == null ? 0 : Integer.parseInt(atta.getAttachmentType());
                // 验证文档转换
                if (contentType == CourseChapterSection.SECTION_TYPE_DOC) {
                    Attachment attachment = fileService.get(atta.getAttachmentId())
                            .orElseThrow(() -> new ValidationException(ErrorCode.AttachmentNotExtends));
                    if (!"pdf".equalsIgnoreCase(attachment.getExtention())
                            && org.springframework.util.StringUtils.isEmpty(attachment.getTranslateId())) {
                        throw new ValidationException(ErrorCode.DocumentNoTranslateSuccess);
                    }
                }
            });
        }
        if (businessType == CourseInfo.BUSINESS_TYPE_SUBJECT && com.alibaba.dubbo.common.utils.StringUtils.isEmpty(url)) {
            if (org.springframework.util.StringUtils.isEmpty(course.getStyles())) {
                throw new ValidationException(ErrorCode.SubjectStyleNotNull);
            }
        }
        return course;
    }

    /**
     * 转换专题广告
     *
     * @param advertisings
     * @return
     */
    private List<SubjectAdvertising> parseAdvertising(Optional<String> advertisings) {
        List<SubjectAdvertising> advertisingList = Lists.newArrayList();
        try {
            if (advertisings.isPresent()) {
                advertisingList = com.alibaba.fastjson.JSON.parseArray(advertisings.get(), SubjectAdvertising.class);
            }
        } catch (Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        return advertisingList;
    }

    /**
     * 转换专题文字区域
     *
     * @param textAreas
     * @return
     */
    private List<SubjectTextArea> parseTextArea(Optional<String> textAreas) {
        List<SubjectTextArea> textAreaList = Lists.newArrayList();
        try {
            if (textAreas.isPresent()) {
                textAreaList = com.alibaba.fastjson.JSON.parseArray(textAreas.get(), SubjectTextArea.class);
            }
        } catch (Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        return textAreaList;
    }

    /**
     * 个人中心首页推荐
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/person-index", method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @JSON("recordCount")
    @JSON("items.(*)")
    public PagedResult<CourseInfo> personIndex(RequestContext context, Subject<Member> subject) {

        Optional<RuleConfig> optionalRuleConfig = ruleConfigService.getByName(subject.getRootOrganizationId(), RuleConfig.KEY.GUESS_YOU_RE_INTERESTED);
        if (optionalRuleConfig.isPresent() && Objects.equals(optionalRuleConfig.get().getValue(), RuleConfig.STATUS_ENABLED.toString())){
            LOGGER.info("个人中心内部开关开启,返回-1,屏蔽模块");
            return PagedResult.create(-1, new ArrayList<>());
        }


        InternalSwitch internalSwitch = internalSwitchCache.get(InternalSwitch.KEY + GUESS_YOU_LIKE, () -> internalSwitchService.findByType(InternalSwitch.guessYouLike), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
        if (!ObjectUtils.isEmpty(internalSwitch) && Objects.equals(internalSwitch.getStatus(), InternalSwitch.switchStatusNO)){
            LOGGER.info("个人中心内部开关开启,返回-1,屏蔽模块");
            return PagedResult.create(-1, new ArrayList<>());
        }


        String memberId = subject.getCurrentUserId();
		 List<MemberTopic> memberTopics = memberTopicService.findByMemberId(memberId);
		 List<String> topicIdList = memberTopics.stream().map(r -> { return r.getTopicId(); }).collect(Collectors.toList());
		 return courseInfoService.personIndex(context.getInteger("page"), context.getInteger("pageSize"), memberId, topicIdList);
    }

    @RequestMapping(value = "/front/find-by-ids", method = RequestMethod.GET)
    @Param(name = "ids", required = true)
    @JSON("*.*")
    @Permitted
    //@JSON("organization.(id,name)")
    @CachedResult(params = {"ids"}, key = "course-front-find-by-ids", expired = 60 * 60 * 12)
    public List<CourseInfo> findByIds(RequestContext context) {
//        System.out.println("courseinfocontroller invoke /front/find-by-ids");
        return courseInfoService.findByIds(Arrays.asList(context.getString("ids").split(",")));
    }

  /*  @RequestMapping(value = "/front/home-page/find-by-ids", method = {RequestMethod.GET, RequestMethod.POST})
    @Param(name = "ids", required = true)
    @JSON("id,name,visits,beginDate,endDate,coverPath,cover,url,releaseTime,source,avgScore,integral,descriptionText")
    @CachedResult(params = {"ids"}, key = "course-front-home-page-find-by-ids", expired = 60 * 60)
    @Permitted
    public List<CourseInfo> homePageFindByIds(RequestContext context) {
        return courseInfoService.findByIds(Arrays.asList(context.getString("ids").split(",")),
                false,false,true);
    }*/

    /**
     * 改为post请求防止超载
     * @param context
     * @return
     */
    @RequestMapping(value = "/front/find-by-ids", method = RequestMethod.POST)
    @Param(name = "ids", required = true)
    @JSON("*.*")
    //@JSON("organization.(id,name)")
    @CachedResult(params = {"ids"}, key = "course-front-find-by-ids-post", expired = 60 * 5)
    @Permitted
    public List<CourseInfo> findCourseInfoByIdsPost(RequestContext context) {
        return courseInfoService.findByIds(Arrays.asList(context.getString("ids").split(",")));
    }

    @RequestMapping(value = "/front/home-page/find-by-ids", method = {RequestMethod.GET, RequestMethod.POST})
    @Param(name = "ids", required = true)
    @Param(name = "needDescriptionText", type = Boolean.class)
    @JSON("id,name,visits,beginDate,endDate,coverPath,cover,url,releaseTime,source,avgScore,integral,descriptionText")
    //@CachedResult(params = {"ids"}, key = "course-front-home-page-find-by-ids", expired = 60 * 60)
    @Permitted
    public List<CourseInfo> homePageFindByIds(RequestContext context) {
        Boolean needDescriptionText =  context.getOptional("needDescriptionText",Boolean.class).orElse(false);
        List<CourseInfo> result;
        if(needDescriptionText){
            result= courseCache.get(HOME_PAGE_DETAIL_KEY + context.getString("ids"), () -> courseInfoService.findByIds(Arrays.asList(context.getString("ids").split(",")),
                    false, false, true), 60 * 60);
        }else{
            result= courseCache.get(HOME_PAGE_KEY + context.getString("ids"), () -> courseInfoService.findByIds(Arrays.asList(context.getString("ids").split(",")),
                    false, false, false), 60 * 60);
        }
        result.forEach(r -> r.setCoverPath(generateSecurePathCdn(r.getCoverPath())));
        return result;
    }

    // 培训课程选择器。 1 归属部门是集团 2 所在机构d的所有课程
    @RequestMapping(value = "/select-for-train", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "category")
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "selectedIds")
    @JSON("recordCount")
    @JSON("items.(*)")
    @JSON("items.category.(*)")
    @JSON("items.organization.(*)")
    public PagedResult<CourseInfo> selectForTrain(RequestContext context, Subject<Member> subject) {
        Member member = subject.getCurrentUser();
        return courseInfoService.selectForTrain(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                member.getRootOrganization().getId(),
                Optional.ofNullable(member.getCompanyOrganization()).map(Organization::getId),
                context.getOptionalString("organizationId"),
                context.getOptionalString("category"),
                context.getOptionalString("name"),
                context.getOptionalString("selectedIds")
        );
    }

    /** 判断组织关联 */
    @RequestMapping(value = "/judge-org-releated", method = RequestMethod.GET)
    @Param(name = "organizationId", type = String.class, required = true)
    @JSON("*")
    public Map<String,Object> judgeOrganizationReleated(RequestContext requestContext) {
        return organizationService.judgeOrgReleated(requestContext.getString("organizationId"));
    }

    @RequestMapping(value = "/topic/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("id, name")
    public List<Topic> getTopicsByCourseId(RequestContext context) {
        return this.getTopicsByCourseId(context.getString("id"));
    }
    private List<Topic> getTopicsByCourseId(String bussinessId) {
        List<BusinessTopic> topicList = businessTopicService.findTopicByBusinessId(bussinessId);
        if (!topicList.isEmpty()) {
            List<String> topicIds = new ArrayList<>();
            topicList.stream().forEach(r -> topicIds.add(r.getTopicId()));
            return topicService.get(topicIds.toArray(new String[topicIds.size()]));
        }
        return new ArrayList<>();
    }

    /**
     * 只查询专题的基本信息t_course_info表中的数据，用于首页大banner判断是否为个性化专题
     * @param context
     * @return
     */
    @RequestMapping(value = "/subject-detail/{id}", method = RequestMethod.GET)
    @Param(name = "id", required = true)
    @JSON("*")
    public CourseInfo getSubjectInfoDetailById(RequestContext context) {

    	return courseInfoService.getSubjectInfoDetailById(context.getString("id"));
    }

    private List<Topic> getTopicsByCourseId(String bussinessId,List<Topic> topics) {
        List<BusinessTopic> topicList = businessTopicService.findTopicByBusinessId(bussinessId);
        return topics.stream().
                filter(x-> topicList.stream().anyMatch(y-> y.getTopicId().equals(x.getId())))
                .collect(Collectors.toList());
    }

    private List<Topic> getTopicMap(Integer bussindessType) {
        List<BusinessTopic> topicList = businessTopicService.findAllByType(bussindessType);
        if (!topicList.isEmpty()) {
            List<String> topicIds = topicList.stream().map(x->x.getTopicId()).distinct().collect(Collectors.toList());
            return topicService.get(topicIds.toArray(new String[topicIds.size()]));
        }
        return new ArrayList<>();
    }

    /**
     * 用于审计查询
     * @param context
     * @return
     */
    @RequestMapping(value = "/audit/{id}", method = RequestMethod.GET)
    @Param(name = "id", type = String.class, required = true)
    @JSON("name")
    @JSON("organization.(id,name)")
    @JSON("releaseOrg.(id,name)")
    @JSON("releaseUser.(id,name,fullName)")
    @JSON("developUser.(id,name,fullName)")
    @JSON("createUser.(id,name,fullName)")
    @JSON("category.(id,name)")
    @JSON("courseAttachments.(*)")
    public CourseInfo getBasicCourseInfo(RequestContext context) {
        return courseInfoService.getCourse(context.get("id", String.class));
    }

    @RequestMapping(value = "/hand-regist",method = RequestMethod.POST)
    @Permitted
    @Param(name = "subjectId",required = true)
    @JSON("*.*")
    public Map<String, Object> handRegist(RequestContext requestContext){
        String id= requestContext.getString("subjectId");
        courseCacheService.setHandRegist(id);
        return ImmutableMap.of("id", id);
    }

    @RequestMapping(value = "/hot-visits-count", method = RequestMethod.GET)
    @Param(name = "size", type = Integer.class, required = true)
    @Param(name = "organizationId", type = String.class, required = true)
    @Param(name = "businessType", type = Integer.class, required = true)
    @Param(name = "clientType", type = Integer.class, required = false, defaultValue = "2")
    @JSON("id,name,visits,businessType,coverPath")
    @Permitted
    //@CachedResult(params = {"size", "organizationId", "businessType", "clientType"}, key = "hot-visits-count", expired = 60 * 10)
    public List<CourseInfo> rankForBrowseCount(RequestContext context) {

        InternalSwitch internalSwitch = internalSwitchCache.get(InternalSwitch.KEY + HOT_CONTENT, () -> internalSwitchService.findByType(InternalSwitch.hotContent), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
        if (!ObjectUtils.isEmpty(internalSwitch) && Objects.equals(internalSwitch.getStatus(), InternalSwitch.switchStatusNO)) {
            LOGGER.info("热门内容开关开启,查询固定资源,查询指定从库");
            return hotContent(
                    context.getInteger("businessType"),
                    context.getInteger("size")
            );
        }


//        String organizationId = ROOT_ORGANIZATION_ID;
//        Integer size = context.getInteger("size");
//        if (size > 20) {
//            return null;
//        }
//        Integer businessType = context.getInteger("businessType");
//        Integer clientType = context.getOptionalInteger("clientType").orElse(Integer.valueOf(2));
//        String cacheKey = "idx-hot-visit-" + organizationId + "-" + businessType + "-" + clientType + "-" +size;
//        List<CourseInfo> hotVisitsList = cache.get(cacheKey, List.class);
//        if (hotVisitsList != null) {
//            return hotVisitsList;
//        }
//        synchronized (HOT_VISIT_LOCK) {
//            hotVisitsList = cache.get(cacheKey, List.class);
//            if (hotVisitsList != null) {
//                return hotVisitsList;
//            }
//            hotVisitsList = courseInfoService.hotVisitsList(
//                    organizationId,
//                    size,
//                    businessType,
//                    clientType);
//            cache.set(cacheKey, hotVisitsList, hotVistRankCacheExpireTime.intValue());
//        }
//        return hotVisitsList;

        //热门学习更新 -- LJY --  2022/05/17
        String organizationId = ROOT_ORGANIZATION_ID;
        Integer size = context.getInteger("size");
        if (size > 20) {
            return null;
        }
        Integer businessType = context.getInteger("businessType");
        Integer clientType = context.getOptionalInteger("clientType").orElse(Integer.valueOf(2));

        //热门内容缓存键优化 -- LJY -- 2022/06/10
        String cacheKey = "hot-visits-key-"+ organizationId + "-" + businessType + "-" + clientType;
        List<CourseInfo> hotVisitsList = courseInfoService.getHotVisitsCache(cacheKey);
        if (hotVisitsList != null) {
            return hotVisitsList;
        }
        synchronized (HOT_VISIT_LOCK) {
            hotVisitsList = courseInfoService.getHotVisitsCache(cacheKey);
            if (hotVisitsList != null) {
                return hotVisitsList;
            }
//            hotVisitsList = courseInfoService.hotVisitsListNew(
//                    organizationId,
//                    businessType,
//                    clientType);
            hotVisitsList = getCurrentHotVisitsLists(
                    organizationId,
                    businessType,
                    clientType);
            if (!hotVisitsList.isEmpty()) {
                courseInfoService.setHotVisitsCache(cacheKey, hotVisitsList);
            }
        }
        return hotVisitsList;
    }

    private List<CourseInfo> hotContent(Integer businessType, Integer size) {
        return internalSwitchCache.get(InternalSwitch.KEY + HOT_CONTENT + DATA_KEY + businessType, () -> businessEmergencyService.findHotContent(businessType, size), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
    }

    /**
     * 党建云屏（新老党支部书记数据回传接口）
     */
    @RequestMapping(value = "/courseStatus", method = RequestMethod.GET)
    @Param(name = "ihrCord", required = true, value = "ihr员工编码")
    @Param(name = "courseId", required = true, value = "专区ID")
    @Param(name = "nonce", required = true, value = "随机字符串，对接方生成，每次调用不一样")
    @Param(name = "timestamp", required = true, value = "时间戳，例如1600157524461")
    @Param(name = "sign", required = true)
    @JSON("code,msg")
    @JSON("data.(courseSum, courseFinishNumber, courseProgressBar, oldCertificateNumber, oldCertificationTime," +
            "newCertificateNumber, newCertificationTime, dashBoard, dashBoardTwo, name, finishTimeMax, alreadyExamNumber," +
            "passOrNot)")
    @JSON("data.courseFinishStatusList.(finishStatus, courseId)")
    public Result findCourseStatus(RequestContext context){
        // 1.验证签名
        if (!this.verify(context, "courseId", "ihrCord", "nonce", "timestamp")) {
            return Result.failure(ResultCodeEnum.SIGN_ERROR);
        }

        List<CourseInfoDjyp> courseInfoDjyps = thematicService.getCourseDjyp(CourseInfoDjyp.STATUS_ON);//必修课程清单
        List<String> courseList = new ArrayList<>();
        courseInfoDjyps.forEach(w -> courseList.add(w.getCourseId()));//获取课程id
        List<CourseInfoDjyp> courseInfoDjyps2 = thematicService.getCourseDjyp(CourseInfoDjyp.STATUS_OFF);//非必修课程清单
        List<String> courseList2 = new ArrayList<>();
        courseInfoDjyps2.forEach(w -> courseList2.add(w.getCourseId()));//获取课程id

        List<Member> member =
                thematicService.getMemberByIhrCode(context.getString("ihrCord"));//查询学员数据

        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(member)){
            return Result.failure(ResultCodeEnum.PARAM_ERROR);
        }

        String oldCertificateNumber = null;
        Long oldCertificationTime = null;
        String courseProgressBar;

        Integer finishCount = 0;
        String memberId = member.get(0).getId();
        String passOrNot = "未考试";

        for (int i = 0; i < member.size(); i++) {//处理党建多账号
            List<CourseStudyProgress> list = thematicService.findCourseStudyProgressByCourseIds(member.get(i).getId(), courseList);
            if (!com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(list) && list.size() > finishCount) {
                finishCount = list.size();
                memberId = member.get(i).getId();
            }
        }

        List<CourseStudyProgress> courseStudyProgress = thematicService.findCourseStudyProgressByCourseIds(memberId, courseList);//必修课程完成数
        List<CourseStudyProgress> courseStudyProgressFinishStatus = thematicService.findCourseStudyProgressFinishStatus(memberId, courseList);//必修每门课程完成状态
        List<CourseStudyProgress> courseStudyProgressFinishStatus2 = thematicService.findCourseStudyProgressFinishStatus(memberId, courseList2);//非必修每门课程完成状态
        List<ThematicMember> thematicMember = thematicService.findMemberCertificate(memberId, "18d35a16-0d53-11ea-ae76-0050568d3015");//老党支部书记//18d35a16-0d53-11ea-ae76-0050568d3015
        List<com.zxy.product.exam.entity.CertificateRecord> memberCertificate = certificateRecordService.findMemberCertificate(memberId, EXAM_ID_DJ);//新党支部书记//EXAM_ID_DJ
        Integer examRegion = memberService.findExamRegion(memberId);
        List<com.zxy.product.exam.entity.ExamRecord> memberExamRecordScore = examRecordStuService.getMemberExamRecordScore(examRegion, memberId, EXAM_ID_DJ);//获取该学员的考试状态
        long count = memberExamRecordScore.stream().filter(w -> w.getStatus() != null
                && w.getStatus() != ExamRecord.STATUS_TO_BE_STARTED
                && w.getStatus() != ExamRecord.STATUS_DOING).count();//该学员考试的次数
        boolean contains = memberExamRecordScore.stream().map(com.zxy.product.exam.entity.ExamRecord::getStatus).collect(Collectors.toList()).contains(ExamRecord.STATUS_PASS);//该学员考试是否及格

        double examSum = 1;
        if(!com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(thematicMember)) {//获取老支部书记证书编号
            oldCertificateNumber = thematicMember.get(0).getQualifiedNumber();
            oldCertificationTime = thematicMember.get(0).getQualifiedTime();
        }

        double certificateNumber = 0;
        Long newCertificationTime = null;
        String newCertificateNumber = null;
        if(!com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(memberCertificate)) {//获取新支部书记证书编号
            certificateNumber = 1;
            newCertificateNumber = memberCertificate.get(0).getNum();
            newCertificationTime = memberCertificate.get(0).getIssueTime();
        }

        DecimalFormat df = new DecimalFormat("######0.00");
        Thematic thematic = new Thematic();
        double courseFinishSum = courseStudyProgress.size();
        double courseSum = courseInfoDjyps.size();

        thematic.setDashBoard(df.format((courseFinishSum + certificateNumber) / (courseSum + examSum) * 100) + "0%"); // 计算新党支部书记仪表盘
        thematic.setDashBoardTwo(df.format((courseFinishSum + certificateNumber) / courseSum * 100) + "0%"); // // 计算老党支部书记仪表盘

        CourseStudyProgress finishTimeMax = new CourseStudyProgress();
        if (org.springframework.util.StringUtils.isEmpty(oldCertificateNumber)){
            finishTimeMax = courseStudyProgress.stream().filter(t->t.getFinishTime() != null).max(Comparator.comparing(CourseStudyProgress::getFinishTime))
                    .orElse(courseStudyProgress.stream().filter(t->t.getLastAccessTime() != null).max(Comparator.comparing(CourseStudyProgress::getLastAccessTime))
                            .orElse(new CourseStudyProgress()));
        }

        if (count > 0) {
            passOrNot = contains ? "及格" : "不及格";
        }

        List<String> two = new ArrayList<>();
        courseStudyProgressFinishStatus.forEach(w -> two.add(w.getCourseId()));

        List<String> collect = courseList.stream().filter(w -> !two.contains(w)).collect(Collectors.toList());
        for (int i = 0; i < collect.size(); i++) {
            CourseStudyProgress courseStudyProgress3 = new CourseStudyProgress();
            courseStudyProgress3.setCourseId(collect.get(i));
            courseStudyProgress3.setFinishStatus(CourseStudyProgress.FINISH_STATUS_DEFAULT);
            courseStudyProgressFinishStatus.add(courseStudyProgress3);
        }

        List<String> two2 = new ArrayList<>();
        courseStudyProgressFinishStatus2.forEach(w -> two2.add(w.getCourseId()));

        List<String> collect2 = courseList2.stream().filter(w -> !two2.contains(w)).collect(Collectors.toList());
        for (int i = 0; i < collect2.size(); i++) {
            CourseStudyProgress courseStudyProgress4 = new CourseStudyProgress();
            courseStudyProgress4.setCourseId(collect2.get(i));
            courseStudyProgress4.setFinishStatus(CourseStudyProgress.FINISH_STATUS_DEFAULT);
            courseStudyProgressFinishStatus2.add(courseStudyProgress4);
        }

        List<Map<String, Object>> list = new ArrayList<>();
        for (int i = 0; i < courseStudyProgressFinishStatus.size(); i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("courseId", courseStudyProgressFinishStatus.get(i).getCourseId());
            map.put("finishStatus", courseStudyProgressFinishStatus.get(i).getFinishStatus());
            list.add(map);
        }

        for (int i = 0; i < courseStudyProgressFinishStatus2.size(); i++) {
            Map<String, Object> map = new HashMap<>();
            map.put("courseId", courseStudyProgressFinishStatus2.get(i).getCourseId());
            map.put("finishStatus", courseStudyProgressFinishStatus2.get(i).getFinishStatus());
            list.add(map);
        }

        Map<String, Object> map = new HashMap<>();
        courseProgressBar = df.format((courseFinishSum / courseSum * 100L)) + "0%";
        map.put("courseSum", courseInfoDjyps.size() + courseInfoDjyps2.size()); // 课程总数
        map.put("courseFinishNumber", courseStudyProgress.size()); // 课程完成数量
        map.put("courseProgressBar", courseStudyProgress.size() == 0 ? (0 + "0%")  : courseProgressBar); //课程进度条
        map.put("oldCertificateNumber", oldCertificateNumber); // 老党支部书记证书编号
        map.put("oldCertificationTime", oldCertificationTime); // 老党支部书记获证时间
        map.put("newCertificateNumber", newCertificateNumber); // 新党支部书记证书编号
        map.put("newCertificationTime", newCertificationTime); // 新党支部书记获证时间
        map.put("dashBoard", thematic.getDashBoard()); // 新党支部书记仪表盘
        map.put("dashBoardTwo", thematic.getDashBoardTwo()); // 老党支部书记仪表盘
        map.put("name", member.get(0).getFullName()); // 人员姓名
        map.put("finishTimeMax", finishTimeMax.getFinishTime() == null ? finishTimeMax.getLastAccessTime() : finishTimeMax.getFinishTime()); // 最后一门课程完成时间（如果没有课程完成，则返回最后一次访问时间，如果两者皆无则为null）
        map.put("alreadyExamNumber", count); // 该学员已考试次数
        map.put("passOrNot", passOrNot); // 该学员的考试是否及格过
        map.put("courseFinishStatusList", list); // 所有课程的完成状态

        return Result.success(map);
    }

    /**
     * 党建云屏（新老党支部书记插入群组功能）
     */
    @RequestMapping(value = "/insertMemberToTag", method = RequestMethod.POST)
    @Param(name = "ihrCord", required = true, value = "ihr员工编码")
    @Param(name = "nonce", required = true, value = "随机字符串，对接方生成，每次调用不一样")
    @Param(name = "timestamp", required = true, value = "时间戳，例如1600157524461")
    @Param(name = "sign", required = true)
    @JSON("code,msg")
    public Result insertMemberToTag(RequestContext context) {
        // 1.验证签名
        if (!this.verify(context, "ihrCord", "nonce", "timestamp")) {
            return Result.failure(ResultCodeEnum.SIGN_ERROR);
        }
        String ihrCord = context.getString("ihrCord");

        // 2.验证E开头11位编码
        if (!VerifyIhrCodeUtil.verify(ihrCord)){
            djLogService.insertLog(ihrCord, "员工编号格式错误（非E开头11位员工编号）", 0);
            return Result.failure(ResultCodeEnum.IHR_CODE_ERROR);
        }

        List<Member> member = thematicService.getMemberByIhrCode(ihrCord);

        // 3.验证该学员是否存在
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(member)){
            djLogService.insertLog(ihrCord, "未查询到该人员信息", 0);
            return Result.failure(ResultCodeEnum.MEMBER_ERROR);
        }

        // 4.验证该学员是否在群组
        int state = memberTagService.findMember(member.get(0).getId(), "fcef6e02-d724-43b9-86a2-d586bafc60b2");
        if (state == 0){
            djLogService.insertLog(ihrCord, "员工已存在于该群组中", 0);
            return Result.failure(ResultCodeEnum.TAG_ERROR);
        }

        memberTagService.insert(member.get(0).getId(), "fcef6e02-d724-43b9-86a2-d586bafc60b2");
        djLogService.insertLog(ihrCord, "新增成功", 1);
        return Result.success();
    }

    @RequestMapping(value = "/check-archived")
    @Permitted
    @Param(name = "courseId",required = true)
    @JSON("*.*")
    public Map<String, Object> checkArchived(RequestContext requestContext,Subject<Member> subject){

        String courseId = requestContext.getString("courseId");
        ErrorCode.CourseInfoShelves.throwIf(!courseInfoService.existedVersionId(courseId));
        Boolean aBoolean = cache.get("check-archived#"+subject.getCurrentUserId()+"#"+courseId,
                ()-> courseStudyProgressArchivedService.checkAllStatusExistenceArchived(subject.getCurrentUserId(), courseId ),
                60*10);
        return ImmutableMap.of("existence", aBoolean);
    }



    /**
     * 获取智能播报列表接口
     */
    @RequestMapping(value = "/broadcastList", method = RequestMethod.GET)
    @Param(name = "businessType", required = true, value = "业务类型")
    @Param(name = "courseId", required = true, value = "ID")
    @JSON("pdfList.id")
    @JSON("pdfList.name")
    @JSON("pdfList.courseChapterSections.(name,id,resourceId,audioTime,attachmentAudioId,referenceId,sectionType,intelligentBroadcastTxtId)")
    public Map intelligentBroadcast(Subject<Member> memberSubject,RequestContext requestContext){
        String id = requestContext.getString("courseId");
        Integer businessType = requestContext.getInteger("businessType");
        String memberId = memberSubject.getCurrentUserId();
        List<CourseInfo> lists = courseInfoService.getAllCourseChapterInfo(id,memberId,businessType);

        List<String> attachmentAudioIds =
                lists.stream()
                        .map(CourseInfo::getCourseChapterSections)
                        .flatMap(Collection::stream).map(CourseChapterSection::getAttachmentAudioId)
                        .collect(Collectors.toList());
        Map<String, Integer> attachmentDurationMap = fileService.findDurationByIds(attachmentAudioIds);
        lists.forEach(map -> map.getCourseChapterSections().forEach(map2 -> map2.setAudioTime(Optional.ofNullable(attachmentDurationMap.get(map2.getAttachmentAudioId())).orElse(0) / 1000)));

        Map<String,Object> chapterMap = new HashMap<>();
        chapterMap.put("pdfList",lists);
        return chapterMap;

    }

    /**
     * 判断个性化专题是否存档
     */
    @RequestMapping(value = "/whether-personalized-topics-are-archived", method = RequestMethod.GET)
    @Params({@Param(name = "url"), @Param(name = "id")})
    @Permitted
    @JSON("*.*")
    public CourseInfo whetherPersonalizedTopicsAreArchived(Subject<Member> memberSubject,RequestContext requestContext){
        return courseStudyProgressArchivedService.whetherPersonalizedTopicsAreArchived(memberSubject.getCurrentUserId(), requestContext.getOptionalString("url"),requestContext.getOptionalString("id") );
    }

    /**
     * 虚拟空间-推荐列表
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/get-course-or-exam", method = RequestMethod.GET)
    @Param(name = "courseIds") //课程id or 专题id
    @Param(name = "examIds") //考试id
    @Permitted
    @JSON("code,msg")
    @JSON("data.courseOrSubjectList.(id, name, description, descriptionText, allCourseTime, coverPath, courseTime, studyMemberCount, avgScore)")
    @JSON("data.examList.(id, name, startTime, endTime, joinNumber, coverIdPath)")
    public Result findCourseAndSubjectAndExam(RequestContext context, Subject<Member> subject){
        Optional<String> courseIds = context.getOptionalString("courseIds");
        Optional<String> examIds = context.getOptionalString("examIds");
        Map<String, Object> map = new HashMap<>();
        if (courseIds.isPresent()){
            List<String> courseIdList = Arrays.stream(courseIds.get().split("，")).collect(Collectors.toList());
            List<CourseInfo> courseOrSubjectList = courseInfoService.getCourseOrSubject(courseIdList, subject.getCurrentUserId());
            map.put("courseOrSubjectList", courseOrSubjectList);
        }if (examIds.isPresent()){
            List<String> examIdList = Arrays.stream(examIds.get().split("，")).collect(Collectors.toList());
            List<Exam> examList = examRecordService.getExam(examIdList);
            map.put("examList", examList);
        }
        return Result.success(map);
    }

    /**
     * 验证签名
     *
     * @param context 请求上下文
     * @param keys    参数keys,按ASCII码顺序排列
     * @return 验签结果
     */
    private boolean verify(RequestContext context, String... keys) {
        StringBuilder sb = new StringBuilder();
        for (String key : keys) {
            sb.append(key).append("=").append(context.getString(key)).append("&");
        }
        sb.append("secret=").append(secret);
        String _sign = DigestUtils.md5DigestAsHex(sb.toString().getBytes()).toUpperCase();
        return context.getString("sign").equals(_sign);
    }

    /**
     * 如果部署时为空，手动更新相关内容
     * @return 当前最新的热门学习列表
     */
    public List<CourseInfo> getCurrentHotVisitsLists(String organizationId, Integer businessType, Integer clientType){
        List<CourseInfo> hotVisitsList= new ArrayList();
        Date date = new Date();
        for (int i = 1; i < 10000; i++) {
            //查找出相关课程ID
            List<ResourceVisit> courseList = displayService.resourceTop10(date, i, 10);
            //如果为空，可能分为两种情况：
            //查询不到满足条件的课程信息为止，返回目前所有列表
            if (courseList.isEmpty()) {
                //如果更新flag未更新，说明未更新缓存，同时看存在多少热门课程，进行缓存设定
                break;
            }
            //记录每个课程对应的点击数，用于更新查询数据库时候的数据
            Map<String, Integer> courseVisits = Maps.newHashMap();
            //用于批量选取条件
            List<String> courseListContentId = Lists.newArrayList();
            for (ResourceVisit rv : courseList) {
                courseListContentId.add(rv.getContentId());
                courseVisits.put(rv.getContentId(), rv.getVisit());
            }
            //填充课程相关信息，需要从课程信息数据库查询
            //此时课程信息可能小于个人数据采集库的课程
            List<CourseInfo> currentCourseList = courseInfoService.hotVisitsListNew(courseListContentId);
            if(!currentCourseList.isEmpty()){
                for (CourseInfo c : currentCourseList) {
                    //先设置频次
                    c.setVisits(courseVisits.get(c.getId()));
                    if (c.getBusinessType() != null && c.getPublishClient() != null) {
                        if (c.getBusinessType().equals(businessType) && (c.getPublishClient().equals(0) || c.getPublishClient().equals(clientType))) {
                            hotVisitsList.add(c);
                        }
                    }
                }
            }
            //课程+WEB
            if(hotVisitsList.size() >= 10){
                //先排序
                hotVisitsList = sortList(hotVisitsList);
                //截断前10个
                hotVisitsList = hotVisitsList.subList(0,10);
                break;
            }
        }
        return sortList(hotVisitsList);
    }

    //降序排列
    private List<CourseInfo> sortList(List<CourseInfo> courseInfoList){
        //排序,降序
        Collections.sort(courseInfoList, new Comparator<CourseInfo>() {
            @Override
            public int compare(CourseInfo o1, CourseInfo o2) {
                return o2.getVisits().compareTo(o1.getVisits());
            }
        });
        return courseInfoList;
    }


    /**
     * 行为数据采集,数据大屏固定值
     * @return
     */
    @RequestMapping(value = "/hot-resources-top3", method = RequestMethod.GET)
    @Permitted
    @JSON("course.(contentName,visit)")
    @JSON("subject.(contentName,visit)")
    public Map<String, List<CourseInfo>> findHotResourcesTop3() {
        return cache.get(REDIS_RESOURCE_TOP3, this::hotResourcesTop3, 60 * 60 * 24);
    }

    private Map<String, List<CourseInfo>> hotResourcesTop3() {
        Map<String, List<CourseInfo>> map = Maps.newHashMap();
        List<CourseInfo> courseList = courseInfoService.findResourceTop3(CourseInfo.BUSINESS_TYPE_COURSE);
        List<CourseInfo> subjectList = courseInfoService.findResourceTop3(CourseInfo.BUSINESS_TYPE_SUBJECT);
        map.put("course", courseList);
        map.put("subject", subjectList);
        return map;
    }

    /**
     * 查询归档课程是否更新
     * @param requestContext
     * @param subject
     * @return
     */
    @RequestMapping(value = "/find-update-or-not", method = RequestMethod.GET)
    @Permitted
    @JSON("*")//0=课程未更新,1=课程更新
    @Param(name = "courseId" , required = true)
    public Integer findUpdateOrNot(RequestContext requestContext, Subject<Member> subject){
        String courseId = requestContext.getString("courseId");
        Optional<CourseInfo> courseInfoOptional = courseInfoService.getOptional(courseId);
        Optional<CourseStudyProgressArchived> courseStudyProgressArchivedOptional = courseStudyProgressArchivedService.findByCourseIdAndMemberId(courseId, subject.getCurrentUserId());
        if (courseInfoOptional.isPresent() && courseStudyProgressArchivedOptional.isPresent()){
            CourseInfo courseInfo = courseInfoOptional.get();
            CourseStudyProgressArchived courseStudyProgressArchived = courseStudyProgressArchivedOptional.get();
            if (courseInfo.getModifyDate().getTime() > courseStudyProgressArchived.getBeginTime()){
                return 1;
            }
        }
        return 0;
        //courseStudyProgressArchivedService

    }
    /**
     * 校验是否匹配考试操作
     *
     * @param requestContext 请求上下文
     * @param subject 用户容器
     * @return 课程信息
     */
    @Param(name = "examId", required = true)
    @JSON("*")
    @RequestMapping(value = "/check-matching-exam-operation",method = RequestMethod.GET)
    public String checkMatchingExamOperation(RequestContext requestContext, Subject<Member> subject){
        return courseInfoService.checkMatchingExamOperation(
                requestContext.get("examId",String.class),
                subject.getCurrentUserId()
        );
    }

    /**
     * 课程详情页查询当前课程，用户可以查看的所属专题
     */
    @JSON("id,name,cover,coverPath")
    @Permitted
    @Param(name = "courseId", required = true)
    @RequestMapping(value = "/find-subjects",method = RequestMethod.GET)
    public List<CourseInfo> findSubjectsByCourse(RequestContext requestContext, Subject<Member> subject){
        return courseInfoService.findSubjectsByCourse(
                requestContext.get("courseId",String.class),
                subject.getCurrentUserId()
        );
    }
    private  Integer findStatus(String courseId){
        List<Caption> captionList = captionService.findStatus(courseId);
        if (ObjectUtils.isEmpty(captionList) || captionList.isEmpty()){
            return CourseInfo.CAPTION_OVERALL_STATUS_NO_NEED_TO_GENERATE;
        }

        for (Caption caption : captionList) {
            if (Objects.equals(caption.getStatus(), Caption.CAPTION_STATUS_FAILED) || Objects.equals(caption.getStatus(), Caption.CAPTION_STATUS_UPLOAD_FAILED)){
                return CourseInfo.CAPTION_OVERALL_STATUS_GENERATE;
            }
        }
        return updateCaptionOverallStatus(courseId);
    }

    /*
     * 查询专题是否配置考试
     */
    @JSON("return")
    @Permitted
    @Param(name = "subjectId", required = true)
//    @Param(name = "subjectName", required = true)
    @RequestMapping(value = "/whether-add-exam", method = RequestMethod.GET)
//    @ImAudit(module = "个人中心", subModule = "学情查询", action = ImAudit.Action.FIND, fisrtAction = "查询", desc = "导出学情分析报告《{0}》", params = {"subjectName"}, businessType = "flag", businessValue = "true")
    public ImmutableMap<String, Boolean> whetherAddExam(RequestContext requestContext){
        return ImmutableMap.of("return", courseInfoService.whetherAddExam(
                requestContext.get("subjectId",String.class)
        ));
    }

    private Integer updateCaptionOverallStatus(String courseId) {
        List<Caption> captionList = captionService.findStatus(courseId);
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(captionList)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_NO_NEED_TO_GENERATE;
        }

        Map<Integer, Integer> statusCount = new HashMap<>();
        for (Caption caption : captionList) {
            int status = caption.getStatus();
            statusCount.merge(status, 1, Integer::sum);
        }

        if (statusCount.containsKey(Caption.CAPTION_STATUS_UPLOAD_FAILED)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_NO_UPLOAD_FAILED;
        } else if (statusCount.containsKey(Caption.CAPTION_STATUS_FAILED)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_BUILD_FAILED;
        } else if (statusCount.containsKey(Caption.CAPTION_STATUS_ANALYSISING)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_GENERATE;
        } else if (statusCount.containsKey(Caption.CAPTION_STATUS_UNPUBLISHED) || statusCount.containsKey(Caption.CAPTION_STATUS_UNPUBLISH)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_GENERATED;
        } else if (statusCount.containsKey(Caption.CAPTION_STATUS_PUBLISHED)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_PUBLISH_ALL;
        } else {
            return CourseInfo.CAPTION_OVERALL_STATUS_NO_NEED_TO_GENERATE;
        }
    }
}
