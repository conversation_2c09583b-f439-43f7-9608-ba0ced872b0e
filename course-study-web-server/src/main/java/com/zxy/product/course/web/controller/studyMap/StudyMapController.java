package com.zxy.product.course.web.controller.studyMap;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.validation.ValidationException;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.CourseStudyProgressService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.subject.SubjectService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.web.kit.AudienceKit;
import com.zxy.product.course.web.kit.CourseInfoAdminKit;
import com.zxy.product.system.api.setting.RuleConfigService;
import com.zxy.product.system.entity.RuleConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 学习地图
 * <AUTHOR>
 */
@Controller
@RequestMapping("/studyMap")
public class StudyMapController {

    private CourseInfoAdminKit courseInfoAdminKit;
    private SubjectService subjectService;
    private CourseCacheService courseCacheService;
    private AudienceKit audienceKit;
    private CourseInfoService courseInfoService;
    private MessageSender messageSender;
    private RuleConfigService ruleConfigService;
    private CourseStudyProgressService courseStudyProgressService;

    @Autowired
    public void setCourseStudyProgressService(CourseStudyProgressService courseStudyProgressService) {
        this.courseStudyProgressService = courseStudyProgressService;
    }

    @Autowired
    public void setRuleConfigService(RuleConfigService ruleConfigService) {
        this.ruleConfigService = ruleConfigService;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setAudienceKit(AudienceKit audienceKit) {
        this.audienceKit = audienceKit;
    }

    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }

    @Autowired
    public void setCourseInfoAdminKit(CourseInfoAdminKit courseInfoAdminKit) {
        this.courseInfoAdminKit = courseInfoAdminKit;
    }

    @Autowired
    public void setSubjectService(SubjectService subjectService) {
        this.subjectService = subjectService;
    }

    /**
     * 查询专题列表
     */
    @RequestMapping(value = "/page", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "code")
    @Param(name = "status", type = Integer.class)
    @Param(name = "beginBeginDate", type = Long.class)
    @Param(name = "beginEndDate", type = Long.class)
    @Param(name = "endBeginDate", type = Long.class)
    @Param(name = "endEndDate", type = Long.class)
    @Param(name = "shelveBeginTime", type = Long.class)
    @Param(name = "shelveEndTime", type = Long.class)
    @Param(name = "selectIds", type = String.class)
    @Param(name = "contain", type = Integer.class)    //是否包含子级【0：不包含；1：包含】
    @Param(name = "uri")
    @Permitted
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,status,beginDate,endDate,cover,coverPath,shelveTime,code,publishClient,url, styles,skinType)")
    @JSON("items.organization.(id,name)")
    public PagedResult<CourseInfo> findPage(RequestContext context, Subject<Member> subject) {
        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));
        List<String> grantOrganizationIds;
        if (context.getOptionalInteger("contain").orElse(0) == 0) {
            grantOrganizationIds = courseInfoAdminKit.findGrantTopOrganizationIds(subject.getCurrentUserId(),context.getOptionalString("organizationId"),context.getOptionalString("uri").orElse("course-study/studyMap-info"));
        }else {
            grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(),context.getOptionalString("organizationId"),context.getOptionalString("uri").orElse("course-study/studyMap-info"));
        }
        return subjectService.find(
            context.get("page", Integer.class),
            context.get("pageSize", Integer.class),
            subject.getCurrentUserId(),
            context.getOptionalString("name"),
            context.getOptionalString("organizationId"),
            context.getOptionalString("code"),
            context.getOptionalInteger("status"),
            context.getOptionalString("releaseUserId"),
            context.getOptionalInteger("publishClient"),
            context.getOptional("subjectType", Integer.class),
            context.getOptional("beginBeginDate", Long.class),
            context.getOptional("beginEndDate", Long.class),
            context.getOptional("endBeginDate", Long.class),
            context.getOptional("endEndDate", Long.class),
            context.getOptional("shelveBeginTime", Long.class),
            context.getOptional("shelveEndTime", Long.class),
            context.getOptionalInteger("isParty"),
            selectIds,
            grantOrganizationIds,
            Optional.of(CourseInfo.BUSINESS_TYPE_STUDY_MAP),
            Optional.empty()
        );
    }


    /**
     * 学习地图新增
     */
    @RequestMapping(method = RequestMethod.POST)
    @Permitted
    @Param(name = "name", required = true)
    @Param(name = "beginDate", type = Long.class)
    @Param(name = "endDate", type = Long.class)
    @Param(name = "description")
    @Param(name = "descriptionText")
    @Param(name = "releaseMemberId")
    @Param(name = "releaseOrgId")
    @Param(name = "organizationId", required = true)
    @Param(name = "code")
    @Param(name = "courseChapters")
    @Param(name = "courseAttachments")
    @Param(name = "audienceItems")
    @Param(name = "shelves")
    @Param(name = "studyDays", type = Integer.class) // 建议学习天数
    @Param(name = "publishClient", type = Integer.class, required = true) // 发布终端，0:
    @Param(name = "publishType", type = Integer.class) // 发布类型 0-定向(测试) 1-正式
    @Param(name = "cover") // 头像
    @Param(name = "coverPath") //头像路径
    @Param(name = "coverMaterialId") // 封面素材id
    @Param(name = "appBanner")
    @Param(name = "pcBanner")
    @Param(name = "pcBannerPath")
    @Param(name = "appBannerPath")
    @Param(name = "businessType")
    @Param(name = "styles") // 风格配置
    @Param(name = "topicIds") // 相关话题
    @Param(name = "advertisings") // 专题广告
    @Param(name = "photos") // 专题相册
    @Param(name = "textAreas") // 专题文字区域
    @Param(name = "url") // 个性化专题链接地址
    @Param(name = "type")//用于区分个性专题，普通专题，正向发布，定向发布
    @Param(name = "shareSub")//2018-01-30  专题新增是否分享下级使用
    @Param(name = "addType", type = Integer.class) // 添加类别 普通模式,章节模式
    @Param(name = "isPublic", type = Integer.class) // 非公开专题
    @Param(name = "descriptionApp", type = String.class) // 简介--app加粗换行样式 updated by wangdongyan
    @Param(name = "certificateId", type = String.class) // 专题配置的证书id
    @Param(name = "isSign", type = Integer.class) // 专题是否需要报名（新动能重塑培训新增功能）
    @Param(name = "managerItems") // 专题管理员
    @Param(name = "explicitLearningStatus", type = Integer.class)
    @Param(name = "learnSequence", type = Integer.class)
    @Param(name = "skinType", type = Integer.class)
    @JSON("id, name,description, scromType,createTime,supportApp,source,status,portalNum")
    public CourseInfo add(RequestContext context, Subject<Member> subject) {
        Optional<CourseShelves> courseShelves = parseShelves(context.getOptionalString("shelves"));
        List<CourseAbility> courseAbilities = courseInfoAdminKit.parseCourseAbilitys(context.getOptionalString("courseChapters"));
        List<CourseAttachment> courseAttachments = courseInfoAdminKit.parseCourseAttachment(context.getOptionalString("courseAttachments"));
        List<AudienceItem> audienceItems = audienceKit.parseAudienceItems(context.getOptionalString("audienceItems"));
        Optional<Integer> businessType = context.getOptionalInteger("businessType");
        if (courseShelves.isPresent()) {
            courseAbilities.forEach(ability -> validationSubject2(audienceItems, ability.getCourseChapters(), courseAttachments, context.getOptionalString("url"),
                context.getOptionalString("styles"), context.getOptionalString("certificateId"), context.getOptionalInteger("publishType"), context.getOptionalInteger("type")));
        }

        CourseInfo courseInfo = subjectService.add(
            context.get("name", String.class),
            subject.getCurrentUserId(),
            context.getInteger("publishClient"),
            context.getString("organizationId"),
            context.getOptionalInteger("publishType"),
            context.getOptional("beginDate", Long.class),
            context.getOptional("endDate", Long.class),
            context.getOptionalString("description"),
            context.getOptionalString("descriptionText"),
            context.getOptionalString("releaseMemberId"),
            context.getOptionalString("releaseOrgId"),
            context.getOptionalString("code"),
            context.getOptionalInteger("studyDays"),
            context.getOptionalString("cover"),
            context.getOptionalString("coverPath"),
            context.getOptionalString("coverMaterialId"),
            new ArrayList<>(),
            courseAttachments,
            audienceItems,
            courseShelves,
            context.getOptionalString("styles"),
            context.getOptionalString("topicIds"),
            new ArrayList<>(),
            new ArrayList<>(),
            new ArrayList<>(),
            context.getOptionalString("url"),
            context.getOptionalInteger("shareSub"),
            context.getOptionalInteger("addType"),
            context.getOptionalInteger("isPublic"),
            context.getOptionalString("descriptionApp"),
            context.getOptionalString("certificateId"),
            context.getOptionalInteger("isSign"),
            new ArrayList<>(),
            context.getOptionalInteger("explicitLearningStatus"),
            businessType,
            context.getOptionalInteger("learnSequence"),
                Optional.empty(),
                Collections.emptyList(),
                Optional.empty(),Optional.empty()
                , Optional.empty(),
                Optional.empty()
                , Optional.empty(),context.getOptionalInteger("skinType"),
                context.getOptionalString("pcBanner"),
                context.getOptionalString("pcBannerPath"));
        ArrayList<CourseChapter> courseChapters = new ArrayList<>();
        courseAbilities.forEach(c -> courseChapters.addAll(c.getCourseChapters()));
        courseInfoService.saveCourseChapters(courseInfo.getId(), courseChapters);
        if (courseShelves.isPresent()) {
            courseAbilities.forEach(ability -> senderCourse(ability.getCourseChapters()));
        }
        return courseInfo;
    }

    private void senderCourse(List<CourseChapter> courseChapters) {
        if (!CollectionUtils.isEmpty(courseChapters)) {
            List<String> courseIds = courseChapters.stream()
                                                   .filter(x -> x.getCourseChapterSections() != null) // 检查是否为 null
                                                   .flatMap(x -> x.getCourseChapterSections().stream())
                                                   .filter(s -> null != s.getSectionType() && CourseChapterSection.SECTION_TYPE_COURSE == s.getSectionType())
                                                   .map(CourseChapterSection::getResourceId)
                                                   .filter(org.springframework.util.StringUtils::hasText)
                                                   .collect(Collectors.toList());
            //发送搜索监听,更新课程的引用专题
            if (!CollectionUtils.isEmpty(courseIds)) {
                courseIds.forEach(r -> messageSender.send(MessageTypeContent.COURSE_INFO_UPDATE, MessageHeaderContent.ID, r));
            }
        }
    }

    /**
     * 校验专题信息是否完整，专题添加证书后验证规则改变，配置证书时必须有一个必修章节，其他没有配置证书时不需要验证
     */
    private void validationSubject2(List<AudienceItem> audienceItems, List<CourseChapter> courseChapters, List<CourseAttachment> courseAttachments, Optional<String> subjectUrl,
                                    Optional<String> styles, Optional<String> certificateId, Optional<Integer> publishType, Optional<Integer> type) {
        if (audienceItems == null || audienceItems.isEmpty()) {
            throw new ValidationException(ErrorCode.AudienceItemsNotNull);
        }

        if (courseAttachments != null) {
            courseInfoAdminKit.validationAttachments(courseAttachments);
        }

        /**
         * 1.判断上架类型为正式发布，
         * 2.判断专题内课程状态，
         * 3.若有不是已发布状态课程则抛异常
         */
        publishType.ifPresent(t -> {
            if (t == CourseInfo.PUBLISH_TYPE_FORMAL) {
                Integer l = type.orElse(0);
                if (l.equals(3) || l.equals(5))
                    validateCourses(courseChapters);
            }
        });

    }

    /**
     * 验证课程中是否包含未发布的课程活资源
     */
    private void validateCourses(List<CourseChapter> courseChapters) {
        List<String> sectionIds = null;
        if (!CollectionUtils.isEmpty(courseChapters)) {
            sectionIds = courseChapters.stream().filter(chapter -> !CollectionUtils.isEmpty(chapter.getCourseChapterSections()))
                                       .flatMap(chapter -> chapter.getCourseChapterSections().stream())
                                       .map(CourseChapterSection::getResourceId)
                                       .collect(Collectors.toList());
            //未发布课程的课程数量
            Long num = courseInfoService.getChapterSectionStatus(Optional.ofNullable(sectionIds));
            if (Objects.nonNull(num) && num != 0) {
                throw new ValidationException(ErrorCode.CourseInfoNotInShelvesOrIsNull);
            }
        }
    }

    /***
     * 转换上架(发布)信息
     *
     * @param shelves
     * @return
     */
    private Optional<CourseShelves> parseShelves(Optional<String> shelves) {
        Optional<CourseShelves> courseShelves;
        try {
            courseShelves = shelves.map(s -> {
                CourseShelves cs = com.alibaba.fastjson.JSON.parseObject(s, CourseShelves.class);
                return cs;
            });
        } catch (Exception ex) {
            throw new ValidationException(ErrorCode.JsonTransformFail);
        }
        courseShelves.ifPresent(cs -> {
            int noticeUser = cs.getNoticeUser() != null ? cs.getNoticeUser().intValue() : CourseShelves.NOTICE_NO;
            if (noticeUser == CourseShelves.NOTICE_YES
                && com.alibaba.dubbo.common.utils.StringUtils.isEmpty(cs.getNoticeUserContent())) {
                throw new ValidationException(ErrorCode.NoticeTextNotNull);
            }
            cs.setRule(cs.getRule() != null ? cs.getRule() : CourseShelves.RULE_NOT_AFFECT);
        });
        return courseShelves;
    }

    /**
     * 学习地图修改
     */
    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", required = true)
    @Param(name = "name", required = true) // 专题名称
    @Param(name = "publishClient", type = Integer.class, required = true) // 发布终端，0:全部, 1: PC, 2: APP
    @Param(name = "publishType", type = Integer.class) // 发布类型 0-定向(测试) 1-正式
    @Param(name = "beginDate", type = Long.class) // 开始时间
    @Param(name = "endDate", type = Long.class) // 结束时间
    @Param(name = "description") // 专题简介
    @Param(name = "descriptionText") // 专题简介-纯文本格式
    @Param(name = "releaseMemberId") // 发布人
    @Param(name = "releaseOrgId") // 发布部门
    @Param(name = "organizationId") // 所属部门
    @Param(name = "code") // 专题编码
    @Param(name = "courseChapters") // 专题章节
    @Param(name = "courseAttachments") // 专题附件
    @Param(name = "audienceItems") // 专题受众对象
    @Param(name = "shelves") // 上架(发布)信息
    @Param(name = "studyDays", type = Integer.class) // 建议学习天数
    @Param(name = "cover") // 头像
    @Param(name = "coverPath") // 头像
    @Param(name = "coverMaterialId") // 封面素材id
    @Param(name = "appBanner")
    @Param(name = "pcBanner")
    @Param(name = "pcBannerPath")
    @Param(name = "appBannerPath")
    @Param(name = "styles") // 风格配置
    @Param(name = "topicIds") // 相关话题
    @Param(name = "advertisings") // 专题广告
    @Param(name = "photos") // 专题相册
    @Param(name = "textAreas") // 专题文字区域
    @Param(name = "url") // 个性化专题链接地址
    @Param(name = "status", type = Integer.class)
    @Param(name = "type")//用于区分审计功能
    @Param(name = "businessType")
    @Param(name = "shareSub")//2018-01-30  专题新增是否分享下级使用
    @Param(name = "addType", type = Integer.class) // 添加类别 普通模式,章节模式
    @Param(name = "isPublic", type = Integer.class) // 非公开专题
    @Param(name = "descriptionApp", type = String.class) // 简介--app加粗换行样式 updated by wangdongyan
    @Param(name = "certificateId", type = String.class) // 专题配置的证书id
    @Param(name = "isSign", type = Integer.class) // 专题是否需要报名（新动能重塑培训新增功能）
    @Param(name = "managerItems") // 专题管理员
    @Param(name = "explicitLearningStatus", type = Integer.class)
    @Param(name = "learnSequence", type = Integer.class)
    @Param(name = "skinType", type = Integer.class)
    @JSON("id")
    public CourseInfo update(RequestContext context, Subject<Member> memberSubject) {
        boolean isPublish = context.getOptionalInteger("status").map(s -> s == CourseInfo.STATUS_SHELVES || s == CourseInfo.STATUS_THE_TEST).orElse(false);
        Optional<CourseShelves> courseShelves = parseShelves(context.getOptionalString("shelves"));

        Integer subjectStatus = courseCacheService.getSubjectPublishStatus(context.get("id", String.class));
        if (null != subjectStatus && subjectStatus.equals(CourseInfo.STATUS_IN_SHELVES)) {
            throw new UnprocessableException(ErrorCode.CourseStatusIsAnnouncing);
        }
        List<CourseAbility> courseAbilities = courseInfoAdminKit.parseCourseAbilitys(context.getOptionalString("courseChapters"));
        List<CourseAttachment> courseAttachments = courseInfoAdminKit.parseCourseAttachment(context.getOptional("courseAttachments", String.class));
        List<AudienceItem> audienceItems = audienceKit.parseAudienceItems(context.getOptionalString("audienceItems"));
        // 发布时||已发布状态下点击保存，加入校验
        if ((courseShelves.isPresent() || isPublish) && !ObjectUtils.isEmpty(courseAbilities)) {
            courseAbilities.forEach(ability -> validationSubject2(audienceItems, ability.getCourseChapters(), courseAttachments, context.getOptionalString("url"),
                context.getOptionalString("styles"), context.getOptionalString("certificateId"), context.getOptionalInteger("publishType"), context.getOptionalInteger("type")));
        }

        CourseInfo courseInfo = subjectService.update(
            context.get("id", String.class),
            memberSubject.getCurrentUserId(),
            context.getInteger("publishClient"),
            context.getOptionalInteger("publishType"),
            context.getOptional("name", String.class),
            context.getOptional("beginDate", Long.class),
            context.getOptional("endDate", Long.class),
            context.getOptional("description", String.class),
            context.getOptional("descriptionText", String.class),
            context.getOptional("releaseMemberId", String.class),
            context.getOptional("releaseOrgId", String.class),
            context.getOptional("organizationId", String.class),
            context.getOptional("code", String.class),
            context.getOptional("learnSequence", Integer.class),
            context.getOptional("studyDays", Integer.class),
            context.getOptionalString("cover"),
            context.getOptionalString("coverPath"),
            context.getOptionalString("coverMaterialId"),
            context.getOptionalInteger("shareSub"),
            new ArrayList<>(),
            courseAttachments,
            audienceItems,
            courseShelves,
            context.getOptionalString("styles"),
            context.getOptionalString("topicIds"),
            new ArrayList<>(),
            new ArrayList<>(),
            new ArrayList<>(),
            context.getOptionalString("url"),
            context.getOptionalInteger("addType"),
            context.getOptionalInteger("isPublic"),
            context.getOptionalString("descriptionApp"),
            context.getOptionalString("certificateId"),
            context.getOptionalInteger("isSign"),
            new ArrayList<>(),
            context.getOptionalInteger("explicitLearningStatus"),
                Optional.empty(),
                Collections.emptyList(),
                Optional.empty(),
                Optional.empty(),
                Optional.empty(),
                Optional.empty(),
                Optional.empty(),
                context.getOptionalInteger("skinType"),
                context.getOptionalString("pcBanner"),
                context.getOptionalString("pcBannerPath")
        );

        ArrayList<CourseChapter> courseChapters = new ArrayList<>();
        courseAbilities.forEach(c -> courseChapters.addAll(c.getCourseChapters()));
        courseInfoService.saveCourseAbilities(courseInfo.getId(), courseAbilities);
        if (courseShelves.isPresent() || isPublish) {
            senderCourse(courseChapters);
        }

        //专题的修改埋点-同步ihr
        boolean isEdit = context.getOptionalInteger("type").map(s -> s == 1 || s == 2).orElse(false);
        if (isEdit) {
            //专题培训班埋点-修改,关联的培训班数据同步
            messageSender.send(com.zxy.product.course.content.MessageTypeContent.IHR_TOPIC_TRAIN_SYNC,
                MessageHeaderContent.OPERATION_STATE, CourseInfo.OPERATION_STATE_MODIFY,
                MessageHeaderContent.TOPIC_ID, context.get("id", String.class));
        }
        return courseInfo;
    }

    /**
     * 学习地图复制
     */
    @RequestMapping(value = "/copy", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "courseId", required = true)
    @JSON("result")
    public Map<String, Object> copy(RequestContext rc) {
        return ImmutableMap.of("result", courseInfoService.copyStudyMap(rc.getString("courseId")));
    }
    /**
     * 个人中心-我的学习专题
     */
    @RequestMapping(value = "/person-study-map-list-map", method = RequestMethod.GET)
    @Permitted
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "findStudy", type = Integer.class)
    @Param(name = "name")
    @Param(name = "finishStatus", type = Integer.class)//学习状态，0-未开始，1-学习中，2-已完成，3-已放弃 ，4-标记完成
    @Param(name = "isRequired", type = Integer.class)//0选修，1必修
    @Param(name = "studyTimeOrder")//学习时间排序
    @Param(name = "startTime", type = Long.class)
    @Param(name = "endTime", type = Long.class)
    @JSON("recordCount")
    @JSON("more")
    @JSON("items.(id, memberId, courseId,beginTime,type,isRequired, finishStatus, finishTime,studyTotalTime,"
        + "registerTime,lastAccessTime,markMemberId,markTime,completedRate,currentChapterId,currentSectionId)")
    @JSON("items.courseInfo.(name,cover,coverPath,url,versionId,status,publishClient,display,learnSequence,description)")
    public Map<String, Object> personCourseMap(RequestContext context, Subject<Member> subject) {
        int pageSwitch = ruleConfigService.getByName("1", RuleConfig.KEY.PC_PAGE_STYLE).map(x-> Integer.parseInt(x.getValue())).orElse(0);
        List<String> ids = Arrays.asList(subject.getCurrentUserId());
        return courseInfoService.findPersonCourseStudyMap(context.get("page", Integer.class),
            context.get("pageSize", Integer.class),
            CourseInfo.BUSINESS_TYPE_STUDY_MAP,
            ids, context.getOptionalInteger("findStudy"),
            context.getOptionalString("name"),
            context.getOptionalInteger("finishStatus"),
            context.getOptionalInteger("isRequired"),
            context.getOptionalString("studyTimeOrder"),
            pageSwitch == 1,
            context.getOptionalLong("startTime"),
            context.getOptionalLong("endTime"));
    }

}
