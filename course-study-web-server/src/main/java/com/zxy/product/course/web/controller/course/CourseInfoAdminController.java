package com.zxy.product.course.web.controller.course;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.office.excel.export.Writer;
import com.zxy.common.office.excel.export.support.ExcelWriter;
import com.zxy.common.restful.RequestContext;
import com.zxy.common.restful.annotation.Param;
import com.zxy.common.restful.audit.Audit;
import com.zxy.common.restful.json.JSON;
import com.zxy.common.restful.security.Permitted;
import com.zxy.common.restful.security.SecurityManager;
import com.zxy.common.restful.security.Subject;
import com.zxy.common.restful.util.Encrypt;
import com.zxy.common.restful.validation.ValidationException;
import com.zxy.product.askbar.api.StudioService;
import com.zxy.product.course.api.*;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.course.CourseInfoAdminService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.dto.knowledge.graph.SynchronousNodeDTO;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.util.StringUtils;
import com.zxy.product.course.web.kit.AudienceKit;
import com.zxy.product.course.web.kit.CourseInfoAdminKit;
import com.zxy.product.course.web.kit.HomeCertifyKit;
import com.zxy.product.course.web.util.BrowserUtil;
import com.zxy.product.course.web.util.ImportExcelUtil;
import com.zxy.product.human.api.MemberService;
import com.zxy.product.human.content.MessageTypeContent;
import com.zxy.product.human.content.RedisKeys;
import com.zxy.product.human.entity.StudyPlan;
import com.zxy.product.system.api.homeconfig.CourseVirtualSpaceService;
import com.zxy.product.system.api.internalswitch.InternalSwitchService;
import com.zxy.product.system.api.permission.GrantService;
import com.zxy.product.system.api.permission.OrganizationService;
import com.zxy.product.system.api.permission.SignService;
import com.zxy.product.system.api.setting.RuleRedShipConfigService;
import com.zxy.product.system.api.topic.TopicService;
import com.zxy.product.system.entity.CourseVirtualSpace;
import com.zxy.product.system.entity.GrantOrganization;
import com.zxy.product.system.entity.InternalSwitch;
import com.zxy.product.system.entity.Topic;
import com.zxy.product.system.util.CodeUtils;
import com.zxy.product.system.util.DateUtil;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.EnvironmentAware;
import org.springframework.core.env.Environment;
import org.springframework.stereotype.Controller;
import org.springframework.util.ObjectUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.zxy.product.course.content.MessageTypeContent.GRAPH_CASE_NODE;
import static com.zxy.product.course.content.MessageTypeContent.GRAPH_COURSE_NODE;
import static com.zxy.product.course.content.model.mentor.MentorCourseTypeEnum.TurnOnModelMentor;
import static java.util.stream.Collectors.groupingBy;

/**
 *
 * <AUTHOR>
 * @date 2017/9/8
 * 新优化的控制层
 */
@Controller
@RequestMapping("/course-info/admin")
public class CourseInfoAdminController implements EnvironmentAware {
    private static final String SIGN_PREFIX = "course-info-admin";
    private  final  static String  INTERNAL_ORGANIZATION_ID = "10000001";
    private  final  static String  KEY = "PublicationRestriction";
    private static final String INTERNAL_ORGANIZATION_AUDIENCE_ITEMS ="[{'joinId':'10000001','joinType':'2','joinName':'内部组织'}]";
    private static final int TYPE = 88802;
    private static final String ORGANIZATION_ID = "organization_id";
    private static final String MODULE = "module";
    private static final String SUB_MODULE = "sub-module";
    private static final String ACTION = "action";
    private static final String FIRST_ACTION = "first-action";
    private static final String SECOND_ACTION = "second-action";
    private static final String MEMBER_FULL_NAME = "member_full_name";
    private static final String CURRENT_DATE = "current-date";
    private static final String DESC = "desc";
    private static final String DESC_REQUEST = "desc-request";
    private static final String DESC_REST_TEMPLATE_KEYS = "desc-rest-template-keys";
    private static final String DESC_REST_TEMPLATE_IDS = "desc-rest-template-ids";
    private static final String DESC_REST_TEMPLATE_JSONS = "desc-rest-template-jsons";
    private static final String USER_AGENT = "user-agent";
    private static final String IP = "ip";
    private static final int FIRST_RELEASE_0 =0;
    private static final int FIRST_RELEASE_1 =1;
    private static final int CONSTRUCTION_TYPE_FLAG_1 =1;
    private static final int CONSTRUCTION_TYPE_FLAG_0 =0;
    private static final int FLAG_1 =1;
    private static final int FLAG_0 =0;


    private InternalSwitchService internalSwitchService;
    private CourseInfoAdminService courseInfoAdminService;
    private CourseInfoAdminKit courseInfoAdminKit;
    private AudienceKit audienceKit;
    private GrantService grantService;
    private OrganizationService organizationService;
    private CourseInfoService courseInfoService;
    private TopicService topicService;
    private BusinessTopicService businessTopicService;
    private CourseCacheService courseCacheService;
    private SignService signService;
    private String aesKey;
    private StudioService StudioService;
    private RedissonClient redissonClient;
    private CourseStudyPlanConfigService courseStudyPlanConfigService;
    private MessageSender messageSender;
    private CaptionService captionService;
    private MemberService memberService;

    private CourseVirtualSpaceService courseVirtualSpaceService;

    private com.zxy.product.course.api.OrganizationService courseOrganizationService;

    private  Cache cache;

    private KnowledgeGraphService knowledgeGraphService;

    private RuleRedShipConfigService ruleRedShipConfigService;
    @Resource
    private HomeCertifyKit homeCertifyKit;

    private SecurityManager<?> securityManager;

    @Autowired
    public void setSecurityManager(SecurityManager<?> securityManager) {
        this.securityManager = securityManager;
    }

    @Autowired
    public void setRuleRedShipConfigService(RuleRedShipConfigService ruleRedShipConfigService) {
        this.ruleRedShipConfigService = ruleRedShipConfigService;
    }

    @Autowired
    public void setKnowledgeGraphService(KnowledgeGraphService knowledgeGraphService){
        this.knowledgeGraphService=knowledgeGraphService;
    }

    @Autowired
    public void setCache(CacheService cache) {
        this.cache = cache.create("anti-corruption", InternalSwitch.KEY);
    }


    @Autowired
    public void setInternalSwitchService(InternalSwitchService internalSwitchService) {
        this.internalSwitchService = internalSwitchService;
    }

    @Autowired
    public void setCourseOrganizationService(com.zxy.product.course.api.OrganizationService courseOrganizationService) {
        this.courseOrganizationService = courseOrganizationService;
    }

    @Autowired
    public void setCourseVirtualSpaceService(CourseVirtualSpaceService courseVirtualSpaceService) {
        this.courseVirtualSpaceService = courseVirtualSpaceService;
    }

    @Autowired
    public void setStudioService(com.zxy.product.askbar.api.StudioService studioService) {
        StudioService = studioService;
    }

    @Autowired
    public void setSignService(SignService signService) {
        this.signService = signService;
    }
    @Autowired
    public void setGrantService(GrantService grantService) {
        this.grantService = grantService;
    }

    private static final Logger logger = LoggerFactory.getLogger(CourseInfoAdminController.class);
    @Autowired
    public void setAudienceKit(AudienceKit audienceKit) {
        this.audienceKit = audienceKit;
    }

    @Autowired
    public void setCourseInfoAdminKit(CourseInfoAdminKit courseInfoAdminKit) {
        this.courseInfoAdminKit = courseInfoAdminKit;
    }

    @Autowired
    public void setCourseInfoAdminService(CourseInfoAdminService courseInfoAdminService) {
        this.courseInfoAdminService = courseInfoAdminService;
    }

    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }

    @Autowired
    public void setTopicService(TopicService topicService) {
		this.topicService = topicService;
	}

    @Autowired
	public void setBusinessTopicService(BusinessTopicService businessTopicService) {
		this.businessTopicService = businessTopicService;
	}

	@Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }
    @Autowired
    public void setRedissonClient(RedissonClient redissonClient) {
        this.redissonClient = redissonClient;
    }
    @Autowired
    public void setCourseStudyPlanConfigService(CourseStudyPlanConfigService courseStudyPlanConfigService) {
        this.courseStudyPlanConfigService = courseStudyPlanConfigService;
    }
    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setCaptionService(CaptionService captionService) {
        this.captionService = captionService;
    }

    @Override
    public void setEnvironment(Environment environment) {
        aesKey = environment.getProperty("aes.key", "d8cg8gVakEq9Agup");
    }

    @Autowired
    public void setMemberService(MemberService memberService) {
        this.memberService = memberService;
    }

    @RequestMapping(method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "categoryId")
    @Param(name = "code")
    @Param(name = "status", type = Integer.class)
    @Param(name = "source", type = Integer.class)
    @Param(name = "releaseUserId") // 发布人
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "shelveBeginDate", type = String.class)
    @Param(name = "shelveEndDate", type = String.class)
    @Param(name = "contain",type=Integer.class)	//是否包含子级【0：不包含；1：包含】
    @Param(name = "releaseBeginDate", type = String.class) // 最新发布开始时间
    @Param(name = "releaseEndDate", type = String.class)// 最新发布结束时间 updated by wangdongyan 1020新需求开发
    @Param(name = "isParty",type = Integer.class) //是否是党建课程 【0：不是；1：是】
    @Param(name = "uri")
    @Param(name = "allCategory") //1: 查询主副所有序列 0:或者没有只查询主序列
    @Param(name = "redAuditStatus") // 红船审核状态：0审核中 1通过审核 2 未通过审核 3 超时 4 准备中 5准备失败
    @Param(name = "checkAuditStatus") // 复核状态：0：待复核，1：采纳，2：未采纳
    @Param(name = "captionOverallStatus")//字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)
    @Param(name = "mentorState")
    @JSON("recordCount")
    @JSON("items.(id,name,type,publishType,createTime,source,status,portalNum,courseHour,credit,beginDate,endDate,cover,coverPath,shelveTime,isPublic,code,publishClient,visits,url,avgScore,description,shareSub,price, releaseTime,relativeGenseeId,captionOverallStatus,auditFlag,mentorState,skinType,switchMentor,open,constructionType)")
    @JSON("items.organization.(id,name)")
    @JSON("items.createUser.(id,name,fullName)")
    @JSON("items.releaseUser.(id,name,fullName)")
    @JSON("items.category.(id,name)")
    @JSON("items.courseRedShipAuditDetail.(id,redAuditStatus,checkAuditStatus,redAuditTime)")
    public PagedResult<CourseInfo> pagedResult(RequestContext context, Subject<Member> memberSubject) {
        Optional<String> organizationId = context.getOptionalString("organizationId");
        List<String> grantOrganizationIds = null;
        if (context.getOptionalInteger("contain").orElse(0) == 0) {
        	grantOrganizationIds = courseInfoAdminKit.findGrantTopOrganizationIds(memberSubject.getCurrentUserId(),organizationId,context.getOptionalString("uri").orElse(CourseInfo.URI));
		}else {
			grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(memberSubject.getCurrentUserId(),organizationId,context.getOptionalString("uri").orElse(CourseInfo.URI));
		}
        PagedResult<CourseInfo> result =  courseInfoAdminService.find(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                memberSubject.getCurrentUserId(),
                grantOrganizationIds,
                CourseInfo.BUSINESS_TYPE_COURSE,
                context.getOptionalString("name"),
                context.getOptionalString("organizationId"),
                context.getOptionalString("categoryId"),
                context.getOptionalString("code"),
                context.getOptionalInteger("status"),
                context.getOptionalInteger("source"),
                context.getOptionalString("releaseUserId"),
                context.getOptionalInteger("publishClient"),
                StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginDate")),
                StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndDate")),
                StringUtils.dateString2OptionalLong(context.getOptionalString("releaseBeginDate")),
                StringUtils.dateString2OptionalLongMore(context.getOptionalString("releaseEndDate")),
                context.getOptionalInteger("isParty"),
                context.getOptionalInteger("allCategory"),
                context.getOptionalInteger("redAuditStatus"),
                context.getOptionalInteger("checkAuditStatus"),
                context.getOptionalInteger("captionOverallStatus"),
                context.getOptionalInteger("mentorState")
        );

        return findRedShipAuditFlag(result);
    }

    /**
     * 查询哪些组织有红船审核
     */
    private PagedResult<CourseInfo> findRedShipAuditFlag(PagedResult<CourseInfo> result) {
        List<CourseInfo> items = result.getItems();
        List<String> ids = items.stream().map(CourseInfo::getOrganizationId).collect(Collectors.toList());
        Set<String> orgIds = ruleRedShipConfigService.findByOrgIds(ids);
        setAuditFlag(items, orgIds);
        return PagedResult.create(result.getRecordCount(), items);
    }

    private void setAuditFlag(List<CourseInfo> items, Set<String> orgIds) {
        for (CourseInfo courseInfo : items) {
            if (orgIds.contains(courseInfo.getOrganizationId()) || courseInfo.getOrganizationId().equals(Organization.ROOT_ORGANIZATION)) {
                courseInfo.setAuditFlag(CourseInfo.STATUS_ENABLED);
            } else {
                courseInfo.setAuditFlag(CourseInfo.STATUS_FORBIDDEN);
            }
        }
    }

    @RequestMapping(method = RequestMethod.GET, value = "/select")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "client", type = Integer.class) // 课程类型
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "category")
    @Param(name = "selectIds")
    @Param(name = "uri")
    @Param(name = "isParty",type = Integer.class) //是否是党建课程 【0：不是；1：是】
    @Param(name = "courseStatus")//课程状态
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,source,status,portalNum,organizationId,courseHour,credit,beginDate,endDate,cover,coverPath,shelveTime,isPublic,code,publishClient,visits,description,integral,courseTime)")
    @JSON("items.organization.(id,name)")
    @JSON("items.category.(id,name)")
    public PagedResult<CourseInfo> findSelect(RequestContext context, Subject<Member> memberSubject) {
        Optional<List<Integer>> courseStatus = context.getOptionalString("courseStatus").map(s-> Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));
        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));
        Optional<String> organizationId = context.getOptionalString("organizationId");
        String uri = context.getOptionalString("uri").orElse(CourseInfo.URI);
        List<String> grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(memberSubject.getCurrentUserId(),organizationId, uri);
        Optional<List<String>> parentIds = Optional.empty();
        if (!organizationId.isPresent()) {
            List<String> parentOrganizationIds = grantService.findGrantedOrganizationWithParent(memberSubject.getCurrentUserId(), uri, null).stream().map(a -> a.getId()).collect(Collectors.toList());
            parentIds = Optional.of(parentOrganizationIds.stream()
                    .filter(x->!grantOrganizationIds.contains(x)).collect(Collectors.toList())
            );
        }
        //如果组织数量超过6000，则去查当前登录者grant_organization数据关联去查，不用in的方式，用in的方式性能太差
        if(grantOrganizationIds != null && grantOrganizationIds.size() > 6000) {
        	List<GrantOrganization> goList = grantService.findGrantOrganizationByMemberAndUri(memberSubject.getCurrentUserId(),uri);
        	//如果有中国移动或内部组织节点的授权则关联查询
        	List<GrantOrganization> topList = goList.stream().filter(x -> ("1".equals(x.getOrganizationId()) || "10000001".equals(x.getOrganizationId())) && x.getChildFind() == 1).collect(Collectors.toList());
        	if(topList.size() > 0) {
        		return courseInfoAdminService.findSelectNormal(
                        context.get("page", Integer.class),
                        context.get("pageSize", Integer.class),
                        context.getOptionalInteger("client"),
                        context.getOptionalString("name"),
                        context.getOptionalString("category"),
                        context.getOptionalInteger("isParty"),
                        topList.stream().map(GrantOrganization::getOrganizationId).collect(Collectors.toList()),
                        parentIds,
                        selectIds,
                        courseStatus,
                        null);
        	}
        	//如果有且只有一个grant_organization并且包含子节点，去关联查询
        	if(goList.size() == 1 && goList.get(0).getChildFind() == 1) {
        		return courseInfoAdminService.findSelectNormal(
                        context.get("page", Integer.class),
                        context.get("pageSize", Integer.class),
                        context.getOptionalInteger("client"),
                        context.getOptionalString("name"),
                        context.getOptionalString("category"),
                        context.getOptionalInteger("isParty"),
                        goList.stream().map(GrantOrganization::getOrganizationId).collect(Collectors.toList()),
                        parentIds,
                        selectIds,
                        courseStatus,
                        null);
        	}
        }
        return courseInfoAdminService.findSelect(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptionalInteger("client"),
                context.getOptionalString("name"),
                context.getOptionalString("category"),
                context.getOptionalInteger("isParty"),
                grantOrganizationIds,
                parentIds,
                selectIds,
                courseStatus,
                null);
    }


    @RequestMapping(method = RequestMethod.POST, value = "/select")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "client", type = Integer.class) // 课程类型
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "category")
    @Param(name = "selectIds")
    @Param(name = "uri")
    @Param(name = "courseStatus")//课程状态
    @Param(name = "captionOverallStatus")//字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,source,status,portalNum,organizationId,courseHour,credit,beginDate,endDate,cover,coverPath,shelveTime,isPublic,code,publishClient,visits,description,integral,courseTime)")
    @JSON("items.organization.(id,name)")
    @JSON("items.category.(id,name)")
    public PagedResult<CourseInfo> findSelectCourseListPostRequest(RequestContext context, Subject<Member> memberSubject) {
        Optional<List<Integer>> captionOverallStatus = context.getOptionalString("captionOverallStatus").map(s-> Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));

        Optional<List<Integer>> courseStatus = context.getOptionalString("courseStatus").map(s-> Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));

        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));
        Optional<String> organizationId = context.getOptionalString("organizationId");
        String uri = context.getOptionalString("uri").orElse(CourseInfo.URI);
        List<String> grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(memberSubject.getCurrentUserId(),organizationId, uri);
        Optional<List<String>> parentIds = Optional.empty();
        if (!organizationId.isPresent()) {
            List<String> parentOrganizationIds = grantService.findGrantedOrganizationWithParent(memberSubject.getCurrentUserId(), uri, null).stream().map(a -> a.getId()).collect(Collectors.toList());
            parentIds = Optional.of(parentOrganizationIds.stream()
                    .filter(x->!grantOrganizationIds.contains(x)).collect(Collectors.toList())
                );
        }
        //如果组织数量超过6000，则去查当前登录者grant_organization数据关联去查，不用in的方式，用in的方式性能太差
        if(grantOrganizationIds != null && grantOrganizationIds.size() > 6000) {
        	List<GrantOrganization> goList = grantService.findGrantOrganizationByMemberAndUri(memberSubject.getCurrentUserId(),uri);
        	//如果有中国移动或内部组织节点的授权则关联查询
        	List<GrantOrganization> topList = goList.stream().filter(x -> ("1".equals(x.getOrganizationId()) || "10000001".equals(x.getOrganizationId())) && x.getChildFind() == 1).collect(Collectors.toList());
        	if(topList.size() > 0) {
        		return courseInfoAdminService.findSelectNormal(
                        context.get("page", Integer.class),
                        context.get("pageSize", Integer.class),
                        context.getOptionalInteger("client"),
                        context.getOptionalString("name"),
                        context.getOptionalString("category"),
                        context.getOptionalInteger("isParty"), topList.stream().map(GrantOrganization::getOrganizationId).collect(Collectors.toList()),
                        parentIds,
                        selectIds,
                        courseStatus,
                        captionOverallStatus);
        	}
        	//如果有且只有一个grant_organization并且包含子节点，去关联查询
        	if(goList.size() == 1 && goList.get(0).getChildFind() == 1) {
        		return courseInfoAdminService.findSelectNormal(
                        context.get("page", Integer.class),
                        context.get("pageSize", Integer.class),
                        context.getOptionalInteger("client"),
                        context.getOptionalString("name"),
                        context.getOptionalString("category"),
                        context.getOptionalInteger("isParty"), goList.stream().map(GrantOrganization::getOrganizationId).collect(Collectors.toList()),
                        parentIds,
                        selectIds,
                        courseStatus,
                        captionOverallStatus);
        	}
        }
        return courseInfoAdminService.findSelect(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptionalInteger("client"),
                context.getOptionalString("name"),
                context.getOptionalString("category"),
                context.getOptionalInteger("isParty"), grantOrganizationIds,
                parentIds,
                selectIds,
                courseStatus,
                captionOverallStatus);
    }

    @RequestMapping(method = RequestMethod.POST, value = "/selectForKnowledge")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "client", type = Integer.class) // 课程类型
    @Param(name = "name")
    @Param(name = "organizationId")
    @Param(name = "category")
    @Param(name = "selectIds")
    @Param(name = "uri")
    @Param(name = "courseStatus")//课程状态
    @Param(name = "captionOverallStatus")//字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,source,status,portalNum,organizationId,courseHour,credit,beginDate,endDate,cover,coverPath,shelveTime,isPublic,code,publishClient,visits,description,integral,courseTime)")
    @JSON("items.organization.(id,name)")
    @JSON("items.category.(id,name)")
    public PagedResult<CourseInfo> selectForKnowledge(RequestContext context, Subject<Member> memberSubject) {
        Optional<List<Integer>> captionOverallStatus = context.getOptionalString("captionOverallStatus").map(s-> Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));

        Optional<List<Integer>> courseStatus = context.getOptionalString("courseStatus").map(s-> Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).boxed().collect(Collectors.toList()));

        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));
        Optional<String> organizationId = context.getOptionalString("organizationId");
        String uri = context.getOptionalString("uri").orElse(CourseInfo.URI);
        List<String> grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(memberSubject.getCurrentUserId(),organizationId, uri);
        Optional<List<String>> parentIds = Optional.empty();
        if (!organizationId.isPresent()) {
            List<String> parentOrganizationIds = grantService.findGrantedOrganizationWithParent(memberSubject.getCurrentUserId(), uri, null).stream().map(a -> a.getId()).collect(Collectors.toList());
            parentIds = Optional.of(parentOrganizationIds.stream()
                    .filter(x->!grantOrganizationIds.contains(x)).collect(Collectors.toList())
            );
        }
        return courseInfoAdminService.findSelect(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptionalInteger("client"),
                context.getOptionalString("name"),
                context.getOptionalString("category"),
                context.getOptionalInteger("isParty"), grantOrganizationIds,
                parentIds,
                selectIds,
                courseStatus,
                captionOverallStatus);
    }

    @RequestMapping(method = RequestMethod.POST,value = "/find-status")
    @Permitted(perms = "course-study/subject-info")
    @Param(name = "selectIds",required = true)
    @JSON("id,status")
    public List<CourseInfo> findCourseStatusById(RequestContext context, Subject<Member> memberSubject){
        Optional<String[]> selectIds = context.getOptionalString("selectIds").map(ids -> ids.split(","));

        List<CourseInfo> status = courseInfoAdminService.findStatus(selectIds);
        if (CollectionUtils.isEmpty(status)) throw new ValidationException(ErrorCode.DJDataNotVerify);
        return status;


    }


    @RequestMapping(method = RequestMethod.POST)
    @Param(name = "name")
    @Param(name = "description")
    @Param(name = "categoryId")
    @Param(name = "lecturer")
    @Param(name = "source", type = Integer.class)
    @Param(name = "sourceDetail")
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "organizationId")
    @Param(name = "releaseOrgId")
    @Param(name = "releaseMemberId")
    @Param(name = "cover")
    @Param(name = "coverPath")
    @Param(name = "courseTime", type = Integer.class) // 课程时长 秒
    @Param(name = "audiences") // 所有受众项
    @Param(name = "addType", type = Integer.class) // 新增类型 1 普通模式章节模式
    @Param(name = "shareSub", type = Integer.class) // 分享给下级
    @Param(name = "learnSequence", type = Integer.class) // 课程学习顺序
    @Param(name = "isPublic", type = Integer.class) // 非公开课程
    @Param(name = "publishType", type = Integer.class, defaultValue = "0") // 发布类型
    @Param(name = "courseChapters") // 课程章节
    @Param(name = "courseAttachments") // 课程附件
    @Param(name = "topicIds") // 相关话题
    @Param(name = "audienceItems") // 受众项
    @Param(name = "shelves") // 上架(发布)信息
    @Param(name = "type")//用于区分审计功能描述
    @Param(name = "descriptionText")// 描述纯文本内容，新需求开发 updated by wangdongyan 2018-06-04
    @Param(name = "descriptionApp")// 简介APP加粗换行样式updated by wangdongyan 2018-07-17
    @Param(name = "integral", type = Integer.class) // 所需积分
    @Param(name = "relativeGenseeId")
    @Param(name = "useVideoDoubleSpeed" ,type = Integer.class)//是否开启速播放功能
    @Param(name = "caption" ,type = Integer.class)//是否生成字幕标签
    @Param(name = "deputyCategoryIds")//副序列
    @Param(name = "smartTagName")//需要添加的智能标签
    @Param(name = "isThereAudioAndVideo")//是否有音视频,1=有,不传则代表无
    @Param(name = "switchMentor",type = Integer.class)//是否开启数智导师
    @Param(name = "captionFollowRelease",type = Integer.class)//字幕是否跟随课程发布
    @Param(name = "showStatus",type = Integer.class)//0=关闭,1=开启
    @Param(name = "explicitLearningStatus",type = Integer.class)
    @Param(name = "certificateType",type = Integer.class)
    @Param(name = "certificateTime",type = Long.class)
    @Param(name = "constructionType",type = Integer.class)// 全员共建共享，0=不是，1=是
    @JSON("*")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.INSERT, fisrtAction = "新增课程", desc = "新增课程《{0}》", params = {"name"}, businessType = "type,constructionType", businessValue = "1,0")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "正式发布", desc = "新增并正式发布课程《{0}》", params = {"name"}, businessType = "type,constructionType", businessValue = "2,0")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "定向发布", desc = "新增并定向发布课程《{0}》", params = {"name"}, businessType = "type,constructionType", businessValue = "3,0")
    public CourseInfo insert(RequestContext context, Subject<Member> memberSubject) {
        Optional<Integer> type = context.getOptionalInteger("type");
        boolean isSubjectPublic = false;
        //正式发布=2
        if (type.isPresent() && Objects.equals(type.get(), CourseInfo.BUSINESS_TYPE_SUBJECT)){
            issueJudgment(memberSubject, context.getOptionalString("audienceItems"));
        }

        CourseInfo courseInfo = this.conver(context);
        courseInfo.forInsert();
        courseInfo.setBusinessType(CourseInfo.BUSINESS_TYPE_COURSE);
        courseInfo.setStatus(CourseInfo.STATUS_UNPUBLISHED);
        courseInfo.setCreateMemberId(memberSubject.getCurrentUserId());
        Optional<Integer> switchMentor = context.getOptionalInteger("switchMentor");
        switchMentor.ifPresent(courseInfo::setSwitchMentor);
        courseInfo.setCaptionFollowRelease(context.getOptionalInteger("captionFollowRelease").orElse(0));
        logger.error("即将添加课程数智导师入参{}",JSONObject.toJSONString(switchMentor));
        courseInfo.setCode(CodeUtils.getModuleCode("course"));
        Optional<String > topicIds = context.getOptionalString("topicIds");
        List<CourseAttachment> courseAttachments = courseInfoAdminKit.parseCourseAttachment(context.getOptionalString("courseAttachments"));
        List<AudienceItem> audienceItems = audienceKit.parseAudienceItems(context.getOptionalString("audienceItems"));
        CourseShelves courseShelves = courseInfoAdminKit.parseShelves(context.getOptionalString("shelves"));
        List<CourseChapter> courseChapters = courseInfoAdminKit.parseCourseChapters(context.getOptionalString("courseChapters"));

        if(courseShelves != null) {
            courseInfoAdminKit.validationShelves(courseShelves);
            courseInfoAdminKit.validationChapter(courseChapters, CourseInfo.BUSINESS_TYPE_COURSE);
            courseInfoAdminKit.validationAttachments(courseAttachments);
            isSubjectPublic = true;
        }
        String [] ids = topicIds.map(x-> x.split(",")).orElse(null);

        List<String> deputyCategoryIds = context.getOptionalString("deputyCategoryIds").map(s -> s.split(",")).map(Arrays::asList).orElse(new ArrayList<>());
        ErrorCode.CheckDeputyCategorySize.throwIf(deputyCategoryIds.size()>CourseInfoCategory.DEPUTY_CATEGORY_MAX_SIZE);

        //是否生成字幕
        captionStatusJudge(context, courseInfo, courseShelves);
        //添加智能标签
        Optional<String> smartTagName = context.getOptionalString("smartTagName");
        if (smartTagName.isPresent()){
           ids = getSmartTagName(smartTagName.get(), ids);
        }
        Optional<Integer> optionalShowStatus = context.getOptionalInteger("showStatus");
        // captionStatus(courseInfo ,Optional.ofNullable(courseShelves), optionalShowStatus);
        optionalShowStatus.ifPresent(courseInfo::setShowStatus);
        courseInfo.setUpdateDate(System.currentTimeMillis());
        CourseInfo info = courseInfoAdminService.insert(
                courseInfo,
                courseChapters,
                courseAttachments,
                audienceItems,
                courseShelves,
                ids,
                deputyCategoryIds);

        if (ruleRedShipConfigService.getRootOrganizationId(info.getOrganizationId()) && !ObjectUtils.isEmpty(courseShelves)) {
            messageSender.send(com.zxy.product.course.content.MessageTypeContent.RED_SHIP_AUTO_AUDIT,
                    MessageHeaderContent.COURSE_ID, info.getId());
        }
        if (isSubjectPublic) {
            homeCertifyKit.certifySubjectHomeReset(courseInfo.getId());
        }

        sendAudit(context, courseInfo, courseShelves, FLAG_1, null);
        return info;
    }

    private  void whetherToGenerateCaption(RequestContext context, CourseInfo courseInfo) {
        if(context.getOptionalInteger("caption").isPresent()) {
            courseInfo.setCaptionFlag(context.getOptionalInteger("caption").get());
            Optional<Integer> isThereAudioAndVideo = context.getOptionalInteger("isThereAudioAndVideo");
            isThereAudioAndVideo.ifPresent(integer -> courseInfo.setCaptionOverallStatus(CourseInfo.CAPTION_OVERALL_STATUS_GENERATE));

            if (Objects.equals(context.getOptionalInteger("caption").get(), CourseInfo.CLOSE_CAPTION)){
                courseInfo.setCaptionOverallStatus(CourseInfo.CAPTION_OVERALL_STATUS_NO);
            }
        }else {
            courseInfo.setCaptionOverallStatus(CourseInfo.CAPTION_OVERALL_STATUS_NO);
        }
    }

    /**
     * 判断是否跟随发布
     *
     * @param course        课程详情
     * @param courseShelves 发布
     * @param showStatus
     */
    private void captionStatus(CourseInfo course, Optional<CourseShelves> courseShelves, Optional<Integer> showStatus) {
        if (Objects.equals(course.getCaptionFlag(), CourseInfo.CAPTION_FLAG) && Objects.equals(course.getCaptionFollowRelease(), CourseInfo.CAPTION_FOLLOW_RELEASE_YES) && (courseShelves.isPresent() || (Objects.equals(course.getStatus(), CourseInfo.STATUS_SHELVES) || Objects.equals(course.getStatus(), CourseInfo.STATUS_THE_TEST)))) {
            List<Integer> statusAll = captionService.findOtherStatus(course.getId());
            if (!CollectionUtils.isEmpty(statusAll)) {
                if (!statusAll.contains(Caption.CAPTION_STATUS_ANALYSISING)
                        && !statusAll.contains(Caption.CAPTION_STATUS_FAILED)
                        && !statusAll.contains(Caption.CAPTION_STATUS_UPLOAD_FAILED)) {
                    captionService.updateCourseStatusNew(course.getId(), Caption.CAPTION_STATUS_PUBLISHED, showStatus);
                    course.setCaptionOverallStatus(CourseInfo.CAPTION_OVERALL_STATUS_PUBLISH_ALL);
                }
            }
        }
    }

    private String[] getSmartTagName(String smartTagName, String[] ids) {
        List<String> topicNameList = Arrays.asList(smartTagName.split(","));
        List<String> topicIdList = Lists.newArrayList();
        if (!ObjectUtils.isEmpty(ids)) {
            topicIdList = new ArrayList<>(Arrays.asList(ids));
        }
        topicNameList = finByTopicName(topicNameList, topicIdList);
        List<String> newIds = topicService.insetIntelligentTopic(topicNameList);

        if (!topicIdList.isEmpty()) {
            if (!ObjectUtils.isEmpty(newIds)) {
                topicIdList.addAll(newIds);
            }
            return topicIdList.toArray(new String[topicIdList.size()]);
        }
        if (!ObjectUtils.isEmpty(newIds)) {
            return newIds.toArray(new String[newIds.size()]);
        }
        return ids;
    }


    private List<String> finByTopicName(List<String> topicNameList, List<String> topicIds) {
        List<String> topicNames = Lists.newArrayList();
        Map<String, String> mapTopicName = topicService.findTopicByName(topicNameList);
        for (String topicName : topicNameList) {
            if (mapTopicName.containsKey(topicName)) {
                topicIds(topicIds, mapTopicName.get(topicName));
            } else {
                topicNames.add(topicName);
            }

        }
        return topicNames;
    }

    private void topicIds(List<String> topicIds, String topicId) {
        if (!topicIds.contains(topicId)) {
            topicIds.add(topicId);
        }
    }


    /**
     * 发布判断,是否是中国移动或者内部组织,开关是否开启,是否再时间内
     * @param subject
     * @param audienceItems
     */
    private void issueJudgment(Subject<Member> subject, Optional<String> audienceItems) {
            //判断内部开关是否开启
            InternalSwitch internalSwitch = cache.get(InternalSwitch.KEY + KEY, () -> internalSwitchService.findByType(InternalSwitch.PUBLICATION_RESTRICTION), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
            if (!ObjectUtils.isEmpty(internalSwitch) && Objects.equals(internalSwitch.getStatus(), InternalSwitch.switchStatusNO)) {
                //白天不允许发布/时间使用逗号隔开,7,18
                List<String> date = Arrays.asList(internalSwitch.getDescribe().split(","));
                //判断是否是再限制的时间内
                publicationRestriction(
                        audienceItems,
                        subject.getRootOrganizationId(),
                        Integer.parseInt(date.get(1)),
                        Integer.parseInt(date.get(2))
                );
            }
    }


    @RequestMapping(value = "/{id}", method = RequestMethod.PUT)
    @Param(name = "id", required = true)
    @Param(name = "name")
    @Param(name = "description")
    @Param(name = "categoryId")
    @Param(name = "lecturer")
    @Param(name = "source", type = Integer.class)
    @Param(name = "sourceDetail")
    @Param(name = "publishClient", type = Integer.class)
    @Param(name = "organizationId")
    @Param(name = "releaseOrgId")
    @Param(name = "releaseMemberId")
    @Param(name = "cover")
    @Param(name = "coverPath")
    @Param(name = "courseTime", type = Integer.class) // 课程时长 秒
    @Param(name = "audiences") // 所有受众项

    @Param(name = "addType", type = Integer.class) // 新增类型 1 普通模式章节模式
    @Param(name = "shareSub", type = Integer.class) // 分享给下级
    @Param(name = "learnSequence", type = Integer.class) // 课程学习顺序
    @Param(name = "isPublic", type = Integer.class) // 非公开课程
    @Param(name = "publishType", type = Integer.class, defaultValue = "0") // 发布类型

    @Param(name = "courseChapters") // 课程章节
    @Param(name = "courseAttachments") // 课程附件
    @Param(name = "audienceItems") // 受众项
    @Param(name = "topicIds") // 相关话题
    @Param(name = "shelves") // 上架(发布)信息
    @Param(name = "type")//用于区分审计添加的功能
    @Param(name = "descriptionText")// 描述纯文本内容，新需求开发 updated by wangdongyan 2018-06-04
    @Param(name = "descriptionApp")// 描述app加粗换行样式，新需求开发 updated by wangdongyan 2018-07-17
    @Param(name = "integral", type = Integer.class) // 所需积分
    @Param(name = "relativeGenseeId")
    @Param(name = "useVideoDoubleSpeed" ,type = Integer.class)//是否开启速播放功能
    @Param(name = "caption" ,type = Integer.class)//是否生成字幕
    @Param(name = "deputyCategoryIds")//副序列
    @Param(name = "status" ,type = Integer.class)//课程状态
    @Param(name = "smartTagName")//需要添加的智能标签
    @Param(name = "isThereAudioAndVideo")//是否有音视频,1=有,不传则代表无
    @Param(name = "switchMentor",type = Integer.class)
    @Param(name = "captionFollowRelease",type = Integer.class)//字幕是否跟随课程发布
    @Param(name = "showStatus",type = Integer.class)//0=关闭,1=开启
    @Param(name = "explicitLearningStatus",type = Integer.class)
    @Param(name = "constructionType",type = Integer.class, value = "全员共建共享，0=不是，1=是")
    @Param(name = "firstRelease",type = Integer.class, defaultValue = "0", value = "是否是首次发布，0=不是，1=是")
    @Param(name = "flag",type = Integer.class, required = true, defaultValue = "0", value = "是否修改课程基本信息，0=不是，1=是")
    @Param(name = "constructionTypeFlag",type = Integer.class, required = true, value = "判断本次修改有没有勾选全员共建共享，0=不是，1=是")
    @JSON("*")
    //@Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.UPDATE, fisrtAction = "修改（含资源配置）", desc = "修改课程《{0}》", params = {"name"}, businessType = "type,constructionType", businessValue = "1,0")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "正式发布", desc = "修改并正式发布课程《{0}》", params = {"name"}, businessType = "type,constructionType", businessValue = "2,0")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.PUBLISH, fisrtAction = "发布", secondAction = "定向发布", desc = "修改并定向发布课程《{0}》", params = {"name"}, businessType = "type,constructionType", businessValue = "3,0")
    public CourseInfo update(RequestContext context,  Subject<Member> memberSubject) {

        CourseShelves courseShelves = courseInfoAdminKit.parseShelves(context.getOptionalString("shelves"));

        Optional<Integer> optionalStatus = context.getOptionalInteger("status");
        if ((optionalStatus.isPresent() && optionalStatus.get().equals(CourseInfo.STATUS_SHELVES)) || !ObjectUtils.isEmpty(courseShelves)){
            //判断内部开关是否开启
            InternalSwitch internalSwitch = cache.get(InternalSwitch.KEY + KEY, () -> internalSwitchService.findByType(InternalSwitch.PUBLICATION_RESTRICTION), DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
            if (!ObjectUtils.isEmpty(internalSwitch) && Objects.equals(internalSwitch.getStatus(), InternalSwitch.switchStatusNO)){
                //白天不允许发布/时间使用逗号隔开,7,18
                List<String> date = Arrays.asList(internalSwitch.getDescribe().split(","));
                //判断是否是再限制的时间内
                publicationRestriction(
                        context.getOptionalString("audienceItems"),
                        memberSubject.getRootOrganizationId(),
                        Integer.parseInt(date.get(1)),
                        Integer.parseInt(date.get(2))
                );
            }
        }
        List<AudienceItem> audienceItems = audienceKit.parseAudienceItems(context.getOptionalString("audienceItems"));


        String courseId = context.getString("id");
        /*Map<String, String> tagMap = queryExistRelation(courseId);*/
        CourseInfo courseInfo = this.conver(context);
        courseInfo.setId(courseId);
        courseInfo.setCreateMemberId(memberSubject.getCurrentUserId());
        Optional<Integer> switchMentor = context.getOptionalInteger("switchMentor");
        logger.error("修改课程入参数智导师{}",JSONObject.toJSONString(switchMentor));
        switchMentor.ifPresent(courseInfo::setSwitchMentor);
        switchMentor.filter(ew1->Objects.equals(CourseInfo.STATUS_ENABLED,ew1)).ifPresent(ew2-> courseInfo.setMentorState(TurnOnModelMentor.getCode()));
        List<CourseAttachment> courseAttachments = courseInfoAdminKit.parseCourseAttachment(context.getOptionalString("courseAttachments"));
        List<CourseChapter> courseChapters = courseInfoAdminKit.parseCourseChapters(context.getOptionalString("courseChapters"));


        Optional<String > topicIds = context.getOptionalString("topicIds");

        CourseInfo info = courseInfoService.getCourseInfo(courseId);
        if(courseShelves != null) {
            if (CourseInfo.SOURCE_STUDIO == info.getSource()) {
                StudioService.getStudioByBusinessId(info.getId());
            }
            // 如果课程正在发布则不能发布课程
            Integer subjectStatus = courseCacheService.getSubjectPublishStatus(courseId);
            if (null != subjectStatus && subjectStatus.equals(CourseInfo.STATUS_IN_SHELVES)) {
                throw new UnprocessableException(ErrorCode.CourseStatusIsAnnouncing);
            }
            courseInfoAdminKit.validationShelves(courseShelves);
            if(hasEditchapter(courseId)){ courseInfoAdminKit.validationChapter(courseChapters, CourseInfo.BUSINESS_TYPE_COURSE); }
            courseInfoAdminKit.validationAttachments(courseAttachments);
            //学习计划推送校验
            RLock lock = redissonClient.getLock(String.join("#", RedisKeys.STUDY_PLAN_INSERT_LOCK, courseId));
            ErrorCode.PushingTryAgainLater.throwIf(lock.isLocked());
        }
        String [] ids = topicIds.map(x-> x.split(",")).orElse(null);

        //添加智能标签
        Optional<String> smartTagName = context.getOptionalString("smartTagName");
        if (smartTagName.isPresent()){
            ids = getSmartTagName(smartTagName.get(), ids);
        }

        List<String> deputyCategoryIds = context.getOptionalString("deputyCategoryIds").map(s -> s.split(",")).map(Arrays::asList).orElse(new ArrayList<>());
        ErrorCode.CheckDeputyCategorySize.throwIf(deputyCategoryIds.size()>CourseInfoCategory.DEPUTY_CATEGORY_MAX_SIZE);
        courseInfo.setCaptionFollowRelease(context.getOptionalInteger("captionFollowRelease").orElse(0));
        context.getOptionalInteger("caption").ifPresent(courseInfo::setCaptionFlag);
        boolean status = optionalStatus.isPresent() && (optionalStatus.get().equals(CourseInfo.STATUS_SHELVES) || optionalStatus.get().equals(CourseInfo.STATUS_THE_TEST));

        Optional<Integer> showStatus = context.getOptionalInteger("showStatus");
        showStatus.ifPresent(courseInfo::setShowStatus);
        //captionStatus(courseInfo, Optional.ofNullable(courseShelves), showStatus);
        //showStatus.ifPresent(integer -> captionService.updateShowStatus(courseInfo.getId(), integer));


        //是否生成字幕
        captionStatusJudge(context, courseInfo, courseShelves);
        courseInfo.setUpdateDate(System.currentTimeMillis());
        courseInfoAdminService.update(courseInfo,courseChapters,courseAttachments,audienceItems,courseShelves, ids,deputyCategoryIds);
        //重新推送学习计划
        pushStudyPlan(courseInfo);
        /*if(!Objects.isNull(tagMap)){
            sendMqSynchronousGraph(tagMap,courseId);
        }*/
        if (ruleRedShipConfigService.getRootOrganizationId(courseInfo.getOrganizationId())
                && status
                && !ObjectUtils.isEmpty(courseShelves)) {
            messageSender.send(com.zxy.product.course.content.MessageTypeContent.RED_SHIP_AUTO_AUDIT,
                    MessageHeaderContent.COURSE_ID, courseInfo.getId());
        }
        sendAudit(context, courseInfo, courseShelves, FLAG_0, info);
        return courseInfo;
    }

    /**
     * 手动发送审计
     */
    private void  sendAudit(RequestContext requestContext, CourseInfo courseInfo, CourseShelves courseShelves, int type, CourseInfo info){
        if (type == FLAG_1){
            insertAudit(requestContext, courseInfo.getName());
        }else {
            updateAudit(requestContext, courseInfo.getName(), courseShelves, info);
        }
    }

    private void insertAudit(RequestContext requestContext, String courseName){
        Optional<Integer> optionType = requestContext.getOptionalInteger("type");
        Optional<Integer> constructionType = requestContext.getOptionalInteger("constructionType");
        if (optionType.isPresent() && constructionType.isPresent() && constructionType.get().equals(CourseInfo.CONSTRUCTION_TYPE_YES)){
            if (optionType.get().equals(CourseInfo.STATUS_THE_TEST)){
                sendAudit(requestContext, Audit.Action.PUBLISH.toString(), "发布", "定向发布", "新增并定向发布课程《"+ courseName +"》（全员共建共享）");
            }else if (optionType.get().equals(CourseInfo.STATUS_THE_SHELVES)){
                sendAudit(requestContext, Audit.Action.PUBLISH.toString(), "发布", "正式发布", "新增并正式发布课程《"+ courseName +"》（全员共建共享）");
            }

        }

    }

    private void updateAudit(RequestContext requestContext, String courseName, CourseShelves courseShelves, CourseInfo info) {
        String courseTitle = "课程《" + courseName + "》";
        Integer publishType = requestContext.getOptionalInteger("publishType").orElse(null);
        Integer constructionType = requestContext.getOptionalInteger("constructionType").orElse(null);
        Integer firstRelease = requestContext.getOptionalInteger("firstRelease").orElse(null);
        Integer constructionTypeFlag = requestContext.getOptionalInteger("constructionTypeFlag").orElse(null);
        Integer status = requestContext.getInteger("status");
        Integer flag = requestContext.getOptionalInteger("flag").orElse(null);

        boolean isConstructionYes = Objects.equals(constructionType, CourseInfo.CONSTRUCTION_TYPE_YES);
        boolean isFirstRelease = Objects.equals(firstRelease, FIRST_RELEASE_1);
        boolean isReRelease = Objects.equals(firstRelease, FIRST_RELEASE_0);
        boolean oldConstructionType = Objects.equals(info.getConstructionType(), CourseInfo.CONSTRUCTION_TYPE_YES);

        // 定向发布
        if (Objects.equals(publishType, CourseInfo.PUBLISH_TYPE_MEMBER) && isConstructionYes && isFirstRelease) {
            sendAudit(requestContext, Audit.Action.PUBLISH.toString(), "发布", "定向发布",
                    "新增并定向发布" + courseTitle + "（全员共建共享）");

            // 正式发布
        } else if (Objects.equals(publishType, CourseInfo.PUBLISH_TYPE_FORMAL) && isConstructionYes && isFirstRelease) {
            sendAudit(requestContext, Audit.Action.PUBLISH.toString(), "发布", "正式发布",
                    "新增并正式发布" + courseTitle + "（全员共建共享）");

            // 重新发布
        } else if (courseShelves != null && isConstructionYes && isReRelease) {
            sendAudit(requestContext, Audit.Action.PUBLISH.toString(), "发布", "正式发布",
                    "重新发布" + courseTitle + "（全员共建共享）");

            // 修改
        } else if (Objects.equals(constructionTypeFlag, CONSTRUCTION_TYPE_FLAG_1)) {
            sendAudit(requestContext, Audit.Action.UPDATE.toString(), "修改", "修改课程",
                    "修改" + courseTitle + "（全员共建共享）");

            // 取消共建共享
        } else if (oldConstructionType
                && Objects.equals(constructionType, CourseInfo.CONSTRUCTION_TYPE_NO)) {
            sendAudit(requestContext, Audit.Action.UPDATE.toString(), "修改", "修改课程",
                    "修改" + courseTitle + "（取消全员共享）");

            // 修改共享课程状态
        } else if (Objects.equals(flag, FLAG_1) && isConstructionYes) {
            sendAudit(requestContext, Audit.Action.UPDATE.toString(), "修改", "修改课程",
                    "修改" + courseTitle + "（修改共享课程）");
            //修改课程
        } else if (Objects.equals(flag, FLAG_1) && !isConstructionYes && !oldConstructionType) {
            sendAudit(requestContext, Audit.Action.UPDATE.toString(), "修改（含资源配置）", "修改课程",
                    "修改课程" + courseTitle);
        }
    }

    private void sendAudit(RequestContext requestContext, String action, String firstAction, String secondAction, String desc){
        try {
            messageSender.send(
                    TYPE, ORGANIZATION_ID, securityManager.get(requestContext.getRequest()).getRootOrganizationId(),
                    MODULE, "课程管理",
                    SUB_MODULE, "在线课程",
                    ACTION, action,
                    FIRST_ACTION, firstAction,
                    SECOND_ACTION, secondAction,
                    MEMBER_FULL_NAME, JSONObject.parseObject(com.alibaba.fastjson.JSON.toJSONString(securityManager.get(requestContext.getRequest()).getCurrentUser())).getString("fullName"),
                    USER_AGENT, requestContext.getRequest().getHeader("User-Agent"),
                    IP, getIpAddr(requestContext.getRequest()),
                    CURRENT_DATE, String.valueOf(System.currentTimeMillis()),
                    DESC, desc,
                    DESC_REQUEST, "",
                    DESC_REST_TEMPLATE_KEYS,"",
                    DESC_REST_TEMPLATE_IDS, "",
                    DESC_REST_TEMPLATE_JSONS, "");
        } catch (Exception e) {
            logger.error("全员共建共享，发送审计失败");
        }
    }

    /**
     * 取得真实地址IP(优先取x-forwarded-for)
     *
     * @param request
     * @return
     */
    private String getIpAddr(HttpServletRequest request) {
        //此方式用于nginx服务器参数设置
        String ip = request.getHeader("x-forwarded-for");
        if (ip != null && ip.length() > 0 && !"unknown".equalsIgnoreCase(ip)) {
            return ip.split(",")[0];
        }
        if (request.getHeader("X-Real-IP") != null) {
            return request.getHeader("X-Real-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getHeader("WL-Proxy-Client-IP");
        }
        if (ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {
            ip = request.getRemoteAddr();
        }
        return ip;
    }

    private void captionStatusJudge(RequestContext context, CourseInfo courseInfo, CourseShelves courseShelves) {
        if(context.getOptionalInteger("caption").isPresent()  && CourseInfo.OPEN_CAPTION.equals(context.getOptionalInteger("caption").get())) {
            courseInfo.setCaptionFlag(context.getOptionalInteger("caption").get());
            Optional<Integer> isThereAudioAndVideo = context.getOptionalInteger("isThereAudioAndVideo");
            if (!isThereAudioAndVideo.isPresent()){
                courseInfo.setCaptionOverallStatus(CourseInfo.CAPTION_OVERALL_STATUS_GENERATE);
            }else {
                //没有新增的,则去判断状态
                courseInfo.setCaptionOverallStatus(findStatus(courseInfo.getId()));
            }
        }else {
            courseInfo.setCaptionFlag(CourseInfo.CAPTION_OVERALL_STATUS_NO);
            courseInfo.setCaptionOverallStatus(CourseInfo.CAPTION_OVERALL_STATUS_NO);
            //将已发布的字幕修改成未发布
            captionService.updateCaptionStatusUnpublish(courseInfo.getId());
        }
        int showStatus;
        if (context.getOptionalInteger("showStatus").isPresent()) {
            showStatus = context.getOptionalInteger("showStatus").get();
        }else {
            showStatus = captionService.selectShowStatus(courseInfo.getId());
        }
        captionStatus(courseInfo, Optional.ofNullable(courseShelves), Optional.of(showStatus));
        captionService.updateShowStatus(courseInfo.getId(), showStatus);
        updateCaptionOverallStatus(courseInfo.getId());
    }

    /**
     * 查询已存在的课程/案例图谱关系Map
     *
     * @param courseId 课程id
     * @return 满足图谱课程/案例同步信息Map
     */
    private Map<String,String> queryExistRelation(String courseId){
        List<String> courseIdCollect = courseInfoService.checkCourseSynchronous(courseId);
        Map<String, String> tagMap = Optional.ofNullable(courseIdCollect)
                .filter(CollectionUtils::isNotEmpty)
                .map(ew1 -> knowledgeGraphService.getGraphNodeTag(ew1.get(0)))
                .orElse(null);
        logger.info("查询已存在的课程/案例图谱关系Map{}",JSONObject.toJSONString(tagMap));
        return tagMap;
    }

    /**
     * 查询已存在的课程/案例图谱关系Map
     *
     * @param relationMap 图谱课程/案例同步信息Map
     * @param courseId 课程id
     */
    private void sendMqSynchronousGraph(Map<String,String> relationMap,String courseId){
        String isCaseFlag = relationMap.get("isCaseFlag");
        SynchronousNodeDTO synchronousNodeDTO = new SynchronousNodeDTO();
        synchronousNodeDTO.setNodeId(courseId);
        synchronousNodeDTO.setNodeType("course");
        synchronousNodeDTO.setGraphRelationCollectJson(relationMap.get("existRelationCollect"));
        if("Yes".equals(isCaseFlag)){
            synchronousNodeDTO.setCaseType(0);
            messageSender.send(
                    GRAPH_CASE_NODE,
                    MessageHeaderContent.CONTENT, JSONObject.toJSONString(synchronousNodeDTO)
            );
        }
        if( "No".equals(isCaseFlag)){
            messageSender.send(
                    GRAPH_COURSE_NODE,
                    MessageHeaderContent.CONTENT,JSONObject.toJSONString(synchronousNodeDTO)
            );
        }
    }



    /**
     * 是否是全员,是否再限制时间内
     * @param optionalAudienceItems
     * @param rootOrgId
     * @param minDate
     * @param maxDate
     */
    private void publicationRestriction(Optional<String> optionalAudienceItems, String rootOrgId, int minDate, int maxDate) {
        List<AudienceItem> audienceItems;
        audienceItems = audienceKit.parseAudienceItems(optionalAudienceItems);
        for (AudienceItem audienceItem : audienceItems) {
            if (audienceItem.getJoinId().equals(rootOrgId) || audienceItem.getJoinId().equals(INTERNAL_ORGANIZATION_ID)) {
                int hours = Calendar.getInstance().get(Calendar.HOUR_OF_DAY);
                if (hours >= minDate && hours <= maxDate) {
                    throw new UnprocessableException(ErrorCode.PublicationRestriction);
                }
            }
        }
    }

    private void pushStudyPlan( CourseInfo courseInfo) {
        Optional<CourseStudyPlanConfig> studyPlanConfig = courseStudyPlanConfigService.findNeedPushStudyPlan(courseInfo.getId(), CourseStudyPlanConfig.BUSINESS_TYPE_COURSE);
        studyPlanConfig.ifPresent(c -> {
                //发送消息
                messageSender.send(MessageTypeContent.COURSE_STUDY_PLAN_CONFIG_INSERT,
                        com.zxy.product.human.content.MessageHeaderContent.BUSINESS_ID, courseInfo.getId(),
                        com.zxy.product.human.content.MessageHeaderContent.TYPE, String.valueOf(CourseStudyPlanConfig.BUSINESS_TYPE_COURSE),
                        com.zxy.product.human.content.MessageHeaderContent.BUSINESS_NAME, courseInfo.getName(),
                        com.zxy.product.human.content.MessageHeaderContent.SOURCE, String.valueOf(StudyPlan.SOURCE_TYPE_ORG),//2:组织推送
                        com.zxy.product.human.content.MessageHeaderContent.DEAD_LINE, String.valueOf(c.getEndTime()),
                        com.zxy.product.human.content.MessageHeaderContent.REMIND_TIME, String.valueOf(c.getRemindTime()),
                        com.zxy.product.human.content.MessageHeaderContent.REMIND_TYPE, String.valueOf(c.getRemindType()),
                        com.zxy.product.human.content.MessageHeaderContent.DISTRIBUTION_WAYS, String.valueOf(c.getDistributionWays()));
        });
    }

    /**
     * 拼装参数
     * @param context
     * @return
     */
    private CourseInfo conver(RequestContext context) {
        Function<String, String> optStr = title -> context.getOptionalString(title).orElse(null);
        Function<String, Integer> optInt = title -> context.getOptionalInteger(title).orElse(null);

        CourseInfo courseInfo = new CourseInfo();
        courseInfo.setName(optStr.apply("name"));
        courseInfo.setDescription(optStr.apply("description"));
        courseInfo.setCategoryId(optStr.apply("categoryId"));
        courseInfo.setLecturer(optStr.apply("lecturer"));
        courseInfo.setSource(optInt.apply("source"));
        courseInfo.setSourceDetail(optStr.apply("sourceDetail"));
        courseInfo.setPublishClient(optInt.apply("publishClient"));
        courseInfo.setOrganizationId(optStr.apply("organizationId"));
        courseInfo.setReleaseOrgId(optStr.apply("releaseOrgId"));
        courseInfo.setReleaseMemberId(optStr.apply("releaseMemberId"));
        courseInfo.setCover(optStr.apply("cover"));
        courseInfo.setCoverPath(optStr.apply("coverPath"));
        courseInfo.setPcBanner(optStr.apply("pcBanner"));
        courseInfo.setPcBannerPath(optStr.apply("pcBannerPath"));
        courseInfo.setAppBanner(optStr.apply("appBanner"));
        courseInfo.setAppBannerPath(optStr.apply("appBannerPath"));
        courseInfo.setCourseTime(optInt.apply("courseTime"));
        courseInfo.setAudiences(optStr.apply("audiences"));

        courseInfo.setAddType(optInt.apply("addType"));
        courseInfo.setShareSub(optInt.apply("shareSub"));
        courseInfo.setLearnSequence(optInt.apply("learnSequence"));
        courseInfo.setIsPublic(optInt.apply("isPublic"));
        courseInfo.setPublishType(optInt.apply("publishType"));
        // updated by wangdongyan 2018-06-04 pc端展示简介样式
        courseInfo.setDescriptionText(optStr.apply("descriptionText"));
        // updated by wangdongyan 2018-07-17 app端展示简介样式
        courseInfo.setDescriptionApp(optStr.apply("descriptionApp"));
        courseInfo.setIntegral(optInt.apply("integral"));
        courseInfo.setRelativeGenseeId(optStr.apply("relativeGenseeId"));
        courseInfo.setUseVideoSpeed(optInt.apply("useVideoDoubleSpeed"));
        //默认开启讨论区
        courseInfo.setSwitchHide(CourseInfo.FORUM_OPEN);
        courseInfo.setHistoryHide(CourseInfo.FORUM_OPEN);
        courseInfo.setExplicitLearningStatus(optInt.apply("explicitLearningStatus"));
        courseInfo.setConstructionType(optInt.apply("constructionType"));
        return courseInfo;
    }

    private boolean hasEditchapter(String courseId) {
        CourseInfo oldCourse = courseInfoAdminService.get(courseId);
        return (oldCourse.getStatus() != null
                && !oldCourse.getStatus().equals(CourseInfo.STATUS_SHELVES)
                && !oldCourse.getStatus().equals(CourseInfo.STATUS_THE_TEST)
                && oldCourse.getVersionId() == null);
    }

    @RequestMapping(method = RequestMethod.GET, value = "/available-course-list")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "client", type = Integer.class) // 课程发布终端
    @Param(name = "name", type = String.class)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "categoryId", type = String.class)
    @Param(name = "uri", type = String.class)
    @Param(name = "code", type = String.class)
    @Param(name = "shelveBeginDate", type = String.class)
    @Param(name = "shelveEndDate", type = String.class)
    @JSON("recordCount")
    @JSON("items.(id,name,type,createTime,source,status,portalNum,organizationId,courseHour,credit,beginDate,endDate,cover,coverPath,shelveTime,isPublic,code,publishClient,visits,description,audiences,courseTime, lecturer, descriptionText)")
    @JSON("items.organization.(id,name)")
    @JSON("items.category.(id,name)")
    public PagedResult<CourseInfo> findAvailableCourseList(RequestContext context, Subject<Member> memberSubject) {
        Optional<String> organizationId = context.getOptionalString("organizationId");
        String uri = "course-study/available-course";
//        String uri = "course-study/course-info";
        List<String> grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(memberSubject.getCurrentUserId(),organizationId, uri);
//		String rootId = organizationService.findMaxGrantOriganization(memberSubject.getCurrentUserId(), uri).getId();
//        List<String> parentOrganizationIds = grantService.findGrantedOrganizationWithParent(memberSubject.getCurrentUserId(), uri, organizationId.orElse(rootId)).stream().map(a -> a.getId()).collect(Collectors.toList());
//        parentOrganizationIds = parentOrganizationIds.stream()
//                .filter(x->!grantOrganizationIds.contains(x)).collect(Collectors.toList());
        Optional<List<String>> parentIds = Optional.empty();
        if (!organizationId.isPresent()) {
//        	parentIds = Optional.of(grantService.findGrantedOrganizationWithParent(memberSubject.getCurrentUserId(), uri, null).stream().map(a -> a.getId()).collect(Collectors.toList()));

            List<String> parentOrganizationIds = grantService.findGrantedOrganizationWithParent(memberSubject.getCurrentUserId(), uri, null).stream().map(a -> a.getId()).collect(Collectors.toList());
            parentIds = Optional.of(parentOrganizationIds.stream()
                    .filter(x->!grantOrganizationIds.contains(x)).collect(Collectors.toList()));
        }
        return courseInfoService.findAvailableCourseList(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                context.getOptionalInteger("client"),
                context.getOptionalString("name"),
                context.getOptionalString("categoryId"),
                context.getOptionalString("code"),
                StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginDate")),
                StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndDate")),
                grantOrganizationIds,
                parentIds);
    }

    /**
     * 可用资源导出
     * @param context
     * @param memberSubject
     * @return
     * @throws IOException
     */
    @RequestMapping(method = RequestMethod.GET, value = "/download-available-course")
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "sign", type = String.class, required = true)
    @Param(name = "client", type = Integer.class) // 课程发布终端
    @Param(name = "name", type = String.class)
    @Param(name = "organizationId", type = String.class)
    @Param(name = "categoryId", type = String.class)
    @Param(name = "uri", type = String.class)
    @Param(name = "code", type = String.class)
    @Param(name = "shelveBeginDate", type = String.class)
    @Param(name = "shelveEndDate", type = String.class)
    public void downloadAvailableCourse(RequestContext context, Subject<Member> memberSubject) throws IOException {
        //验证越权，sign参数需要前端调用接口生成
        //logger.info("sign:" + Encrypt.Decrypt(context.getString("sign"), aesKey));
        try {
            if(!signService.checkSign(SIGN_PREFIX, Encrypt.Decrypt(context.getString("sign"), aesKey))){
                throw new UnprocessableException(ErrorCode.ExportSignError);
            }
        } catch (Exception e) {
            throw new UnprocessableException(ErrorCode.ExportSignError);
        }
        List<CourseInfo> list = this.findAvailableCourseList(
                context,
                memberSubject).getItems();
        String filename = "可用课程资源清单导出信息.xlsx";
        List<String> courseIds = list.stream().map(c -> c.getId()).collect(Collectors.toList());
        // 查询每个课程关联的标签id
        List<BusinessTopic> topicList = businessTopicService.findAllTopicsByBusinessIds(courseIds);
        List<String> topicIds = topicList.stream().map(t -> t.getTopicId()).collect(Collectors.toList());
        // 查询system库中对应的标签名称
        Map<String, String> topicMap = topicService.getTopicsByIds(String.join(",", topicIds).split(",")).stream().distinct().collect(Collectors.toMap(Topic::getId, o -> o.getName()));
//        Map<String, String> topicMap = topicService.get(String.join(",", topicIds).split(",")).stream().distinct().collect(Collectors.toMap(Topic::getId, o -> o.getName()));
        topicList.forEach(x -> {
        	if (!org.jooq.tools.StringUtils.isEmpty(topicMap.get(x.getTopicId()))) {
        		x.setTopicName(topicMap.get(x.getTopicId()));
        		x.setStatus(1);
        	} else {
        		x.setStatus(0);// 已禁用
        	}
        });
        Map<String, List<BusinessTopic>> businessTopicMap = topicList.stream().collect(groupingBy(BusinessTopic::getBusinessId));
        // 组合课程以及课程关联的标签
        list.forEach(c -> {
        	List<BusinessTopic> ts = businessTopicMap.get(c.getId());
        	if (null != ts && ts.size() > 0) {
        		List<String> topicNames = ts.stream().filter(b -> !b.getStatus().equals(0)).map(b -> b.getTopicName()).collect(Collectors.toList());
        		c.setTopicNames(String.join(",", topicNames));
        	}
        });
        if (BrowserUtil.isMSBrowser(context.getRequest().getHeader("User-Agent"))) {
            filename = URLEncoder.encode(filename, "UTF-8");
        } else {
            filename = new String(filename.getBytes("UTF-8"), "ISO-8859-1");
        }
        HttpServletResponse response = context.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        response.setHeader("Content-Disposition", "attachment;filename=" + filename);
        Writer writer = new ExcelWriter();
        writer.sheet("可用课程资源清单导出信息", list)
        .indexColumn(Optional.empty())
        .field("课程编码", CourseInfo::getCode)
        .field("课程名称", CourseInfo::getName)
        .field("课程简介", CourseInfo::getDescriptionText)
        .field("讲师", CourseInfo::getLecturer)
        .field("分类/序列", courseInfo -> courseInfo.getCategory().getName())
        .field("课程标签", CourseInfo::getTopicNames)
        .field("课程时长", CourseInfo::getCourseTime, x -> {
        	Integer minute = 0;
        	Integer second = 0;
        	if (x != null) {
        		minute = (x/60);
        		second = (x - minute * 60);
        	}
        	return minute + "分" + second + "秒";
        })
        .field("发布时间", CourseInfo::getShelveTime, x -> StringUtils.long2ShortDate(x))
        .field("发布范围", CourseInfo::getAudiences)
        .field("归属部门", courseInfo -> courseInfo.getOrganization().getName())
        .field("适用终端", CourseInfo::getPublishClient, "PC&APP", "1", "PC", "2", "APP")
        .field("课程浏览人次（学习人次）", courseInfo -> {
        	if (null == courseInfo.getVisits()) {
        		courseInfo.setVisits(0);
        	}
        	return courseInfo.getVisits();
        })
        ;
        String date = DateUtil.dateLongToString(System.currentTimeMillis(), DateUtil.YYYYMMDD);
        com.zxy.product.human.entity.Member nameAndFullName = memberService.getNameAndFullName(memberSubject.getCurrentUserId());
        String content = (nameAndFullName.getFullName() + "  " + nameAndFullName.getName() + "  " + date);
        writer.write(response.getOutputStream(), workBook -> {
            try {
                ImportExcelUtil.putWaterRemarkToExcel(workBook, workBook.getSheetAt(0), null, 0, 0, 0, 0, 1, 1, 0, 0, content);
            } catch (IOException e) {
                e.printStackTrace();
            }
        });
    }

    /**
     * 上传大课件时，每隔一段时间刷新
     * @return
     */
    @RequestMapping(value = "/refresh-token", method = RequestMethod.GET)
    @Permitted
    @JSON("*.*")
    public CourseInfo refreshToken() {
        return new CourseInfo();
    }

    /**
     * 课程关联直播
     */
    @RequestMapping(value = "/relate-gensee/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "name", type = String.class, required = true)
    @Param(name = "relativeGenseeId", type = String.class, required = true)
    @JSON("*")
    @Audit(module = "课程管理", subModule = "在线课程-关联直播", action = Audit.Action.MANAGE, fisrtAction = "关联直播", desc = "操作关联直播于课程《{0}》", params = {"name"})
    public CourseInfo relateGensee(RequestContext context,Subject<Member> subject){
        return courseInfoAdminService.relateGensee(
                context.getString("id"),
                context.getString("relativeGenseeId"),
                subject.getCurrentUserId()
        );
    }

    /**
     * 课程取消关联直播
     */
    @RequestMapping(value = "/unrelate-gensee/{id}", method = RequestMethod.PUT)
    @Permitted
    @Param(name = "id", type = String.class, required = true)
    @Param(name = "name", type = String.class, required = true)
    @JSON("*")
    @Audit(module = "课程管理", subModule = "在线课程-取消关联直播", action = Audit.Action.MANAGE, fisrtAction = "取消关联直播", desc = "取消关联直播于课程《{0}》", params = {"name"})
    public CourseInfo unrelateGensee(RequestContext context,Subject<Member> subject){
        return courseInfoAdminService.unrelateGensee(
                context.getString("id"),
                subject.getCurrentUserId()
        );
    }

    /**
     * 查询课程已关联的直播ids
     */
    @RequestMapping(value = "/related-gensee-ids", method = RequestMethod.GET)
    @Permitted(perms = "course-study/course-info")
    @JSON("*")
    public List<String> findRelatedGenseeIds(Subject<Member> subject){
        return courseInfoAdminService.findRelatedGenseeIds();
    }

    /**
     * 查询单个课程讨论区状态and讨论历史记录状态
     */
    @RequestMapping(value = "/get-switch-hide",method = RequestMethod.GET)
    @Permitted(perms = {"course-study/course-info","course-study/subject-info"})
    @Param(name = "id", type = String.class, required = true, value = "课程Id")
    @JSON("name,switchHide,historyHide")
    public CourseInfo getSwitchHide(RequestContext context){
        return courseInfoAdminService.getSwitchHideAndHistoryHide(
                context.getString("id")
        );
    }

    /**
     * 讨论区and讨论记录单个课程开关
     */
    @RequestMapping(value = "/switch-hide-one", method = RequestMethod.PUT)
    @Param(name = "businessId", type = String.class, required = true, value = "课程Id")
    @Param(name = "hide", type = Integer.class, required = true, value = "讨论区开关（0：关，1：开）")
    @Param(name = "history", type = Integer.class, value = "历史讨论记录开关（0：关，1：开）")
    @JSON("name,switchHide,historyHide")
    public CourseInfo updateSwitchHideOne(RequestContext context){

        return courseInfoAdminService.updateSwitchHideOne(
                context.getString("businessId"),context.getInteger("hide"),
                context.getOptionalInteger("history")
        );
    }


    /**
     * 课程-资源池列表
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/find-course-virtual-space", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")//课程名
    @Param(name = "organizationId")//归属部门
    @Param(name = "categoryId")//课程目录id
    @Param(name = "shelveBeginDate", type = String.class)//发布时间
    @Param(name = "shelveEndDate", type = String.class)
    @Param(name = "uri")//uri
    @Param(name = "businessType", required = true)//0=课程 2=专题
    @Param(name = "status", type = Integer.class)//课程状态
    @Param(name = "virtualSpacesId", required = true)//空间id
    @Param(name = "virtualSpacesOrganizationId", required = true)//空间归属部门id
    @Param(name = "virtualSpacesStatus", type = Integer.class)//空资源状态
    @Param(name = "contain", type = Integer.class)//是否包含子级【0：不包含；1：包含】
    @Param(name = "ids")//精选内容过滤
    @JSON("recordCount")
    @JSON("items.(id,name,shelveTime,source,status,code,courseVirtualSpacesStatus,publishClient,cover,coverPath)")
    @JSON("items.organization.(id,name)")
    @JSON("items.category.(id,name)")
    @Permitted
    public PagedResult<CourseInfo> findCourseVirtualSpace(RequestContext context, Subject<Member> subject) {

        String currentUserId = subject.getCurrentUserId();

        String virtualSpacesOrganizationId = context.getString("virtualSpacesOrganizationId");
        Optional<String> organizationId = context.getOptionalString("organizationId");
        String virtualSpacesId = context.getString("virtualSpacesId");
        Integer businessType = context.getInteger("businessType");
        //资源状态条件
        Optional<Integer> virtualSpacesStatus = context.getOptionalInteger("virtualSpacesStatus");
        List<String> spacesStatus = Lists.newArrayList();
        List<String> spacesStatusAddTo = Lists.newArrayList();
        List<String> grantOrganizationIds = Lists.newArrayList();
        List<String> virtualSpacesGrantOrganizationIds = null;
        List<String> superiorIds = Lists.newArrayList();



        if (organizationId.isPresent()) {
            //查询归属条件的子级部门,组织深度截至级别2
            superiorIds = courseOrganizationService.findSuperiorOrgId(organizationId.get());
            //查询当前虚拟空间的子级部门
            grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(currentUserId, Optional.of(virtualSpacesOrganizationId), context.getString("uri"));

            //选择了以外的部门
            if (!superiorIds.contains(organizationId.get()) && !grantOrganizationIds.contains(organizationId.get())) {
                return PagedResult.create(0, new ArrayList<>());
            }

            if (!organizationId.get().equals(virtualSpacesOrganizationId)  && ObjectUtils.isEmpty(superiorIds)) {
                //等于null,查询的是三级及以下
                grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(currentUserId, organizationId, context.getString("uri"));
            }
        } else {
            //默认查询空间归属的资源  包含子级别
            virtualSpacesGrantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(currentUserId, Optional.of(virtualSpacesOrganizationId), context.getString("uri"));
            superiorIds = courseOrganizationService.findSuperiorOrgId(virtualSpacesOrganizationId);
        }


        //查询禁用
        if (virtualSpacesStatus.isPresent() && Objects.equals(CourseVirtualSpace.STATUS_FORBIDDEN, virtualSpacesStatus.get())) {
            spacesStatus = courseVirtualSpaceService.findByVirtualSpacesStatus(virtualSpacesId, businessType, virtualSpacesStatus.get(), Optional.empty());
            if (ObjectUtils.isEmpty(spacesStatus)) {
                return PagedResult.create(0, new ArrayList<>());
            }
            return findCourseVirtualSpaceForbidden(context, currentUserId, businessType, organizationId, virtualSpacesId, spacesStatus, virtualSpacesStatus, grantOrganizationIds);
        }


        //查询启用
        if (virtualSpacesStatus.isPresent() && Objects.equals(CourseVirtualSpace.STATUS_ENABLED, virtualSpacesStatus.get())) {
            spacesStatus = courseVirtualSpaceService.findByVirtualSpacesStatus(virtualSpacesId, businessType, CourseVirtualSpace.STATUS_FORBIDDEN, Optional.of(CourseInfo.TYPE_INSIDE));
        }

        Optional<String> optionalString = context.getOptionalString("ids");

        if (optionalString.isPresent()){
            spacesStatus.addAll(Arrays.asList(optionalString.get().split(",")));
        }

        //查询添加上级分享启用 in
        spacesStatusAddTo = courseVirtualSpaceService.findByVirtualSpacesId(virtualSpacesId, businessType, virtualSpacesStatus);

        if (!ObjectUtils.isEmpty(spacesStatus) && !ObjectUtils.isEmpty(spacesStatusAddTo)){
            spacesStatusAddTo.removeAll(spacesStatus);
        }



        spacesStatusAddTo = additionalResources(organizationId, superiorIds, spacesStatusAddTo,context.getOptionalInteger("status"), context.getOptionalString("name"), context.getOptionalString("categoryId"), StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginDate")), StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndDate")));
        PagedResult<CourseInfo> courseVirtualSpace = findCourseVirtualSpace(context, subject, grantOrganizationIds, businessType, virtualSpacesId, virtualSpacesGrantOrganizationIds, organizationId, spacesStatus, virtualSpacesStatus, spacesStatusAddTo);
        //查询课程再空间的状态
        findCourseVirtualSpacesStatus(courseVirtualSpace.getItems(), virtualSpacesId, businessType);

        return courseVirtualSpace;
    }


    /**
     * 排除归属组织外的资源
     *
     * @param organizationId
     * @param superiorIds
     * @param spacesStatusAddTo
     * @param status
     * @param name
     * @param categoryId
     * @param shelveBeginDate
     * @param shelveEndDate
     * @return
     */
    private List<String> additionalResources(Optional<String> organizationId, List<String> superiorIds, List<String> spacesStatusAddTo, Optional<Integer> status, Optional<String> name, Optional<String> categoryId, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate) {
        return courseInfoService.findVirtualSpacesAndOrgId(organizationId,superiorIds,spacesStatusAddTo,status,name,categoryId,shelveBeginDate,shelveEndDate);
    }

    /**
     * 查询启用
     *
     * @param context
     * @param subject
     * @param grantOrganizationIds
     * @param businessType
     * @param virtualSpacesId
     * @param virtualSpacesGrantOrganizationIds
     * @param organizationId
     * @param spacesStatus
     * @param virtualSpacesStatus
     * @param spacesStatusAddTo
     * @return
     */
    private PagedResult<CourseInfo> findCourseVirtualSpace(RequestContext context,
                                                           Subject<Member> subject,
                                                           List<String> grantOrganizationIds,
                                                           Integer businessType,
                                                           String virtualSpacesId,
                                                           List<String> virtualSpacesGrantOrganizationIds,
                                                           Optional<String> organizationId,
                                                           List<String> spacesStatus,
                                                           Optional<Integer> virtualSpacesStatus,
                                                           List<String> spacesStatusAddTo) {
        return courseInfoAdminService.findCourseVirtualSpace(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                subject.getCurrentUserId(),
                grantOrganizationIds,
                businessType,
                context.getOptionalString("name"),
                context.getOptionalString("organizationId"),
                context.getOptionalString("categoryId"),
                context.getOptionalInteger("status"),
                StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginDate")),
                StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndDate")),
                virtualSpacesId,
                virtualSpacesGrantOrganizationIds,
                organizationId,
                spacesStatus,
                virtualSpacesStatus,
                spacesStatusAddTo);
    }

    /**
     * 查询禁用
     */
    private PagedResult<CourseInfo> findCourseVirtualSpaceForbidden(RequestContext context,
                                                                    String currentUserId,
                                                                    Integer businessType,
                                                                    Optional<String> organizationId,
                                                                    String virtualSpacesId,
                                                                    List<String> spacesStatus,
                                                                    Optional<Integer> virtualSpacesStatus,
                                                                    List<String> grantOrganizationIds){

        return courseInfoAdminService.findCourseVirtualSpaceForbidden(
                context.get("page", Integer.class),
                context.get("pageSize", Integer.class),
                currentUserId,
                businessType,
                context.getOptionalString("name"),
                organizationId,
                context.getOptionalString("categoryId"),
                context.getOptionalInteger("status"),
                StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginDate")),
                StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndDate")),
                virtualSpacesId,
                spacesStatus,
                virtualSpacesStatus,
                grantOrganizationIds);
    }


    /**
     * 查询上级分享的课程
     *
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/find-superior-course", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")//课程名
    @Param(name = "categoryId")//课程目录id
    @Param(name = "shelveBeginDate", type = String.class)//发布时间
    @Param(name = "shelveEndDate", type = String.class)
    @Param(name = "businessType", required = true)//0=课程 2=专题
    @Param(name = "status", type = Integer.class)//课程状态
    @Param(name = "virtualSpacesId", required = true)//空间id
    @Param(name = "virtualSpacesOrganizationId", required = true)//空间归属部门id
    @Param(name = "source", type = Integer.class)//课程来源(0：内部录制，1：外部引进，2：共建共享；3：自主研发；4：其他；5：工作室
    @JSON("recordCount")
    @JSON("items.(id,name,createTime,source,status,code,publishClient)")
    @JSON("items.organization.(id,name)")
    @JSON("items.category.(id,name)")
    @Permitted
    public PagedResult<CourseInfo> findSuperiorCourse(RequestContext requestContext) {

        String virtualSpacesId = requestContext.getString("virtualSpacesId");
        Integer businessType = requestContext.getInteger("businessType");
        //查询已经添加的上级资源id
        List<String> byVirtualSpacesId = courseVirtualSpaceService.findByVirtualSpacesId(virtualSpacesId, businessType,Optional.empty());


        return courseInfoAdminService.findSuperiorCourse(
                byVirtualSpacesId,
                requestContext.getInteger("page"),
                requestContext.getInteger("pageSize"),
                requestContext.getOptionalString("name"),
                requestContext.getOptionalString("categoryId"),
                StringUtils.dateString2OptionalLong(requestContext.getOptionalString("shelveBeginDate")),
                StringUtils.dateString2OptionalLongMore(requestContext.getOptionalString("shelveEndDate")),
                businessType,
                virtualSpacesId,
                requestContext.getOptionalInteger("status"),
                requestContext.getString("virtualSpacesOrganizationId"),
                requestContext.getOptionalInteger("source")
        );
    }


    /**
     * 课程-资源池列表-导出
     *
     * @param context
     * @param subject
     * @return
     */
    @RequestMapping(value = "/download-course", method = RequestMethod.GET)
    @Param(name = "page", type = Integer.class, required = true)
    @Param(name = "pageSize", type = Integer.class, required = true)
    @Param(name = "name")//课程名
    @Param(name = "organizationId")//归属部门
    @Param(name = "categoryId")//课程目录id
    @Param(name = "shelveBeginDate", type = String.class)//发布时间
    @Param(name = "shelveEndDate", type = String.class)
    @Param(name = "uri")//uri
    @Param(name = "businessType", required = true)//0=课程 2=专题
    @Param(name = "status", type = Integer.class)//课程状态
    @Param(name = "virtualSpacesId", required = true)//空间id
    @Param(name = "virtualSpacesOrganizationId", required = true)//空间归属部门id
    @Param(name = "virtualSpacesStatus", type = Integer.class)//空资源状态
    @Param(name = "contain", type = Integer.class)//是否包含子级【0：不包含；1：包含】
    @JSON("recordCount")
    @JSON("items.(id,name,shelveTime,source,status,code,courseVirtualSpacesStatus,publishClient)")
    @JSON("items.organization.(id,name)")
    @JSON("items.category.(id,name)")
    @Permitted
    public void courseVirtualSpaceDownload(RequestContext context, Subject<Member> subject) {

        String virtualSpacesOrganizationId = context.getString("virtualSpacesOrganizationId");
        Optional<String> organizationId = context.getOptionalString("organizationId");


        String virtualSpacesId = context.getString("virtualSpacesId");
        Integer businessType = context.getInteger("businessType");


        //资源状态条件
        Optional<Integer> virtualSpacesStatus = context.getOptionalInteger("virtualSpacesStatus");
        List<String> spacesStatus = Lists.newArrayList();
        List<String> spacesStatusAddTo = Lists.newArrayList();
        List<String> grantOrganizationIds = null;
        List<String> virtualSpacesGrantOrganizationIds = null;
        List<CourseInfo> courseList = Lists.newArrayList();
        List<String> superiorIds = Lists.newArrayList();

        if (organizationId.isPresent()) {
            //查询归属条件的子级部门,组织深度截至级别2
            superiorIds = courseOrganizationService.findSuperiorOrgId(organizationId.get());
            //查询当前虚拟空间的子级部门
            grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(), Optional.of(virtualSpacesOrganizationId), context.getString("uri"));

            //选择了以外的部门
            if (!superiorIds.contains(organizationId.get()) && !grantOrganizationIds.contains(organizationId.get())) {
                downloadCourse(context, courseList);
            }

            if (!organizationId.get().equals(virtualSpacesOrganizationId)  && ObjectUtils.isEmpty(superiorIds)) {
                //等于null,查询的是三级及以下
                grantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(), organizationId, context.getString("uri"));
            }
        } else {
            //默认查询空间归属的资源  包含子级别
            virtualSpacesGrantOrganizationIds = courseInfoAdminKit.findGrantOrganizationIds(subject.getCurrentUserId(), Optional.of(virtualSpacesOrganizationId), context.getString("uri"));
            superiorIds = courseOrganizationService.findSuperiorOrgId(virtualSpacesOrganizationId);
        }


        //查询禁用
        if (virtualSpacesStatus.isPresent() && Objects.equals(CourseVirtualSpace.STATUS_FORBIDDEN, virtualSpacesStatus.get())) {
            spacesStatus = courseVirtualSpaceService.findByVirtualSpacesStatus(virtualSpacesId, businessType, virtualSpacesStatus.get(), Optional.empty());
            if (ObjectUtils.isEmpty(spacesStatus)) {
                downloadCourse(context, courseList);
            }
            courseList = findCourseVirtualSpaceForbidden(context, subject.getCurrentUserId(), businessType, organizationId, virtualSpacesId, spacesStatus, virtualSpacesStatus, grantOrganizationIds).getItems();
            downloadCourse(context, courseList);
        }

        //查询启用
        if (virtualSpacesStatus.isPresent() && Objects.equals(CourseVirtualSpace.STATUS_ENABLED, virtualSpacesStatus.get())) {
            //查询资源禁用 not in
            spacesStatus = courseVirtualSpaceService.findByVirtualSpacesStatus(virtualSpacesId, businessType, CourseVirtualSpace.STATUS_FORBIDDEN, Optional.of(CourseInfo.TYPE_INSIDE));
        }

        //查询添加上级分享启用 in
        spacesStatusAddTo = courseVirtualSpaceService.findByVirtualSpacesId(virtualSpacesId, businessType, virtualSpacesStatus);
        if (!ObjectUtils.isEmpty(spacesStatus) && !ObjectUtils.isEmpty(spacesStatusAddTo)){
            spacesStatusAddTo.removeAll(spacesStatus);
        }
        spacesStatusAddTo = additionalResources(organizationId, superiorIds, spacesStatusAddTo,context.getOptionalInteger("status"), context.getOptionalString("name"), context.getOptionalString("categoryId"), StringUtils.dateString2OptionalLong(context.getOptionalString("shelveBeginDate")), StringUtils.dateString2OptionalLongMore(context.getOptionalString("shelveEndDate")));
        courseList = findCourseVirtualSpace(context, subject, grantOrganizationIds, businessType, virtualSpacesId, virtualSpacesGrantOrganizationIds, organizationId, spacesStatus, virtualSpacesStatus, spacesStatusAddTo).getItems();
        //查询课程再空间的状态
        findCourseVirtualSpacesStatus(courseList, virtualSpacesId, businessType);
        downloadCourse(context, courseList);
    }


    /**
     * 查询空间资源状态
     *
     * @param courseList
     * @param virtualSpacesId
     * @param businessType
     */
    private void findCourseVirtualSpacesStatus(List<CourseInfo> courseList, String virtualSpacesId, Integer businessType){
        //查询课程再空间的状态
        if (!ObjectUtils.isEmpty(courseList)) {
            List<String> ids = courseList.stream().map(CourseInfo::getId).collect(Collectors.toList());
            Map<String, Integer> integerMap = courseVirtualSpaceService.findBusinessIdStatus(
                    ids,
                    virtualSpacesId,
                    businessType
            );
            courseList.forEach(r -> r.setCourseVirtualSpacesStatus(ObjectUtils.isEmpty(integerMap.get(r.getId())) ? CourseVirtualSpace.STATUS_ENABLED : integerMap.get(r.getId())));
        }
    }



    /**
     * 课程导出
     * @param requestContext
     * @param courseList
     */
    private  void downloadCourse(RequestContext requestContext, List<CourseInfo> courseList){

        HttpServletResponse response = requestContext.getResponse();
        response.setContentType("application/octet-stream;charset=utf-8");
        try {
            response.setHeader("Content-Disposition",
                    "attachment;filename=" + new String("虚拟空间资源池-课程资源列表".getBytes("gb2312"), StandardCharsets.ISO_8859_1) + ".xlsx");
        } catch (
                UnsupportedEncodingException e) {
            throw new RuntimeException(e);
        }

        if (!ObjectUtils.isEmpty(courseList)){
            Writer writer = new ExcelWriter();
            writer.sheet("在线课程导出信息", courseList)
                    .field("课程名称", CourseInfo::getName)
                    .field("课程编码", CourseInfo::getCode)
                    .field("归属部门",courseInfo -> courseInfo.getOrganization().getName())
                    .field("课程状态", courseInfo -> {

                        if (ObjectUtils.isEmpty(courseInfo.getStatus())){
                            courseInfo.setStatus(-1);
                        }
                        switch (courseInfo.getStatus()){
                            case CourseInfo.STATUS_SHELVES:
                                return "已发布";
                            case CourseInfo.STATUS_NO:
                                return "未发布";
                            case CourseInfo.STATUS_THE_SHELVES:
                                return "已下架";
                            case CourseInfo.STATUS_THE_TEST:
                                return "测试中";
                            case CourseInfo.STATUS_IN_SHELVES:
                                return "发布中";
                            case CourseInfo.STATUS_FIVE_SHELVES:
                                return "退库";
                            case CourseInfo.STATUS_APPROVE:
                                return "审核";
                            default:
                                return "";
                        }
                    })
                    .field("适用终端", courseInfo -> {
                        if (ObjectUtils.isEmpty(courseInfo.getPublishClient())){
                            courseInfo.setPublishClient(-1);
                        }
                        switch (courseInfo.getPublishClient()){
                            case CourseInfo.PUBLISH_CLIENT_ALL:
                                return "PC&APP";
                            case CourseInfo.PUBLISH_CLIENT_PC:
                                return "PC";
                            case CourseInfo.PUBLIST_CLIENT_APP:
                                return "APP";
                            default:
                                return "";
                        }
                    })
                    .field("资源空间状态", r->{
                        if (Objects.equals(r.getCourseVirtualSpacesStatus(),CourseInfo.STATUS_FORBIDDEN)){
                            return "禁用";
                        }else {
                            return "启用";
                        }
                    })
                    .field("分类/序列", r->r.getCategory().getName())
                    .field("首次发布时间", r->{
                        if (ObjectUtils.isEmpty(r.getShelveTime())){
                            return "";
                        }else{
                            return DateUtil.format(r.getShelveTime());
                        }
                    });
            try {
                writer.write(response.getOutputStream());
            } catch (
                    IOException e) {
                throw new RuntimeException(e);
            }
        }
    }

    private  Integer findStatus(String courseId){
        List<Caption> captionList = captionService.findStatus(courseId);
        if (ObjectUtils.isEmpty(captionList) || captionList.isEmpty()){
            return CourseInfo.CAPTION_OVERALL_STATUS_NO_NEED_TO_GENERATE;
        }

        for (Caption caption : captionList) {
            if (Objects.equals(caption.getStatus(), Caption.CAPTION_STATUS_FAILED) || Objects.equals(caption.getStatus(), Caption.CAPTION_STATUS_UPLOAD_FAILED)){
                return CourseInfo.CAPTION_OVERALL_STATUS_GENERATE;
            }
        }
        return updateCaptionOverallStatus(courseId);
    }

    private Integer updateCaptionOverallStatus(String courseId) {
        List<Caption> captionList = captionService.findStatus(courseId);
        if (CollectionUtils.isEmpty(captionList)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_NO_NEED_TO_GENERATE;
        }

        Map<Integer, Integer> statusCount = new HashMap<>();
        for (Caption caption : captionList) {
            int status = caption.getStatus();
            statusCount.merge(status, 1, Integer::sum);
        }

        if (statusCount.containsKey(Caption.CAPTION_STATUS_UPLOAD_FAILED)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_NO_UPLOAD_FAILED;
        } else if (statusCount.containsKey(Caption.CAPTION_STATUS_FAILED)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_BUILD_FAILED;
        } else if (statusCount.containsKey(Caption.CAPTION_STATUS_ANALYSISING)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_GENERATE;
        } else if (statusCount.containsKey(Caption.CAPTION_STATUS_UNPUBLISHED) || statusCount.containsKey(Caption.CAPTION_STATUS_UNPUBLISH)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_GENERATED;
        } else if (statusCount.containsKey(Caption.CAPTION_STATUS_PUBLISHED)) {
            return CourseInfo.CAPTION_OVERALL_STATUS_PUBLISH_ALL;
        } else {
            return CourseInfo.CAPTION_OVERALL_STATUS_NO_NEED_TO_GENERATE;
        }
    }


    /**
     * 勾选全员共建共享
     * @param requestContext
     * @return
     */
    @RequestMapping(value = "/update-construction-type",method = RequestMethod.PUT)
    @Param(name = "courseId", required = true)
    @Param(name = "constructionType", defaultValue = "1", type = Integer.class)
    @Param(name = "courseName", required = true)
    @JSON("message")
    @Audit(module = "课程管理", subModule = "在线课程", action = Audit.Action.PUBLISH, fisrtAction = "修改", secondAction = "修改课程", desc = "修改课程《{0}》（全员共建共享）", params = {"courseName"}, businessType = "constructionType", businessValue = "1")
    public Map<String, Object> updateConstructionType(RequestContext requestContext) {
        String courseId = requestContext.getString("courseId");
        List<AudienceItem> audienceItems = audienceKit.parseAudienceItems(Optional.of(INTERNAL_ORGANIZATION_AUDIENCE_ITEMS));
        courseInfoAdminService.updateConstructionType(courseId, CourseInfo.CONSTRUCTION_TYPE_YES, audienceItems);
        return ImmutableMap.of("message", "更新成功");
    }
}
