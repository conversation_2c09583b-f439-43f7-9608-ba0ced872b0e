<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.zxy.product</groupId>
        <artifactId>course-study</artifactId>
        <version>cmu-9.6.0</version>
    </parent>
    <artifactId>course-study-web-server</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>web-server-parent</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>redis.clients</groupId>
                    <artifactId>jedis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.3</version>
        </dependency>
        <!-- socket -->
        <dependency>
            <groupId>io.socket</groupId>
            <artifactId>socket.io-client</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>course-study-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>exam-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>exam-stu-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.10.3</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>system-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>human-resource-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>ask-bar-api</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>zxy-log-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>train-api</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>common-office</artifactId>
        </dependency>
        <dependency>
            <groupId>org.csource</groupId>
            <artifactId>fastdfs-client-java</artifactId>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>

        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
            <version>1.1.6</version>
        </dependency>
        <!-- ��ͼ -->
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>3.17.3</version>
        </dependency>

        <!--   智能播报 开始     -->

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>fontbox</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>jempbox</artifactId>
            <version>1.8.11</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>xmpbox</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>preflight</artifactId>
            <version>2.0.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox-tools</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.15</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>3.15</version>
        </dependency>
        <!--   智能播报 结束     -->

        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>history-api</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.68</version> <!-- 替换为合适的版本号 -->
        </dependency>

        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.6</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.1.4</version>
        </dependency>

        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>common-encrypt</artifactId>
            <version>CMU-0.1.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.13</version>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.13</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <dependencies>
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>1.2.3.RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>
</project>