<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>course-study</artifactId>
        <groupId>com.zxy.product</groupId>
        <version>cmu-9.6.0</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>course-study-async-task-service</artifactId>

    <dependencies>
        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>async-service-parent</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.curator</groupId>
                    <artifactId>curator-recipes</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-framework</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>curator-client</artifactId>
                    <groupId>org.apache.curator</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>system-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>human-resource-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>course-study-api</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>zxy-log-api</artifactId>
            <version>${version}</version>
        </dependency>

        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>exam-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>train-api</artifactId>
            <version>${version}</version>
        </dependency>
        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>common-cache</artifactId>
        </dependency>
        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.3</version>
        </dependency>
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.17</version>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>jaxen</groupId>
            <artifactId>jaxen</artifactId>
            <version>1.1.6</version>
        </dependency>
        <!--分布式-->
        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>common-distributed</artifactId>
        </dependency>

        <!-- PCCW依赖配置-->
        <dependency>
            <groupId>commons-lang</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.6</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.6</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-io</artifactId>
            <version>1.3.2</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.3</version>
        </dependency>


        <dependency>
            <groupId>com.asiainfo</groupId>
            <artifactId>aiesb_sdk</artifactId>
            <version>1.0.0</version>
            <scope>system</scope>
            <systemPath>${basedir}/src/main/lib/aiesb_sdk_1.8_2020.4.16.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>1.8.13</version>
        </dependency>
        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.53</version>
        </dependency>

        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.15</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>3.15</version>
        </dependency>

        <dependency>
            <groupId>net.sourceforge.javacsv</groupId>
            <artifactId>javacsv</artifactId>
            <version>2.0</version>
        </dependency>

        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.53</version>
        </dependency>

        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>web-server-parent</artifactId>
            <type>pom</type>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.1.46</version>
            <scope>compile</scope>
        </dependency>

    </dependencies>

    <build>

        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <targetPath>BOOT-INF/classes/</targetPath>
            </resource>
            <resource>
                <directory>src/main/resources/</directory>
                <includes>
                    <include>**/*.*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/lib</directory>
                <targetPath>BOOT-INF/lib/</targetPath>
                <includes>
                    <include>**/*.jar</include>
                </includes>
            </resource>
        </resources>

        <plugins>

            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <includeSystemScope>true</includeSystemScope>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>org.springframework</groupId>
                        <artifactId>springloaded</artifactId>
                        <version>1.2.3.RELEASE</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>
    </build>

</project>