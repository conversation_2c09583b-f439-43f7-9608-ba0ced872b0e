package com.zxy.product.course.async.config;


import com.zxy.product.course.content.DataSourceEnum;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.autoconfigure.jdbc.DataSourceBuilder;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;

import javax.sql.DataSource;
import java.util.HashMap;
import java.util.Map;

@Configuration
public class DynamicDataSourceConfig {

    private static final Logger logger= LoggerFactory.getLogger(DynamicDataSourceConfig.class);


    //主数据源
    @Bean(name = "masterDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.master")
    public DataSource masterDataSource() {
        return DataSourceBuilder.create().build();
    }


    //从数据源
    @Bean(name = "slaveDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.slave")
    public DataSource slaverDataSource() {
        return DataSourceBuilder.create().build();
    }

    //动态切换数据源
    @Bean
    @Primary
    public DynamicDataSource myDataSource(@Qualifier("masterDataSource") DataSource masterDataSource, @Qualifier("slaveDataSource") DataSource slaveDataSource) {
        logger.error("课程主库数据源{}" , masterDataSource);
        logger.error("课程从库数据源{}" , slaveDataSource );
        Map<Object, Object> targetDataSources = new HashMap<>();
        targetDataSources.put(DataSourceEnum.MASTER.getName(), masterDataSource);
        targetDataSources.put(DataSourceEnum.SLAVE.getName(), slaveDataSource);
        return new DynamicDataSource(masterDataSource, targetDataSources);
    }

}
