package com.zxy.product.course.async.task;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.asiainfo.openplatform.AIESBClient;
import com.asiainfo.openplatform.utils.AIESBConstants;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Maps;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.async.config.DynamicDataSource;
import com.zxy.product.course.async.listener.PccwCollectListener;
import com.zxy.product.course.async.util.*;
import com.zxy.product.course.content.DataSource;
import com.zxy.product.course.content.DataSourceEnum;
import com.zxy.product.course.dto.*;
import com.zxy.product.course.entity.CourseCategory;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.jooq.tables.pojos.MemberEntity;
import com.zxy.product.system.api.permission.OrganizationHrMappingService;
import com.zxy.product.system.entity.OrganizationHrMapping;
import org.apache.commons.lang.StringUtils;
import org.jooq.Condition;
import org.jooq.Field;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.*;

/**
 * 电讯盈科(PCCW)人力集中化系统(iHR)接口.
 *
 * <AUTHOR>
 */
@Component
@EnableAsync
public class PccwTask {

    private static final Logger log = LoggerFactory.getLogger(PccwTask.class);

    @Autowired
    private CommonDao<CourseSectionStudyLogAhDay> courseSectionStudyLogDayDao;

    @Autowired
    private CommonDao<PccwResult> pccwResultDao;

    @Autowired
    private CommonDao<CourseInfo> courseInfoDao;

    @Autowired
    private CommonDao<CourseCategory> courseCategoryDao;

    @Autowired
    private CommonDao<Organization> organizationDao;

    @Autowired
    private CommonDao<SplitTableConfig> splitTableConfigDao;

    @Autowired
    private OrganizationHrMappingService organizationHrMappingService;

    @Autowired
    private PccwCollectListener pccwCollectListener;

    @Autowired
    private CommonDao<PccwOrganizationConfig> configDao;

    @Autowired
    private CommonDao<CourseStudyProgress> progressDao;

    @Autowired
    private CommonDao<PccwResultBusiness> businessCommonDao;

    @Autowired
    private CourseCacheService cacheService;


    /**
     * M_HR_CUST_140000001-导入员工在线学习时长. 凌晨四点执行.
     */
    //@Scheduled(cron = "0 30 4 * * ? ")
    @Transactional(rollbackFor = Exception.class)
    @Async
    @DataSource(type= DataSourceEnum.SLAVE)
    public void onlineStudyTotalTime() {
        String method = "M_HR_CUST_140000001";
        Map<String, String> sysParam = getSysParamMap(method);
        // 处理失败历史数据
        List<PccwResult> failResultList = pccwResultDao
                .fetch(PCCW_RESULT.STATUS.eq(0).and(PCCW_RESULT.METHOD.eq(method)));
        if (Objects.nonNull(failResultList) && failResultList.size() > 0) {
            failResultList.stream().forEach(fr -> {
                Long createTime = fr.getCreateTime();
                String linkId = fr.getId();
                // 返回前一天 00:00:00 - 23:59:59增量数据
                Integer date = DateUtil.getYesterdayStr(createTime);
                //long end = DateUtil.getYesterdayEndTime(createTime);
                boolean dealResult = dealOnlineStudyTotalTime(sysParam, method, date, linkId,null);
                log.info("dealOnlineStudyTotalTime. time = {}, result = {},linkId={}",
                        System.currentTimeMillis(), dealResult, linkId);
                //处理成功
                int state = 1;
                if (!dealResult) {
                    //处理失败,下次继续处理
                    state = 0;
                }
                //更新历史处理记录
                fr.setStatus(state);
                fr.setUpdateTime(System.currentTimeMillis());
                pccwResultDao.update(fr);
            });
        }
        //处理当天记录
        long now = System.currentTimeMillis();
        Integer date = DateUtil.getYesterdayStr(now);
        //long end = DateUtil.getYesterdayEndTime(now);
        boolean deal = dealOnlineStudyTotalTime(sysParam, method, date, null,null);
        log.info("dealOnlineStudyTotalTime. time = {}, result = {}", System.currentTimeMillis(), deal);
    }

    /**
     * M_HR_CUST_140000005-导入课程清单服务.
     * <p>
     * 凌晨四点执行.
     */
    @DataSource(type= DataSourceEnum.SLAVE)
    @Scheduled(cron = "0 5 4 * * ? ")
    //@Scheduled(cron = "0 5 * * * ? ")
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void courseInfo() {
        String method = "M_HR_CUST_140000005";
        Map<String, String> sysParam = getSysParamMap(method);
        // 处理失败历史数据
        List<PccwResult> failResultList = pccwResultDao.fetch(PCCW_RESULT.STATUS.eq(0)
                .and(PCCW_RESULT.METHOD.eq(method)));
        if (Objects.nonNull(failResultList) && failResultList.size() > 0) {
            failResultList.stream().forEach(fr -> {
                Long createTime = fr.getCreateTime();
                String linkId = fr.getId();
                // 返回前一天 00:00:00 - 23:59:59增量数据
                long start = DateUtil.getYesterdayBeginTime(createTime);
                long end = DateUtil.getYesterdayEndTime(createTime);
                boolean dealResult = dealImportCourseInfo(sysParam, method, start, end, linkId);
                log.info("courseInfo. time = {}, result = {},linkId={}", System.currentTimeMillis(),
                        dealResult, linkId);
                //处理成功
                int state = 1;
                if (!dealResult) {
                    //处理失败,下次继续处理
                    state = 0;
                }
                //更新历史处理记录
                fr.setStatus(state);
                fr.setUpdateTime(System.currentTimeMillis());
                pccwResultDao.update(fr);
            });
        }
        //处理当天记录
        long start = DateUtil.getYesterdayBeginTime(System.currentTimeMillis());
        long end = DateUtil.getYesterdayEndTime(System.currentTimeMillis());
        boolean deal = dealImportCourseInfo(sysParam, method, start, end, null);
        log.info("courseInfo. time = {}, result = {}", System.currentTimeMillis(), deal);
    }

    /**
     * M_HR_CUST_1400000011-员工收藏的网大课程记录
     * 定时处理同步员工收藏记录失败的数据
     */
    @Scheduled(cron = "0 15 2 * * ? ")
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void collectCourse() {
        String method = "M_HR_CUST_1400000011";
        // 查询失败的记录
        List<PccwResult> failList = pccwResultDao.fetch(PCCW_RESULT.METHOD.eq(method).and(PCCW_RESULT.STATUS.eq(0)));
        // 遍历处理数据
        failList.forEach(result -> {
            // 失败的相关数据
            String outputExt = result.getOutputExt();
            if(!StringUtils.isEmpty(outputExt)) {
                String[] resultList = outputExt.split("&");
                // outputExt存储了失败的对应学员以及课程数据
                if (resultList.length >= 4) {
                    String courseId = resultList[0];
                    String memberId = resultList[1];
                    Long collectTime = Long.valueOf(resultList[2]);
                    Integer businessType = Integer.valueOf(resultList[3]);
                    // 查询是否课程，只同步课程数据
                    CourseInfo courseInfo = pccwCollectListener.findCourseInfo(courseId);

                    // 如果不是课程不需要同步数据
                    if (courseInfo == null) {
                        log.error("同步人员收藏记录，非课程不需要同步，businessId={}, memberId={}, collectTime={}, businessType={}", courseId, memberId, collectTime, businessType);
                        return ;
                    }
                    Map<String, String> sysParamMap = PccwUtil.getSysParamMap(method);
                    // 同步失败的记录，找个点儿定时同步
                    pccwCollectListener.dealImportCollect(sysParamMap, courseInfo.getId(), memberId, businessType, collectTime, courseInfo.getName(), method, Optional.of(result.getId()));
                }

            }
        });

    }


    /**
     * M_HR_CUST_1400000010-导出网大课程分类接口. 凌晨四点执行
     */
    @Scheduled(cron = "0 25 4 * * ? ")
    //@Scheduled(cron = "0 25 * * * ? ")
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void exportCourseCategory() {
        log.info("courseCategory开始. time = {}", System.currentTimeMillis());
        //接口请求路径
        String path = "/hrd/sync/train/categoryList";
        //能力编码
        String method = "M_HR_CUST_1400000010";
        Map<String, String> sysParam = getSysParamMap(method);
        com.zxy.product.course.jooq.tables.CourseCategory parentCategory = COURSE_CATEGORY.as("parentCategory");
        Field<String> parentCategoryId = parentCategory.ID.as("parentCategoryId");
        Field<String> parentCategoryName = parentCategory.NAME.as("parentCategoryName");
        List<com.zxy.product.course.dto.CourseCategory> courseCategoryList = courseCategoryDao.execute(cf -> cf.select(
                COURSE_CATEGORY.ID,
                COURSE_CATEGORY.NAME,
                parentCategoryId,
                parentCategoryName)
                .from(COURSE_CATEGORY).leftJoin(parentCategory).on(parentCategory.ID.eq(COURSE_CATEGORY.PARENT_ID))
                .where(COURSE_CATEGORY.ORGANIZATION_ID.eq("1")).and(COURSE_CATEGORY.PARENT_ID.isNotNull())
                .fetch().map(x -> {
                            com.zxy.product.course.dto.CourseCategory courseCategory = new com.zxy.product.course.dto.CourseCategory();
                            courseCategory.setTrainSerial(x.get(COURSE_CATEGORY.NAME, String.class));
                            courseCategory.setTrainSerialID(x.get(COURSE_CATEGORY.ID, String.class));
                            courseCategory.setTrainType(x.get(parentCategoryName, String.class));
                            courseCategory.setTrainTypeID(x.get(parentCategoryId, String.class));
                            return courseCategory;
                        }
                ));
        RequestDto<com.zxy.product.course.dto.CourseCategory> categoryRequestDto = new RequestDto<>();
        categoryRequestDto.setCurrentPage(1);
        categoryRequestDto.setData(courseCategoryList);
        categoryRequestDto.setPageSize(courseCategoryList.size());
        //省公司编码
        //courseInfoDTO.setProvinceCode(provinceCode);
        //表示是数据来源为网大
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("source", "OU");
        categoryRequestDto.setInputExt(JSON.toJSONString(jsonObject));
        // 封装业务数据
        String busiParam = JSONObject.toJSONString(categoryRequestDto);
        try {
            // 调用外部服务接口
            log.info("courseCategory request param = {}", busiParam);
            String responseStr = AIESBClient
                    .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
            log.info("courseCategory response = {}", responseStr);
            dealResponse(responseStr);
        } catch (Exception e) {
            log.info("courseCategory. time = {}, result = {}", System.currentTimeMillis(), "失败");
            log.error("导出网大课程分类ERROR", e);
        }
    }

    /**
     * M_HR_CUST_1400000012-网大浏览课程次数同步接口. 凌晨四点执行
     */
    @Scheduled(cron = "0 15 4 1 * ? ")
    //@Scheduled(cron = "0 15 * * * ? ")
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void exportCourseCollectCount() {
        log.info("courseVisit开始. time = {}", System.currentTimeMillis());
        //接口请求路径
        String path = "/hrd/sync/train/browseList";
        //能力编码
        String method = "M_HR_CUST_1400000012";
        Map<String, String> sysParam = getSysParamMap(method);
        // 获取所有有浏览量课程的省公司组织ID
        List<String> orgIds = courseInfoDao
                .execute(cf -> cf.selectDistinct(COURSE_INFO.ORGANIZATION_ID).from(COURSE_INFO)
                        .where(COURSE_INFO.VISITS.ge(1))
                        .and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                        .fetch(COURSE_INFO.ORGANIZATION_ID));
        Map<String, Organization> companyOrganizeMap = getCompanyOrganizeMap(orgIds);
        Set<String> rootOrgIds = companyOrganizeMap.keySet();
        int rootOrgSize = rootOrgIds.size();
        log.info("待处理网大浏览课程次数同步,组织数量IdSize={}", rootOrgSize);
        if (rootOrgSize == 0) {
            return;
        }
        for (String rootOrg : rootOrgIds) {
            Organization organization = companyOrganizeMap.get(rootOrg);
            String provinceCode = null;
            try {
                provinceCode = getProvinceCode(organization);
            } catch (Exception e) {
                log.error("网大浏览课程次数同步状态：org={},msg={}", organization.getId(), e.getMessage());
                continue;
            }
            int page = 1;
            int pageSize = 50;
            //当k<1时退出循环
            int k = 1;
            Condition condition = ORGANIZATION.PATH.like("1,10000001," + rootOrg + "%");
            if (Objects.equals(rootOrg, "1")) {
                condition = ORGANIZATION.ID.eq("1");
            }
            String lastMonth = DateUtil.getLastMonth(DateUtil.DATE_PATTERN_YM);
            while (k == 1) {
                final int currentPage = (page - 1) * pageSize;
                Condition finalCondition = condition;
                List<CourseVisit> courseVisitList = courseInfoDao
                        .execute(cf -> cf.select(
                                COURSE_INFO.ID,
                                COURSE_INFO.NAME,
                                COURSE_INFO.VISITS)
                                .from(COURSE_INFO)
                                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                                .where(COURSE_INFO.VISITS.ge(1))
                                .and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                                .and(finalCondition)
                                .limit(currentPage, pageSize + 1).fetch().map(x -> {
                                    CourseVisit courseVisit = new CourseVisit();
                                    //课程id
                                    courseVisit.setCourseId(x.get(COURSE_INFO.ID, String.class));
                                    //课程名称
                                    courseVisit.setCourseName(x.get(COURSE_INFO.NAME, String.class));
                                    //浏览量
                                    courseVisit.setViewsTotal(x.get(COURSE_INFO.VISITS, String.class));
                                    //年月
                                    courseVisit.setPeriod(lastMonth);
                                    return courseVisit;
                                }));
                //记录总数
                int size = courseVisitList == null ? 0 : courseVisitList.size();
                log.info("网大浏览课程次数同步,待处理总数据size={}", size);
                if (size > 0) {
                    // 是否还有下一页记录
                    if (size > pageSize) {
                        courseVisitList.remove(size - 1);
                    }
                    RequestDto<CourseVisit> requestDto = new RequestDto<>();
                    requestDto.setCurrentPage(page);
                    requestDto.setData(courseVisitList);
                    requestDto.setPageSize(pageSize);
                    //省公司编码
                    requestDto.setProvinceCode(provinceCode);
                    //表示是数据来源为网大
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("source", "OU");
                    requestDto.setInputExt(JSON.toJSONString(jsonObject));
                    // 封装业务数据
                    String busiParam = JSONObject.toJSONString(requestDto);
                    try {
                        // 调用外部服务接口
                        log.info("courseVisit request param = {}", busiParam);
                        String responseStr = AIESBClient
                                .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
                        log.info("courseVisit response = {}", responseStr);
                        dealResponse(responseStr);
                    } catch (Exception e) {
                        log.info("courseVisit. time = {}, result = {}", System.currentTimeMillis(), "失败");
                        log.error("网大浏览课程次数同步ERROR", e);
                    }
                }
                // 多页记录
                if (size > pageSize) {
                    page++;
                } else {
                    k = 0;
                    page = 1;
                }
            }
        }
    }


    /**
     * M_HR_CUST_140010015--员工网大课程学习状态推送服务. 凌晨四点执行
     */
    // 这个定时器是以前写的，后面使用PccwStudySatusForCsvTask.exportCourseStudyProgress这个替代，按照文件的形式传输
//    @Scheduled(cron = "0 0 2 * * ? ")
    @Transactional(rollbackFor = Exception.class)
    @Async
    public void exportCourseStudyProgress() {
        DynamicDataSource.setDataSource(DataSourceEnum.SLAVE.getName());
        log.info("courseStudyProgress开始，time = {}", System.currentTimeMillis());

        //能力编码
        String method = "M_HR_CUST_140010015";
        Map<String, String> sysParam = getSysParamMap(method);
        // 处理失败历史数据
        List<PccwResult> failResultList = pccwResultDao.fetch(PCCW_RESULT.STATUS.eq(0)
                .and(PCCW_RESULT.METHOD.eq(method)));
        if (Objects.nonNull(failResultList) && failResultList.size() > 0) {
            failResultList.stream().forEach(fr -> {
                Long createTime = fr.getCreateTime();
                String linkId = fr.getId();
                // 返回前一天 00:00:00 - 23:59:59增量数据
                long start = DateUtil.getYesterdayBeginTime(createTime);
                long end = DateUtil.getYesterdayEndTime(createTime);
                boolean dealResult = dealImportCourseStudyProgress(sysParam, method, start, end, fr.getId(),null,null);
                log.info("progress同步. time = {}, result = {},linkId={}", System.currentTimeMillis(),
                        dealResult, linkId);
            });
        }
        //处理当天记录
        long start = DateUtil.getYesterdayBeginTime(System.currentTimeMillis());
        long end = DateUtil.getYesterdayEndTime(System.currentTimeMillis());
        boolean deal = dealImportCourseStudyProgress(sysParam, method, start, end, null,null,null);
        log.info("couseStudyProgress. time = {}, result = {}", System.currentTimeMillis(), deal);
    }

    /**
     * M_HR_CUST_140000001在线学习时长手动推送数据
     */
    //@Scheduled(fixedDelay = 60 * 60 * 1000)
    @Async
    public void handPushCourseOnlineStudyTime() {
        DynamicDataSource.setDataSource(DataSourceEnum.SLAVE.getName());
        // 在线学习时长
        String method = "M_HR_CUST_140000001";
        Map<String, String> sysParam = getSysParamMap(method);
        try{
            Map<String,Object> map=cacheService.getHandPushData(method);
            log.error("缓存中的数据，{}", map!=null&&!map.isEmpty() ? map.get("methodType") : "");
            if(map!=null&&!map.isEmpty()){
                Integer type=(Integer)map.get("type");
                switch (type){
                    case 1:
                        dealOnlineStudyTotalTime(sysParam, method);
                        break;
                    case 3:
                        log.error("测试集中人力课程在线学习时长数据手动推送正常，date={}",System.currentTimeMillis());
                        break;
                }
            }
        }catch(Exception e){
            log.error("集中人力课程在线学习时长数据手动推送失败，error={}",e);
        }finally{
            //无论是否成功调用，必须清除redis
            cacheService.clearHandPushPccwData(method);
        }
    }

    /**
     * M_HR_CUST_140000005 课程信息手动推送数据
     */
    @Scheduled(fixedDelay = 60 * 60 * 1000)
    @Async
    @DataSource(type= DataSourceEnum.SLAVE)
    public void handPushCourseInfo() {
        DynamicDataSource.setDataSource(DataSourceEnum.SLAVE.getName());
        // 在线学习时长
        String method = "M_HR_CUST_140000005";
        Map<String, String> sysParam = getSysParamMap(method);
        try{
            Map<String,Object> map=cacheService.getHandPushData(method);
            log.error("缓存中的数据，{}", map!=null&&!map.isEmpty() ? map.get("methodType") : "");
            if(map!=null&&!map.isEmpty()){
                Integer type=(Integer)map.get("type");
                switch (type){
                    case 1:
                        Long startTime=(Long)map.get("startTime");
                        Long endTime=(Long)map.get("endTime");
                        dealImportCourseInfo(sysParam, method, startTime, endTime, null);
                        break;
                    case 3:
                        log.error("测试集中人力课程信息数据手动推送正常，date={}",System.currentTimeMillis());
                        break;
                }
            }
        }catch(Exception e){
            log.error("集中人力课程信息数据手动推送失败，error={}",e);
        }finally{
            //无论是否成功调用，必须清除redis
            cacheService.clearHandPushPccwData(method);
        }
    }

    /**
     * M_HR_CUST_140010015 课程进度信息手动推送数据
     */
    @Scheduled(fixedDelay =60 * 60 * 1000)
    @Async
    public void handPushCourseStudyProgress() {
        DynamicDataSource.setDataSource(DataSourceEnum.SLAVE.getName());
        // 在线学习时长
        String method = "M_HR_CUST_140010015";
        Map<String, String> sysParam = getSysParamMap(method);
        try{
            Map<String,Object> map=cacheService.getHandPushData(method);
            log.error("缓存中的数据，{}", map!=null&&!map.isEmpty() ? map.get("methodType") : "");
            if(map!=null&&!map.isEmpty()){
                Integer type=(Integer)map.get("type");
                switch (type){
                    case 1:
                        Long startTime=(Long)map.get("startTime");
                        Long endTime=(Long)map.get("endTime");

                        List<String> userId = new ArrayList<>();
                        Object userId1 = map.get("userId");
                        if (!ObjectUtils.isEmpty(userId1)){
                            userId=(List)userId1;
                        }

                        List<String> courseIds = new ArrayList<>();
                        Object courseId1 = map.get("courseId");
                        if (!ObjectUtils.isEmpty(courseId1)){
                            courseIds=(List)courseId1;
                        }

                        log.error("缓存中的数据userid，{}", userId.toString());
                        dealImportCourseStudyProgress(sysParam, method, startTime, endTime, null,userId,courseIds);
                        break;
                    case 3:
                        log.error("测试集中人力课程进度信息数据手动推送正常，date={}",System.currentTimeMillis());
                        break;
                }
            }
        }catch(Exception e){
            log.error("集中人力课程进度信息数据手动推送失败，error={}",e);
        }finally{
            //无论是否成功调用，必须清除redis
            cacheService.clearHandPushPccwData(method);
        }
    }

    /**
     * 获取公司组织编码
     *
     * @param orgIds 组织id
     * @return 公司组织编码
     */
    private Map<String, Organization> getCompanyOrganizeMap(List<String> orgIds) {
        int orgSize = orgIds == null ? 0 : orgIds.size();
        // 省公司信息
        Map<String, Organization> rootOrgMap = Maps.newHashMap();
        if (orgSize > 0) {
            List<String> companyIds = organizationDao.execute(x ->
                    x.selectDistinct(ORGANIZATION.COMPANY_ID).from(ORGANIZATION)
                            .where(ORGANIZATION.ID.in(orgIds))).fetch(ORGANIZATION.COMPANY_ID);
            List<Organization> rootOrgs = organizationDao.execute(on ->
                    on.selectDistinct(ORGANIZATION.ID, ORGANIZATION.IHR_CODE, ORGANIZATION.CODE).from(ORGANIZATION)
                            .where(ORGANIZATION.ID.in(companyIds))
                            .fetchInto(Organization.class));
            rootOrgs.forEach(x -> {
                rootOrgMap.put(x.getId(), x);
            });
        }
        return rootOrgMap;
    }

    /**
     * 获取公司组织编码
     *
     * @param organization 组织信息
     * @return 省编码
     */
    private String getProvinceCode(Organization organization) throws Exception {
        String provinceCode = null;
        // 返回新组织编码,没有返回旧组织编码
        if (Objects.nonNull(organization)) {
            provinceCode = organization.getIhrCode();
            if (StringUtils.isBlank(provinceCode)) {
                provinceCode = organization.getCode();
            }
            //如果是中国移动节点或者内部组织节点，数据归属到集团总部组织下（HQ）
            if ("inner".equals(provinceCode) || "cmcc".equals(provinceCode)) {
                provinceCode = "HQ";
            } else {
                //获取人力发展系统组织映射编码
                provinceCode = getHrMappingCode(organization.getId());
            }
        }
        return provinceCode;
    }

    /**
     * 公共参数Map.
     */
    private Map<String, String> getSysParamMap(String method) {
        Map<String, String> sysParam = Maps.newHashMap();
        sysParam.put("appId", "10036");
        sysParam.put("method", method);
        sysParam.put("format", "json");
        sysParam.put("version", "V1.0");
        sysParam.put("timestamp", DateUtil.format(new Date(), DateUtil.DATE_TIME_PATTERN));
        return sysParam;
    }

    /**
     * 导入课程清单.
     *
     * @param sysParam 公共入参
     * @param start    开始时间
     * @param end      结束时间
     * @param method   请求接口类型
     * @param linkId   上次处理失败的记录ID(默认和当前主键相同)
     */
    private boolean dealImportCourseInfo(Map<String, String> sysParam, String method, long start,
                                         long end, String linkId) {
        //接口请求路径
        String path = "/hrd/sync/train/courseList";

        // 获取所有有新增课程的省公司组织ID
        List<String> orgIds = courseInfoDao
                .execute(cf -> cf.selectDistinct(COURSE_INFO.ORGANIZATION_ID).from(COURSE_INFO)
                        .where(COURSE_INFO.RELEASE_TIME.between(start, end))
                        .fetch(COURSE_INFO.ORGANIZATION_ID));
        Map<String, Organization> companyOrganizeMap = getCompanyOrganizeMap(orgIds);
        Set<String> rootOrgIds = companyOrganizeMap.keySet();
        int rootOrgSize = rootOrgIds.size();
        log.info("待处理导入课程清单省公司信息,组织数量IdSize={}", rootOrgSize);
        if (rootOrgSize == 0) {
            return true;
        }
        for (String rootOrg : rootOrgIds) {
            Organization organization = companyOrganizeMap.get(rootOrg);
            String provinceCode = null;
            try {
                provinceCode = getProvinceCode(organization);
            } catch (Exception e) {
                log.error("导入课程清单状态：org={},msg={}", organization.getId(), e.getMessage());
                continue;
            }
            int page = 1;
            int pageSize = 50;
            //当k<1时退出循环
            int k = 1;
            Condition condition = ORGANIZATION.PATH.like("1,10000001," + rootOrg + "%");
            if (Objects.equals(rootOrg, "1")) {
                condition = ORGANIZATION.ID.eq("1");
            }
            while (k >= 1) {
                final int currentPage = (page - 1) * pageSize;
                Condition finalCondition = condition;
                com.zxy.product.course.jooq.tables.CourseCategory parentCategory = COURSE_CATEGORY.as("parentCategory");
                Field<String> parentCategoryId = parentCategory.ID.as("parentCategoryId");
                Field<String> parentCategoryName = parentCategory.NAME.as("parentCategoryName");
                List<com.zxy.product.course.dto.CourseInfo> courseInfos = courseInfoDao
                        .execute(cf -> cf.select(
                                COURSE_INFO.COURSE_TIME,
                                COURSE_INFO.ID,
                                COURSE_INFO.NAME,
                                COURSE_INFO.LECTURER,
                                COURSE_INFO.CODE,
                                COURSE_INFO.DESCRIPTION_TEXT,
                                COURSE_INFO.COVER_PATH,
                                COURSE_CATEGORY.ID,
                                COURSE_CATEGORY.NAME,
                                parentCategoryId,
                                parentCategoryName)
                                .from(COURSE_INFO)
                                .leftJoin(COURSE_CATEGORY).on(COURSE_INFO.CATEGORY_ID.eq(COURSE_CATEGORY.ID))
                                .leftJoin(parentCategory).on(parentCategory.ID.eq(COURSE_CATEGORY.PARENT_ID))
                                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                                .where(COURSE_INFO.RELEASE_TIME.between(start, end))
                                .and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                                .and(finalCondition)
                                .limit(currentPage, pageSize + 1).fetch().map(x -> {
                                    com.zxy.product.course.dto.CourseInfo courseInfo = new com.zxy.product.course.dto.CourseInfo();
                                    //时长(s)
                                    int duration =
                                            Objects.isNull(x.get(COURSE_INFO.COURSE_TIME, Integer.class)) ? 0 : x.get(COURSE_INFO.COURSE_TIME, Integer.class);
                                    //将秒转化为分
                                    courseInfo.setTrainDuration(duration / 60);
                                    //课程id
                                    courseInfo.setCourseId(x.get(COURSE_INFO.ID, String.class));
                                    //课程名称
                                    courseInfo.setCourseName(x.get(COURSE_INFO.NAME, String.class));
                                    //讲师名称
                                    courseInfo.setTrainerName(x.get(COURSE_INFO.LECTURER, String.class) == null ?
                                            "暂无" : x.get(COURSE_INFO.LECTURER, String.class));
                                    //序列
                                    courseInfo.setTrainSerial(x.get(COURSE_CATEGORY.NAME, String.class));
                                    //分类
                                    courseInfo.setTrainType(
                                            StringUtils.isBlank(x.get(parentCategoryName, String.class)) ? x
                                                    .get(COURSE_CATEGORY.NAME, String.class) : x.get(parentCategoryName, String.class));
                                    //资源URL
                                    courseInfo
                                            .setTrainResourceUrl("/study/course/detail/" + courseInfo.getCourseId());
                                    //扩展字段--课程编号
                                    JSONObject jsonObject = new JSONObject();
                                    jsonObject.put("course_no", x.get(COURSE_INFO.CODE, String.class));
                                    String categoryId = x.get(COURSE_CATEGORY.ID, String.class);
                                    String parentId = x.get(parentCategoryId, String.class);
                                    String desc = x.get(COURSE_INFO.DESCRIPTION_TEXT, String.class);
                                    String imgUrl = x.get(COURSE_INFO.COVER_PATH, String.class);
                                    String defaultImgUrl = "images/default-cover/default_course.jpg";
                                    //课程序列编号
                                    jsonObject.put("trainSerialID", categoryId);
                                    //课程类别编号
                                    jsonObject.put("trainTypeID", StringUtils.isBlank(parentId) ? categoryId : parentId);
                                    //课程图片链接
                                    jsonObject.put("trainImg", "/" + (StringUtils.isBlank(imgUrl) ? defaultImgUrl : imgUrl));
                                    //课程简介
                                    jsonObject.put("trainIntroduction", desc);

                                    //课程标签
                                    List<String> topicNames = courseInfoDao.execute(s -> s.select(TOPIC.NAME).from(COURSE_INFO)
                                            .leftJoin(BUSINESS_TOPIC).on(COURSE_INFO.ID.eq(BUSINESS_TOPIC.BUSINESS_ID))
                                            .leftJoin(TOPIC).on(TOPIC.ID.eq(BUSINESS_TOPIC.TOPIC_ID))
                                            .where(COURSE_INFO.ID.eq(x.get(COURSE_INFO.ID, String.class))).fetch(TOPIC.NAME)
                                    );
                                    if (topicNames != null && !topicNames.isEmpty()) {
                                        String topicName = String.join(",", topicNames);
                                        jsonObject.put("trainTag", topicName);
                                    }
                                    courseInfo.setInputExt(JSON.toJSONString(jsonObject));
                                    return courseInfo;
                                }));
                //记录总数
                int size = courseInfos == null ? 0 : courseInfos.size();
                log.info("导入课程清单,待处理总数据size={}", size);
                if (size > 0) {
                    // 是否还有下一页记录
                    if (size > pageSize) {
                        courseInfos.remove(size - 1);
                    }
                    CourseInfoDTO courseInfoDTO = new CourseInfoDTO();
                    courseInfoDTO.setCurrentPage(page);
                    courseInfoDTO.setData(courseInfos);
                    courseInfoDTO.setPageSize(pageSize);
                    //省公司编码
                    courseInfoDTO.setProvinceCode(provinceCode);
                    //表示是数据来源为网大
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("source", "OU");
                    courseInfoDTO.setInputExt(JSON.toJSONString(jsonObject));
                    // 封装业务数据
                    String busiParam = JSONObject.toJSONString(courseInfoDTO);
                    PccwResult pccwResult = new PccwResult();
                    pccwResult.forInsert();
                    //默认失败处理linkId与主键相同
                    pccwResult.setLinkId(pccwResult.getId());
                    boolean linkIdIsBlank = StringUtils.isNotBlank(linkId);
                    if (linkIdIsBlank) {
                        pccwResult.setLinkId(linkId);
                    }
                    pccwResult.setUpdateTime(System.currentTimeMillis());
                    pccwResult.setMethod(method);
                    //处理状态码 0:失败 1:成功 2:失败记录本次处理失败,下次待处理
                    int status = linkIdIsBlank ? 2 : 0;
                    try {
                        // 调用外部服务接口
                        log.info("courseInfo request param = {}", busiParam);
                        String responseStr = AIESBClient
                                .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
                        log.error("courseInfo response = {}", responseStr);
                        pccwResultDao.insert(dealResponse(pccwResult, responseStr, status));
                    } catch (Exception e) {
                        log.error("导入课程清单服务ERROR", e);
                        pccwResult.setStatus(status);
                        pccwResult.setInstanceId("-1");
                        pccwResult.setRespCode("-1");
                        pccwResult.setRespDesc("导入课程清单服务ERROR");
                        pccwResultDao.insert(pccwResult);
                        return false;
                    }
                }
                // 多页记录
                if (size > pageSize) {
                    page++;
                } else {
                    k = 0;
                    page = 1;
                }
            }
        }
        return true;
    }

    /**
     * 导入员工在线学习时长.
     *
     * @param sysParam 公共入参
     * @param date     查询时间 例如：20191018
     * @param method   请求接口类型
     * @param linkId   上次处理失败的记录ID(默认和当前主键相同)
     */
    private boolean dealOnlineStudyTotalTime(Map<String, String> sysParam, String method, Integer date,
                                             String linkId,String userId) {
        Optional<String> userOpt = (userId == null ? Optional.empty() :  Optional.of(userId));
        //接口路径
        String path = "/hrd/sync/train/onlineStudyDuration";
        // 待处理分表
        List<SplitTableConfig> splitTables = splitTableConfigDao.fetch(SPLIT_TABLE_CONFIG.SOURCE.eq(4));
        // 除other外其他省公司数据
        for (SplitTableConfig config : splitTables) {
            String targetTable = config.getTargetTable();
            //省公司ID
            String orgCode = config.getOrganizationId();
            String provinceCode = null;
//            organizationDao.fetchOne(ORGANIZATION.ID.eq(orgCode)).ifPresent(x -> {
//                provinceCode[0] = x.getIhrCode();
//                if (StringUtils.isBlank(provinceCode[0])) {
//                    provinceCode[0] = x.getCode();
//                }
//
//            });
            // 获取人力发展系统组织映射编码
            try {
                provinceCode = getHrMappingCode(orgCode);
            } catch (Exception e) {
                log.error("导入员工在线学习时长状态,org={},msg={}", orgCode, e.getMessage());
                continue;
            }
            TableImpl table = getTable(targetTable);

            // 跳过other
            if (table == null) {
                continue;
            }
            log.info("导入员工在线学习时长处理表名：" + table.getName());
            //获取t_course_study_progress_.*分表
//            TableImpl progressTable=getProgressTable(targetTable);
//            if(progressTable==null){
//                continue;
//            }
            int page = 1;
            int pageSize = 50;
            //当k<1时退出循环
            int k = 1;
            Condition condition = Stream.of( userOpt.map(x-> table.field("f_member_id", String.class).eq(userId))
            ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());
            while (k >= 1) {
                final int currentPage = (page - 1) * pageSize;
                //先分页查出section_study_log_day的主键ID，然后根据ID查询相应的关联表字段
                List<String> logDayIdList = courseSectionStudyLogDayDao
                        .execute(csl ->
                                csl.select(
                                        table.field("f_id", String.class)
                                )
                                        .from(table)
                                        .where(table.field("f_day", Integer.class).eq(date))
                                        .and(condition)
                                        .limit(currentPage, pageSize + 1).fetch().map(x -> {
                                    return String.valueOf(x.get(table.field("f_id"), String.class));
                                }));

                List<OnlineStudyTotalTime> onlineStudyTotalTimeList = null;
                if (logDayIdList != null && logDayIdList.size() > 0) {
                    onlineStudyTotalTimeList = courseSectionStudyLogDayDao
                            .execute(csl ->
                                    csl.select(
                                            MEMBER.NAME,
                                            MEMBER.IHR_CODE,
                                            MEMBER.FULL_NAME,
                                            COURSE_INFO.ID,
                                            COURSE_INFO.NAME,
                                            table.field("f_day", Integer.class),
                                            table.field("f_study_time", Integer.class)
                                            //progressTable.field("f_finish_status", Integer.class)
                                    )
                                            .from(table)
                                            .leftJoin(COURSE_INFO)
                                            .on(COURSE_INFO.ID.eq(table.field("f_course_id")))
                                            .leftJoin(MEMBER).on(MEMBER.ID.eq(table.field("f_member_id")))
                                            .where(table.field("f_id", String.class).in(logDayIdList))
                                            .fetch().map(x -> {
                                        OnlineStudyTotalTime onlineStudyTotalTime = new OnlineStudyTotalTime();
                                        // 新旧员工编号设置
                                        String ihrCode = x.get(MEMBER.IHR_CODE, String.class);
                                        if (StringUtils.isBlank(ihrCode)) {
                                            ihrCode = x.get(MEMBER.NAME, String.class);
                                        }
                                        onlineStudyTotalTime.setEmpNumber(ihrCode);
                                        onlineStudyTotalTime.setEmpName(x.get(MEMBER.FULL_NAME, String.class));
                                        onlineStudyTotalTime.setCourseId(x.get(COURSE_INFO.ID, String.class));
                                        onlineStudyTotalTime.setCourseName(x.get(COURSE_INFO.NAME, String.class));
                                        onlineStudyTotalTime.setStudyDate(String.valueOf(x.get(table.field("f_day"), Integer.class)));
                                        // 空值处理
                                        Integer durationObj = x.get(table.field("f_study_time"), Integer.class);
                                        int duration = Objects.isNull(durationObj) ? 0 : durationObj.intValue();
                                        onlineStudyTotalTime.setStudyDuration(duration);
                                        //onlineStudyTotalTime.setInputExt(getFinishStatus(x.get(progressTable.field("f_finish_status"), Integer.class)));
                                        return onlineStudyTotalTime;
                                    }));
                }
                //记录总数
                int size = onlineStudyTotalTimeList == null ? 0 : onlineStudyTotalTimeList.size();
                log.info("导入非other分表员工在线学习时长,待处理总数据size={}", size);
                if (size > 0) {
                    // 是否还有下一页记录
                    if (size > pageSize) {
                        onlineStudyTotalTimeList.remove(size - 1);
                    }
                    OnlineStudyTotalTimeDTO onlineStudyTotalTimeDTO = new OnlineStudyTotalTimeDTO();
                    onlineStudyTotalTimeDTO.setCurrentPage(page);
                    onlineStudyTotalTimeDTO.setData(onlineStudyTotalTimeList);
                    onlineStudyTotalTimeDTO.setPageSize(pageSize);
                    //省公司编码
                    onlineStudyTotalTimeDTO.setProvinceCode(provinceCode);
                    //表示是数据来源为网大
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("source", "OU");
                    onlineStudyTotalTimeDTO.setInputExt(JSON.toJSONString(jsonObject));
                    // 封装业务数据
                    String busiParam = JSONObject.toJSONString(onlineStudyTotalTimeDTO);
                    PccwResult pccwResult = new PccwResult();
                    pccwResult.forInsert();
                    //默认失败处理linkId与主键相同
                    pccwResult.setLinkId(pccwResult.getId());
                    boolean linkIdIsBlank = StringUtils.isNotBlank(linkId);
                    if (linkIdIsBlank) {
                        pccwResult.setLinkId(linkId);
                    }
                    pccwResult.setUpdateTime(System.currentTimeMillis());
                    pccwResult.setMethod(method);
                    //处理状态码 0:失败 1:成功 2:失败记录本次处理失败,下次待处理
                    int status = linkIdIsBlank ? 2 : 0;
                    try {
                        // 调用外部服务接口
                        log.info("dealOnlineStudyTotalTime request param = {}", busiParam);
                        String responseStr = AIESBClient
                                .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
                        log.error("dealOnlineStudyTotalTime  response = {}", responseStr);
                        pccwResultDao.insert(dealResponse(pccwResult, responseStr, status));
                    } catch (Exception e) {
                        log.error("导入员工在线学习时长ERROR", e);
                        pccwResult.setStatus(status);
                        pccwResult.setInstanceId("-1");
                        pccwResult.setRespCode("-1");
                        pccwResult.setRespDesc("导入员工在线学习时长ERROR");
                        pccwResultDao.insert(pccwResult);
                        return false;
                    }
                }
                // 多页记录
                if (size > pageSize) {
                    page++;
                } else {
                    k = 0;
                    page = 1;
                }
            }
        }

        // 单独处理other中的公司数据
        TableImpl otherTable = COURSE_SECTION_STUDY_LOG_OTHER_DAY;
        Condition condition = Stream.of( userOpt.map(x-> otherTable.field("f_member_id", String.class).eq(userId))
        ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());
        List<Member> members = courseSectionStudyLogDayDao
                .execute(csl ->
                        csl.select(
                                MEMBER.NAME,
                                MEMBER.IHR_CODE,
                                MEMBER.FULL_NAME,
                                MEMBER.ORGANIZATION_ID
                        ).from(otherTable).leftJoin(COURSE_INFO)
                                .on(COURSE_INFO.ID.eq(otherTable.field("f_course_id")))
                                .leftJoin(MEMBER).on(MEMBER.ID.eq(otherTable.field("f_member_id")))
                                .where(otherTable.field("f_day", Integer.class).eq(date)))
                .and(condition)
                .fetchInto(Member.class);
        if (members != null && members.size() > 0) {
            //所有有时长的组织信息
            List<String> orgIds = members.stream().map(x -> x.getOrganizationId()).collect(Collectors.toList());
            Map<String, Organization> rootOrgMap = getCompanyOrganizeMap(orgIds);
            Set<String> rootOrgIds = rootOrgMap.keySet();
            int rootOrgSize = rootOrgIds.size();
            log.info("导入other分表员工在线学习时长,组织数量IdSize={}", rootOrgSize);
            if (rootOrgSize > 0) {
                for (String rootOrgId : rootOrgIds) {
                    Organization organization = rootOrgMap.get(rootOrgId);
                    String provinceCode = null;
                    // 返回新组织编码,没有返回旧组织编码
                    if (Objects.nonNull(organization)) {
//                        provinceCode = organization.getIhrCode();
//                        if (StringUtils.isBlank(provinceCode)) {
//                            provinceCode = organization.getCode();
//                        }
                        //获取人力发展系统组织映射编码
                        try {
                            provinceCode = getHrMappingCode(organization.getId());
                        } catch (Exception e) {
                            log.error("面授课程数据推送状态,org={},msg={}", organization.getId(), e.getMessage());
                            continue;
                        }
                    }
                    int page = 1;
                    int pageSize = 50;
                    //当k<1时退出循环
                    int k = 1;
                    while (k >= 1) {
                        final int currentPage = (page - 1) * pageSize;
                        // 获取该公司下所有用户的时长信息
                        List<OnlineStudyTotalTime> onlineStudyTotalTimeList = courseSectionStudyLogDayDao
                                .execute(csl ->
                                        csl.select(
                                                MEMBER.NAME,
                                                MEMBER.IHR_CODE,
                                                MEMBER.FULL_NAME,
                                                COURSE_INFO.ID,
                                                COURSE_INFO.NAME,
                                                otherTable.field("f_day", Integer.class),
                                                otherTable.field("f_study_time", Integer.class)
                                        ).from(otherTable).leftJoin(COURSE_INFO)
                                                .on(COURSE_INFO.ID.eq(otherTable.field("f_course_id")))
                                                .innerJoin(MEMBER).on(MEMBER.ID.eq(otherTable.field("f_member_id")))
                                                .innerJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                                                .where(otherTable.field("f_day", Integer.class).eq(date))
                                                .and(ORGANIZATION.COMPANY_ID.eq(rootOrgId)).and(condition)
                                                .limit(currentPage, pageSize + 1).fetch().map(x -> {
                                            OnlineStudyTotalTime onlineStudyTotalTime = new OnlineStudyTotalTime();
                                            // 新旧员工编号设置
                                            String ihrCode = x.get(MEMBER.IHR_CODE, String.class);
                                            if (StringUtils.isBlank(ihrCode)) {
                                                ihrCode = x.get(MEMBER.NAME, String.class);
                                            }
                                            onlineStudyTotalTime.setEmpNumber(ihrCode);
                                            onlineStudyTotalTime.setEmpName(x.get(MEMBER.FULL_NAME, String.class));
                                            onlineStudyTotalTime.setCourseId(x.get(COURSE_INFO.ID, String.class));
                                            onlineStudyTotalTime.setCourseName(x.get(COURSE_INFO.NAME, String.class));
                                            onlineStudyTotalTime.setStudyDate(String.valueOf(x.get(otherTable.field("f_day"), Integer.class)));
                                            // 空值处理
                                            Integer durationObj = x.get(otherTable.field("f_study_time"), Integer.class);
                                            int duration = Objects.isNull(durationObj) ? 0 : durationObj.intValue();
                                            onlineStudyTotalTime.setStudyDuration(duration);
                                            return onlineStudyTotalTime;
                                        }));

                        //记录总数
                        int size = onlineStudyTotalTimeList == null ? 0 : onlineStudyTotalTimeList.size();
                        log.info("导入other分表员工在线学习时长,待处理总数据size={}", size);
                        if (size > 0) {
                            // 是否还有下一页记录
                            if (size > pageSize) {
                                onlineStudyTotalTimeList.remove(size - 1);
                            }
                            OnlineStudyTotalTimeDTO onlineStudyTotalTimeDTO = new OnlineStudyTotalTimeDTO();
                            onlineStudyTotalTimeDTO.setCurrentPage(page);
                            onlineStudyTotalTimeDTO.setData(onlineStudyTotalTimeList);
                            onlineStudyTotalTimeDTO.setPageSize(pageSize);
                            //省公司编码
                            onlineStudyTotalTimeDTO.setProvinceCode(provinceCode);
                            //表示是数据来源为网大
                            JSONObject jsonObject = new JSONObject();
                            jsonObject.put("source", "OU");
                            onlineStudyTotalTimeDTO.setInputExt(JSON.toJSONString(jsonObject));
                            // 封装业务数据
                            String busiParam = JSONObject.toJSONString(onlineStudyTotalTimeDTO);
                            PccwResult pccwResult = new PccwResult();
                            pccwResult.forInsert();
                            //默认失败处理linkId与主键相同
                            pccwResult.setLinkId(pccwResult.getId());
                            boolean linkIdIsBlank = StringUtils.isNotBlank(linkId);
                            if (linkIdIsBlank) {
                                pccwResult.setLinkId(linkId);
                            }
                            pccwResult.setUpdateTime(System.currentTimeMillis());
                            pccwResult.setMethod(method);
                            //处理状态码 0:失败 1:成功 2:失败记录本次处理失败,下次待处理
                            int status = linkIdIsBlank ? 2 : 0;
                            try {
                                // 调用外部服务接口
                                log.info("dealOnlineStudyTotalTime other request param = {}", busiParam);
                                String responseStr = AIESBClient
                                        .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
                                log.error("dealOnlineStudyTotalTime other response = {}", responseStr);
                                pccwResultDao.insert(dealResponse(pccwResult, responseStr, status));
                            } catch (Exception e) {
                                log.error("导入员工在线学习时长ERROR", e);
                                pccwResult.setStatus(status);
                                pccwResult.setInstanceId("-1");
                                pccwResult.setRespCode("-1");
                                pccwResult.setRespDesc("导入员工在线学习时长ERROR");
                                pccwResultDao.insert(pccwResult);
                                return false;
                            }
                        }
                        // 多页记录
                        if (size > pageSize) {
                            page++;
                        } else {
                            k = 0;
                            page = 1;
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 处理接口响应.
     */
    private void dealResponse(String responseStr) {
        String success = "00000";
        if (StringUtils.isNotBlank(responseStr)) {
            JSONObject resObject = JSONObject.parseObject(responseStr);
            String respCode = resObject.getString("respCode");
            String respDesc = resObject.getString("respDesc");
            //成功
            if (Objects.equals(success, respCode)) {
                log.info("time = {}, result = {}", System.currentTimeMillis(), respCode + respDesc);
                return;
            }
            JSONObject result = resObject.getJSONObject("result");
            if (Objects.nonNull(result)) {
                String statusCode = result.getString("statusCode");
                String errReason = result.getString("errReason");
                int totalRecord = result.getIntValue("totalRecord");
                log.info("time = {}, statusCode = {}, errReason = {}, totalRecord = {}, result = 失败",
                        System.currentTimeMillis(), statusCode, errReason, totalRecord);
            }
        }
    }


    /**
     * 处理接口响应.
     */
    private PccwResult dealResponse(PccwResult pccwResult, String responseStr, int status) {
        String success = "00000";
        if (StringUtils.isNotBlank(responseStr)) {
            JSONObject jsonObject = JSONObject.parseObject(responseStr);
            String instanceId = jsonObject.getString("instanceId");
            String respCode = jsonObject.getString("respCode");
            String respDesc = jsonObject.getString("respDesc");
            pccwResult.setInstanceId(instanceId);
            pccwResult.setRespCode(respCode);
//            pccwResult.setRespDesc(respDesc);
            //成功
            if (Objects.equals(success, respCode)) {
                status = 1;
            }
            JSONObject result = jsonObject.getJSONObject("result");
            if (Objects.nonNull(result)) {
                String statusCode = result.getString("statusCode");
                String errReason = result.getString("errReason");
                int totalRecord = result.getIntValue("totalRecord");
                if ("1110".equals(statusCode) && 0 == totalRecord) {
                    errReason = "DataIntegrityViolationException";
                }
                // 只有0000的状态码才是调用接口正常的
                if (Objects.equals("0000", statusCode)) {
                    status = 1;
                } else {
                    status = 0;
                }
                pccwResult.setStatusCode(statusCode);
//                pccwResult.setErrReason(errReason);
                pccwResult.setTotalRecord(totalRecord);
            }
            pccwResult.setStatus(status);
            return pccwResult;
        }
        return pccwResult;
    }

    /**
     * 导入员工课程状态.
     *
     * @param sysParam 公共入参
     * @param start    开始时间
     * @param end      结束时间
     * @param method   请求接口类型
     * @param resultId   上次处理失败的记录ID(默认和当前主键相同)
     */
    private boolean dealImportCourseStudyProgress(Map<String, String> sysParam, String method, long start,
                                         long end, String resultId,List<String> userId,List<String> courseIds) {
        //接口请求路径
        String path = "/hrd/sync/train/studyStatus";

        // 如果处理的是失败记录，组织id以及要处理的数据从结果记录表中读取
        List<Organization> orgList = new ArrayList<>();
        List<String> progressIds = new ArrayList<>();

        boolean linkIdIsBlank = !ObjectUtils.isEmpty(resultId);
        if (linkIdIsBlank) {
            // 组织id
            PccwResult pccwResult = pccwResultDao.get(resultId);
            String orgId = pccwResult.getOutputExt();
            Organization organization = organizationDao.execute(s ->
                    s.select(ORGANIZATION.ID, ORGANIZATION.CODE, ORGANIZATION.NAME, ORGANIZATION.IHR_CODE).from(ORGANIZATION).where(ORGANIZATION.ID.eq(orgId))).fetchOptional(r -> {
                Organization or = new Organization();
                or.setId(r.getValue(ORGANIZATION.ID));
                or.setCode(r.getValue(ORGANIZATION.CODE));
                or.setName(r.getValue(ORGANIZATION.NAME));
                or.setIhrCode(r.getValue(ORGANIZATION.IHR_CODE));
                return or;
            }).orElse(null);
            if (organization != null) {
                orgList.add(organization);
            }
            // 查询需要重新同步的数据
            progressIds = businessCommonDao.execute(x -> x.select(PCCW_RESULT_BUSINESS.PROGRESS_ID).from(PCCW_RESULT_BUSINESS).where(PCCW_RESULT_BUSINESS.RESULT_ID.eq(pccwResult.getId()))).fetch(PCCW_RESULT_BUSINESS.PROGRESS_ID);
        } else {
            // 查询需要同步的省公司的组织id（根组织id“1”不进行处理
            orgList = configDao.execute(x -> x.select(ORGANIZATION.ID, ORGANIZATION.CODE, ORGANIZATION.NAME, ORGANIZATION.IHR_CODE).from(PCCW_ORGANIZATION_CONFIG)
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(PCCW_ORGANIZATION_CONFIG.ORGANIZATION_ID))
                    .where(PCCW_ORGANIZATION_CONFIG.STATE.eq(PccwOrganizationConfig.STATE_PUSHING))
                    .fetchInto(Organization.class));
        }


        if (orgList.isEmpty() && orgList.size() <= 0) {
            log.error("导入课程进度数据失败，需要同步的组织数据为空");
            return true;
        }
        log.info("待处理导入员工课程完成状态省公司信息,组织数量IdSize={}", orgList.size());
        // 考虑后续要开发全国的数据同步，将接口同步改为多线程
        List<String> finalProgressIds = progressIds;
        MultiThread<Organization, Boolean> multiThread = new MultiThread<Organization, Boolean>(orgList) {
            @Override
            public List<Boolean> outExecute(int currentThread, Organization rootOrg) {
                List<Boolean> result = new ArrayList<>();
                // 中国移动根组织的id不进行处理
                if ("1".equals(rootOrg.getId())) {
                    log.error("根组织的数据不同步");
                    result.add(true);
                    return result;
                }
                String provinceCode = null;
                try {
                    provinceCode = getProvinceCode(rootOrg);
                } catch (Exception e) {
                    log.error("导入课程完成状态异常：org={},msg={}", rootOrg.getId(), e.getMessage());
                    result.add(true);
                    return result;
                }
                int page = 1;
                int pageSize = 50;
                //当k<1时退出循环
                int k = 1;

                // 查询分表名
                String tableName = splitTableConfigDao.execute(x -> x.select(SPLIT_TABLE_CONFIG.TARGET_TABLE).from(SPLIT_TABLE_CONFIG)
                        .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.ROOT.eq(SPLIT_TABLE_CONFIG.ORGANIZATION_ID))
                        .where(ORGANIZATION_DETAIL.SUB.eq(rootOrg.getId()).and(SPLIT_TABLE_CONFIG.SOURCE.eq(SplitTableConfig.COURSE_STUDY_PROGRESS)))
                        .limit(1)
                        .fetchOptional(SPLIT_TABLE_CONFIG.TARGET_TABLE).orElse("t_course_study_progress_other")
                );
                TableImpl<?> table = SplitTableName.getTableNameByCode(tableName);
                // 组装条件
                Condition condition = COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE)
                        .and(table.field("f_last_access_time", Long.class).between(start, end))
                        .and(ORGANIZATION.PATH.like("1,10000001," + rootOrg.getId() + "%"));
                // 失败的历史数据重新同步
                if (linkIdIsBlank) {
                    condition = table.field("f_id", String.class).in(finalProgressIds);
                }
                if(!ObjectUtils.isEmpty(userId)){
                    condition = condition.and(table.field("f_member_id", String.class).in(userId));
                }
                if(!ObjectUtils.isEmpty(courseIds)){
                    condition = condition.and(table.field("f_course_id", String.class).in(courseIds));
                }
                while (k >= 1) {
                    final int currentPage = (page - 1) * pageSize;
                    Condition finalCondition = condition;
                    List<com.zxy.product.course.dto.CourseStudyProgress> progressList = progressDao.execute(x -> x.select(MEMBER.NAME, MEMBER.IHR_CODE, MEMBER.FULL_NAME,
                            COURSE_INFO.ID, COURSE_INFO.NAME,
                            table.field("f_study_total_time", Integer.class), table.field("f_finish_status", Integer.class),
                            table.field("f_last_access_time", Long.class), table.field("f_finish_time", Long.class) ,
                            table.field("f_id", String.class))
                            .from(table)
                            .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(table.field("f_course_id", String.class)))
                            .leftJoin(MEMBER).on(MEMBER.ID.eq(table.field("f_member_id", String.class)))
                            .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                            .where(finalCondition)
                            .orderBy(table.field("f_last_access_time", Integer.class))
                            .limit(currentPage, pageSize)
                            .fetch(r -> {
                                com.zxy.product.course.dto.CourseStudyProgress progress = new com.zxy.product.course.dto.CourseStudyProgress();
                                progress.setCourseId(r.getValue(COURSE_INFO.ID));
                                progress.setCourseName(r.getValue(COURSE_INFO.NAME));
                                progress.setEmpName(r.getValue(MEMBER.FULL_NAME));
                                String ihrCode = r.getValue(MEMBER.IHR_CODE);
                                if (StringUtils.isEmpty(ihrCode)) {
                                    ihrCode = r.getValue(MEMBER.NAME);
                                }
                                progress.setEmpNumber(ihrCode);
                                Integer finishStatus = r.getValue(table.field("f_finish_status"), Integer.class);
                                if (ObjectUtils.isEmpty(finishStatus)) {
                                    finishStatus = 0;
                                }
                                progress.setStudyStatus(getFinishStatus(finishStatus));
                                Integer studyTotalTime = r.getValue(table.field("f_study_total_time", Integer.class));
                                if (ObjectUtils.isEmpty(studyTotalTime)) {
                                    studyTotalTime = 0;
                                }
                                progress.setStudyTotalTime(studyTotalTime.toString());
                                String time = changeTimeStyle(r.getValue(table.field("f_last_access_time", Long.class)), "yyyyMMdd HHmmss");
                                progress.setStudyLastTime(time);
                                // 完成时间
                                Long finishTime = r.getValue(table.field("f_finish_time", Long.class));
                                if (ObjectUtils.isEmpty(finishTime)) {
                                    progress.setStudyCompletionTime("");
                                } else {
                                    progress.setStudyCompletionTime(changeTimeStyle(finishTime, "yyyyMMdd HHmmss"));
                                }
                                progress.setProgressId(r.getValue(table.field("f_id", String.class)));
                                return progress;
                            })
                    );

                    //记录总数
                    int size = progressList == null ? 0 : progressList.size();
                    log.info("导入员工课程完成状态数据,待处理总数据size={}", size);
                    if (size > 0) {
                        CourseStudyProgressDTO progressDTO = new CourseStudyProgressDTO();
                        progressDTO.setCurrentPage(page);
                        progressDTO.setData(progressList);
                        progressDTO.setPageSize(pageSize);
                        //省公司编码
                        progressDTO.setProvinceCode(provinceCode);
                        //表示是数据来源为网大
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("source", "OU");
                        // 封装业务数据
                        String busiParam = JSONObject.toJSONString(progressDTO);
                        // 处理错误数据
                        PccwResult pccwResult = pccwResultDao.fetchOne(PCCW_RESULT.ID.eq(resultId)).orElse(null);
                        if (pccwResult == null) {
                            pccwResult = new PccwResult();
                            pccwResult.forInsert();
                        }
                        //默认失败处理linkId与主键相同
                        pccwResult.setLinkId(pccwResult.getId());
                        pccwResult.setUpdateTime(System.currentTimeMillis());
                        pccwResult.setMethod(method);
                        //处理状态码 0:失败 1:成功 2:失败记录本次处理失败,下次待处理
                        int status = linkIdIsBlank ? 2 : 0;
                        try {
                            // 调用外部服务接口
                            log.info("courseStudyProgress request param = {}", busiParam);
                            String responseStr = AIESBClient
                                    .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
                            log.error("courseStudyProgress response = {}", responseStr);
                            PccwResult newResult = dealResponse(pccwResult, responseStr, status);

                            if (linkIdIsBlank) {
                                pccwResultDao.update(newResult);
                            } else {
                                pccwResultDao.insert(newResult);
                                if (newResult.getStatus() == 0) {
                                    insertResultBusiness(newResult.getId(), progressList.stream().map(p-> p.getProgressId()).collect(Collectors.toList()));
                                }
                            }
                            // 将处理失败的记录保存到
                        } catch (Exception e) {
                            log.error("导入课程完成状态服务ERROR", e);
                            pccwResult.setStatus(status);
                            pccwResult.setInstanceId("-1");
                            pccwResult.setRespCode("-1");
                            pccwResult.setRespDesc("导入课程完成状态服务ERROR");
                            if (linkIdIsBlank) {
                                pccwResultDao.update(pccwResult);
                            } else {
                                pccwResultDao.insert(pccwResult);
                                // 新增失败的记录
                                insertResultBusiness(pccwResult.getId(), progressList.stream().map(p-> p.getProgressId()).collect(Collectors.toList()));
                            }
                        }
                    }
                    // 多页记录
                    if (size >= pageSize) {
                        page++;
                    } else {
                        k = 0;
                        page = 1;
                    }
                }
                //需要再根据完成时间进行查询同步
                //当j<1时退出循环
                int j = 1;
                while (j >= 1) {
                    final int currentPage = (page - 1) * pageSize;
                    // 组装条件
                    Condition conditionFinish = COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE)
                            .and(table.field("f_finish_time", Long.class).between(start, end))
                            .and(ORGANIZATION.PATH.like("1,10000001," + rootOrg.getId() + "%"));
                    if(!ObjectUtils.isEmpty(userId)){
                        conditionFinish =conditionFinish.and(table.field("f_member_id", String.class).in(userId));
                    }
                    Condition finalConditionFinish = conditionFinish;
                    List<com.zxy.product.course.dto.CourseStudyProgress> progressList = progressDao.execute(x -> x.select(MEMBER.NAME, MEMBER.IHR_CODE, MEMBER.FULL_NAME,
                                    COURSE_INFO.ID, COURSE_INFO.NAME,
                                    table.field("f_study_total_time", Integer.class), table.field("f_finish_status", Integer.class),
                                    table.field("f_last_access_time", Long.class), table.field("f_finish_time", Long.class) ,
                                    table.field("f_id", String.class))
                            .from(table)
                            .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(table.field("f_course_id", String.class)))
                            .leftJoin(MEMBER).on(MEMBER.ID.eq(table.field("f_member_id", String.class)))
                            .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                            .where(finalConditionFinish)
                            .orderBy(table.field("f_last_access_time", Integer.class))
                            .limit(currentPage, pageSize)
                            .fetch(r -> {
                                com.zxy.product.course.dto.CourseStudyProgress progress = new com.zxy.product.course.dto.CourseStudyProgress();
                                progress.setCourseId(r.getValue(COURSE_INFO.ID));
                                progress.setCourseName(r.getValue(COURSE_INFO.NAME));
                                progress.setEmpName(r.getValue(MEMBER.FULL_NAME));
                                String ihrCode = r.getValue(MEMBER.IHR_CODE);
                                if (StringUtils.isEmpty(ihrCode)) {
                                    ihrCode = r.getValue(MEMBER.NAME);
                                }
                                progress.setEmpNumber(ihrCode);
                                Integer finishStatus = r.getValue(table.field("f_finish_status"), Integer.class);
                                if (ObjectUtils.isEmpty(finishStatus)) {
                                    finishStatus = 0;
                                }
                                progress.setStudyStatus(getFinishStatus(finishStatus));
                                Integer studyTotalTime = r.getValue(table.field("f_study_total_time", Integer.class));
                                if (ObjectUtils.isEmpty(studyTotalTime)) {
                                    studyTotalTime = 0;
                                }
                                progress.setStudyTotalTime(studyTotalTime.toString());
                                String time = changeTimeStyle(r.getValue(table.field("f_last_access_time", Long.class)), "yyyyMMdd HHmmss");
                                progress.setStudyLastTime(time);
                                // 完成时间
                                Long finishTime = r.getValue(table.field("f_finish_time", Long.class));
                                if (ObjectUtils.isEmpty(finishTime)) {
                                    progress.setStudyCompletionTime("");
                                } else {
                                    progress.setStudyCompletionTime(changeTimeStyle(finishTime, "yyyyMMdd HHmmss"));
                                }
                                progress.setProgressId(r.getValue(table.field("f_id", String.class)));
                                return progress;
                            })
                    );

                    //记录总数
                    int size = progressList == null ? 0 : progressList.size();
                    log.info("导入员工课程完成状态数据,待处理总数据size={}", size);
                    if (size > 0) {
                        CourseStudyProgressDTO progressDTO = new CourseStudyProgressDTO();
                        progressDTO.setCurrentPage(page);
                        progressDTO.setData(progressList);
                        progressDTO.setPageSize(pageSize);
                        //省公司编码
                        progressDTO.setProvinceCode(provinceCode);
                        //表示是数据来源为网大
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("source", "OU");
                        // 封装业务数据
                        String busiParam = JSONObject.toJSONString(progressDTO);
                        // 处理错误数据
                        PccwResult pccwResult = pccwResultDao.fetchOne(PCCW_RESULT.ID.eq(resultId)).orElse(null);
                        if (pccwResult == null) {
                            pccwResult = new PccwResult();
                            pccwResult.forInsert();
                        }
                        //默认失败处理linkId与主键相同
                        pccwResult.setLinkId(pccwResult.getId());
                        pccwResult.setUpdateTime(System.currentTimeMillis());
                        pccwResult.setMethod(method);
                        //处理状态码 0:失败 1:成功 2:失败记录本次处理失败,下次待处理
                        int status = linkIdIsBlank ? 2 : 0;
                        try {
                            // 调用外部服务接口
                            log.info("courseStudyProgress request param = {}", busiParam);
                            String responseStr = AIESBClient
                                    .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
                            log.error("courseStudyProgress response = {}", responseStr);
                            PccwResult newResult = dealResponse(pccwResult, responseStr, status);

                            if (linkIdIsBlank) {
                                pccwResultDao.update(newResult);
                            } else {
                                pccwResultDao.insert(newResult);
                                if (newResult.getStatus() == 0) {
                                    insertResultBusiness(newResult.getId(), progressList.stream().map(p-> p.getProgressId()).collect(Collectors.toList()));
                                }
                            }
                            // 将处理失败的记录保存到
                        } catch (Exception e) {
                            log.error("导入课程完成状态服务ERROR", e);
                            pccwResult.setStatus(status);
                            pccwResult.setInstanceId("-1");
                            pccwResult.setRespCode("-1");
                            pccwResult.setRespDesc("导入课程完成状态服务ERROR");
                            if (linkIdIsBlank) {
                                pccwResultDao.update(pccwResult);
                            } else {
                                pccwResultDao.insert(pccwResult);
                                // 新增失败的记录
                                insertResultBusiness(pccwResult.getId(), progressList.stream().map(p-> p.getProgressId()).collect(Collectors.toList()));
                            }
                        }
                    }
                    // 多页记录
                    if (size >= pageSize) {
                        page++;
                    } else {
                        j = 0;
                        page = 1;
                    }
                }

                result.add(true);
                return result;
            }
        };
        try {
            // 同步数据
            multiThread.getResult();
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (ExecutionException e) {
            e.printStackTrace();
        }
        return true;
    }

    private void insertResultBusiness(String resultId, List<String> progressIds) {
        List<PccwResultBusiness> resultBusinessList = new ArrayList<>();
        progressIds.forEach(id -> {
            PccwResultBusiness business = new PccwResultBusiness();
            business.forInsert();
            business.setProgressId(id);
            business.setResultId(resultId);
            resultBusinessList.add(business);
        });
        businessCommonDao.insert(resultBusinessList);
    };

    /**
     * 分表信息.
     */
    private TableImpl<?> getTable(String code) {
        switch (code) {
            case "t_course_section_study_log_ah_day":
                return COURSE_SECTION_STUDY_LOG_AH_DAY;
            case "t_course_section_study_log_bj_day":
                return COURSE_SECTION_STUDY_LOG_BJ_DAY;
            case "t_course_section_study_log_cm_day":
                return COURSE_SECTION_STUDY_LOG_CM_DAY;
            case "t_course_section_study_log_cq_day":
                return COURSE_SECTION_STUDY_LOG_CQ_DAY;
            case "t_course_section_study_log_eb_day":
                return COURSE_SECTION_STUDY_LOG_EB_DAY;
            case "t_course_section_study_log_fj_day":
                return COURSE_SECTION_STUDY_LOG_FJ_DAY;
            case "t_course_section_study_log_gd_day":
                return COURSE_SECTION_STUDY_LOG_GD_DAY;
            case "t_course_section_study_log_gs_day":
                return COURSE_SECTION_STUDY_LOG_GS_DAY;
            case "t_course_section_study_log_gx_day":
                return COURSE_SECTION_STUDY_LOG_GX_DAY;
            case "t_course_section_study_log_gz_day":
                return COURSE_SECTION_STUDY_LOG_GZ_DAY;
            case "t_course_section_study_log_hb_day":
                return COURSE_SECTION_STUDY_LOG_HB_DAY;
            case "t_course_section_study_log_hl_day":
                return COURSE_SECTION_STUDY_LOG_HL_DAY;
            case "t_course_section_study_log_hn_day":
                return COURSE_SECTION_STUDY_LOG_HN_DAY;
            case "t_course_section_study_log_jl_day":
                return COURSE_SECTION_STUDY_LOG_JL_DAY;
            case "t_course_section_study_log_js_day":
                return COURSE_SECTION_STUDY_LOG_JS_DAY;
            case "t_course_section_study_log_jx_day":
                return COURSE_SECTION_STUDY_LOG_JX_DAY;
            case "t_course_section_study_log_ln_day":
                return COURSE_SECTION_STUDY_LOG_LN_DAY;
            case "t_course_section_study_log_nm_day":
                return COURSE_SECTION_STUDY_LOG_NM_DAY;
            case "t_course_section_study_log_nx_day":
                return COURSE_SECTION_STUDY_LOG_NX_DAY;
            case "t_course_section_study_log_qh_day":
                return COURSE_SECTION_STUDY_LOG_QH_DAY;
            case "t_course_section_study_log_qo_day":
                return COURSE_SECTION_STUDY_LOG_QO_DAY;
            case "t_course_section_study_log_sc_day":
                return COURSE_SECTION_STUDY_LOG_SC_DAY;
            case "t_course_section_study_log_sd_day":
                return COURSE_SECTION_STUDY_LOG_SD_DAY;
            case "t_course_section_study_log_sh_day":
                return COURSE_SECTION_STUDY_LOG_SH_DAY;
            case "t_course_section_study_log_sn_day":
                return COURSE_SECTION_STUDY_LOG_SN_DAY;
            case "t_course_section_study_log_sx_day":
                return COURSE_SECTION_STUDY_LOG_SX_DAY;
            case "t_course_section_study_log_tj_day":
                return COURSE_SECTION_STUDY_LOG_TJ_DAY;
            case "t_course_section_study_log_xj_day":
                return COURSE_SECTION_STUDY_LOG_XJ_DAY;
            case "t_course_section_study_log_xn_day":
                return COURSE_SECTION_STUDY_LOG_XN_DAY;
            case "t_course_section_study_log_xz_day":
                return COURSE_SECTION_STUDY_LOG_XZ_DAY;
            case "t_course_section_study_log_yn_day":
                return COURSE_SECTION_STUDY_LOG_YN_DAY;
            case "t_course_section_study_log_zgtt_day":
                return COURSE_SECTION_STUDY_LOG_ZGTT_DAY;
            case "t_course_section_study_log_zj_day":
                return COURSE_SECTION_STUDY_LOG_ZJ_DAY;
            case "t_course_section_study_log_zx_day":
                return COURSE_SECTION_STUDY_LOG_ZX_DAY;
            default:
                return null;
        }
    }

    private TableImpl<?> getProgressTable(String targetTable) {
        targetTable = targetTable.replace("t_course_section_study_log_", "");
        String code = targetTable.replace("_day", "");
        switch (code) {
            case "ah":
                return COURSE_STUDY_PROGRESS_AH;
            case "bj":
                return COURSE_STUDY_PROGRESS_BJ;
            case "cm":
                return COURSE_STUDY_PROGRESS_CM;
            case "cq":
                return COURSE_STUDY_PROGRESS_CQ;
            case "eb":
                return COURSE_STUDY_PROGRESS_EB;
            case "fj":
                return COURSE_STUDY_PROGRESS_FJ;
            case "gd":
                return COURSE_STUDY_PROGRESS_GD;
            case "gs":
                return COURSE_STUDY_PROGRESS_GS;
            case "gx":
                return COURSE_STUDY_PROGRESS_GX;
            case "gz":
                return COURSE_STUDY_PROGRESS_GZ;
            case "hb":
                return COURSE_STUDY_PROGRESS_HB;
            case "hl":
                return COURSE_STUDY_PROGRESS_HL;
            case "hn":
                return COURSE_STUDY_PROGRESS_HN;
            case "jl":
                return COURSE_STUDY_PROGRESS_JL;
            case "js":
                return COURSE_STUDY_PROGRESS_JS;
            case "jx":
                return COURSE_STUDY_PROGRESS_JX;
            case "ln":
                return COURSE_STUDY_PROGRESS_LN;
            case "nm":
                return COURSE_STUDY_PROGRESS_NM;
            case "nx":
                return COURSE_STUDY_PROGRESS_NX;
            case "other":
                return COURSE_STUDY_PROGRESS_OTHER;
            case "qh":
                return COURSE_STUDY_PROGRESS_QH;
            case "qo":
                return COURSE_STUDY_PROGRESS_QO;
            case "sc":
                return COURSE_STUDY_PROGRESS_SC;
            case "sd":
                return COURSE_STUDY_PROGRESS_SD;
            case "sh":
                return COURSE_STUDY_PROGRESS_SH;
            case "sn":
                return COURSE_STUDY_PROGRESS_SN;
            case "sx":
                return COURSE_STUDY_PROGRESS_SX;
            case "tj":
                return COURSE_STUDY_PROGRESS_TJ;
            case "xj":
                return COURSE_STUDY_PROGRESS_XJ;
            case "xn":
                return COURSE_STUDY_PROGRESS_XN;
            case "xz":
                return COURSE_STUDY_PROGRESS_XZ;
            case "yn":
                return COURSE_STUDY_PROGRESS_YN;
            case "zgtt":
                return COURSE_STUDY_PROGRESS_ZGTT;
            case "zj":
                return COURSE_STUDY_PROGRESS_ZJ;
            case "zx":
                return COURSE_STUDY_PROGRESS_ZX;
            default:
                return null;
        }
    }

    private String getFinishStatus(Integer finishStatus) {
        if (finishStatus == null) {
            return null;
        }
        switch (finishStatus) {
            case 0:
                return "未开始";
            case 1:
                return "学习中";
            case 2:
                return "已完成";
            case 3:
                return "已放弃";
            case 4:
                return "标记完成";
            default:
                return "";
        }
    }

    //获取人力发展系统组织映射编码
    private String getHrMappingCode(String orgId) throws Exception {
        Optional<OrganizationHrMapping> hrMappingOptional = organizationHrMappingService.getOptional(orgId);
        String provinceCode = null;
        if (hrMappingOptional.isPresent()) {
            provinceCode = hrMappingOptional.get().getHrProvinceCode();
        } else {
            throw new Exception("当前机构没有人才发展系统省份编码或者数据推送权限为不可推送状态");
        }
        return provinceCode;
    }

    private String changeTimeStyle(Long time, String partten) {
        SimpleDateFormat sdf = new SimpleDateFormat(partten);
        return sdf.format(new Date(time));
    }

    /**
     * 导入员工在线学习时长.
     *
     * @param sysParam 公共入参
     * @param method   请求接口类型
     */
    private void dealOnlineStudyTotalTime(Map<String, String> sysParam, String method) {
        //  根据每个人的数据处理
        List<String> memberIds = null;
        do {
            JSONObject jsonParamObject = new JSONObject();
            String sql = "select f_member_id,group_concat(f_business_id) as ids from t_ihr_manual_processing group by f_member_id limit 500;";
            memberIds = splitTableConfigDao.execute(dsl -> dsl.resultQuery(sql).fetch(record -> {
                String memberId = record.getValue("f_member_id", String.class);
                String ids = record.getValue("ids", String.class);
                if (Objects.nonNull(ids)) {
                    List<String> list = Arrays.asList(ids.split(","));
                    jsonParamObject.put(memberId, JSONArray.toJSONString(list));
                }
                return memberId;
            }));
            if (Objects.isNull(memberIds) || memberIds.size() == 0) {
                return;
            }
            Set<String> userIds = jsonParamObject.keySet();
            String deleteSql = "delete from t_ihr_manual_processing where f_member_id in(#{memberIds});";
            StringJoiner ids = new StringJoiner("','", "'", "'");
            userIds.forEach(ids::add);
            deleteSql = SQLUtil.buildSql(deleteSql, ImmutableMap.of("memberIds", ids.toString()));

            //接口路径
            String path = "/hrd/sync/train/onlineStudyDuration";
            // 待处理分表
            List<SplitTableConfig> splitTables = splitTableConfigDao.fetch(SPLIT_TABLE_CONFIG.SOURCE.eq(4));
            // 除other外其他省公司数据
            for (SplitTableConfig config : splitTables) {
                String targetTable = config.getTargetTable();
                //省公司ID
                String orgCode = config.getOrganizationId();
                String provinceCode = null;

                // 获取人力发展系统组织映射编码
                try {
                    provinceCode = getHrMappingCode(orgCode);
                } catch (Exception e) {
                    log.error("导入员工在线学习时长状态,org={},msg={}", orgCode, e.getMessage());
                    continue;
                }
                TableImpl table = getTable(targetTable);

                // 跳过other
                if (table == null) {
                    continue;
                }
                log.info("导入员工在线学习时长处理表名：" + table.getName());

                int page = 1;
                int pageSize = 50;
                //当k<1时退出循环
                int k = 1;
                Condition condition = userIds.stream().map(x ->
                        table.field("f_member_id", String.class).eq(x)
                                .and(table.field("f_course_id", String.class).in(jsonParamObject.getJSONArray(x).toArray(new String[0]))
                                )).reduce((acc, item) -> acc.or(item)).orElse(DSL.trueCondition());

                while (k >= 1) {
                    final int currentPage = (page - 1) * pageSize;
                    //先分页查出section_study_log_day的主键ID，然后根据ID查询相应的关联表字段
                    List<String> logDayIdList = courseSectionStudyLogDayDao
                            .execute(csl ->
                                    csl.select(
                                                    table.field("f_id", String.class)
                                            )
                                            .from(table)
                                            .where(DSL.trueCondition())
                                            .and(condition)
                                            .limit(currentPage, pageSize + 1).fetch().map(x -> {
                                                return String.valueOf(x.get(table.field("f_id"), String.class));
                                            }));

                    List<OnlineStudyTotalTime> onlineStudyTotalTimeList = null;
                    if (logDayIdList != null && logDayIdList.size() > 0) {
                        onlineStudyTotalTimeList = courseSectionStudyLogDayDao
                                .execute(csl ->
                                        csl.select(
                                                        MEMBER.NAME,
                                                        MEMBER.IHR_CODE,
                                                        MEMBER.FULL_NAME,
                                                        COURSE_INFO.ID,
                                                        COURSE_INFO.NAME,
                                                        table.field("f_day", Integer.class),
                                                        table.field("f_study_time", Integer.class)
                                                        //progressTable.field("f_finish_status", Integer.class)
                                                )
                                                .from(table)
                                                .leftJoin(COURSE_INFO)
                                                .on(COURSE_INFO.ID.eq(table.field("f_course_id")))
                                                .leftJoin(MEMBER).on(MEMBER.ID.eq(table.field("f_member_id")))
                                                .where(table.field("f_id", String.class).in(logDayIdList))
                                                .fetch().map(x -> {
                                                    OnlineStudyTotalTime onlineStudyTotalTime = new OnlineStudyTotalTime();
                                                    // 新旧员工编号设置
                                                    String ihrCode = x.get(MEMBER.IHR_CODE, String.class);
                                                    if (StringUtils.isBlank(ihrCode)) {
                                                        ihrCode = x.get(MEMBER.NAME, String.class);
                                                    }
                                                    onlineStudyTotalTime.setEmpNumber(ihrCode);
                                                    onlineStudyTotalTime.setEmpName(x.get(MEMBER.FULL_NAME, String.class));
                                                    onlineStudyTotalTime.setCourseId(x.get(COURSE_INFO.ID, String.class));
                                                    onlineStudyTotalTime.setCourseName(x.get(COURSE_INFO.NAME, String.class));
                                                    onlineStudyTotalTime.setStudyDate(String.valueOf(x.get(table.field("f_day"), Integer.class)));
                                                    // 空值处理
                                                    Integer durationObj = x.get(table.field("f_study_time"), Integer.class);
                                                    int duration = Objects.isNull(durationObj) ? 0 : durationObj.intValue();
                                                    onlineStudyTotalTime.setStudyDuration(duration);
                                                    //onlineStudyTotalTime.setInputExt(getFinishStatus(x.get(progressTable.field("f_finish_status"), Integer.class)));
                                                    return onlineStudyTotalTime;
                                                }));
                    }
                    //记录总数
                    int size = onlineStudyTotalTimeList == null ? 0 : onlineStudyTotalTimeList.size();
                    log.info("导入非other分表员工在线学习时长,待处理总数据size={}", size);
                    if (size > 0) {
                        // 是否还有下一页记录
                        if (size > pageSize) {
                            onlineStudyTotalTimeList.remove(size - 1);
                        }
                        OnlineStudyTotalTimeDTO onlineStudyTotalTimeDTO = new OnlineStudyTotalTimeDTO();
                        onlineStudyTotalTimeDTO.setCurrentPage(page);
                        onlineStudyTotalTimeDTO.setData(onlineStudyTotalTimeList);
                        onlineStudyTotalTimeDTO.setPageSize(pageSize);
                        //省公司编码
                        onlineStudyTotalTimeDTO.setProvinceCode(provinceCode);
                        //表示是数据来源为网大
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("source", "OU");
                        onlineStudyTotalTimeDTO.setInputExt(JSON.toJSONString(jsonObject));
                        // 封装业务数据
                        String busiParam = JSONObject.toJSONString(onlineStudyTotalTimeDTO);
                        PccwResult pccwResult = new PccwResult();
                        pccwResult.forInsert();
                        //默认失败处理linkId与主键相同
                        pccwResult.setLinkId(pccwResult.getId());
                        pccwResult.setUpdateTime(System.currentTimeMillis());
                        pccwResult.setMethod(method);
                        //处理状态码 0:失败 1:成功 2:失败记录本次处理失败,下次待处理
                        int status = 0;
                        try {
                            // 调用外部服务接口
                            log.info("dealOnlineStudyTotalTime request param = {}", busiParam);
                            String responseStr = AIESBClient
                                    .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
                            log.error("dealOnlineStudyTotalTime  response = {}", responseStr);
                            pccwResultDao.insert(dealResponse(pccwResult, responseStr, status));
                        } catch (Exception e) {
                            log.error("导入员工在线学习时长ERROR", e);
                            pccwResult.setStatus(status);
                            pccwResult.setInstanceId("-1");
                            pccwResult.setRespCode("-1");
                            pccwResult.setRespDesc("导入员工在线学习时长ERROR");
                            pccwResultDao.insert(pccwResult);
                            return;
                        }
                    }
                    // 多页记录
                    if (size > pageSize) {
                        page++;
                    } else {
                        k = 0;
                        page = 1;
                    }
                }
            }

            // 单独处理other中的公司数据
            TableImpl otherTable = COURSE_SECTION_STUDY_LOG_OTHER_DAY;
            Condition condition = userIds.stream().map(x ->
                    otherTable.field("f_member_id", String.class).eq(x)
                            .and(otherTable.field("f_course_id", String.class).in(jsonParamObject.getJSONArray(x).toArray(new String[0]))
                            )).reduce((acc, item) -> acc.or(item)).orElse(DSL.trueCondition());
            List<Member> members = courseSectionStudyLogDayDao
                    .execute(csl ->
                            csl.select(
                                            MEMBER.NAME,
                                            MEMBER.IHR_CODE,
                                            MEMBER.FULL_NAME,
                                            MEMBER.ORGANIZATION_ID
                                    ).from(otherTable).leftJoin(COURSE_INFO)
                                    .on(COURSE_INFO.ID.eq(otherTable.field("f_course_id")))
                                    .leftJoin(MEMBER).on(MEMBER.ID.eq(otherTable.field("f_member_id")))
                                    .where(DSL.trueCondition()))
                    .and(condition)
                    .fetchInto(Member.class);
            if (members != null && members.size() > 0) {
                //所有有时长的组织信息
                List<String> orgIds = members.stream().map(MemberEntity::getOrganizationId).collect(Collectors.toList());
                Map<String, Organization> rootOrgMap = getCompanyOrganizeMap(orgIds);
                Set<String> rootOrgIds = rootOrgMap.keySet();
                int rootOrgSize = rootOrgIds.size();
                log.info("导入other分表员工在线学习时长,组织数量IdSize={}", rootOrgSize);
                if (rootOrgSize > 0) {
                    for (String rootOrgId : rootOrgIds) {
                        Organization organization = rootOrgMap.get(rootOrgId);
                        String provinceCode = null;
                        // 返回新组织编码,没有返回旧组织编码
                        if (Objects.nonNull(organization)) {
//                        provinceCode = organization.getIhrCode();
//                        if (StringUtils.isBlank(provinceCode)) {
//                            provinceCode = organization.getCode();
//                        }
                            //获取人力发展系统组织映射编码
                            try {
                                provinceCode = getHrMappingCode(organization.getId());
                            } catch (Exception e) {
                                log.error("面授课程数据推送状态,org={},msg={}", organization.getId(), e.getMessage());
                                continue;
                            }
                        }
                        int page = 1;
                        int pageSize = 50;
                        //当k<1时退出循环
                        int k = 1;
                        while (k >= 1) {
                            final int currentPage = (page - 1) * pageSize;
                            // 获取该公司下所有用户的时长信息
                            List<OnlineStudyTotalTime> onlineStudyTotalTimeList = courseSectionStudyLogDayDao
                                    .execute(csl ->
                                            csl.select(
                                                            MEMBER.NAME,
                                                            MEMBER.IHR_CODE,
                                                            MEMBER.FULL_NAME,
                                                            COURSE_INFO.ID,
                                                            COURSE_INFO.NAME,
                                                            otherTable.field("f_day", Integer.class),
                                                            otherTable.field("f_study_time", Integer.class)
                                                    ).from(otherTable).leftJoin(COURSE_INFO)
                                                    .on(COURSE_INFO.ID.eq(otherTable.field("f_course_id")))
                                                    .innerJoin(MEMBER).on(MEMBER.ID.eq(otherTable.field("f_member_id")))
                                                    .innerJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                                                    .where(DSL.trueCondition())
                                                    .and(ORGANIZATION.COMPANY_ID.eq(rootOrgId)).and(condition)
                                                    .limit(currentPage, pageSize + 1).fetch().map(x -> {
                                                        OnlineStudyTotalTime onlineStudyTotalTime = new OnlineStudyTotalTime();
                                                        // 新旧员工编号设置
                                                        String ihrCode = x.get(MEMBER.IHR_CODE, String.class);
                                                        if (StringUtils.isBlank(ihrCode)) {
                                                            ihrCode = x.get(MEMBER.NAME, String.class);
                                                        }
                                                        onlineStudyTotalTime.setEmpNumber(ihrCode);
                                                        onlineStudyTotalTime.setEmpName(x.get(MEMBER.FULL_NAME, String.class));
                                                        onlineStudyTotalTime.setCourseId(x.get(COURSE_INFO.ID, String.class));
                                                        onlineStudyTotalTime.setCourseName(x.get(COURSE_INFO.NAME, String.class));
                                                        onlineStudyTotalTime.setStudyDate(String.valueOf(x.get(otherTable.field("f_day"), Integer.class)));
                                                        // 空值处理
                                                        Integer durationObj = x.get(otherTable.field("f_study_time"), Integer.class);
                                                        int duration = Objects.isNull(durationObj) ? 0 : durationObj.intValue();
                                                        onlineStudyTotalTime.setStudyDuration(duration);
                                                        return onlineStudyTotalTime;
                                                    }));

                            //记录总数
                            int size = onlineStudyTotalTimeList == null ? 0 : onlineStudyTotalTimeList.size();
                            log.info("导入other分表员工在线学习时长,待处理总数据size={}", size);
                            if (size > 0) {
                                // 是否还有下一页记录
                                if (size > pageSize) {
                                    onlineStudyTotalTimeList.remove(size - 1);
                                }
                                OnlineStudyTotalTimeDTO onlineStudyTotalTimeDTO = new OnlineStudyTotalTimeDTO();
                                onlineStudyTotalTimeDTO.setCurrentPage(page);
                                onlineStudyTotalTimeDTO.setData(onlineStudyTotalTimeList);
                                onlineStudyTotalTimeDTO.setPageSize(pageSize);
                                //省公司编码
                                onlineStudyTotalTimeDTO.setProvinceCode(provinceCode);
                                //表示是数据来源为网大
                                JSONObject jsonObject = new JSONObject();
                                jsonObject.put("source", "OU");
                                onlineStudyTotalTimeDTO.setInputExt(JSON.toJSONString(jsonObject));
                                // 封装业务数据
                                String busiParam = JSONObject.toJSONString(onlineStudyTotalTimeDTO);
                                PccwResult pccwResult = new PccwResult();
                                pccwResult.forInsert();
                                //默认失败处理linkId与主键相同
                                pccwResult.setLinkId(pccwResult.getId());
                                StringUtils.isNotBlank(null);
                                boolean linkIdIsBlank = false;
                                pccwResult.setUpdateTime(System.currentTimeMillis());
                                pccwResult.setMethod(method);
                                //处理状态码 0:失败 1:成功 2:失败记录本次处理失败,下次待处理
                                int status = 0;
                                try {
                                    // 调用外部服务接口
                                    log.info("dealOnlineStudyTotalTime other request param = {}", busiParam);
                                    String responseStr = AIESBClient
                                            .rest(sysParam, busiParam, path, AIESBConstants.METHOD.POST);
                                    log.error("dealOnlineStudyTotalTime other response = {}", responseStr);
                                    pccwResultDao.insert(dealResponse(pccwResult, responseStr, status));
                                } catch (Exception e) {
                                    log.error("导入员工在线学习时长ERROR", e);
                                    pccwResult.setStatus(status);
                                    pccwResult.setInstanceId("-1");
                                    pccwResult.setRespCode("-1");
                                    pccwResult.setRespDesc("导入员工在线学习时长ERROR");
                                    pccwResultDao.insert(pccwResult);
                                    return;
                                }
                            }
                            // 多页记录
                            if (size > pageSize) {
                                page++;
                            } else {
                                k = 0;
                                page = 1;
                            }
                        }
                    }
                }
            }
            String finalDeleteSql = deleteSql;
            splitTableConfigDao.execute(dsl -> dsl.execute(finalDeleteSql)); // 删除已经处理的数据列表
        }
        while (memberIds.size() > 0);
    }

}
