package com.zxy.product.course.async.task.mentor;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.async.helper.CourseKnowledgeHelper;
import com.zxy.product.course.dto.model.mentor.FileStatusDto;
import com.zxy.product.course.entity.CourseKnowledge;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/16:34
 * @Description:
 */
@Component
@EnableAsync
public class UpdateStatusTask {

    private static Logger logger = LoggerFactory.getLogger(UpdateStatusTask.class);
    private static Integer LIMIT = 200;
    private CommonDao<CourseKnowledge> courseKnowledgeCommonDao;
    private CourseKnowledgeHelper courseKnowledgeHelper;

    @Autowired
    public void setCourseKnowledgeHelper(CourseKnowledgeHelper courseKnowledgeHelper) {
        this.courseKnowledgeHelper = courseKnowledgeHelper;
    }

    @Autowired
    public void setCourseKnowledgeCommonDao(CommonDao<CourseKnowledge> courseKnowledgeCommonDao) {
        this.courseKnowledgeCommonDao = courseKnowledgeCommonDao;
    }

    private List<String> getIdByThirdId(){
        return courseKnowledgeCommonDao.execute(e -> e.select(COURSE_KNOWLEDGE.ATTACHMENT_BATCH).from(COURSE_KNOWLEDGE)
                .where(COURSE_KNOWLEDGE.FINSH_STATUS.eq(CourseKnowledge.FEEDBACK_STATUS_UNPUBLISH), COURSE_KNOWLEDGE.STATUS.eq(CourseKnowledge.STATUS_PUBLISH),
                        COURSE_KNOWLEDGE.DELETE.eq(CourseKnowledge.DELETE_NO))
                .limit(0, LIMIT)).fetch(COURSE_KNOWLEDGE.ATTACHMENT_BATCH);

    }

    private void batchUpdate(Set<String> thirdIds){
        logger.info("thirdIds = {}", thirdIds);
        courseKnowledgeCommonDao.execute(e->e.update(COURSE_KNOWLEDGE).set(COURSE_KNOWLEDGE.FINSH_STATUS, CourseKnowledge.FEEDBACK_STATUS_PUBLISH)
                .where(COURSE_KNOWLEDGE.ATTACHMENT_UP_ID.in(thirdIds)).execute());

    }
    @Async
    @Scheduled(cron = "0 0/30 * * * ?")
    public void synchronizeNote(){
        logger.info("查询文件向量化状态");
        List<String> batch = new ArrayList<>();
        do {
            batch = getIdByThirdId();
            List<FileStatusDto> fileStatus = new ArrayList<>();
            batch.forEach(item->{
                List<FileStatusDto> status = courseKnowledgeHelper.getFileStatus(item);
                logger.info("status={}", status);
                if(!Objects.isNull(status)){
                    //过滤已完成的向量对象
                    List<FileStatusDto> statusDtos = status.stream().filter(r -> Objects.equals(r.getIndexing_status(), CourseKnowledge.INDEXING_STATUS_COMPLETED)).collect(Collectors.toList());
                    fileStatus.addAll(statusDtos);
                }
            });
            logger.info("fileStatus={}", fileStatus);
            batchUpdate(fileStatus.stream().map(FileStatusDto::getId).collect(Collectors.toSet()));
        }while (Objects.equals(batch.size(), LIMIT));

    }
    
    
}
