package com.zxy.product.course.async.helper;

import com.alibaba.fastjson.JSON;
import com.zxy.product.course.dto.model.mentor.AiResponse;
import com.zxy.product.course.dto.model.mentor.FileStatusDto;
import com.zxy.product.course.util.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/14:13
 * @Description:
 */
@Component
public class CourseKnowledgeHelper {

    @Value("${course.model.url}")
    private String url;
    @Value("${course.model.dataset-id}")
    private String datasetId;
    @Value("${course.model.app-api-key}")
    private String appApiKey;
    @Value("${course.model.dataset-api-key}")
    private String datasetApiKey;
    @Value("${course.model.interface.indexing-status}")
    private String getFileStatus;


    private static Logger logger = LoggerFactory.getLogger(CourseKnowledgeHelper.class);


    public List<FileStatusDto> getFileStatus(String batch){
        HashMap<String, String> heard = new HashMap();
        List<FileStatusDto> data = new ArrayList<>();
        heard.put("Authorization", datasetApiKey);
        String response = HttpClientUtil.httpGetCustomize(url + String.format(getFileStatus,datasetId, batch), heard, null, HttpClientUtil.RequestConfigType.TIMEOUT_10000);
        logger.info("response={}",response);
        if(Objects.nonNull(response)){
            AiResponse aiResponse = JSON.parseObject(response, AiResponse.class);
            data = aiResponse.getData();
        } else {
            logger.warn("上传文件错误：错误 response = {}", response);
        }
        return data;
    }

}
