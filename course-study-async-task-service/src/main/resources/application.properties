spring.application.name=course-study-async-task-service
application.env.name=dev9
logging.level.root=INFO
server.port=8980
#spring.datasource.url=**************************************************************
#spring.datasource.url=**************************************************************
#spring.datasource.username=cmudevuser
#spring.datasource.password=DreamtechIT%9
#spring.datasource.url=****************************************************************
#spring.datasource.username=root
#spring.datasource.password=dreamtech%IT
spring.datasource.master.url=***********************************************************
spring.datasource.master.username=root
spring.datasource.master.password=root


spring.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.master.test-while-idle=true
spring.datasource.master.test-on-borrow=true
spring.datasource.master.time-between-eviction-runs-millis=5000
spring.datasource.master.min-evictable-idle-time-millis=60000
spring.datasource.master.validation-query=SELECT 1


spring.datasource.slave.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.slave.test-while-idle=true
spring.datasource.slave.test-on-borrow=true
spring.datasource.slave.time-between-eviction-runs-millis=5000
spring.datasource.slave.min-evictable-idle-time-millis=60000
spring.datasource.slave.validation-query=SELECT 1


spring.jooq.sql-dialect = mysql
logging.level.org.jooq=DEBUG

dubbo.application.name=course-study-async-task-service
dubbo.application.version=1
dubbo.registry.address=zookeeper://127.0.0.1:2181
#dubbo.registry.address=zookeeper://mw9.zhixueyun.com:10501
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator

spring.rabbitmq.host=127.0.0.1
spring.rabbitmq.port=5672
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1

graphite.server=**************
graphite.port=30004

spring.data.mongodb.host = **************
spring.data.mongodb.port = 30005
spring.data.mongodb.dbname = cmu_homecfg
spring.data.mongodb.connectTimeout = 60000
spring.data.mongodb.socketTimeout = 120000
spring.data.mongodb.socketKeepAlive = true
spring.data.mongodb.maxConnectionIdleTime = 120000
spring.data.mongodb.maxConnectionLifeTime = 120000
spring.data.mongodb.connectionsPerHost = 100
spring.data.mongodb.username = admin
spring.data.mongodb.password = dreamtech


# restTemplate
resttemplate.charset = UTF-8
resttemplate.timeout.read = 10000
resttemplate.timeout.connect = 10000
resttemplate.header.authorization = #@&^%#!@1

# course sync
notice.url = http://localhost/api/v1/cloud-center/company-sync/write-back/{id}/{module}/{status}
get.unsync.course.url = http://localhost/api/v1/cloud-center/company-sync/courses/{id}
get.template.course.url = http://localhost/api/v1/cloud-center/company-sync/categories/{id}

# cloud-center certification
sys.component.gensee.portAddress=http://zxy9.zhixueyun.com/api/v1/cloud-center
sys.component.gensee.loginName=admin
sys.component.gensee.passWord=admin
sys.component.gensee.site=zxy9.zhixueyun.com

#add_gensee_web_cast_URL=https://zxy9.zhixueyun.com/api/v1/cloud-center/gensee/create
add_gensee_web_cast_URL=http://localhost/api/v1/cloud-center/gensee/create
update_gensee_web_cast_URL=http://localhost/api/v1/cloud-center/gensee/edit
delete_gensee_web_cast_by_id_URL=http://zxy9.zhixueyun.com/api/v1/cloud-center/gensee/delete
query_user_join_history_URL=http://localhost/api/v1/cloud-center/gensee/history

# name of queues
study.message.queue.restfulLog = zxy-course-restfulLog
study.message.queue.member = zxy-course-member
study.message.queue.organization = zxy-course-organization
study.message.queue.organizationDetail = zxy-course-organizationDetail
study.message.queue.grantDetail = zxy-course-grantDetail
study.message.queue.audienceItem = zxy-course-audienceItem
study.message.queue.audienceItemK = zxy-course-audienceItem-k
study.message.queue.audienceMember = zxy-course-audienceMember
study.message.queue.audienceMemberK = zxy-course-audienceMember-k
study.message.queue.version = zxy-course-version
study.message.queue.studyProgress = zxy-course-studyProgress
study.message.queue.studyPush = zxy-course-studyPush
study.message.queue.knowledge = zxy-course-knowledge
study.message.queue.notice = zxy-course-notice
study.message.queue.courseSync = zxy-course-courseSync
study.message.queue.pushMessage = zxy-course-pushMessage
study.message.queue.push.record = zxy-course-pushRecord
study.message.queue.businessTopic = zxy-course-businessTopic
study.message.queue.task = zxy-course-task
study.message.queue.study.task = zxy-course-studyTask
study.message.queue.schedule = zxy-course-schedule
study.message.queue.course = zxy-course-info
study.message.queue.genseeBusinessProgress = zxy-course-genseeBusinessProgress
study.message.queue.scormUnZip = zxy-course-scorm-unzip
study.message.queue.genseeWebCast = zxy-course-genseeWebCast
study.message.queue.courseStatistics = zxy-course-statistics
study.message.queue.memberStatistics = zxy-member-statistics
study.message.queue.knowledge.statistics = zxy-knowledge-statistics



# unzip service
scorm.unzip.service.url = https://zxy9.zhixueyun.com/scorm-unzip

scorm.web.url = https://zxy9.zhixueyun.com/scorm-file
subject.log.day.repair.time.limit=00:00:00-20:50:00
audience.member.repair.time.limit=04:00:00-07:00:00


spring.redis.cluster = false
spring.redis.timeout = 10000
spring.redis.cluster.nodes = ***********:30006
spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3

# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1

# jiutian
jiutian.ftp.ip=***********
jiutian.ftp.port=37921
jiutian.ftp.username=zydj
jiutian.ftp.timeout=60000
jiutian.ftp.password=x0M2l#reks6
jiutian.ftp.path=/zgyd/zydj

# communityParty
communityParty.ftp.host=**********
communityParty.ftp.port=5232
communityParty.ftp.username=grid-organization
communityParty.ftp.timeout=60000
communityParty.ftp.password=93^y>2657-X7vkQCh
communityParty.ftp.path=/wangda

organization.province.ids=96493,1105092,1567690,1011203,98209,1093623,1061451,1073360,44261,1754249,1148390,80307,1093072,552512,567498,1180849,55836,1018307,105408,1073923,993478,1013550,1095592,1495996,1495590,1094794,1133482,1012970,1322085,1100282,1012774,1062701
course.category.codes=1,2,3,4
#http://yk-prd-hrdtrainasic.prd-yk.svc.cluster.local:8900/hrd/sync/train/onlineStudyHour ????
hrd.sync.train.onlineStudyHour=http://yk-uat-hrdtrainasic.prd-yk.svc.cluster.local:8900/hrd/sync/train/onlineStudyHour

# fastdfs
spring.fastdfs.connect-timeout = 30
spring.fastdfs.network-timeout = 60
spring.fastdfs.charset = utf-8
spring.fastdfs.tracker-servers = *************:10401
spring.fastdfs.tracker-http-port = 10402
spring.fastdfs.anti-steal-token = false
spring.fastdfs.secret-key = 123456


# exam localhost
#exam.csv.ftp.time.start = 01
exam.csv.ftp.ip =*************
exam.csv.ftp.port =22
exam.csv.ftp.username =online
exam.csv.ftp.password =CJ28v^pkNUkydLxZ

# MFT_HR_RCFZ_HQ_00006 è¯¾ç¨å­¦ä¹ è¿åº¦  MFT_HR_RCFZ_HQ_00007 æåº¦æ±æ»å­¦æ¶
onlineCourseStatus.csv.ftp.dir.put = /uat/MFTS/HQ_WDtoIHR_HQ/MftXfeOnlineCourseStatusSrv
onlineMonthtotalHours.csv.ftp.dir.put = /uat/MFTS/HQ_WDtoIHR_HQ/MftXfeOnlineMonthtotalHoursSrv

#MFT_HR_RCFZ_HQ_00004  ç§»ç½å¤§çº¿ä¸ä¸é¢åå¸èå´ä¿¡æ¯ä¼ è¾æå¡
onlineTopicGroupScope.csv.ftp.dir.put = /uat/MFTS/HQ_WDtoIHR_HQ/MftXfeOnlineTopicGroupScopeSrv

#MFT_HR_RCFZ_HQ_00003-ä¸­ç§»ç½å¤§çº¿ä¸ä¸é¢å­¦ä¹ äººåèè¯æç»©ä¼ è¾æå¡
onlineTopicExamSrv.csv.ftp.dir.put = /uat/MFTS/HQ_WDtoIHR_HQ/MftXfeOnlineTopicExamSrv

#MFT_HR_RCFZ_HQ_00001-ä¸­ç§»ç½å¤§çº¿ä¸ä¸é¢å­¦ä¹ äººåå­¦ä¹ è¿åº¦ä¼ è¾æå¡
onlineTopicLearnstate.csv.ftp.dir.put = /uat/MFTS/HQ_WDtoIHR_HQ/MftXfeOnlineTopicLearnstateSrv

#ç½å¤§è¯¾ç¨ç¬è®°|è¯¾ä»¶ç¬è®°|çä½ æ³é®ç¸å³Api
#ai.mentor.synchronize.synchronizeNoteApi=http://***********:8082/v1/recommend/wangda/findSubjectIntroduction


ai.mentor.synchronize.synchronizeNoteApi=http://*************:8082/v1/recommend/wangda/findSubjectIntroduction
course.model.url: http://exam.cmiivip.com
course.model.dataset-id:c2e4f95b-0518-4fb9-bea9-d4d8c1dceefb
course.model.app-api-key: Bearer app-9KIvY4ycYBL7PtIhdhKYk9fi
course.model.dataset-api-key: Bearer dataset-wyJ5dGtiGiMsqk7nrg0Etve2
course.model.interface.indexing-status=  /assistant/v1/datasets/%s/documents/%s/indexing-status