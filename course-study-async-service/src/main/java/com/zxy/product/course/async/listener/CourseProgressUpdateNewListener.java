package com.zxy.product.course.async.listener;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Sets;
import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.MemberCourseHoursService;
import com.zxy.product.course.api.archived.CourseStudyProgressArchivedService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.course.CourseProcessService;
import com.zxy.product.course.async.cache.CacheTableName;
import com.zxy.product.course.async.util.SplitTableName;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.CourseChapter;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseSectionStudyProgress;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.MemberCourseHours;
import com.zxy.product.course.entity.SplitTableConfig;
import com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressEntity;
import com.zxy.product.course.util.CourseSectionStudyProgressUtil;
import com.zxy.product.human.entity.StudyPlan;
import com.zxy.product.system.api.setting.RuleConfigService;
import com.zxy.product.system.entity.RuleConfig;
import com.zxy.product.system.util.RuleConfigRedisKeyUtil;
import org.jooq.SQL;
import org.jooq.UpdateSetMoreStep;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.StringJoiner;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER;
import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER_SECTION;
import static com.zxy.product.course.jooq.Tables.COURSE_INFO;
import static com.zxy.product.course.jooq.Tables.MEMBER;
import static com.zxy.product.course.jooq.Tables.ORGANIZATION;

/**
 * Created by keeley on 2017/12/4.
 */
@Component
public class CourseProgressUpdateNewListener extends AbstractMessageListener {
    private static Logger logger = LoggerFactory.getLogger(CourseProgressUpdateNewListener.class);

    private MessageSender messageSender;
    private CommonDao<CourseStudyProgress> courseStudyProgressCommonDao;
    private CommonDao<CourseSectionStudyProgress> progressCommonDao;
    private CommonDao<CourseChapterSection> sectionDao;
    private CourseProcessService courseProcessService;
    private CommonDao<Member> memberDao;
    private CacheTableName tableName;
    private CommonDao<CourseInfo> courseInfoCommonDao;
    private CourseCacheService courseCacheService;
    @Resource
    private CourseStudyProgressArchivedService archivedService;
    private MemberCourseHoursService memberCourseHoursService;
    private RuleConfigService ruleConfigService;
    private Cache cacheSystem;


    @Autowired
    public static void setLogger(Logger logger) {
    	CourseProgressUpdateNewListener.logger = logger;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }
    @Autowired
    public void setCourseStudyProgressCommonDao(CommonDao<CourseStudyProgress> courseStudyProgressCommonDao) {
        this.courseStudyProgressCommonDao = courseStudyProgressCommonDao;
    }
    @Autowired
    public void setCourseInfoCommonDao(CommonDao<CourseInfo> courseInfoCommonDao) {
        this.courseInfoCommonDao = courseInfoCommonDao;
    }
    @Autowired
    public void setProgressCommonDao(CommonDao<CourseSectionStudyProgress> progressCommonDao) {
        this.progressCommonDao = progressCommonDao;
    }
    @Autowired
    public void setSectionDao(CommonDao<CourseChapterSection> sectionDao) {
        this.sectionDao = sectionDao;
    }
    @Autowired
    public void setCourseProcessService(CourseProcessService courseProcessService) {
        this.courseProcessService = courseProcessService;
    }
    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Autowired
    public void setTableName(CacheTableName tableName) {
        this.tableName = tableName;
    }
    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }
    @Autowired
    public void setMemberCourseHoursService(MemberCourseHoursService memberCourseHoursService) {
        this.memberCourseHoursService = memberCourseHoursService;
    }
    @Autowired
    public void setRuleConfigService(RuleConfigService ruleConfigService) {
        this.ruleConfigService = ruleConfigService;
    }
    @Autowired
    public void setCacheSystem(CacheService cacheService) {
        this.cacheSystem = cacheService.create(RuleConfigRedisKeyUtil.getSpringApplicationName(), RuleConfigRedisKeyUtil.getModeName());
    }

    @Override
    protected void onMessage(Message message) {
        String courseId = message.getHeader(MessageHeaderContent.COURSE_ID);
        String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
        Long finishTime = Long.valueOf(Optional.ofNullable(message.getHeader(MessageHeaderContent.FINISHSTIME)).orElse("0"));
        logger.info("开始更新学习日志和课程进度, courseId={}, memberId= {}",courseId, memberId);
        this.updateCourseStudyProgress(memberId,courseId, finishTime);
        logger.info("更新完成");
    }


    private CourseStudyProgress updateCourseStudyProgress(String memberId, String courseId, Long finishTime) {
        logger.debug("开始异步计算课程学习进度,memberId = {},courseId = {} ", memberId, courseId);
        logger.info("开始异步处理，time={}", System.currentTimeMillis());
        courseProcessService.deleteCourseStudyRedis(memberId,courseId);
        logger.info("清除courseMap缓存，time={}", System.currentTimeMillis());

        /**
         * 目的 修改时长，修改状态
         */
        int currentStudyTime;
        // 查询出用户注册记录
        TableImpl<?> courseStudyProgressTable = SplitTableName.getTableNameByCode(courseCacheService.getCacheTableName(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS));

        CourseStudyProgress courseStudyProgress = courseStudyProgressCommonDao.execute(dslContext -> dslContext
                .select(courseStudyProgressTable.fields())
                .from(courseStudyProgressTable)
                .where(courseStudyProgressTable.field("f_course_id",String.class).eq(courseId), courseStudyProgressTable.field("f_member_id",String.class).eq(memberId)).limit(1)
                .fetchOne(record -> record.into(CourseStudyProgress.class)));
        if(courseStudyProgress == null) {
            logger.info("课程进度找不到 未开始更新,memberId = {}, courseId = {} ", memberId, courseId);
            return null;
        }
        logger.info("开始异步更新课程进,memberId = {}, courseId = {} , courseStudeyProcess[id={}]", memberId, courseId,
                courseStudyProgress.getId());
        // update for xdn
//        TableImpl<?> csspTable = tableName.getSectionTableName(courseId, ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS, ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId,courseId);

        logger.info("查询章节学习时长开始： time= {}", System.currentTimeMillis());
        // update 2019-11-23 查询时长修改 学习时长 = 所有版本的章节的总学习的时长(不过滤以前删除的章节的学习时长)
        Integer studentTimes = progressCommonDao.execute(
                x -> x.select(csspTable.field("f_study_total_time")).from(csspTable)
                        .where(csspTable.field("f_course_id", String.class).eq(courseId),
                                csspTable.field("f_member_id", String.class).eq(memberId))
                        .fetch(csspTable.field("f_study_total_time", Integer.class)))
                .stream().filter(r -> !ObjectUtils.isEmpty(r)).reduce(0, (a, b) -> a + b);
        logger.info("查询章节学习时长结束， time={}", System.currentTimeMillis());
        logger.info("本次学习时长：studyTime = {}, memberId = {}", studentTimes, memberId);
//        if (courseStudyProgress.getBeginTime() == null) {
//            progressCommonDao.execute(dslContext ->
//                    dslContext.select(csspTable.field("f_begin_time")).from(csspTable)
//                            .where(csspTable.field("f_course_id", String.class).eq(courseId),csspTable.field("f_member_id", String.class).eq(memberId))
//                            .orderBy(csspTable.field("f_begin_time")).limit(1).fetchOptional(csspTable.field("f_begin_time", Long.class)))
//                    .ifPresent(courseStudyProgress::setBeginTime);
//        }
        // updated 2019-11-15 不在异步更新开始时间，直接在注册时加上开始时间
        if (courseStudyProgress.getBeginTime() == null) {
            courseStudyProgress.setBeginTime(courseStudyProgress.getCreateTime());
        }
        logger.info("查询开始学习时间， time={}", System.currentTimeMillis());
        if ((courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_DEFAULT
                || courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_GIVEUP)
                &&courseStudyProgress.getBeginTime()!=null) {
            courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_STUDY);
        }

        // 当前学习时长
        currentStudyTime = studentTimes == null ? 0 : studentTimes;
        courseStudyProgress.setStudyTotalTime(currentStudyTime);
        // 每5分钟一提交更新最后访问时间以及最后访问章节id
//        String lastSectionId = progressCommonDao.execute(
//                x -> x.select(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID).from(COURSE_SECTION_STUDY_PROGRESS)
//                        .where(COURSE_SECTION_STUDY_PROGRESS.COURSE_ID.eq(courseId).and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId)))
//                        .orderBy(COURSE_SECTION_STUDY_PROGRESS.LAST_ACCESS_TIME.desc()).limit(1)
//        ).fetchOne(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID);
//
//        courseStudyProgress.setCurrentSectionId(lastSectionId);
        // 如果状态为未开始并且开始学习时间为null时没有最后访问时间
        if ((courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_DEFAULT)
                &&courseStudyProgress.getBeginTime()==null) {
            courseStudyProgress.setLastAccessTime(null);
        } else {
            courseStudyProgress.setLastAccessTime(finishTime);
        }

        boolean finish = courseStudyProgress.isFinish();
        if (!finish) {
            // 1 用户需要必修的进度 = 必修的数量*100
            List<String> referenceIds = sectionDao.execute(x -> x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID).from(COURSE_CHAPTER_SECTION)
                    .innerJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
//                    .leftJoin(COURSE_INFO).on(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(COURSE_INFO.ID))
                    .where(COURSE_CHAPTER.COURSE_ID.eq(courseId)
                            .and(COURSE_CHAPTER.VERSION_ID.eq(courseStudyProgress.getCourseVersionId()))
                            .and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED)))
//                            .and(COURSE_INFO.STATUS.notEqual(CourseInfo.STATUS_FIVE_SHELVES)) //过滤退库的数据
                    .fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID);

            // 2 用户当前学习版本必修的章节学习进度百分比
            // update for xdn
            Map<String, String> finishSectionIdsMap = progressCommonDao.execute(x ->
                    x.select(csspTable.field("f_section_id", String.class)).from(csspTable)
                            .where(csspTable.field("f_member_id", String.class).eq(memberId))
                            .and(csspTable.field("f_course_id", String.class).eq(courseId))
                            .and(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
                                    .or(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                            .fetch(csspTable.field("f_section_id", String.class)))
                    .stream().collect(Collectors.toMap(r -> r, r -> r));
//            Map<String, String> finishSectionIdsMap = progressCommonDao.execute(x ->
//                    x.select(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID).from(COURSE_SECTION_STUDY_PROGRESS)
//                    .where(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
//                    .and(COURSE_SECTION_STUDY_PROGRESS.COURSE_ID.eq(courseId))
//                            .and(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
//                                    .or(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
//                            .fetch(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID))
//                    .stream().collect(Collectors.toMap(r -> r, r -> r));
            List<String> finishList = new ArrayList<>();
            referenceIds.forEach(r -> {
                if (!StringUtils.isEmpty(finishSectionIdsMap.get(r))) {
                    finishList.add(r);
                }
            });
            // 1.从所有章节中排除所有的已完成的课程
            Sets.SetView<String> diff = Sets.difference(new HashSet<>(referenceIds), new HashSet<>(finishList));
            StringJoiner sectionIdsString = new StringJoiner("','", "'", "'");
            diff.forEach(sectionIdsString::add);
            // 2. 查询未完成的章节对应的课程ID
            SQL sql = DSL.sql(String.format("select f_course_id from t_course_chapter_section where f_id in (%s);", sectionIdsString));
            Set<String> fCourseId = progressCommonDao.execute(dsl -> dsl.fetch(sql).intoSet("f_course_id", String.class));
            for(String id : fCourseId) {
                // 3.将剩下的课程去归档记录中查询看下是否完成
                if (archivedService.checkExistenceArchived(memberId, id)) {
                    // 4.如果完成了就添加一个完成记录(扩容list)
                    // 下文没有使用finishList中的元素 所以随便添加了符合条件的courseId只要 finishList的数量一致就行了.
                    finishList.add(id);
                }
            }
            logger.info("查询是否完成课程， time={}", System.currentTimeMillis());
//            int finishNum = progressCommonDao.execute(x ->
//                    x.select(COURSE_SECTION_STUDY_PROGRESS.ID.count()).from(COURSE_SECTION_STUDY_PROGRESS)
//                            .where(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.in(referenceIds))
//                            .and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
//                            .and(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
//                                    .or(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
//                            .fetchOptional(COURSE_SECTION_STUDY_PROGRESS.ID.count()).orElse(0));
            int finishNum = finishList.size();
            if (referenceIds.size() == finishNum) {
                courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
                // 直接用发送消息的时间
                courseStudyProgress.setFinishTime(finishTime);
                courseStudyProgress.setCompletedRate(100);
                //初次完成更新最新完成时间为finishtime,完成次数初始化为1
                courseStudyProgress.setLatestCompletionTime(finishTime);
                courseStudyProgress.setCompletionTimes(1);
                logger.info("fisrt_finished update last_completetime courseId= {},memberId= {},updateCompletionTimes={},completionTimes={}", courseId,memberId,finishTime,1);
                //课程完成后发送消息指专题班
//                messageSender.send(MessageTypeContent.THEMATIC_CLASS_COURSE,
//                        MessageHeaderContent.MEMBER_ID,memberId,
//                        MessageHeaderContent.COURSE_ID,courseId);
                //课程完成后添加积分
//                messageSender.send(com.zxy.product.system.content.MessageTypeContent.SYSTEM_SCORE_RESULT_CHANGE,
//                        com.zxy.product.system.content.MessageHeaderContent.INTEGRAL_MEMBER_ID,memberId,
//                        com.zxy.product.system.content.MessageHeaderContent.INTEGRAL_RULE_KEY,IntegralRuleConstant.COURSE_COMPLETE,
//                        com.zxy.product.system.content.MessageHeaderContent.INTEGRAL_BUSINESS_ID,courseId);

                //学习计划完成率
                logger.info("学习计划完成率100%， time={}", System.currentTimeMillis());
                messageSender.send(com.zxy.product.human.content.MessageTypeContent.COURSE_STUDY_PLAN_UPDATE,
                        com.zxy.product.human.content.MessageHeaderContent.BUSINESS_ID,courseId,
                        com.zxy.product.human.content.MessageHeaderContent.MEMBER_ID,memberId,
                        com.zxy.product.human.content.MessageHeaderContent.COMPLETE_TIME, String.valueOf(finishTime),//完成时间
                        com.zxy.product.human.content.MessageHeaderContent.FINISH_STATUS, String.valueOf(StudyPlan.FINISH_STATUS_DONE),//完成状态
                        com.zxy.product.human.content.MessageHeaderContent.REQUIRED_COMPLETE_RATE,"100"//完成率
                );
            } else {
                courseStudyProgress.setCompletedRate(50);
                //学习计划完成率
                BigDecimal allCount = BigDecimal.valueOf(referenceIds.size());
                BigDecimal finishCount = BigDecimal.valueOf(finishNum);
                int completeRate = finishCount.divide(allCount, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).intValue();//完成率

                logger.info("学习计划完成率{}， time={}",completeRate, System.currentTimeMillis());
                messageSender.send(com.zxy.product.human.content.MessageTypeContent.COURSE_STUDY_PLAN_UPDATE,
                        com.zxy.product.human.content.MessageHeaderContent.BUSINESS_ID,courseId,
                        com.zxy.product.human.content.MessageHeaderContent.MEMBER_ID,memberId,
                        com.zxy.product.human.content.MessageHeaderContent.FINISH_STATUS,String.valueOf(StudyPlan.FINISH_STATUS_UN_FINISH),//完成状态
                        com.zxy.product.human.content.MessageHeaderContent.REQUIRED_COMPLETE_RATE, String.valueOf(completeRate)//完成率
                );
            }
        }
        //计算最新完成时间
        Integer courseHours = this.computeLastCompletetime(courseId,courseStudyProgress,memberId,courseStudyProgressTable);
        this.update(courseStudyProgress, courseStudyProgressTable);
        this.computeCourseHours(courseId,memberId, finish,courseHours,courseStudyProgress);

        // 课程为完成状态时同步课程进度到专题班
        if (courseStudyProgress.getFinishStatus() != null &&
                CourseStudyProgress.FINISH_STATUS_FINISH == courseStudyProgress.getFinishStatus()) {
            messageSender.send(MessageTypeContent.THEMATIC_CLASS_COURSE,
                    MessageHeaderContent.MEMBER_ID,memberId,
                    MessageHeaderContent.COURSE_ID,courseId);
        }

        messageSender.send(MessageTypeContent.COURSE_STUDY_PROGRESS_UPDATE,
                MessageHeaderContent.ID, courseStudyProgress.getId(),
                MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId()
                ,MessageHeaderContent.MEMBER_ID,memberId);
        // updated by wangdongyan 课程完成后才发消息更新专题信息
//        if (courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_FINISH || courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_MARKSUCCESS) {
        messageSender.send(MessageTypeContent.SUBJECT_PROGRESS_UPDATE_NEW,
                MessageHeaderContent.BUSINESS_ID,courseStudyProgress.getId(),
                MessageHeaderContent.FINISHSTIME, finishTime+"",MessageHeaderContent.MEMBER_ID,memberId);
//        }
        // add 2020-4-24 异步更新studyProgress分表数据
        messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE,
                MessageHeaderContent.ID, courseStudyProgress.getId(),
                MessageHeaderContent.MEMBER_ID, memberId
        );
        return courseStudyProgress;
    }

    private void computeCourseHours(String courseId, String memberId, boolean oldIsFinish, Integer courseHours, CourseStudyProgress courseStudyProgress) {
        if (courseStudyProgress.getFinishStatus() != null
                && courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_FINISH
                && !oldIsFinish
        ) {
            String rootOrgId = "1";
            Optional<RuleConfig> config = Optional.ofNullable(cacheSystem.get(RuleConfigRedisKeyUtil.getByNameRedisKey(RuleConfig.KEY.HOURS_CONFIG), RuleConfig.class));
            if (!config.isPresent())
                config = ruleConfigService.getByName(rootOrgId, RuleConfig.KEY.HOURS_CONFIG);


            config.flatMap(ruleConfig -> Optional.ofNullable(ruleConfig.getValue())).ifPresent(value -> {
                JSONObject jsonObject = JSONObject.parseObject(value);
                Optional.ofNullable(jsonObject.getString("course")).ifPresent(course -> {
                    String enable = "1";
                    if (enable.equals(course)) {
                        memberCourseHoursService.addCourseHours(courseId, memberId, courseHours, courseStudyProgress.getFinishTime(), MemberCourseHours.BUSINESS_TYPE_COURSE);
                    }
                });
            });

        }
    }

    //计算最近完成时间
    private Integer computeLastCompletetime(String courseId, CourseStudyProgress courseStudyProgress, String memberId, TableImpl<?> courseStudyProgressTable){
        //完成次数：总学习时长/（课程时长*80%）取整
        //最新完成时间，完成次数有变更时，记录当前时间为最新完成时间
        CourseInfo courseInfo = courseInfoCommonDao.get(courseId);
        Integer courseTime = courseInfo.getCourseTime();
        //如果课程表课程时长无数据则根据章节时间统计
        if (null == courseTime){
            String courseVersion = Optional.ofNullable(courseStudyProgressCommonDao.execute(dslContext -> dslContext.select(courseStudyProgressTable.fields())
                            .from(courseStudyProgressTable)
                            .where(
                                    courseStudyProgressTable.field("f_course_id", String.class).eq(courseId),
                                    courseStudyProgressTable.field("f_member_id", String.class).eq(memberId)
                            ).limit(1).fetchOne(r -> r.into(CourseStudyProgress.class))
                    ))
                    .map(CourseStudyProgressEntity::getCourseVersionId).orElse(courseInfo.getVersionId());


            // 查询章节和章节详情
            List<CourseChapter> courseChapters = courseCacheService.getCourseChapter(courseId, courseVersion);
            //计算课程总时长
            List<CourseChapterSection> sectionList = new ArrayList<CourseChapterSection>();
            courseChapters.stream().filter(chapter -> chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()).forEach(c -> sectionList.addAll(c.getCourseChapterSections()));
            courseTime = sectionList.stream()
                    .filter(section -> section.getSectionType() != 8 && section.getSectionType() != 9 && section.getSectionType() != 13 && section.getSectionType() != 12)
                    .map(s -> Optional.ofNullable(s.getTimeSecond()).orElse(0) + Optional.ofNullable(s.getTimeMinute()).orElse(0) * 60)
                    .reduce(0, (a, b) -> a + b);
        }
        int updateCompletionTimes = 0;
        if(courseTime != 0){
            updateCompletionTimes = new BigDecimal(courseStudyProgress.getStudyTotalTime())
                    .divide(new BigDecimal(courseTime).multiply(new BigDecimal(0.8)), BigDecimal.ROUND_DOWN).intValue();
        }
        Integer completionTimes = courseStudyProgress.getCompletionTimes() == null ? 1 : courseStudyProgress.getCompletionTimes();
        logger.info("show last_completetime  courseId= {},memberId= {},updateCompletionTimes={},completionTimes={}", courseId,memberId,updateCompletionTimes,completionTimes);
        if (updateCompletionTimes > completionTimes){
            courseStudyProgress.setCompletionTimes(updateCompletionTimes);
            courseStudyProgress.setLatestCompletionTime(System.currentTimeMillis());
            logger.info("update last_completetime courseId= {},memberId= {},updateCompletionTimes={},completionTimes={}", courseId,memberId,updateCompletionTimes,completionTimes);
        }
        return courseTime;
    }

    private int update(CourseStudyProgress progress, TableImpl<?> courseStudyProgressTable) {
        // UpdateSetMoreStep<CourseStudyProgressRecord> updateSql = courseStudyProgressCommonDao.execute(x -> x.update(courseStudyProgressTable)
        //         .set(courseStudyProgressTable.field("f_last_access_time",Long.class),progress.getLastAccessTime())
        //         .set(courseStudyProgressTable.field("f_current_section_id",String.class),progress.getCurrentSectionId()));
        UpdateSetMoreStep<?> updateSql = courseStudyProgressCommonDao.execute(x -> x.update(courseStudyProgressTable)
                .set(courseStudyProgressTable.field("f_last_access_time", Long.class), progress.getLastAccessTime())
                .set(courseStudyProgressTable.field("f_current_section_id", String.class), progress.getCurrentSectionId()));
        logger.info("本次更新progress的时长为： studyTime = {}", progress.getStudyTotalTime());
        if(progress.getFinishStatus()!=null) {
            updateSql.set(courseStudyProgressTable.field("f_finish_status",Integer.class),progress.getFinishStatus());
        }
        if(progress.getFinishTime()!=null) {
            updateSql.set(courseStudyProgressTable.field("f_finish_time",Long.class),progress.getFinishTime());
        }
        if(progress.getCompletedRate()!=null) {
            updateSql.set(courseStudyProgressTable.field("f_completed_rate",Integer.class),progress.getCompletedRate());
        }
        if(progress.getStudyTotalTime()!=null) {
            updateSql.set(courseStudyProgressTable.field("f_study_total_time",Integer.class),progress.getStudyTotalTime());
        }
        if(progress.getBeginTime()!=null) {
            updateSql.set(courseStudyProgressTable.field("f_begin_time",Long.class),progress.getBeginTime());
        }
        if(progress.getCompletionTimes()!=null) {
            updateSql.set(courseStudyProgressTable.field("f_completion_times",Integer.class),progress.getCompletionTimes());
        }
        if(progress.getLatestCompletionTime()!=null) {
            updateSql.set(courseStudyProgressTable.field("f_latest_completion_time",Long.class),progress.getLatestCompletionTime());
        }
        // add by wangdongyan 分表使用，最后一次修改时间
        updateSql.set(courseStudyProgressTable.field("f_last_modify_time",Long.class), System.currentTimeMillis());
        updateSql.set(courseStudyProgressTable.field("f_create_time",Long.class), courseStudyProgressTable.field("f_create_time",Long.class).plus(1));

        //更新最近学习课程 -- LJY
//        logger.info("###更新最近学习 异步发送，在函数：update()中，memberid:{},courseId:{}",progress.getMemberId(),progress.getCourseId());
//            CourseInfo courseInfo =courseInfoCommonDao.get(progress.getCourseId());
        if(!progress.getCourseId().isEmpty()) {
            CourseInfo courseInfo = courseInfoCommonDao.execute(x -> x.select(COURSE_INFO.ID,COURSE_INFO.NAME,
                    COURSE_INFO.COVER_PATH,COURSE_INFO.BUSINESS_TYPE).
                    from(COURSE_INFO).
                    where(COURSE_INFO.ID.eq(progress.getCourseId()))).
                    fetchOptional(r -> {
                        CourseInfo info = new CourseInfo();
                        info.setId(r.getValue(COURSE_INFO.ID));
                        info.setName(r.getValue(COURSE_INFO.NAME));
                        info.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH));
                        info.setBusinessType(r.getValue(COURSE_INFO.BUSINESS_TYPE));
                        return info;
                    }).orElse(null);
            if(courseInfo !=null && courseInfo.getBusinessType() == 0 && progress.getLastAccessTime()!=null){
                messageSender.send(MessageTypeContent.STUDY_CARD_UPDATE,
                        MessageHeaderContent.MEMBER_ID, progress.getMemberId(),
                        MessageHeaderContent.COURSE_ID, progress.getCourseId());
            }
        }

        return updateSql.where(courseStudyProgressTable.field("f_id",String.class).eq(progress.getId())).execute();
    }


    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.COURSE_PROGRESS_UPDATE_NEW
        };
    }

    private String getPath(String memberId) {
        return memberDao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION)
                .innerJoin(MEMBER).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(MEMBER.ID.eq(memberId))).fetchOne(ORGANIZATION.PATH);
    }

    private Long getTime(Integer date) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd HH:mm:ss");
        try {
            return sdf.parse(date + " 00:00:00").getTime();
        } catch (ParseException e) {
            e.printStackTrace();
        }
        return 0L;
    }

}
