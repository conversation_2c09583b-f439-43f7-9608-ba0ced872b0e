package com.zxy.product.course.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.async.util.SplitTableName;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseSectionStudyProgress;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.SplitTableConfig;
import com.zxy.product.course.util.CourseSectionStudyProgressUtil;
import com.zxy.product.human.entity.StudyPlan;
import org.jooq.UpdateSetMoreStep;
import org.jooq.impl.TableImpl;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER;
import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER_SECTION;
import static com.zxy.product.course.jooq.Tables.COURSE_INFO;
import static com.zxy.product.course.jooq.Tables.COURSE_SECTION_STUDY_PROGRESS;
import static com.zxy.product.course.jooq.Tables.COURSE_SECTION_STUDY_PROGRESS_XDNS;

/**
 * 专题进度细分
 */
@Component
public class SubjectProgressUpdateSubListener extends AbstractMessageListener {
    private static Logger logger = LoggerFactory.getLogger(SubjectProgressUpdateSubListener.class);
    @Autowired
    private MessageSender messageSender;
    @Autowired
    private CommonDao<CourseChapterSection> sectionDao;
    @Autowired
    private CommonDao<CourseStudyProgress> courseStudyProgressCommonDao;
    @Autowired
    private CommonDao<CourseSectionStudyProgress> progressCommonDao;
    @Resource
    private CourseCacheService courseCacheService;

    @Override
    protected void onMessage(Message message) {
        String courseSectionStudyProgressId = message.getHeader(MessageHeaderContent.ID);
        if (courseSectionStudyProgressId == null) {
            return;
        }
        String sectionMemberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
        if (org.apache.commons.lang3.StringUtils.isBlank(sectionMemberId)){
            logger.error("缺少重要的MemberId参数,请好好检查");
            return;
        }
        String courseId = message.getHeader(MessageHeaderContent.COURSE_ID);
        Long finishTime = Long.valueOf(Optional.ofNullable(message.getHeader(MessageHeaderContent.FINISHSTIME)).orElse("0"));
        this.updateSubject(courseSectionStudyProgressId, courseId, finishTime, sectionMemberId);
    }

    private void updateSubject(String courseSectionStudyProgressId, String courseId, Long finishTime, String sectionMemberId) {

        CourseSectionStudyProgress progress;
        TableImpl<?> tempTable = COURSE_SECTION_STUDY_PROGRESS;
        // update for xdn
        // 旧的消息没有消费完,么有courseId参数,遍历查找
        if (StringUtils.isEmpty(courseId)) {
            progress = progressCommonDao.execute(e -> e.select(COURSE_SECTION_STUDY_PROGRESS_XDNS.fields())
                            .from(COURSE_SECTION_STUDY_PROGRESS_XDNS)
                            .where(COURSE_SECTION_STUDY_PROGRESS_XDNS.ID.eq(courseSectionStudyProgressId)))
                    .fetchOptional(r -> new CourseSectionStudyProgress().fill(COURSE_SECTION_STUDY_PROGRESS_XDNS, r)).orElse(null);
            if (progress != null) {
                tempTable = COURSE_SECTION_STUDY_PROGRESS_XDNS;
            } else {
                progress = progressCommonDao.getOptional(courseSectionStudyProgressId).orElse(null);
            }
        } else {
//            tempTable = tableName.getSectionTableName(courseId, ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS, ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
            tempTable = CourseSectionStudyProgressUtil.getTable(sectionMemberId, courseId);
            TableImpl<?> finalTable = tempTable;
            progress = progressCommonDao.execute(e -> e.select(finalTable.fields()).from(finalTable)
                            .where(finalTable.field("f_id", String.class).eq(courseSectionStudyProgressId)))
                    .fetchOptional(r -> new CourseSectionStudyProgress().fill(finalTable, r)).orElse(null);
        }

        if (progress == null) {
            return;
        }

        TableImpl<?> csspTable = tempTable;
        String memberId = progress.getMemberId();
        TableImpl<?> cacheTable = SplitTableName.getTableNameByCode(courseCacheService.getCacheTableName(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS));
        CourseStudyProgress courseStudyProgress = courseStudyProgressCommonDao.execute(dslContext -> dslContext.select(cacheTable.fields()).from(cacheTable).where(cacheTable.field("f_course_id", String.class).eq(courseId), cacheTable.field("f_member_id", String.class).eq(memberId)).limit(1).fetchOne(r->r.into(CourseStudyProgress.class)));
        if(courseStudyProgress == null) {
            logger.info("专题进度找不到 未开始更新,memberId = {}, courseId = {} ", memberId, courseId);
            return;
        }
        String versionId = courseStudyProgress.getCourseVersionId();
        // updated 2019-11-14 专题发布后不更新studyProgress表的versionId，直接用info表的versionId
//        Optional<String> versionId = courseInfoCommonDao.execute(s -> s.select(COURSE_INFO.VERSION_ID).from(COURSE_INFO).where(COURSE_INFO.ID.eq(courseId)).fetchOptional(COURSE_INFO.VERSION_ID));

        if(courseStudyProgress.getBeginTime()==null) {
            courseStudyProgress.setBeginTime(progress.getBeginTime());
        }
        boolean isStart = (Integer.valueOf(CourseStudyProgress.FINISH_STATUS_DEFAULT).equals(courseStudyProgress.getFinishStatus())
                || Integer.valueOf(CourseStudyProgress.FINISH_STATUS_GIVEUP).equals(courseStudyProgress.getFinishStatus()))
                &&courseStudyProgress.getBeginTime()!=null;
        if (isStart) {
            courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_STUDY);
        }
        // 学习时长 = 所有版本的章节的总学习的时长(不过滤以前删除的章节的学习时长)
//        BigDecimal studentTimes = progressCommonDao.execute(
//                x -> x.select(COURSE_SECTION_STUDY_PROGRESS.STUDY_TOTAL_TIME.sum()).from(COURSE_SECTION_STUDY_PROGRESS)
//                        .where(COURSE_SECTION_STUDY_PROGRESS.COURSE_ID.eq(courseId)
//                                .and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId)))
//                        .fetchOne(COURSE_SECTION_STUDY_PROGRESS.STUDY_TOTAL_TIME.sum()));
        // update for xdn
        Integer studentTimes = progressCommonDao.execute(
                x -> x.select(csspTable.field("f_study_total_time")).from(csspTable)
                        .where(csspTable.field("f_course_id", String.class).eq(courseId),
                                csspTable.field("f_member_id", String.class).eq(memberId),
                                csspTable.field("f_study_total_time", Integer.class).gt(0)))
                        .fetch(csspTable.field("f_study_total_time", Integer.class))
        .stream().reduce(0, Integer::sum);
        Integer currentStudyTime = Optional.ofNullable(studentTimes).orElse(0);
        // 当前学习时长
        courseStudyProgress.setStudyTotalTime(currentStudyTime);
        // 设置最后访问的章节
        courseStudyProgress.setCurrentSectionId(progress.getSectionId());
        courseStudyProgress.setLastAccessTime(finishTime);

        // updated 2019-11-19 如果更新的章节进度已完成才发消息更新专题的完成状态
        if ((CourseSectionStudyProgress.FINISH_STATUS_FINISH.equals(progress.getFinishStatus()) || CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS.equals(progress.getFinishStatus())) && !courseStudyProgress.isFinish()) {
//            // add 2019-11-19 发消息更新专题的完成状态
//            messageSender.send(MessageTypeContent.SUBJECT_SECTION_PROGRESS_FINISH_STATUS_UPDATE,
//                    MessageHeaderContent.ID, courseStudyProgress.getId(),
//                    MessageHeaderContent.FINISHSTIME, finishTime + ""
//                    );
            // updated 2019-11-14 专题发布后不更新studyProgress表的versionId，直接用info表的versionId
            List<String> referenceIds = sectionDao.execute(x -> x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID).from(COURSE_CHAPTER_SECTION)
                    .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
                    .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER_SECTION.RESOURCE_ID))
                    .where(COURSE_CHAPTER.COURSE_ID.eq(courseId)
                            .and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED))
                            .and(COURSE_INFO.STATUS.notIn(CourseInfo.STATUS_FIVE_SHELVES, CourseInfo.STATUS_THE_SHELVES).or(COURSE_INFO.STATUS.isNull())) //过滤退库数据
                            .and(COURSE_CHAPTER.VERSION_ID.eq(versionId)))
//                            .and(versionId.map(v -> COURSE_CHAPTER.VERSION_ID.eq(v)).orElse(COURSE_CHAPTER.VERSION_ID.isNull())))
                    .fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID);

            // 2 用户当前学习版本必修的章节学习进度百分比
            Map<String, String> finishSectionIdsMap = progressCommonDao.execute(x ->
                    x.select(csspTable.field("f_section_id")).from(csspTable)
                            .where(csspTable.field("f_member_id", String.class).eq(memberId))
                            .and(csspTable.field("f_course_id", String.class).eq(courseId))
                            .and(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
                                    .or(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                            .fetch(csspTable.field("f_section_id", String.class)))
                    .stream().collect(Collectors.toMap(r -> r, r -> r));
            List<String> finishList = new ArrayList<>();
            referenceIds.forEach(r -> {
                if (!StringUtils.isEmpty(finishSectionIdsMap.get(r))) {
                    finishList.add(r);
                }
            });
//            int finishNum = progressCommonDao.execute(x ->
//                    x.select(COURSE_SECTION_STUDY_PROGRESS.ID.count()).from(COURSE_SECTION_STUDY_PROGRESS)
//                            .where(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.in(referenceIds))
//                            .and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
//                            .and(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH).or(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
//                            .fetchOptional(COURSE_SECTION_STUDY_PROGRESS.ID.count()).orElse(0));
            int finishNum = finishList.size();
            if (referenceIds.size() == finishNum) {
                courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
                courseStudyProgress.setFinishTime(finishTime);
                courseStudyProgress.setCompletedRate(100);
                //学习计划完成率
                logger.info("学习计划完成率100%，专题id：{}, time={}", courseStudyProgress.getCourseId(),System.currentTimeMillis());
                messageSender.send(com.zxy.product.human.content.MessageTypeContent.COURSE_STUDY_PLAN_UPDATE,
                        com.zxy.product.human.content.MessageHeaderContent.BUSINESS_ID,courseStudyProgress.getCourseId(),
                        com.zxy.product.human.content.MessageHeaderContent.MEMBER_ID,memberId,
                        com.zxy.product.human.content.MessageHeaderContent.COMPLETE_TIME, String.valueOf(finishTime),//完成时间
                        com.zxy.product.human.content.MessageHeaderContent.FINISH_STATUS, String.valueOf(StudyPlan.FINISH_STATUS_DONE),//完成状态
                        com.zxy.product.human.content.MessageHeaderContent.REQUIRED_COMPLETE_RATE,"100"//完成率
                );
            } else {
                courseStudyProgress.setCompletedRate(50);

                //学习计划完成率
                BigDecimal allCount = BigDecimal.valueOf(referenceIds.size());//必修章节数
                BigDecimal finishCount1 = BigDecimal.valueOf(finishNum);//完成数
                int completeRate = finishCount1.divide(allCount, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).intValue();//完成率

                logger.info("学习计划完成率{}，专题id：{}, time={}",courseStudyProgress.getCourseId(), System.currentTimeMillis());
                messageSender.send(com.zxy.product.human.content.MessageTypeContent.COURSE_STUDY_PLAN_UPDATE,
                        com.zxy.product.human.content.MessageHeaderContent.BUSINESS_ID,courseStudyProgress.getCourseId(),
                        com.zxy.product.human.content.MessageHeaderContent.MEMBER_ID,memberId,
                        com.zxy.product.human.content.MessageHeaderContent.FINISH_STATUS,String.valueOf(StudyPlan.FINISH_STATUS_UN_FINISH),//完成状态
                        com.zxy.product.human.content.MessageHeaderContent.REQUIRED_COMPLETE_RATE, String.valueOf(completeRate)//完成率
                );

            }
        }
        this.update(courseStudyProgress, cacheTable);
        //todo 2025-08-03发版暂时不发消息， 注释，待后续功能完善打开
//        messageSender.send(MessageTypeContent.CONCENTRATE_STUDY_HOURS_MEMBER,
//                           MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId()
//                ,MessageHeaderContent.MEMBER_ID,memberId);

        messageSender.send(MessageTypeContent.COURSE_STUDY_PROGRESS_UPDATE,
                MessageHeaderContent.ID, courseStudyProgress.getId(),
                MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId()
                ,MessageHeaderContent.MEMBER_ID,memberId);
        // 专题引用专题之后
        messageSender.send(MessageTypeContent.SUBJECT_REFERENCE_SUBJECT_PROGRESS_UPDATE,
                MessageHeaderContent.ID, courseStudyProgress.getId(),
                MessageHeaderContent.FINISHSTIME, finishTime+"",MessageHeaderContent.MEMBER_ID,memberId);
        // 专题完成后颁发证书
        if (courseStudyProgress.getFinishStatus() != null &&
                CourseStudyProgress.FINISH_STATUS_FINISH == courseStudyProgress.getFinishStatus() || CourseStudyProgress.FINISH_STATUS_MARKSUCCESS == courseStudyProgress.getFinishStatus()) {
            messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD,
                    MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId(),
                    MessageHeaderContent.MEMBER_ID, courseStudyProgress.getMemberId());
            // add 2020-6-10 CHBN活动颁证书
            messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD_CHBN,
                    MessageHeaderContent.ID, courseStudyProgress.getId(),
                    MessageHeaderContent.MEMBER_ID,courseStudyProgress.getMemberId()
            );
        }

        // 专题更新后，发消息更新重塑培训进度
        messageSender.send(MessageTypeContent.REMODELING_TRAIN_PLAN_STUDENT_SING_UP,
                MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId(),
                MessageHeaderContent.MEMBER_ID, courseStudyProgress.getMemberId()
        );
        // add 2020-4-24 异步更新studyProgress分表数据
        messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE,
                MessageHeaderContent.ID, courseStudyProgress.getId(),MessageHeaderContent.MEMBER_ID,memberId);
    }

    private int update(CourseStudyProgress progress, TableImpl<?> cacheTable) {
        UpdateSetMoreStep<?> updateSql = courseStudyProgressCommonDao.execute(x -> x.update(cacheTable)
                .set(cacheTable.field("f_last_access_time", Long.class), progress.getLastAccessTime())
                .set(cacheTable.field("f_current_section_id", String.class), progress.getCurrentSectionId()));
        if(progress.getFinishStatus()!=null) {
            updateSql.set(cacheTable.field("f_finish_status", Integer.class),progress.getFinishStatus());
        }
        if(progress.getFinishTime()!=null) {
            updateSql.set(cacheTable.field("f_finish_time", Long.class),progress.getFinishTime());
        }
        if(progress.getCompletedRate()!=null) {
            updateSql.set(cacheTable.field("f_completed_rate", Integer.class),progress.getCompletedRate());
        }
        if(progress.getStudyTotalTime()!=null) {
            updateSql.set(cacheTable.field("f_study_total_time", Integer.class),progress.getStudyTotalTime());
        }
        if(progress.getBeginTime()!=null) {
            updateSql.set(cacheTable.field("f_begin_time", Long.class),progress.getBeginTime());
        }
        updateSql.set(cacheTable.field("f_last_modify_time", Long.class), System.currentTimeMillis()); // add by wangdongyan 分表使用，最后一次修改时间
        return updateSql.where(cacheTable.field("f_id", String.class).eq(progress.getId())).execute();
    }

    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.SUBJECT_PROGRESS_UPDATE_SUB_NEW
        };
    }
}
