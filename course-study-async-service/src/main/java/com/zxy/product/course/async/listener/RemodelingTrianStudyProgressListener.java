package com.zxy.product.course.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.cache.Cache;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.course.async.cache.CacheTableName;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.CertificateRecord;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseSectionStudyProgress;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.RemodelingEntryStudyProgress;
import com.zxy.product.course.util.CourseSectionStudyProgressUtil;
import org.jooq.impl.TableImpl;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.Tables.CERTIFICATE_RECORD;
import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER;
import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER_SECTION;
import static com.zxy.product.course.jooq.Tables.COURSE_INFO;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS;
import static com.zxy.product.course.jooq.Tables.REMODELING_ENTRY_STUDY_PROGRESS;

/**
 * 更新重塑培训计划中的报名进度表记录
 */
@Component
public class RemodelingTrianStudyProgressListener extends AbstractMessageListener {

    protected static final Logger LOGGER = LoggerFactory.getLogger(RemodelingTrianStudyProgressListener.class);
    private CommonDao<RemodelingEntryStudyProgress> studyProgressCommonDao;
    private CommonDao<CourseStudyProgress> courseStudyProgressCommonDao;
    private CommonDao<CourseChapterSection> sectionDao;
    private CommonDao<CourseSectionStudyProgress> sectionStudyProgressCommonDao;
    private CommonDao<CertificateRecord> certificateRecordCommonDao;
    private CacheTableName tableName;

    private Cache cache;

    @Autowired
    public void setStudyProgressCommonDao(CommonDao<RemodelingEntryStudyProgress> studyProgressCommonDao) {
        this.studyProgressCommonDao = studyProgressCommonDao;
    }

    @Autowired
    public void setCourseStudyProgressCommonDao(CommonDao<CourseStudyProgress> courseStudyProgressCommonDao) {
        this.courseStudyProgressCommonDao = courseStudyProgressCommonDao;
    }

    @Autowired
    public void setSectionDao(CommonDao<CourseChapterSection> sectionDao) {
        this.sectionDao = sectionDao;
    }

    @Autowired
    public void setSectionStudyProgressCommonDao(CommonDao<CourseSectionStudyProgress> sectionStudyProgressCommonDao) {
        this.sectionStudyProgressCommonDao = sectionStudyProgressCommonDao;
    }

    @Autowired
    public void setCache(Cache cache) {
        this.cache = cache;
    }

    @Autowired
    public void setCertificateRecordCommonDao(CommonDao<CertificateRecord> certificateRecordCommonDao) {
        this.certificateRecordCommonDao = certificateRecordCommonDao;
    }

    @Autowired
    public void setTableName(CacheTableName tableName) {
        this.tableName = tableName;
    }


    @Override
    protected void onMessage(Message message) {
        int type = message.getType();
        switch (type) {
            case MessageTypeContent.REMODELING_TRAIN_PLAN_STUDENT_SING_UP:
                String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
                String subjectId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
                LOGGER.info("更新重塑培训获得证书状态，memberId={}， subjectId={}", memberId, subjectId);
                this.signUpStudy(memberId, subjectId);
                break;
            case MessageTypeContent.REMODELING_TRAIN_ACQUIRE_CERTIFICATE_BATCH:
                String ids = message.getHeader(MessageHeaderContent.IDS);
                String businessId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
                LOGGER.info("更新重塑培训获得证书状态，ids={}， subjectId={}", ids, businessId);
                if (!StringUtils.isEmpty(ids) && !StringUtils.isEmpty(businessId)) {
                    String[] memberIds = ids.split(",");
                    this.batchUpdateStudyPorgressByMemberIds(memberIds, businessId);
                }
                break;
            default:
                break;
        }
    }

    @Override
    public int[] getTypes() {
        return new int[] {
                MessageTypeContent.REMODELING_TRAIN_PLAN_STUDENT_SING_UP,
                MessageTypeContent.REMODELING_TRAIN_ACQUIRE_CERTIFICATE_BATCH
        };
    }

    private void signUpStudy (String memberId, String subjectId) {
        RemodelingEntryStudyProgress remodelingEntryStudyProgress = studyProgressCommonDao.execute(s ->
                s.select(Fields.start().add(REMODELING_ENTRY_STUDY_PROGRESS).end())
                .from(REMODELING_ENTRY_STUDY_PROGRESS)
                .where(REMODELING_ENTRY_STUDY_PROGRESS.MEMBER_ID.eq(memberId)
                        .and(REMODELING_ENTRY_STUDY_PROGRESS.SUBJECT_ID.eq(subjectId)))
                .limit(1)
                .fetchOptional(r -> r.into(REMODELING_ENTRY_STUDY_PROGRESS).into(RemodelingEntryStudyProgress.class))
                .orElse(null)
        );
        if (remodelingEntryStudyProgress == null) {
            return;
        }
        CourseStudyProgress courseStudyProgress = courseStudyProgressCommonDao.execute(x ->
                x.select(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME, COURSE_STUDY_PROGRESS.FINISH_STATUS, COURSE_STUDY_PROGRESS.COURSE_VERSION_ID)
                        .from(COURSE_STUDY_PROGRESS)
                        .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(remodelingEntryStudyProgress.getSubjectId())
                                .and(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(remodelingEntryStudyProgress.getMemberId())))
                        .fetchOptional(p -> {
                            CourseStudyProgress study = new CourseStudyProgress();
                            study.setStudyTotalTime(p.getValue(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME));
                            study.setFinishStatus(p.getValue(COURSE_STUDY_PROGRESS.FINISH_STATUS));
                            study.setCourseVersionId(p.getValue(COURSE_STUDY_PROGRESS.COURSE_VERSION_ID));
                            return study;
                        })
        ).orElse(null);

        if (courseStudyProgress == null) {
            return;
        }
        String versionId = courseStudyProgress.getCourseVersionId();
        remodelingEntryStudyProgress.setStudyTotalTime(courseStudyProgress.getStudyTotalTime() != null ? courseStudyProgress.getStudyTotalTime() : 0);
//        if (!remodelingEntryStudyProgress.isFinish()) {
            List<String> referenceIds = sectionDao.execute(x -> x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID).from(COURSE_CHAPTER_SECTION)
                    .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
//                    .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER_SECTION.RESOURCE_ID))
                    .where(COURSE_CHAPTER.COURSE_ID.eq(remodelingEntryStudyProgress.getSubjectId())
//                            .and(COURSE_INFO.STATUS.notEqual(CourseInfo.STATUS_FIVE_SHELVES)) //过滤退库数据
                            .and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED))
                            .and(COURSE_CHAPTER.VERSION_ID.eq(versionId)))
                    .fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID);

//            TableImpl<?> csspTable = tableName.getSectionTableName(remodelingEntryStudyProgress.getSubjectId(), ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS, ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(remodelingEntryStudyProgress.getMemberId(),remodelingEntryStudyProgress.getSubjectId());
        // 2 用户当前学习版本必修的章节学习进度百分比
            Map<String, String> finishSectionIdsMap = sectionStudyProgressCommonDao.execute(x ->
                    x.select(csspTable.field("f_section_id")).from(csspTable)
                            .where(csspTable.field("f_member_id", String.class).eq(remodelingEntryStudyProgress.getMemberId()))
                            .and(csspTable.field("f_course_id", String.class).eq(remodelingEntryStudyProgress.getSubjectId()))
                            .and(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
                                    .or(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                            .fetch(csspTable.field("f_section_id", String.class)))
                    .stream().collect(Collectors.toMap(r -> r, r -> r));
            List<String> finishList = new ArrayList<>();
            referenceIds.forEach(r -> {
                if (!StringUtils.isEmpty(finishSectionIdsMap.get(r))) {
                    finishList.add(r);
                }
            });
            int finishNum = finishList.size() > 0 ? finishList.size() : 0;
            remodelingEntryStudyProgress.setFinishSectionNum(finishNum);
//        }

        // 完成状态
        Integer finishStatus = CourseStudyProgress.FINISH_STATUS_DEFAULT;
        Integer certificateStatus = remodelingEntryStudyProgress.getIsCertificate() ==  null ? RemodelingEntryStudyProgress.CERTIFICATE_STATUS_UNFINISH : remodelingEntryStudyProgress.getIsCertificate();
        if (courseStudyProgress.getFinishStatus() != null) {
            if (courseStudyProgress.getFinishStatus()== CourseStudyProgress.FINISH_STATUS_MARKSUCCESS || courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_FINISH) {
                finishStatus = CourseStudyProgress.FINISH_STATUS_FINISH;
                String certificateRecordId = certificateRecordCommonDao.execute(x -> x.select(CERTIFICATE_RECORD.ID)
                        .from(CERTIFICATE_RECORD)
                        .where(CERTIFICATE_RECORD.BUSINESS_ID.eq(remodelingEntryStudyProgress.getSubjectId())
                                .and(CERTIFICATE_RECORD.MEMBER_ID.eq(remodelingEntryStudyProgress.getMemberId())))
                        .limit(1)
                        .fetchOne(CERTIFICATE_RECORD.ID)
                );
                if (!StringUtils.isEmpty(certificateRecordId)) {
                    certificateStatus = RemodelingEntryStudyProgress.CERTIFICATE_STATUS_ACQUISITION;
                }
            } else {
                finishStatus = courseStudyProgress.getFinishStatus();
            }
        }
        remodelingEntryStudyProgress.setFinishStatus(finishStatus);
        remodelingEntryStudyProgress.setIsCertificate(certificateStatus);
        studyProgressCommonDao.execute(x -> x.update(REMODELING_ENTRY_STUDY_PROGRESS)
                .set(REMODELING_ENTRY_STUDY_PROGRESS.FINISH_SECTION_NUM, remodelingEntryStudyProgress.getFinishSectionNum())
                .set(REMODELING_ENTRY_STUDY_PROGRESS.FINISH_STATUS, remodelingEntryStudyProgress.getFinishStatus())
                .set(REMODELING_ENTRY_STUDY_PROGRESS.IS_CERTIFICATE, remodelingEntryStudyProgress.getIsCertificate())
                .set(REMODELING_ENTRY_STUDY_PROGRESS.STUDY_TOTAL_TIME, remodelingEntryStudyProgress.getStudyTotalTime())
                .where(REMODELING_ENTRY_STUDY_PROGRESS.ID.eq(remodelingEntryStudyProgress.getId()))
                .execute()
        );
    }

    private void batchUpdateStudyPorgressByMemberIds(String[] memberIds, String subjectId) {
        for (String memberId: memberIds) {
            LOGGER.info("当前更新重塑培训memberId={}", memberId);
            RemodelingEntryStudyProgress remodelingEntryStudyProgress = studyProgressCommonDao.execute(s -> s.select(Fields.start().add(REMODELING_ENTRY_STUDY_PROGRESS).end())
                    .from(REMODELING_ENTRY_STUDY_PROGRESS)
                    .where(REMODELING_ENTRY_STUDY_PROGRESS.MEMBER_ID.eq(memberId)
                            .and(REMODELING_ENTRY_STUDY_PROGRESS.SUBJECT_ID.eq(subjectId)))
                    .limit(1)
                    .fetchOptional(r -> r.into(REMODELING_ENTRY_STUDY_PROGRESS).into(RemodelingEntryStudyProgress.class))
                    .orElse(null)
            );
            if (remodelingEntryStudyProgress == null) {
                return;
            }
            if (RemodelingEntryStudyProgress.CERTIFICATE_STATUS_ACQUISITION == remodelingEntryStudyProgress.getIsCertificate()) {
                return;
            }
            if (remodelingEntryStudyProgress.getFinishStatus() != null && RemodelingEntryStudyProgress.FINISH_STATUS_FINISHED != remodelingEntryStudyProgress.getFinishStatus()) {
                remodelingEntryStudyProgress.setFinishStatus(RemodelingEntryStudyProgress.FINISH_STATUS_FINISHED);
            }

            studyProgressCommonDao.execute(x -> x.update(REMODELING_ENTRY_STUDY_PROGRESS)
                    .set(REMODELING_ENTRY_STUDY_PROGRESS.FINISH_STATUS, remodelingEntryStudyProgress.getFinishStatus())
                    .set(REMODELING_ENTRY_STUDY_PROGRESS.IS_CERTIFICATE, RemodelingEntryStudyProgress.CERTIFICATE_STATUS_ACQUISITION)
                    .where(REMODELING_ENTRY_STUDY_PROGRESS.ID.eq(remodelingEntryStudyProgress.getId()))
                    .execute()
            );

        }
    }

}
