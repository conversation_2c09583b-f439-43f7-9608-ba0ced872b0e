package com.zxy.product.course.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.course.api.certificate.CertificateNormalCodeGenerator;
import com.zxy.product.course.api.certificate.CertificateRecordService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.async.util.SplitTableName;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.BusinessCertificate;
import com.zxy.product.course.entity.CertificateRecord;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.SplitTableConfig;
import com.zxy.product.course.util.StringUtils;
import com.zxy.product.exam.api.GridExamService;
import org.jooq.BatchBindStep;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.Tables.BUSINESS_CERTIFICATE;
import static com.zxy.product.course.jooq.Tables.COURSE_INFO;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS;
import static com.zxy.product.course.jooq.tables.CertificateRecord.CERTIFICATE_RECORD;

/**
 * 颁发证书
 */
@Component
public class IssueCertificateRecordListener extends AbstractMessageListener {
    private static final Logger logger = LoggerFactory.getLogger(IssueCertificateRecordListener.class);
    private CommonDao<CourseInfo> infoCommonDao;
    private CommonDao<CourseStudyProgress> progressCommonDao;
    private CommonDao<CertificateRecord> recordCommonDao;
    private CertificateRecordService certificateRecordService;
    private CertificateNormalCodeGenerator normalCodeGenerator;
    private GridExamService gridExamService;

    @Autowired
    public void setGridExamService(GridExamService gridExamService) {
        this.gridExamService = gridExamService;
    }
    @Resource
    private CourseCacheService courseCacheService;

    @Autowired
    public void setInfoCommonDao(CommonDao<CourseInfo> infoCommonDao) {
        this.infoCommonDao = infoCommonDao;
    }

    @Autowired
    public void setProgressCommonDao(CommonDao<CourseStudyProgress> progressCommonDao) {
        this.progressCommonDao = progressCommonDao;
    }

    @Autowired
    public void setCertificateRecordService(CertificateRecordService certificateRecordService) {
        this.certificateRecordService = certificateRecordService;
    }

    @Autowired
    public void setRecordCommonDao(CommonDao<CertificateRecord> recordCommonDao) {
        this.recordCommonDao = recordCommonDao;
    }

    @Autowired
    public void setNormalCodeGenerator(CertificateNormalCodeGenerator normalCodeGenerator) {
        this.normalCodeGenerator = normalCodeGenerator;
    }

    @Override
    protected void onMessage(Message message) {
        int type = message.getType();
        String courseId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
        if(Objects.equals(MessageTypeContent.ISSUE_CERTIFICATE_RECORD_FIX, type)){
            //修复自定义发证时间
            fixCourseByCertifiacte();
            return;
        }
        CourseInfo courseInfo = infoCommonDao.getOptional(courseId).orElse(null);
        logger.info("消息类型以及遍历的生产证书的id={}, type={}", courseId, message.getType());
        // 课程暂不发证
        if (courseInfo == null || CourseInfo.BUSINESS_TYPE_COURSE == courseInfo.getBusinessType()) return;
        // 查询是否配置证书
        BusinessCertificate businessCertificate = certificateRecordService.getByBusinessId(courseId);
        if (businessCertificate == null) return;

        // 重塑专区的专题不自动发证书（新做了重塑专区手动发放证书）
        if (Arrays.asList(CourseInfo.IMPORT_CERTIFICATE_SUBJECT_IDS).contains(courseId)) {
            return;
        }

        // 强基专区的专题不自动发证书（新做了强基专区手动发放证书）
        if (Arrays.asList(CourseInfo.IMPORT_CERTIFICATE_SUBJECT_IDS_QJ).contains(courseId)) {
            return;
        }



        switch (type) {
            case MessageTypeContent.ISSUE_CERTIFICATE_RECORD:
                //专题统一不发证，学员手动发证
                if(Objects.equals(courseInfo.getBusinessType(), CourseInfo.BUSINESS_TYPE_SUBJECT)){
                    return;
                }
                String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
                singleIssueCertificate(courseInfo, memberId);
                break;
            case MessageTypeContent.ISSUE_CERTIFICATE_RECORD_MANUAL:
                //学员手动申请发证
                String userId = message.getHeader(MessageHeaderContent.MEMBER_ID);
                singleIssueCertificate(courseInfo, userId);
                break;
            case MessageTypeContent.ISSUE_CERTIFICATE_RECORD_BATCH:
                //专题统一不发证，学员手动发证
                if(Objects.equals(courseInfo.getBusinessType(), CourseInfo.BUSINESS_TYPE_SUBJECT)){
                    return;
                }
                batchIssueCertificate(courseInfo);
                break;
            default:
                break;
        }


    }


    private void fixCourseByCertifiacte(){
        int page = 1;
        int pageSize = 200;
        List<String> certCourse = new ArrayList<>();
        do {
            logger.info("修复证书发布时间-- paga={}",page);
            certCourse = getCertCourse(page, pageSize);
            certCourse.forEach(item->{
                Optional<CertificateRecord> byCourseId = certificateRecordService.findSubjectCertificateByCourseId(item);
                if(byCourseId.isPresent()){
                    //批量处理更新证书颁发时间
                    updateCertTimeByOne(byCourseId.get());
                }
            });
            page ++;
        }while (Objects.equals(certCourse.size(), pageSize));
    }

    //查询所有，存在证书id的已发布的专题
    private List<String> getCertCourse(Integer page, Integer pageSize){
       return infoCommonDao.execute(e->e.select(COURSE_INFO.ID).from(COURSE_INFO)
               .innerJoin(BUSINESS_CERTIFICATE).on(COURSE_INFO.ID.eq(BUSINESS_CERTIFICATE.BUSINESS_ID))
                .where(BUSINESS_CERTIFICATE.BUSINESS_TYPE.eq(BusinessCertificate.BUSINESS_TYPE_SUBJECT),COURSE_INFO.CERTIFICATE_TIME.isNull(),
                        COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT), COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO))
                .orderBy(COURSE_INFO.CREATE_TIME.desc())
                .limit((page-1)*pageSize, pageSize)
                .fetch(COURSE_INFO.ID)
        );
    }
    //拼接sql
    private void updateCertTimeByOne(CertificateRecord recordsByType){
//        List<Query> updates = new ArrayList<>();
        logger.info("修复证书发布时间-- satrt");
        infoCommonDao.execute(e->e.update(COURSE_INFO)
                .set(COURSE_INFO.CERTIFICATE_TYPE,CourseInfo.COURSE_INFO_CERT_TYPE_NOMAL)
                .set(COURSE_INFO.CERTIFICATE_TIME, recordsByType.getFinishTime())
                .set(COURSE_INFO.CERTIFICATE_UPDATE_TIME, recordsByType.getFinishTime())
                .where(COURSE_INFO.ID.eq(recordsByType.getBusinessId())).execute());
        logger.info("修复证书发布时间-- end");
//        return updates;
    }

    //拼接sql
    private void updateCertTime(List<CertificateRecord> recordsByType){
//        List<Query> updates = new ArrayList<>();
        logger.info("修复证书发布时间-- satrt");
        infoCommonDao.execute(ctx->{
                    BatchBindStep batch = ctx.batch(
                            ctx.update(COURSE_INFO)
                                    .set(COURSE_INFO.CERTIFICATE_TYPE, (Integer) null)
                                    .set(COURSE_INFO.CERTIFICATE_TIME, (Long) null)
                                    .set(COURSE_INFO.CERTIFICATE_UPDATE_TIME, (Long) null)
                                    .where(COURSE_INFO.ID.eq((String) null))
                    );
            Map<String, List<CertificateRecord>> listMap = recordsByType.stream().collect(Collectors.groupingBy(CertificateRecord::getBusinessId));
            listMap.forEach((k,v)->{
                        if(!CollectionUtils.isEmpty(v)){
                            //可能手动情况下，存在同一个证书，发送证书时间不一致
                            List<CertificateRecord> records = v.stream().sorted(Comparator.comparing(CertificateRecord::getFinishTime)).collect(Collectors.toList());
                            CertificateRecord record = records.get(0);
                            if(Objects.nonNull(record.getFinishTime())){
                                batch.bind(CourseInfo.COURSE_INFO_CERT_TYPE_NOMAL, record.getFinishTime(),record.getFinishTime(), k);
                            }
                        }
                    });
            return batch.execute();
        });
        logger.info("修复证书发布时间-- end");
//        return updates;
    }

    @Override
    public int[] getTypes() {
        return new int[] {
                MessageTypeContent.ISSUE_CERTIFICATE_RECORD,
                MessageTypeContent.ISSUE_CERTIFICATE_RECORD_BATCH,
                MessageTypeContent.ISSUE_CERTIFICATE_RECORD_MANUAL,
                MessageTypeContent.ISSUE_CERTIFICATE_RECORD_FIX
        };
    }

    private void batchIssueCertificate(CourseInfo courseInfo) {
        int page = 1;
        boolean flag = true;
        logger.info("开始遍历的时间，time={}", System.currentTimeMillis());

        while(flag) {
            int finalRuselt = (page-1) * 1000;
            List<CourseStudyProgress> progressList = progressCommonDao.execute(x -> x.select(
                    COURSE_STUDY_PROGRESS.ID,
                    COURSE_STUDY_PROGRESS.MEMBER_ID,
                    COURSE_STUDY_PROGRESS.FINISH_TIME,
                    COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME
            ).from(COURSE_STUDY_PROGRESS)
                    .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(courseInfo.getId())
                            .and(COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(CourseStudyProgress.FINISH_STATUS_FINISH).or(COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS))))
                    .orderBy(COURSE_STUDY_PROGRESS.FINISH_TIME.asc())
                    .limit(finalRuselt, 1000))
                    .fetch(record -> {
                        CourseStudyProgress p = new CourseStudyProgress();
                        p.setId(record.getValue(COURSE_STUDY_PROGRESS.ID));
                        p.setMemberId(record.getValue(COURSE_STUDY_PROGRESS.MEMBER_ID));
                        p.setFinishTime(record.getValue(COURSE_STUDY_PROGRESS.FINISH_TIME));
                        p.setLastAccessTime(record.getValue(COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME));
                        return p;
                    });
            List<CertificateRecord> insertList = new ArrayList<CertificateRecord>();
            if (!progressList.isEmpty() && progressList.size() > 0) {
                progressList.forEach(progress -> {
                    // 查询学员在当前业务中是否有证书
                    String id = recordCommonDao.execute(x -> x.select(CERTIFICATE_RECORD.ID).from(CERTIFICATE_RECORD).where(CERTIFICATE_RECORD.MEMBER_ID.eq(progress.getMemberId()).and(CERTIFICATE_RECORD.BUSINESS_ID.eq(courseInfo.getId())))).fetchOptional(CERTIFICATE_RECORD.ID).orElse(null);
                    if (ObjectUtils.isEmpty(id)) {
                        CertificateRecord record = new CertificateRecord();
                        record.forInsert();
                        record.setAccessType(CertificateRecord.ACCESS_TYPE_AUTO);
                        record.setBusinessId(courseInfo.getId());
                        record.setMemberId(progress.getMemberId());
                        record.setFinishTime(progress.getFinishTime() == null ? System.currentTimeMillis() : progress.getFinishTime());
                        record.setType(CertificateRecord.ISSUE_CERTIFICATE_TYPE_QUALIFIED);
                        record.setIssueTime(StringUtils.stringDate(record.getFinishTime()));
                        record.setBusinessType(CertificateRecord.BUSINESS_TYPE_SUBJECT);
                        // 专题证书默认颁证机构为“中国移动网上人才发展中心”
                        record.setIssueAgency("中国移动网上人才发展中心");
                        try {
                            String code = normalCodeGenerator.getCodeForNormalSubject(CertificateRecord.BUSINESS_TYPE_SUBJECT, BusinessCertificate.CERTIFICATE_NUMBER_PREFIX_SUBJECT, StringUtils.stampToDate(record.getFinishTime(), 0));
                            record.setCertificateNumber(code);
                        } catch (Exception e) {
                            logger.error("批量生成证书编码异常，需重新处理， subjecId={}, memberId={}", courseInfo.getId(), progress.getMemberId());
                            e.printStackTrace();
                        }
                        insertList.add(record);
                    }

                });
                certificateRecordService.insertRecordList(insertList);

            }
            if (progressList.isEmpty() || progressList.size() < 1000) {
                flag = false;
            }
            page++;
        }
    }

    private void singleIssueCertificate(CourseInfo courseInfo, String memberId) {
        logger.info("开始生成证书， courseId={}, memberId ={}", courseInfo.getId(), memberId);
        TableImpl<?> cacheTable = SplitTableName.getTableNameByCode(courseCacheService.getCacheTableName(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS));
        CourseStudyProgress progress = progressCommonDao.execute(x -> x.select(
                        cacheTable.field("f_id",String.class),
                        cacheTable.field("f_member_id",String.class),
                        cacheTable.field("f_finish_time",Long.class),
                        cacheTable.field("f_last_access_time",Long.class)
        ).from(cacheTable)
                .where(cacheTable.field("f_course_id",String.class).eq(courseInfo.getId())
                        .and(cacheTable.field("f_member_id",String.class).eq(memberId)))
                .fetchOne(r -> {
                    CourseStudyProgress p = new CourseStudyProgress();
                    p.setId(r.getValue(cacheTable.field("f_id",String.class)));
                    p.setMemberId(r.getValue(cacheTable.field("f_member_id",String.class)));
                    p.setFinishTime(r.getValue(cacheTable.field("f_finish_time",Long.class)));
                    p.setLastAccessTime(r.getValue(cacheTable.field("f_last_access_time",Long.class)));
                    return p;
                }));
        if (progress == null) {
            logger.info("找不到进度表数据， courseId={}， memberId={}", courseInfo.getId(), memberId);
            return ;
        }
        // 查询学员在当前业务中是否获得证书
        String id = recordCommonDao.execute(x -> x.select(CERTIFICATE_RECORD.ID).from(CERTIFICATE_RECORD).where(CERTIFICATE_RECORD.MEMBER_ID.eq(progress.getMemberId()).and(CERTIFICATE_RECORD.BUSINESS_ID.eq(courseInfo.getId())))).fetchOptional(CERTIFICATE_RECORD.ID).orElse(null);
        if (ObjectUtils.isEmpty(id)) {
            CertificateRecord record = new CertificateRecord();
            record.forInsert();
            record.setAccessType(CertificateRecord.ACCESS_TYPE_AUTO);
            record.setBusinessId(courseInfo.getId());
            record.setMemberId(progress.getMemberId());
            record.setFinishTime(progress.getFinishTime() == null ? System.currentTimeMillis() : progress.getFinishTime());
            record.setType(CertificateRecord.ISSUE_CERTIFICATE_TYPE_QUALIFIED);
            //专题自定义颁布时间
            if(Objects.equals(courseInfo.getBusinessType(), CourseInfo.BUSINESS_TYPE_SUBJECT)){
                record.setFinishTime(Objects.equals(courseInfo.getCertificateType(), CourseInfo.COURSE_INFO_CERT_TYPE_CUSTOM) ? courseInfo.getCertificateTime() : System.currentTimeMillis());
                record.setIssueTime(Objects.equals(courseInfo.getCertificateType(), CourseInfo.COURSE_INFO_CERT_TYPE_CUSTOM) ? StringUtils.stringDate(courseInfo.getCertificateTime()) : StringUtils.stringDate(System.currentTimeMillis()));
            }else {
                record.setIssueTime(StringUtils.stringDate(record.getFinishTime()));
            }
            record.setBusinessType(CertificateRecord.BUSINESS_TYPE_SUBJECT);
            // 专题证书默认颁证机构为“中国移动网上人才发展中心”
            record.setIssueAgency("中国移动网上人才发展中心");
            try {
                String code = normalCodeGenerator.getCodeForNormalSubject(CertificateRecord.BUSINESS_TYPE_SUBJECT, BusinessCertificate.CERTIFICATE_NUMBER_PREFIX_SUBJECT, StringUtils.stampToDate(record.getFinishTime(), 0));
                record.setCertificateNumber(code);
            } catch (Exception e) {
                logger.error("批量生成证书编码异常，需重新处理， subjecId={}, memberId={}", courseInfo.getId(), progress.getMemberId());
                e.printStackTrace();
            }
            certificateRecordService.insertRecordList(Arrays.asList(record));


        }
    }

}
