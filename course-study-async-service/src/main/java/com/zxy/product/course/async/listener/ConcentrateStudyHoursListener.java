package com.zxy.product.course.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.course.api.CourseChapterInfoService;
import com.zxy.product.course.api.CourseStudyProgressService;
import com.zxy.product.course.api.subject.SubjectService;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.exam.content.MessageHeaderContent;
import com.zxy.product.history.api.HistoryCourseStudyProgressService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;


@Component
public class ConcentrateStudyHoursListener extends AbstractMessageListener {

    private static final Logger LOGGER = LoggerFactory.getLogger(ConcentrateStudyHoursListener.class);

    private CourseChapterInfoService courseChapterInfoService;
    private CourseStudyProgressService courseStudyProgressService;
    private HistoryCourseStudyProgressService historyCourseStudyProgressService;
    private SubjectService subjectService;

    @Autowired
    public void setCourseChapterInfoService(CourseChapterInfoService courseChapterInfoService) {
        this.courseChapterInfoService = courseChapterInfoService;
    }

    @Autowired
    public void setCourseStudyProgressService(CourseStudyProgressService courseStudyProgressService) {
        this.courseStudyProgressService = courseStudyProgressService;
    }

    @Autowired
    public void setHistoryCourseStudyProgressService(HistoryCourseStudyProgressService historyCourseStudyProgressService) {
        this.historyCourseStudyProgressService = historyCourseStudyProgressService;
    }

    @Autowired
    public void setSubjectService(SubjectService subjectService) {
        this.subjectService = subjectService;
    }

    @Override
    public void onMessage(Message message) {
        LOGGER.info("course/ConcentrateStudyHoursListener:{}", message.toString());

        switch (message.getType()) {
            case MessageTypeContent.CONCENTRATE_STUDY_HOURS:
                String subjectId = message.getHeader(MessageHeaderContent.ID);
                String params = message.getHeader(MessageHeaderContent.PARAMS);
                updateConcentrateStudyHours(subjectId,params);
                break;
            case MessageTypeContent.CONCENTRATE_STUDY_HOURS_MEMBER:
                String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
                String businessId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
                updateConcentrateStudyHoursByMember(businessId,memberId);
                break;
            default:
                break;
        }

    }
    private void updateConcentrateStudyHoursByMember(String subjectId,String memberId) {

        if(subjectService.isNetworkSubject(subjectId)){
            // 获取专题下所有必修课程及子专题
            List<CourseChapterSection> courseChapterSections = getAllCourseChapterSection(subjectId);

            Map<String, Integer> sectionIdAndConcentrateHoursMap = studyHoursConvert(courseChapterSections);

            Set<String> courseIds = sectionIdAndConcentrateHoursMap.keySet();

            courseStudyProgressService.updateConcentrateStudyHoursByMember(memberId,new ArrayList<>(courseIds), subjectId, sectionIdAndConcentrateHoursMap);
        }
    }

    private void updateConcentrateStudyHours(String subjectId, String params) {

            // 获取专题下所有必修课程及子专题
            List<CourseChapterSection> courseChapterSections = getAllCourseChapterSection(subjectId);

            Map<String, Integer> sectionIdAndConcentrateHoursMap;

            if ("RECALCULATE_CONCENTRATE".equals(params)) {
                sectionIdAndConcentrateHoursMap = studyHoursConvert(courseChapterSections);
            } else {
                sectionIdAndConcentrateHoursMap = courseChapterSections.stream()
                                                                       .collect(Collectors.toMap(
                                                                               CourseChapterSection::getResourceId,
                                                                               section -> 0
                                                                       ));
            }
            Set<String> courseIds = sectionIdAndConcentrateHoursMap.keySet();

            courseStudyProgressService.updateConcentrateStudyHours(new ArrayList<>(courseIds), subjectId, sectionIdAndConcentrateHoursMap);

            historyCourseStudyProgressService.updateConcentrateStudyHours(new ArrayList<>(courseIds), subjectId, sectionIdAndConcentrateHoursMap);
    }


    private List<CourseChapterSection> getCourseChapterSections(String subjectId) {
        return courseChapterInfoService.getRequiredCourseByCourseId(Collections.singletonList(subjectId));
    }

    private List<CourseChapterSection> getAllCourseChapterSection(String subjectId) {
        // 获取专题下所有必修课程及子专题
        List<CourseChapterSection> courseChapterSections = getCourseChapterSections(subjectId);

        //子专题ID
        List<String> innerSubjectIds = courseChapterSections.stream().filter(courseChapterSection -> CourseChapterSection.SECTION_TYPE_SUBJECT == courseChapterSection.getSectionType())
                                                            .map(CourseChapterSection::getResourceId).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(innerSubjectIds)){
            //如果包含子专题， 查询子专题内必修课程
            List<CourseChapterSection> innerCourseChapterSections = courseChapterInfoService.getRequiredCourseByCourseId(innerSubjectIds);
            courseChapterSections.addAll(innerCourseChapterSections);
        }

        return courseChapterSections;
    }


    // 集中学时计算规则 ：
    // 课程的【课程总时长】大于0分钟 小于等于15分钟时，可获得0.25集中学时
    // 【课程总时长】为大于15分钟  小于等于30分钟时，可获得0.5集中学时；
    // 【课程总时长】大于30分钟时，可获得1集中学时；
    private Map<String, Integer> studyHoursConvert(List<CourseChapterSection> courseChapterSections){
        if (CollectionUtils.isEmpty(courseChapterSections)) {
            return Collections.emptyMap();
        }

        return courseChapterSections.stream()
                                    .filter(Objects::nonNull)
                                    .filter(section -> StringUtils.isNotBlank(section.getResourceId()))
                                    .collect(Collectors.toMap(
                                            CourseChapterSection::getResourceId,
                                            section -> {
                                                int minutes = section.getTimeSecond() / 60;
                                                if (minutes > 0 && minutes <= 15) {
                                                    return 25;
                                                } else if (minutes <= 30) {
                                                    return 50;
                                                } else {
                                                    return 100;
                                                }
                                            }
                                    ));
    }

    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.CONCENTRATE_STUDY_HOURS,
                MessageTypeContent.CONCENTRATE_STUDY_HOURS_MEMBER
        };
    }
}
