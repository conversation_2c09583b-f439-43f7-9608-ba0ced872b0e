package com.zxy.product.course.async.listener;

import com.google.common.collect.Lists;
import com.zxy.common.base.message.Message;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.archived.CourseStudyProgressArchivedService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.async.util.SplitTableName;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseSectionStudyProgress;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.SplitTableConfig;
import com.zxy.product.course.util.CourseSectionStudyProgressUtil;
import com.zxy.product.human.entity.StudyPlan;
import org.jooq.Condition;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER;
import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER_SECTION;
import static com.zxy.product.course.jooq.Tables.COURSE_INFO;

@Component
public class SubjectSectionProgressUpdateListener extends AbstractMessageListener {

	private static Logger logger = LoggerFactory.getLogger(SubjectSectionProgressUpdateListener.class);
	private static final List<String> ROT_COURSE2024 =Lists.newArrayList(
			"cccb84a1-5538-483b-8a91-593c253c8a46", "9960fa6e-b0d2-4af4-801f-d5cee3fa1615", "cca0d84a-af5d-4a59-87eb-106d284ec2ce",
			"8ac5dfc5-a88f-4ced-b4b0-716f82256b99", "956a8da0-65e1-4c7d-9215-b03ebc511c50", "6009e9bc-b1df-4f37-8ebf-859ef5cd9903",
			"f7440073-a685-4712-b7c1-5fc7e229ba5c", "08f1fbc8-5570-41fc-89f5-9bde63438be3", "6f8e5a0e-ee35-4864-9257-f2d8031424a0",
			"0ca3f39d-25f9-4654-a2cf-4460a05c0ccd", "cfe3154e-f26c-43b0-a9b7-895a267c7f3a", "062a41c4-a5de-4e74-9272-77acb675b042",
			"89e87aa0-29b2-4e8e-8ad6-5d2216551925", "e54b7fa2-615f-40a4-a08d-6563dd8d8dfe", "e548b3cd-2337-4e9e-8097-357f1a32a012",
			"f07efa35-1e88-4028-aff1-ce70b9494f08"
			);
	private CommonDao<CourseStudyProgress> courseStudyProgressDao;
	private CommonDao<CourseSectionStudyProgress> courseSectionStudyProgressDao;
	private CommonDao<CourseChapterSection> chapterSectionDao;
	private CommonDao<CourseInfo> courseInfoDao;
	@Resource
	private CourseStudyProgressArchivedService archivedService;

	@Autowired
	private CourseCacheService courseCacheService;
	@Autowired
	private MessageSender messageSender;

	@Autowired
	public void setCourseStudyProgressDao(CommonDao<CourseStudyProgress> courseStudyProgressDao) {
		this.courseStudyProgressDao = courseStudyProgressDao;
	}


	@Autowired
	public void setCourseSectionStudyProgressDao(CommonDao<CourseSectionStudyProgress> courseSectionStudyProgressDao) {
		this.courseSectionStudyProgressDao = courseSectionStudyProgressDao;
	}

	@Autowired
	public void setChapterSectionDao(CommonDao<CourseChapterSection> chapterSectionDao) {
		this.chapterSectionDao = chapterSectionDao;
	}

	@Autowired
	public void setCourseInfoDao(CommonDao<CourseInfo> courseInfoDao) {
		this.courseInfoDao = courseInfoDao;
	}

	@Override
	protected void onMessage(Message message) {
		String subjectId = message.getHeader(MessageHeaderContent.COURSE_ID);
		String memberId = message.getHeader(MessageHeaderContent.MEMBER_ID);
		Optional<String> finishTimeOptional = Optional.ofNullable(message.getHeader(MessageHeaderContent.FINISHSTIME));
		Long finishTime = finishTimeOptional.isPresent() ? Long.valueOf(finishTimeOptional.get()) : null;
		updateSectionProgress(subjectId, memberId, finishTime);


	}

	private void updateSectionProgress(String subjectId, String memberId, Long finishTime) {
		logger.info("开始更新专题数据subjectId={}, memberId={}", subjectId, memberId);
		TableImpl<?> cacheTable = SplitTableName.getTableNameByCode(courseCacheService.getCacheTableName(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS));
		CourseStudyProgress subjectStudyProgress = Optional.ofNullable(courseStudyProgressDao.execute(dsl ->
				dsl.select(cacheTable.field("f_id", String.class),
								cacheTable.field("f_study_total_time", Integer.class),
								cacheTable.field("f_finish_status", Integer.class),
								cacheTable.field("f_finish_time", Long.class),
								cacheTable.field("f_completed_rate", Integer.class))
						.from(cacheTable)
						.where(cacheTable.field("f_course_id", String.class).eq(subjectId).and(cacheTable.field("f_member_id", String.class).eq(memberId))).limit(1).fetchOne(r -> {
							CourseStudyProgress courseStudyProgress = new CourseStudyProgress();
							courseStudyProgress.setId(r.getValue(cacheTable.field("f_id", String.class)));
							courseStudyProgress.setStudyTotalTime(r.getValue(cacheTable.field("f_study_total_time", Integer.class)));
							courseStudyProgress.setFinishStatus(r.getValue(cacheTable.field("f_finish_status", Integer.class)));
							courseStudyProgress.setFinishTime(r.getValue(cacheTable.field("f_finish_time", Long.class)));
							courseStudyProgress.setCompletedRate(r.getValue(cacheTable.field("f_completed_rate", Integer.class)));
							return courseStudyProgress;
						}))).orElse(null);
		if (subjectStudyProgress == null) {
			return ;
		}
		Integer oldFinishStatus = subjectStudyProgress.getFinishStatus();
		Optional<String> versionId = courseInfoDao.execute(dsl -> dsl.select(COURSE_INFO.VERSION_ID)
		.from(COURSE_INFO).where(COURSE_INFO.ID.eq(subjectId)).fetchOptional(COURSE_INFO.VERSION_ID));
		List<Condition> where = Stream
                .of(Optional.of(COURSE_CHAPTER.COURSE_ID.eq(subjectId)),
                		versionId.map(COURSE_CHAPTER.VERSION_ID::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        if (!versionId.isPresent()) {
			where.add(COURSE_CHAPTER.VERSION_ID.isNull());
		}
        List<CourseChapterSection> sectionList = chapterSectionDao.execute(x -> x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID, COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.SECTION_TYPE)
				.from(COURSE_CHAPTER)
				.leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
				.where(where).and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_COURSE).or(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_SUBJECT)))
				.fetchInto(COURSE_CHAPTER_SECTION).into(CourseChapterSection.class));
        if (sectionList != null && !sectionList.isEmpty()) {
        	List<String> courseIds = sectionList.stream().map(CourseChapterSection::getResourceId).collect(Collectors.toList());
        	List<CourseStudyProgress> courseProgressList = Optional.ofNullable(courseStudyProgressDao.execute(x ->
					x.select(cacheTable.field("f_is_required", Integer.class), cacheTable.field("f_course_id", String.class),
									cacheTable.field("f_member_id", String.class), cacheTable.field("f_begin_time", Long.class), cacheTable.field("f_finish_status", Integer.class),
									cacheTable.field("f_finish_time", Long.class), cacheTable.field("f_study_total_time",Integer.class), cacheTable.field("f_visits",Integer.class))
							.from(cacheTable)
							.where(cacheTable.field("f_course_id", String.class).in(courseIds).and(cacheTable.field("f_member_id",String.class).eq(memberId)))
							.fetch(r -> {
								CourseStudyProgress p = new CourseStudyProgress();
								p.setIsRequired(r.get(cacheTable.field("f_is_required", Integer.class)));
								p.setCourseId(r.get(cacheTable.field("f_course_id", String.class)));
								p.setMemberId(r.get(cacheTable.field("f_member_id", String.class)));
								p.setBeginTime(r.get(cacheTable.field("f_begin_time", Long.class)));
								p.setFinishStatus(r.get(cacheTable.field("f_finish_status", Integer.class)));
								p.setFinishTime(r.get(cacheTable.field("f_finish_time", Long.class)));
								p.setStudyTotalTime(r.get(cacheTable.field("f_study_total_time",Integer.class)));
								p.setVisits(r.get(cacheTable.field("f_visits",Integer.class)));
								return p;
							}))).orElse(new ArrayList<>());
			// archived [1.查询已经归档的课程]
			//2024年反腐课程无需查询归档数据
			List<String> archiveCourseIds = Lists.newArrayList();
			for(String courseId : courseIds) {
				if (!ROT_COURSE2024.contains(courseId) && archivedService.checkExistenceArchived(memberId, courseId)) {
					archiveCourseIds.add(courseId);
				}
			}
        	if (!courseProgressList.isEmpty() || !archiveCourseIds.isEmpty()) {
				List<Integer> addStudyTimeList = new ArrayList<Integer>();
				List<Integer> addVisitsList = new ArrayList<Integer>();
				// update for xdn
				TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId,subjectId);
//				TableImpl<?> csspTable = tableName.getSectionTableName(subjectId, ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS, ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
				List<CourseSectionStudyProgress> addSectionProgressList = new ArrayList<>();
        		courseProgressList.forEach(p -> {
        			// 部分学习记录存储的courseID值为大写字母，导致使用equals过滤数据为null报错
					CourseChapterSection section = sectionList
        					.stream()
        					.filter(s -> s.getResourceId().equalsIgnoreCase(p.getCourseId()))
        					.findFirst().orElse(null);
					String sectionProgressId = courseSectionStudyProgressDao.execute(e->
							e.select(csspTable.field("f_id")).from(csspTable)
							.where(csspTable.field("f_member_id", String.class).eq(memberId),
									csspTable.field("f_section_id", String.class).eq(section.getReferenceId())))
							.fetchOptional(csspTable.field("f_id", String.class)).orElse(null);
//        			String sectionProgressId = courseSectionStudyProgressDao.execute(dsl ->
//							dsl.select(COURSE_SECTION_STUDY_PROGRESS.ID).from(COURSE_SECTION_STUDY_PROGRESS)
//									.where(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.eq(section.getReferenceId()),
//											COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
//									.fetchOptional(COURSE_SECTION_STUDY_PROGRESS.ID).orElse(null));
        			// 不存在章节进度且课程完成状态为已完成时新增章节进度，已存在不做处理
        			if (StringUtils.isEmpty(sectionProgressId)) {
        				CourseSectionStudyProgress sectionProgress = new CourseSectionStudyProgress();
        				sectionProgress.setRequired(p.getIsRequired());
        				sectionProgress.setCourseId(subjectId);
        				sectionProgress.setMemberId(memberId);
        				sectionProgress.setSectionId(section.getReferenceId());
        				sectionProgress.setBeginTime(p.getBeginTime());
        				sectionProgress.setFinishStatus(p.getFinishStatus());
        				sectionProgress.setFinishTime(p.getFinishTime());
        				// 章节类型为课程时才增加时长
        				if(section.getSectionType().equals(CourseChapterSection.SECTION_TYPE_COURSE)) {
							sectionProgress.setStudyTotalTime(p.getStudyTotalTime());
						}
        				sectionProgress.forInsert();
        				sectionProgress.setLastAccessTime(sectionProgress.getCreateTime());
        				// update for xdn
						courseSectionStudyProgressDao.execute(dslContext -> sectionProgress.insert(csspTable, dslContext)).execute();
						addSectionProgressList.add(sectionProgress);
//        				courseStudyProgressService.insertSectionProgress(sectionProgress);
						// 章节类型为课程时才增加时长
						if(section.getSectionType().equals(CourseChapterSection.SECTION_TYPE_COURSE)) {
							addStudyTimeList.add(p.getStudyTotalTime());
						}
						addVisitsList.add(p.getVisits());
        				//记录专题log updated 2019-11-20 log异步更新
						if (section.getSectionType().equals(CourseChapterSection.SECTION_TYPE_COURSE)) {
							messageSender.send(MessageTypeContent.SUBJECT_INSERT_SECTION_STUDY_LOG_AND_DAY,
									MessageHeaderContent.MEMBER_ID, memberId,
									MessageHeaderContent.COURSE_ID, subjectId,
									MessageHeaderContent.BUSINESS_ID, section.getReferenceId(),
									MessageHeaderContent.PARAMS, p.getFinishStatus()+"",
									MessageHeaderContent.STUDYTIME, p.getStudyTotalTime()+"",
									MessageHeaderContent.FINISHSTIME, finishTime == null ? System.currentTimeMillis()+"" : finishTime+""
							);
//							courseProcessService.insertSubjectLog(memberId, subjectId, section.getReferenceId(), p.getFinishStatus(), p.getStudyTotalTime());
						} else {
							// 专题类型的章节不增加时长
							messageSender.send(MessageTypeContent.SUBJECT_INSERT_SECTION_STUDY_LOG_AND_DAY,
									MessageHeaderContent.MEMBER_ID, memberId,
									MessageHeaderContent.COURSE_ID, subjectId,
									MessageHeaderContent.BUSINESS_ID, section.getReferenceId(),
									MessageHeaderContent.PARAMS, p.getFinishStatus()+"",
									MessageHeaderContent.STUDYTIME, 0+"",
									MessageHeaderContent.FINISHSTIME, finishTime == null ? System.currentTimeMillis()+"" : finishTime+""
							);
//							courseProcessService.insertSubjectLog(memberId, subjectId, section.getReferenceId(), p.getFinishStatus(), 0);
						}
        			}
        		});
				Integer addStudyTime = addStudyTimeList.stream().filter(Objects::nonNull).reduce(0, (a, b) -> a+b);
				Integer addVisits = addVisitsList.stream().filter(Objects::nonNull).reduce(0, (a, b) -> a+b);

        		logger.info("新增的时长，和学习次数， addStudyTime={}，addVisits={}", addStudyTime, addVisits);
        		// 新增的章节进度不为空再判断是否为完成，在没有章节变动是不需要判断是否完成
				if (!subjectStudyProgress.isFinish()) {
					// add by wdy 2019-06-24专题中的所有章节如果都完成需将专题状态更新为已完成
					// 查询必修章节的数量
					List<String> reuqeiredReferenceIds = chapterSectionDao.execute(x -> x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID).from(COURSE_CHAPTER_SECTION)
							.leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
							.leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER_SECTION.RESOURCE_ID))
							.where(where).and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED))
							.and(COURSE_INFO.STATUS.notIn(CourseInfo.STATUS_FIVE_SHELVES,CourseInfo.STATUS_THE_SHELVES).or(COURSE_INFO.STATUS.isNull())) //过滤退库数据
							.fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID);
					// 查询必修章节完成数量
					Map<String, String> finishSectionIdsMap = courseSectionStudyProgressDao.execute(x ->
							x.select(csspTable.field("f_section_id")).from(csspTable)
									.where(csspTable.field("f_member_id", String.class).eq(memberId))
									.and(csspTable.field("f_course_id", String.class).eq(subjectId))
									.and(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
											.or(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
									.fetch(csspTable.field("f_section_id", String.class)))
							.stream().collect(Collectors.toMap(r -> r, r -> r));
					List<String> finishList = new ArrayList<>();
					reuqeiredReferenceIds.forEach(r -> {
						if (!StringUtils.isEmpty(finishSectionIdsMap.get(r))) {
							finishList.add(r);
						}
					});
                    // archived [2.添加已经归档的课程到完成的列表中(列表中的值无所谓)]
                    finishList.addAll(archiveCourseIds);
//					Integer finishCount = courseSectionStudyProgressDao.execute(x ->
//							x.select(COURSE_SECTION_STUDY_PROGRESS.ID.count()).from(COURSE_SECTION_STUDY_PROGRESS)
//									.where(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.in(reuqeiredReferenceIds))
//									.and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
//									.and(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH).or(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
//									.fetchOptional(COURSE_SECTION_STUDY_PROGRESS.ID.count()).orElse(0));

					int finishCount = finishList.size();
					int requiredSize = reuqeiredReferenceIds.isEmpty() ? 0 : reuqeiredReferenceIds.size();
					if (requiredSize == finishCount) {
						subjectStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
						subjectStudyProgress.setFinishTime(finishTime == null ? System.currentTimeMillis() : finishTime);
						subjectStudyProgress.setCompletedRate(100);

                        //学习计划完成率
                        logger.info("学习计划完成率100%，专题id：{}, time={}", subjectId,System.currentTimeMillis());
                        messageSender.send(com.zxy.product.human.content.MessageTypeContent.COURSE_STUDY_PLAN_UPDATE,
                                com.zxy.product.human.content.MessageHeaderContent.BUSINESS_ID,subjectId,
                                com.zxy.product.human.content.MessageHeaderContent.MEMBER_ID,memberId,
                                com.zxy.product.human.content.MessageHeaderContent.COMPLETE_TIME, String.valueOf(finishTime),//完成时间
                                com.zxy.product.human.content.MessageHeaderContent.FINISH_STATUS, String.valueOf(StudyPlan.FINISH_STATUS_DONE),//完成状态
                                com.zxy.product.human.content.MessageHeaderContent.REQUIRED_COMPLETE_RATE,"100"//完成率
                        );

					} else {
						subjectStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_STUDY);
						subjectStudyProgress.setCompletedRate(50);

                        //学习计划完成率
                        BigDecimal allCount = BigDecimal.valueOf(requiredSize);//必修章节数
                        BigDecimal finishCount1 = BigDecimal.valueOf(finishCount);//完成数
						int completeRate = 0;
						if (allCount.intValue() != 0){
							completeRate = finishCount1.divide(allCount, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).intValue();//完成率
						}

                        logger.info("学习计划完成率{}，专题id：{}, time={}",subjectId, System.currentTimeMillis());
                        messageSender.send(com.zxy.product.human.content.MessageTypeContent.COURSE_STUDY_PLAN_UPDATE,
                                com.zxy.product.human.content.MessageHeaderContent.BUSINESS_ID,subjectId,
                                com.zxy.product.human.content.MessageHeaderContent.MEMBER_ID,memberId,
                                com.zxy.product.human.content.MessageHeaderContent.FINISH_STATUS,String.valueOf(StudyPlan.FINISH_STATUS_UN_FINISH),//完成状态
                                com.zxy.product.human.content.MessageHeaderContent.REQUIRED_COMPLETE_RATE, String.valueOf(completeRate)//完成率
                        );
					}
					logger.info("处理后专题的状态，finishStatus={}", subjectStudyProgress.getFinishStatus());
				}

				// 更新专题时长、状态
        		if (oldFinishStatus == CourseStudyProgress.FINISH_STATUS_DEFAULT) {
        			courseStudyProgressDao.execute(d -> d.update(cacheTable)
        					.set(cacheTable.field("f_study_total_time",Integer.class), addStudyTime)
        					.set(cacheTable.field("f_finish_status",Integer.class), subjectStudyProgress.getFinishStatus())
        					.set(cacheTable.field("f_finish_time",Long.class), subjectStudyProgress.getFinishTime())
							.set(cacheTable.field("f_visits",Integer.class), addVisits)
        					.set(cacheTable.field("f_begin_time",Long.class),cacheTable.field("f_register_time",Long.class))
        					.set(cacheTable.field("f_last_modify_time",Long.class), System.currentTimeMillis())
							.set(cacheTable.field("f_last_access_time",Long.class), finishTime == null ? System.currentTimeMillis() : finishTime)
							.set(cacheTable.field("f_completed_rate",Integer.class), subjectStudyProgress.getCompletedRate())
        					.where(cacheTable.field("f_id",String.class).eq(subjectStudyProgress.getId()))
        					.execute()
        					);
        		} else {
        			courseStudyProgressDao.execute(d -> d.update(cacheTable)
							.set(cacheTable.field("f_begin_time",Long.class),cacheTable.field("f_register_time",Long.class))
							.set(cacheTable.field("f_study_total_time",Integer.class), DSL.nvl(cacheTable.field("f_study_total_time",Integer.class), 0).add(addStudyTime))
        					.set(cacheTable.field("f_visits",Integer.class), DSL.nvl(cacheTable.field("f_visits",Integer.class), 0).add(addVisits))
							.set(cacheTable.field("f_finish_status",Integer.class), subjectStudyProgress.getFinishStatus())
							.set(cacheTable.field("f_finish_time",Long.class), subjectStudyProgress.getFinishTime())
							.set(cacheTable.field("f_last_modify_time",Long.class), System.currentTimeMillis())
							.set(cacheTable.field("f_last_access_time",Long.class), finishTime == null ? System.currentTimeMillis() : finishTime)
							.set(cacheTable.field("f_completed_rate",Integer.class), subjectStudyProgress.getCompletedRate())
        					.where(cacheTable.field("f_id",String.class).eq(subjectStudyProgress.getId()))
        					.execute()
        					);
        		}
				// 专题引用专题之后
				messageSender.send(MessageTypeContent.SUBJECT_REFERENCE_SUBJECT_PROGRESS_UPDATE,
						MessageHeaderContent.ID, subjectStudyProgress.getId(),
						MessageHeaderContent.FINISHSTIME, System.currentTimeMillis()+"",MessageHeaderContent.MEMBER_ID,memberId);
				// 专题完成后颁发证书
				if (subjectStudyProgress.getFinishStatus() != null &&
						CourseStudyProgress.FINISH_STATUS_FINISH == subjectStudyProgress.getFinishStatus() || CourseStudyProgress.FINISH_STATUS_MARKSUCCESS == subjectStudyProgress.getFinishStatus()) {
					messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD,
							MessageHeaderContent.BUSINESS_ID, subjectId,
							MessageHeaderContent.MEMBER_ID, memberId);
					// add 2020-6-10 CHBN活动颁证书
					messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD_CHBN,
							MessageHeaderContent.ID, subjectStudyProgress.getId(),
							MessageHeaderContent.MEMBER_ID,memberId
					);
				}

				// 专题更新后，发消息更新重塑培训进度
				messageSender.send(MessageTypeContent.REMODELING_TRAIN_PLAN_STUDENT_SING_UP,
						MessageHeaderContent.BUSINESS_ID, subjectId,
						MessageHeaderContent.MEMBER_ID, memberId
				);
				// add 2020-4-24 异步更新studyProgress分表数据
				messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE,
						MessageHeaderContent.ID, subjectStudyProgress.getId(),MessageHeaderContent.MEMBER_ID,memberId);
        	}
        }
	}

	@Override
	public int[] getTypes() {
		return new int [] { MessageTypeContent.SUBJECT_SECTION_STUDY_PROGRESS_ENDTER };
	}

}
