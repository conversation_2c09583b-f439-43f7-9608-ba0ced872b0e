package com.zxy.product.course.async.listener;

import com.google.common.collect.ImmutableMap;
import com.zxy.common.base.message.Message;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.GenseeWebCastService;
import com.zxy.product.course.api.NoticeService;
import com.zxy.product.course.api.StudyTaskService;
import com.zxy.product.course.async.util.DateUtil;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.AudienceMember;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseShelves;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.GbCourseLibrary;
import com.zxy.product.course.entity.GbCourseRecord;
import com.zxy.product.course.entity.GenseeBusiness;
import com.zxy.product.course.entity.GenseeLecturer;
import com.zxy.product.course.entity.GenseeWebCast;
import com.zxy.product.course.entity.Notice;
import com.zxy.product.course.jooq.tables.pojos.GenseeLecturerEntity;
import com.zxy.product.exam.api.ExamService;
import com.zxy.product.exam.api.MarkConfigService;
import com.zxy.product.exam.entity.Exam;
import com.zxy.product.system.api.operation.MessageSendService;
import com.zxy.product.system.api.permission.RoleService;
import com.zxy.product.system.content.MessageConstant;
import org.jooq.Condition;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.Tables.AUDIENCE_MEMBER;
import static com.zxy.product.course.jooq.Tables.AUDIENCE_OBJECT;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS;
import static com.zxy.product.course.jooq.Tables.GENSEE_LECTURER;

/**
 * 发送通知监听
 * Created by TJ on 2017/3/21.
 */
@Service
public class NoticeListener extends AbstractMessageListener {

    private static Logger LOGGER = LoggerFactory.getLogger(NoticeListener.class);

    private CourseInfoService courseInfoService;
    private RoleService roleService;
    private MessageSendService messageSendService;
    private CommonDao<CourseStudyProgress> progressDao;
    private NoticeService noticeService;
    private GenseeWebCastService genseeWebCastService;
    private ExamService examService;
    private MarkConfigService markConfigService;
    private StudyTaskService studyTaskService;
    private CommonDao<AudienceMember> audienceMemberCommonDao;
    private CommonDao<GenseeLecturer> lecturerDao;
    private CommonDao<GenseeWebCast> genseeWebCastDao;
    private CommonDao<GbCourseRecord> gbCourseRecordCommonDao;
    private CommonDao<GbCourseLibrary> gbCourseLibraryCommonDao;

    private static final int PAGE_SIZE = 10000;

    @Autowired
    public void setNoticeService(NoticeService noticeService) {
        this.noticeService = noticeService;
    }


    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }


    @Autowired
    public void setRoleService(RoleService roleService) {
        this.roleService = roleService;
    }

    @Autowired
    public void setMessageSendService(MessageSendService messageSendService) {
        this.messageSendService = messageSendService;
    }

    @Autowired
    public void setProgressDao(CommonDao<CourseStudyProgress> progressDao) {
        this.progressDao = progressDao;
    }

    @Autowired
    public void setGenseeWebCastService(GenseeWebCastService genseeWebCastService) {
        this.genseeWebCastService = genseeWebCastService;
    }

    @Autowired
    public void setExamService(ExamService examService) {
        this.examService = examService;
    }

    @Autowired
    public void setMarkConfigService(MarkConfigService markConfigService) {
        this.markConfigService = markConfigService;
    }

    @Autowired
    public void setStudyTaskService(StudyTaskService studyTaskService) {
        this.studyTaskService = studyTaskService;
    }

    @Autowired
    public void setAudienceMemberCommonDao(CommonDao<AudienceMember> audienceMemberCommonDao) {
        this.audienceMemberCommonDao = audienceMemberCommonDao;
    }

    @Autowired
    public void setLecturerDao(CommonDao<GenseeLecturer> lecturerDao) {
        this.lecturerDao = lecturerDao;
    }

    @Autowired
    public void setGenseeWebCastDao(CommonDao<GenseeWebCast> genseeWebCastDao) {
        this.genseeWebCastDao = genseeWebCastDao;
    }

    @Autowired
    public void setGbCourseRecordCommonDao(CommonDao<GbCourseRecord> gbCourseRecordCommonDao) {
        this.gbCourseRecordCommonDao = gbCourseRecordCommonDao;
    }

    @Autowired
    public void setGbCourseLibraryCommonDao(CommonDao<GbCourseLibrary> gbCourseLibraryCommonDao) {
        this.gbCourseLibraryCommonDao = gbCourseLibraryCommonDao;
    }

    @Override
    protected void onMessage(Message message) {
        LOGGER.info("course-study/NoticeListener:" + message.getType());
        String id = message.getHeader(MessageHeaderContent.ID);
        String businessId = message.getHeader(MessageHeaderContent.BUSINESS_ID);
        String courseId = message.getHeader(MessageHeaderContent.COURSE_ID);
        String memberIds = message.getHeader(MessageHeaderContent.IDS);
        int type = message.getType();
        switch (type) {
            case MessageTypeContent.COURSE_VERSIN_CHANGE: // 课程、专题上架通知
                this.shelvesNotice(id);
                break;
            case MessageTypeContent.NOTICE_INSERT: // 通知
                this.noticeHandler(id, Optional.ofNullable(memberIds));
                break;
            case MessageTypeContent.NOTICE_PARAMS_INSERT: // 根据模板和参数直接进行发送消息操作
                noticeTempletParamsHandler(message);
                break;
            case MessageTypeContent.YSX_NOTICE_PARAMS_INSERT: // 根据模板和参数直接进行发送云视讯的消息操作
                ysxNoticeTempletParamsHandler(message);
                break;
            case MessageTypeContent.GB_NOTICE_PARAMS_INSERT: // 根据模板和参数直接进行发送高标党建的消息操作
                gbdjNoticeTempletParamsHandler(message);
                break;
            case MessageTypeContent.GB_SATISFIED_NOTICE_PARAMS_INSERT: // 根据模板和参数直接进行发送高标党建的消息操作
                gbdjSatisfiedNoticeTempletParamsHandler(message);
                break;
            case MessageTypeContent.STUDY_TASK_SUBMIT_AUDIT: // 用户提交审核发送通知
                submitWorkNoticeAuditMember(courseId, id, businessId);
                break;
            case MessageTypeContent.COURSE_DISAPPEAR:
                noticeSubjectReleaseMember(message);
            default:
                break;
        }
    }

    /**
     * 通知课程相关专题发布人 该专题下架
     * @param message
     */
    private void noticeSubjectReleaseMember(Message message) {
        String courseId = message.getHeader(MessageHeaderContent.ID);
        String senderId = message.getHeader(MessageHeaderContent.NAME);
        String status =  message.getHeader(MessageHeaderContent.COURSE_STATUS);
        List<CourseInfo> subjects =   courseInfoService.getSubjectsBySectionId(Optional.ofNullable(courseId));

          if (!CollectionUtils.isEmpty(subjects)){

              for (CourseInfo courseInfo : subjects) {
                  if (CourseInfo.BUSINESS_TYPE_STUDY_MAP.equals(courseInfo.getBusinessType())){ // 学习地图不发消息
                      return;
                  }
                  String[] tar = new String[1];
                  tar[0] = courseInfo.getCreateMemberId();
                  String[] contentParam = new String[4];
                  contentParam[0] = courseInfo.getName();
                  messageSendService.send(senderId,
                          tar,
                          com.zxy.product.course.content.MessageConstant .COURSE_DISAPPEAR,
                          Optional.ofNullable(courseInfo.getId()),
                          Optional.empty(),
                          Optional.ofNullable(contentParam)
                  );
                  LOGGER.info("课程名称" + courseInfo.getContentName() + "退库信息" + status);
                  if(Objects.nonNull(status) && Objects.equals(Integer.parseInt(status), CourseInfo.STATUS_FIVE_SHELVES)){
                      LOGGER.info("课程名称" + courseInfo.getContentName() + "退库信息" + status);
                      //您好，您创建的专题【{专题名称}】内课程【{退库课程名称}，根据网大资源管理要求，于【{退库时间}】已进行退库处理，请知悉。
                      contentParam[1] = courseInfo.getContentName();
                      contentParam[2] = DateUtil.dateLongToString(System.currentTimeMillis(), DateUtil.YYYY_MM_DD_HH_MM_SS);
                      //发送站内信
                      messageSendService.send(senderId, tar, com.zxy.product.course.content.MessageConstant.COURSE_DISAPPEAR_INNER,
                              String.valueOf(com.zxy.product.course.content.MessageConstant.MESSAGE_TYPE_INNER),
                              Optional.ofNullable(courseInfo.getId()), Optional.empty(), Optional.ofNullable(contentParam));
                  }
              }
          }

    }

    /** 对于落地notice的通知直接进行发送消息操作，直接调用模板不会编辑内容的操作 */
    private void noticeTempletParamsHandler(Message message) {
        String noticeId = message.getHeader(MessageHeaderContent.ID); // 通知id
        String params = message.getHeader(MessageHeaderContent.PARAMS); // 内容参数
        String ids = message.getHeader(MessageHeaderContent.IDS);// 人员id
        Optional<Notice> notice = noticeService.getOptional(noticeId);
        notice.ifPresent(n ->{
            if(!StringUtils.isEmpty(ids)){ // 指定人员 发送
                String[] memberIds = ids.split(",");
                LOGGER.info("ExamNotice指定人员通知，businessType:{},businessId:{}", n.getBusinessType(), n.getBusinessId());
                sendTempletParamsNotice(Arrays.asList(memberIds), n, Optional.ofNullable(params));
                return;
            }
        });
    }

    /** 对于落地notice的通知直接进行发送消息操作，直接调用模板不会编辑内容的操作 */
    private void ysxNoticeTempletParamsHandler(Message message) {
        String id = message.getHeader(MessageHeaderContent.ID); // 通知id
        String doMain = message.getHeader(MessageHeaderContent.PARAMS); // url
        GenseeWebCast gensee = genseeWebCastDao.get(id);
        List<GenseeLecturer> lecturers = lecturerDao.execute(w -> w
                        .select(Fields.start().add(GENSEE_LECTURER).end()) //讲师
                        .from(GENSEE_LECTURER)
                        .where(GENSEE_LECTURER.GENSEE_ID.eq(id)))
                .fetchInto(GenseeLecturer.class);

        List<String> memberIds = new ArrayList<>();
        LOGGER.info("云视讯直播发布通知受众下的所有人, id：{}" , id);
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        // 获取需要发送的受众人
        int count = findCountByBusinessId(id);
        int pages = countPages(count, PAGE_SIZE);
        for (int i = 1; i <= pages; i++) {
            List<AudienceMember> memberList = findByBusinessId(i, id);
            memberIds = memberList.stream().map(AudienceMember::getMemberId).collect(Collectors.toList());

            // 消息通知
            noticeService.insert(
                    Notice.BUSINESS_TYPE_GENSEE,
                    id,
                    Notice.TYPE_PUBLISH_USER,
                    gensee.getOrganizationId(),
                    com.zxy.product.course.content.MessageConstant.YSX_LIVE_PUBLISH,
                    Optional.of(new String[]{
                            lecturers.stream().map(GenseeLecturerEntity::getLecturerName).collect(Collectors.joining("，")),
                            gensee.getSubject(),
                            "云视讯",
                            dateFormat.format(new Date(gensee.getStartTime()))+"~"+dateFormat.format(new Date(gensee.getEndTime())),
                            gensee.getRoomNumber(),
                            gensee.getYsxCode() == null ? "无需密码" : gensee.getYsxCode(),
                            "https://" + doMain + "/#/activity/gensee/detail/" + id
                    }),
                    Optional.of(memberIds.stream().collect(Collectors.joining(","))));
            }

        // 消息通知讲师
        noticeService.insert(
                Notice.BUSINESS_TYPE_GENSEE,
                id,
                Notice.TYPE_PUBLISH_USER,
                gensee.getOrganizationId(),
                com.zxy.product.course.content.MessageConstant.YSX_LIVE_PUBLISH,
                Optional.of(new String[]{
                        lecturers.stream().map(GenseeLecturerEntity::getLecturerName).collect(Collectors.joining("，")),
                        gensee.getSubject(),
                        "云视讯",
                        dateFormat.format(new Date(gensee.getStartTime()))+"~"+dateFormat.format(new Date(gensee.getEndTime())),
                        gensee.getRoomNumber(),
                        gensee.getYsxCode() == null ? "无需密码" : gensee.getYsxCode(),
                        "https://" + doMain + "/#/activity/gensee/detail/" + id
                }),
                Optional.of(lecturers.stream().map(GenseeLecturerEntity::getLecturerId)
                        .filter(Objects::nonNull).collect(Collectors.joining(","))));

        // 消息通知主持人
        noticeService.insert(
                Notice.BUSINESS_TYPE_GENSEE,
                id,
                Notice.TYPE_PUBLISH_USER,
                gensee.getOrganizationId(),
                com.zxy.product.course.content.MessageConstant.YSX_LIVE_PUBLISH,
                Optional.of(new String[]{
                        lecturers.stream().map(GenseeLecturerEntity::getLecturerName).collect(Collectors.joining("，")),
                        gensee.getSubject(),
                        "云视讯",
                        dateFormat.format(new Date(gensee.getStartTime()))+"~"+dateFormat.format(new Date(gensee.getEndTime())),
                        gensee.getRoomNumber(),
                        gensee.getYsxCode() == null ? "无需密码" : gensee.getYsxCode(),
                        "https://" + doMain + "/#/activity/gensee/detail/" + id
                }),
                Optional.of(gensee.getCompanyId()));
    }

    private void gbdjNoticeTempletParamsHandler(Message message) {
        String id = message.getHeader(MessageHeaderContent.ID); // 通知id
        GbCourseRecord gbCourseRecord = gbCourseRecordCommonDao.get(id);
        GbCourseLibrary gbCourseLibrary = gbCourseLibraryCommonDao.get(gbCourseRecord.getCourseId());

        // 消息通知
        noticeService.insert(
                Notice.BUSINESS_TYPE_COURSE,
                id,
                Notice.TYPE_PUBLISH_USER,
                "1",
                com.zxy.product.course.content.MessageConstant.COURSE_ACCUSE_SCHEDULE,
                Optional.of(new String[]{gbCourseLibrary.getCourseName()}),
                Optional.of(gbCourseRecord.getMemberId()));

    }

    private void gbdjSatisfiedNoticeTempletParamsHandler(Message message) {
        String id = message.getHeader(MessageHeaderContent.ID); // 通知id
        Optional<GbCourseRecord> gbCourseRecord = gbCourseRecordCommonDao.getOptional(id);
        if (gbCourseRecord.isPresent()) {
            Optional<GbCourseLibrary> gbCourseLibrary = gbCourseLibraryCommonDao.getOptional(gbCourseRecord.get().getCourseId());
            if (gbCourseLibrary.isPresent()) {
                // 消息通知
                noticeService.insert(
                        Notice.BUSINESS_TYPE_COURSE,
                        id,
                        Notice.TYPE_PUBLISH_USER,
                        "1",
                        com.zxy.product.course.content.MessageConstant.COURSE_ACCUSE_SCHEDULE_SATISFIED,
                        Optional.of(new String[]{gbCourseLibrary.get().getCourseName()}),
                        Optional.of(gbCourseRecord.get().getMemberId()));
            } else {
                LOGGER.error("高标党建课程库课程不存在，GbCourseRecord id：{}", id);
            }
        }

    }

    /** 发送消息通知 */
    private void noticeHandler(String noticeId, Optional<String> ids) {
        Optional<Notice> examNotice = noticeService.getOptional(noticeId);
        examNotice.ifPresent(n ->{
            if (n.getNoticeUser() == Notice.NOTICE_NO) return;// 不需要发送消息
            if(ids.isPresent() && !StringUtils.isEmpty(ids.get())){ // 指定人员 发送(直播报名通知等特殊场景)
                String[] memberIds = ids.get().split(",");
                sendNotice(Arrays.asList(memberIds), n);
                return;
            }
            // 没有指定人员，根据消息类型与业务类型决定要发送的用户对象
            if(n.getNoticeType() == null || n.getBusinessType() == null || StringUtils.isEmpty(n.getBusinessId()))// 没有消息类型和业务数据
                return;
            switch (n.getNoticeType().intValue()) {// 消息类型
            case Notice.TYPE_PUBLISH_USER: // 发布通知用户
                sendPublishUserNotice(n);
                break;
            default:
                break;
            }
        });
    }

    /** 发布通知用户 */
    private void sendPublishUserNotice(Notice notice) {
        String businessId = notice.getBusinessId();
        List<String> memberIds = new ArrayList<>();
        if(Notice.BUSINESS_TYPE_GENSEE == notice.getBusinessType()){ // 直播
            LOGGER.info("直播发布通知受众下的所有人, id：{}" , businessId);
            // 获取需要发送的受众人
            int count = findCountByBusinessId(businessId);
            int pages = countPages(count, PAGE_SIZE);
            for (int i = 1; i <= pages; i++) {
                List<AudienceMember> memberList = findByBusinessId(i, businessId);
                memberIds = memberList.stream().map(AudienceMember::getMemberId).collect(Collectors.toList());
                // 直播发布还需要通知，考试评卷人
                noticeExamMark(businessId);
            }

        }
        sendNotice(memberIds, notice);
    }

    /** 直播发布还需要通知，考试评卷人 */
    private void noticeExamMark(String businessId) {
        List<GenseeBusiness> examBusiness = genseeWebCastService.findExamsById(businessId);
        GenseeWebCast gensee = genseeWebCastService.getSimpleData(businessId);
        List<String> examIds = examBusiness.stream().map(b -> b.getBusinessId()).collect(Collectors.toList());
        examIds.forEach(e -> {
            // 调用exam API查询阅卷老师
            Optional<String> tearcherIds = markConfigService.findMarkTeacherIds(e);
            tearcherIds.ifPresent(t -> {
                Exam exam = examService.getSimpleData(e);
                Notice examNotice = new Notice();
                examNotice.setOrganizationId(gensee.getOrganizationId());
                examNotice.setBusinessId(businessId);
                examNotice.setTempletCode(MessageConstant.LIVE_EXAM_PLAN);
                String[] params = new String[]{gensee.getSubject(), exam.getName()}; // 您好，您被设置为【直播名称】中的考试【考试名称】的评卷老师，如有学员提交考试，敬请评卷，谢谢。
                sendTempletParamsNotice(Arrays.asList(t.split(",")), examNotice, Optional.of(Arrays.asList(params).stream().collect(Collectors.joining(","))));
            });
        });
    }

    /** 分批发送消息通知:消息内容直接发送 */
    private void sendNotice(List<String> memberIds, Notice notice) {
        int count = memberIds.size();
        boolean hasSendType = !StringUtils.isEmpty(notice.getSendType()); // 是否是自定消息发送类型的，true代表是
        LOGGER.info("本次需要通知的人数：{}，共分{}批发送" , count);
        if(hasSendType){ // 自定义消息发送类型的
            messageSendService.send(notice.getCreateMemberId(),
                    memberIds.toArray(new String[memberIds.size()]),
                    notice.getTempletCode(),
                    notice.getNoticeUserContent(),
                    notice.getNoticeUserText(),
                    notice.getSendType(),
                    Optional.of(notice.getBusinessId()),
                    Optional.empty(), Optional.empty());
        }else{
            messageSendService.send(
                    notice.getCreateMemberId(),
                    memberIds.toArray(new String[memberIds.size()]),
                    notice.getTempletCode(),
                    notice.getNoticeUserContent(),
                    notice.getNoticeUserText(),
                    Optional.of(notice.getBusinessId()),
                    Optional.empty(), Optional.empty());
        }
    }

    /** 发送消息通知 ：根据模板和参数发送*/
    private void sendTempletParamsNotice(List<String> memberIds, Notice notice, Optional<String> params) {
        LOGGER.info("notice根据模板和参数直接发送通知，templetCode:{},businessId:{}", notice.getTempletCode(), notice.getBusinessId());
        messageSendService.sendSystem(notice.getOrganizationId(), memberIds.toArray(new String[memberIds.size()]),
                notice.getTempletCode(), Optional.of(notice.getBusinessId()),
                Optional.empty(), params.map(p -> p.split(",")));
    }


    /**
     * 课程/专题发布通知
     * @param id
     */
    private void shelvesNotice(String id){
        courseInfoService.getShelves(id).ifPresent(shelves -> {
            String courseId = shelves.getCourseId();
            courseInfoService.getOptional(courseId).ifPresent(course -> {
                // 通知用户
                noticeBusinessUser(shelves, course);
                // 通知课程管理员
                noticeCourseManager(shelves, course);
            });
        });
    }

    /**
     * 课程/专题发布or重新发布时发送通知
     * @param shelves
     * @param course
     */
    private void noticeBusinessUser(CourseShelves shelves, CourseInfo course){
        Map<Integer, String> templateCodeMap = ImmutableMap.of(CourseInfo.BUSINESS_TYPE_COURSE, MessageConstant.COURSE_PUBLISH,
            CourseInfo.BUSINESS_TYPE_SUBJECT, MessageConstant.SUBJECT_PUBLISH,
            CourseInfo.BUSINESS_TYPE_STUDY_MAP, MessageConstant.STUDY_MAP_PUBLISH
        );
        if(shelves.getNoticeUser() != null && shelves.getNoticeUser().equals(CourseShelves.NOTICE_YES)) {
            String[] memberIds = null;
            String templateCode = templateCodeMap.getOrDefault(course.getBusinessType(), MessageConstant.COURSE_PUBLISH);
            Optional<String> businessId = StringUtils.isEmpty(course.getUrl()) ? Optional.of(course.getId()) : Optional.empty();
            // 需要通知 且 不影响学习中 的场景只发生在第一次定向发布or第一次正式发布
            if(shelves.getIsFirst() != null && shelves.getIsFirst().equals(CourseShelves.IS_FIRST_YES)) {
                LOGGER.info("首次发布通知受众下的所有人, courseId：{}" , course.getId());
                // 获取需要发送的受众人
                int count = findCountByBusinessId(course.getId());
                int pages = countPages(count, PAGE_SIZE);
                for (int i = 1; i <= pages; i++) {
                    List<AudienceMember> memberList = findByBusinessId(i, course.getId());
                    memberIds = memberList.stream().map(AudienceMember::getMemberId).toArray(String[]::new);
                    if(memberIds!=null && memberIds.length > 0) {
                        messageSendService.send(shelves.getCreateMemberId(), memberIds,  templateCode,
                                shelves.getNoticeUserContent(), shelves.getNoticeUserText(), businessId, Optional.empty(), Optional.empty());
                    }
                }
            } else {
                LOGGER.info("重新发布通知注册课程的人, courseId：{}" , course.getId());
                Condition condition = shelves.getRule().equals(CourseShelves.RULE_AFFECT) ?
                        COURSE_STUDY_PROGRESS.FINISH_STATUS.notIn(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS) :
                        DSL.trueCondition();
                templateCode = course.getBusinessType() != null && course.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_SUBJECT)
                        ? MessageConstant.SUBJECT_UPDATE : MessageConstant.COURSE_UPDATE;
                memberIds = progressDao.execute(dao -> dao.selectDistinct(COURSE_STUDY_PROGRESS.MEMBER_ID)
                        .from(COURSE_STUDY_PROGRESS)
                        .where(condition.and(COURSE_STUDY_PROGRESS.COURSE_ID.eq(course.getId())))
                        .fetch(COURSE_STUDY_PROGRESS.MEMBER_ID)).toArray(new String[]{});
                if(memberIds!=null && memberIds.length > 0) {
                    messageSendService.send(shelves.getCreateMemberId(), memberIds,  templateCode,
                            shelves.getNoticeUserContent(), shelves.getNoticeUserText(), businessId, Optional.empty(), Optional.empty());
                }
            }


            // 通知考试评卷人
            noticeExamMarkUser(course);
            // 通知作业评审人
            noticeWorkMarkUser(course);
        }
    }


    /**
     * 课程发布通知下级管理员
     * @param shelves
     * @param courseInfo
     */
    private void noticeCourseManager(CourseShelves shelves, CourseInfo courseInfo){
        if(shelves.getNoticeManager() != null  && shelves.getNoticeManager().equals(CourseShelves.NOTICE_YES)) {
            LOGGER.info("发布通知管理员, courseId：{}" , courseInfo.getId());
            List<com.zxy.product.system.entity.Member> members =roleService.findMemberWithRole(shelves.getCreateMemberId(),
                    CourseInfoService.URI,
                    courseInfo.getOrganizationId());
            if(members!=null && !members.isEmpty()){
                String[] memberIds = members.stream().map(com.zxy.product.system.entity.Member::getId).toArray(String[]::new);
                messageSendService.send(shelves.getCreateMemberId(), memberIds,  MessageConstant.COURSE_PUBLISH,
                        shelves.getNoticeManagerContent(), shelves.getNoticeManagerText(), Optional.of(courseInfo.getId()), Optional.empty(), Optional.empty());
            }

        }
    }

    /**
     * 课程/专题包含考试时通知考试评卷人
     * @param course
     */
    private void noticeExamMarkUser(CourseInfo course) {
        List<CourseChapterSection> sectionList = courseInfoService.getChapterSectionBySectionType(course.getId(), Optional.ofNullable(course.getVersionId()), CourseChapterSection.SECTION_TYPE_EXAM);
        if(sectionList == null || sectionList.isEmpty()) return;
        sectionList.stream().forEach(section -> {
            String examId = section.getResourceId();
            // 调用exam API查询阅卷老师
            Optional<String> teacherIds = markConfigService.findMarkTeacherIds(examId);
            teacherIds.ifPresent(t -> {
                String templateCode = MessageConstant.COURSE_EXAM_ARRANGE;
                if(course.getBusinessType()!=null && course.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
                    templateCode = MessageConstant.SUBJECT_EXAM_EVALUATION;
                }
                Exam exam = examService.getSimpleData(examId);
                Notice examNotice = new Notice();
                examNotice.setOrganizationId(course.getOrganizationId());
                examNotice.setBusinessId(course.getId());
                examNotice.setTempletCode(templateCode);
                String[] params = new String[]{course.getName(), exam.getName()};
                sendTempletParamsNotice(Arrays.asList(t.split(",")), examNotice, Optional.of(Arrays.asList(params).stream().collect(Collectors.joining(","))));
            });
        });
    }

    /**
     * 课程/专题发布包含作业时通知作业评审人
     * @param course
     */
    private void noticeWorkMarkUser(CourseInfo course) {
        List<CourseChapterSection> sectionList = courseInfoService.getChapterSectionBySectionType(course.getId(),
                Optional.ofNullable(course.getVersionId()), CourseChapterSection.SECTION_TYPE_TASK);
        if(sectionList == null || sectionList.isEmpty()) return;
        sectionList.stream().forEach(section -> {
            String workId = section.getResourceId();
            String templateCode = MessageConstant.COURSE_HOMEWORK_REVIEWER;
            if(course.getBusinessType()!=null && course.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
                templateCode = MessageConstant.SUBJECT_HOMEWORK_REVIEWER;
            }
            List<String> memberIds = studyTaskService.findAuditMemberByWorkId(workId);
            if(memberIds!=null && !memberIds.isEmpty()){
                messageSendService.sendSystem(course.getOrganizationId(), memberIds.toArray(new String[memberIds.size()]),
                        templateCode, Optional.empty(),
                        Optional.empty(), Optional.of(new String[]{course.getName(), section.getName()}));
            }
        });
    }


    /**
     * 用户提交作业时通知作业评审人
     * @param courseId
     * @param progressId
     */
    private void submitWorkNoticeAuditMember(String courseId, String progressId, String workId) {
        courseInfoService.getOptional(courseId).ifPresent(course -> {
            String templateCode = MessageConstant.COURSE_HOMEWORK_SUBMIT;
            if(course.getBusinessType()!=null && course.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
                templateCode = MessageConstant.SUBJECT_HOMEWORD_DEAL;
            }
            List<String> memberIds = studyTaskService.findAuditMemberByWorkId(workId);
            if(memberIds!=null && !memberIds.isEmpty()){
                messageSendService.sendSystem(course.getOrganizationId(), memberIds.toArray(new String[memberIds.size()]),
                        templateCode, Optional.of(progressId),
                        Optional.empty(), Optional.of(new String[]{course.getName()}));
            }
        });
    }

    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.NOTICE_INSERT,
                MessageTypeContent.NOTICE_PARAMS_INSERT,
                MessageTypeContent.YSX_NOTICE_PARAMS_INSERT,
                MessageTypeContent.STUDY_TASK_SUBMIT_AUDIT,
                MessageTypeContent.COURSE_VERSIN_CHANGE,
                MessageTypeContent.COURSE_DISAPPEAR,
                MessageTypeContent.GB_NOTICE_PARAMS_INSERT,
                MessageTypeContent.GB_SATISFIED_NOTICE_PARAMS_INSERT
        };
    }

    private List<AudienceMember> findByBusinessId(int page, String businessId) {
        return audienceMemberCommonDao.execute(context -> context
                .selectDistinct(AUDIENCE_MEMBER.MEMBER_ID)
                .from(AUDIENCE_MEMBER)
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(businessId)))
                .limit((page - 1) * PAGE_SIZE, PAGE_SIZE)
                .fetchInto(AudienceMember.class);
    }

    private int findCountByBusinessId(String businessId) {
        return audienceMemberCommonDao.execute(context -> context
                .select(AUDIENCE_MEMBER.MEMBER_ID.countDistinct())
                .from(AUDIENCE_MEMBER)
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(businessId)))
                .fetchOne(AUDIENCE_MEMBER.MEMBER_ID.countDistinct());
    }

    private int countPages(int count, int pageSize) {
        return (count / PAGE_SIZE) + ((count % PAGE_SIZE) > 0 ? 1 : 0);
    }

}
