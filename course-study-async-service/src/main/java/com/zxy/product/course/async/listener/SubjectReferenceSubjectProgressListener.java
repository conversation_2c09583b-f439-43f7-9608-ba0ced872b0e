package com.zxy.product.course.async.listener;

import com.zxy.common.base.message.Message;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.consumer.AbstractMessageListener;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.course.CourseProcessService;
import com.zxy.product.course.async.cache.CacheTableName;
import com.zxy.product.course.async.util.SplitTableName;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.util.CourseSectionStudyProgressUtil;
import org.jooq.UpdateSetMoreStep;
import org.jooq.impl.TableImpl;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.Tables.*;

@Component
public class SubjectReferenceSubjectProgressListener extends AbstractMessageListener {
    private static Logger logger = LoggerFactory.getLogger(SubjectReferenceSubjectProgressListener.class);

    private CommonDao<CourseStudyProgress> courseStudyProgressCommonDao;
    private CommonDao<CourseChapterSection> sectionDao;
    private CommonDao<CourseSectionStudyProgress> progressCommonDao;
    private CourseProcessService processService;
    private CourseCacheService courseCacheService;
    private CommonDao<CourseInfo> infoCommonDao;
    @Autowired
    private MessageSender messageSender;
    @Autowired
    private CacheTableName tableName;

    @Autowired
    public void setCourseStudyProgressCommonDao(CommonDao<CourseStudyProgress> courseStudyProgressCommonDao) {
        this.courseStudyProgressCommonDao = courseStudyProgressCommonDao;
    }

    @Autowired
    public void setSectionDao(CommonDao<CourseChapterSection> sectionDao) {
        this.sectionDao = sectionDao;
    }

    @Autowired
    public void setProgressCommonDao(CommonDao<CourseSectionStudyProgress> progressCommonDao) {
        this.progressCommonDao = progressCommonDao;
    }

    @Autowired
    public void setProcessService(CourseProcessService processService) {
        this.processService = processService;
    }

    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }

    @Autowired
    public void setInfoCommonDao(CommonDao<CourseInfo> infoCommonDao) {
        this.infoCommonDao = infoCommonDao;
    }

    @Override
    protected void onMessage(Message message) {
        String courseStudyProgressId = message.getHeader(MessageHeaderContent.ID);
        String memberId= message.getHeader(MessageHeaderContent.MEMBER_ID);
        if (courseStudyProgressId == null) {
            return;
        }
        Long finishTime = Long.valueOf(Optional.ofNullable(message.getHeader(MessageHeaderContent.FINISHSTIME)).orElse("0"));
        this.updateSubject(courseStudyProgressId, finishTime,memberId);
    }

    @Override
    public int[] getTypes() {
        return new int[]{
                MessageTypeContent.SUBJECT_REFERENCE_SUBJECT_PROGRESS_UPDATE
        };
    }

    private void updateSubject(String courseStudyProgressId, Long finishTime, String memberId) {
        TableImpl<?> cacheTable = SplitTableName.getTableNameByCode(courseCacheService.getCacheTableName(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS));
        CourseStudyProgress courseStudyProgress =  courseStudyProgressCommonDao.execute(dsl -> dsl.select(cacheTable.fields()).from(cacheTable).where(cacheTable.field("f_id",String.class).eq(courseStudyProgressId)).fetchOne(r->r.into(CourseStudyProgress.class)));
        // 如果不存在进度则不进行修改
        if (courseStudyProgress == null) {
            return;
        }
        CourseInfo info = infoCommonDao.getOptional(courseStudyProgress.getCourseId()).orElse(null);
        // 如果不存在专题或为课程则不进行修改
        if (info == null || CourseInfo.BUSINESS_TYPE_COURSE == info.getBusinessType()) {
            return;
        }

        logger.info("开始更新引用专题的进度，courseId={}, memberId={}, time={}", courseStudyProgress.getCourseId(), courseStudyProgress.getMemberId(), finishTime);

        // 查询该专题是否被引用 updated 2019-11-20 查询当前章节必选修类型，修改bug，一个专题可能被多个专题引用
        List<CourseChapterSection> courseChapterSectionList = sectionDao.execute(dao -> dao.selectDistinct(COURSE_CHAPTER_SECTION.REFERENCE_ID, COURSE_CHAPTER_SECTION.COURSE_ID, COURSE_CHAPTER_SECTION.REQUIRED).from(COURSE_CHAPTER_SECTION)
                .leftJoin(cacheTable).on(COURSE_CHAPTER_SECTION.COURSE_ID.eq(cacheTable.field("f_course_id", String.class)))
                .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID))
                .where(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(courseStudyProgress.getCourseId())
                        .and(cacheTable.field("f_member_id", String.class).eq(courseStudyProgress.getMemberId())))
                .and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID))
        )
            .fetch(r -> {
                CourseChapterSection cs = new CourseChapterSection();
                cs.setReferenceId(r.get(COURSE_CHAPTER_SECTION.REFERENCE_ID));
                cs.setCourseId(r.get(COURSE_CHAPTER_SECTION.COURSE_ID));
                cs.setRequired(r.get(COURSE_CHAPTER_SECTION.REQUIRED));
                return cs;
            });
        for (CourseChapterSection section:courseChapterSectionList) {
            // 查询父专题是否注册学习
            CourseStudyProgress subjectStudyProgress = courseStudyProgressCommonDao.execute(dsl -> dsl.select(cacheTable.fields()).from(cacheTable)
                    .where(cacheTable.field("f_course_id", String.class).eq(section.getCourseId()), cacheTable.field("f_member_id", String.class).eq(memberId)).limit(1).fetchOne(r->r.into(CourseStudyProgress.class)));
            // 不存在不更新数据
            if(subjectStudyProgress == null) {
                logger.error("专题进度找不到 未开始更新,memberId = {}, courseId = {} ", memberId, section.getCourseId());
                return;
            }

            String referenceId = section.getReferenceId();

            // 获取专题节进度，进行更新
            // update for xdn
            CourseInfo courseInfo = courseCacheService.getCourseByReferenceId(referenceId);
//            TableImpl<?> csspTable = tableName.getSectionTableName(courseInfo.getId(), ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS, ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
            TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId,courseInfo.getId());
            CourseSectionStudyProgress progress = progressCommonDao.execute(e->e.select(csspTable.fields()).from(csspTable)
                    .where(csspTable.field("f_member_id", String.class).eq(memberId),
                            csspTable.field("f_section_id", String.class).eq(referenceId)))
                    .fetchOptional(r-> new CourseSectionStudyProgress().fill(csspTable, r)).orElse(null);
//        CourseSectionStudyProgress progress = progressCommonDao.fetchOne( COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId),
//                COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.eq(referenceId)).orElse(null);
            if(progress ==null) {
                logger.info("找不到节进度，开始插入memberId={},sectionId={}", memberId,referenceId);
                progress = new CourseSectionStudyProgress();
                progress.setMemberId(memberId);
                progress.setSectionId(section.getReferenceId());
                progress.setCourseId(section.getCourseId());
                progress.setFinishStatus(CourseSectionStudyLog.FINISH_STATUS_UNSTART);
            }
            progress.setBeginTime(courseStudyProgress.getBeginTime());
            progress.setCompletedRate(courseStudyProgress.getCompletedRate());
            progress.setFinishStatus(courseStudyProgress.getFinishStatus());
            progress.setLastAccessTime(courseStudyProgress.getLastAccessTime());
            if (progress.getId() == null) {
                progress.forInsert();
                CourseSectionStudyProgress cssp = progress;
                progressCommonDao.execute(dslContext -> cssp.insert(csspTable, dslContext)).execute();
                // progressCommonDao.insert(progress);
            } else {
                CourseSectionStudyProgress cssp = progress;
                progressCommonDao.execute(dslContext -> cssp.updateById(csspTable, dslContext)).execute();
                // progressCommonDao.update(progress);
            }
            // 新增一条log记录方便我的档案查询 updated 2019-11-20 log异步更新
            messageSender.send(MessageTypeContent.SUBJECT_INSERT_SECTION_STUDY_LOG_AND_DAY,
                    MessageHeaderContent.MEMBER_ID, memberId,
                    MessageHeaderContent.COURSE_ID, section.getCourseId(),
                    MessageHeaderContent.BUSINESS_ID, section.getReferenceId(),
                    MessageHeaderContent.PARAMS, courseStudyProgress.getFinishStatus()+"",
                    MessageHeaderContent.STUDYTIME, 0+"",
                    MessageHeaderContent.FINISHSTIME, finishTime+""
            );
//        processService.insertSubjectLog(memberId, courseChapterSection.getCourseId(), courseChapterSection.getReferenceId(), courseStudyProgress.getFinishStatus(), 0);

        // 开始更新引用专题父专题的总进度
        String versionId = subjectStudyProgress.getCourseVersionId();

        if(subjectStudyProgress.getBeginTime()==null) {
            subjectStudyProgress.setBeginTime(progress.getBeginTime());
        }
        boolean isStart = (Integer.valueOf(CourseStudyProgress.FINISH_STATUS_DEFAULT).equals(subjectStudyProgress.getFinishStatus())
                || Integer.valueOf(CourseStudyProgress.FINISH_STATUS_GIVEUP).equals(subjectStudyProgress.getFinishStatus()))
                &&subjectStudyProgress.getBeginTime()!=null;
        if (isStart) {
            subjectStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_STUDY);
        }
        // 设置最后访问的章节
        subjectStudyProgress.setCurrentSectionId(progress.getSectionId());
        subjectStudyProgress.setLastAccessTime(finishTime);

        // 判断专题是否学习完成 updated by 2019-11-15 在章节完成时再更新专题的完成状态，如果为必修章节并且已完成才更新专题的总完成状态
        if (CourseChapterSection.IS_REQUIRED.equals(section.getRequired()) && (CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS.equals(progress.getFinishStatus()) || CourseSectionStudyProgress.FINISH_STATUS_FINISH.equals(progress.getFinishStatus())) &&!subjectStudyProgress.isFinish()) {
            // 1 用户需要必修的进度 = 必修的数量*100
            List<String> referenceIds = sectionDao.execute(x -> x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID).from(COURSE_CHAPTER_SECTION)
                    .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
//                    .leftJoin(COURSE_INFO).on(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(COURSE_INFO.ID))
                    .where(COURSE_CHAPTER.COURSE_ID.eq(subjectStudyProgress.getCourseId())
                            .and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED))
                            .and(COURSE_CHAPTER.VERSION_ID.eq(versionId))
//                            .and(COURSE_INFO.STATUS.notEqual(CourseInfo.STATUS_FIVE_SHELVES)) //过滤退库数据
                    )
                    .fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID);

            // 2 用户当前学习版本必修的章节学习进度百分比
            // update for xdn
            Map<String, String> finishSectionIdsMap = progressCommonDao.execute(x ->
                    x.select(csspTable.field("f_section_id")).from(csspTable)
                            .where(csspTable.field("f_member_id", String.class).eq(memberId))
                            .and(csspTable.field("f_course_id", String.class).eq(subjectStudyProgress.getCourseId()))
                            .and(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
                                    .or(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                            .fetch(csspTable.field("f_section_id", String.class)))
                    .stream().collect(Collectors.toMap(r -> r, r -> r));
            List<String> finishList = new ArrayList<>();
            referenceIds.forEach(r -> {
                if (!StringUtils.isEmpty(finishSectionIdsMap.get(r))) {
                    finishList.add(r);
                }
            });
//            int finishNum = progressCommonDao.execute(x ->
//                    x.select(COURSE_SECTION_STUDY_PROGRESS.ID.count()).from(COURSE_SECTION_STUDY_PROGRESS)
//                            .where(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.in(referenceIds))
//                            .and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
//                            .and(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH).or(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
//                            .fetchOptional(COURSE_SECTION_STUDY_PROGRESS.ID.count()).orElse(0));
                int finishNum = finishList.size() > 0 ? finishList.size() : 0;
                if (referenceIds.size() == finishNum) {
                    subjectStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
                    subjectStudyProgress.setFinishTime(finishTime);
                    subjectStudyProgress.setCompletedRate(100);
                } else {
                    subjectStudyProgress.setCompletedRate(50);
                }
            }

            this.update(subjectStudyProgress,cacheTable);
            logger.info("引用专题更新结束， time={}", System.currentTimeMillis());
            // 状态更新后发消息处理其他业务
            messageSender.send(MessageTypeContent.COURSE_STUDY_PROGRESS_UPDATE,
                    MessageHeaderContent.ID, subjectStudyProgress.getId(),
                    MessageHeaderContent.BUSINESS_ID, subjectStudyProgress.getCourseId()
                    ,MessageHeaderContent.MEMBER_ID,memberId);
            // 专题完成后颁发证书
            if (subjectStudyProgress.getFinishStatus() != null &&
                    CourseStudyProgress.FINISH_STATUS_FINISH == subjectStudyProgress.getFinishStatus() || CourseStudyProgress.FINISH_STATUS_MARKSUCCESS == subjectStudyProgress.getFinishStatus()) {
                messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD,
                        MessageHeaderContent.BUSINESS_ID, subjectStudyProgress.getCourseId(),
                        MessageHeaderContent.MEMBER_ID, subjectStudyProgress.getMemberId());
                // add 2020-6-10 CHBN活动颁证书
                messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD_CHBN,
                        MessageHeaderContent.ID, subjectStudyProgress.getId(),
                        MessageHeaderContent.MEMBER_ID,courseStudyProgress.getMemberId()
                );
            }

            // 专题更新后，发消息更新重塑培训进度
            messageSender.send(MessageTypeContent.REMODELING_TRAIN_PLAN_STUDENT_SING_UP,
                    MessageHeaderContent.BUSINESS_ID, subjectStudyProgress.getCourseId(),
                    MessageHeaderContent.MEMBER_ID, subjectStudyProgress.getMemberId()
            );
            // add 2020-4-24 异步更新studyProgress分表数据
            messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE,
                    MessageHeaderContent.ID, subjectStudyProgress.getId(),MessageHeaderContent.MEMBER_ID,memberId);
            //todo 2025-08-03发版暂时不发消息， 注释，待后续功能完善打开
//            messageSender.send(MessageTypeContent.CONCENTRATE_STUDY_HOURS_MEMBER,
//                               MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId()
//                    ,MessageHeaderContent.MEMBER_ID,memberId);
        }
    }

    private int update(CourseStudyProgress progress, TableImpl<?> cacheTable) {
        UpdateSetMoreStep<?> updateSql = courseStudyProgressCommonDao.execute(x -> x.update(cacheTable)
                .set(cacheTable.field("f_last_access_time", Long.class),progress.getLastAccessTime())
                .set(cacheTable.field("f_current_section_id", String.class),progress.getCurrentSectionId()));

        if(progress.getFinishStatus()!=null) {
            updateSql.set(cacheTable.field("f_finish_status", Integer.class),progress.getFinishStatus());
        }
        if(progress.getFinishTime()!=null) {
            updateSql.set(cacheTable.field("f_finish_time", Long.class),progress.getFinishTime());
        }
        if(progress.getCompletedRate()!=null) {
            updateSql.set(cacheTable.field("f_completed_rate", Integer.class),progress.getCompletedRate());
        }
        if(progress.getBeginTime()!=null) {
            updateSql.set(cacheTable.field("f_begin_time", Long.class),progress.getBeginTime());
        }
        updateSql.set(cacheTable.field("f_last_modify_time", Long.class), System.currentTimeMillis()); // add by wangdongyan 分表使用，最后一次修改时间
        return updateSql.where(cacheTable.field("f_id", String.class).eq(progress.getId())).execute();
    }
}
