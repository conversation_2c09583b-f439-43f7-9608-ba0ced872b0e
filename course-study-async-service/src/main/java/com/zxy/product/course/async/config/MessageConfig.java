package com.zxy.product.course.async.config;

import com.zxy.common.message.CommonMessageConverter;
import com.zxy.common.message.consumer.MessageException;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.common.message.provider.MessageSenderFactory;
import com.zxy.common.serialize.Serializer;
import com.zxy.common.serialize.hessian.HessianSerializer;
import com.zxy.product.course.async.listener.*;
import com.zxy.product.system.content.MessageHeaderContent;
import com.zxy.product.system.content.MessageTypeContent;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.DirectExchange;
import org.springframework.amqp.core.MessageListener;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.rabbit.connection.ConnectionFactory;
import org.springframework.amqp.rabbit.listener.SimpleMessageListenerContainer;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.support.DefaultListableBeanFactory;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.env.Environment;
import org.springframework.util.ErrorHandler;

import java.util.Arrays;

/**
 * <AUTHOR>
 */
@Configuration
public class MessageConfig implements EnvironmentAware, BeanFactoryAware {
    private static final Logger LOGGER = LoggerFactory.getLogger(MessageConfig.class);
    private Environment environment;
    private DefaultListableBeanFactory beanFactory;
    private MessageSender messageSender;

    @Override
    public void setBeanFactory(BeanFactory beanFactory) throws BeansException {
        if (beanFactory instanceof DefaultListableBeanFactory) {
            this.beanFactory = (DefaultListableBeanFactory) beanFactory;
        }
    }

    @Bean
    DirectExchange exchange(Environment env) {
        return new DirectExchange(env.getProperty("spring.rabbitmq.default-exchange"));
    }

//    private String restfulLogQueueName;
    private String memberQueue;
    //    private String organizationQueue;
//    private String organizationDetailQueue;
//    private String grantDetailQueue;
    private String audienceItemQueue;
    private String audienceMemberQueue;
    private String courseVersionQueue;
    private String courseStudyProgressQueue;
    private String knowledgeQueue;
    private String noticeQueue;
    private String courseSyncQueue;
    private String pushMessageQueue;
    private String pushRecordQueue;
    private String businessTopicQueue;
    private String taskQueue;
    private String studyTaskQueue;
    private String scheduleQueue;
    private String courseQueue;
    private String genseeBussinessProgressQueue;
    //    private String scormUnZipQueueName;
    private String genseeWebCastQueue;
    private String courseStatisticsQueue;
    //    private String memberStatisticsQueue;
    private String audienceItemKQueue;
    private String audienceMemberKQueue;
    private String knowledgeStatisticsQueue;
    private String thematicClassQueue;
    private String shardingDataMigrationQueue;
    private String courseInFormQueue;
    private String studyPlanAddNumCalculateQueue;
    private String courseOnlineDistributeQueue;
    private String mentorSynchronousQueue;

    private String miguUserAccess;
    private String concentrateStudyHoursQueue;


    @Override
    public void setEnvironment(Environment env) {
        this.environment = env;
//        restfulLogQueueName = env.getProperty("study.message.queue.restfulLog");
        memberQueue = env.getProperty("study.message.queue.member");
//        organizationQueue = env.getProperty("study.message.queue.organization");
//        organizationDetailQueue = env.getProperty("study.message.queue.organizationDetail");
//        grantDetailQueue = env.getProperty("study.message.queue.grantDetail");
        audienceItemQueue = env.getProperty("study.message.queue.audienceItem");
        audienceMemberQueue = env.getProperty("study.message.queue.audienceMember");
        courseVersionQueue = env.getProperty("study.message.queue.version");
        courseStudyProgressQueue = env.getProperty("study.message.queue.studyProgress");
        knowledgeQueue = env.getProperty("study.message.queue.knowledge");
        noticeQueue = env.getProperty("study.message.queue.notice");
        courseSyncQueue = env.getProperty("study.message.queue.courseSync");
        pushMessageQueue = env.getProperty("study.message.queue.pushMessage");
        pushRecordQueue = env.getProperty("study.message.queue.push.record");
        businessTopicQueue = env.getProperty("study.message.queue.businessTopic");
        taskQueue = env.getProperty("study.message.queue.task");
        studyTaskQueue = env.getProperty("study.message.queue.study.task");
        scheduleQueue = env.getProperty("study.message.queue.schedule");
        courseQueue = env.getProperty("study.message.queue.course");
        genseeBussinessProgressQueue = env.getProperty("study.message.queue.genseeBusinessProgress");
//        scormUnZipQueueName = env.getProperty("study.message.queue.scormUnZip");
        genseeWebCastQueue = env.getProperty("study.message.queue.genseeWebCast");
        courseStatisticsQueue = env.getProperty("study.message.queue.courseStatistics");
//        memberStatisticsQueue = env.getProperty("study.message.queue.memberStatistics");
        audienceItemKQueue = env.getProperty("study.message.queue.audienceItemK");
        audienceMemberKQueue = env.getProperty("study.message.queue.audienceMemberK");
        knowledgeStatisticsQueue = env.getProperty("study.message.queue.knowledge.statistics");
        thematicClassQueue = env.getProperty("study.message.queue.thematic.class");
        shardingDataMigrationQueue = env.getProperty("study.message.queue.shardingDataMigration");
        courseInFormQueue=env.getProperty("study.message.queue.course.inform");
        this.studyPlanAddNumCalculateQueue=env.getProperty("study.message.queue.course.studyPlanNum.calculate");
        miguUserAccess = env.getProperty("study.message.queue.miguUserAccess");
        mentorSynchronousQueue=env.getProperty("study.message.queue.mentor.synchronous");
        courseOnlineDistributeQueue=env.getProperty("study.message.queue.online.distribute");
        concentrateStudyHoursQueue=env.getProperty("study.message.queue.subject.concentrate.hours");
    }

    @Bean
    public MessageSenderFactory messageSenderFactory() {
        return new MessageSenderFactory();
    }

    @Bean
    public MessageSender messageSender(MessageSenderFactory messageSenderFactory, Environment env) {
        messageSender = messageSenderFactory.create(env.getProperty("spring.rabbitmq.default-exchange"));
        return messageSender;
    }

    @Bean
    SimpleMessageListenerContainer memberListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, MemberListener memberListener) {
        String name = "member" + "Queue";
        Queue queue = new Queue(memberQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(memberListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, memberListener, memberQueue);
    }

//    @Bean
//    public SimpleMessageListenerContainer restfulLogListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, RestfulLogListener restfulLogListener) {
//        String name = "restfulLogQueue";
//        Queue queue = new Queue(restfulLogQueueName, true);
//        this.beanFactory.registerSingleton(name, queue);
//
//        Arrays.stream(restfulLogListener.getTypes()).forEach(type -> {
//            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
//        });
//        return createListen(connectionFactory, restfulLogListener, restfulLogQueueName);
//    }

    @Bean
    SimpleMessageListenerContainer audienceItemContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
                                                         AudienceItemListener audienceItemListener) {
        String name = "audienceItemQueue";
        Queue queue = new Queue(audienceItemQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(audienceItemListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, audienceItemListener, audienceItemQueue);
    }

    @Bean
    SimpleMessageListenerContainer audienceItemKContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
                                                          AudienceItemKListener audienceItemKListener) {
        String name = "audienceItemKQueue";
        Queue queue = new Queue(audienceItemKQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(audienceItemKListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, audienceItemKListener, audienceItemKQueue);
    }

    @Bean
    SimpleMessageListenerContainer audienceMemberContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
                                                           AudienceMemberListener audienceMemberListener) {

        String name = "audienceMemberQueue";
        Queue queue = new Queue(audienceMemberQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(audienceMemberListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, audienceMemberListener, audienceMemberQueue);
    }
    @Bean
    SimpleMessageListenerContainer audienceMemberKContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
                                                            AudienceMemberKListener audienceMemberKListener) {

        String name = "audienceMemberKQueue";
        Queue queue = new Queue(audienceMemberKQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(audienceMemberKListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, audienceMemberKListener, audienceMemberKQueue);
    }


    @Bean
    SimpleMessageListenerContainer CourseVersionContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
                                                          CourseVersionListener courseVersionListener) {
        String name = "courseVersionQueue";
        Queue queue = new Queue(courseVersionQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseVersionListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseVersionListener, courseVersionQueue);
    }

    @Bean
    SimpleMessageListenerContainer CourseStudyProgressContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
                                                                CourseStudyProgressListener courseStudyProgressListener) {
        String name = "courseStudyProgressQueue";
        Queue queue = new Queue(courseStudyProgressQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseStudyProgressListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseStudyProgressListener, courseStudyProgressQueue);
    }


    @Bean
    SimpleMessageListenerContainer knowledgeContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
                                                      KnowledgeListener knowledgeListener) {
        String name = "knowledgeQueue";
        Queue queue = new Queue(knowledgeQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(knowledgeListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, knowledgeListener, knowledgeQueue);
    }

    @Bean
    SimpleMessageListenerContainer noticeContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
                                                   NoticeListener noticeListener) {
        String name = "noticeQueue";
        Queue queue = new Queue(noticeQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(noticeListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, noticeListener, noticeQueue);
    }
//    @Bean
//    SimpleMessageListenerContainer PushMessageContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
//                                                        PushMessageListener pushMessageListener) {
//        String name = "pushMessageQueue";
//        Queue queue = new Queue(pushMessageQueue, true);
//        this.beanFactory.registerSingleton(name, queue);
//
//        Arrays.stream(pushMessageListener.getTypes()).forEach(type -> {
//            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
//        });
//        return createListen(connectionFactory, pushMessageListener, pushMessageQueue);
//    }

    @Bean
    SimpleMessageListenerContainer PushRecordContainer(DirectExchange exchange, ConnectionFactory connectionFactory,
                                                       StudyPushRecordListener pushRecordListener) {
        String name = "pushRecordQueue";
        Queue queue = new Queue(pushRecordQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(pushRecordListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, pushRecordListener, pushRecordQueue);
    }

    @Bean
    SimpleMessageListenerContainer businessTopicContainer(DirectExchange exchange, ConnectionFactory connectionFactory, BusinessTopicListener businessTopicListener) {
        String name = "businessTopicQueue";
        Queue queue = new Queue(businessTopicQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(businessTopicListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });

        return createListen(connectionFactory, businessTopicListener, businessTopicQueue);
    }

    @Bean
    SimpleMessageListenerContainer taskContainer(DirectExchange exchange, ConnectionFactory connectionFactory, TaskListener taskListener) {
        String name = "taskQueue";
        Queue queue = new Queue(taskQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(taskListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, taskListener, taskQueue);
    }

    @Bean
    SimpleMessageListenerContainer studyTaskContainer(DirectExchange exchange, ConnectionFactory connectionFactory, StudyTaskListener studyTaskListener) {
        String name = "studyTaskQueue";
        Queue queue = new Queue(studyTaskQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(studyTaskListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, studyTaskListener, studyTaskQueue);
    }

    @Bean
    SimpleMessageListenerContainer scheduleContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ScheduleListener scheduleListener) {
        String name = "scheduleQueue";
        Queue queue = new Queue(scheduleQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(scheduleListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, scheduleListener, scheduleQueue);
    }

    @Bean
    SimpleMessageListenerContainer courseContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseInfoListener courseListener) {
        String name = "courseQueue";
        Queue queue = new Queue(courseQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseListener, courseQueue);
    }

    @Bean
    SimpleMessageListenerContainer genseeBussinessProgressContainer(DirectExchange exchange, ConnectionFactory connectionFactory, GenseeBusinessProgressListener genseeBusinessProgress) {
        String name = "genseeBussinessProgressQueue";
        Queue queue = new Queue(genseeBussinessProgressQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(genseeBusinessProgress.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, genseeBusinessProgress, genseeBussinessProgressQueue);
    }

//    @Bean
//    SimpleMessageListenerContainer scormZipContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ScormUnZipListener scormUnZipListener) {
//        String name = "scormUnZipQueueName";
//        Queue queue = new Queue(scormUnZipQueueName, true);
//        this.beanFactory.registerSingleton(name, queue);
//
//        Arrays.stream(scormUnZipListener.getTypes()).forEach(type -> {
//            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
//        });
//        return createListen(connectionFactory, scormUnZipListener, scormUnZipQueueName);
//    }

    @Bean
    SimpleMessageListenerContainer genseeWebCastContainer(DirectExchange exchange, ConnectionFactory connectionFactory, GenseeWebCastListener genseeWebCastListener) {
        String name = "genseeWebCastQueue";
        Queue queue = new Queue(genseeWebCastQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(genseeWebCastListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, genseeWebCastListener, genseeWebCastQueue);
    }

    @Bean
    SimpleMessageListenerContainer courseStatisticsContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseStatisticsListener courseStatisticsListener) {
        String name = "courseStatisticsQueue";
        Queue queue = new Queue(courseStatisticsQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseStatisticsListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseStatisticsListener, courseStatisticsQueue);
    }

//    @Bean
//    SimpleMessageListenerContainer memberStatisticsContainer(DirectExchange exchange, ConnectionFactory connectionFactory, MemberStatisticsListener memberStatisticsListener) {
//        String name = "memberStatisticsQueue";
//        Queue queue = new Queue(memberStatisticsQueue, true);
//        this.beanFactory.registerSingleton(name, queue);
//        Arrays.stream(memberStatisticsListener.getTypes()).forEach(type -> {
//            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
//        });
//        return createListen(connectionFactory, memberStatisticsListener, memberStatisticsQueue);
//    }

//    @Bean
//    SimpleMessageListenerContainer memberCourseMonthContainer(DirectExchange exchange, ConnectionFactory connectionFactory, MemberCourseMonthListener memberCourseMonthListener) {
//        String name = "memberCourseMonthQueue";
//        String queueName = "zxy-course-memberCourseMonth";
//        Queue queue = new Queue(queueName, true);
//        this.beanFactory.registerSingleton(name, queue);
//
//        Arrays.stream(memberCourseMonthListener.getTypes()).forEach(type -> {
//            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
//        });
//        return createListen(connectionFactory, memberCourseMonthListener, queueName);
//    }

    @Bean
    SimpleMessageListenerContainer audienceItemNameKContainer(DirectExchange exchange, ConnectionFactory connectionFactory, AudienceItemNameKListener audienceItemNameKListener) {
        String name = "audienceItemNameKListenerQueue";
        String queueName = "zxy-course-audienceItem-name";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(audienceItemNameKListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, audienceItemNameKListener, queueName);
    }
    @Bean
    SimpleMessageListenerContainer courseProgressUpdateContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseProgressUpdateNewListener courseProgressUpdateListener) {
        String name = "courseProgressUpdateQueue";
        String queueName = "zxy-course-progress-update-new";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseProgressUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseProgressUpdateListener, queueName);
    }
    @Bean
    SimpleMessageListenerContainer courseBatchStudyLogContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseBatchStudyLogListener courseBatchStudyLogListener) {
        String name = "courseBatchStudyLogQueue";
        String queueName = "zxy-course-batch-study-log";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseBatchStudyLogListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseBatchStudyLogListener, queueName);
    }



    @Bean
    SimpleMessageListenerContainer knowledgeStatisticsContainer(DirectExchange exchange, ConnectionFactory connectionFactory, KnowledgeStatisticsListener knowledgeStatisticsListener) {
        String name = "knowledgeStatisticsQueue";
        Queue queue = new Queue(knowledgeStatisticsQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(knowledgeStatisticsListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });

        return createListen(connectionFactory, knowledgeStatisticsListener, knowledgeStatisticsQueue);
    }

    @Bean
    SimpleMessageListenerContainer thematicClassContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ThematicClassListener thematicClassListener) {
        String name = "thematicClassQueue";
        Queue queue = new Queue(thematicClassQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(thematicClassListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });

        return createListen(connectionFactory, thematicClassListener, thematicClassQueue);
    }

    @Bean
    SimpleMessageListenerContainer subjectProgressUpdateContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SubjectProgressUpdateListener subjectProgressUpdateListener) {
        String name = "courseSubjectProgressUpdate";
        String queueName = "zxy-course-subject-progress-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(subjectProgressUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));

        });
        return createListen(connectionFactory, subjectProgressUpdateListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer subjectProgressUpdateSubContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SubjectProgressUpdateSubListener subjectProgressUpdateSubListener) {
        String name = "courseSubjectProgressSubUpdate";
        String queueName = "zxy-course-subject-progress-sub-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(subjectProgressUpdateSubListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));

        });
        return createListen(connectionFactory, subjectProgressUpdateSubListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer FileBackContainer(DirectExchange exchange, ConnectionFactory connectionFactory, FileBackListener fileBackListener) {
        String name = "fileBackQueueName";
        String queueName = "file-back-convert";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(fileBackListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, fileBackListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer SubjectKnowledgeContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SubjectKnowledgeUpdateListener subjectKnowledgeUpdateListener) {
        String name = "SubjectKnowledgeQueueName";
        String queueName = "subject-knowledge-progress-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(subjectKnowledgeUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, subjectKnowledgeUpdateListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer CompeteCourseVoteContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CompeteCourseVoteListener competeCourseVoteListener) {
        String name = "CompeteCourseVoteQueueName";
        String queueName = "compete-course-vote-trainner";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(competeCourseVoteListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, competeCourseVoteListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer SplitCourseSectionStudyLogContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SplitCourseSectionStudyLogListener splitCourseSectionStudyLogListener) {
        String name = "splitCourseSectionStudyLogQueueName";
        String queueName = "split-course-section-study-log";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(splitCourseSectionStudyLogListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, splitCourseSectionStudyLogListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer SplitSubjectSectionStudyLogContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SplitSubjectSectionStudyLogListener splitSubjectSectionStudyLogListener) {
        String name = "splitSubjectSectionStudyLogQueueName";
        String queueName = "split-subject-section-study-log";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(splitSubjectSectionStudyLogListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, splitSubjectSectionStudyLogListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer CourseStudyUpdateLogMemberDayTimeContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseStudyLogMemberDayUpdateListener courseStudyLogMemberDayUpdateListener) {
        String name = "courseStudyUpdateLogMemberDayTimeQueueName";
        String queueName = "zxy-course-log-member-day-time";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseStudyLogMemberDayUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseStudyLogMemberDayUpdateListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer SubjectSectionProgressUpdateContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SubjectSectionProgressUpdateListener subjectSectionProgressUpdateListener) {
        String name = "subjectSectionProgressUpdateListenerQueueName";
        String queueName = "zxy-course-subject-section-progress-enter";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(subjectSectionProgressUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, subjectSectionProgressUpdateListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer CourseInfoStudyNumberContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseInfoStudyNumberListener courseInfoStudyNumberListener) {
        String name = "courseInfoStudyNumberListenerQueueName";
        String queueName = "zxy-course-info-study-number";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseInfoStudyNumberListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseInfoStudyNumberListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer CourseExceptionContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseExceptionListener courseExceptionListener) {
        String name = "courseExceptionListenerQueueName";
        String queueName = "zxy-course-study-exception";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseExceptionListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseExceptionListener, queueName);
    }


    @Bean
    SimpleMessageListenerContainer SubjectReferenceSubjectContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SubjectReferenceSubjectProgressListener subjectReferenceSubjectListener) {
        String name = "subjectReferenceSubjectListenerQueueName";
        String queueName = "zxy-course-subject-reference-subject-progress";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(subjectReferenceSubjectListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, subjectReferenceSubjectListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer CourseProgressVersionUpdateContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseProgressVersionUpdateListener courseProgressVersionUpdateListener) {
        String name = "courseProgressVersionUpdateListenerQueueName";
        String queueName = "zxy-course-progress-version-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseProgressVersionUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseProgressVersionUpdateListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer SubjectSectionStudyLogAndDayInsertContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SubjectSectionStudyLogAndDayInsertListener subjectSectionStudyLogAndDayInsertListener) {
        String name = "subjectSectionStudyLogAndDayQueueName";
        String queueName = "zxy-course-subject-sectionStudyLogAndDay-insert";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(subjectSectionStudyLogAndDayInsertListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, subjectSectionStudyLogAndDayInsertListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer CourseSectionStudyProgressUpdateContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseSectionStudyProgressUpdateListener courseSectionStudyProgressUpdateListener) {
        String name = "courseSectionStudyProgressUpdateQueueName";
        String queueName = "zxy-course-section-study-progress-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseSectionStudyProgressUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseSectionStudyProgressUpdateListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer IssueCertificateRecordContainer(DirectExchange exchange, ConnectionFactory connectionFactory, IssueCertificateRecordListener issueCertificateRecordListener) {
        String name = "issueCertificateRecordQueueName";
        String queueName = "zxy-course-issue-subject-certificate";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(issueCertificateRecordListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, issueCertificateRecordListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer RemodelingTrainStudyProgressContainer(DirectExchange exchange, ConnectionFactory connectionFactory, RemodelingTrianStudyProgressListener remodelingTrianStudyProgressListener) {
        String name = "remodelingTrainStudyProgressQueueName";
        String queueName = "zxy-course-remodeling-train-study-progress-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(remodelingTrianStudyProgressListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, remodelingTrianStudyProgressListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer SubAuthenticatedStudyProgressExportContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SubAuthenticatedStudyProgressExportListener subAuthenticatedStudyProgressExportListener) {
        String name = "subAuthenticatedStudyProgressExportQueueName";
        String queueName = "zxy-course-sub-authenticated-study-progress-export";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(subAuthenticatedStudyProgressExportListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, subAuthenticatedStudyProgressExportListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer ShardingDataMigrationContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ShardingDataMigrationListener shardingDataMigrationListener) {
        String name = "shardingDataMigrationQueue";
        Queue queue = new Queue(shardingDataMigrationQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(shardingDataMigrationListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, shardingDataMigrationListener, shardingDataMigrationQueue);
    }


    @Bean
    SimpleMessageListenerContainer SplitCourseStudyProgressContainer(DirectExchange exchange, ConnectionFactory connectionFactory, SplitCourseStudyProgressTableListener splitCourseStudyProgressListener) {
        String name = "splitCourseStudyProgressQueueName";
        String queueName = "zxy-course-split-course-study-progress";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(splitCourseStudyProgressListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        SimpleMessageListenerContainer container = createListen(connectionFactory, splitCourseStudyProgressListener, queueName);
        container.setMaxConcurrentConsumers(2);
        container.setPrefetchCount(5);
        return container;
    }

    @Bean
    SimpleMessageListenerContainer chbnIssueCertificateContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ChbnIssueCertificateListener chbnIssueCertificateListener) {
        String name = "chbnIssueCertificateRecordQueueName";
        String queueName = "zxy-course-chbn-issue-certificate-record";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(chbnIssueCertificateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, chbnIssueCertificateListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer topicCourseVisitsContainer(DirectExchange exchange, ConnectionFactory connectionFactory, TopicCourseVistisListener topicCourseVistisListener) {
        String name = "topicCourseVisitsRecordQueueName";
        String queueName = "zxy-course-topic-course-visits-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(topicCourseVistisListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, topicCourseVistisListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer externalCourseStudyDataContainer(DirectExchange exchange, ConnectionFactory connectionFactory, RemodelingExternalCourseStudyDataUpdateListener remodelingExternalCourseStudyDataUpdateListener) {
        String name = "remodelingExternalCourseStudyDataUpdateQueueName";
        String queueName = "zxy-course-remodeling-external-course-data-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(remodelingExternalCourseStudyDataUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, remodelingExternalCourseStudyDataUpdateListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer externalExamDataContainer(DirectExchange exchange, ConnectionFactory connectionFactory, RemodelingExternalExamDataUpdateListener remodelingExternalExamDataUpdateListener) {
        String name = "remodelingExternalExamDataUpdateQueueName";
        String queueName = "zxy-course-remodeling-external-exam-data-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(remodelingExternalExamDataUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, remodelingExternalExamDataUpdateListener, queueName);
    }
	
	@Bean
    SimpleMessageListenerContainer studyTeamConfirmedCreditsListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, StudyTeamConfirmedCreditsListener studyTeamConfirmedCreditsListener) {
        String name = "studyTeamConfirmedCreditsQueueName";
        String queueName = "zxy-course-study-team-confirmed-credits";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(studyTeamConfirmedCreditsListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, studyTeamConfirmedCreditsListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer internalCourseDataContainer(DirectExchange exchange, ConnectionFactory connectionFactory, RemodelingInternalCourseStudyUpdateListener remodelingInternalCourseStudyUpdateListener) {
        String name = "remodelingInternalCourseDataUpdateQueueName";
        String queueName = "zxy-course-remodeling-internal-course-data-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(remodelingInternalCourseStudyUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, remodelingInternalCourseStudyUpdateListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer CourseInFormContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseInFormListener courseInFormListener) {
        String name = "courseInFormQueue";
        String queueName = "zxy-course-in-form-queue";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseInFormListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseInFormListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer StudyPlanContainer(DirectExchange exchange, ConnectionFactory connectionFactory, StudyPlanListener studyPlanListener) {
        String name = "studyPlanAddNumCalculateQueueName";
        Queue queue = new Queue(studyPlanAddNumCalculateQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(studyPlanListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, studyPlanListener, studyPlanAddNumCalculateQueue);
    }

    @Bean
    SimpleMessageListenerContainer CoachTestsProgressContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CoachTestsProgressListener coachTestsProgressListener) {
        String name = "coachTestsProgressQueueName";
        String queueName = "coach-tests-progress-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(coachTestsProgressListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, coachTestsProgressListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer ConcentrateStudyHoursContainer(DirectExchange exchange, ConnectionFactory connectionFactory, ConcentrateStudyHoursListener concentrateStudyHoursListener) {
        String name = "concentrateStudyHoursQueueName";
        Queue queue = new Queue(concentrateStudyHoursQueue, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(concentrateStudyHoursListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, concentrateStudyHoursListener, concentrateStudyHoursQueue);
    }

    private SimpleMessageListenerContainer createListen(ConnectionFactory connectionFactory, MessageListener listener,
                                                        String queue) {
        SimpleMessageListenerContainer container = new SimpleMessageListenerContainer();
        container.setConnectionFactory(connectionFactory);
        container.setQueueNames(queue);
        container.setMessageListener(listener);
        container.setPrefetchCount(environment.getProperty("spring.rabbitmq.listener.simple.prefetch", Integer.class, 1));
        container.setConcurrentConsumers(environment.getProperty("spring.rabbitmq.listener.simple.concurrency", Integer.class, 1));
        container.setMaxConcurrentConsumers(environment.getProperty("spring.rabbitmq.listener.simple.max-concurrency", Integer.class, 1));
        container.setErrorHandler(new ErrorHandler() {
            @Override
            public void handleError(Throwable throwable) {
                if (causeChainContainsARADRE(throwable)) {
                    StringBuilder errorMessage = new StringBuilder(environment.getProperty("application.env.name", String.class, "dev9"));
                    errorMessage.append("环境异步监听服务出错: ");
                    errorMessage.append(throwable.getCause().getMessage());
                    LOGGER.error("message listener shutdown: " + throwable.getCause().getMessage());
                    // 发送邮件消息
                    messageSender.send(MessageTypeContent.SEND_MESSAGE_WARNING_EMAIL, (Object)errorMessage.toString(),
                            MessageHeaderContent.SUBJECT, "course-study project " + listener.getClass().getSimpleName() + "服务挂起");
                    // 停止监听
                    container.shutdown();
                }
            }
            private boolean causeChainContainsARADRE(Throwable t) {
                for(Throwable cause = t.getCause(); cause != null; cause = cause.getCause()) {
                    if(cause instanceof MessageException) {
                        return true;
                    }
                }
                return false;
            }
        });
        return container;
    }

    /*@Bean
    public KryoPool kryoPool() {
        KryoFactory kryoFactory = new KryoFactory() {
            @Override
            public Kryo create() {
                return new Kryo();
            }
        };
        KryoPool kryoPool = new KryoPool.Builder(kryoFactory).softReferences().build();
        return kryoPool;
    }

    @Bean
    public KryoMessageConverter kryoMessageConverter(KryoPool kryoPool) {
        KryoMessageConverter converter = new KryoMessageConverter();
        converter.setKryoPool(kryoPool);
        return converter;
    }*/

    @Bean
    public Serializer serializer() {
        return new HessianSerializer();
    }

    @Bean
    public CommonMessageConverter commonMessageConverter(Serializer serializer) {
        CommonMessageConverter converter = new CommonMessageConverter();
        converter.setSerializer(serializer);
        return converter;
    }

    //更新最近学习 -- LJY
    @Bean
    SimpleMessageListenerContainer CourseStudyCardUpdateContainer(DirectExchange exchange, ConnectionFactory connectionFactory,CourseStudyCardUpdateListener courseStudyCardUpdateListener) {
        String name = "courseStudyCardUpdateQueueName";
        String queueName = "zxy-course-study-card-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);

        Arrays.stream(courseStudyCardUpdateListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, courseStudyCardUpdateListener, queueName);
    }

    /**
     * 多维度课程评分关系
     */
    @Bean
    SimpleMessageListenerContainer multidimensionalScoringListenerContainer(DirectExchange exchange, ConnectionFactory connectionFactory, MultidimensionalScoringListener multidimensionalScoringListener) {
        String name = "multidimensionalScoringInsertOrEditQueueName";
        String queueName = "zxy-course-multidimensional-scoring-insert-or-edit";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);
        Arrays.stream(multidimensionalScoringListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, multidimensionalScoringListener, queueName);
    }


    //咪咕直播访问记录回调
    @Bean
    SimpleMessageListenerContainer miguUserAccessQueueContainer(DirectExchange exchange, ConnectionFactory connectionFactory, MiguUserAccessListener miguUserAccessListener) {
        String name = "migu-user-access";
        String queueName = "zxy-course-migu-user-access";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);
        Arrays.stream(miguUserAccessListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, miguUserAccessListener, queueName);
    }

    //直播统计
    @Bean
    SimpleMessageListenerContainer pointAddAccessQueueContainer(DirectExchange exchange, ConnectionFactory connectionFactory, PointAddListener pointAddListener) {
        String name = "point-add-live";
        String queueName = "zxy-course-point-add-live";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);
        Arrays.stream(pointAddListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, pointAddListener, queueName);
    }
    /**
     * 虚拟空间修改直播归属
     */
    @Bean
    SimpleMessageListenerContainer liveVirtualSpaceQueueContainer(DirectExchange exchange, ConnectionFactory connectionFactory, LiveVirtualSpaceListener liveVirtualSpaceListener) {
        String name = "liveVirtualSpaceOrgUpdate";
        String queueName = "zxy-course-live-virtual-space-org-update";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);
        Arrays.stream(liveVirtualSpaceListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, liveVirtualSpaceListener, queueName);
    }

    @Bean
    SimpleMessageListenerContainer broadcastCountQueueContainer(DirectExchange exchange, ConnectionFactory connectionFactory, BroadcastCountListener broadcastCountListener) {
        String name = "broadcastCountQueueName";
        String queueName = "zxy-course-broadcast-count-queue";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);
        Arrays.stream(broadcastCountListener.getTypes()).forEach(type -> {
            this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + ""));
        });
        return createListen(connectionFactory, broadcastCountListener, queueName);
    }

    /**
     * 红船自动审核监听
     * @param exchange
     * @param connectionFactory
     * @param courseRedShipAuditListener
     * @return
     */
    @Bean
    SimpleMessageListenerContainer redShipAutoAuditQueueContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseRedShipAuditListener courseRedShipAuditListener) {
        String name = "redShipAutoAuditQueueName";
        String queueName = "zxy-course-red-ship-auto-audit-queue";
        Queue queue = new Queue(queueName, true);
        this.beanFactory.registerSingleton(name, queue);
        Arrays.stream(courseRedShipAuditListener.getTypes()).forEach(type -> this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + "")));
        return createListen(connectionFactory, courseRedShipAuditListener, queueName);
    }


    /**九天数智导师拉取监听*/
    @Bean
    SimpleMessageListenerContainer mentorSynchronousQueueContainer(DirectExchange exchange, ConnectionFactory connectionFactory, MentorSynchronousListener mentorSynchronousListener) {
        String name = "mentorSynchronousQueueName";
        Queue queue = new Queue(mentorSynchronousQueue, true);
        this.beanFactory.registerSingleton(name, queue);
        Arrays.stream(mentorSynchronousListener.getTypes()).forEach(type -> this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + "")));
        return createListen(connectionFactory, mentorSynchronousListener, mentorSynchronousQueue);
    }

    /**课程在线任务分发监听*/
    @Bean
    SimpleMessageListenerContainer courseOnlineDistributeQueueContainer(DirectExchange exchange, ConnectionFactory connectionFactory, CourseOnlineDistributeListener courseOnlineDistributeListener) {
        String name = "courseOnlineDistributeQueueName";
        Queue queue = new Queue(courseOnlineDistributeQueue, true);
        this.beanFactory.registerSingleton(name, queue);
        Arrays.stream(courseOnlineDistributeListener.getTypes()).forEach(type -> this.beanFactory.registerSingleton(name + "#" + type, BindingBuilder.bind(queue).to(exchange).with(type + "")));
        return createListen(connectionFactory, courseOnlineDistributeListener, courseOnlineDistributeQueue);
    }
}
