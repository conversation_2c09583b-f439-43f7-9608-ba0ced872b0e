spring.application.name=course-study-async-service
application.env.name=dev9
logging.level.root=INFO
#spring.datasource.url=**************************************************************
#spring.datasource.url=**************************************************************
#spring.datasource.username=cmudevuser
#spring.datasource.password=DreamtechIT%9
#spring.datasource.url=****************************************************************
#spring.datasource.username=root
#spring.datasource.password=dreamtech%IT
spring.datasource.master.url=**************************************************************
spring.datasource.master.username=root
spring.datasource.master.password=dreamtech%9
spring.datasource.driver-class-name=com.mysql.jdbc.Driver

spring.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.master.test-while-idle=true
spring.datasource.master.test-on-borrow=true
spring.datasource.master.time-between-eviction-runs-millis=5000
spring.datasource.master.min-evictable-idle-time-millis=60000
spring.datasource.master.validation-query=SELECT 1


spring.datasource.slave.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.slave.test-while-idle=true
spring.datasource.slave.test-on-borrow=true
spring.datasource.slave.time-between-eviction-runs-millis=5000
spring.datasource.slave.min-evictable-idle-time-millis=60000
spring.datasource.slave.validation-query=SELECT 1



spring.jooq.sql-dialect = mysql
logging.level.org.jooq=DEBUG

dubbo.application.name=course-study-async-service
dubbo.application.version=1
dubbo.registry.address=zookeeper://127.0.0.1:2181
#dubbo.registry.address=zookeeper://mw9.zhixueyun.com:10501
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator


spring.rabbitmq.host=***********
spring.rabbitmq.port=30007
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1

graphite.server=**************
graphite.port=30004

spring.data.mongodb.host = **************
spring.data.mongodb.port = 30005
spring.data.mongodb.dbname = cmu_homecfg
spring.data.mongodb.connectTimeout = 60000
spring.data.mongodb.socketTimeout = 120000
spring.data.mongodb.socketKeepAlive = true
spring.data.mongodb.maxConnectionIdleTime = 120000
spring.data.mongodb.maxConnectionLifeTime = 120000
spring.data.mongodb.connectionsPerHost = 100
spring.data.mongodb.username = admin
spring.data.mongodb.password = dreamtech

# restTemplate
resttemplate.charset = UTF-8
resttemplate.timeout.read = 10000
resttemplate.timeout.connect = 10000
resttemplate.header.authorization = #@&^%#!@1

# course sync
notice.url = http://localhost/api/v1/cloud-center/company-sync/write-back/{id}/{module}/{status}
get.unsync.course.url = http://localhost/api/v1/cloud-center/company-sync/courses/{id}
get.template.course.url = http://localhost/api/v1/cloud-center/company-sync/categories/{id}

# cloud-center certification
sys.component.gensee.portAddress=http://zxy9.zhixueyun.com/api/v1/cloud-center
sys.component.gensee.loginName=admin
sys.component.gensee.passWord=admin
sys.component.gensee.site=zxy9.zhixueyun.com

#add_gensee_web_cast_URL=https://zxy9.zhixueyun.com/api/v1/cloud-center/gensee/create
add_gensee_web_cast_URL=http://localhost/api/v1/cloud-center/gensee/create
update_gensee_web_cast_URL=http://localhost/api/v1/cloud-center/gensee/edit
delete_gensee_web_cast_by_id_URL=http://zxy9.zhixueyun.com/api/v1/cloud-center/gensee/delete
query_user_join_history_URL=http://localhost/api/v1/cloud-center/gensee/history

# name of queues
study.message.queue.restfulLog = zxy-course-restfulLog
study.message.queue.member = zxy-course-member
study.message.queue.organization = zxy-course-organization
study.message.queue.organizationDetail = zxy-course-organizationDetail
study.message.queue.grantDetail = zxy-course-grantDetail
study.message.queue.audienceItem = zxy-course-audienceItem
study.message.queue.audienceItemK = zxy-course-audienceItem-k
study.message.queue.audienceMember = zxy-course-audienceMember
study.message.queue.audienceMemberK = zxy-course-audienceMember-k
study.message.queue.version = zxy-course-version
study.message.queue.studyProgress = zxy-course-studyProgress
study.message.queue.studyPush = zxy-course-studyPush
study.message.queue.knowledge = zxy-course-knowledge
study.message.queue.notice = zxy-course-notice
study.message.queue.courseSync = zxy-course-courseSync
study.message.queue.pushMessage = zxy-course-pushMessage
study.message.queue.push.record = zxy-course-pushRecord
study.message.queue.businessTopic = zxy-course-businessTopic
study.message.queue.task = zxy-course-task
study.message.queue.study.task = zxy-course-studyTask
study.message.queue.schedule = zxy-course-schedule
study.message.queue.course = zxy-course-info
study.message.queue.genseeBusinessProgress = zxy-course-genseeBusinessProgress
study.message.queue.scormUnZip = zxy-course-scorm-unzip
study.message.queue.genseeWebCast = zxy-course-genseeWebCast
study.message.queue.courseStatistics = zxy-course-statistics
study.message.queue.memberStatistics = zxy-member-statistics
study.message.queue.knowledge.statistics = zxy-knowledge-statistics
study.message.queue.thematic.class = zxy-thematic-class
study.message.queue.shardingDataMigration = zxy-course-sharding-data-migration
study.message.queue.course.studyPlanNum.calculate=zxy-course-studyPlanNum-calculate
study.message.queue.miguUserAccess = zxy-course-migu-user-access
study.message.queue.mentor.synchronous=zxy-course-mentor-synchronous
study.message.queue.online=zxy-course-online-log
study.message.queue.online.distribute=zxy-course-online-distribute-log
study.message.queue.subject.concentrate.hours = zxy-course-subject-concentrate-hours

# unzip service
scorm.unzip.service.url = https://zxy9.zhixueyun.com/scorm-unzip

scorm.web.url = https://zxy9.zhixueyun.com/scorm-file

spring.redis.cluster = false
spring.redis.timeout = 10000
spring.redis.cluster.nodes = ***********:30006
spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3

# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1


spring_fastdfs_tracker_servers = ***********:10401
spring_fastdfs_tracker_http_port = 10401
spring_fastdfs_secret_key = FastDFS1234567890
spring_fastdfs_connect_timeout = 30
spring_fastdfs_network_timeout = 60
spring_fastdfs_charset = utf-8
spring_fastdfs_anti_steal_token = false
spring_fastdfs_max_total = 5
spring_fastdfs_max_idle = 5


wanda.domain = https://uat.wangda.chinamobile.com/#/study/subject/detail/
#\u7F51\u5927\u8BFE\u7A0B\u7B14\u8BB0|\u8BFE\u4EF6\u7B14\u8BB0|\u731C\u4F60\u60F3\u95EE\u76F8\u5173Api
#ai.mentor.synchronize.synchronizeNoteApi=http://***********:8082/v1/recommend/wangda/findSubjectIntroduction
ai.mentor.synchronize.synchronizeNoteApi=http://10.225.84.132:8082/v1/recommend/wangda/findSubjectIntroduction

