<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<parent>
		<artifactId>course-study</artifactId>
		<groupId>com.zxy.product</groupId>
		<version>cmu-9.6.0</version>
	</parent>
	<modelVersion>4.0.0</modelVersion>
	<artifactId>course-study-async-service</artifactId>

	<dependencies>
		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>async-service-parent</artifactId>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<groupId>org.apache.curator</groupId>
					<artifactId>curator-recipes</artifactId>
				</exclusion>
				<exclusion>
					<artifactId>curator-framework</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<artifactId>curator-client</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>system-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>human-resource-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>course-study-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>pk-game-api</artifactId>
			<version>${version}</version>
		</dependency>

		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>exam-api</artifactId>
			<version>${version}</version>
		</dependency>

		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>exam-stu-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>train-api</artifactId>
            <version>${version}</version>
        </dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>archived-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>history-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>common-cache</artifactId>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.3</version>
		</dependency>
		<dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
        </dependency>
        <dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
		</dependency>
		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>1.6.1</version>
		</dependency>
		<dependency>
			<groupId>jaxen</groupId>
			<artifactId>jaxen</artifactId>
			<version>1.1.6</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>common-distributed</artifactId>
			<version>0.0.3</version>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.8.13</version>
		</dependency>
        <dependency>
            <groupId>org.csource</groupId>
            <artifactId>fastdfs-client-java</artifactId>
        </dependency>
		<dependency>
			<groupId>io.springside</groupId>
			<artifactId>springside-utils</artifactId>
			<version>5.0.0-RC1</version>
		</dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>com.zxy.common</groupId>
            <artifactId>common-fastdfs</artifactId>
            <version>2.0.3-SNAPSHOT</version>
        </dependency>
		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>common-office</artifactId>
		</dependency>

		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.9.0</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>common-restful-support</artifactId>
			<version>0.0.5-SNAPSHOT</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>archived-api</artifactId>
			<version>${version}</version>
			<scope>compile</scope>
		</dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<dependencies>
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>springloaded</artifactId>
						<version>1.2.3.RELEASE</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>

</project>
