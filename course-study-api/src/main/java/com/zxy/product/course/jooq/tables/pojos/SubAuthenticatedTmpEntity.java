/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ISubAuthenticatedTmp;

import javax.annotation.Generated;
import java.sql.Timestamp;


/**
 * 子认证-维度导入表-临时表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubAuthenticatedTmpEntity extends BaseEntity implements ISubAuthenticatedTmp {

    private static final long serialVersionUID = 1L;

    private String    name;
    private String    code;
    private String    fileId;
    private String    subAuthenticatedId;
    private Integer      deleteFlag;
    private String    creator;
    private Timestamp modifyDate;

    public SubAuthenticatedTmpEntity() {}

    public SubAuthenticatedTmpEntity(SubAuthenticatedTmpEntity value) {
        this.name = value.name;
        this.code = value.code;
        this.fileId = value.fileId;
        this.subAuthenticatedId = value.subAuthenticatedId;
        this.deleteFlag = value.deleteFlag;
        this.creator = value.creator;
        this.modifyDate = value.modifyDate;
    }

    public SubAuthenticatedTmpEntity(
        String    id,
        String    name,
        String    code,
        String    fileId,
        String    subAuthenticatedId,
        Integer      deleteFlag,
        String    creator,
        Long      createTime,
        Timestamp modifyDate
    ) {
        super.setId(id);
        this.name = name;
        this.code = code;
        this.fileId = fileId;
        this.subAuthenticatedId = subAuthenticatedId;
        this.deleteFlag = deleteFlag;
        this.creator = creator;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getFileId() {
        return this.fileId;
    }

    @Override
    public void setFileId(String fileId) {
        this.fileId = fileId;
    }

    @Override
    public String getSubAuthenticatedId() {
        return this.subAuthenticatedId;
    }

    @Override
    public void setSubAuthenticatedId(String subAuthenticatedId) {
        this.subAuthenticatedId = subAuthenticatedId;
    }

    @Override
    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    @Override
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public String getCreator() {
        return this.creator;
    }

    @Override
    public void setCreator(String creator) {
        this.creator = creator;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("SubAuthenticatedTmpEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(code);
        sb.append(", ").append(fileId);
        sb.append(", ").append(subAuthenticatedId);
        sb.append(", ").append(deleteFlag);
        sb.append(", ").append(creator);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISubAuthenticatedTmp from) {
        setId(from.getId());
        setName(from.getName());
        setCode(from.getCode());
        setFileId(from.getFileId());
        setSubAuthenticatedId(from.getSubAuthenticatedId());
        setDeleteFlag(from.getDeleteFlag());
        setCreator(from.getCreator());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISubAuthenticatedTmp> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends SubAuthenticatedTmpEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.SubAuthenticatedTmpRecord r = new com.zxy.product.course.jooq.tables.records.SubAuthenticatedTmpRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.ID, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.NAME, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CODE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CODE, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CODE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.FILE_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.FILE_ID, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.FILE_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.SUB_AUTHENTICATED_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.SUB_AUTHENTICATED_ID, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.SUB_AUTHENTICATED_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.DELETE_FLAG.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.DELETE_FLAG, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.DELETE_FLAG));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CREATOR.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CREATOR, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CREATOR));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.MODIFY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.MODIFY_DATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
