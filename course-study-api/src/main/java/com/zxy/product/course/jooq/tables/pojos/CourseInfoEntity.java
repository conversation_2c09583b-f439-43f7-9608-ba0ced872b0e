/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseInfo;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseInfoEntity extends BaseEntity implements ICourseInfo {

    private static final long serialVersionUID = 1L;

    private String    organizationId;
    private String    name;
    private String    cover;
    private String    code;
    private Integer   price;
    private Integer   integral;
    private Integer   courseHour;
    private Integer   courseTime;
    private Integer   courseSecond;
    private Integer   credit;
    private String    description;
    private Long      beginDate;
    private Long      endDate;
    private String    certificateId;
    private Integer      certificateType;
    private Long      certificateTime;
    private Long      certificateUpdateTime;
    private Integer   status;
    private Integer   portalNum;
    private Integer   type;
    private Integer   source;
    private Long      releaseTime;
    private String    releaseMemberId;
    private String    releaseOrgId;
    private String    developMemberId;
    private Long      developTime;
    private String    createMemberId;
    private String    categoryId;
    private Integer   addType;
    private String    versionId;
    private Integer   learnSequence;
    private Integer   isSubject;
    private Integer   studyDays;
    private Integer   studyMemberCount;
    private Integer   registerMemberCount;
    private Integer   addPlanMemberCount;
    private String    styles;
    private Integer   totalScore;
    private Integer   scoreMemberCount;
    private Integer   avgScore;
    private Long      shelveTime;
    private Long      offTime;
    private Integer   isPublic;
    private Integer   deleteFlag;
    private String    descriptionText;
    private Integer   publishClient;
    private Integer   publishType;
    private Integer   businessType;
    private String    lecturer;
    private Integer   sequence;
    private String    sourceDetail;
    private Integer   visits;
    private Integer   shareSub;
    private String    url;
    private String    coverPath;
    private String    coverMaterialId;
    private Integer   collectionCount;
    private Integer   shareCount;
    private String    audienceOrg;
    private Integer   cloudStatus;
    private Long      cloudTime;
    private Integer   cloudRule;
    private String    audiences;
    private String    descriptionApp;
    private String    relativeGenseeId;
    private Integer   isSign;
    private Integer   isParty;
    private Integer   useVideoSpeed;
    private Integer   switchHide;
    private Integer   historyHide;
    private Integer   captionFlag;
    private Timestamp modifyDate;
    private Integer      captionOverallStatus;
    private Integer      switchMentor;
    private Integer   captionFollowRelease;
    private Integer   explicitLearningStatus;
    private Integer   learningSituation;
    private String    sponsoringOrg;
    private String    pcBanner;
    private String    pcBannerPath;
    private String    appBanner;
    private String    appBannerPath;
    private Integer   mentorState;
    private Integer   open;
    private Long      updateDate;
    private Integer   voiceToText;
    private Integer   newLaunchVisitNum;
    private Integer   recommendVisitNum;
    private Integer   constructionType;
    private Integer      skinType;
    private Integer   subjectType;
    private String    subjectMisCode;

    public CourseInfoEntity() {}

    public CourseInfoEntity(CourseInfoEntity value) {
        this.organizationId = value.organizationId;
        this.name = value.name;
        this.cover = value.cover;
        this.code = value.code;
        this.price = value.price;
        this.integral = value.integral;
        this.courseHour = value.courseHour;
        this.courseTime = value.courseTime;
        this.courseSecond = value.courseSecond;
        this.credit = value.credit;
        this.description = value.description;
        this.beginDate = value.beginDate;
        this.endDate = value.endDate;
        this.certificateId = value.certificateId;
        this.certificateType = value.certificateType;
        this.certificateTime = value.certificateTime;
        this.certificateUpdateTime = value.certificateUpdateTime;
        this.status = value.status;
        this.portalNum = value.portalNum;
        this.type = value.type;
        this.source = value.source;
        this.releaseTime = value.releaseTime;
        this.releaseMemberId = value.releaseMemberId;
        this.releaseOrgId = value.releaseOrgId;
        this.developMemberId = value.developMemberId;
        this.developTime = value.developTime;
        this.createMemberId = value.createMemberId;
        this.categoryId = value.categoryId;
        this.addType = value.addType;
        this.versionId = value.versionId;
        this.learnSequence = value.learnSequence;
        this.isSubject = value.isSubject;
        this.studyDays = value.studyDays;
        this.studyMemberCount = value.studyMemberCount;
        this.registerMemberCount = value.registerMemberCount;
        this.addPlanMemberCount = value.addPlanMemberCount;
        this.styles = value.styles;
        this.totalScore = value.totalScore;
        this.scoreMemberCount = value.scoreMemberCount;
        this.avgScore = value.avgScore;
        this.shelveTime = value.shelveTime;
        this.offTime = value.offTime;
        this.isPublic = value.isPublic;
        this.deleteFlag = value.deleteFlag;
        this.descriptionText = value.descriptionText;
        this.publishClient = value.publishClient;
        this.publishType = value.publishType;
        this.businessType = value.businessType;
        this.lecturer = value.lecturer;
        this.sequence = value.sequence;
        this.sourceDetail = value.sourceDetail;
        this.visits = value.visits;
        this.shareSub = value.shareSub;
        this.url = value.url;
        this.coverPath = value.coverPath;
        this.coverMaterialId = value.coverMaterialId;
        this.collectionCount = value.collectionCount;
        this.shareCount = value.shareCount;
        this.audienceOrg = value.audienceOrg;
        this.cloudStatus = value.cloudStatus;
        this.cloudTime = value.cloudTime;
        this.cloudRule = value.cloudRule;
        this.audiences = value.audiences;
        this.descriptionApp = value.descriptionApp;
        this.relativeGenseeId = value.relativeGenseeId;
        this.isSign = value.isSign;
        this.isParty = value.isParty;
        this.useVideoSpeed = value.useVideoSpeed;
        this.switchHide = value.switchHide;
        this.historyHide = value.historyHide;
        this.captionFlag = value.captionFlag;
        this.modifyDate = value.modifyDate;
        this.captionOverallStatus = value.captionOverallStatus;
        this.switchMentor = value.switchMentor;
        this.captionFollowRelease = value.captionFollowRelease;
        this.explicitLearningStatus = value.explicitLearningStatus;
        this.learningSituation = value.learningSituation;
        this.sponsoringOrg = value.sponsoringOrg;
        this.pcBanner = value.pcBanner;
        this.pcBannerPath = value.pcBannerPath;
        this.appBanner = value.appBanner;
        this.appBannerPath = value.appBannerPath;
        this.mentorState = value.mentorState;
        this.open = value.open;
        this.updateDate = value.updateDate;
        this.voiceToText = value.voiceToText;
        this.newLaunchVisitNum = value.newLaunchVisitNum;
        this.recommendVisitNum = value.recommendVisitNum;
        this.constructionType = value.constructionType;
        this.skinType = value.skinType;
        this.subjectType = value.subjectType;
        this.subjectMisCode = value.subjectMisCode;
    }

    public CourseInfoEntity(
        String    id,
        String    organizationId,
        String    name,
        String    cover,
        String    code,
        Integer   price,
        Integer   integral,
        Integer   courseHour,
        Integer   courseTime,
        Integer   courseSecond,
        Integer   credit,
        String    description,
        Long      beginDate,
        Long      endDate,
        String    certificateId,
        Integer      certificateType,
        Long      certificateTime,
        Long      certificateUpdateTime,
        Integer   status,
        Integer   portalNum,
        Integer   type,
        Integer   source,
        Long      releaseTime,
        String    releaseMemberId,
        String    releaseOrgId,
        String    developMemberId,
        Long      developTime,
        String    createMemberId,
        Long      createTime,
        String    categoryId,
        Integer   addType,
        String    versionId,
        Integer   learnSequence,
        Integer   isSubject,
        Integer   studyDays,
        Integer   studyMemberCount,
        Integer   registerMemberCount,
        Integer   addPlanMemberCount,
        String    styles,
        Integer   totalScore,
        Integer   scoreMemberCount,
        Integer   avgScore,
        Long      shelveTime,
        Long      offTime,
        Integer   isPublic,
        Integer   deleteFlag,
        String    descriptionText,
        Integer   publishClient,
        Integer   publishType,
        Integer   businessType,
        String    lecturer,
        Integer   sequence,
        String    sourceDetail,
        Integer   visits,
        Integer   shareSub,
        String    url,
        String    coverPath,
        String    coverMaterialId,
        Integer   collectionCount,
        Integer   shareCount,
        String    audienceOrg,
        Integer   cloudStatus,
        Long      cloudTime,
        Integer   cloudRule,
        String    audiences,
        String    descriptionApp,
        String    relativeGenseeId,
        Integer   isSign,
        Integer   isParty,
        Integer   useVideoSpeed,
        Integer   switchHide,
        Integer   historyHide,
        Integer   captionFlag,
        Timestamp modifyDate,
        Integer      captionOverallStatus,
        Integer      switchMentor,
        Integer   captionFollowRelease,
        Integer   explicitLearningStatus,
        Integer   learningSituation,
        String    sponsoringOrg,
        String    pcBanner,
        String    pcBannerPath,
        String    appBanner,
        String    appBannerPath,
        Integer   mentorState,
        Integer   open,
        Long      updateDate,
        Integer   voiceToText,
        Integer   newLaunchVisitNum,
        Integer   recommendVisitNum,
        Integer   constructionType,
        Integer      skinType,
        Integer   subjectType,
        String    subjectMisCode
    ) {
        super.setId(id);
        this.organizationId = organizationId;
        this.name = name;
        this.cover = cover;
        this.code = code;
        this.price = price;
        this.integral = integral;
        this.courseHour = courseHour;
        this.courseTime = courseTime;
        this.courseSecond = courseSecond;
        this.credit = credit;
        this.description = description;
        this.beginDate = beginDate;
        this.endDate = endDate;
        this.certificateId = certificateId;
        this.certificateType = certificateType;
        this.certificateTime = certificateTime;
        this.certificateUpdateTime = certificateUpdateTime;
        this.status = status;
        this.portalNum = portalNum;
        this.type = type;
        this.source = source;
        this.releaseTime = releaseTime;
        this.releaseMemberId = releaseMemberId;
        this.releaseOrgId = releaseOrgId;
        this.developMemberId = developMemberId;
        this.developTime = developTime;
        this.createMemberId = createMemberId;
        super.setCreateTime(createTime);
        this.categoryId = categoryId;
        this.addType = addType;
        this.versionId = versionId;
        this.learnSequence = learnSequence;
        this.isSubject = isSubject;
        this.studyDays = studyDays;
        this.studyMemberCount = studyMemberCount;
        this.registerMemberCount = registerMemberCount;
        this.addPlanMemberCount = addPlanMemberCount;
        this.styles = styles;
        this.totalScore = totalScore;
        this.scoreMemberCount = scoreMemberCount;
        this.avgScore = avgScore;
        this.shelveTime = shelveTime;
        this.offTime = offTime;
        this.isPublic = isPublic;
        this.deleteFlag = deleteFlag;
        this.descriptionText = descriptionText;
        this.publishClient = publishClient;
        this.publishType = publishType;
        this.businessType = businessType;
        this.lecturer = lecturer;
        this.sequence = sequence;
        this.sourceDetail = sourceDetail;
        this.visits = visits;
        this.shareSub = shareSub;
        this.url = url;
        this.coverPath = coverPath;
        this.coverMaterialId = coverMaterialId;
        this.collectionCount = collectionCount;
        this.shareCount = shareCount;
        this.audienceOrg = audienceOrg;
        this.cloudStatus = cloudStatus;
        this.cloudTime = cloudTime;
        this.cloudRule = cloudRule;
        this.audiences = audiences;
        this.descriptionApp = descriptionApp;
        this.relativeGenseeId = relativeGenseeId;
        this.isSign = isSign;
        this.isParty = isParty;
        this.useVideoSpeed = useVideoSpeed;
        this.switchHide = switchHide;
        this.historyHide = historyHide;
        this.captionFlag = captionFlag;
        this.modifyDate = modifyDate;
        this.captionOverallStatus = captionOverallStatus;
        this.switchMentor = switchMentor;
        this.captionFollowRelease = captionFollowRelease;
        this.explicitLearningStatus = explicitLearningStatus;
        this.learningSituation = learningSituation;
        this.sponsoringOrg = sponsoringOrg;
        this.pcBanner = pcBanner;
        this.pcBannerPath = pcBannerPath;
        this.appBanner = appBanner;
        this.appBannerPath = appBannerPath;
        this.mentorState = mentorState;
        this.open = open;
        this.updateDate = updateDate;
        this.voiceToText = voiceToText;
        this.newLaunchVisitNum = newLaunchVisitNum;
        this.recommendVisitNum = recommendVisitNum;
        this.constructionType = constructionType;
        this.skinType = skinType;
        this.subjectType = subjectType;
        this.subjectMisCode = subjectMisCode;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getCover() {
        return this.cover;
    }

    @Override
    public void setCover(String cover) {
        this.cover = cover;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public Integer getPrice() {
        return this.price;
    }

    @Override
    public void setPrice(Integer price) {
        this.price = price;
    }

    @Override
    public Integer getIntegral() {
        return this.integral;
    }

    @Override
    public void setIntegral(Integer integral) {
        this.integral = integral;
    }

    @Override
    public Integer getCourseHour() {
        return this.courseHour;
    }

    @Override
    public void setCourseHour(Integer courseHour) {
        this.courseHour = courseHour;
    }

    @Override
    public Integer getCourseTime() {
        return this.courseTime;
    }

    @Override
    public void setCourseTime(Integer courseTime) {
        this.courseTime = courseTime;
    }

    @Override
    public Integer getCourseSecond() {
        return this.courseSecond;
    }

    @Override
    public void setCourseSecond(Integer courseSecond) {
        this.courseSecond = courseSecond;
    }

    @Override
    public Integer getCredit() {
        return this.credit;
    }

    @Override
    public void setCredit(Integer credit) {
        this.credit = credit;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Long getBeginDate() {
        return this.beginDate;
    }

    @Override
    public void setBeginDate(Long beginDate) {
        this.beginDate = beginDate;
    }

    @Override
    public Long getEndDate() {
        return this.endDate;
    }

    @Override
    public void setEndDate(Long endDate) {
        this.endDate = endDate;
    }

    @Override
    public String getCertificateId() {
        return this.certificateId;
    }

    @Override
    public void setCertificateId(String certificateId) {
        this.certificateId = certificateId;
    }

    @Override
    public Integer getCertificateType() {
        return this.certificateType;
    }

    @Override
    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    @Override
    public Long getCertificateTime() {
        return this.certificateTime;
    }

    @Override
    public void setCertificateTime(Long certificateTime) {
        this.certificateTime = certificateTime;
    }

    @Override
    public Long getCertificateUpdateTime() {
        return this.certificateUpdateTime;
    }

    @Override
    public void setCertificateUpdateTime(Long certificateUpdateTime) {
        this.certificateUpdateTime = certificateUpdateTime;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getPortalNum() {
        return this.portalNum;
    }

    @Override
    public void setPortalNum(Integer portalNum) {
        this.portalNum = portalNum;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public Integer getSource() {
        return this.source;
    }

    @Override
    public void setSource(Integer source) {
        this.source = source;
    }

    @Override
    public Long getReleaseTime() {
        return this.releaseTime;
    }

    @Override
    public void setReleaseTime(Long releaseTime) {
        this.releaseTime = releaseTime;
    }

    @Override
    public String getReleaseMemberId() {
        return this.releaseMemberId;
    }

    @Override
    public void setReleaseMemberId(String releaseMemberId) {
        this.releaseMemberId = releaseMemberId;
    }

    @Override
    public String getReleaseOrgId() {
        return this.releaseOrgId;
    }

    @Override
    public void setReleaseOrgId(String releaseOrgId) {
        this.releaseOrgId = releaseOrgId;
    }

    @Override
    public String getDevelopMemberId() {
        return this.developMemberId;
    }

    @Override
    public void setDevelopMemberId(String developMemberId) {
        this.developMemberId = developMemberId;
    }

    @Override
    public Long getDevelopTime() {
        return this.developTime;
    }

    @Override
    public void setDevelopTime(Long developTime) {
        this.developTime = developTime;
    }

    @Override
    public String getCreateMemberId() {
        return this.createMemberId;
    }

    @Override
    public void setCreateMemberId(String createMemberId) {
        this.createMemberId = createMemberId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getCategoryId() {
        return this.categoryId;
    }

    @Override
    public void setCategoryId(String categoryId) {
        this.categoryId = categoryId;
    }

    @Override
    public Integer getAddType() {
        return this.addType;
    }

    @Override
    public void setAddType(Integer addType) {
        this.addType = addType;
    }

    @Override
    public String getVersionId() {
        return this.versionId;
    }

    @Override
    public void setVersionId(String versionId) {
        this.versionId = versionId;
    }

    @Override
    public Integer getLearnSequence() {
        return this.learnSequence;
    }

    @Override
    public void setLearnSequence(Integer learnSequence) {
        this.learnSequence = learnSequence;
    }

    @Override
    public Integer getIsSubject() {
        return this.isSubject;
    }

    @Override
    public void setIsSubject(Integer isSubject) {
        this.isSubject = isSubject;
    }

    @Override
    public Integer getStudyDays() {
        return this.studyDays;
    }

    @Override
    public void setStudyDays(Integer studyDays) {
        this.studyDays = studyDays;
    }

    @Override
    public Integer getStudyMemberCount() {
        return this.studyMemberCount;
    }

    @Override
    public void setStudyMemberCount(Integer studyMemberCount) {
        this.studyMemberCount = studyMemberCount;
    }

    @Override
    public Integer getRegisterMemberCount() {
        return this.registerMemberCount;
    }

    @Override
    public void setRegisterMemberCount(Integer registerMemberCount) {
        this.registerMemberCount = registerMemberCount;
    }

    @Override
    public Integer getAddPlanMemberCount() {
        return this.addPlanMemberCount;
    }

    @Override
    public void setAddPlanMemberCount(Integer addPlanMemberCount) {
        this.addPlanMemberCount = addPlanMemberCount;
    }

    @Override
    public String getStyles() {
        return this.styles;
    }

    @Override
    public void setStyles(String styles) {
        this.styles = styles;
    }

    @Override
    public Integer getTotalScore() {
        return this.totalScore;
    }

    @Override
    public void setTotalScore(Integer totalScore) {
        this.totalScore = totalScore;
    }

    @Override
    public Integer getScoreMemberCount() {
        return this.scoreMemberCount;
    }

    @Override
    public void setScoreMemberCount(Integer scoreMemberCount) {
        this.scoreMemberCount = scoreMemberCount;
    }

    @Override
    public Integer getAvgScore() {
        return this.avgScore;
    }

    @Override
    public void setAvgScore(Integer avgScore) {
        this.avgScore = avgScore;
    }

    @Override
    public Long getShelveTime() {
        return this.shelveTime;
    }

    @Override
    public void setShelveTime(Long shelveTime) {
        this.shelveTime = shelveTime;
    }

    @Override
    public Long getOffTime() {
        return this.offTime;
    }

    @Override
    public void setOffTime(Long offTime) {
        this.offTime = offTime;
    }

    @Override
    public Integer getIsPublic() {
        return this.isPublic;
    }

    @Override
    public void setIsPublic(Integer isPublic) {
        this.isPublic = isPublic;
    }

    @Override
    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    @Override
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public String getDescriptionText() {
        return this.descriptionText;
    }

    @Override
    public void setDescriptionText(String descriptionText) {
        this.descriptionText = descriptionText;
    }

    @Override
    public Integer getPublishClient() {
        return this.publishClient;
    }

    @Override
    public void setPublishClient(Integer publishClient) {
        this.publishClient = publishClient;
    }

    @Override
    public Integer getPublishType() {
        return this.publishType;
    }

    @Override
    public void setPublishType(Integer publishType) {
        this.publishType = publishType;
    }

    @Override
    public Integer getBusinessType() {
        return this.businessType;
    }

    @Override
    public void setBusinessType(Integer businessType) {
        this.businessType = businessType;
    }

    @Override
    public String getLecturer() {
        return this.lecturer;
    }

    @Override
    public void setLecturer(String lecturer) {
        this.lecturer = lecturer;
    }

    @Override
    public Integer getSequence() {
        return this.sequence;
    }

    @Override
    public void setSequence(Integer sequence) {
        this.sequence = sequence;
    }

    @Override
    public String getSourceDetail() {
        return this.sourceDetail;
    }

    @Override
    public void setSourceDetail(String sourceDetail) {
        this.sourceDetail = sourceDetail;
    }

    @Override
    public Integer getVisits() {
        return this.visits;
    }

    @Override
    public void setVisits(Integer visits) {
        this.visits = visits;
    }

    @Override
    public Integer getShareSub() {
        return this.shareSub;
    }

    @Override
    public void setShareSub(Integer shareSub) {
        this.shareSub = shareSub;
    }

    @Override
    public String getUrl() {
        return this.url;
    }

    @Override
    public void setUrl(String url) {
        this.url = url;
    }

    @Override
    public String getCoverPath() {
        return this.coverPath;
    }

    @Override
    public void setCoverPath(String coverPath) {
        this.coverPath = coverPath;
    }

    @Override
    public String getCoverMaterialId() {
        return this.coverMaterialId;
    }

    @Override
    public void setCoverMaterialId(String coverMaterialId) {
        this.coverMaterialId = coverMaterialId;
    }

    @Override
    public Integer getCollectionCount() {
        return this.collectionCount;
    }

    @Override
    public void setCollectionCount(Integer collectionCount) {
        this.collectionCount = collectionCount;
    }

    @Override
    public Integer getShareCount() {
        return this.shareCount;
    }

    @Override
    public void setShareCount(Integer shareCount) {
        this.shareCount = shareCount;
    }

    @Override
    public String getAudienceOrg() {
        return this.audienceOrg;
    }

    @Override
    public void setAudienceOrg(String audienceOrg) {
        this.audienceOrg = audienceOrg;
    }

    @Override
    public Integer getCloudStatus() {
        return this.cloudStatus;
    }

    @Override
    public void setCloudStatus(Integer cloudStatus) {
        this.cloudStatus = cloudStatus;
    }

    @Override
    public Long getCloudTime() {
        return this.cloudTime;
    }

    @Override
    public void setCloudTime(Long cloudTime) {
        this.cloudTime = cloudTime;
    }

    @Override
    public Integer getCloudRule() {
        return this.cloudRule;
    }

    @Override
    public void setCloudRule(Integer cloudRule) {
        this.cloudRule = cloudRule;
    }

    @Override
    public String getAudiences() {
        return this.audiences;
    }

    @Override
    public void setAudiences(String audiences) {
        this.audiences = audiences;
    }

    @Override
    public String getDescriptionApp() {
        return this.descriptionApp;
    }

    @Override
    public void setDescriptionApp(String descriptionApp) {
        this.descriptionApp = descriptionApp;
    }

    @Override
    public String getRelativeGenseeId() {
        return this.relativeGenseeId;
    }

    @Override
    public void setRelativeGenseeId(String relativeGenseeId) {
        this.relativeGenseeId = relativeGenseeId;
    }

    @Override
    public Integer getIsSign() {
        return this.isSign;
    }

    @Override
    public void setIsSign(Integer isSign) {
        this.isSign = isSign;
    }

    @Override
    public Integer getIsParty() {
        return this.isParty;
    }

    @Override
    public void setIsParty(Integer isParty) {
        this.isParty = isParty;
    }

    @Override
    public Integer getUseVideoSpeed() {
        return this.useVideoSpeed;
    }

    @Override
    public void setUseVideoSpeed(Integer useVideoSpeed) {
        this.useVideoSpeed = useVideoSpeed;
    }

    @Override
    public Integer getSwitchHide() {
        return this.switchHide;
    }

    @Override
    public void setSwitchHide(Integer switchHide) {
        this.switchHide = switchHide;
    }

    @Override
    public Integer getHistoryHide() {
        return this.historyHide;
    }

    @Override
    public void setHistoryHide(Integer historyHide) {
        this.historyHide = historyHide;
    }

    @Override
    public Integer getCaptionFlag() {
        return this.captionFlag;
    }

    @Override
    public void setCaptionFlag(Integer captionFlag) {
        this.captionFlag = captionFlag;
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public Integer getCaptionOverallStatus() {
        return this.captionOverallStatus;
    }

    @Override
    public void setCaptionOverallStatus(Integer captionOverallStatus) {
        this.captionOverallStatus = captionOverallStatus;
    }

    @Override
    public Integer getSwitchMentor() {
        return this.switchMentor;
    }

    @Override
    public void setSwitchMentor(Integer switchMentor) {
        this.switchMentor = switchMentor;
    }

    @Override
    public Integer getCaptionFollowRelease() {
        return this.captionFollowRelease;
    }

    @Override
    public void setCaptionFollowRelease(Integer captionFollowRelease) {
        this.captionFollowRelease = captionFollowRelease;
    }

    @Override
    public Integer getExplicitLearningStatus() {
        return this.explicitLearningStatus;
    }

    @Override
    public void setExplicitLearningStatus(Integer explicitLearningStatus) {
        this.explicitLearningStatus = explicitLearningStatus;
    }

    @Override
    public Integer getLearningSituation() {
        return this.learningSituation;
    }

    @Override
    public void setLearningSituation(Integer learningSituation) {
        this.learningSituation = learningSituation;
    }

    @Override
    public String getSponsoringOrg() {
        return this.sponsoringOrg;
    }

    @Override
    public void setSponsoringOrg(String sponsoringOrg) {
        this.sponsoringOrg = sponsoringOrg;
    }

    @Override
    public String getPcBanner() {
        return this.pcBanner;
    }

    @Override
    public void setPcBanner(String pcBanner) {
        this.pcBanner = pcBanner;
    }

    @Override
    public String getPcBannerPath() {
        return this.pcBannerPath;
    }

    @Override
    public void setPcBannerPath(String pcBannerPath) {
        this.pcBannerPath = pcBannerPath;
    }

    @Override
    public String getAppBanner() {
        return this.appBanner;
    }

    @Override
    public void setAppBanner(String appBanner) {
        this.appBanner = appBanner;
    }

    @Override
    public String getAppBannerPath() {
        return this.appBannerPath;
    }

    @Override
    public void setAppBannerPath(String appBannerPath) {
        this.appBannerPath = appBannerPath;
    }

    @Override
    public Integer getMentorState() {
        return this.mentorState;
    }

    @Override
    public void setMentorState(Integer mentorState) {
        this.mentorState = mentorState;
    }

    @Override
    public Integer getOpen() {
        return this.open;
    }

    @Override
    public void setOpen(Integer open) {
        this.open = open;
    }

    @Override
    public Long getUpdateDate() {
        return this.updateDate;
    }

    @Override
    public void setUpdateDate(Long updateDate) {
        this.updateDate = updateDate;
    }

    @Override
    public Integer getVoiceToText() {
        return this.voiceToText;
    }

    @Override
    public void setVoiceToText(Integer voiceToText) {
        this.voiceToText = voiceToText;
    }

    @Override
    public Integer getNewLaunchVisitNum() {
        return this.newLaunchVisitNum;
    }

    @Override
    public void setNewLaunchVisitNum(Integer newLaunchVisitNum) {
        this.newLaunchVisitNum = newLaunchVisitNum;
    }

    @Override
    public Integer getRecommendVisitNum() {
        return this.recommendVisitNum;
    }

    @Override
    public void setRecommendVisitNum(Integer recommendVisitNum) {
        this.recommendVisitNum = recommendVisitNum;
    }

    @Override
    public Integer getConstructionType() {
        return this.constructionType;
    }

    @Override
    public void setConstructionType(Integer constructionType) {
        this.constructionType = constructionType;
    }

    @Override
    public Integer getSkinType() {
        return this.skinType;
    }

    @Override
    public void setSkinType(Integer skinType) {
        this.skinType = skinType;
    }

    @Override
    public Integer getSubjectType() {
        return this.subjectType;
    }

    @Override
    public void setSubjectType(Integer subjectType) {
        this.subjectType = subjectType;
    }

    @Override
    public String getSubjectMisCode() {
        return this.subjectMisCode;
    }

    @Override
    public void setSubjectMisCode(String subjectMisCode) {
        this.subjectMisCode = subjectMisCode;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseInfoEntity (");

        sb.append(getId());
        sb.append(", ").append(organizationId);
        sb.append(", ").append(name);
        sb.append(", ").append(cover);
        sb.append(", ").append(code);
        sb.append(", ").append(price);
        sb.append(", ").append(integral);
        sb.append(", ").append(courseHour);
        sb.append(", ").append(courseTime);
        sb.append(", ").append(courseSecond);
        sb.append(", ").append(credit);
        sb.append(", ").append(description);
        sb.append(", ").append(beginDate);
        sb.append(", ").append(endDate);
        sb.append(", ").append(certificateId);
        sb.append(", ").append(certificateType);
        sb.append(", ").append(certificateTime);
        sb.append(", ").append(certificateUpdateTime);
        sb.append(", ").append(status);
        sb.append(", ").append(portalNum);
        sb.append(", ").append(type);
        sb.append(", ").append(source);
        sb.append(", ").append(releaseTime);
        sb.append(", ").append(releaseMemberId);
        sb.append(", ").append(releaseOrgId);
        sb.append(", ").append(developMemberId);
        sb.append(", ").append(developTime);
        sb.append(", ").append(createMemberId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(categoryId);
        sb.append(", ").append(addType);
        sb.append(", ").append(versionId);
        sb.append(", ").append(learnSequence);
        sb.append(", ").append(isSubject);
        sb.append(", ").append(studyDays);
        sb.append(", ").append(studyMemberCount);
        sb.append(", ").append(registerMemberCount);
        sb.append(", ").append(addPlanMemberCount);
        sb.append(", ").append(styles);
        sb.append(", ").append(totalScore);
        sb.append(", ").append(scoreMemberCount);
        sb.append(", ").append(avgScore);
        sb.append(", ").append(shelveTime);
        sb.append(", ").append(offTime);
        sb.append(", ").append(isPublic);
        sb.append(", ").append(deleteFlag);
        sb.append(", ").append(descriptionText);
        sb.append(", ").append(publishClient);
        sb.append(", ").append(publishType);
        sb.append(", ").append(businessType);
        sb.append(", ").append(lecturer);
        sb.append(", ").append(sequence);
        sb.append(", ").append(sourceDetail);
        sb.append(", ").append(visits);
        sb.append(", ").append(shareSub);
        sb.append(", ").append(url);
        sb.append(", ").append(coverPath);
        sb.append(", ").append(coverMaterialId);
        sb.append(", ").append(collectionCount);
        sb.append(", ").append(shareCount);
        sb.append(", ").append(audienceOrg);
        sb.append(", ").append(cloudStatus);
        sb.append(", ").append(cloudTime);
        sb.append(", ").append(cloudRule);
        sb.append(", ").append(audiences);
        sb.append(", ").append(descriptionApp);
        sb.append(", ").append(relativeGenseeId);
        sb.append(", ").append(isSign);
        sb.append(", ").append(isParty);
        sb.append(", ").append(useVideoSpeed);
        sb.append(", ").append(switchHide);
        sb.append(", ").append(historyHide);
        sb.append(", ").append(captionFlag);
        sb.append(", ").append(modifyDate);
        sb.append(", ").append(captionOverallStatus);
        sb.append(", ").append(switchMentor);
        sb.append(", ").append(captionFollowRelease);
        sb.append(", ").append(explicitLearningStatus);
        sb.append(", ").append(learningSituation);
        sb.append(", ").append(sponsoringOrg);
        sb.append(", ").append(pcBanner);
        sb.append(", ").append(pcBannerPath);
        sb.append(", ").append(appBanner);
        sb.append(", ").append(appBannerPath);
        sb.append(", ").append(mentorState);
        sb.append(", ").append(open);
        sb.append(", ").append(updateDate);
        sb.append(", ").append(voiceToText);
        sb.append(", ").append(newLaunchVisitNum);
        sb.append(", ").append(recommendVisitNum);
        sb.append(", ").append(constructionType);
        sb.append(", ").append(skinType);
        sb.append(", ").append(subjectType);
        sb.append(", ").append(subjectMisCode);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseInfo from) {
        setId(from.getId());
        setOrganizationId(from.getOrganizationId());
        setName(from.getName());
        setCover(from.getCover());
        setCode(from.getCode());
        setPrice(from.getPrice());
        setIntegral(from.getIntegral());
        setCourseHour(from.getCourseHour());
        setCourseTime(from.getCourseTime());
        setCourseSecond(from.getCourseSecond());
        setCredit(from.getCredit());
        setDescription(from.getDescription());
        setBeginDate(from.getBeginDate());
        setEndDate(from.getEndDate());
        setCertificateId(from.getCertificateId());
        setCertificateType(from.getCertificateType());
        setCertificateTime(from.getCertificateTime());
        setCertificateUpdateTime(from.getCertificateUpdateTime());
        setStatus(from.getStatus());
        setPortalNum(from.getPortalNum());
        setType(from.getType());
        setSource(from.getSource());
        setReleaseTime(from.getReleaseTime());
        setReleaseMemberId(from.getReleaseMemberId());
        setReleaseOrgId(from.getReleaseOrgId());
        setDevelopMemberId(from.getDevelopMemberId());
        setDevelopTime(from.getDevelopTime());
        setCreateMemberId(from.getCreateMemberId());
        setCreateTime(from.getCreateTime());
        setCategoryId(from.getCategoryId());
        setAddType(from.getAddType());
        setVersionId(from.getVersionId());
        setLearnSequence(from.getLearnSequence());
        setIsSubject(from.getIsSubject());
        setStudyDays(from.getStudyDays());
        setStudyMemberCount(from.getStudyMemberCount());
        setRegisterMemberCount(from.getRegisterMemberCount());
        setAddPlanMemberCount(from.getAddPlanMemberCount());
        setStyles(from.getStyles());
        setTotalScore(from.getTotalScore());
        setScoreMemberCount(from.getScoreMemberCount());
        setAvgScore(from.getAvgScore());
        setShelveTime(from.getShelveTime());
        setOffTime(from.getOffTime());
        setIsPublic(from.getIsPublic());
        setDeleteFlag(from.getDeleteFlag());
        setDescriptionText(from.getDescriptionText());
        setPublishClient(from.getPublishClient());
        setPublishType(from.getPublishType());
        setBusinessType(from.getBusinessType());
        setLecturer(from.getLecturer());
        setSequence(from.getSequence());
        setSourceDetail(from.getSourceDetail());
        setVisits(from.getVisits());
        setShareSub(from.getShareSub());
        setUrl(from.getUrl());
        setCoverPath(from.getCoverPath());
        setCoverMaterialId(from.getCoverMaterialId());
        setCollectionCount(from.getCollectionCount());
        setShareCount(from.getShareCount());
        setAudienceOrg(from.getAudienceOrg());
        setCloudStatus(from.getCloudStatus());
        setCloudTime(from.getCloudTime());
        setCloudRule(from.getCloudRule());
        setAudiences(from.getAudiences());
        setDescriptionApp(from.getDescriptionApp());
        setRelativeGenseeId(from.getRelativeGenseeId());
        setIsSign(from.getIsSign());
        setIsParty(from.getIsParty());
        setUseVideoSpeed(from.getUseVideoSpeed());
        setSwitchHide(from.getSwitchHide());
        setHistoryHide(from.getHistoryHide());
        setCaptionFlag(from.getCaptionFlag());
        setModifyDate(from.getModifyDate());
        setCaptionOverallStatus(from.getCaptionOverallStatus());
        setSwitchMentor(from.getSwitchMentor());
        setCaptionFollowRelease(from.getCaptionFollowRelease());
        setExplicitLearningStatus(from.getExplicitLearningStatus());
        setLearningSituation(from.getLearningSituation());
        setSponsoringOrg(from.getSponsoringOrg());
        setPcBanner(from.getPcBanner());
        setPcBannerPath(from.getPcBannerPath());
        setAppBanner(from.getAppBanner());
        setAppBannerPath(from.getAppBannerPath());
        setMentorState(from.getMentorState());
        setOpen(from.getOpen());
        setUpdateDate(from.getUpdateDate());
        setVoiceToText(from.getVoiceToText());
        setNewLaunchVisitNum(from.getNewLaunchVisitNum());
        setRecommendVisitNum(from.getRecommendVisitNum());
        setConstructionType(from.getConstructionType());
        setSkinType(from.getSkinType());
        setSubjectType(from.getSubjectType());
        setSubjectMisCode(from.getSubjectMisCode());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseInfo> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseInfoEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseInfoRecord r = new com.zxy.product.course.jooq.tables.records.CourseInfoRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ORGANIZATION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ORGANIZATION_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ORGANIZATION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.NAME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.NAME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.NAME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COVER) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COVER, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COVER));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CODE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CODE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CODE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PRICE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PRICE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PRICE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.INTEGRAL) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.INTEGRAL, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.INTEGRAL));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COURSE_HOUR) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COURSE_HOUR, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COURSE_HOUR));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COURSE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COURSE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COURSE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COURSE_SECOND) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COURSE_SECOND, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COURSE_SECOND));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CREDIT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CREDIT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CREDIT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DESCRIPTION) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DESCRIPTION, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DESCRIPTION));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.BEGIN_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.BEGIN_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.BEGIN_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.END_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.END_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.END_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_UPDATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_UPDATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CERTIFICATE_UPDATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PORTAL_NUM) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PORTAL_NUM, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PORTAL_NUM));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SOURCE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SOURCE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SOURCE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELEASE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELEASE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELEASE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELEASE_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELEASE_MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELEASE_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELEASE_ORG_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELEASE_ORG_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELEASE_ORG_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DEVELOP_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DEVELOP_MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DEVELOP_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DEVELOP_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DEVELOP_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DEVELOP_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CREATE_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CREATE_MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CREATE_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CATEGORY_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CATEGORY_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CATEGORY_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ADD_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ADD_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ADD_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.VERSION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.VERSION_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.VERSION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.LEARN_SEQUENCE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.LEARN_SEQUENCE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.LEARN_SEQUENCE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_SUBJECT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_SUBJECT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_SUBJECT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STUDY_DAYS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STUDY_DAYS, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STUDY_DAYS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STUDY_MEMBER_COUNT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STUDY_MEMBER_COUNT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STUDY_MEMBER_COUNT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.REGISTER_MEMBER_COUNT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.REGISTER_MEMBER_COUNT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.REGISTER_MEMBER_COUNT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ADD_PLAN_MEMBER_COUNT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ADD_PLAN_MEMBER_COUNT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.ADD_PLAN_MEMBER_COUNT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STYLES) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STYLES, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.STYLES));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.TOTAL_SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.TOTAL_SCORE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.TOTAL_SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SCORE_MEMBER_COUNT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SCORE_MEMBER_COUNT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SCORE_MEMBER_COUNT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.AVG_SCORE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.AVG_SCORE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.AVG_SCORE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SHELVE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SHELVE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SHELVE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.OFF_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.OFF_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.OFF_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_PUBLIC) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_PUBLIC, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_PUBLIC));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DELETE_FLAG) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DELETE_FLAG, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DELETE_FLAG));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DESCRIPTION_TEXT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DESCRIPTION_TEXT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DESCRIPTION_TEXT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PUBLISH_CLIENT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PUBLISH_CLIENT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PUBLISH_CLIENT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PUBLISH_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PUBLISH_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PUBLISH_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.BUSINESS_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.BUSINESS_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.BUSINESS_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.LECTURER) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.LECTURER, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.LECTURER));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SEQUENCE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SEQUENCE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SEQUENCE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SOURCE_DETAIL) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SOURCE_DETAIL, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SOURCE_DETAIL));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.VISITS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.VISITS, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.VISITS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SHARE_SUB) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SHARE_SUB, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SHARE_SUB));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.URL) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.URL, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.URL));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COVER_PATH) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COVER_PATH, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COVER_PATH));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COVER_MATERIAL_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COVER_MATERIAL_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COVER_MATERIAL_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COLLECTION_COUNT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COLLECTION_COUNT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.COLLECTION_COUNT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SHARE_COUNT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SHARE_COUNT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SHARE_COUNT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.AUDIENCE_ORG) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.AUDIENCE_ORG, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.AUDIENCE_ORG));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CLOUD_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CLOUD_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CLOUD_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CLOUD_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CLOUD_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CLOUD_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CLOUD_RULE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CLOUD_RULE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CLOUD_RULE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.AUDIENCES) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.AUDIENCES, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.AUDIENCES));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DESCRIPTION_APP) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DESCRIPTION_APP, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.DESCRIPTION_APP));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELATIVE_GENSEE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELATIVE_GENSEE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RELATIVE_GENSEE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_SIGN) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_SIGN, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_SIGN));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_PARTY) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_PARTY, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.IS_PARTY));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.USE_VIDEO_SPEED) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.USE_VIDEO_SPEED, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.USE_VIDEO_SPEED));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SWITCH_HIDE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SWITCH_HIDE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SWITCH_HIDE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.HISTORY_HIDE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.HISTORY_HIDE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.HISTORY_HIDE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CAPTION_FLAG) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CAPTION_FLAG, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CAPTION_FLAG));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.MODIFY_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CAPTION_OVERALL_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CAPTION_OVERALL_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CAPTION_OVERALL_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SWITCH_MENTOR) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SWITCH_MENTOR, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SWITCH_MENTOR));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CAPTION_FOLLOW_RELEASE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CAPTION_FOLLOW_RELEASE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CAPTION_FOLLOW_RELEASE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.EXPLICIT_LEARNING_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.EXPLICIT_LEARNING_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.EXPLICIT_LEARNING_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.LEARNING_SITUATION) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.LEARNING_SITUATION, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.LEARNING_SITUATION));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SPONSORING_ORG) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SPONSORING_ORG, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SPONSORING_ORG));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PC_BANNER) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PC_BANNER, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PC_BANNER));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PC_BANNER_PATH) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PC_BANNER_PATH, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.PC_BANNER_PATH));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.APP_BANNER) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.APP_BANNER, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.APP_BANNER));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.APP_BANNER_PATH) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.APP_BANNER_PATH, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.APP_BANNER_PATH));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.MENTOR_STATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.MENTOR_STATE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.MENTOR_STATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.OPEN) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.OPEN, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.OPEN));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.UPDATE_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.UPDATE_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.UPDATE_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.VOICE_TO_TEXT) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.VOICE_TO_TEXT, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.VOICE_TO_TEXT));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.NEW_LAUNCH_VISIT_NUM) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.NEW_LAUNCH_VISIT_NUM, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.NEW_LAUNCH_VISIT_NUM));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RECOMMEND_VISIT_NUM) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RECOMMEND_VISIT_NUM, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.RECOMMEND_VISIT_NUM));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CONSTRUCTION_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CONSTRUCTION_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.CONSTRUCTION_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SKIN_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SKIN_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SKIN_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SUBJECT_TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SUBJECT_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SUBJECT_TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SUBJECT_MIS_CODE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SUBJECT_MIS_CODE, record.getValue(com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO.SUBJECT_MIS_CODE));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
