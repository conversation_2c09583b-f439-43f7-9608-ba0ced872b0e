/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseKnowledge;
import com.zxy.product.course.jooq.tables.interfaces.ICourseKnowledge;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record15;
import org.jooq.Row15;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 学习助手知识表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseKnowledgeRecord extends UpdatableRecordImpl<CourseKnowledgeRecord> implements Record15<String, String, String, String, Integer, String, String, Integer, Integer, Integer, String, String, Long, Long, Long>, ICourseKnowledge {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_knowledge.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_name</code>. 知识名称
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_name</code>. 知识名称
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_id</code>. 附件id
     */
    @Override
    public void setAttachmentId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_id</code>. 附件id
     */
    @Override
    public String getAttachmentId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_name</code>. 附件名称
     */
    @Override
    public void setAttachmentName(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_name</code>. 附件名称
     */
    @Override
    public String getAttachmentName() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_type</code>. 0 word 1 pdf
     */
    @Override
    public void setAttachmentType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_type</code>. 0 word 1 pdf
     */
    @Override
    public Integer getAttachmentType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_up_id</code>. 上传到成研的附件id
     */
    @Override
    public void setAttachmentUpId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_up_id</code>. 上传到成研的附件id
     */
    @Override
    public String getAttachmentUpId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_batch</code>. 上传到成研的附件批次号
     */
    @Override
    public void setAttachmentBatch(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_batch</code>. 上传到成研的附件批次号
     */
    @Override
    public String getAttachmentBatch() {
        return (String) get(6);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_status</code>. 状态 0 未发布 1 已发布
     */
    @Override
    public void setStatus(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_status</code>. 状态 0 未发布 1 已发布
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_delete</code>. 是否删除 0 未删除 1 已删除
     */
    @Override
    public void setDelete(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_delete</code>. 是否删除 0 未删除 1 已删除
     */
    @Override
    public Integer getDelete() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_finsh_status</code>. 向量化状态 0 未完成 1已完成
     */
    @Override
    public void setFinshStatus(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_finsh_status</code>. 向量化状态 0 未完成 1已完成
     */
    @Override
    public Integer getFinshStatus() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_knowledge</code>. 知识简介
     */
    @Override
    public void setKnowledge(String value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_knowledge</code>. 知识简介
     */
    @Override
    public String getKnowledge() {
        return (String) get(10);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_knowledge_text</code>. 知识简介文本
     */
    @Override
    public void setKnowledgeText(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_knowledge_text</code>. 知识简介文本
     */
    @Override
    public String getKnowledgeText() {
        return (String) get(11);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_shelve_time</code>. 首次发布时间
     */
    @Override
    public void setShelveTime(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_shelve_time</code>. 首次发布时间
     */
    @Override
    public Long getShelveTime() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(13, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(13);
    }

    /**
     * Setter for <code>course-study.t_course_knowledge.f_modify_date</code>. 更新时间
     */
    @Override
    public void setModifyDate(Long value) {
        set(14, value);
    }

    /**
     * Getter for <code>course-study.t_course_knowledge.f_modify_date</code>. 更新时间
     */
    @Override
    public Long getModifyDate() {
        return (Long) get(14);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record15 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row15<String, String, String, String, Integer, String, String, Integer, Integer, Integer, String, String, Long, Long, Long> fieldsRow() {
        return (Row15) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row15<String, String, String, String, Integer, String, String, Integer, Integer, Integer, String, String, Long, Long, Long> valuesRow() {
        return (Row15) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseKnowledge.COURSE_KNOWLEDGE.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseKnowledge.COURSE_KNOWLEDGE.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field5() {
        return CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_UP_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_BATCH;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return CourseKnowledge.COURSE_KNOWLEDGE.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field9() {
        return CourseKnowledge.COURSE_KNOWLEDGE.DELETE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return CourseKnowledge.COURSE_KNOWLEDGE.FINSH_STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field11() {
        return CourseKnowledge.COURSE_KNOWLEDGE.KNOWLEDGE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field12() {
        return CourseKnowledge.COURSE_KNOWLEDGE.KNOWLEDGE_TEXT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field13() {
        return CourseKnowledge.COURSE_KNOWLEDGE.SHELVE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field14() {
        return CourseKnowledge.COURSE_KNOWLEDGE.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field15() {
        return CourseKnowledge.COURSE_KNOWLEDGE.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getAttachmentId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getAttachmentName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value5() {
        return getAttachmentType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getAttachmentUpId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getAttachmentBatch();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value9() {
        return getDelete();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getFinshStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value11() {
        return getKnowledge();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value12() {
        return getKnowledgeText();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value13() {
        return getShelveTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value14() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value15() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value3(String value) {
        setAttachmentId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value4(String value) {
        setAttachmentName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value5(Integer value) {
        setAttachmentType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value6(String value) {
        setAttachmentUpId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value7(String value) {
        setAttachmentBatch(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value8(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value9(Integer value) {
        setDelete(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value10(Integer value) {
        setFinshStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value11(String value) {
        setKnowledge(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value12(String value) {
        setKnowledgeText(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value13(Long value) {
        setShelveTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value14(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord value15(Long value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledgeRecord values(String value1, String value2, String value3, String value4, Integer value5, String value6, String value7, Integer value8, Integer value9, Integer value10, String value11, String value12, Long value13, Long value14, Long value15) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        value13(value13);
        value14(value14);
        value15(value15);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseKnowledge from) {
        setId(from.getId());
        setName(from.getName());
        setAttachmentId(from.getAttachmentId());
        setAttachmentName(from.getAttachmentName());
        setAttachmentType(from.getAttachmentType());
        setAttachmentUpId(from.getAttachmentUpId());
        setAttachmentBatch(from.getAttachmentBatch());
        setStatus(from.getStatus());
        setDelete(from.getDelete());
        setFinshStatus(from.getFinshStatus());
        setKnowledge(from.getKnowledge());
        setKnowledgeText(from.getKnowledgeText());
        setShelveTime(from.getShelveTime());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseKnowledge> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseKnowledgeRecord
     */
    public CourseKnowledgeRecord() {
        super(CourseKnowledge.COURSE_KNOWLEDGE);
    }

    /**
     * Create a detached, initialised CourseKnowledgeRecord
     */
    public CourseKnowledgeRecord(String id, String name, String attachmentId, String attachmentName, Integer attachmentType, String attachmentUpId, String attachmentBatch, Integer status, Integer delete, Integer finshStatus, String knowledge, String knowledgeText, Long shelveTime, Long createTime, Long modifyDate) {
        super(CourseKnowledge.COURSE_KNOWLEDGE);

        set(0, id);
        set(1, name);
        set(2, attachmentId);
        set(3, attachmentName);
        set(4, attachmentType);
        set(5, attachmentUpId);
        set(6, attachmentBatch);
        set(7, status);
        set(8, delete);
        set(9, finshStatus);
        set(10, knowledge);
        set(11, knowledgeText);
        set(12, shelveTime);
        set(13, createTime);
        set(14, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseKnowledgeEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseKnowledgeEntity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseKnowledgeEntity)source;
        pojo.into(this);
        return true;
    }
}
