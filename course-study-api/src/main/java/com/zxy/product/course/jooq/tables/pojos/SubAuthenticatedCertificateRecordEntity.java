/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ISubAuthenticatedCertificateRecord;

import javax.annotation.Generated;


/**
 * 子认证-学员-证书记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubAuthenticatedCertificateRecordEntity extends BaseEntity implements ISubAuthenticatedCertificateRecord {

    private static final long serialVersionUID = 1L;

    private String  subAuthenticatedId;
    private String  memberId;
    private String  cancelOperatorId;
    private String  operatorId;
    private Long    cancelTime;
    private String  attachementId;
    private String  reason;
    private Integer deleteFlag;
    private Long    importTime;
    private Integer    status;

    public SubAuthenticatedCertificateRecordEntity() {}

    public SubAuthenticatedCertificateRecordEntity(SubAuthenticatedCertificateRecordEntity value) {
        this.subAuthenticatedId = value.subAuthenticatedId;
        this.memberId = value.memberId;
        this.cancelOperatorId = value.cancelOperatorId;
        this.operatorId = value.operatorId;
        this.cancelTime = value.cancelTime;
        this.attachementId = value.attachementId;
        this.reason = value.reason;
        this.deleteFlag = value.deleteFlag;
        this.importTime = value.importTime;
        this.status = value.status;
    }

    public SubAuthenticatedCertificateRecordEntity(
        String  id,
        Long    createTime,
        String  subAuthenticatedId,
        String  memberId,
        String  cancelOperatorId,
        String  operatorId,
        Long    cancelTime,
        String  attachementId,
        String  reason,
        Integer deleteFlag,
        Long    importTime,
        Integer    status
    ) {
        super.setId(id);
        super.setCreateTime(createTime);
        this.subAuthenticatedId = subAuthenticatedId;
        this.memberId = memberId;
        this.cancelOperatorId = cancelOperatorId;
        this.operatorId = operatorId;
        this.cancelTime = cancelTime;
        this.attachementId = attachementId;
        this.reason = reason;
        this.deleteFlag = deleteFlag;
        this.importTime = importTime;
        this.status = status;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getSubAuthenticatedId() {
        return this.subAuthenticatedId;
    }

    @Override
    public void setSubAuthenticatedId(String subAuthenticatedId) {
        this.subAuthenticatedId = subAuthenticatedId;
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getCancelOperatorId() {
        return this.cancelOperatorId;
    }

    @Override
    public void setCancelOperatorId(String cancelOperatorId) {
        this.cancelOperatorId = cancelOperatorId;
    }

    @Override
    public String getOperatorId() {
        return this.operatorId;
    }

    @Override
    public void setOperatorId(String operatorId) {
        this.operatorId = operatorId;
    }

    @Override
    public Long getCancelTime() {
        return this.cancelTime;
    }

    @Override
    public void setCancelTime(Long cancelTime) {
        this.cancelTime = cancelTime;
    }

    @Override
    public String getAttachementId() {
        return this.attachementId;
    }

    @Override
    public void setAttachementId(String attachementId) {
        this.attachementId = attachementId;
    }

    @Override
    public String getReason() {
        return this.reason;
    }

    @Override
    public void setReason(String reason) {
        this.reason = reason;
    }

    @Override
    public Integer getDeleteFlag() {
        return this.deleteFlag;
    }

    @Override
    public void setDeleteFlag(Integer deleteFlag) {
        this.deleteFlag = deleteFlag;
    }

    @Override
    public Long getImportTime() {
        return this.importTime;
    }

    @Override
    public void setImportTime(Long importTime) {
        this.importTime = importTime;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("SubAuthenticatedCertificateRecordEntity (");

        sb.append(getId());
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(subAuthenticatedId);
        sb.append(", ").append(memberId);
        sb.append(", ").append(cancelOperatorId);
        sb.append(", ").append(operatorId);
        sb.append(", ").append(cancelTime);
        sb.append(", ").append(attachementId);
        sb.append(", ").append(reason);
        sb.append(", ").append(deleteFlag);
        sb.append(", ").append(importTime);
        sb.append(", ").append(status);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISubAuthenticatedCertificateRecord from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setSubAuthenticatedId(from.getSubAuthenticatedId());
        setMemberId(from.getMemberId());
        setCancelOperatorId(from.getCancelOperatorId());
        setOperatorId(from.getOperatorId());
        setCancelTime(from.getCancelTime());
        setAttachementId(from.getAttachementId());
        setReason(from.getReason());
        setDeleteFlag(from.getDeleteFlag());
        setImportTime(from.getImportTime());
        setStatus(from.getStatus());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISubAuthenticatedCertificateRecord> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends SubAuthenticatedCertificateRecordEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.SubAuthenticatedCertificateRecordRecord r = new com.zxy.product.course.jooq.tables.records.SubAuthenticatedCertificateRecordRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.MEMBER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.MEMBER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_OPERATOR_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_OPERATOR_ID, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_OPERATOR_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.OPERATOR_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.OPERATOR_ID, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.OPERATOR_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.ATTACHEMENT_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.ATTACHEMENT_ID, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.ATTACHEMENT_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.REASON.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.REASON, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.REASON));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.DELETE_FLAG.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.DELETE_FLAG, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.DELETE_FLAG));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.IMPORT_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.IMPORT_TIME, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.IMPORT_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.STATUS, record.getValue(com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.STATUS));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
