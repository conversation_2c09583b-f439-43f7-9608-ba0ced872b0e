/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.AbilityRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 能力信息表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class Ability extends TableImpl<AbilityRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_ability</code>
     */
    public static final Ability ABILITY = new Ability();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<AbilityRecord> getRecordType() {
        return AbilityRecord.class;
    }

    /**
     * The column <code>course-study.t_ability.f_id</code>. ID
     */
    public final TableField<AbilityRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "ID");

    /**
     * The column <code>course-study.t_ability.f_level</code>. 能力级别
     */
    public final TableField<AbilityRecord, Integer> LEVEL = createField("f_level", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").nullable(false).defaultValue(org.jooq.impl.DSL.inline("1", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "能力级别");

    /**
     * The column <code>course-study.t_ability.f_name</code>. 能力名称
     */
    public final TableField<AbilityRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "能力名称");

    /**
     * The column <code>course-study.t_ability.f_code</code>. 能力编码
     */
    public final TableField<AbilityRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(45).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "能力编码");

    /**
     * The column <code>course-study.t_ability.f_description</code>. 描述
     */
    public final TableField<AbilityRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.VARCHAR.length(1024).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "描述");

    /**
     * The column <code>course-study.t_ability.f_status</code>. 能力状态 0:未发布,1:发布 2:取消发布
     */
    public final TableField<AbilityRecord, Integer> STATUS = createField("f_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "能力状态 0:未发布,1:发布 2:取消发布");

    /**
     * The column <code>course-study.t_ability.f_organization_id</code>. 部门id
     */
    public final TableField<AbilityRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false).defaultValue(org.jooq.impl.DSL.inline("''", org.jooq.impl.SQLDataType.VARCHAR)), this, "部门id");

    /**
     * The column <code>course-study.t_ability.f_create_time</code>. 创建时间
     */
    public final TableField<AbilityRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_ability.f_modify_date</code>. 修改时间
     */
    public final TableField<AbilityRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>course-study.t_ability.f_type</code>. 来源， 0：能力管理 1：学习地图
     */
    public final TableField<AbilityRecord, Integer> TYPE = createField("f_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "来源， 0：能力管理 1：学习地图");

    /**
     * The column <code>course-study.t_ability.f_ability_category</code>. 能力管理大类
     */
    public final TableField<AbilityRecord, Integer> ABILITY_CATEGORY = createField("f_ability_category", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "能力管理大类");

    /**
     * The column <code>course-study.t_ability.f_ability_sub_category</code>. 能力管理子类
     */
    public final TableField<AbilityRecord, String> ABILITY_SUB_CATEGORY = createField("f_ability_sub_category", org.jooq.impl.SQLDataType.VARCHAR.length(60).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "能力管理子类");

    /**
     * Create a <code>course-study.t_ability</code> table reference
     */
    public Ability() {
        this("t_ability", null);
    }

    /**
     * Create an aliased <code>course-study.t_ability</code> table reference
     */
    public Ability(String alias) {
        this(alias, ABILITY);
    }

    private Ability(String alias, Table<AbilityRecord> aliased) {
        this(alias, aliased, null);
    }

    private Ability(String alias, Table<AbilityRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "能力信息表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<AbilityRecord> getPrimaryKey() {
        return Keys.KEY_T_ABILITY_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<AbilityRecord>> getKeys() {
        return Arrays.<UniqueKey<AbilityRecord>>asList(Keys.KEY_T_ABILITY_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Ability as(String alias) {
        return new Ability(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public Ability rename(String name) {
        return new Ability(name, null);
    }
}
