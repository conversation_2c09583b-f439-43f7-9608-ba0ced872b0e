/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.SubAuthenticatedTmp;
import com.zxy.product.course.jooq.tables.interfaces.ISubAuthenticatedTmp;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record9;
import org.jooq.Row9;
import org.jooq.impl.UpdatableRecordImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;


/**
 * 子认证-维度导入表-临时表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubAuthenticatedTmpRecord extends UpdatableRecordImpl<SubAuthenticatedTmpRecord> implements Record9<String, String, String, String, String, Integer, String, Long, Timestamp>, ISubAuthenticatedTmp {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_id</code>. id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_id</code>. id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_name</code>. 姓名
     */
    @Override
    public void setName(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_name</code>. 姓名
     */
    @Override
    public String getName() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_code</code>. 编号
     */
    @Override
    public void setCode(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_code</code>. 编号
     */
    @Override
    public String getCode() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_file_id</code>. 文件id
     */
    @Override
    public void setFileId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_file_id</code>. 文件id
     */
    @Override
    public String getFileId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_sub_authenticated_id</code>. 子认证id
     */
    @Override
    public void setSubAuthenticatedId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_sub_authenticated_id</code>. 子认证id
     */
    @Override
    public String getSubAuthenticatedId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_delete_flag</code>. 是否删除 0 否 1是
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_delete_flag</code>. 是否删除 0 否 1是
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_creator</code>. 创建人
     */
    @Override
    public void setCreator(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_creator</code>. 创建人
     */
    @Override
    public String getCreator() {
        return (String) get(6);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(8);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record9 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, String, String, Integer, String, Long, Timestamp> fieldsRow() {
        return (Row9) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row9<String, String, String, String, String, Integer, String, Long, Timestamp> valuesRow() {
        return (Row9) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.NAME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CODE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.FILE_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.SUB_AUTHENTICATED_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field7() {
        return SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CREATOR;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field8() {
        return SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Timestamp> field9() {
        return SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getName();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getCode();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getFileId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getSubAuthenticatedId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value7() {
        return getCreator();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value8() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Timestamp value9() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord value2(String value) {
        setName(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord value3(String value) {
        setCode(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord value4(String value) {
        setFileId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord value5(String value) {
        setSubAuthenticatedId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord value6(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord value7(String value) {
        setCreator(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord value8(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord value9(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmpRecord values(String value1, String value2, String value3, String value4, String value5, Integer value6, String value7, Long value8, Timestamp value9) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISubAuthenticatedTmp from) {
        setId(from.getId());
        setName(from.getName());
        setCode(from.getCode());
        setFileId(from.getFileId());
        setSubAuthenticatedId(from.getSubAuthenticatedId());
        setDeleteFlag(from.getDeleteFlag());
        setCreator(from.getCreator());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISubAuthenticatedTmp> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SubAuthenticatedTmpRecord
     */
    public SubAuthenticatedTmpRecord() {
        super(SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP);
    }

    /**
     * Create a detached, initialised SubAuthenticatedTmpRecord
     */
    public SubAuthenticatedTmpRecord(String id, String name, String code, String fileId, String subAuthenticatedId, Integer deleteFlag, String creator, Long createTime, Timestamp modifyDate) {
        super(SubAuthenticatedTmp.SUB_AUTHENTICATED_TMP);

        set(0, id);
        set(1, name);
        set(2, code);
        set(3, fileId);
        set(4, subAuthenticatedId);
        set(5, deleteFlag);
        set(6, creator);
        set(7, createTime);
        set(8, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedTmpEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedTmpEntity pojo = (com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedTmpEntity)source;
        pojo.into(this);
        return true;
    }
}
