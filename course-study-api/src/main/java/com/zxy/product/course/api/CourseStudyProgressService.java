package com.zxy.product.course.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.*;
import org.jooq.impl.TableImpl;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

/**
 * Created by keeley on 16/11/2.
 */
@RemoteService(timeout = 60000)
public interface CourseStudyProgressService {

    Integer AUDIT_PASS = 1; // 作业审核通过

    /**
     * 课程学习列表
     *
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseStudyProgress> page(int pageNum, int pageSize, String courseId, Optional<String> memberName,
            Optional<String> memberReadName, Optional<String> organizationId, Optional<Integer> finishStatus,
            Optional<Integer> requiredStatus, Optional<Long> beginBeginDate, Optional<Long> beginEndDate,
            Optional<Long> beginRegisterDate, Optional<Long> endRegisterDate);

    /**
     * 课程学习列表
     * @param registerEndDate
     * @param registerBeginDate
     * @param beginEndDate
     * @param beginBeginDate
     *
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseStudyProgress> findList(String courseId, Optional<String> memberName,
                                          Optional<String> memberReadName, Optional<String> organizationId, Optional<Integer> finishStatus,
                                          Optional<Integer> requiredStatus, Optional<Long> studyTime, Optional<Long> registerTime, Optional<Long> beginBeginDate, Optional<Long> beginEndDate, Optional<Long> registerBeginDate, Optional<Long> registerEndDate);

    /**
     * 为培训班提供查找课程状态
     * @param member
     * @param courseIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseStudyProgress> findList(List<String> member,List<String> courseIds);

    /**
     * 为培训班提供查找当前登录人的课程状态
     * @param member
     * @param courseIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseStudyProgress> getStatus(String member,List<String> courseIds);

    /**
     * 返回非未完成的其他状态的课程ID
     * @param member
     * @param courseIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> getStatusNe(String member,List<String> courseIds);

    @Transactional
    int delete(String id);

    /**
     * 标记完成
     *
     * @param id
     * @return
     */
    @Transactional
    CourseStudyProgress markCompleted(String id, String memberId);

    /**
     * 批量标记完成
     *
     * @param map@return
     */
    @Transactional
    int markCompleted(Map<String,String> map,String markMemberId);

    /**
     * 所有注册记录
     *
     * @param memberId
     * @param courseId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseRegister> findRegisters(String memberId, String courseId);

    /**
     * 章节学习列表
     *
     * @param referenceId 节参考ID
     * @param sectionType
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseSectionStudyProgress> findCourseSectionStudyList(int page, int pageSize, String referenceId,
            Integer sectionType, Optional<String> memberName, Optional<String> loginId, Optional<Long> startTime,
            Optional<Long> endTime, String organizationId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseSectionStudyProgress> findCourseSectionStudyList(String referenceId,
             Integer sectionType, Optional<String> memberName, Optional<String> loginId, Optional<Long> startTime,
             Optional<Long> endTime, String organizationId);

    @Transactional
    Optional<CourseStudyProgress> getByMemberIdAndCourseId(String memberId, String courseId);
    /**
     * 学习进度审核
     *
     * @param auditMemberId
     * @param sectionProgressId
     * @param score
     * @param comments
     * @param auditPass
     * @return
     */
    @Transactional
    CourseSectionStudyProgress auditSectionStudyProgress(String auditMemberId, String sectionProgressId, Integer auditPass, Optional<String> courseId,
            Optional<String> comments, Optional<Integer> score);
    /**
     * 学习进度审核
     * (增加参数) 重写防止远程调用引用
     * @param auditMemberId
     * @param sectionProgressId
     * @param score
     * @param comments
     * @param auditPass
     * @param
     * @return
     */
    @Transactional
    CourseSectionStudyProgress auditSectionStudyProgress(String auditMemberId, String sectionProgressId, Integer auditPass,
                                                         Optional<String> courseId, Optional<String> comments,
                                                         Optional<Integer> score, Optional<String> beAuditedId);
    /**
     *  视频、音频更新进度
     * @param memberId
     * @param sectionId 节ID
     * @param clientType 客户端类型
     * @param studyTime 单次学习时长
     * @param videoTotalTime  视频总时长
     * @param beginTime  学习开始时间
     * @param lessonLocation  最后标记位
     * @return
     */
    @Transactional
    CourseSectionStudyProgress updateVideoStudyProgress(String memberId, String sectionId, int clientType, int studyTime, int videoTotalTime, Long beginTime, Optional<Integer> lessonLocation);

    /**
     * 更新普通课程进度
     * @param sectionId
     * @param clientType
     * @param beginTime
     * @return
     */
    @Transactional
    CourseSectionStudyProgress updateDocStudyProgress(String memberId, String sectionId, int clientType,Optional<Integer> lessonLocation, Long beginTime);

    /**
     * 专题URL进度更新 - 打开即完成
     * @param sectionId
     * @param clientType
     * @param beginTime
     * @return
     */
    @Transactional
    CourseSectionStudyProgress updateUrlStudyProgress(String memberId, String sectionId, int clientType,Optional<Integer> lessonLocation, Long beginTime, List<String> domains);

    /**
     * 用户提交任务
     *  @param sectionId
     * @param memberId
     * @param clientType
     * @return
     */
    @Transactional
    CourseSectionStudyProgress submitProgress(String sectionId, String memberId, int clientType,
            Optional<List<CourseSectionProgressAttachment>> sectionAttachments);

    /**
     * 开始学习
     *  @param sectionId
     * @param memberId
     * @param clientType
     * @return
     */
    @Transactional
    CourseSectionStudyProgress startStudy(String sectionId, String memberId, int clientType);


    /**
     * 批量插入节日志
     *
     * @param logList
     * @return
     */
    @Transactional
    int insertCourseSectionStudyLogList(List<CourseSectionStudyLog> logList);

    /**
     * 根据节进度ID获取节进度信息
     * update for xdn 添加courseId参数判断分表
     *
     * @param id
     * @param memberId
     * @param currentUserId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    CourseSectionStudyProgress getSectionStudyProgressById(String id, Optional<String> courseId, String memberId);

    /**
     * 更新课程学习进度
     *
     * @param memberId
     * @param courseId
     * @return
     */
    @Transactional
    CourseStudyProgress updateCourseStudyProgress(String memberId, String courseId, Long commitTime);

    /**
     * 批量插入课程学习进度
     *
     * @param courseProgresses
     * @return
     */
    @Transactional
    int insertList(List<CourseStudyProgress> courseProgresses);

    /**
     * 批量更新课程学习进度
     *
     * @param courseProgresses
     * @return
     */
    @Transactional
    int updateList(List<CourseStudyProgress> courseProgresses);

    /**
     * 批量插入节学习进度
     *
     * @param sectionProgresses
     * @return
     */
    @Transactional
    int insertSectionProgressList(List<CourseSectionStudyProgress> sectionProgresses);

    /**
     * 根据用户id和课程id查询课程学习进度
     *
     * @param memberId
     * @param courseId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Optional<CourseStudyProgress> findByMemberIdAndCourseId(String memberId, String courseId);

    /**
     * 查询内容学习排行榜(学习累计时长逆序)
     *
     * @param businessId
     * @param limitCount
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseStudyProgress> findStudyProgressList(String businessId, int limitCount,List<String>exceludeIds);
    /**
     * 查询内容学习排行榜(学习累计时长逆序)
     *
     * @param businessId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseStudyProgress> findStudyProgressPageList(int page, int pageSize, String businessId,List<String>exceludeIds);

    /**
     * 异步处理节日志、课程进度
     * update for xdn 添加courseId参数用来判断分表
     * @param sectionProgressId
     */
    @Transactional
    CourseSectionStudyProgress updateSectionProgressAsync(String sectionProgressId, String courseId, Optional<Integer> clientType, Optional<Integer> studentTime, Long commitTime,String memberId);

    /**
     * 异步处理节日志、课程进度(适用考试、调研、评估)
     * @param sectionId
     * @param memberId
     * @param finishStatus
     * @param score
     * @return
     */
    @Transactional
    CourseSectionStudyProgress updateSectionProgressAsync(String sectionId, String memberId, int finishStatus, Integer clientType, Optional<Integer> score, Long beginTime, Long submitTime);

    /**
     * 查询节学习流水
     *
     * @param memberId
     * @param sectionId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseSectionStudyLog> getSectionStudyLogs(String memberId, String sectionId);


    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseChapterSection> findCourseSectionStudyProgress(String courseId, String memberId);

    /**
     * 统计用户学习时长
     * @return key=来源,value=时长
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<String, Integer> getStudyTimeStatistics(List<String> memberIds, Optional<Long> startTime, Optional<Long> endTime);

    /**
     * 个人中心我的档案学习记录
     * @param pageNum
     * @param pageSize
     * @param businessType
     * @param currentMemberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseStudyProgress> personPage(int pageNum, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy,
                                                Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired,
                                                Optional<Long> startTime, Optional<Long> endTime);

    /**
     * 个人中心我的课程及我的档案
     * @param pageNum
     * @param pageSize
     * @param businessType
     * @param currentMemberId
     * @param findStudy
     * @param name
     * @param finishStatus
     * @param isRequired
     * @param studyTimeOrder
     * @param registerTimeOrder
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseStudyProgress> personCourse(int pageNum, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy,
                                                Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired,
                                                Optional<String> studyTimeOrder);


    /**
     * 个人中心我的课程，兼容历史迁移数据
     * @param pageNum
     * @param pageSize
     * @param businessType
     * @param currentMemberId
     * @param findStudy
     * @param name
     * @param finishStatus
     * @param isRequired
     * @param studyTimeOrder
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseStudyProgress> personCourseUnionHistory(int pageNum, int pageSize, Integer businessType, String currentMemberId, Optional<Integer> findStudy,
                                                              Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired,

                                                              Optional<String> studyTimeOrder);


    /**
     * 个人中心-我的课程、专题去掉count查询
     * @param pageNum
     * @param pageSize
     * @param businessType
     * @param currentMemberId
     * @param findStudy
     * @param name
     * @param finishStatus
     * @param isRequired
     * @param studyTimeOrder
     * @param startTime
     * @param endTime
     * @param courseIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<String, Object> personCourseUnionHistoryMap(int pageNum, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy,
                                                    Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired,

                                                    Optional<String> studyTimeOrder, boolean pageSwitch, Optional<Long> startTime, Optional<Long> endTime,
                                                    Optional<List<String>> courseIds);

    /**
     * 个人中心我的档案:课程、专题学习记录，查所有记录
     * @param businessType
     * @param currentMemberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseStudyProgress> personAll(Integer businessType, List<String> currentMemberId);

    /**
     * 学习时长排行榜
     * @param organizationId
     * @param size
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<StudyRank> rankForMemberStudyTime(String organizationId, int size);

    /**
     * 统计总学习时长
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer getPersonStudyTotalTime(String memberId);

    /**
     * 统计课程学习人数
     * @param courseId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer totalStudyMember(String courseId);

    /**
     * 课程完成数据排行版
     * @param organizationId
     * @param size
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<StudyRank> rankForMemberCourse(String organizationId, int size);

    /**
     * 放弃
     * @param id
     * @return
     */
    @Transactional
    CourseStudyProgress giveUpStudyProgress(String id);


    /**
     * 放弃后重新学习,回滚学习状态
     * @param studyProgress
     * @return
     */
    @Transactional
    CourseStudyProgress againStudy(CourseStudyProgress studyProgress);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Optional<CourseStudyProgress> getOptional(String id);

    /**
     * 根据推送id/人员id查询推送学习进度
     * @param pushId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseRegister> findProgressByPushId(String pushId, String memberId);

    /**
     *  查询人对应的课程学习时长
     * @param courseIds
     * @param memberIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<Map<String, Object>> totalStudyMember(String courseIds, String memberIds);

    /**
     * 查询这个人的所有包含的课程进度
     * @param courseIds
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseStudyProgress> findProgressByCourseIds(String courseIds, String memberId);

 	/**
     * 查询所有未完成的必修课程/专题
     * @param businessType 0-课程,1-学习路径,2-专题
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseStudyProgress> findRequiredUnfinished(Integer businessType);

    /**
     * 查询每个人对应每个课程的学习时长
     */
    List<CourseStudyProgress> findProgressByCourseIdsAndMemberIds(List<String> courseIds, List<String> memberIds);
    /**
     * 查询当前用户的作业批阅情况
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseSectionStudyProgress> findWorksByAuditMemberId(int page, int pageSize, String memberId, Optional<Integer> finishStatus);

    /**
     * 查询当前用户的学时排名
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer getCurrentMemberStudyTimeRank(String memberId, String organizationId);

    /**
     * 查询当前用户在课程or专题中的学习时长排名
     * @param currentMemberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer getListByCurrentMemberId(String currentMemberId, String businessId, List<String>exceludeIds);

    /**
     * 查询当前用户的课程数排名
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer getCurrentMemberCourseRank(String memberId, String organizationId);

    /**
     * 创建节进度
     * @param progress
     * @return
     */
    @Transactional
    CourseSectionStudyProgress insertSectionProgress(CourseSectionStudyProgress progress);

    /**
     * 根据专题id，用户id查询节进度
     * @param courseId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseSectionStudyProgress> findSectionProgressByCourseId(String courseId, String memberId);

    /**
     * 根据节id查询每节的学习人数
     * @param ids
     * @return Map<节id, 学习人数>
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<String, Integer> getSectionStudyMemberCount(List<String> ids,String memberId);

    /**
     * 用于作业审核审计查询课程名称
     * @param progressId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    CourseInfo findCourseInfoByProgressId(String progressId);

    /**
     * 个人中心，查询总学习时长（包括历史和网大三期）
     * @param memberIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<String, Integer> getHistoryAndNowDuration(List<String> memberIds);

	    /**
     * csv月度学习总时长
     * @param lastMonthFirstDay
     * @param monthFirstDay
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
	List<CourseStudyProgress> findMonthCourseStudyProgress(Integer page, Integer pageSize, Optional<Long> lastMonthFirstDay, Optional<Long> monthFirstDay);

    /**
     * csv月度学习总时长数据量
     * @param lastMonthFirstDay
     * @param monthFirstDay
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
	Integer findMonthCourseStudyProgressCount(Optional<Long> lastMonthFirstDay, Optional<Long> monthFirstDay);

    /**
     * 为培训班提供，查看必修课学习状态
     * @param courseIds
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer countStatus(List<String> courseIds,String memberId);

    /**
     * 异步更新专题学习知识的记录
     * @param currentUserId
     * @param resourceId
     * @param clientType
     * @param beginTime
     * @return
     */
    @Transactional
	void updateKnowledgeStudyProgressAsync(String currentUserId, String resourceId,
			Long beginTime);

    /**
     * 查询是否获得完成所有学习(提供给：网格长认证考试)
     * @param memberId
     * @param courseIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Boolean finishAllCourse(String memberId, List<String> courseIds);

    /**
     *  个人中心 学习画像 学习时长趋势
     */
    @Transactional
    Map<String, BigDecimal> findStudyTimeTendency(String memberId, String organizationId, Integer size, String type);

    /**
     *  更新学习卡片
     */
    @Transactional
    Map<String, List<CourseInfo>> updateStudyCard(String memberId,String courseId,Integer size);

    /**
     *  距离24点时间
     */
    @Transactional
    int timeLeftInSeconds();

    /**
     *  获取学习卡片缓存
     */
    @Transactional
    Map<String, List<CourseInfo>> getCacheStudyCard(String cacheKey);

    /**
     *  设置学习卡片缓存
     */
    @Transactional
    void setCacheStudyCard(String key, Map<String, List<CourseInfo>> studyCard, int time);


    /**
     * 计算对应课程/专题必须课程的完成率
     * @param courseId 课程id
     * @param memberId 人员id
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    Integer calculateRequiredRate(String courseId,String memberId);

    /**
     * 计算对应课程/专题必须课程的完成率
     * @param courseId 课程id
     * @param memberId 人员id
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    CourseStudyProgress calculateRequiredRateToMap(String courseId, String memberId);

    /**
     * 获取当前用户课程完成时间
     * @param courseId 课程id
     * @param memberId 用户id
     * @return 完成时间
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    Long getCourseFinishTime(String courseId,String memberId);


    /**
     * 计算对应课程/专题必须课程的完成率
     * @param courseId 课程id
     * @param memberIds 人员ids
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    Map<String,Integer> calculateRequiredRate(String courseId,List<String> memberIds);

    /**
     * 批量查找用户对应的的课程的版本号
     * @param memberId 用户id
     * @param courseIds 需要查找的课程id
     * @return map<>k:课程id，v：版本号 </>
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String,String> findCourseVersionIds(String memberId,List<String> courseIds);

    @Transactional
    CourseSectionStudyProgress updateBroadcastStudyProgress(String currentUserId, String sectionId, Integer clientType, Optional<Integer> lessonLocation, Long beginTime);

    List<CourseSectionProgressAttachment> getProgressAttachment(String sectionProgressId);

    // 查询当前用户学习该专题的学习状态
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String, List<CourseStudyProgress>> getFinishStatus(TableImpl<?> table, Set<String> subjectIds, List<String> memberIds);

    /**
     * 知识测验进度更新
     *
     * @param sectionId
     * @param memberId
     * @param finishTime
     * @return
     */
    @Transactional
    CourseSectionStudyProgress updateCoachTestsStudyProgress(String sectionId, String memberId, Integer finishStatus, Long finishTime);


    /**
     * 课程学习列表
     * @param pageNum
     * @param pageSize
     * @param courseId
     * @param memberName
     * @param memberReadName
     * @param organizationPath
     * @param finishStatus
     * @param requiredStatus
     * @param isDisable
     * @param beginBeginDate
     * @param beginEndDate
     * @param beginLearnDate
     * @param endLearnDate
     * @param beginRegisterDate
     * @param endRegisterDate
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseStudyProgress> findPage(int pageNum, int pageSize, String courseId, Optional<String> memberName,
                                              Optional<String> memberReadName, Optional<String> organizationPath, Optional<Integer> finishStatus,
                                              Optional<Integer> requiredStatus, Optional<Integer>isDisable, Optional<Long> beginBeginDate, Optional<Long> beginEndDate,
                                              Optional<Long> beginLearnDate, Optional<Long> endLearnDate,Optional<Long> beginRegisterDate, Optional<Long> endRegisterDate);

    /**
     *  查询指定课程数组,同专题的时长
     * <br/>
     * @param id 专题id
     * @param memberIds 用户ids
     * @return  {key= memberId, value=studyTotalTime}
     * <AUTHOR>
     * @date 18/12/11 19:34
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    Map<String,Integer> sumStudyTimeByIdAndMemberIds(String id,List<String> memberIds);

    /**
     *  查询指定人和专题的已完成的专题章节数量
     * <br/>
     * @param id
     * @param memberIds
     * @return java.util.Map<java.lang.String , java.lang.Integer>
     * <AUTHOR>
     * @date 18/12/11 20:18
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    Map<String,Integer> sumFinishSectionByIdAndMemberIds(String id,List<String> memberIds);

    /**
     * 查询专题章节进度详情
     * @param memberId 人员ID
     * @param subjectId 专题ID
     * @return 专题章节进度
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    List<CourseSectionStudyProgress> subjectSectionProgress(String memberId,String subjectId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<String, Object> personCourseListMapTutor(int pageNum, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy,
                                                 Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired,

                                                 Optional<String> studyTimeOrder, boolean pageSwitch, Optional<Long> startTime, Optional<Long> endTime,
                                                 Optional<List<String>> courseIds);
    /**
     * 查询专题章节进度详情
     *
     * @param memberId  人员ID
     * @param subjectId 专题ID
     * @param page      页码
     * @param pageSize  页大小
     * @return 专题章节进度
     */
    @Transactional(rollbackFor = Exception.class, propagation = Propagation.REQUIRED)
    PagedResult<CourseSectionStudyProgress> subjectSectionProgress(String memberId, String subjectId, Integer page, Integer pageSize);


    @Transactional
    void updateConcentrateStudyHours(List<String> courseIds,String subjectId,Map<String, Integer> sectionIdAndConcentrateHoursMap);

    @Transactional
    void updateConcentrateStudyHoursByMember(String memberId,List<String> courseIds,String subjectId,Map<String, Integer> sectionIdAndConcentrateHoursMap);
}
