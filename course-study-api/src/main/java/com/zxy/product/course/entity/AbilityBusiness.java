package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.AbilityBusinessEntity;

public class AbilityBusiness extends AbilityBusinessEntity {

    /**
     * 内容类型-课程
     */
    public static final Integer COURSE_TYPE = 1;
    /**
     * 内容类型-专题
     */
    public static final Integer SUBJECT_TYPE = 2;
    /**
     * 内容类型-知识
     */
    public static final Integer KNOWLEDGE_TYPE = 15;
    /**
     * 必修
     */
    public static final Integer REQUIRE = 1;
    /**
     * 选修
     */
    public static final Integer NOT_REQUIRE = 0;

    /**
     * 一个能力最多可加入资源数
     */
    public static final Integer RESOURCE_MAX_NUM = 1000;

    /**
     * 资源
     */
    private CourseInfo courseInfo;
    /**
     * 当前库中的最大排序
     */
    private Integer maxSequence;
    /**
     * 业务编码
     */
    private String businessCode;
    /**
     * 课程&专题名称
     */
    private String businessName;

    private String name;

    private Integer sectionType;

    private Integer required;

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Integer getSectionType() {
        return sectionType;
    }

    public void setSectionType(Integer sectionType) {
        this.sectionType = sectionType;
    }

    public Integer getRequired() {
        return required;
    }

    public void setRequired(Integer required) {
        this.required = required;
    }

    public Integer getMaxSequence() {
        return maxSequence;
    }

    public void setMaxSequence(Integer maxSequence) {
        this.maxSequence = maxSequence;
    }

    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }

    public String getBusinessCode() {
        return businessCode;
    }

    public void setBusinessCode(String businessCode) {
        this.businessCode = businessCode;
    }

    public String getBusinessName() {
        return businessName;
    }

    public void setBusinessName(String businessName) {
        this.businessName = businessName;
    }
}
