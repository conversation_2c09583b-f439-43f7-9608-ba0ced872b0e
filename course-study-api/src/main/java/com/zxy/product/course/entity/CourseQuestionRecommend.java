package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseQuestionRecommendEntity;

import java.util.Objects;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/10:37
 * @Description:
 */
public class CourseQuestionRecommend extends CourseQuestionRecommendEntity {
    private static final long serialVersionUID = -7518863539720052065L;

    //是否为推荐问题 0 否 1 是
    public static Integer RECOMMEND_YES = 1;
    public static Integer RECOMMEND_NO = 0;

    private Member member;

    public Member getMember() {
        return member;
    }

    public void setMember(Member member) {
        this.member = member;
    }

    public static String getRecommendStr(Integer recommend){
        if(Objects.equals(recommend, RECOMMEND_YES)){
            return "是";
        }
        return "否";
    }

}
