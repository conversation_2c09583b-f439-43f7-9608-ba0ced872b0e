/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProgressHl;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudyProgressHlEntity extends BaseEntity implements ICourseStudyProgressHl {

    private static final long serialVersionUID = 1L;

    private String    memberId;
    private String    courseId;
    private Long      beginTime;
    private Integer   type;
    private Integer   isRequired;
    private Integer   finishStatus;
    private Long      finishTime;
    private Integer   studyTotalTime;
    private Long      registerTime;
    private Long      lastAccessTime;
    private String    courseVersionId;
    private String    markMemberId;
    private Long      markTime;
    private Integer   completedRate;
    private String    currentChapterId;
    private String    currentSectionId;
    private String    pushId;
    private Integer   visits;
    private Long      lastModifyTime;
    private Timestamp modifyDate;
    private Long      subjectFinishTime;
    private Integer   completionTimes;
    private Long      latestCompletionTime;
    private Long      studyHoursRecordTime;
    private Integer   concentrateStudyHours;

    public CourseStudyProgressHlEntity() {}

    public CourseStudyProgressHlEntity(CourseStudyProgressHlEntity value) {
        this.memberId = value.memberId;
        this.courseId = value.courseId;
        this.beginTime = value.beginTime;
        this.type = value.type;
        this.isRequired = value.isRequired;
        this.finishStatus = value.finishStatus;
        this.finishTime = value.finishTime;
        this.studyTotalTime = value.studyTotalTime;
        this.registerTime = value.registerTime;
        this.lastAccessTime = value.lastAccessTime;
        this.courseVersionId = value.courseVersionId;
        this.markMemberId = value.markMemberId;
        this.markTime = value.markTime;
        this.completedRate = value.completedRate;
        this.currentChapterId = value.currentChapterId;
        this.currentSectionId = value.currentSectionId;
        this.pushId = value.pushId;
        this.visits = value.visits;
        this.lastModifyTime = value.lastModifyTime;
        this.modifyDate = value.modifyDate;
        this.subjectFinishTime = value.subjectFinishTime;
        this.completionTimes = value.completionTimes;
        this.latestCompletionTime = value.latestCompletionTime;
        this.studyHoursRecordTime = value.studyHoursRecordTime;
        this.concentrateStudyHours = value.concentrateStudyHours;
    }

    public CourseStudyProgressHlEntity(
        String    id,
        String    memberId,
        String    courseId,
        Long      beginTime,
        Integer   type,
        Integer   isRequired,
        Integer   finishStatus,
        Long      finishTime,
        Integer   studyTotalTime,
        Long      registerTime,
        Long      lastAccessTime,
        Long      createTime,
        String    courseVersionId,
        String    markMemberId,
        Long      markTime,
        Integer   completedRate,
        String    currentChapterId,
        String    currentSectionId,
        String    pushId,
        Integer   visits,
        Long      lastModifyTime,
        Timestamp modifyDate,
        Long      subjectFinishTime,
        Integer   completionTimes,
        Long      latestCompletionTime,
        Long      studyHoursRecordTime,
        Integer   concentrateStudyHours
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.courseId = courseId;
        this.beginTime = beginTime;
        this.type = type;
        this.isRequired = isRequired;
        this.finishStatus = finishStatus;
        this.finishTime = finishTime;
        this.studyTotalTime = studyTotalTime;
        this.registerTime = registerTime;
        this.lastAccessTime = lastAccessTime;
        super.setCreateTime(createTime);
        this.courseVersionId = courseVersionId;
        this.markMemberId = markMemberId;
        this.markTime = markTime;
        this.completedRate = completedRate;
        this.currentChapterId = currentChapterId;
        this.currentSectionId = currentSectionId;
        this.pushId = pushId;
        this.visits = visits;
        this.lastModifyTime = lastModifyTime;
        this.modifyDate = modifyDate;
        this.subjectFinishTime = subjectFinishTime;
        this.completionTimes = completionTimes;
        this.latestCompletionTime = latestCompletionTime;
        this.studyHoursRecordTime = studyHoursRecordTime;
        this.concentrateStudyHours = concentrateStudyHours;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getCourseId() {
        return this.courseId;
    }

    @Override
    public void setCourseId(String courseId) {
        this.courseId = courseId;
    }

    @Override
    public Long getBeginTime() {
        return this.beginTime;
    }

    @Override
    public void setBeginTime(Long beginTime) {
        this.beginTime = beginTime;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public Integer getIsRequired() {
        return this.isRequired;
    }

    @Override
    public void setIsRequired(Integer isRequired) {
        this.isRequired = isRequired;
    }

    @Override
    public Integer getFinishStatus() {
        return this.finishStatus;
    }

    @Override
    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }

    @Override
    public Long getFinishTime() {
        return this.finishTime;
    }

    @Override
    public void setFinishTime(Long finishTime) {
        this.finishTime = finishTime;
    }

    @Override
    public Integer getStudyTotalTime() {
        return this.studyTotalTime;
    }

    @Override
    public void setStudyTotalTime(Integer studyTotalTime) {
        this.studyTotalTime = studyTotalTime;
    }

    @Override
    public Long getRegisterTime() {
        return this.registerTime;
    }

    @Override
    public void setRegisterTime(Long registerTime) {
        this.registerTime = registerTime;
    }

    @Override
    public Long getLastAccessTime() {
        return this.lastAccessTime;
    }

    @Override
    public void setLastAccessTime(Long lastAccessTime) {
        this.lastAccessTime = lastAccessTime;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String getCourseVersionId() {
        return this.courseVersionId;
    }

    @Override
    public void setCourseVersionId(String courseVersionId) {
        this.courseVersionId = courseVersionId;
    }

    @Override
    public String getMarkMemberId() {
        return this.markMemberId;
    }

    @Override
    public void setMarkMemberId(String markMemberId) {
        this.markMemberId = markMemberId;
    }

    @Override
    public Long getMarkTime() {
        return this.markTime;
    }

    @Override
    public void setMarkTime(Long markTime) {
        this.markTime = markTime;
    }

    @Override
    public Integer getCompletedRate() {
        return this.completedRate;
    }

    @Override
    public void setCompletedRate(Integer completedRate) {
        this.completedRate = completedRate;
    }

    @Override
    public String getCurrentChapterId() {
        return this.currentChapterId;
    }

    @Override
    public void setCurrentChapterId(String currentChapterId) {
        this.currentChapterId = currentChapterId;
    }

    @Override
    public String getCurrentSectionId() {
        return this.currentSectionId;
    }

    @Override
    public void setCurrentSectionId(String currentSectionId) {
        this.currentSectionId = currentSectionId;
    }

    @Override
    public String getPushId() {
        return this.pushId;
    }

    @Override
    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    @Override
    public Integer getVisits() {
        return this.visits;
    }

    @Override
    public void setVisits(Integer visits) {
        this.visits = visits;
    }

    @Override
    public Long getLastModifyTime() {
        return this.lastModifyTime;
    }

    @Override
    public void setLastModifyTime(Long lastModifyTime) {
        this.lastModifyTime = lastModifyTime;
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public Long getSubjectFinishTime() {
        return this.subjectFinishTime;
    }

    @Override
    public void setSubjectFinishTime(Long subjectFinishTime) {
        this.subjectFinishTime = subjectFinishTime;
    }

    @Override
    public Integer getCompletionTimes() {
        return this.completionTimes;
    }

    @Override
    public void setCompletionTimes(Integer completionTimes) {
        this.completionTimes = completionTimes;
    }

    @Override
    public Long getLatestCompletionTime() {
        return this.latestCompletionTime;
    }

    @Override
    public void setLatestCompletionTime(Long latestCompletionTime) {
        this.latestCompletionTime = latestCompletionTime;
    }

    @Override
    public Long getStudyHoursRecordTime() {
        return this.studyHoursRecordTime;
    }

    @Override
    public void setStudyHoursRecordTime(Long studyHoursRecordTime) {
        this.studyHoursRecordTime = studyHoursRecordTime;
    }

    @Override
    public Integer getConcentrateStudyHours() {
        return this.concentrateStudyHours;
    }

    @Override
    public void setConcentrateStudyHours(Integer concentrateStudyHours) {
        this.concentrateStudyHours = concentrateStudyHours;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseStudyProgressHlEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(courseId);
        sb.append(", ").append(beginTime);
        sb.append(", ").append(type);
        sb.append(", ").append(isRequired);
        sb.append(", ").append(finishStatus);
        sb.append(", ").append(finishTime);
        sb.append(", ").append(studyTotalTime);
        sb.append(", ").append(registerTime);
        sb.append(", ").append(lastAccessTime);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(courseVersionId);
        sb.append(", ").append(markMemberId);
        sb.append(", ").append(markTime);
        sb.append(", ").append(completedRate);
        sb.append(", ").append(currentChapterId);
        sb.append(", ").append(currentSectionId);
        sb.append(", ").append(pushId);
        sb.append(", ").append(visits);
        sb.append(", ").append(lastModifyTime);
        sb.append(", ").append(modifyDate);
        sb.append(", ").append(subjectFinishTime);
        sb.append(", ").append(completionTimes);
        sb.append(", ").append(latestCompletionTime);
        sb.append(", ").append(studyHoursRecordTime);
        sb.append(", ").append(concentrateStudyHours);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseStudyProgressHl from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setBeginTime(from.getBeginTime());
        setType(from.getType());
        setIsRequired(from.getIsRequired());
        setFinishStatus(from.getFinishStatus());
        setFinishTime(from.getFinishTime());
        setStudyTotalTime(from.getStudyTotalTime());
        setRegisterTime(from.getRegisterTime());
        setLastAccessTime(from.getLastAccessTime());
        setCreateTime(from.getCreateTime());
        setCourseVersionId(from.getCourseVersionId());
        setMarkMemberId(from.getMarkMemberId());
        setMarkTime(from.getMarkTime());
        setCompletedRate(from.getCompletedRate());
        setCurrentChapterId(from.getCurrentChapterId());
        setCurrentSectionId(from.getCurrentSectionId());
        setPushId(from.getPushId());
        setVisits(from.getVisits());
        setLastModifyTime(from.getLastModifyTime());
        setModifyDate(from.getModifyDate());
        setSubjectFinishTime(from.getSubjectFinishTime());
        setCompletionTimes(from.getCompletionTimes());
        setLatestCompletionTime(from.getLatestCompletionTime());
        setStudyHoursRecordTime(from.getStudyHoursRecordTime());
        setConcentrateStudyHours(from.getConcentrateStudyHours());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseStudyProgressHl> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseStudyProgressHlEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseStudyProgressHlRecord r = new com.zxy.product.course.jooq.tables.records.CourseStudyProgressHlRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COURSE_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COURSE_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COURSE_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.BEGIN_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.BEGIN_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.BEGIN_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.TYPE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.TYPE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.IS_REQUIRED) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.IS_REQUIRED, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.IS_REQUIRED));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.FINISH_STATUS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.FINISH_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.FINISH_STATUS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.FINISH_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.FINISH_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.FINISH_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.STUDY_TOTAL_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.STUDY_TOTAL_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.STUDY_TOTAL_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.REGISTER_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.REGISTER_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.REGISTER_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.LAST_ACCESS_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.LAST_ACCESS_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.LAST_ACCESS_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CREATE_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COURSE_VERSION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COURSE_VERSION_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COURSE_VERSION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MARK_MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MARK_MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MARK_MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MARK_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MARK_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MARK_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COMPLETED_RATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COMPLETED_RATE, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COMPLETED_RATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CURRENT_CHAPTER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CURRENT_CHAPTER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CURRENT_CHAPTER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CURRENT_SECTION_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CURRENT_SECTION_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CURRENT_SECTION_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.PUSH_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.PUSH_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.PUSH_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.VISITS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.VISITS, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.VISITS));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.LAST_MODIFY_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.LAST_MODIFY_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.LAST_MODIFY_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MODIFY_DATE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.MODIFY_DATE));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.SUBJECT_FINISH_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.SUBJECT_FINISH_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.SUBJECT_FINISH_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COMPLETION_TIMES) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COMPLETION_TIMES, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.COMPLETION_TIMES));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.LATEST_COMPLETION_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.LATEST_COMPLETION_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.LATEST_COMPLETION_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.STUDY_HOURS_RECORD_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.STUDY_HOURS_RECORD_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.STUDY_HOURS_RECORD_TIME));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CONCENTRATE_STUDY_HOURS) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CONCENTRATE_STUDY_HOURS, record.getValue(com.zxy.product.course.jooq.tables.CourseStudyProgressHl.COURSE_STUDY_PROGRESS_HL.CONCENTRATE_STUDY_HOURS));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
