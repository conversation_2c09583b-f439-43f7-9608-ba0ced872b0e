/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.SubAuthenticatedCertificateRecord;
import com.zxy.product.course.jooq.tables.interfaces.ISubAuthenticatedCertificateRecord;
import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;

import javax.annotation.Generated;


/**
 * 子认证-学员-证书记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubAuthenticatedCertificateRecordRecord extends UpdatableRecordImpl<SubAuthenticatedCertificateRecordRecord> implements Record12<String, Long, String, String, String, String, Long, String, String, Integer, Long, Integer>, ISubAuthenticatedCertificateRecord {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_id</code>. id
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_id</code>. id
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_create_time</code>. 发证时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_create_time</code>. 发证时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(1);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_sub_authenticated_id</code>. 子认证id
     */
    @Override
    public void setSubAuthenticatedId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_sub_authenticated_id</code>. 子认证id
     */
    @Override
    public String getSubAuthenticatedId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_member_id</code>. 学员id
     */
    @Override
    public void setMemberId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_member_id</code>. 学员id
     */
    @Override
    public String getMemberId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_cancel_operator_id</code>. 删除操作人的id
     */
    @Override
    public void setCancelOperatorId(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_cancel_operator_id</code>. 删除操作人的id
     */
    @Override
    public String getCancelOperatorId() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_operator_id</code>. 发证人id
     */
    @Override
    public void setOperatorId(String value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_operator_id</code>. 发证人id
     */
    @Override
    public String getOperatorId() {
        return (String) get(5);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_cancel_time</code>. 删除时间
     */
    @Override
    public void setCancelTime(Long value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_cancel_time</code>. 删除时间
     */
    @Override
    public Long getCancelTime() {
        return (Long) get(6);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_attachement_id</code>. 证明材料id
     */
    @Override
    public void setAttachementId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_attachement_id</code>. 证明材料id
     */
    @Override
    public String getAttachementId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_reason</code>. 删除原因描述
     */
    @Override
    public void setReason(String value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_reason</code>. 删除原因描述
     */
    @Override
    public String getReason() {
        return (String) get(8);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_delete_flag</code>. 删除状态 0-未删除，1-已删除
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_delete_flag</code>. 删除状态 0-未删除，1-已删除
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_import_time</code>. 导入时间
     */
    @Override
    public void setImportTime(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_import_time</code>. 导入时间
     */
    @Override
    public Long getImportTime() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_status</code>. 发证状态 0 未发 1已发
     */
    @Override
    public void setStatus(Integer value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_status</code>. 发证状态 0 未发 1已发
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, Long, String, String, String, String, Long, String, String, Integer, Long, Integer> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row12<String, Long, String, String, String, String, Long, String, String, Integer, Long, Integer> valuesRow() {
        return (Row12) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field2() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.SUB_AUTHENTICATED_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_OPERATOR_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field6() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.OPERATOR_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field7() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.CANCEL_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.ATTACHEMENT_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field9() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.REASON;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field10() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.DELETE_FLAG;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field11() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.IMPORT_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field12() {
        return SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value2() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getSubAuthenticatedId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getCancelOperatorId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value6() {
        return getOperatorId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value7() {
        return getCancelTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getAttachementId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value9() {
        return getReason();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value10() {
        return getDeleteFlag();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value11() {
        return getImportTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value12() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value2(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value3(String value) {
        setSubAuthenticatedId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value4(String value) {
        setMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value5(String value) {
        setCancelOperatorId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value6(String value) {
        setOperatorId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value7(Long value) {
        setCancelTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value8(String value) {
        setAttachementId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value9(String value) {
        setReason(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value10(Integer value) {
        setDeleteFlag(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value11(Long value) {
        setImportTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord value12(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecordRecord values(String value1, Long value2, String value3, String value4, String value5, String value6, Long value7, String value8, String value9, Integer value10, Long value11, Integer value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ISubAuthenticatedCertificateRecord from) {
        setId(from.getId());
        setCreateTime(from.getCreateTime());
        setSubAuthenticatedId(from.getSubAuthenticatedId());
        setMemberId(from.getMemberId());
        setCancelOperatorId(from.getCancelOperatorId());
        setOperatorId(from.getOperatorId());
        setCancelTime(from.getCancelTime());
        setAttachementId(from.getAttachementId());
        setReason(from.getReason());
        setDeleteFlag(from.getDeleteFlag());
        setImportTime(from.getImportTime());
        setStatus(from.getStatus());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ISubAuthenticatedCertificateRecord> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached SubAuthenticatedCertificateRecordRecord
     */
    public SubAuthenticatedCertificateRecordRecord() {
        super(SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD);
    }

    /**
     * Create a detached, initialised SubAuthenticatedCertificateRecordRecord
     */
    public SubAuthenticatedCertificateRecordRecord(String id, Long createTime, String subAuthenticatedId, String memberId, String cancelOperatorId, String operatorId, Long cancelTime, String attachementId, String reason, Integer deleteFlag, Long importTime, Integer status) {
        super(SubAuthenticatedCertificateRecord.SUB_AUTHENTICATED_CERTIFICATE_RECORD);

        set(0, id);
        set(1, createTime);
        set(2, subAuthenticatedId);
        set(3, memberId);
        set(4, cancelOperatorId);
        set(5, operatorId);
        set(6, cancelTime);
        set(7, attachementId);
        set(8, reason);
        set(9, deleteFlag);
        set(10, importTime);
        set(11, status);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedCertificateRecordEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedCertificateRecordEntity pojo = (com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedCertificateRecordEntity)source;
        pojo.into(this);
        return true;
    }
}
