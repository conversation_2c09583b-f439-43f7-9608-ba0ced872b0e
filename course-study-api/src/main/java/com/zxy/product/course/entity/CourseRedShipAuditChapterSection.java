package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseRedShipAuditChapterSectionEntity;

/**
 * Created by TJ on 2022/10/28.
 */
public class CourseRedShipAuditChapterSection extends CourseRedShipAuditChapterSectionEntity {
    private static final long serialVersionUID = 6763914450568360332L;

    private String courseChapterName;
    private String courseChapterSectionName;

    /**
     * 1=字幕,0=课件
     */
    public final static int TYPE_CAPTION = 1;
    public final static int TYPE_COURSE = 0;


    public String getCourseChapterName() {
        return courseChapterName;
    }

    public void setCourseChapterName(String courseChapterName) {
        this.courseChapterName = courseChapterName;
    }

    public String getCourseChapterSectionName() {
        return courseChapterSectionName;
    }

    public void setCourseChapterSectionName(String courseChapterSectionName) {
        this.courseChapterSectionName = courseChapterSectionName;
    }
}
