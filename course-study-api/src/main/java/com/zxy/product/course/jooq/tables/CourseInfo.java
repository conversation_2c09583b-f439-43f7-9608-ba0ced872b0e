/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseInfoRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseInfo extends TableImpl<CourseInfoRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_info</code>
     */
    public static final CourseInfo COURSE_INFO = new CourseInfo();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseInfoRecord> getRecordType() {
        return CourseInfoRecord.class;
    }

    /**
     * The column <code>course-study.t_course_info.f_id</code>.
     */
    public final TableField<CourseInfoRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_info.f_organization_id</code>. 组织ID
     */
    public final TableField<CourseInfoRecord, String> ORGANIZATION_ID = createField("f_organization_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "组织ID");

    /**
     * The column <code>course-study.t_course_info.f_name</code>. 课程名称
     */
    public final TableField<CourseInfoRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程名称");

    /**
     * The column <code>course-study.t_course_info.f_cover</code>. 课程封面
     */
    public final TableField<CourseInfoRecord, String> COVER = createField("f_cover", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程封面");

    /**
     * The column <code>course-study.t_course_info.f_code</code>. 课程编码
     */
    public final TableField<CourseInfoRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程编码");

    /**
     * The column <code>course-study.t_course_info.f_price</code>. 定价(分)
     */
    public final TableField<CourseInfoRecord, Integer> PRICE = createField("f_price", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "定价(分)");

    /**
     * The column <code>course-study.t_course_info.f_integral</code>. 所需积分
     */
    public final TableField<CourseInfoRecord, Integer> INTEGRAL = createField("f_integral", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "所需积分");

    /**
     * The column <code>course-study.t_course_info.f_course_hour</code>. 课时
     */
    public final TableField<CourseInfoRecord, Integer> COURSE_HOUR = createField("f_course_hour", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "课时");

    /**
     * The column <code>course-study.t_course_info.f_course_time</code>.
     */
    public final TableField<CourseInfoRecord, Integer> COURSE_TIME = createField("f_course_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_info.f_course_second</code>. 课程时长秒
     */
    public final TableField<CourseInfoRecord, Integer> COURSE_SECOND = createField("f_course_second", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "课程时长秒");

    /**
     * The column <code>course-study.t_course_info.f_credit</code>. 学分
     */
    public final TableField<CourseInfoRecord, Integer> CREDIT = createField("f_credit", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学分");

    /**
     * The column <code>course-study.t_course_info.f_description</code>.
     */
    public final TableField<CourseInfoRecord, String> DESCRIPTION = createField("f_description", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>course-study.t_course_info.f_begin_date</code>. 开始日期
     */
    public final TableField<CourseInfoRecord, Long> BEGIN_DATE = createField("f_begin_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "开始日期");

    /**
     * The column <code>course-study.t_course_info.f_end_date</code>. 结束日期
     */
    public final TableField<CourseInfoRecord, Long> END_DATE = createField("f_end_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "结束日期");

    /**
     * The column <code>course-study.t_course_info.f_certificate_id</code>. 授予证书ID
     */
    public final TableField<CourseInfoRecord, String> CERTIFICATE_ID = createField("f_certificate_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "授予证书ID");

    /**
     * The column <code>course-study.t_course_info.f_certificate_type</code>. 0 默认 1自定义时间
     */
    public final TableField<CourseInfoRecord, Integer> CERTIFICATE_TYPE = createField("f_certificate_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "0 默认 1自定义时间");

    /**
     * The column <code>course-study.t_course_info.f_certificate_time</code>. 证书日期
     */
    public final TableField<CourseInfoRecord, Long> CERTIFICATE_TIME = createField("f_certificate_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "证书日期");

    /**
     * The column <code>course-study.t_course_info.f_certificate_update_time</code>. 更新证书时间
     */
    public final TableField<CourseInfoRecord, Long> CERTIFICATE_UPDATE_TIME = createField("f_certificate_update_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "更新证书时间");

    /**
     * The column <code>course-study.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    public final TableField<CourseInfoRecord, Integer> STATUS = createField("f_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)");

    /**
     * The column <code>course-study.t_course_info.f_portal_num</code>. 关联门户数
     */
    public final TableField<CourseInfoRecord, Integer> PORTAL_NUM = createField("f_portal_num", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "关联门户数");

    /**
     * The column <code>course-study.t_course_info.f_type</code>. 课件类型（0：混合类，1: 文档, 2: 图片，3: URL, 4: scrom, 5: 音频类, 6: 视频类, 7: 电子书, 8: 任务, 9: 考试，10：课程, 11: 面授, 12 调研, 13: 评估）
     */
    public final TableField<CourseInfoRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "课件类型（0：混合类，1: 文档, 2: 图片，3: URL, 4: scrom, 5: 音频类, 6: 视频类, 7: 电子书, 8: 任务, 9: 考试，10：课程, 11: 面授, 12 调研, 13: 评估）");

    /**
     * The column <code>course-study.t_course_info.f_source</code>. 课程来源(0：内部录制，1：外部引进，2：共建共享；3：自主研发；4：其他；5：工作室)
     */
    public final TableField<CourseInfoRecord, Integer> SOURCE = createField("f_source", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "课程来源(0：内部录制，1：外部引进，2：共建共享；3：自主研发；4：其他；5：工作室)");

    /**
     * The column <code>course-study.t_course_info.f_release_time</code>. 发布时间
     */
    public final TableField<CourseInfoRecord, Long> RELEASE_TIME = createField("f_release_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "发布时间");

    /**
     * The column <code>course-study.t_course_info.f_release_member_id</code>. 发布人
     */
    public final TableField<CourseInfoRecord, String> RELEASE_MEMBER_ID = createField("f_release_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发布人");

    /**
     * The column <code>course-study.t_course_info.f_release_org_id</code>. 发布部门
     */
    public final TableField<CourseInfoRecord, String> RELEASE_ORG_ID = createField("f_release_org_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发布部门");

    /**
     * The column <code>course-study.t_course_info.f_develop_member_id</code>. 开发人
     */
    public final TableField<CourseInfoRecord, String> DEVELOP_MEMBER_ID = createField("f_develop_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "开发人");

    /**
     * The column <code>course-study.t_course_info.f_develop_time</code>. 开发时间
     */
    public final TableField<CourseInfoRecord, Long> DEVELOP_TIME = createField("f_develop_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "开发时间");

    /**
     * The column <code>course-study.t_course_info.f_create_member_id</code>. 创建人
     */
    public final TableField<CourseInfoRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>course-study.t_course_info.f_create_time</code>. 创建时间
     */
    public final TableField<CourseInfoRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_course_info.f_category_id</code>. 目录类别
     */
    public final TableField<CourseInfoRecord, String> CATEGORY_ID = createField("f_category_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "目录类别");

    /**
     * The column <code>course-study.t_course_info.f_add_type</code>. 1 普通模式
2 章节模式
     */
    public final TableField<CourseInfoRecord, Integer> ADD_TYPE = createField("f_add_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "1 普通模式\n2 章节模式");

    /**
     * The column <code>course-study.t_course_info.f_version_id</code>. 当前最新版本
     */
    public final TableField<CourseInfoRecord, String> VERSION_ID = createField("f_version_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "当前最新版本");

    /**
     * The column <code>course-study.t_course_info.f_learn_sequence</code>. 按顺序完成 0 否 1 按章节顺序
     */
    public final TableField<CourseInfoRecord, Integer> LEARN_SEQUENCE = createField("f_learn_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "按顺序完成 0 否 1 按章节顺序");

    /**
     * The column <code>course-study.t_course_info.f_is_subject</code>. 是否为专题 0 - 否 1 - 是
     */
    public final TableField<CourseInfoRecord, Integer> IS_SUBJECT = createField("f_is_subject", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否为专题 0 - 否 1 - 是");

    /**
     * The column <code>course-study.t_course_info.f_study_days</code>. 建议学习天数
     */
    public final TableField<CourseInfoRecord, Integer> STUDY_DAYS = createField("f_study_days", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "建议学习天数");

    /**
     * The column <code>course-study.t_course_info.f_study_member_count</code>. 学习总人数
     */
    public final TableField<CourseInfoRecord, Integer> STUDY_MEMBER_COUNT = createField("f_study_member_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习总人数");

    /**
     * The column <code>course-study.t_course_info.f_register_member_count</code>. 注册总人数
     */
    public final TableField<CourseInfoRecord, Integer> REGISTER_MEMBER_COUNT = createField("f_register_member_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "注册总人数");

    /**
     * The column <code>course-study.t_course_info.f_add_plan_member_count</code>. 学习计划添加人数
     */
    public final TableField<CourseInfoRecord, Integer> ADD_PLAN_MEMBER_COUNT = createField("f_add_plan_member_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "学习计划添加人数");

    /**
     * The column <code>course-study.t_course_info.f_styles</code>. 风格配置(专题)
     */
    public final TableField<CourseInfoRecord, String> STYLES = createField("f_styles", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "风格配置(专题)");

    /**
     * The column <code>course-study.t_course_info.f_total_score</code>. 课程总得分(各学员的评分之和)
     */
    public final TableField<CourseInfoRecord, Integer> TOTAL_SCORE = createField("f_total_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "课程总得分(各学员的评分之和)");

    /**
     * The column <code>course-study.t_course_info.f_score_member_count</code>. 评分人数
     */
    public final TableField<CourseInfoRecord, Integer> SCORE_MEMBER_COUNT = createField("f_score_member_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "评分人数");

    /**
     * The column <code>course-study.t_course_info.f_avg_score</code>. 平均评分
     */
    public final TableField<CourseInfoRecord, Integer> AVG_SCORE = createField("f_avg_score", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "平均评分");

    /**
     * The column <code>course-study.t_course_info.f_shelve_time</code>. 首次上架时间
     */
    public final TableField<CourseInfoRecord, Long> SHELVE_TIME = createField("f_shelve_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "首次上架时间");

    /**
     * The column <code>course-study.t_course_info.f_off_time</code>. 下架时间,每次下架更新该时间
     */
    public final TableField<CourseInfoRecord, Long> OFF_TIME = createField("f_off_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "下架时间,每次下架更新该时间");

    /**
     * The column <code>course-study.t_course_info.f_is_public</code>. 是否公开：1受限，2公开
     */
    public final TableField<CourseInfoRecord, Integer> IS_PUBLIC = createField("f_is_public", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否公开：1受限，2公开");

    /**
     * The column <code>course-study.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    public final TableField<CourseInfoRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "删除标识 0 未删除， 1 已删除");

    /**
     * The column <code>course-study.t_course_info.f_description_text</code>.
     */
    public final TableField<CourseInfoRecord, String> DESCRIPTION_TEXT = createField("f_description_text", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "");

    /**
     * The column <code>course-study.t_course_info.f_publish_client</code>. 发布终端   0: 全部, 1: PC, 2: APP
     */
    public final TableField<CourseInfoRecord, Integer> PUBLISH_CLIENT = createField("f_publish_client", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "发布终端   0: 全部, 1: PC, 2: APP");

    /**
     * The column <code>course-study.t_course_info.f_publish_type</code>. 发布类型  0-定向(测试) 1-正式
     */
    public final TableField<CourseInfoRecord, Integer> PUBLISH_TYPE = createField("f_publish_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "发布类型  0-定向(测试) 1-正式");

    /**
     * The column <code>course-study.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    public final TableField<CourseInfoRecord, Integer> BUSINESS_TYPE = createField("f_business_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "业务类型，0-课程；1-学习路径；2-专题");

    /**
     * The column <code>course-study.t_course_info.f_lecturer</code>. 讲师
     */
    public final TableField<CourseInfoRecord, String> LECTURER = createField("f_lecturer", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "讲师");

    /**
     * The column <code>course-study.t_course_info.f_sequence</code>. 序列
     */
    public final TableField<CourseInfoRecord, Integer> SEQUENCE = createField("f_sequence", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "序列");

    /**
     * The column <code>course-study.t_course_info.f_source_detail</code>. 来源明细
     */
    public final TableField<CourseInfoRecord, String> SOURCE_DETAIL = createField("f_source_detail", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "来源明细");

    /**
     * The column <code>course-study.t_course_info.f_visits</code>. 浏览次数
     */
    public final TableField<CourseInfoRecord, Integer> VISITS = createField("f_visits", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "浏览次数");

    /**
     * The column <code>course-study.t_course_info.f_share_sub</code>. 分享给下级部门，0 不分享 1 分享
     */
    public final TableField<CourseInfoRecord, Integer> SHARE_SUB = createField("f_share_sub", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "分享给下级部门，0 不分享 1 分享");

    /**
     * The column <code>course-study.t_course_info.f_url</code>. 专题链接，个性化专题必填
     */
    public final TableField<CourseInfoRecord, String> URL = createField("f_url", org.jooq.impl.SQLDataType.VARCHAR.length(3000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "专题链接，个性化专题必填");

    /**
     * The column <code>course-study.t_course_info.f_cover_path</code>. 课程封面
     */
    public final TableField<CourseInfoRecord, String> COVER_PATH = createField("f_cover_path", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程封面");

    /**
     * The column <code>course-study.t_course_info.f_cover_material_id</code>. 课程封面素材id,用于优先获取关联的专题banner素材id
     */
    public final TableField<CourseInfoRecord, String> COVER_MATERIAL_ID = createField("f_cover_material_id", org.jooq.impl.SQLDataType.VARCHAR.length(400).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程封面素材id,用于优先获取关联的专题banner素材id");

    /**
     * The column <code>course-study.t_course_info.f_collection_count</code>. 收藏次数
     */
    public final TableField<CourseInfoRecord, Integer> COLLECTION_COUNT = createField("f_collection_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "收藏次数");

    /**
     * The column <code>course-study.t_course_info.f_share_count</code>. 分享次数
     */
    public final TableField<CourseInfoRecord, Integer> SHARE_COUNT = createField("f_share_count", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "分享次数");

    /**
     * The column <code>course-study.t_course_info.f_audience_org</code>. 课程发布的最大受众部门名字，待删除
     */
    public final TableField<CourseInfoRecord, String> AUDIENCE_ORG = createField("f_audience_org", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程发布的最大受众部门名字，待删除");

    /**
     * The column <code>course-study.t_course_info.f_cloud_status</code>. 0 已上架, 1 待同步，2 已下架
     */
    public final TableField<CourseInfoRecord, Integer> CLOUD_STATUS = createField("f_cloud_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "0 已上架, 1 待同步，2 已下架");

    /**
     * The column <code>course-study.t_course_info.f_cloud_time</code>. 云课程同步时间 （判断操作冲突用。）
     */
    public final TableField<CourseInfoRecord, Long> CLOUD_TIME = createField("f_cloud_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "云课程同步时间 （判断操作冲突用。）");

    /**
     * The column <code>course-study.t_course_info.f_cloud_rule</code>. 云课程发布规则，0 不影响，1 影响学习 2 全部影响
     */
    public final TableField<CourseInfoRecord, Integer> CLOUD_RULE = createField("f_cloud_rule", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "云课程发布规则，0 不影响，1 影响学习 2 全部影响");

    /**
     * The column <code>course-study.t_course_info.f_audiences</code>. 课程的所有受众项，逗号分隔
     */
    public final TableField<CourseInfoRecord, String> AUDIENCES = createField("f_audiences", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "课程的所有受众项，逗号分隔");

    /**
     * The column <code>course-study.t_course_info.f_description_app</code>. 课程简介--app加粗换行样式
     */
    public final TableField<CourseInfoRecord, String> DESCRIPTION_APP = createField("f_description_app", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "课程简介--app加粗换行样式");

    /**
     * The column <code>course-study.t_course_info.f_relative_gensee_id</code>. 课程关联直播id
     */
    public final TableField<CourseInfoRecord, String> RELATIVE_GENSEE_ID = createField("f_relative_gensee_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程关联直播id");

    /**
     * The column <code>course-study.t_course_info.f_is_sign</code>. 是否需要报名（0.否；1.是，默认不需要报名）
     */
    public final TableField<CourseInfoRecord, Integer> IS_SIGN = createField("f_is_sign", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否需要报名（0.否；1.是，默认不需要报名）");

    /**
     * The column <code>course-study.t_course_info.f_is_party</code>. 是否为党政课程或专题，0.否，1是
     */
    public final TableField<CourseInfoRecord, Integer> IS_PARTY = createField("f_is_party", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否为党政课程或专题，0.否，1是");

    /**
     * The column <code>course-study.t_course_info.f_use_video_speed</code>. 是否开启视频倍速播放
     */
    public final TableField<CourseInfoRecord, Integer> USE_VIDEO_SPEED = createField("f_use_video_speed", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "是否开启视频倍速播放");

    /**
     * The column <code>course-study.t_course_info.f_switch_hide</code>. 讨论区开关（0：关，1：开）
     */
    public final TableField<CourseInfoRecord, Integer> SWITCH_HIDE = createField("f_switch_hide", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "讨论区开关（0：关，1：开）");

    /**
     * The column <code>course-study.t_course_info.f_history_hide</code>. 历史讨论记录开关（0：关，1：开）
     */
    public final TableField<CourseInfoRecord, Integer> HISTORY_HIDE = createField("f_history_hide", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("1", org.jooq.impl.SQLDataType.INTEGER)), this, "历史讨论记录开关（0：关，1：开）");

    /**
     * The column <code>course-study.t_course_info.f_caption_flag</code>. 是否生成字幕（0：关，1开）
     */
    public final TableField<CourseInfoRecord, Integer> CAPTION_FLAG = createField("f_caption_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "是否生成字幕（0：关，1开）");

    /**
     * The column <code>course-study.t_course_info.f_modify_date</code>. 修改时间
     */
    public final TableField<CourseInfoRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>course-study.t_course_info.f_caption_overall_status</code>. 字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)
     */
    public final TableField<CourseInfoRecord, Integer> CAPTION_OVERALL_STATUS = createField("f_caption_overall_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)");

    /**
     * The column <code>course-study.t_course_info.f_switch_mentor</code>. 数智导师开关
     */
    public final TableField<CourseInfoRecord, Integer> SWITCH_MENTOR = createField("f_switch_mentor", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "数智导师开关");

    /**
     * The column <code>course-study.t_course_info.f_caption_follow_release</code>. 字幕跟随课程自动发布 0=不跟随,1=跟随
     */
    public final TableField<CourseInfoRecord, Integer> CAPTION_FOLLOW_RELEASE = createField("f_caption_follow_release", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "字幕跟随课程自动发布 0=不跟随,1=跟随");

    /**
     * The column <code>course-study.t_course_info.f_explicit_learning_status</code>. 学习状态显性化
     */
    public final TableField<CourseInfoRecord, Integer> EXPLICIT_LEARNING_STATUS = createField("f_explicit_learning_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习状态显性化");

    /**
     * The column <code>course-study.t_course_info.f_learning_situation</code>. 开启学情分析报告(0: 关、1:开)
     */
    public final TableField<CourseInfoRecord, Integer> LEARNING_SITUATION = createField("f_learning_situation", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "开启学情分析报告(0: 关、1:开)");

    /**
     * The column <code>course-study.t_course_info.f_sponsoring_org</code>. 主办部门
     */
    public final TableField<CourseInfoRecord, String> SPONSORING_ORG = createField("f_sponsoring_org", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "主办部门");

    /**
     * The column <code>course-study.t_course_info.f_pc_banner</code>. pc banner图片
     */
    public final TableField<CourseInfoRecord, String> PC_BANNER = createField("f_pc_banner", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "pc banner图片");

    /**
     * The column <code>course-study.t_course_info.f_pc_banner_path</code>. pc banner图片路径
     */
    public final TableField<CourseInfoRecord, String> PC_BANNER_PATH = createField("f_pc_banner_path", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "pc banner图片路径");

    /**
     * The column <code>course-study.t_course_info.f_app_banner</code>. app banner图片
     */
    public final TableField<CourseInfoRecord, String> APP_BANNER = createField("f_app_banner", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "app banner图片");

    /**
     * The column <code>course-study.t_course_info.f_app_banner_path</code>. app banner图片路径
     */
    public final TableField<CourseInfoRecord, String> APP_BANNER_PATH = createField("f_app_banner_path", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "app banner图片路径");

    /**
     * The column <code>course-study.t_course_info.f_mentor_state</code>. 课件摘要状态 0无需生成 1未生成 2待审核  3未审核通过  4已审核通过  5开启数智导师  6开启无摘要数智导师
     */
    public final TableField<CourseInfoRecord, Integer> MENTOR_STATE = createField("f_mentor_state", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "课件摘要状态 0无需生成 1未生成 2待审核  3未审核通过  4已审核通过  5开启数智导师  6开启无摘要数智导师");

    /**
     * The column <code>course-study.t_course_info.f_open</code>. 课程是否全员受众, 0=非全员、1=全员、2=内部人员
     */
    public final TableField<CourseInfoRecord, Integer> OPEN = createField("f_open", org.jooq.impl.SQLDataType.INTEGER.nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "课程是否全员受众, 0=非全员、1=全员、2=内部人员");

    /**
     * The column <code>course-study.t_course_info.f_update_date</code>. 修改时间，仅限后台管理修改使用
     */
    public final TableField<CourseInfoRecord, Long> UPDATE_DATE = createField("f_update_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "修改时间，仅限后台管理修改使用");

    /**
     * The column <code>course-study.t_course_info.f_voice_to_text</code>. 课程中智能笔记使用语音转文字数量
     */
    public final TableField<CourseInfoRecord, Integer> VOICE_TO_TEXT = createField("f_voice_to_text", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "课程中智能笔记使用语音转文字数量");

    /**
     * The column <code>course-study.t_course_info.f_new_launch_visit_num</code>. 通过 最新上线 模块学习的人次
     */
    public final TableField<CourseInfoRecord, Integer> NEW_LAUNCH_VISIT_NUM = createField("f_new_launch_visit_num", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "通过 最新上线 模块学习的人次");

    /**
     * The column <code>course-study.t_course_info.f_recommend_visit_num</code>. 通过 为你推荐 模块学习的人次
     */
    public final TableField<CourseInfoRecord, Integer> RECOMMEND_VISIT_NUM = createField("f_recommend_visit_num", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "通过 为你推荐 模块学习的人次");

    /**
     * The column <code>course-study.t_course_info.f_construction_type</code>. 全员共建共享，0=不是，1=是
     */
    public final TableField<CourseInfoRecord, Integer> CONSTRUCTION_TYPE = createField("f_construction_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "全员共建共享，0=不是，1=是");

    /**
     * The column <code>course-study.t_course_info.f_skin_type</code>. 皮肤类型：0：科技蓝，1：党建红，2：活力橙
     */
    public final TableField<CourseInfoRecord, Integer> SKIN_TYPE = createField("f_skin_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").nullable(false).defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "皮肤类型：0：科技蓝，1：党建红，2：活力橙");

    /**
     * The column <code>course-study.t_course_info.f_subject_type</code>. 专题类型 1: 集团公司统一组织 2: 省专公司组织 3: 网络自学
     */
    public final TableField<CourseInfoRecord, Integer> SUBJECT_TYPE = createField("f_subject_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "专题类型 1: 集团公司统一组织 2: 省专公司组织 3: 网络自学");

    /**
     * The column <code>course-study.t_course_info.f_subject_mis_code</code>. 网络专题MIS编号
     */
    public final TableField<CourseInfoRecord, String> SUBJECT_MIS_CODE = createField("f_subject_mis_code", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "网络专题MIS编号");

    /**
     * Create a <code>course-study.t_course_info</code> table reference
     */
    public CourseInfo() {
        this("t_course_info", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_info</code> table reference
     */
    public CourseInfo(String alias) {
        this(alias, COURSE_INFO);
    }

    private CourseInfo(String alias, Table<CourseInfoRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseInfo(String alias, Table<CourseInfoRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseInfoRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_INFO_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseInfoRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseInfoRecord>>asList(Keys.KEY_T_COURSE_INFO_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseInfo as(String alias) {
        return new CourseInfo(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseInfo rename(String name) {
        return new CourseInfo(name, null);
    }
}
