/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.SubAuthenticatedTmpRecord;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;


/**
 * 子认证-维度导入表-临时表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubAuthenticatedTmp extends TableImpl<SubAuthenticatedTmpRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_sub_authenticated_tmp</code>
     */
    public static final SubAuthenticatedTmp SUB_AUTHENTICATED_TMP = new SubAuthenticatedTmp();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SubAuthenticatedTmpRecord> getRecordType() {
        return SubAuthenticatedTmpRecord.class;
    }

    /**
     * The column <code>course-study.t_sub_authenticated_tmp.f_id</code>. id
     */
    public final TableField<SubAuthenticatedTmpRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "id");

    /**
     * The column <code>course-study.t_sub_authenticated_tmp.f_name</code>. 姓名
     */
    public final TableField<SubAuthenticatedTmpRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "姓名");

    /**
     * The column <code>course-study.t_sub_authenticated_tmp.f_code</code>. 编号
     */
    public final TableField<SubAuthenticatedTmpRecord, String> CODE = createField("f_code", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "编号");

    /**
     * The column <code>course-study.t_sub_authenticated_tmp.f_file_id</code>. 文件id
     */
    public final TableField<SubAuthenticatedTmpRecord, String> FILE_ID = createField("f_file_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "文件id");

    /**
     * The column <code>course-study.t_sub_authenticated_tmp.f_sub_authenticated_id</code>. 子认证id
     */
    public final TableField<SubAuthenticatedTmpRecord, String> SUB_AUTHENTICATED_ID = createField("f_sub_authenticated_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "子认证id");

    /**
     * The column <code>course-study.t_sub_authenticated_tmp.f_delete_flag</code>. 是否删除 0 否 1是
     */
    public final TableField<SubAuthenticatedTmpRecord, Integer> DELETE_FLAG = createField("f_delete_flag", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否删除 0 否 1是");

    /**
     * The column <code>course-study.t_sub_authenticated_tmp.f_creator</code>. 创建人
     */
    public final TableField<SubAuthenticatedTmpRecord, String> CREATOR = createField("f_creator", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>course-study.t_sub_authenticated_tmp.f_create_time</code>. 创建时间
     */
    public final TableField<SubAuthenticatedTmpRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_sub_authenticated_tmp.f_modify_date</code>. 修改时间
     */
    public final TableField<SubAuthenticatedTmpRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * Create a <code>course-study.t_sub_authenticated_tmp</code> table reference
     */
    public SubAuthenticatedTmp() {
        this("t_sub_authenticated_tmp", null);
    }

    /**
     * Create an aliased <code>course-study.t_sub_authenticated_tmp</code> table reference
     */
    public SubAuthenticatedTmp(String alias) {
        this(alias, SUB_AUTHENTICATED_TMP);
    }

    private SubAuthenticatedTmp(String alias, Table<SubAuthenticatedTmpRecord> aliased) {
        this(alias, aliased, null);
    }

    private SubAuthenticatedTmp(String alias, Table<SubAuthenticatedTmpRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "子认证-维度导入表-临时表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SubAuthenticatedTmpRecord> getPrimaryKey() {
        return Keys.KEY_T_SUB_AUTHENTICATED_TMP_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SubAuthenticatedTmpRecord>> getKeys() {
        return Arrays.<UniqueKey<SubAuthenticatedTmpRecord>>asList(Keys.KEY_T_SUB_AUTHENTICATED_TMP_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedTmp as(String alias) {
        return new SubAuthenticatedTmp(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SubAuthenticatedTmp rename(String name) {
        return new SubAuthenticatedTmp(name, null);
    }
}
