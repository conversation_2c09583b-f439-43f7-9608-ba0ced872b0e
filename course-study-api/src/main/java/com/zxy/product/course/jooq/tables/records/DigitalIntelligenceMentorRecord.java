/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor;
import com.zxy.product.course.jooq.tables.interfaces.IDigitalIntelligenceMentor;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 数智导师结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DigitalIntelligenceMentorRecord extends UpdatableRecordImpl<DigitalIntelligenceMentorRecord> implements Record5<String, String, String, String, Long>, IDigitalIntelligenceMentor {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_id</code>. 主键ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_id</code>. 主键ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_member_id</code>. 用户ID，关联t_member表
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_member_id</code>. 用户ID，关联t_member表
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_user_query</code>. 用户问题
     */
    @Override
    public void setUserQuery(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_user_query</code>. 用户问题
     */
    @Override
    public String getUserQuery() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_bot_response</code>. 大模型响应
     */
    @Override
    public void setBotResponse(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_bot_response</code>. 大模型响应
     */
    @Override
    public String getBotResponse() {
        return (String) get(3);
    }



    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row5<String, String, String, String, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    @Override
    public Row5<String, String, String, String, Long> valuesRow() {
        return (Row5) super.valuesRow();
    }

    @Override
    public Field<String> field1() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.ID;
    }

    @Override
    public Field<String> field2() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID;
    }

    @Override
    public Field<String> field3() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.USER_QUERY;
    }

    @Override
    public Field<String> field4() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.BOT_RESPONSE;
    }

    @Override
    public Field<Long> field5() {
        return DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.CREATE_TIME;
    }

    @Override
    public String value1() {
        return getId();
    }

    @Override
    public String value2() {
        return getMemberId();
    }

    @Override
    public String value3() {
        return getUserQuery();
    }

    @Override
    public String value4() {
        return getBotResponse();
    }

    @Override
    public Long value5() {
        return getCreateTime();
    }

    @Override
    public DigitalIntelligenceMentorRecord value1(String value) {
        setId(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord value2(String value) {
        setMemberId(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord value3(String value) {
        setUserQuery(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord value4(String value) {
        setBotResponse(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord value5(Long value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public DigitalIntelligenceMentorRecord values(String value1, String value2, String value3, String value4, Long value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IDigitalIntelligenceMentor from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setUserQuery(from.getUserQuery());
        setBotResponse(from.getBotResponse());
        setCreateTime(from.getCreateTime());
    }

    @Override
    public <E extends IDigitalIntelligenceMentor> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached DigitalIntelligenceMentorRecord
     */
    public DigitalIntelligenceMentorRecord() {
        super(DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR);
    }

    /**
     * Create a detached, initialised DigitalIntelligenceMentorRecord
     */
    public DigitalIntelligenceMentorRecord(String id, String memberId, String userQuery, String botResponse, Long createTime) {
        super(DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR);

        set(0, id);
        set(1, memberId);
        set(2, userQuery);
        set(3, botResponse);
        set(4, createTime);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.DigitalIntelligenceMentorEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.DigitalIntelligenceMentorEntity pojo = (com.zxy.product.course.jooq.tables.pojos.DigitalIntelligenceMentorEntity)source;
        pojo.into(this);
        return true;
    }
}
