/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 学习助手知识表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseKnowledge extends Serializable {

    /**
     * Setter for <code>course-study.t_course_knowledge.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_name</code>. 知识名称
     */
    public void setName(String value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_name</code>. 知识名称
     */
    public String getName();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_id</code>. 附件id
     */
    public void setAttachmentId(String value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_id</code>. 附件id
     */
    public String getAttachmentId();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_name</code>. 附件名称
     */
    public void setAttachmentName(String value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_name</code>. 附件名称
     */
    public String getAttachmentName();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_type</code>. 0 word 1 pdf
     */
    public void setAttachmentType(Integer value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_type</code>. 0 word 1 pdf
     */
    public Integer getAttachmentType();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_up_id</code>. 上传到成研的附件id
     */
    public void setAttachmentUpId(String value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_up_id</code>. 上传到成研的附件id
     */
    public String getAttachmentUpId();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_attachment_batch</code>. 上传到成研的附件批次号
     */
    public void setAttachmentBatch(String value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_attachment_batch</code>. 上传到成研的附件批次号
     */
    public String getAttachmentBatch();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_status</code>. 状态 0 未发布 1 已发布
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_status</code>. 状态 0 未发布 1 已发布
     */
    public Integer getStatus();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_delete</code>. 是否删除 0 未删除 1 已删除
     */
    public void setDelete(Integer value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_delete</code>. 是否删除 0 未删除 1 已删除
     */
    public Integer getDelete();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_finsh_status</code>. 向量化状态 0 未完成 1已完成
     */
    public void setFinshStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_finsh_status</code>. 向量化状态 0 未完成 1已完成
     */
    public Integer getFinshStatus();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_knowledge</code>. 知识简介
     */
    public void setKnowledge(String value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_knowledge</code>. 知识简介
     */
    public String getKnowledge();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_knowledge_text</code>. 知识简介文本
     */
    public void setKnowledgeText(String value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_knowledge_text</code>. 知识简介文本
     */
    public String getKnowledgeText();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_shelve_time</code>. 首次发布时间
     */
    public void setShelveTime(Long value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_shelve_time</code>. 首次发布时间
     */
    public Long getShelveTime();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_knowledge.f_modify_date</code>. 更新时间
     */
    public void setModifyDate(Long value);

    /**
     * Getter for <code>course-study.t_course_knowledge.f_modify_date</code>. 更新时间
     */
    public Long getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseKnowledge
     */
    public void from(ICourseKnowledge from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseKnowledge
     */
    public <E extends ICourseKnowledge> E into(E into);
}
