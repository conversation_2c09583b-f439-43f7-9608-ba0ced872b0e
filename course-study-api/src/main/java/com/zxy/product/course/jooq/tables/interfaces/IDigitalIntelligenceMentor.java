/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 数智导师结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IDigitalIntelligenceMentor extends Serializable {

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_id</code>. 主键ID
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_id</code>. 主键ID
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_member_id</code>. 用户ID，关联t_member表
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_member_id</code>. 用户ID，关联t_member表
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_user_query</code>. 用户问题
     */
    public void setUserQuery(String value);

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_user_query</code>. 用户问题
     */
    public String getUserQuery();

    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_bot_response</code>. 大模型响应
     */
    public void setBotResponse(String value);

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_bot_response</code>. 大模型响应
     */
    public String getBotResponse();



    /**
     * Setter for <code>course-study.t_digital_intelligence_mentor.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_digital_intelligence_mentor.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IDigitalIntelligenceMentor
     */
    public void from(IDigitalIntelligenceMentor from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IDigitalIntelligenceMentor
     */
    public <E extends IDigitalIntelligenceMentor> E into(E into);
}
