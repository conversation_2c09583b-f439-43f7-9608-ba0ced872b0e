package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseChapterSectionEntity;

import java.util.List;

/**
 * Created by keeley on 16/10/14.
 * 课程节资源(课件)
 */
public class CourseChapterSection extends CourseChapterSectionEntity {

    public static final Integer SECTION_TYPE_COACH_TESTS = 18;
    private static final long serialVersionUID = -5279446404032286379L;

    /**
     * 必修
     */
    public static final Integer IS_REQUIRED = 1;
    public static final Integer IS_NOT_REQUIRED = 0;

    /**
     * 专题节类型： 课程
     */
    public static final int SECTION_TYPE_COURSE = 10;
    /**
     * 专题节类型： 能力
     */
    public static final int SECTION_TYPE_ABILITY = 19;

    /**
     * 节类型 - 任务
     */
    public static final int SECTION_TYPE_TASK = 8; //任务
    public static final int SECTION_TYPE_EXAM = 9; //考试
    public static final int SECTION_TYPE_RESEARCH = 12;//调研
    public static final int SECTION_TYPE_EVALUATION = 13;//评估
    public static final int SECTION_TYPE_GENSEE = 14;//直播

    /**
     * 专题节类型： 专题
     */
    public static final Integer SECTION_TYPE_SUBJECT = 16;

    /**
     * 专题节类型： 知识
     */
    public static final Integer SECTION_TYPE_KNOWLEDGE = 15;


    /**
     * 节类型 - 文档
     */
    public static final int SECTION_TYPE_DOC = 1;
    /**
     * 节类型 - 图片
     */
    public static final int SECTION_TYPE_PIC = 2;
    public static final int SECTION_TYPE_URL = 3;
    public static final int SECTION_TYPE_SCROM = 4;
    public static final int SECTION_TYPE_VIDEO = 6;
    public static final int SECTION_TYPE_AUDIO = 5;

    /**  调研评估来源：0选择  */
    public static final Integer RESEARCH_SOURCE_TYPE_SELECT = 0;
    /**  调研评估来源：1自己新增 */
    public static final Integer RESEARCH_SOURCE_TYPE_ADD = 1;

    // 课件是否为更新课件1，更新课件，0，非更新课件
    public static final Integer IS_MODIFY_FILE = 1;
    public static final Integer IS_NOT_MODIFY_FILE = 0;

    // 是否生成满意度问卷 0-不生成 1-生成 默认0
    public static final Integer QUESTIONNAIRE_FLAG_NO = 0;
    public static final Integer QUESTIONNAIRE_FLAG_YES = 1;

    // 调查问卷按钮隐藏、显示、置灰  0-隐藏 1-置灰 2-正常显示
    public static final Integer QUESTIONNAIRE_BUTTON_FLAG_HIDE = 0;
    public static final Integer QUESTIONNAIRE_BUTTON_FLAG_GREY = 1;
    public static final Integer QUESTIONNAIRE_BUTTON_FLAG_DISPLAY = 2;

    /**
     * 是否为重塑外部平台章节
     * 1.是，0.否
     */
    public static final int IS_EXTERNAL_CHONGSU_YES = 1;
    public static final int IS_EXTERNAL_CHONGSU_NO = 0;

    /**
     * 用于区分重塑我的培训页的跳转标识
     * 1.直接根据回传的url跳转
     * 2.外部平台，需要根据重塑单点接口将url组装后再跳转
     * 3.直接根据回传的courseId，跳转专区页面
     * 4.内部引用课程，需要根据resourceId拼接课程跳转地址
     */
    public static final int JUMP_WAY_FLAG_INTERNAL_COURSE = 1;
    public static final int JUMP_WAY_FLAG_EXTERNAL_COURSE = 2;
    public static final int JUMP_WAY_FLAG_SUBJECT = 3;
    public static final int JUMP_WAY_FLAG_REFERENCE_COURSE = 4;

    private StudyTask studyTask;
    private Integer studyPersonNum; // 学习、参加人数
    private CourseSectionStudyProgress progress; // 学习进度

    private Integer ExamRegistTotalScore; // 考试注册表的分数（考试记录的最高分）

    private String ExamRegistTotalExamRecordId; // 考试注册表的考试记录id（考试记录的最高分id）

    private String ExamRegistId; // 考试注册表的id

    /**
     * 专题url是否为重塑专区的url,0不是，1是
     */
    private Integer isExternalChongSu = 0;

    //发布终端   0: 全部, 1: PC, 2: APP
    private Integer publishClient;

    //课程总时间
    private String allCourseTime;

    /**
     * 用于区分重塑专区我的培训页的章节跳转方式
     */
    private int flag;

    private CourseChapter courseChapter;
    private CourseInfo courseInfo;

    private MultidimensionalStudentScoreSheet multidimensionalStudentScoreSheet;


    private String chapterName;

    //播报id
    private String attachmentAudioId;

    //是否可以播报
    private Integer intelligentBroadcastType;

    //播放位置
    private String lessonLocation;

    //时常
    private Integer audioTime;

    private Integer noteNumber;
    private String intelligentBroadcastId;
    private Integer intelligentBroadcastStatus;

    private String intelligentBroadcastTxtId;
    private List<CourseChapterSection> courseChapterSections;

    private Integer constructionType;

    public Integer getConstructionType() {
        return constructionType;
    }

    public void setConstructionType(Integer constructionType) {
        this.constructionType = constructionType;
    }

    public String getIntelligentBroadcastTxtId() {
        return intelligentBroadcastTxtId;
    }

    public void setIntelligentBroadcastTxtId(String intelligentBroadcastTxtId) {
        this.intelligentBroadcastTxtId = intelligentBroadcastTxtId;
    }

    public Integer getIntelligentBroadcastStatus() {
        return intelligentBroadcastStatus;
    }

    public void setIntelligentBroadcastStatus(Integer intelligentBroadcastStatus) {
        this.intelligentBroadcastStatus = intelligentBroadcastStatus;
    }

    public String getIntelligentBroadcastId() {
        return intelligentBroadcastId;
    }

    public void setIntelligentBroadcastId(String intelligentBroadcastId) {
        this.intelligentBroadcastId = intelligentBroadcastId;
    }

    public String getLessonLocation() {
        return lessonLocation;
    }

    public void setLessonLocation(String lessonLocation) {
        this.lessonLocation = lessonLocation;
    }

    public Integer getAudioTime() {
        return audioTime;
    }

    public void setAudioTime(Integer audioTime) {
        this.audioTime = audioTime;
    }

    public String getAttachmentAudioId() {
        return attachmentAudioId;
    }

    public void setAttachmentAudioId(String attachmentAudioId) {
        this.attachmentAudioId = attachmentAudioId;
    }

    public Integer getIntelligentBroadcastType() {
        return intelligentBroadcastType;
    }

    public void setIntelligentBroadcastType(Integer intelligentBroadcastType) {
        this.intelligentBroadcastType = intelligentBroadcastType;
    }

    public String getChapterName() {
        return chapterName;
    }

    public void setChapterName(String chapterName) {
        this.chapterName = chapterName;
    }

    public String getScoringSheetId() {
        return scoringSheetId;
    }

    public CourseChapterSection setScoringSheetId(String scoringSheetId) {
        this.scoringSheetId = scoringSheetId;
        return this;
    }

    private String scoringSheetId;

    public Integer finishStatus;// 课程学习状态(单个人的)



    private Boolean addType;

    public String getAllCourseTime() {
        return allCourseTime;
    }

    public void setAllCourseTime(String allCourseTime) {
        this.allCourseTime = allCourseTime;
    }

    public Integer getPublishClient() {
        return publishClient;
    }

    public void setPublishClient(Integer publishClient) {
        this.publishClient = publishClient;
    }


    public Integer getIsExternalChongSu() {
        return isExternalChongSu;
    }
    /**
     * 调查问卷参与人数
     */
    private Integer questionnaireCount;

    public void setIsExternalChongSu(Integer isExternalChongSu) {
        this.isExternalChongSu = isExternalChongSu;
    }

    /**
     * 调查问卷按钮隐藏、显示、置灰
     */
    private Integer questionnaireButtonFlag;

    public Integer getQuestionnaireButtonFlag() {
        return questionnaireButtonFlag;
    }

    public void setQuestionnaireButtonFlag(Integer questionnaireButtonFlag) {
        this.questionnaireButtonFlag = questionnaireButtonFlag;
    }

    public Integer getQuestionnaireCount() {
        return questionnaireCount;
    }

    public void setQuestionnaireCount(Integer questionnaireCount) {
        this.questionnaireCount = questionnaireCount;
    }
    /*課程狀態*/
    private Integer courseStatus;


    public String getExamRegistId() {
        return ExamRegistId;
    }

    public void setExamRegistId(String examRegistId) {
        ExamRegistId = examRegistId;
    }

    public Integer getExamRegistTotalScore() {
        return ExamRegistTotalScore;
    }

    public void setExamRegistTotalScore(Integer examRegistTotalScore) {
        ExamRegistTotalScore = examRegistTotalScore;
    }

    public String getExamRegistTotalExamRecordId() {
        return ExamRegistTotalExamRecordId;
    }

    public void setExamRegistTotalExamRecordId(String examRegistTotalExamRecordId) {
        ExamRegistTotalExamRecordId = examRegistTotalExamRecordId;
    }

    public StudyTask getStudyTask() {
        return studyTask;
    }

    public void setStudyTask(StudyTask studyTask) {
        this.studyTask = studyTask;
    }

    public Integer getStudyPersonNum() {
        return studyPersonNum;
    }

    public void setStudyPersonNum(Integer studyPersonNum) {
        this.studyPersonNum = studyPersonNum;
    }

    public CourseSectionStudyProgress getProgress() {
        return progress;
    }

    public void setProgress(CourseSectionStudyProgress progress) {
        this.progress = progress;
    }
    public Integer getCourseStatus() {
        return courseStatus;
    }

    public void setCourseStatus(Integer courseStatus) {
        this.courseStatus = courseStatus;
    }

    public int getFlag() {
        return flag;
    }

    public void setFlag(int flag) {
        this.flag = flag;
    }

    public CourseChapter getCourseChapter() {
        return courseChapter;
    }

    public void setCourseChapter(CourseChapter courseChapter) {
        this.courseChapter = courseChapter;
    }

    public CourseInfo getCourseInfo() {
        return courseInfo;
    }

    public void setCourseInfo(CourseInfo courseInfo) {
        this.courseInfo = courseInfo;
    }

    public MultidimensionalStudentScoreSheet getMultidimensionalStudentScoreSheet() {
        return multidimensionalStudentScoreSheet;
    }

    public CourseChapterSection setMultidimensionalStudentScoreSheet(MultidimensionalStudentScoreSheet multidimensionalStudentScoreSheet) {
        this.multidimensionalStudentScoreSheet = multidimensionalStudentScoreSheet;
        return this;
    }


    public Boolean getAddType() {
        return addType;
    }

    public CourseChapterSection setAddType(Boolean addType) {
        this.addType = addType;
        return this;
    }

    public Integer getNoteNumber() {
        return noteNumber;
    }

    public void setNoteNumber(Integer noteNumber) {
        this.noteNumber = noteNumber;
    }

    public List<CourseChapterSection> getCourseChapterSections() {
        return courseChapterSections;
    }

    public void setCourseChapterSections(List<CourseChapterSection> courseChapterSections) {
        this.courseChapterSections = courseChapterSections;
    }

    public Integer getFinishStatus() {
        return finishStatus;
    }

    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }
}
