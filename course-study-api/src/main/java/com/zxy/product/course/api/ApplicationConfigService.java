package com.zxy.product.course.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.ApplicationConfig;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 学习助手应用管理
 */
@RemoteService(timeout = 100000)
public interface ApplicationConfigService {

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ApplicationConfig> find();

    /**
     * 查询多个应用名称，给app统计用
     * @param status
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<ApplicationConfig> findByIds(Integer status);

    @Transactional
    boolean insert(String name, Integer type, String uri, Optional<String> icon, Optional<Integer> orderOptional);

    @Transactional
    boolean update(String id, String name, Integer type, String uri, Optional<String> icon, Integer order);

    @Transactional
    boolean delete(String id);

    @Transactional
    boolean updateStatus(String id, Integer status);
}


