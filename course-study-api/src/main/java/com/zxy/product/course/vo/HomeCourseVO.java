package com.zxy.product.course.vo;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 十万：课程模块聚合VO
 * <AUTHOR>
 * @date 2024年11月08日 15:08
 */
public class HomeCourseVO implements Serializable {
    private static final long serialVersionUID = -1728837957417663295L;

    /**原find-by-id课程相关返回值*/
    private Map<String, List<? extends CustomizeParentVO>> courseCustomizeMap;

    /**原学习卡片的相关返回值*/
    private Map<String, List<StudyCardVO>> studyCardMap;

    /**原热门内容的相关返回值*/
    private List<? extends HotContentParentVO> hotContentCollect;

    /**分类课程相关返回值*/
    private Map<String, ClassifiedCourseVo> classifiedCourses;


    public Map<String, ClassifiedCourseVo> getClassifiedCourses() {
        return classifiedCourses;
    }

    public void setClassifiedCourses(Map<String, ClassifiedCourseVo> classifiedCourses) {
        this.classifiedCourses = classifiedCourses;
    }

    public Map<String, List<? extends CustomizeParentVO>> getCourseCustomizeMap() {
        return courseCustomizeMap;
    }

    public void setCourseCustomizeMap(Map<String, List<? extends CustomizeParentVO>> courseCustomizeMap) {
        this.courseCustomizeMap = courseCustomizeMap;
    }

    public Map<String, List<StudyCardVO>> getStudyCardMap() { return studyCardMap; }

    public void setStudyCardMap(Map<String, List<StudyCardVO>> studyCardMap) {
        this.studyCardMap = studyCardMap;
    }


    public List<? extends HotContentParentVO> getHotContentCollect() { return hotContentCollect; }

    public void setHotContentCollect(List<? extends HotContentParentVO> hotContentCollect) { this.hotContentCollect = hotContentCollect; }

    @Override
    public String toString() {
        return "HomeCourseVO{" +
                "courseCustomizeMap=" + courseCustomizeMap +
                ", studyCardMap=" + studyCardMap +
                ", hotContentCollect=" + hotContentCollect +
                '}';
    }
}
