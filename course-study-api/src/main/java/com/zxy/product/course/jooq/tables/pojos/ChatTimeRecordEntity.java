/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IChatTimeRecord;

import javax.annotation.Generated;


/**
 * 回答问题时间差
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ChatTimeRecordEntity extends BaseEntity implements IChatTimeRecord {

    private static final long serialVersionUID = 1L;

    private String answerId;
    private Long   time;
    private Long   modifyDate;

    public ChatTimeRecordEntity() {}

    public ChatTimeRecordEntity(ChatTimeRecordEntity value) {
        this.answerId = value.answerId;
        this.time = value.time;
        this.modifyDate = value.modifyDate;
    }

    public ChatTimeRecordEntity(
        String id,
        String answerId,
        Long   time,
        Long   createTime,
        Long   modifyDate
    ) {
        super.setId(id);
        this.answerId = answerId;
        this.time = time;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getAnswerId() {
        return this.answerId;
    }

    @Override
    public void setAnswerId(String answerId) {
        this.answerId = answerId;
    }

    @Override
    public Long getTime() {
        return this.time;
    }

    @Override
    public void setTime(Long time) {
        this.time = time;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Long modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ChatTimeRecordEntity (");

        sb.append(getId());
        sb.append(", ").append(answerId);
        sb.append(", ").append(time);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IChatTimeRecord from) {
        setId(from.getId());
        setAnswerId(from.getAnswerId());
        setTime(from.getTime());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IChatTimeRecord> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends ChatTimeRecordEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.ChatTimeRecordRecord r = new com.zxy.product.course.jooq.tables.records.ChatTimeRecordRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.ID, record.getValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.ANSWER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.ANSWER_ID, record.getValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.ANSWER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.TIME, record.getValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.MODIFY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.ChatTimeRecord.CHAT_TIME_RECORD.MODIFY_DATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
