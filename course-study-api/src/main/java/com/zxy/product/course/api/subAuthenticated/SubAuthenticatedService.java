package com.zxy.product.course.api.subAuthenticated;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.*;
import org.springframework.transaction.annotation.Propagation;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.SubAuthenticated;
import com.zxy.product.course.entity.SubAuthenticatedContentConfigure;
import com.zxy.product.course.entity.SubAuthenticatedDimension;
import com.zxy.product.course.entity.SubAuthenticatedMemberDimension;
import com.zxy.product.course.entity.SubAuthenticatedResourceAuditRecord;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;


/**
 * 子认证服务
 * Created by futzh on 2023/3/6.
 */
@RemoteService(timeout = 100000)
public interface SubAuthenticatedService {
    @Transactional
    String save(List<SubAuthenticatedContentConfigure> contentConfigureList, Optional<String> name, Optional<String> certificateId,
                Optional<String> organizationId, Optional<Integer> splitTrainingFlag, Optional<String> manageIgnoreFlag, Optional<Integer> authenticatedLevel, Optional<Integer> checkCertificateFlag);

    @Transactional
    String update(String id, List<SubAuthenticatedContentConfigure> contentConfigureList, Optional<String> name, Optional<String> certificateId,
                  Optional<String> organizationId, Optional<Integer> splitTrainingFlag, Optional<String> manageIgnoreFlag, Optional<Integer> authenticatedLevel, Optional<Integer> checkCertificateFlag);

    @Transactional
    Boolean publish(String id, Integer publish, String currentMemberId);

    @Transactional
    Boolean delete(String id);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    SubAuthenticated findSubAuthenticated(String id);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    String findMaxDimensionCode();

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    SubAuthenticated findAdminDetail(String id);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<SubAuthenticatedProgress> page(String subAuthenticatedId, int pageNum, int pageSize, Optional<String> memberName, Optional<String> memberReadName,
                                               Optional<String> organizationId, Optional<Integer> contain, Optional<Long> beginRegisterDate, Optional<Long> endRegisterDate, Map<String, Set<String>> grantOrganizationMap);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<SubAuthenticatedProgress> find(String subAuthenticatedId, int pageNum, int pageSize, Optional<String> memberName, Optional<String> memberReadName,
                                               Optional<String> organizationId, Optional<Integer> contain, Optional<Long> beginRegisterDate, Optional<Long> endRegisterDate,Map<String, Set<String>> grantOrganizationMap);


    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<SubAuthenticated> pageList(int pageNum, int pageSize, Optional<String> subAuthenticatedName, Optional<String> subAuthenticatedCode,
                                           List<String> grantOrganizationIds, Optional<Integer> status, Integer manageFlag, Optional<Long> firstPublishBeginDate, Optional<Long> firstPublishEndDate, Optional<Long> lastPublishBeginDate, Optional<Long> lastPublishEndDate, Optional<Integer> authenticatedLevel);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<Integer, List<SubAuthenticatedContentConfigure>> findProgressDetail(String subAuthenticatedId, String memberId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<SubAuthenticatedStudyOnline> findStudyList(String subAuthenticatedId, String studyGroupId, String memberId);

    @Transactional
    Boolean register(String subAuthenticatedId, String memberId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<SubAuthenticated>  studentSubAuthList(String groupId, String memberId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> findSubIds(String groupId, String memberId);

    @Transactional
    SubAuthenticatedResourceAuditRecord saveMaterials(Integer type, String subAuthenticatedId, String attachmentId, String currentUserId);

    PagedResult<Map<String, String>> findMaterials(Integer type, String subAuthenticatedId, Optional<String> name, Optional<String> code, List<String> orgId, Integer page, Integer pageSize, Optional<Long> beginDate, Optional<Long> endDate);

    List<SubAuthenticatedResourceAuditRecord> findMaterialsByIds(List<String> ids);

    List<SubAuthenticatedResourceAuditRecord> findMaterial(String subAuthenticatedId, String currentUserId);

    PagedResult<Map<String, Object>> getDimensions(Optional<Integer> status, Optional<String> name, Optional<String> code, String subAuthenticatedId, List<String> grantOrganizationIds, Integer page, Integer pageSize);

    List<SubAuthenticatedDimension> getDimensionsBySubId(String id);

    @Transactional
    SubAuthenticatedContentConfigure insertContentConfigure(SubAuthenticatedContentConfigure subAuthenticatedContentConfigure, String subAuthenticatedId);

    /**
     * 不传id的时候新增记录
     *
     * @param memberId
     * @param subAuthenticatedId
     * @return
     */
    @Transactional
    List<SubAuthenticatedMemberDimension> upsetDimensionById(List<Map> list, String memberId, String subAuthenticatedId);

    Map<String, Member> getMemberCodeNameMap(List<String> memberCodes);
    @Transactional
    void upsetSubAuthenticatedMemberDimension(List<SubAuthenticatedMemberDimension> collect);

    List<SubAuthenticatedMemberDimension> queryMemberDimension(String memberId, String subAuthenticatedId);

    PagedResult<Map<String, Object>> getCertificate(Optional<String> name, Optional<String> code, String subAuthenticatedId, List<String> grantOrganizationIds, Integer page, Integer pageSize);
    @Transactional
    void deleteCertificate(String id, String reasonForDeletion, Optional<String> fileId, String currentUserId,Boolean logicDelete);

    /**
     * 更新状态
     * @param subAuthenticatedId
     */
    @Transactional
    void updateCertificate(String subAuthenticatedId);

    @Transactional
    void deleteCertificate(List<String> memberIds, String subAuthenticatedIds);

    PagedResult<Map<String, String>> getDeleteCertificate(Optional<String> name, Optional<String> code, String subAuthenticatedId, List<String> grantOrganizationIds, Optional<Long> issuanceOfCertificatesBeginDate, Optional<Long> issuanceOfCertificatesEndDate, Optional<Long> deleteBeginDate, Optional<Long> deleteEndDate, Integer page, Integer pageSize);

    @Transactional
    void saveSubAuthenticatedCertificateRecord(List<SubAuthenticatedCertificateRecord> subAuthenticatedCertificateRecords) throws Exception;

    /**
     * 根据id查询证书校验状态
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer getCheckCertificateById(String subAuthenticatedId);


    /**
     * 添加子认证数据-文件中数据，不做任何处理
     * @param tmpDtos
     */
    @Transactional
    void addTmp(List<SubAuthenticatedTmp> tmpDtos);

    /**
     * 分页查导入文件中的信息-只用于展示
     * @param page
     * @param pageSize
     * @param fileId
     * @param subAuthenticatedId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<SubAuthenticatedTmp> findTmp(Integer page, Integer pageSize, String fileId, String subAuthenticatedId, String memberId);

    void deleteTmp(String fileId, String subAuthenticatedId, String memberId);
}
