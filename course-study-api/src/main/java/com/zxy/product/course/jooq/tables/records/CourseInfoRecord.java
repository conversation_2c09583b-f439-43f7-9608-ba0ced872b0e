/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseInfo;
import com.zxy.product.course.jooq.tables.interfaces.ICourseInfo;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseInfoRecord extends UpdatableRecordImpl<CourseInfoRecord> implements ICourseInfo {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_info.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_organization_id</code>. 组织ID
     */
    @Override
    public void setOrganizationId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_organization_id</code>. 组织ID
     */
    @Override
    public String getOrganizationId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_name</code>. 课程名称
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_name</code>. 课程名称
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_cover</code>. 课程封面
     */
    @Override
    public void setCover(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_cover</code>. 课程封面
     */
    @Override
    public String getCover() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_code</code>. 课程编码
     */
    @Override
    public void setCode(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_code</code>. 课程编码
     */
    @Override
    public String getCode() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_price</code>. 定价(分)
     */
    @Override
    public void setPrice(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_price</code>. 定价(分)
     */
    @Override
    public Integer getPrice() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_integral</code>. 所需积分
     */
    @Override
    public void setIntegral(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_integral</code>. 所需积分
     */
    @Override
    public Integer getIntegral() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_course_hour</code>. 课时
     */
    @Override
    public void setCourseHour(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_course_hour</code>. 课时
     */
    @Override
    public Integer getCourseHour() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_course_time</code>.
     */
    @Override
    public void setCourseTime(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_course_time</code>.
     */
    @Override
    public Integer getCourseTime() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_course_second</code>. 课程时长秒
     */
    @Override
    public void setCourseSecond(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_course_second</code>. 课程时长秒
     */
    @Override
    public Integer getCourseSecond() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_credit</code>. 学分
     */
    @Override
    public void setCredit(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_credit</code>. 学分
     */
    @Override
    public Integer getCredit() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_description</code>.
     */
    @Override
    public void setDescription(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_description</code>.
     */
    @Override
    public String getDescription() {
        return (String) get(11);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_begin_date</code>. 开始日期
     */
    @Override
    public void setBeginDate(Long value) {
        set(12, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_begin_date</code>. 开始日期
     */
    @Override
    public Long getBeginDate() {
        return (Long) get(12);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_end_date</code>. 结束日期
     */
    @Override
    public void setEndDate(Long value) {
        set(13, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_end_date</code>. 结束日期
     */
    @Override
    public Long getEndDate() {
        return (Long) get(13);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_certificate_id</code>. 授予证书ID
     */
    @Override
    public void setCertificateId(String value) {
        set(14, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_certificate_id</code>. 授予证书ID
     */
    @Override
    public String getCertificateId() {
        return (String) get(14);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_certificate_type</code>. 0 默认 1自定义时间
     */
    @Override
    public void setCertificateType(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_certificate_type</code>. 0 默认 1自定义时间
     */
    @Override
    public Integer getCertificateType() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_certificate_time</code>. 证书日期
     */
    @Override
    public void setCertificateTime(Long value) {
        set(16, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_certificate_time</code>. 证书日期
     */
    @Override
    public Long getCertificateTime() {
        return (Long) get(16);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_certificate_update_time</code>. 更新证书时间
     */
    @Override
    public void setCertificateUpdateTime(Long value) {
        set(17, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_certificate_update_time</code>. 更新证书时间
     */
    @Override
    public Long getCertificateUpdateTime() {
        return (Long) get(17);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    @Override
    public void setStatus(Integer value) {
        set(18, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(18);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_portal_num</code>. 关联门户数
     */
    @Override
    public void setPortalNum(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_portal_num</code>. 关联门户数
     */
    @Override
    public Integer getPortalNum() {
        return (Integer) get(19);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_type</code>. 课件类型（0：混合类，1: 文档, 2: 图片，3: URL, 4: scrom, 5: 音频类, 6: 视频类, 7: 电子书, 8: 任务, 9: 考试，10：课程, 11: 面授, 12 调研, 13: 评估）
     */
    @Override
    public void setType(Integer value) {
        set(20, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_type</code>. 课件类型（0：混合类，1: 文档, 2: 图片，3: URL, 4: scrom, 5: 音频类, 6: 视频类, 7: 电子书, 8: 任务, 9: 考试，10：课程, 11: 面授, 12 调研, 13: 评估）
     */
    @Override
    public Integer getType() {
        return (Integer) get(20);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_source</code>. 课程来源(0：内部录制，1：外部引进，2：共建共享；3：自主研发；4：其他；5：工作室)
     */
    @Override
    public void setSource(Integer value) {
        set(21, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_source</code>. 课程来源(0：内部录制，1：外部引进，2：共建共享；3：自主研发；4：其他；5：工作室)
     */
    @Override
    public Integer getSource() {
        return (Integer) get(21);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_release_time</code>. 发布时间
     */
    @Override
    public void setReleaseTime(Long value) {
        set(22, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_release_time</code>. 发布时间
     */
    @Override
    public Long getReleaseTime() {
        return (Long) get(22);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_release_member_id</code>. 发布人
     */
    @Override
    public void setReleaseMemberId(String value) {
        set(23, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_release_member_id</code>. 发布人
     */
    @Override
    public String getReleaseMemberId() {
        return (String) get(23);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_release_org_id</code>. 发布部门
     */
    @Override
    public void setReleaseOrgId(String value) {
        set(24, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_release_org_id</code>. 发布部门
     */
    @Override
    public String getReleaseOrgId() {
        return (String) get(24);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_develop_member_id</code>. 开发人
     */
    @Override
    public void setDevelopMemberId(String value) {
        set(25, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_develop_member_id</code>. 开发人
     */
    @Override
    public String getDevelopMemberId() {
        return (String) get(25);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_develop_time</code>. 开发时间
     */
    @Override
    public void setDevelopTime(Long value) {
        set(26, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_develop_time</code>. 开发时间
     */
    @Override
    public Long getDevelopTime() {
        return (Long) get(26);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_create_member_id</code>. 创建人
     */
    @Override
    public void setCreateMemberId(String value) {
        set(27, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_create_member_id</code>. 创建人
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(27);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(28, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(28);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_category_id</code>. 目录类别
     */
    @Override
    public void setCategoryId(String value) {
        set(29, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_category_id</code>. 目录类别
     */
    @Override
    public String getCategoryId() {
        return (String) get(29);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_add_type</code>. 1 普通模式
2 章节模式
     */
    @Override
    public void setAddType(Integer value) {
        set(30, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_add_type</code>. 1 普通模式
2 章节模式
     */
    @Override
    public Integer getAddType() {
        return (Integer) get(30);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_version_id</code>. 当前最新版本
     */
    @Override
    public void setVersionId(String value) {
        set(31, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_version_id</code>. 当前最新版本
     */
    @Override
    public String getVersionId() {
        return (String) get(31);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_learn_sequence</code>. 按顺序完成 0 否 1 按章节顺序
     */
    @Override
    public void setLearnSequence(Integer value) {
        set(32, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_learn_sequence</code>. 按顺序完成 0 否 1 按章节顺序
     */
    @Override
    public Integer getLearnSequence() {
        return (Integer) get(32);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_is_subject</code>. 是否为专题 0 - 否 1 - 是
     */
    @Override
    public void setIsSubject(Integer value) {
        set(33, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_is_subject</code>. 是否为专题 0 - 否 1 - 是
     */
    @Override
    public Integer getIsSubject() {
        return (Integer) get(33);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_study_days</code>. 建议学习天数
     */
    @Override
    public void setStudyDays(Integer value) {
        set(34, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_study_days</code>. 建议学习天数
     */
    @Override
    public Integer getStudyDays() {
        return (Integer) get(34);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_study_member_count</code>. 学习总人数
     */
    @Override
    public void setStudyMemberCount(Integer value) {
        set(35, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_study_member_count</code>. 学习总人数
     */
    @Override
    public Integer getStudyMemberCount() {
        return (Integer) get(35);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_register_member_count</code>. 注册总人数
     */
    @Override
    public void setRegisterMemberCount(Integer value) {
        set(36, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_register_member_count</code>. 注册总人数
     */
    @Override
    public Integer getRegisterMemberCount() {
        return (Integer) get(36);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_add_plan_member_count</code>. 学习计划添加人数
     */
    @Override
    public void setAddPlanMemberCount(Integer value) {
        set(37, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_add_plan_member_count</code>. 学习计划添加人数
     */
    @Override
    public Integer getAddPlanMemberCount() {
        return (Integer) get(37);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_styles</code>. 风格配置(专题)
     */
    @Override
    public void setStyles(String value) {
        set(38, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_styles</code>. 风格配置(专题)
     */
    @Override
    public String getStyles() {
        return (String) get(38);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_total_score</code>. 课程总得分(各学员的评分之和)
     */
    @Override
    public void setTotalScore(Integer value) {
        set(39, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_total_score</code>. 课程总得分(各学员的评分之和)
     */
    @Override
    public Integer getTotalScore() {
        return (Integer) get(39);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_score_member_count</code>. 评分人数
     */
    @Override
    public void setScoreMemberCount(Integer value) {
        set(40, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_score_member_count</code>. 评分人数
     */
    @Override
    public Integer getScoreMemberCount() {
        return (Integer) get(40);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_avg_score</code>. 平均评分
     */
    @Override
    public void setAvgScore(Integer value) {
        set(41, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_avg_score</code>. 平均评分
     */
    @Override
    public Integer getAvgScore() {
        return (Integer) get(41);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_shelve_time</code>. 首次上架时间
     */
    @Override
    public void setShelveTime(Long value) {
        set(42, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_shelve_time</code>. 首次上架时间
     */
    @Override
    public Long getShelveTime() {
        return (Long) get(42);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_off_time</code>. 下架时间,每次下架更新该时间
     */
    @Override
    public void setOffTime(Long value) {
        set(43, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_off_time</code>. 下架时间,每次下架更新该时间
     */
    @Override
    public Long getOffTime() {
        return (Long) get(43);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_is_public</code>. 是否公开：1受限，2公开
     */
    @Override
    public void setIsPublic(Integer value) {
        set(44, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_is_public</code>. 是否公开：1受限，2公开
     */
    @Override
    public Integer getIsPublic() {
        return (Integer) get(44);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    @Override
    public void setDeleteFlag(Integer value) {
        set(45, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    @Override
    public Integer getDeleteFlag() {
        return (Integer) get(45);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_description_text</code>.
     */
    @Override
    public void setDescriptionText(String value) {
        set(46, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_description_text</code>.
     */
    @Override
    public String getDescriptionText() {
        return (String) get(46);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_publish_client</code>. 发布终端   0: 全部, 1: PC, 2: APP
     */
    @Override
    public void setPublishClient(Integer value) {
        set(47, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_publish_client</code>. 发布终端   0: 全部, 1: PC, 2: APP
     */
    @Override
    public Integer getPublishClient() {
        return (Integer) get(47);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_publish_type</code>. 发布类型  0-定向(测试) 1-正式
     */
    @Override
    public void setPublishType(Integer value) {
        set(48, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_publish_type</code>. 发布类型  0-定向(测试) 1-正式
     */
    @Override
    public Integer getPublishType() {
        return (Integer) get(48);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    @Override
    public void setBusinessType(Integer value) {
        set(49, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    @Override
    public Integer getBusinessType() {
        return (Integer) get(49);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_lecturer</code>. 讲师
     */
    @Override
    public void setLecturer(String value) {
        set(50, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_lecturer</code>. 讲师
     */
    @Override
    public String getLecturer() {
        return (String) get(50);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_sequence</code>. 序列
     */
    @Override
    public void setSequence(Integer value) {
        set(51, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_sequence</code>. 序列
     */
    @Override
    public Integer getSequence() {
        return (Integer) get(51);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_source_detail</code>. 来源明细
     */
    @Override
    public void setSourceDetail(String value) {
        set(52, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_source_detail</code>. 来源明细
     */
    @Override
    public String getSourceDetail() {
        return (String) get(52);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_visits</code>. 浏览次数
     */
    @Override
    public void setVisits(Integer value) {
        set(53, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_visits</code>. 浏览次数
     */
    @Override
    public Integer getVisits() {
        return (Integer) get(53);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_share_sub</code>. 分享给下级部门，0 不分享 1 分享
     */
    @Override
    public void setShareSub(Integer value) {
        set(54, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_share_sub</code>. 分享给下级部门，0 不分享 1 分享
     */
    @Override
    public Integer getShareSub() {
        return (Integer) get(54);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_url</code>. 专题链接，个性化专题必填
     */
    @Override
    public void setUrl(String value) {
        set(55, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_url</code>. 专题链接，个性化专题必填
     */
    @Override
    public String getUrl() {
        return (String) get(55);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_cover_path</code>. 课程封面
     */
    @Override
    public void setCoverPath(String value) {
        set(56, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_cover_path</code>. 课程封面
     */
    @Override
    public String getCoverPath() {
        return (String) get(56);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_cover_material_id</code>. 课程封面素材id,用于优先获取关联的专题banner素材id
     */
    @Override
    public void setCoverMaterialId(String value) {
        set(57, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_cover_material_id</code>. 课程封面素材id,用于优先获取关联的专题banner素材id
     */
    @Override
    public String getCoverMaterialId() {
        return (String) get(57);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_collection_count</code>. 收藏次数
     */
    @Override
    public void setCollectionCount(Integer value) {
        set(58, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_collection_count</code>. 收藏次数
     */
    @Override
    public Integer getCollectionCount() {
        return (Integer) get(58);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_share_count</code>. 分享次数
     */
    @Override
    public void setShareCount(Integer value) {
        set(59, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_share_count</code>. 分享次数
     */
    @Override
    public Integer getShareCount() {
        return (Integer) get(59);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_audience_org</code>. 课程发布的最大受众部门名字，待删除
     */
    @Override
    public void setAudienceOrg(String value) {
        set(60, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_audience_org</code>. 课程发布的最大受众部门名字，待删除
     */
    @Override
    public String getAudienceOrg() {
        return (String) get(60);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_cloud_status</code>. 0 已上架, 1 待同步，2 已下架
     */
    @Override
    public void setCloudStatus(Integer value) {
        set(61, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_cloud_status</code>. 0 已上架, 1 待同步，2 已下架
     */
    @Override
    public Integer getCloudStatus() {
        return (Integer) get(61);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_cloud_time</code>. 云课程同步时间 （判断操作冲突用。）
     */
    @Override
    public void setCloudTime(Long value) {
        set(62, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_cloud_time</code>. 云课程同步时间 （判断操作冲突用。）
     */
    @Override
    public Long getCloudTime() {
        return (Long) get(62);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_cloud_rule</code>. 云课程发布规则，0 不影响，1 影响学习 2 全部影响
     */
    @Override
    public void setCloudRule(Integer value) {
        set(63, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_cloud_rule</code>. 云课程发布规则，0 不影响，1 影响学习 2 全部影响
     */
    @Override
    public Integer getCloudRule() {
        return (Integer) get(63);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_audiences</code>. 课程的所有受众项，逗号分隔
     */
    @Override
    public void setAudiences(String value) {
        set(64, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_audiences</code>. 课程的所有受众项，逗号分隔
     */
    @Override
    public String getAudiences() {
        return (String) get(64);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_description_app</code>. 课程简介--app加粗换行样式
     */
    @Override
    public void setDescriptionApp(String value) {
        set(65, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_description_app</code>. 课程简介--app加粗换行样式
     */
    @Override
    public String getDescriptionApp() {
        return (String) get(65);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_relative_gensee_id</code>. 课程关联直播id
     */
    @Override
    public void setRelativeGenseeId(String value) {
        set(66, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_relative_gensee_id</code>. 课程关联直播id
     */
    @Override
    public String getRelativeGenseeId() {
        return (String) get(66);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_is_sign</code>. 是否需要报名（0.否；1.是，默认不需要报名）
     */
    @Override
    public void setIsSign(Integer value) {
        set(67, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_is_sign</code>. 是否需要报名（0.否；1.是，默认不需要报名）
     */
    @Override
    public Integer getIsSign() {
        return (Integer) get(67);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_is_party</code>. 是否为党政课程或专题，0.否，1是
     */
    @Override
    public void setIsParty(Integer value) {
        set(68, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_is_party</code>. 是否为党政课程或专题，0.否，1是
     */
    @Override
    public Integer getIsParty() {
        return (Integer) get(68);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_use_video_speed</code>. 是否开启视频倍速播放
     */
    @Override
    public void setUseVideoSpeed(Integer value) {
        set(69, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_use_video_speed</code>. 是否开启视频倍速播放
     */
    @Override
    public Integer getUseVideoSpeed() {
        return (Integer) get(69);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_switch_hide</code>. 讨论区开关（0：关，1：开）
     */
    @Override
    public void setSwitchHide(Integer value) {
        set(70, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_switch_hide</code>. 讨论区开关（0：关，1：开）
     */
    @Override
    public Integer getSwitchHide() {
        return (Integer) get(70);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_history_hide</code>. 历史讨论记录开关（0：关，1：开）
     */
    @Override
    public void setHistoryHide(Integer value) {
        set(71, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_history_hide</code>. 历史讨论记录开关（0：关，1：开）
     */
    @Override
    public Integer getHistoryHide() {
        return (Integer) get(71);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_caption_flag</code>. 是否生成字幕（0：关，1开）
     */
    @Override
    public void setCaptionFlag(Integer value) {
        set(72, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_caption_flag</code>. 是否生成字幕（0：关，1开）
     */
    @Override
    public Integer getCaptionFlag() {
        return (Integer) get(72);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(73, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(73);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_caption_overall_status</code>. 字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)
     */
    @Override
    public void setCaptionOverallStatus(Integer value) {
        set(74, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_caption_overall_status</code>. 字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)
     */
    @Override
    public Integer getCaptionOverallStatus() {
        return (Integer) get(74);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_switch_mentor</code>. 数智导师开关
     */
    @Override
    public void setSwitchMentor(Integer value) {
        set(75, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_switch_mentor</code>. 数智导师开关
     */
    @Override
    public Integer getSwitchMentor() {
        return (Integer) get(75);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_caption_follow_release</code>. 字幕跟随课程自动发布 0=不跟随,1=跟随
     */
    @Override
    public void setCaptionFollowRelease(Integer value) {
        set(76, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_caption_follow_release</code>. 字幕跟随课程自动发布 0=不跟随,1=跟随
     */
    @Override
    public Integer getCaptionFollowRelease() {
        return (Integer) get(76);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_explicit_learning_status</code>. 学习状态显性化
     */
    @Override
    public void setExplicitLearningStatus(Integer value) {
        set(77, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_explicit_learning_status</code>. 学习状态显性化
     */
    @Override
    public Integer getExplicitLearningStatus() {
        return (Integer) get(77);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_learning_situation</code>. 开启学情分析报告(0: 关、1:开)
     */
    @Override
    public void setLearningSituation(Integer value) {
        set(78, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_learning_situation</code>. 开启学情分析报告(0: 关、1:开)
     */
    @Override
    public Integer getLearningSituation() {
        return (Integer) get(78);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_sponsoring_org</code>. 主办部门
     */
    @Override
    public void setSponsoringOrg(String value) {
        set(79, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_sponsoring_org</code>. 主办部门
     */
    @Override
    public String getSponsoringOrg() {
        return (String) get(79);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_pc_banner</code>. pc banner图片
     */
    @Override
    public void setPcBanner(String value) {
        set(80, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_pc_banner</code>. pc banner图片
     */
    @Override
    public String getPcBanner() {
        return (String) get(80);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_pc_banner_path</code>. pc banner图片路径
     */
    @Override
    public void setPcBannerPath(String value) {
        set(81, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_pc_banner_path</code>. pc banner图片路径
     */
    @Override
    public String getPcBannerPath() {
        return (String) get(81);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_app_banner</code>. app banner图片
     */
    @Override
    public void setAppBanner(String value) {
        set(82, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_app_banner</code>. app banner图片
     */
    @Override
    public String getAppBanner() {
        return (String) get(82);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_app_banner_path</code>. app banner图片路径
     */
    @Override
    public void setAppBannerPath(String value) {
        set(83, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_app_banner_path</code>. app banner图片路径
     */
    @Override
    public String getAppBannerPath() {
        return (String) get(83);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_mentor_state</code>. 课件摘要状态 0无需生成 1未生成 2待审核  3未审核通过  4已审核通过  5开启数智导师  6开启无摘要数智导师
     */
    @Override
    public void setMentorState(Integer value) {
        set(84, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_mentor_state</code>. 课件摘要状态 0无需生成 1未生成 2待审核  3未审核通过  4已审核通过  5开启数智导师  6开启无摘要数智导师
     */
    @Override
    public Integer getMentorState() {
        return (Integer) get(84);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_open</code>. 课程是否全员受众, 0=非全员、1=全员、2=内部人员
     */
    @Override
    public void setOpen(Integer value) {
        set(85, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_open</code>. 课程是否全员受众, 0=非全员、1=全员、2=内部人员
     */
    @Override
    public Integer getOpen() {
        return (Integer) get(85);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_update_date</code>. 修改时间，仅限后台管理修改使用
     */
    @Override
    public void setUpdateDate(Long value) {
        set(86, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_update_date</code>. 修改时间，仅限后台管理修改使用
     */
    @Override
    public Long getUpdateDate() {
        return (Long) get(86);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_voice_to_text</code>. 课程中智能笔记使用语音转文字数量
     */
    @Override
    public void setVoiceToText(Integer value) {
        set(87, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_voice_to_text</code>. 课程中智能笔记使用语音转文字数量
     */
    @Override
    public Integer getVoiceToText() {
        return (Integer) get(87);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_new_launch_visit_num</code>. 通过 最新上线 模块学习的人次
     */
    @Override
    public void setNewLaunchVisitNum(Integer value) {
        set(88, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_new_launch_visit_num</code>. 通过 最新上线 模块学习的人次
     */
    @Override
    public Integer getNewLaunchVisitNum() {
        return (Integer) get(88);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_recommend_visit_num</code>. 通过 为你推荐 模块学习的人次
     */
    @Override
    public void setRecommendVisitNum(Integer value) {
        set(89, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_recommend_visit_num</code>. 通过 为你推荐 模块学习的人次
     */
    @Override
    public Integer getRecommendVisitNum() {
        return (Integer) get(89);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_construction_type</code>. 全员共建共享，0=不是，1=是
     */
    @Override
    public void setConstructionType(Integer value) {
        set(90, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_construction_type</code>. 全员共建共享，0=不是，1=是
     */
    @Override
    public Integer getConstructionType() {
        return (Integer) get(90);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_skin_type</code>. 皮肤类型：0：科技蓝，1：党建红，2：活力橙
     */
    @Override
    public void setSkinType(Integer value) {
        set(91, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_skin_type</code>. 皮肤类型：0：科技蓝，1：党建红，2：活力橙
     */
    @Override
    public Integer getSkinType() {
        return (Integer) get(91);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_subject_type</code>. 专题类型 1: 集团公司统一组织 2: 省专公司组织 3: 网络自学
     */
    @Override
    public void setSubjectType(Integer value) {
        set(92, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_subject_type</code>. 专题类型 1: 集团公司统一组织 2: 省专公司组织 3: 网络自学
     */
    @Override
    public Integer getSubjectType() {
        return (Integer) get(92);
    }

    /**
     * Setter for <code>course-study.t_course_info.f_subject_mis_code</code>. 网络专题MIS编号
     */
    @Override
    public void setSubjectMisCode(String value) {
        set(93, value);
    }

    /**
     * Getter for <code>course-study.t_course_info.f_subject_mis_code</code>. 网络专题MIS编号
     */
    @Override
    public String getSubjectMisCode() {
        return (String) get(93);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseInfo from) {
        setId(from.getId());
        setOrganizationId(from.getOrganizationId());
        setName(from.getName());
        setCover(from.getCover());
        setCode(from.getCode());
        setPrice(from.getPrice());
        setIntegral(from.getIntegral());
        setCourseHour(from.getCourseHour());
        setCourseTime(from.getCourseTime());
        setCourseSecond(from.getCourseSecond());
        setCredit(from.getCredit());
        setDescription(from.getDescription());
        setBeginDate(from.getBeginDate());
        setEndDate(from.getEndDate());
        setCertificateId(from.getCertificateId());
        setCertificateType(from.getCertificateType());
        setCertificateTime(from.getCertificateTime());
        setCertificateUpdateTime(from.getCertificateUpdateTime());
        setStatus(from.getStatus());
        setPortalNum(from.getPortalNum());
        setType(from.getType());
        setSource(from.getSource());
        setReleaseTime(from.getReleaseTime());
        setReleaseMemberId(from.getReleaseMemberId());
        setReleaseOrgId(from.getReleaseOrgId());
        setDevelopMemberId(from.getDevelopMemberId());
        setDevelopTime(from.getDevelopTime());
        setCreateMemberId(from.getCreateMemberId());
        setCreateTime(from.getCreateTime());
        setCategoryId(from.getCategoryId());
        setAddType(from.getAddType());
        setVersionId(from.getVersionId());
        setLearnSequence(from.getLearnSequence());
        setIsSubject(from.getIsSubject());
        setStudyDays(from.getStudyDays());
        setStudyMemberCount(from.getStudyMemberCount());
        setRegisterMemberCount(from.getRegisterMemberCount());
        setAddPlanMemberCount(from.getAddPlanMemberCount());
        setStyles(from.getStyles());
        setTotalScore(from.getTotalScore());
        setScoreMemberCount(from.getScoreMemberCount());
        setAvgScore(from.getAvgScore());
        setShelveTime(from.getShelveTime());
        setOffTime(from.getOffTime());
        setIsPublic(from.getIsPublic());
        setDeleteFlag(from.getDeleteFlag());
        setDescriptionText(from.getDescriptionText());
        setPublishClient(from.getPublishClient());
        setPublishType(from.getPublishType());
        setBusinessType(from.getBusinessType());
        setLecturer(from.getLecturer());
        setSequence(from.getSequence());
        setSourceDetail(from.getSourceDetail());
        setVisits(from.getVisits());
        setShareSub(from.getShareSub());
        setUrl(from.getUrl());
        setCoverPath(from.getCoverPath());
        setCoverMaterialId(from.getCoverMaterialId());
        setCollectionCount(from.getCollectionCount());
        setShareCount(from.getShareCount());
        setAudienceOrg(from.getAudienceOrg());
        setCloudStatus(from.getCloudStatus());
        setCloudTime(from.getCloudTime());
        setCloudRule(from.getCloudRule());
        setAudiences(from.getAudiences());
        setDescriptionApp(from.getDescriptionApp());
        setRelativeGenseeId(from.getRelativeGenseeId());
        setIsSign(from.getIsSign());
        setIsParty(from.getIsParty());
        setUseVideoSpeed(from.getUseVideoSpeed());
        setSwitchHide(from.getSwitchHide());
        setHistoryHide(from.getHistoryHide());
        setCaptionFlag(from.getCaptionFlag());
        setModifyDate(from.getModifyDate());
        setCaptionOverallStatus(from.getCaptionOverallStatus());
        setSwitchMentor(from.getSwitchMentor());
        setCaptionFollowRelease(from.getCaptionFollowRelease());
        setExplicitLearningStatus(from.getExplicitLearningStatus());
        setLearningSituation(from.getLearningSituation());
        setSponsoringOrg(from.getSponsoringOrg());
        setPcBanner(from.getPcBanner());
        setPcBannerPath(from.getPcBannerPath());
        setAppBanner(from.getAppBanner());
        setAppBannerPath(from.getAppBannerPath());
        setMentorState(from.getMentorState());
        setOpen(from.getOpen());
        setUpdateDate(from.getUpdateDate());
        setVoiceToText(from.getVoiceToText());
        setNewLaunchVisitNum(from.getNewLaunchVisitNum());
        setRecommendVisitNum(from.getRecommendVisitNum());
        setConstructionType(from.getConstructionType());
        setSkinType(from.getSkinType());
        setSubjectType(from.getSubjectType());
        setSubjectMisCode(from.getSubjectMisCode());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseInfo> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseInfoRecord
     */
    public CourseInfoRecord() {
        super(CourseInfo.COURSE_INFO);
    }

    /**
     * Create a detached, initialised CourseInfoRecord
     */
    public CourseInfoRecord(String id, String organizationId, String name, String cover, String code, Integer price, Integer integral, Integer courseHour, Integer courseTime, Integer courseSecond, Integer credit, String description, Long beginDate, Long endDate, String certificateId, Integer certificateType, Long certificateTime, Long certificateUpdateTime, Integer status, Integer portalNum, Integer type, Integer source, Long releaseTime, String releaseMemberId, String releaseOrgId, String developMemberId, Long developTime, String createMemberId, Long createTime, String categoryId, Integer addType, String versionId, Integer learnSequence, Integer isSubject, Integer studyDays, Integer studyMemberCount, Integer registerMemberCount, Integer addPlanMemberCount, String styles, Integer totalScore, Integer scoreMemberCount, Integer avgScore, Long shelveTime, Long offTime, Integer isPublic, Integer deleteFlag, String descriptionText, Integer publishClient, Integer publishType, Integer businessType, String lecturer, Integer sequence, String sourceDetail, Integer visits, Integer shareSub, String url, String coverPath, String coverMaterialId, Integer collectionCount, Integer shareCount, String audienceOrg, Integer cloudStatus, Long cloudTime, Integer cloudRule, String audiences, String descriptionApp, String relativeGenseeId, Integer isSign, Integer isParty, Integer useVideoSpeed, Integer switchHide, Integer historyHide, Integer captionFlag, Timestamp modifyDate, Integer captionOverallStatus, Integer switchMentor, Integer captionFollowRelease, Integer explicitLearningStatus, Integer learningSituation, String sponsoringOrg, String pcBanner, String pcBannerPath, String appBanner, String appBannerPath, Integer mentorState, Integer open, Long updateDate, Integer voiceToText, Integer newLaunchVisitNum, Integer recommendVisitNum, Integer constructionType, Integer skinType, Integer subjectType, String subjectMisCode) {
        super(CourseInfo.COURSE_INFO);

        set(0, id);
        set(1, organizationId);
        set(2, name);
        set(3, cover);
        set(4, code);
        set(5, price);
        set(6, integral);
        set(7, courseHour);
        set(8, courseTime);
        set(9, courseSecond);
        set(10, credit);
        set(11, description);
        set(12, beginDate);
        set(13, endDate);
        set(14, certificateId);
        set(15, certificateType);
        set(16, certificateTime);
        set(17, certificateUpdateTime);
        set(18, status);
        set(19, portalNum);
        set(20, type);
        set(21, source);
        set(22, releaseTime);
        set(23, releaseMemberId);
        set(24, releaseOrgId);
        set(25, developMemberId);
        set(26, developTime);
        set(27, createMemberId);
        set(28, createTime);
        set(29, categoryId);
        set(30, addType);
        set(31, versionId);
        set(32, learnSequence);
        set(33, isSubject);
        set(34, studyDays);
        set(35, studyMemberCount);
        set(36, registerMemberCount);
        set(37, addPlanMemberCount);
        set(38, styles);
        set(39, totalScore);
        set(40, scoreMemberCount);
        set(41, avgScore);
        set(42, shelveTime);
        set(43, offTime);
        set(44, isPublic);
        set(45, deleteFlag);
        set(46, descriptionText);
        set(47, publishClient);
        set(48, publishType);
        set(49, businessType);
        set(50, lecturer);
        set(51, sequence);
        set(52, sourceDetail);
        set(53, visits);
        set(54, shareSub);
        set(55, url);
        set(56, coverPath);
        set(57, coverMaterialId);
        set(58, collectionCount);
        set(59, shareCount);
        set(60, audienceOrg);
        set(61, cloudStatus);
        set(62, cloudTime);
        set(63, cloudRule);
        set(64, audiences);
        set(65, descriptionApp);
        set(66, relativeGenseeId);
        set(67, isSign);
        set(68, isParty);
        set(69, useVideoSpeed);
        set(70, switchHide);
        set(71, historyHide);
        set(72, captionFlag);
        set(73, modifyDate);
        set(74, captionOverallStatus);
        set(75, switchMentor);
        set(76, captionFollowRelease);
        set(77, explicitLearningStatus);
        set(78, learningSituation);
        set(79, sponsoringOrg);
        set(80, pcBanner);
        set(81, pcBannerPath);
        set(82, appBanner);
        set(83, appBannerPath);
        set(84, mentorState);
        set(85, open);
        set(86, updateDate);
        set(87, voiceToText);
        set(88, newLaunchVisitNum);
        set(89, recommendVisitNum);
        set(90, constructionType);
        set(91, skinType);
        set(92, subjectType);
        set(93, subjectMisCode);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseInfoEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseInfoEntity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseInfoEntity)source;
        pojo.into(this);
        return true;
    }
}
