/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseInfo extends Serializable {

    /**
     * Setter for <code>course-study.t_course_info.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_info.f_organization_id</code>. 组织ID
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_organization_id</code>. 组织ID
     */
    public String getOrganizationId();

    /**
     * Setter for <code>course-study.t_course_info.f_name</code>. 课程名称
     */
    public void setName(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_name</code>. 课程名称
     */
    public String getName();

    /**
     * Setter for <code>course-study.t_course_info.f_cover</code>. 课程封面
     */
    public void setCover(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_cover</code>. 课程封面
     */
    public String getCover();

    /**
     * Setter for <code>course-study.t_course_info.f_code</code>. 课程编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_code</code>. 课程编码
     */
    public String getCode();

    /**
     * Setter for <code>course-study.t_course_info.f_price</code>. 定价(分)
     */
    public void setPrice(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_price</code>. 定价(分)
     */
    public Integer getPrice();

    /**
     * Setter for <code>course-study.t_course_info.f_integral</code>. 所需积分
     */
    public void setIntegral(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_integral</code>. 所需积分
     */
    public Integer getIntegral();

    /**
     * Setter for <code>course-study.t_course_info.f_course_hour</code>. 课时
     */
    public void setCourseHour(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_course_hour</code>. 课时
     */
    public Integer getCourseHour();

    /**
     * Setter for <code>course-study.t_course_info.f_course_time</code>.
     */
    public void setCourseTime(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_course_time</code>.
     */
    public Integer getCourseTime();

    /**
     * Setter for <code>course-study.t_course_info.f_course_second</code>. 课程时长秒
     */
    public void setCourseSecond(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_course_second</code>. 课程时长秒
     */
    public Integer getCourseSecond();

    /**
     * Setter for <code>course-study.t_course_info.f_credit</code>. 学分
     */
    public void setCredit(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_credit</code>. 学分
     */
    public Integer getCredit();

    /**
     * Setter for <code>course-study.t_course_info.f_description</code>.
     */
    public void setDescription(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_description</code>.
     */
    public String getDescription();

    /**
     * Setter for <code>course-study.t_course_info.f_begin_date</code>. 开始日期
     */
    public void setBeginDate(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_begin_date</code>. 开始日期
     */
    public Long getBeginDate();

    /**
     * Setter for <code>course-study.t_course_info.f_end_date</code>. 结束日期
     */
    public void setEndDate(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_end_date</code>. 结束日期
     */
    public Long getEndDate();

    /**
     * Setter for <code>course-study.t_course_info.f_certificate_id</code>. 授予证书ID
     */
    public void setCertificateId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_certificate_id</code>. 授予证书ID
     */
    public String getCertificateId();

    /**
     * Setter for <code>course-study.t_course_info.f_certificate_type</code>. 0 默认 1自定义时间
     */
    public void setCertificateType(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_certificate_type</code>. 0 默认 1自定义时间
     */
    public Integer getCertificateType();

    /**
     * Setter for <code>course-study.t_course_info.f_certificate_time</code>. 证书日期
     */
    public void setCertificateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_certificate_time</code>. 证书日期
     */
    public Long getCertificateTime();

    /**
     * Setter for <code>course-study.t_course_info.f_certificate_update_time</code>. 更新证书时间
     */
    public void setCertificateUpdateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_certificate_update_time</code>. 更新证书时间
     */
    public Long getCertificateUpdateTime();

    /**
     * Setter for <code>course-study.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_status</code>. 状态(0：未发布，1：已发布，2：取消发布，3：测试中 ，4：发布中，5已退库， 6工作室内课程审核中)
     */
    public Integer getStatus();

    /**
     * Setter for <code>course-study.t_course_info.f_portal_num</code>. 关联门户数
     */
    public void setPortalNum(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_portal_num</code>. 关联门户数
     */
    public Integer getPortalNum();

    /**
     * Setter for <code>course-study.t_course_info.f_type</code>. 课件类型（0：混合类，1: 文档, 2: 图片，3: URL, 4: scrom, 5: 音频类, 6: 视频类, 7: 电子书, 8: 任务, 9: 考试，10：课程, 11: 面授, 12 调研, 13: 评估）
     */
    public void setType(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_type</code>. 课件类型（0：混合类，1: 文档, 2: 图片，3: URL, 4: scrom, 5: 音频类, 6: 视频类, 7: 电子书, 8: 任务, 9: 考试，10：课程, 11: 面授, 12 调研, 13: 评估）
     */
    public Integer getType();

    /**
     * Setter for <code>course-study.t_course_info.f_source</code>. 课程来源(0：内部录制，1：外部引进，2：共建共享；3：自主研发；4：其他；5：工作室)
     */
    public void setSource(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_source</code>. 课程来源(0：内部录制，1：外部引进，2：共建共享；3：自主研发；4：其他；5：工作室)
     */
    public Integer getSource();

    /**
     * Setter for <code>course-study.t_course_info.f_release_time</code>. 发布时间
     */
    public void setReleaseTime(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_release_time</code>. 发布时间
     */
    public Long getReleaseTime();

    /**
     * Setter for <code>course-study.t_course_info.f_release_member_id</code>. 发布人
     */
    public void setReleaseMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_release_member_id</code>. 发布人
     */
    public String getReleaseMemberId();

    /**
     * Setter for <code>course-study.t_course_info.f_release_org_id</code>. 发布部门
     */
    public void setReleaseOrgId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_release_org_id</code>. 发布部门
     */
    public String getReleaseOrgId();

    /**
     * Setter for <code>course-study.t_course_info.f_develop_member_id</code>. 开发人
     */
    public void setDevelopMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_develop_member_id</code>. 开发人
     */
    public String getDevelopMemberId();

    /**
     * Setter for <code>course-study.t_course_info.f_develop_time</code>. 开发时间
     */
    public void setDevelopTime(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_develop_time</code>. 开发时间
     */
    public Long getDevelopTime();

    /**
     * Setter for <code>course-study.t_course_info.f_create_member_id</code>. 创建人
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_create_member_id</code>. 创建人
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>course-study.t_course_info.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_info.f_category_id</code>. 目录类别
     */
    public void setCategoryId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_category_id</code>. 目录类别
     */
    public String getCategoryId();

    /**
     * Setter for <code>course-study.t_course_info.f_add_type</code>. 1 普通模式
2 章节模式
     */
    public void setAddType(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_add_type</code>. 1 普通模式
2 章节模式
     */
    public Integer getAddType();

    /**
     * Setter for <code>course-study.t_course_info.f_version_id</code>. 当前最新版本
     */
    public void setVersionId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_version_id</code>. 当前最新版本
     */
    public String getVersionId();

    /**
     * Setter for <code>course-study.t_course_info.f_learn_sequence</code>. 按顺序完成 0 否 1 按章节顺序
     */
    public void setLearnSequence(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_learn_sequence</code>. 按顺序完成 0 否 1 按章节顺序
     */
    public Integer getLearnSequence();

    /**
     * Setter for <code>course-study.t_course_info.f_is_subject</code>. 是否为专题 0 - 否 1 - 是
     */
    public void setIsSubject(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_is_subject</code>. 是否为专题 0 - 否 1 - 是
     */
    public Integer getIsSubject();

    /**
     * Setter for <code>course-study.t_course_info.f_study_days</code>. 建议学习天数
     */
    public void setStudyDays(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_study_days</code>. 建议学习天数
     */
    public Integer getStudyDays();

    /**
     * Setter for <code>course-study.t_course_info.f_study_member_count</code>. 学习总人数
     */
    public void setStudyMemberCount(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_study_member_count</code>. 学习总人数
     */
    public Integer getStudyMemberCount();

    /**
     * Setter for <code>course-study.t_course_info.f_register_member_count</code>. 注册总人数
     */
    public void setRegisterMemberCount(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_register_member_count</code>. 注册总人数
     */
    public Integer getRegisterMemberCount();

    /**
     * Setter for <code>course-study.t_course_info.f_add_plan_member_count</code>. 学习计划添加人数
     */
    public void setAddPlanMemberCount(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_add_plan_member_count</code>. 学习计划添加人数
     */
    public Integer getAddPlanMemberCount();

    /**
     * Setter for <code>course-study.t_course_info.f_styles</code>. 风格配置(专题)
     */
    public void setStyles(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_styles</code>. 风格配置(专题)
     */
    public String getStyles();

    /**
     * Setter for <code>course-study.t_course_info.f_total_score</code>. 课程总得分(各学员的评分之和)
     */
    public void setTotalScore(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_total_score</code>. 课程总得分(各学员的评分之和)
     */
    public Integer getTotalScore();

    /**
     * Setter for <code>course-study.t_course_info.f_score_member_count</code>. 评分人数
     */
    public void setScoreMemberCount(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_score_member_count</code>. 评分人数
     */
    public Integer getScoreMemberCount();

    /**
     * Setter for <code>course-study.t_course_info.f_avg_score</code>. 平均评分
     */
    public void setAvgScore(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_avg_score</code>. 平均评分
     */
    public Integer getAvgScore();

    /**
     * Setter for <code>course-study.t_course_info.f_shelve_time</code>. 首次上架时间
     */
    public void setShelveTime(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_shelve_time</code>. 首次上架时间
     */
    public Long getShelveTime();

    /**
     * Setter for <code>course-study.t_course_info.f_off_time</code>. 下架时间,每次下架更新该时间
     */
    public void setOffTime(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_off_time</code>. 下架时间,每次下架更新该时间
     */
    public Long getOffTime();

    /**
     * Setter for <code>course-study.t_course_info.f_is_public</code>. 是否公开：1受限，2公开
     */
    public void setIsPublic(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_is_public</code>. 是否公开：1受限，2公开
     */
    public Integer getIsPublic();

    /**
     * Setter for <code>course-study.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_delete_flag</code>. 删除标识 0 未删除， 1 已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>course-study.t_course_info.f_description_text</code>.
     */
    public void setDescriptionText(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_description_text</code>.
     */
    public String getDescriptionText();

    /**
     * Setter for <code>course-study.t_course_info.f_publish_client</code>. 发布终端   0: 全部, 1: PC, 2: APP
     */
    public void setPublishClient(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_publish_client</code>. 发布终端   0: 全部, 1: PC, 2: APP
     */
    public Integer getPublishClient();

    /**
     * Setter for <code>course-study.t_course_info.f_publish_type</code>. 发布类型  0-定向(测试) 1-正式
     */
    public void setPublishType(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_publish_type</code>. 发布类型  0-定向(测试) 1-正式
     */
    public Integer getPublishType();

    /**
     * Setter for <code>course-study.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    public void setBusinessType(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_business_type</code>. 业务类型，0-课程；1-学习路径；2-专题
     */
    public Integer getBusinessType();

    /**
     * Setter for <code>course-study.t_course_info.f_lecturer</code>. 讲师
     */
    public void setLecturer(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_lecturer</code>. 讲师
     */
    public String getLecturer();

    /**
     * Setter for <code>course-study.t_course_info.f_sequence</code>. 序列
     */
    public void setSequence(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_sequence</code>. 序列
     */
    public Integer getSequence();

    /**
     * Setter for <code>course-study.t_course_info.f_source_detail</code>. 来源明细
     */
    public void setSourceDetail(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_source_detail</code>. 来源明细
     */
    public String getSourceDetail();

    /**
     * Setter for <code>course-study.t_course_info.f_visits</code>. 浏览次数
     */
    public void setVisits(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_visits</code>. 浏览次数
     */
    public Integer getVisits();

    /**
     * Setter for <code>course-study.t_course_info.f_share_sub</code>. 分享给下级部门，0 不分享 1 分享
     */
    public void setShareSub(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_share_sub</code>. 分享给下级部门，0 不分享 1 分享
     */
    public Integer getShareSub();

    /**
     * Setter for <code>course-study.t_course_info.f_url</code>. 专题链接，个性化专题必填
     */
    public void setUrl(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_url</code>. 专题链接，个性化专题必填
     */
    public String getUrl();

    /**
     * Setter for <code>course-study.t_course_info.f_cover_path</code>. 课程封面
     */
    public void setCoverPath(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_cover_path</code>. 课程封面
     */
    public String getCoverPath();

    /**
     * Setter for <code>course-study.t_course_info.f_cover_material_id</code>. 课程封面素材id,用于优先获取关联的专题banner素材id
     */
    public void setCoverMaterialId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_cover_material_id</code>. 课程封面素材id,用于优先获取关联的专题banner素材id
     */
    public String getCoverMaterialId();

    /**
     * Setter for <code>course-study.t_course_info.f_collection_count</code>. 收藏次数
     */
    public void setCollectionCount(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_collection_count</code>. 收藏次数
     */
    public Integer getCollectionCount();

    /**
     * Setter for <code>course-study.t_course_info.f_share_count</code>. 分享次数
     */
    public void setShareCount(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_share_count</code>. 分享次数
     */
    public Integer getShareCount();

    /**
     * Setter for <code>course-study.t_course_info.f_audience_org</code>. 课程发布的最大受众部门名字，待删除
     */
    public void setAudienceOrg(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_audience_org</code>. 课程发布的最大受众部门名字，待删除
     */
    public String getAudienceOrg();

    /**
     * Setter for <code>course-study.t_course_info.f_cloud_status</code>. 0 已上架, 1 待同步，2 已下架
     */
    public void setCloudStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_cloud_status</code>. 0 已上架, 1 待同步，2 已下架
     */
    public Integer getCloudStatus();

    /**
     * Setter for <code>course-study.t_course_info.f_cloud_time</code>. 云课程同步时间 （判断操作冲突用。）
     */
    public void setCloudTime(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_cloud_time</code>. 云课程同步时间 （判断操作冲突用。）
     */
    public Long getCloudTime();

    /**
     * Setter for <code>course-study.t_course_info.f_cloud_rule</code>. 云课程发布规则，0 不影响，1 影响学习 2 全部影响
     */
    public void setCloudRule(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_cloud_rule</code>. 云课程发布规则，0 不影响，1 影响学习 2 全部影响
     */
    public Integer getCloudRule();

    /**
     * Setter for <code>course-study.t_course_info.f_audiences</code>. 课程的所有受众项，逗号分隔
     */
    public void setAudiences(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_audiences</code>. 课程的所有受众项，逗号分隔
     */
    public String getAudiences();

    /**
     * Setter for <code>course-study.t_course_info.f_description_app</code>. 课程简介--app加粗换行样式
     */
    public void setDescriptionApp(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_description_app</code>. 课程简介--app加粗换行样式
     */
    public String getDescriptionApp();

    /**
     * Setter for <code>course-study.t_course_info.f_relative_gensee_id</code>. 课程关联直播id
     */
    public void setRelativeGenseeId(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_relative_gensee_id</code>. 课程关联直播id
     */
    public String getRelativeGenseeId();

    /**
     * Setter for <code>course-study.t_course_info.f_is_sign</code>. 是否需要报名（0.否；1.是，默认不需要报名）
     */
    public void setIsSign(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_is_sign</code>. 是否需要报名（0.否；1.是，默认不需要报名）
     */
    public Integer getIsSign();

    /**
     * Setter for <code>course-study.t_course_info.f_is_party</code>. 是否为党政课程或专题，0.否，1是
     */
    public void setIsParty(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_is_party</code>. 是否为党政课程或专题，0.否，1是
     */
    public Integer getIsParty();

    /**
     * Setter for <code>course-study.t_course_info.f_use_video_speed</code>. 是否开启视频倍速播放
     */
    public void setUseVideoSpeed(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_use_video_speed</code>. 是否开启视频倍速播放
     */
    public Integer getUseVideoSpeed();

    /**
     * Setter for <code>course-study.t_course_info.f_switch_hide</code>. 讨论区开关（0：关，1：开）
     */
    public void setSwitchHide(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_switch_hide</code>. 讨论区开关（0：关，1：开）
     */
    public Integer getSwitchHide();

    /**
     * Setter for <code>course-study.t_course_info.f_history_hide</code>. 历史讨论记录开关（0：关，1：开）
     */
    public void setHistoryHide(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_history_hide</code>. 历史讨论记录开关（0：关，1：开）
     */
    public Integer getHistoryHide();

    /**
     * Setter for <code>course-study.t_course_info.f_caption_flag</code>. 是否生成字幕（0：关，1开）
     */
    public void setCaptionFlag(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_caption_flag</code>. 是否生成字幕（0：关，1开）
     */
    public Integer getCaptionFlag();

    /**
     * Setter for <code>course-study.t_course_info.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>course-study.t_course_info.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>course-study.t_course_info.f_caption_overall_status</code>. 字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)
     */
    public void setCaptionOverallStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_caption_overall_status</code>. 字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)
     */
    public Integer getCaptionOverallStatus();

    /**
     * Setter for <code>course-study.t_course_info.f_switch_mentor</code>. 数智导师开关
     */
    public void setSwitchMentor(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_switch_mentor</code>. 数智导师开关
     */
    public Integer getSwitchMentor();

    /**
     * Setter for <code>course-study.t_course_info.f_caption_follow_release</code>. 字幕跟随课程自动发布 0=不跟随,1=跟随
     */
    public void setCaptionFollowRelease(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_caption_follow_release</code>. 字幕跟随课程自动发布 0=不跟随,1=跟随
     */
    public Integer getCaptionFollowRelease();

    /**
     * Setter for <code>course-study.t_course_info.f_explicit_learning_status</code>. 学习状态显性化
     */
    public void setExplicitLearningStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_explicit_learning_status</code>. 学习状态显性化
     */
    public Integer getExplicitLearningStatus();

    /**
     * Setter for <code>course-study.t_course_info.f_learning_situation</code>. 开启学情分析报告(0: 关、1:开)
     */
    public void setLearningSituation(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_learning_situation</code>. 开启学情分析报告(0: 关、1:开)
     */
    public Integer getLearningSituation();

    /**
     * Setter for <code>course-study.t_course_info.f_sponsoring_org</code>. 主办部门
     */
    public void setSponsoringOrg(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_sponsoring_org</code>. 主办部门
     */
    public String getSponsoringOrg();

    /**
     * Setter for <code>course-study.t_course_info.f_pc_banner</code>. pc banner图片
     */
    public void setPcBanner(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_pc_banner</code>. pc banner图片
     */
    public String getPcBanner();

    /**
     * Setter for <code>course-study.t_course_info.f_pc_banner_path</code>. pc banner图片路径
     */
    public void setPcBannerPath(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_pc_banner_path</code>. pc banner图片路径
     */
    public String getPcBannerPath();

    /**
     * Setter for <code>course-study.t_course_info.f_app_banner</code>. app banner图片
     */
    public void setAppBanner(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_app_banner</code>. app banner图片
     */
    public String getAppBanner();

    /**
     * Setter for <code>course-study.t_course_info.f_app_banner_path</code>. app banner图片路径
     */
    public void setAppBannerPath(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_app_banner_path</code>. app banner图片路径
     */
    public String getAppBannerPath();

    /**
     * Setter for <code>course-study.t_course_info.f_mentor_state</code>. 课件摘要状态 0无需生成 1未生成 2待审核  3未审核通过  4已审核通过  5开启数智导师  6开启无摘要数智导师
     */
    public void setMentorState(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_mentor_state</code>. 课件摘要状态 0无需生成 1未生成 2待审核  3未审核通过  4已审核通过  5开启数智导师  6开启无摘要数智导师
     */
    public Integer getMentorState();

    /**
     * Setter for <code>course-study.t_course_info.f_open</code>. 课程是否全员受众, 0=非全员、1=全员、2=内部人员
     */
    public void setOpen(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_open</code>. 课程是否全员受众, 0=非全员、1=全员、2=内部人员
     */
    public Integer getOpen();

    /**
     * Setter for <code>course-study.t_course_info.f_update_date</code>. 修改时间，仅限后台管理修改使用
     */
    public void setUpdateDate(Long value);

    /**
     * Getter for <code>course-study.t_course_info.f_update_date</code>. 修改时间，仅限后台管理修改使用
     */
    public Long getUpdateDate();

    /**
     * Setter for <code>course-study.t_course_info.f_voice_to_text</code>. 课程中智能笔记使用语音转文字数量
     */
    public void setVoiceToText(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_voice_to_text</code>. 课程中智能笔记使用语音转文字数量
     */
    public Integer getVoiceToText();

    /**
     * Setter for <code>course-study.t_course_info.f_new_launch_visit_num</code>. 通过 最新上线 模块学习的人次
     */
    public void setNewLaunchVisitNum(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_new_launch_visit_num</code>. 通过 最新上线 模块学习的人次
     */
    public Integer getNewLaunchVisitNum();

    /**
     * Setter for <code>course-study.t_course_info.f_recommend_visit_num</code>. 通过 为你推荐 模块学习的人次
     */
    public void setRecommendVisitNum(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_recommend_visit_num</code>. 通过 为你推荐 模块学习的人次
     */
    public Integer getRecommendVisitNum();

    /**
     * Setter for <code>course-study.t_course_info.f_construction_type</code>. 全员共建共享，0=不是，1=是
     */
    public void setConstructionType(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_construction_type</code>. 全员共建共享，0=不是，1=是
     */
    public Integer getConstructionType();

    /**
     * Setter for <code>course-study.t_course_info.f_skin_type</code>. 皮肤类型：0：科技蓝，1：党建红，2：活力橙
     */
    public void setSkinType(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_skin_type</code>. 皮肤类型：0：科技蓝，1：党建红，2：活力橙
     */
    public Integer getSkinType();

    /**
     * Setter for <code>course-study.t_course_info.f_subject_type</code>. 专题类型 1: 集团公司统一组织 2: 省专公司组织 3: 网络自学
     */
    public void setSubjectType(Integer value);

    /**
     * Getter for <code>course-study.t_course_info.f_subject_type</code>. 专题类型 1: 集团公司统一组织 2: 省专公司组织 3: 网络自学
     */
    public Integer getSubjectType();

    /**
     * Setter for <code>course-study.t_course_info.f_subject_mis_code</code>. 网络专题MIS编号
     */
    public void setSubjectMisCode(String value);

    /**
     * Getter for <code>course-study.t_course_info.f_subject_mis_code</code>. 网络专题MIS编号
     */
    public String getSubjectMisCode();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseInfo
     */
    public void from(ICourseInfo from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseInfo
     */
    public <E extends ICourseInfo> E into(E into);
}
