package com.zxy.product.course.api.course;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.CourseFeedback;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/29/9:32
 * @Description:
 */
@RemoteService
public interface QuestionFeedbackService {

    /**
     * 添加反馈*
     * @param feedback
     */
    @Transactional
    void add(CourseFeedback feedback);

    @Transactional
    void update(CourseFeedback feedback);

    @Transactional
    void updateCreateTime(String questionId, Long createTime);

    /**
     * 得到反馈详情*
     * @param id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Optional<CourseFeedback> getOptional(String id);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Optional<CourseFeedback> getOptionalByQuestionId(String questionContent, String memberId);

    /**
     * 反馈列表*
     * @param page
     * @param pageSize
     * @param content
     * @param name
     * @param fullName
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseFeedback> findPage(Integer page, Integer pageSize, Optional<String> content, Optional<String> name, Optional<String> fullName);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseFeedback> findList(List<String> ids);
}
