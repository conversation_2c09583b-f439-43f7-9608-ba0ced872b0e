/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.SubAuthenticatedCertificateRecordRecord;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 子认证-学员-证书记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class SubAuthenticatedCertificateRecord extends TableImpl<SubAuthenticatedCertificateRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_sub_authenticated_certificate_record</code>
     */
    public static final SubAuthenticatedCertificateRecord SUB_AUTHENTICATED_CERTIFICATE_RECORD = new SubAuthenticatedCertificateRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<SubAuthenticatedCertificateRecordRecord> getRecordType() {
        return SubAuthenticatedCertificateRecordRecord.class;
    }

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_id</code>. id
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "id");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_create_time</code>. 发证时间
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "发证时间");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_sub_authenticated_id</code>. 子认证id
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, String> SUB_AUTHENTICATED_ID = createField("f_sub_authenticated_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "子认证id");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_member_id</code>. 学员id
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "学员id");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_cancel_operator_id</code>. 删除操作人的id
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, String> CANCEL_OPERATOR_ID = createField("f_cancel_operator_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "删除操作人的id");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_operator_id</code>. 发证人id
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, String> OPERATOR_ID = createField("f_operator_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "发证人id");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_cancel_time</code>. 删除时间
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, Long> CANCEL_TIME = createField("f_cancel_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "删除时间");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_attachement_id</code>. 证明材料id
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, String> ATTACHEMENT_ID = createField("f_attachement_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "证明材料id");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_reason</code>. 删除原因描述
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, String> REASON = createField("f_reason", org.jooq.impl.SQLDataType.VARCHAR.length(64).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "删除原因描述");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_delete_flag</code>. 删除状态 0-未删除，1-已删除
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, Integer> DELETE_FLAG = createField("f_delete_flag", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "删除状态 0-未删除，1-已删除");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_import_time</code>. 导入时间
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, Long> IMPORT_TIME = createField("f_import_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "导入时间");

    /**
     * The column <code>course-study.t_sub_authenticated_certificate_record.f_status</code>. 发证状态 0 未发 1已发
     */
    public final TableField<SubAuthenticatedCertificateRecordRecord, Integer> STATUS = createField("f_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "发证状态 0 未发 1已发");

    /**
     * Create a <code>course-study.t_sub_authenticated_certificate_record</code> table reference
     */
    public SubAuthenticatedCertificateRecord() {
        this("t_sub_authenticated_certificate_record", null);
    }

    /**
     * Create an aliased <code>course-study.t_sub_authenticated_certificate_record</code> table reference
     */
    public SubAuthenticatedCertificateRecord(String alias) {
        this(alias, SUB_AUTHENTICATED_CERTIFICATE_RECORD);
    }

    private SubAuthenticatedCertificateRecord(String alias, Table<SubAuthenticatedCertificateRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private SubAuthenticatedCertificateRecord(String alias, Table<SubAuthenticatedCertificateRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "子认证-学员-证书记录表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<SubAuthenticatedCertificateRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_SUB_AUTHENTICATED_CERTIFICATE_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<SubAuthenticatedCertificateRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<SubAuthenticatedCertificateRecordRecord>>asList(Keys.KEY_T_SUB_AUTHENTICATED_CERTIFICATE_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public SubAuthenticatedCertificateRecord as(String alias) {
        return new SubAuthenticatedCertificateRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public SubAuthenticatedCertificateRecord rename(String name) {
        return new SubAuthenticatedCertificateRecord(name, null);
    }
}
