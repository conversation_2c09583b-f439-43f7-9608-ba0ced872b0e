package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.CourseInfoEntity;

import java.util.List;

/**
 * Created by keeley on 16/10/12.
 */
public class CourseInfo extends CourseInfoEntity {

    /**
     * 数智导师开启
     */
    public static final Integer SWITCH_MENTOR_ON = 1;
    public static final Integer SWITCH_MENTOR_OFF = 0;
    public static final Integer SWITCH_MENTOR_NULL = -1;
    private static final long serialVersionUID = -1437809936;

    /**
     * 字幕总体状态,0=-(未勾选生成字幕),1=无需生成(课件没有音视频),2=上传失败(未将音视频传给九天),3=生成中(有上传音视频文件，但非全部都转换字幕成功),4=已生成(所有音视频都转换字幕成功，且非全部都是发布状态),5=有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息,6=已发布(字幕全部发布)
     */
    public static final int CAPTION_OVERALL_STATUS_NO =0;//-  未勾选生成字幕
    public static final int CAPTION_OVERALL_STATUS_NO_NEED_TO_GENERATE =1;//无需生成(课件没有音视频)
    public static final int CAPTION_OVERALL_STATUS_NO_UPLOAD_FAILED =2;//上传失败(未将音视频传给九天)
    public static final int CAPTION_OVERALL_STATUS_GENERATE =3;//生成中(有上传音视频文件，但非全部都转换字幕成功)
    public static final int CAPTION_OVERALL_STATUS_GENERATED = 4;//已生成(所有音视频都转换字幕成功，且并非全部都是发布状态)
    public static final int CAPTION_OVERALL_STATUS_BUILD_FAILED =5;//生成失败(有上传音视频文件，当中有音视频生成字幕九天平台返回失败信息)
    public static final int CAPTION_OVERALL_STATUS_PUBLISH_ALL =6;//全部字幕已发布
    public static final int STATUS_UNPUBLISHED = 0; //未发布,草稿
    public static final int SOURCE_SELF = 0; // 自建
    public static final int SOURCE_CLOUD = 1; // 云课程
    public static final int SOURCE_STUDIO = 5; // 专家工作室
    public static final int STATUS_SHELVES = 1; //已上架(已发布)
    public static final int STATUS_THE_SHELVES = 2; //已下架(取消发布)
    public static final int STATUS_FIVE_SHELVES = 5; //退库(取消发布)
    public static final int STATUS_APPROVE = 6; //审核
    public static final int STATUS_THE_TEST = 3; //测试中
    public static final int SUBJECT_YES = 1; //测试中
    public static final int SHARE_SUB_NO = 0;
    public static final int SHARE_SUB_YES = 1;
    //发布中
    public static final int STATUS_IN_SHELVES = 4;
    //预留状态(消息判断是否更新课程状态所用)
    public static final int STATUS_NO = 0;
    public static final int GENERAL=1;//普通的
    public static final int PERSONALIZATION=2;//个性化的

    public static final int BUSINESS_TYPE_COURSE = 0; //业务类型为课程
    public static final int BUSINESS_TYPE_PATH = 1; //业务类型为学习路径
    public static final Integer BUSINESS_TYPE_SUBJECT = 2; //业务类型为专题
    public static final Integer BUSINESS_TYPE_STUDY_MAP = 3; //业务类型为学习地图

    /**
     * 任务类型: 学习地图
     */
    public static final int TASK_STUDY_MAP_TYPE = 17;

    public static final int TYPE_SUBJECT = 2; //业务类型为专题
    public static final int DELETE_FLAG_YES = 1; //标识为已删除
    public static final int DELETE_FLAG_NO = 0; //标识为未删除
    public static final int PUBLISH_TYPE_MEMBER = 0; //定向发布
    public static final int PUBLISH_TYPE_FORMAL = 1; //正式发布
    public static final int PUBLIST_CLIENT_APP = 2; // 发布终端   0: 全部, 1: PC, 2: APP
    public static final int PUBLISH_CLIENT_ALL = 0; // 发布终端   0: 全部, 1: PC, 2: APP
    public static final int PUBLISH_CLIENT_PC = 1; // 发布终端   0: 全部, 1: PC, 2: APP

    public static final int IS_PUBLIC_YES = 2; //公开

    //埋点数据
    //专题发布
    public static final String OPERATION_STATE_TOPIC_PLUBLISH = "01";
    //培训计划/专题修改
    public static final String OPERATION_STATE_MODIFY = "02";
    //计划关联已发布专题
    public static final String OPERATION_STATE_RELATION = "03";
    //专题取消发布
    public static final String OPERATION_STATE_TOPIC_UNPUBLISH = "04";
    //计划取消关联专题
    public static final String OPERATION_STATE_CANCEL_RELATION = "05";

    /**
     * 证书发布时间 0 默认点击的时间
     */
    public static final Integer COURSE_INFO_CERT_TYPE_NOMAL = 0;

    /**
     * 证书发布时间 0 自定义时间
     */
    public static final Integer COURSE_INFO_CERT_TYPE_CUSTOM = 1;
    /**
     * 音頻
     */
    public static final int COURSE_INFO_TYPE_AUDIO = 5;
    /**
     * 視頻
     */
    public static final int COURSE_INFO_TYPE_VIDEO = 6;

    public static final String URI = "course-study/course-info";

    public static final String SUBJECT_STATUS_KYE = "subject_status_key";

    public static final Integer PARTY_BUILING_COURSE_TRUE = 1;
    public static final Integer PARTY_BUILING_COURSE_FLASE = 0;

    //非党课
    public static final Integer IS_NO_PART=0;
    //开启视频倍速
    public static final Integer USE_VIDEO_DOUBLE_SPEED = 1;
    //关闭视频倍速
    public static final Integer NOT_USE_VIDEO_DOUBLE_SPEED = 0;

    //讨论区状态,评论内容状态   1-开启,0-关闭
    public static final Integer FORUM_OPEN = 1;
    public static final Integer FORUM_CLOSE = 0;
    //开启生成字幕
    public static final Integer OPEN_CAPTION = 1;
    //关闭生成字幕
    public static final Integer CLOSE_CAPTION = 0;

    /**
     * 0=禁用,1=启用
     */
    public static final Integer STATUS_ENABLED = 1;

    /**
     * 0=禁用,1=启用
     */
    public static final Integer STATUS_FORBIDDEN = 0;


    /**
     * 0=系统匹配 ,1=追加上级分享的课程
     */
    public static final Integer TYPE_INSIDE = 0;
    /**
     * 0=系统匹配 ,1=追加上级分享的课程
     */
    public static final Integer TYPE_ADD_TO = 1;

    public static final  String STUDY_PLAN_NUM_OPERATION_ADD= "add";
    public static final  String STUDY_PLAN_NUM_OPERATION_MINUS= "minus";

    /**
     * 0=不跟随发布,1=跟随发布
     */
    public static final int CAPTION_FOLLOW_RELEASE_YES = 1;
    public static final int CAPTION_FOLLOW_RELEASE_NO = 0;
    /**
     * 生成字幕
     */
    public static final int CAPTION_FLAG = 1;

    /**
     * 课程是否全员受众(全员受众, 走该字段：0=非全员、1=全员、2=内部人员)
     */
    public static final int OPEN_0 = 0;
    /**
     * 课程是否全员受众(全员受众, 走该字段：0=非全员、1=全员、2=内部人员)
     */
    public static final int OPEN_1 = 1;
    /**
     * 课程是否全员受众(全员受众, 走该字段：0=非全员、1=全员、2=内部人员)
     */
    public static final int OPEN_2 = 2;
    // 网络专题类型：: 1: 集团公司统一组织 2: 省专公司组织 3: 网络自学
    public static final Integer SUBJECT_TYPE_UNIFIED_ORGANIZATION = 1;
    public static final Integer SUBJECT_TYPE_PROVINCE_ORGANIZATION = 2;
    public static final Integer SUBJECT_TYPE_SELF_STUDY_ONLINE = 3;

    /**
     * 全员共建共享，0=不是，1=是
     */
    public static final int CONSTRUCTION_TYPE_YES = 1;
    public static final int CONSTRUCTION_TYPE_NO = 0;

    private String organizationName;//归属部门
    private String categoryName;//分类或者序列

    private Organization organization; //所属部门
    private Organization releaseOrg;    //  发布部门
    private Organization sponsoringOrganization; // 主办部门
    private Member releaseUser; //  发布人
    private Member developUser; //  开发人
    private Member createUser; //  开发人
    private GenseeWebCast relativeGensee; // 关联直播

    public Integer finishStatus;// 课程学习状态(单个人的)

    private CourseCategory category; // 所属目录

    private CourseRedShipAuditDetail courseRedShipAuditDetail; // 红船审核详情

    private List<CourseChapter> courseChapters;
    private List<CourseAttachment> courseAttachments;
    private List<AudienceItem> audienceItems;
    private List<AudienceItem> audienceOrgs;

    private List<CoursePhoto> photos; // 专题相册

    private List<CourseNote> courseNotes; // 用户的课程笔记列表
    private CourseScore courseScore; // 用户的课程评分
    private boolean isRegister; // 用户是否注册该课程
    private String collectId; // 用户收藏了该课程的收藏id
    private boolean isOwned; //用户是否拥有学习该课程的权限
    private CourseStudyProgress studyProgress;
    private List<BusinessTopic> businessTopics; // 相关话题
    private List<SubjectAdvertising> advertisings; // 专题广告
    private List<SubjectTextArea> textAreas; // 专题文字
    private String allCourseTime; //课程总时间
    private String topicNames; //课程管理标签的名称
//    private String certificateId;// 专题配置证书的id
    private Integer isCertificateRecord; //专题是否有学员获得证书
    private String topicIds;// 课程或者专题关联的标签id集合
    private String relationIds; // 课程或者专题引用的专题名称或者课程名称集合
    private Integer completedSectionCount; // 已完成章节
    private Integer totalSectionCount; //总章节数
    private Boolean studyPlanAdded;//学习计划已添加
    private Integer courseType; //专家工作室课程类型
    private Caption caption;
    private Boolean existedManager;//专题管理员是否存在
    private List<SubjectTopicManager> subjectTopicManager;
    private String notify;

    private List<CourseChapterSection> courseChapterSections;//团队学习班-查询专题下的课程

    private List<CourseCategory> deputyCategories;

    /**
     * 空间资源状态
     */
    private Integer courseVirtualSpacesStatus;

    private String contentName;
    private Integer visit;

    private List<Caption> captionList;
    private List<Topic> topicList;

    private Long usageCount;
    private Integer auditFlag;

    private Integer returnFlag;
    private Integer index;

    private Integer styleHistoryFlag;
    private Boolean display;
    private Boolean coursewareNoteFlag;
    private List<CourseAbility> courseAbilities;
    private Integer finish;
    private Integer required;

    private Integer showStatus;

    public Integer getReturnFlag() {
        return returnFlag;
    }

    public void setReturnFlag(Integer returnFlag) {
        this.returnFlag = returnFlag;
    }

    private String homeId;

    public String getHomeId() {
        return homeId;
    }

    public void setHomeId(String homeId) {
        this.homeId = homeId;
    }

    public String getNotify() {
        return notify;
    }

    public void setNotify(String notify) {
        this.notify = notify;
    }

    public Integer getShowStatus() {
        return showStatus;
    }

    public void setShowStatus(Integer showStatus) {
        this.showStatus = showStatus;
    }

    public Integer getFinish() {
        return finish;
    }

    public void setFinish(Integer finish) {
        this.finish = finish;
    }

    public Integer getRequired() {
        return required;
    }

    public void setRequired(Integer required) {
        this.required = required;
    }

    public List<CourseAbility> getCourseAbilities() {
        return courseAbilities;
    }

    public void setCourseAbilities(List<CourseAbility> courseAbilities) {
        this.courseAbilities = courseAbilities;
    }

    public Integer getStyleHistoryFlag() {
        return styleHistoryFlag;
    }

    public void setStyleHistoryFlag(Integer styleHistoryFlag) {
        this.styleHistoryFlag = styleHistoryFlag;
    }


    public Integer getIndex() {
        return index;
    }

    public void setIndex(Integer index) {
        this.index = index;
    }

    public Integer getAuditFlag() {
        return auditFlag;
    }

    public void setAuditFlag(Integer auditFlag) {
        this.auditFlag = auditFlag;
    }

    public Long getUsageCount() {
        return usageCount;
    }

    public void setUsageCount(Long usageCount) {
        this.usageCount = usageCount;
    }

    public List<Caption> getCaptionList() {
        return captionList;
    }

    public void setCaptionList(List<Caption> captionList) {
        this.captionList = captionList;
    }

    public List<Topic> getTopicList() {
        return topicList;
    }

    public void setTopicList(List<Topic> topicList) {
        this.topicList = topicList;
    }

    public String getContentName() {
        return contentName;
    }

    public void setContentName(String contentName) {
        this.contentName = contentName;
    }

    public Integer getVisit() {
        return visit;
    }

    public void setVisit(Integer visit) {
        this.visit = visit;
    }

    public Integer getCourseVirtualSpacesStatus() {
        return courseVirtualSpacesStatus;
    }

    public void setCourseVirtualSpacesStatus(Integer courseVirtualSpacesStatus) {
        this.courseVirtualSpacesStatus = courseVirtualSpacesStatus;
    }

    public List<CourseChapterSection> getCourseChapterSections() {
        return courseChapterSections;
    }


    // 重塑专区可以手动导入证书的专题ids
    public static final String[] IMPORT_CERTIFICATE_SUBJECT_IDS = {
            // --------------------------------------------uat
            "0301127d-2a87-4bde-b624-57249735f638", "16a6af4b-0107-410c-b02b-dc7a49d1140e", "1e262016-2e23-4f4d-9c1d-84444eeba21f", "1e846efe-ef5a-4d65-80ec-682b92c65364", "2baa0460-2011-4a98-9d73-a871e5a95002", "2c6f5088-8053-4340-8185-9d0b465f510e", "32d34a73-ab70-434c-8bbb-1a431bc2d967",
            "34f75b6a-b6e1-41e8-a0f6-688d0be68dc4", "34fecb26-f08f-4991-ae59-9bf2d2d922e3", "393da0a0-6f5e-45c1-942c-d929d5ce5077", "3ca15cc9-b3ba-4c71-a83d-aae84adcf81a", "4253a7c9-5381-4124-838f-68c5ebdc3c78", "459dd254-746a-4277-aab4-5dd6ac137d4d", "4705aff3-3efb-4711-9545-70a9666b9ca0",
            "54c94907-559f-499d-8ab3-d12aa5c62aa9", "55126afb-8d26-44e7-aa2c-e93583b7358b", "5627d81d-51bb-48b6-8b40-1b99bd44686f", "62e99934-cba8-4c4c-ae8f-ca29c422a66e", "64de7ea1-f19c-4162-9693-80f642e74a73", "664ba56f-27e4-4626-90a4-6973abc21068", "6ac6cac5-0945-4db7-bb48-ce7deb9b549a",
            "6b1da315-88b0-4da9-8c54-8eed71e9136e", "6f026ce9-4339-4fec-91fd-4f4e3960041c", "6fff821f-3440-4313-87b9-e8b8872bdc92", "78da85a2-f456-4493-acc5-5071953cc785", "8be8fbdc-ff04-48ee-8a7f-515ef5b4d269", "8cded185-f1bf-47e1-a933-72ea0b8d5c79", "9cf6c07d-856c-4553-8828-01ab459a6e41",
            "9e0ea5a1-6be6-4b26-834e-b28604c84b05", "a0f40459-6eb8-467f-8264-ffd3f5f0825d", "b2d77666-4c84-4e12-9722-5b363c9de849", "b5fcf10c-2990-4654-a421-602805792f4a", "b60cebdc-aa28-4016-a954-befde7e54be4", "b68e6567-f71c-4cfb-a229-e50ee6523327", "b944dcca-0e89-407c-a84a-80476310d111",
            "bd25c7d1-d104-412d-bd3c-455ecd1e186f", "c363b601-3970-4ef4-844c-fa34cdaad993", "c6912196-f73f-4ff2-8bbb-735bb3cdad61", "c7dbe912-bb77-41b6-b12b-cb9d97bd5898", "c9169d5b-9fac-4eab-8a6c-344cc9cbfef1", "cb06424c-34f3-48ab-a4ef-46b1bb0abbe7", "cb120f87-1c60-488c-9752-cf5dd339cd63",
            "cb479ea8-f07a-4fc2-b43c-2db6b0ebcff6", "cc55aed7-8503-4b95-b8c9-9e4a361c9600", "cca202b4-ac70-4dc5-a807-e1b900e4c8eb", "d00936df-d3aa-4eaf-8428-2461c0cb49b1", "d2934d66-b547-4c36-9c27-f296269e9186", "d6e85d22-3e78-468a-93b9-89b2b97bb955", "dac2620c-0513-48a6-9c13-4535079fee52",
            "dcf2b00a-2e1c-44f2-a58a-d3b9e8823620", "ddb1f463-3444-4e1c-9e9f-281492cd0513", "e3f70a6b-f5c9-4aaa-86c7-4432e907b506", "f7109619-af0e-4b8d-bb8c-6285ce474702", "fd0aa601-ba98-45b7-a2d1-1e73bb32f8f4", "fdc4e002-d13a-4e8c-ac38-32b1c7c6d2cf",


            // 第二次扩展需求
            "b2d77666-4c84-4e12-9722-5b363c9de849", "3ca15cc9-b3ba-4c71-a83d-aae84adcf81a", "6f026ce9-4339-4fec-91fd-4f4e3960041c", "d00936df-d3aa-4eaf-8428-2461c0cb49b1", "f7109619-af0e-4b8d-bb8c-6285ce474702", "6fff821f-3440-4313-87b9-e8b8872bdc92", "dac2620c-0513-48a6-9c13-4535079fee52",
            "34fecb26-f08f-4991-ae59-9bf2d2d922e3", "8be8fbdc-ff04-48ee-8a7f-515ef5b4d269", "1e846efe-ef5a-4d65-80ec-682b92c65364", "62e99934-cba8-4c4c-ae8f-ca29c422a66e", "32d34a73-ab70-434c-8bbb-1a431bc2d967", "b60cebdc-aa28-4016-a954-befde7e54be4", "cb479ea8-f07a-4fc2-b43c-2db6b0ebcff6",
            "55126afb-8d26-44e7-aa2c-e93583b7358b", "6ac6cac5-0945-4db7-bb48-ce7deb9b549a", "cca202b4-ac70-4dc5-a807-e1b900e4c8eb", "8cded185-f1bf-47e1-a933-72ea0b8d5c79", "459dd254-746a-4277-aab4-5dd6ac137d4d", "6b1da315-88b0-4da9-8c54-8eed71e9136e",
            // 扩展第三次的
            "de207682-d243-4178-a415-5d3b7efcdc3e", "5e41e9fd-b637-430e-b57a-cd1a7657e63f", "028a011e-c8d3-4b26-948f-bcdeff32fe06", "9e320bc2-339a-43d0-b39b-68d8c21a21f9", "1bb7b4af-7873-4e0a-b19e-5cd133016155", "27e95857-5572-4ec5-9046-15e0ad70c949", "464296c7-8501-4585-a00b-667314d00676",
            "d5e7f613-ff5b-4d8f-bee1-9ee7ba3c45a6", "0255ad8d-a4c1-4442-af27-eb66ecc22af1", "7cc2d877-6240-4384-af38-664d488b8c15", "6644c9ab-6bbc-11e8-82b1-00163e13fe73", "aa74f52e-a7b8-4e9d-8724-1cc54692e643", "a25dc54c-c60c-45d2-a9e7-9958ad26c6a2", "60590a17-d046-4bb6-beb2-fd46d21bcad8",
            "6214d2d3-bd0d-4a3a-8d47-ab12a42260b1", "4eae00e2-e988-48b1-a3dd-8ca9dc9664a3", "6984039b-d759-4a31-89e9-43ec8e975b9e", "b63a587b-ca7c-4f18-b24e-baa09d8b4151", "7a4da577-04fc-47d8-a5f1-43b9279f3d5d", "4d53774d-a410-4f6d-9822-c7e7e1e7d19f", "ec7644fc-632b-4870-8ed7-031d2ff918ef",
            "a241bc01-6df8-4a63-a9ce-cf3e7a63cc4f", "8216d78b-5ec0-4eec-9463-93c822b173ff", "0695f0ea-cb30-422a-a57a-23f5c86983c2", "7661ba82-7a81-4674-8da7-7217da33a88e", "1f4b3af3-a18d-46a1-8f44-4e28ba1ddf46", "ef5742c1-39aa-47eb-bd4b-52710fd5337a", "ca33999e-c77f-4d65-80ee-9a5fc5bec1ce",
            "b4f4cec7-2d97-406d-8c77-776ee8b2cf97", "ab982084-0284-4bd9-a375-1e7c718ecf9d", "c7759658-c77d-47bc-9815-80f32eb4e7a3", "4b733f2b-40ab-44f5-b1fd-1ee3ba1a6ed8", "136fb0f8-2db8-45ea-85fc-b2ea436a8395", "d3a44d49-f8c8-4d5a-bf74-deca7830cee5", "f96ec28e-8b30-4e64-ad90-bcabe9b5099f",
            "d5218421-baf9-4aec-a898-655c5bbda76c", "b1600c24-ed41-40ce-a04e-ab02a8adc9f8", "2cba1402-e468-4721-baa7-8291e459f79f", "54d1e59a-4738-48c8-af00-abff75c5438f", "38f1e803-2c44-47ac-95a0-d4b89834d5ad", "fdd4ad48-1804-4a08-bc1a-4416f5484aeb", "9c92ab65-70c0-4c9b-8cc5-7a9bfae72a8a",
            "c8c4f914-197a-4f24-b7a4-a67a2c884dc1", "5fff3ee5-27d7-494e-a312-44b55c718b75", "da7956f3-554c-4e65-8cf8-2e316a2d86c5", "1ca46fba-1087-46e7-be73-eb42c5f4c9c7", "e210abb4-4d51-4bf4-b9c5-404fc60c4c58", "9b37cac4-3840-4c39-9f49-47141381f118", "51b8b9d1-1bb7-4c11-98bd-af09b5d46333",
            "6a9db3d9-cf29-4851-a1db-d7df8a209c92", "eb9d2779-0f2a-4129-adac-17e656dc5760", "6282a258-287e-44a9-aead-6326cfc94bcf", "6ed60c85-56cf-4c87-abdb-03dff98ad76c", "83faefc7-1140-4b4c-86c4-00a1972fd71d", "974d4950-4d8e-4a97-b637-0a19d23b6d3e", "d25bf139-b28e-4f2d-9133-f514d5d3d15b",
            "fbb9650f-a91d-446d-a845-03f1337b0213", "11b1c665-a0d0-4919-af30-cceb0f9e12ce", "43d34b5b-1ec6-4436-874e-2ec1ed6bdeb1", "95c4f81d-a7d0-4fc9-8d6a-3c10e7dd8d04", "4ea17d25-b6bb-4ad6-bba7-2e970aa65b72", "60617154-e4b9-437d-9404-66e3e19b39e5", "3923c223-52ea-49d2-a0a7-1e011cd25310",
            "7f938e1c-68ab-438a-9698-c25bd95b8400", "4232ed03-e21a-4df4-8faa-cc48a0276d51", "9ba5a4b9-0aab-41e2-8b9b-8fbfdaaa8415", "e16ae26c-ac5f-44d2-a890-e8d2be935f2f", "5365fc74-f98a-4a30-aa42-afb0644b73c3", "844a7cad-8dfb-49e7-b307-2dd20d2c2078", "34383c8b-ed71-4186-afde-59d23b952c2c",
            "8f2236d0-2ca0-4ae7-a273-044204b9aa0d", "69e887a8-dd49-43f6-b828-050bfcdf7967", "00b46111-07c5-4d96-b17e-92f558e27393", "b6cdc309-d321-404b-97c5-e79ce1696b12", "8177d536-a0ca-4453-8683-c1af9b9ecd2f", "a277b04f-64ba-4953-964b-8f313feb7873", "5b049ac1-4066-4278-a691-72c32a804f35",
            "73710f0b-4cee-4645-b138-21d2c718ca8a", "fa5d5787-63cb-4012-8ac7-772efa55a414", "0ccb0383-5417-4713-9aa5-a8b5811981e9", "f33020cb-5be7-49d8-bec6-5983527e2b87", "c12bc55b-8132-43bc-bc1e-1f7d64b2f6be", "e0ec2497-fc96-41f9-8fa3-9acc50352cd9", "b0839a90-55ed-4caf-9dfb-e9edf655f1c8",
            "3d2fac77-7000-4dd6-a6c9-d5ffe045afa2", "936a7670-e60e-4a2d-b8f4-766924dcd7a8",


            // ------------------------------------------pro
            "78f07595-6aa9-42e6-bb21-71e455658da8", "736350f2-7184-4fd8-8451-d119eaf215db", "71d720e3-969d-4913-8681-8771e305bdb8", "ed22b85a-ad36-44ed-b496-455cd3514365", "b55ecb48-5583-4cf8-b475-8102165d7704", "a22cbe13-6066-41a9-8881-74e9df161f45", "f17ac3c2-84d2-4d9c-aa90-c030753dfd22",
            "4bc4bcda-fc7c-4bb1-a4a8-dbad28837a7d", "f96fcdc8-38af-441f-9a6d-043633375036", "8f80c322-5dab-482f-949b-795b852bc713", "cd205659-b023-400f-807d-4074c1595707", "b787cead-7ff1-4090-8c1e-54c6b9bd6f08", "a18517ab-5f66-4b7d-a27b-b217a638a15c", "7c84c6cb-52bc-42cf-abef-1e70711348a6",
            "7081b452-3cb4-4b38-9451-a34c9635a5fe", "5506aaa4-6dba-47e2-b60f-975f0c045a41", "587faccf-d803-403a-aca5-81c52308a139", "46edd9af-87c5-476d-99bb-6666fd6d7746", "3c9fed0f-df35-4ded-9a96-ec4360ab1937", "13a699ec-d2db-474d-b39a-bbed14301b16", "b1194345-74fe-473f-815b-bb5d9b55abf4",
            "27ddd7bc-aa15-4d44-a4bf-02dcb4d64012", "0f209f16-dd6f-4741-bf38-2c5dcc4f702b", "4fdc3b66-3942-40cb-8a17-757c3407b2e7", "b97cbddb-4f13-4e49-b084-35411ea28296", "6cab8206-cb51-41a9-a161-da9d338ec396", "f869c057-ed14-4c10-a15d-b7a3bf1ae64c", "44643b24-6449-4fa3-b993-a58e56c2e982",
            "d18f88fe-c32e-4cb8-bdb0-fc7838f6fde1", "ad72ff7c-247a-42af-aada-41492730b759", "80f64f70-c3ac-4601-a0bd-3dfb8b8ce870", "b1fa6100-07ae-485d-b408-0cda961af474", "c389e095-8562-439b-a1c1-2f5d9e0dd9e6", "56508243-a2ad-4cef-b9a7-ad9e6509c9ce", "1f7cb5df-3b24-4d31-95cf-12f67352babc",
            "acc953ec-8fec-4e41-b8c8-deccbebfd5d0", "5c188c9b-6403-46ee-bef8-6c3838865b95", "c3691c72-5b59-4573-ab22-f09e5d6979b8", "14c91fea-9a61-47f5-8039-c50eb817a4b9", "1a24e18a-ecc4-44a3-8002-0fd627237d94", "1b3588a9-b79a-428a-8aae-5c18beb49ab4", "aeecb0b5-a5e4-4a4b-8536-d64dead96fd3",
            "c775f0ab-b715-4988-a280-fd2f521575d0", "dc5f1cb7-01d5-420a-aa6b-919a420f25ee", "fdb72728-e188-49c6-89c1-1fdba1f456a9", "51e98474-6882-49e9-97a6-22ad57c74f38", "30a4b9b3-25ff-41de-a092-91d84e144c29", "daf4caf3-41c6-4aa5-8d98-78f8495ed304", "ff38a954-f1f0-456b-8933-2a1ef3b0d1db",
            "cea2c611-58e3-40a1-a5d5-bc7007d95a0e", "ff9b3eb3-5d3a-44e6-ad35-8485765271c1", "d35b1539-9000-41d5-b0b3-170053ef8fc5", "1a637a8e-4a9b-43cb-ae07-c95e52528d15",
            // 第二次需求扩展
            "170217ec-79a8-4aa6-93b5-6b4a36d8129e", "1aee80da-876a-4fb1-8123-c9976c24694f", "265bfa1a-79ef-4476-ae6b-7defb88d30a8", "27a4294a-dd83-4b38-b20b-8b2aece3f543", "3030ba4f-04da-4f6f-8884-3e41bab40e7e", "36294112-6a27-4ad5-ae5e-c0c397350a2a", "3b90751f-b7ad-4d6d-9d15-7f76c7479f3c",
            "4623b882-3a6b-40da-9da9-52b7911d8d07", "5227f90d-2bb5-4d6c-95b0-69ae71fc0482", "6cfe1971-05ee-4ebd-9d9d-c811a34484d4", "8d21f86e-3b79-445b-9a7a-b58d6051e753", "9031b1ab-8877-49db-a6b3-56af88a37159", "9d0acc7d-7e26-4186-9db6-662e7a017828", "9de690ec-e9aa-4b7e-a8c8-7cdd3aa2f1e2",
            "a0063c1d-cad4-4e95-9835-ccd38d2b91fc", "ad52ca9c-902f-471f-be9d-07117e10f185", "b4655df8-265e-4979-8c52-f0e59d3332c0", "c27f2a8b-f704-4000-943e-3933afd82a93", "c7453bcf-311c-4124-9629-651e5b0e7af2", "ca1589d3-2d66-4778-b33f-0ffd09c0f725", "d0284b59-f932-4240-a929-5b077eaa96b2",
            "d0aa029e-4739-45b9-8eb2-9d48a05e2129", "d5cf6d81-2107-47a7-b0d6-4ef9d02072b9", "d7749573-0da3-400a-91ff-352afc33ccd3", "f169e5f9-5ad8-4cc8-8ab5-c28bf22f7de6", "f1b54799-0c3a-4570-a8e7-a3e18c05ee26",
            //第三次扩展
            "00e066c6-8891-4ced-bf7a-70502b5cc78b", "045c5574-7c57-4ac1-af34-8a5d66dda589", "05b04690-7688-43d6-be72-7fbfac60dd07", "0a9ce506-a829-4991-838d-f07a6fe16908", "0b551442-82c6-4042-b1e2-665c69d3089a", "0bbf2cf4-f5a9-483c-a0c6-5dd5acd2f86a", "0d4b1ad6-83ad-4728-a912-a85176186991",
            "157cd903-967b-409e-b868-0a858abb9f90", "1736be47-5394-4b23-9a05-fc2b6243b0b1", "1832066b-f752-41dd-96a6-06b62512bcf7", "19479b1e-7d33-44e2-9d90-5dc4cd82d8e9", "1997f2a6-2259-410f-923a-a4f01f8ea378", "1b8ee143-14ea-4488-b004-72cb450104f4", "1ba262f0-a0f0-4362-8c0e-6e84fdb3b6e0",
            "1d927883-eb06-490b-a065-9dea7799f9f0", "29623ce7-c890-4fa0-bb0a-9a78558925d0", "2b617f56-89e6-4e87-b7c7-616cb8730de3", "32f1c61a-ec9b-4817-995c-5b1f083e602d", "339fe935-db75-48bb-bcbd-68b222418577", "35357a03-2f82-4822-ba42-154361e29eb7", "376ad448-caf1-4a67-a2e8-9ce867a861d0",
            "3f0d5c07-04bb-49e3-8388-390f052c9257", "3f34b499-ff8d-435b-a241-4456dc0f722c", "405d2079-12da-49a8-9997-3ca99d9d7ce6", "40aa67b0-61a4-413f-acba-b48c66ebdcce", "4400e16f-6bc8-426a-b296-856a95e811a3", "480a5b6d-fbe8-496c-b2bc-a5b2636ad6b1", "489ae90d-b1e2-48e9-b400-3fc5e55f6f6a",
            "48c2c483-1065-402c-b098-250e9f8ef7df", "4dd35319-86ee-43f5-a0bf-6f4294f37e1d", "529d4a68-bf76-48e6-b9e7-9d503a934a40", "53287d57-6e15-4e1b-b459-a2cb9d544fc1", "62f63c77-0a6e-44d0-9126-77622171139e", "64daee3e-a0d5-4b11-ba97-64e852b0221b", "6624a040-419a-4f4c-b37f-7f26adcad730",
            "698bdc4e-0dae-447c-a19b-254e462bcf69", "6da7e525-99c9-49f4-bc5c-80d5fdd6d950", "725c8ac5-4bea-4983-b278-72e5e38830cc", "7b4fcd2d-cc5c-4baf-a0a3-4a1d7f04a882", "7c50c96a-a5d6-4c8a-a2e1-b3b79b362725", "7e4d0d5a-65f2-4693-87b4-2f26167fb860", "8e380fac-7f1a-4265-899e-e524ff2b62c2",
            "8e7227e8-fcd9-4c61-964d-ee0cf2fce943", "8fa24062-dddc-4b46-84bc-1b5277311fe2", "928ee4b3-0891-40a8-b11c-7899ae3f734c", "9318b053-6b3e-45ee-bb9c-4014f2a6905d", "937a1b80-d2a8-4b40-9c1f-f3bde1c48ea0", "9499f8a0-c1b9-4102-88cb-6940b1d5476a", "97a07a06-225b-4a61-b19a-bf5cf68a0e58",
            "9d50ed40-b4a3-4482-8dda-6c68ada5f743", "9de1e727-18bc-4b17-bae2-e781d135d807", "a4fe42fb-9cee-4e91-9096-97c11901f455", "a50fe13a-3992-4b73-82e5-c881c238bf7c", "a5dbedc5-ae3b-4c54-b747-279d5642d511", "a7c010e3-9998-435b-9d74-165b76934747", "a92d91a4-cddc-4c89-b797-5e3b6ba9d602",
            "ac90f9d5-fd34-4755-9061-8d65b50f7d02", "ad802636-ba5e-4a92-ae95-ca6c8ec01aa3", "b4968abf-29b6-4dd1-8170-7ab38312fb86", "b71c3b92-852e-496d-9735-06483ac4e902", "b728e57b-1cc1-4910-942d-06f657684d10", "b734cbbe-d1c2-42a2-8a80-dc1b374c4483", "b79e90a3-b4b5-4eb6-a5b4-73ad38831cb3",
            "be0327be-ed83-48ae-ac16-ac95a709986b", "bfae55bb-ad0c-4dc7-bd4a-deed33c65219", "bfe94573-1e7f-4728-b269-645b02515916", "c0c712a6-0bbb-4b8c-a4a2-1da76aacfb4d", "c67a4a10-b36a-417e-b6d6-cd8204cca678", "c9d4bbef-890b-4853-8061-0439c0bf7612", "ca36e17f-a9e5-44f8-a1ac-93f84343ca99",
            "cb149cb1-3ba9-4a06-abbb-9b0bcce85cc7", "d13b5a8b-4297-4186-b7d5-15a51ca38ef7", "d87c6f60-5e2a-4920-80a6-843200d79a15", "d8a9ee29-2cbe-437e-b728-45a2fe0405d3", "d99a642d-f69f-45f0-bd2c-e9d46f823999", "dafc4305-69b9-4fe5-97f8-9e31c334ccd8", "dd3f4cf9-fbfe-4d11-a845-dfad356ed226",
            "e135a0c6-890d-4f9d-afc4-c05c67d09497", "e173cc80-40b3-476d-8ef8-8636f3f96057", "f191e71e-bf09-44f2-ad04-570bdc7b75d6", "f254e66a-f485-4513-86e6-f436ad19c530", "f2ef4d90-eb2f-44e9-8f21-f31d1b1ab6a1", "f80d6c0f-921b-4271-b39e-5cb99817cda5", "f966b4b3-fd61-4f50-b6ef-1d94cd0f613c",
            "fa4a7915-b46c-438a-90a1-786284d1007d", "fca30a9e-9d59-4866-9dd2-221a0c7564f7"


    };

    // 重塑专区可以手动导入证书的专题编码s
    public static final String[] IMPORT_CERTIFICATE_SUBJECT_CODES = {
            // uat
            "200324cmcc002", "200317cmcc003", "210329cmcc001", "211108cmcc011", "210512cmcc007", "210512cmcc006", "211109cmcc002", "210622cmcc002", "211108cmcc009", "200324cmcc005", "211108cmcc003", "210622cmcc001", "211109cmcc009", "200331cmcc002",
            "200325cmcc001", "211109cmcc005", "200324cmcc009", "211109cmcc001", "200304cmcc001", "210524cmcc008", "211109cmcc006", "211109cmcc010", "211108cmcc004", "211108cmcc007", "210524cmcc007", "211108cmcc010", "211109cmcc008", "200324cmcc003",
            "200324cmcc010", "200317cmcc001", "211108cmcc002", "210524cmcc003", "211109cmcc003", "210524cmcc009", "210512cmcc004", "200327cmcc003", "210524cmcc010", "200317cmcc002", "210524cmcc005", "200317cmcc004", "210623cmcc001", "210512cmcc005",
            "211109cmcc004", "210524cmcc002", "211109cmcc007", "211108cmcc005", "200324cmcc008", "200324cmcc011", "211108cmcc008", "210524cmcc001", "210512cmcc003", "200331cmcc001", "211108cmcc006", "210524cmcc004", "210524cmcc006",
            // 第二次拓展导入的范围
            "211108cmcc011", "211109cmcc002", "211108cmcc009", "211108cmcc003", "211109cmcc009", "211109cmcc005", "211109cmcc001", "211109cmcc006", "211109cmcc010", "211108cmcc004", "211108cmcc007", "211108cmcc010", "211109cmcc008", "211108cmcc002",
            "211109cmcc003", "211109cmcc004", "211109cmcc007", "211108cmcc005", "211108cmcc008", "211108cmcc006",
            // 第三次扩展
            "240425cmcc082","240425cmcc019","240425cmcc014","240425cmcc034","240425cmcc088","240425cmcc068","240425cmcc043","230913cmcc001","240425cmcc056","240425cmcc036","240425cmcc016","240425cmcc048","240425cmcc079","240425cmcc050","230914cmcc001",
            "240425cmcc093","240425cmcc074","240425cmcc069","240425cmcc017","240425cmcc042","240425cmcc030","240425cmcc071","240425cmcc026","240425cmcc059","240425cmcc077","240425cmcc049","240425cmcc086","240425cmcc013","240425cmcc054","240425cmcc024",
            "240425cmcc072","240425cmcc025","240425cmcc062","240425cmcc027","240425cmcc081","240425cmcc060","240425cmcc063","230906cmcc002","240425cmcc035","240425cmcc029","240425cmcc020","240425cmcc073","240425cmcc084","240425cmcc033","240425cmcc064",
            "240425cmcc078","240425cmcc080","240425cmcc094","240425cmcc070","240425cmcc065","240425cmcc058","240425cmcc075","240425cmcc052","240425cmcc015","240425cmcc032","240425cmcc023","240425cmcc085","240425cmcc022","240425cmcc040","240425cmcc092",
            "240425cmcc047","240425cmcc039","240425cmcc028","240425cmcc083","240425cmcc090","240425cmcc041","240425cmcc053","240425cmcc038","240425cmcc066","240425cmcc044","240425cmcc046","240425cmcc018","240425cmcc055","240425cmcc001","240425cmcc091",
            "240425cmcc076","240425cmcc057","240425cmcc061","240425cmcc031","240425cmcc037","240425cmcc089","240425cmcc045","240425cmcc087","240425cmcc067","240425cmcc051",



            // pro
            "210311cmcc005", "210311cmcc002", "200527cmcc012", "200527cmcc037", "200527cmcc019", "200527cmcc038", "200527cmcc024", "210311cmcc001", "200527cmcc033", "200527cmcc032", "200526cmcc001", "200527cmcc029", "210311cmcc012", "210311cmcc006",
            "200527cmcc035", "200527cmcc028", "200527cmcc041", "200527cmcc031", "200527cmcc022", "210311cmcc009", "200527cmcc015", "210311cmcc016", "210311cmcc014", "210311cmcc008", "200527cmcc030", "200527cmcc009", "200527cmcc027", "200527cmcc026",
            "210311cmcc011", "200527cmcc014", "200526cmcc003", "200527cmcc023", "210311cmcc003", "200526cmcc004", "210312cmcc003", "200527cmcc016", "210311cmcc007", "200527cmcc013", "200527cmcc010", "200527cmcc040", "200527cmcc025", "200317cmcc001",
            "200520cmcc002", "200527cmcc020", "200527cmcc036", "200527cmcc017", "210312cmcc002", "210311cmcc013", "200527cmcc005", "200527cmcc042", "200527cmcc034", "200527cmcc018", "200527cmcc021",
            // 第二次拓展导入的范围
            "211130cmcc001", "211130cmcc002", "211130cmcc003", "211130cmcc004", "211130cmcc005", "211130cmcc006", "211130cmcc007", "211130cmcc008", "211130cmcc009", "211130cmcc010", "211130cmcc011", "211201cmcc001", "211201cmcc002", "211201cmcc014",
            "211201cmcc003", "211201cmcc004", "211201cmcc005", "211201cmcc006", "211201cmcc007", "211201cmcc008", "211201cmcc009", "211201cmcc015", "211201cmcc010", "211201cmcc011", "211201cmcc012", "211201cmcc013",
            // 第三次扩展
            "230728cmcc001", "230728cmcc002", "230728cmcc003", "230728cmcc004", "230728cmcc005", "230728cmcc007", "230728cmcc008", "230728cmcc009", "230728cmcc011", "230728cmcc012", "230728cmcc014", "230728cmcc015", "230728cmcc010", "230728cmcc013",
            "230728cmcc016", "230728cmcc017", "230728cmcc018", "230728cmcc019", "230728cmcc020", "230728cmcc021", "230728cmcc022", "230728cmcc026", "230728cmcc028", "230728cmcc023", "230728cmcc024", "230728cmcc025", "230728cmcc027", "230728cmcc029",
            "230728cmcc030", "230728cmcc031", "230728cmcc032", "230728cmcc033", "230728cmcc034", "230728cmcc035", "230728cmcc036", "230728cmcc037", "230728cmcc040", "230728cmcc041", "230728cmcc042", "230728cmcc038", "230728cmcc039", "230728cmcc043",
            "230728cmcc044", "230728cmcc045", "230728cmcc046", "230728cmcc047", "230728cmcc048", "230728cmcc049", "230728cmcc050", "230728cmcc051", "230728cmcc052", "230728cmcc053", "230728cmcc054", "230728cmcc060", "230728cmcc055", "230728cmcc056",
            "230728cmcc057", "230728cmcc058", "230728cmcc059", "230728cmcc061", "230728cmcc062", "230728cmcc063", "230728cmcc064", "230728cmcc065", "230728cmcc066", "230728cmcc067", "230728cmcc068", "230728cmcc069", "230728cmcc070", "230728cmcc073",
            "230728cmcc071", "230728cmcc072", "230728cmcc074", "230728cmcc075", "230728cmcc076", "230728cmcc077", "230728cmcc078", "230728cmcc079", "230728cmcc080", "230728cmcc081", "230728cmcc082", "230728cmcc083", "230728cmcc084", "230728cmcc085",
            "230728cmcc086", "230728cmcc087"

    };


    // 强基专区可以手动导入证书的专题ids
    public static final String[] IMPORT_CERTIFICATE_SUBJECT_IDS_QJ = {
            // uat
            "ad320409-16e7-40c9-8da3-4f379d4c2609",
            "8624f12f-1e82-429c-94c1-43da26c56985",
            "674d94fa-bdaa-4181-9285-06a4bac98043",

            // pro
            "19151ce9-8e40-4714-b664-d100e52dcbb8",
            "22eac9b7-d209-48ea-89cd-132f4c091a77",
            "2afd80da-2c39-4eb7-b5c8-ee6ca563a0c4",
            "46526ec8-0799-4ea5-9172-ed215a135f63",
            "5122462c-2241-4568-be82-b56f60e31ab6",
            "5a70f439-3f68-49c0-96d9-00ed94cac51f",
            "6294a248-f64e-40db-8977-8171d6738f5d",
            "93defb75-377d-42b5-863f-de3f74d07329",
            "94668ceb-cb8e-4492-90e5-29e91950dd90",
            "bb2f5f48-a7cf-4f68-9f8e-9f070cc43682",
            "c448011d-ec93-45ec-9897-8c7c26b876be",
            "cfb848fa-5df2-4186-9a02-af98c0553627",
            "f8987ca5-3867-4937-86c6-0abdd944093d",
            "fc125d68-aa69-43ef-8c85-a981126aee44"

    };

    // 强基专区可以手动导入证书的专题编码s
    public static final String[] IMPORT_CERTIFICATE_SUBJECT_CODES_QJ = {
            // uat
            "221212cmcc002",
            "221212cmcc001",
            "221208cmcc001",


            // pro
            "220517cmcc002",
            "221024cmcc007",
            "221024cmcc008",
            "220517cmcc001",
            "221024cmcc009",
            "221024cmcc010",
            "220517cmcc003",
            "221024cmcc011",
            "221024cmcc012",
            "220706cmcc001",
            "221024cmcc013",
            "221024cmcc014",
            "221024cmcc015",
            "221024cmcc016"
    };


    public void setCourseChapterSections(List<CourseChapterSection> courseChapterSections) {
        this.courseChapterSections = courseChapterSections;
    }

    public String getRelationIds() {
        return relationIds;
    }

    public void setRelationIds(String relationIds) {
        this.relationIds = relationIds;
    }

    public String getTopicIds() {
        return topicIds;
    }

    public void setTopicIds(String topicIds) {
        this.topicIds = topicIds;
    }

    public String getTopicNames() {
		return topicNames;
	}

	public void setTopicNames(String topicNames) {
		this.topicNames = topicNames;
	}

	public String getAllCourseTime() {
        return allCourseTime;
    }

    public void setAllCourseTime(String allCourseTime) {
        this.allCourseTime = allCourseTime;
    }

    public Organization getOrganization() {
        return organization;
    }
    public void setOrganization(Organization organization) {
        this.organization = organization;
    }
    public Organization getReleaseOrg() {
        return releaseOrg;
    }
    public void setReleaseOrg(Organization releaseOrg) {
        this.releaseOrg = releaseOrg;
    }
    public Organization getSponsoringOrganization() {
        return sponsoringOrganization;
    }

    public void setSponsoringOrganization(Organization sponsoringOrganization) {
        this.sponsoringOrganization = sponsoringOrganization;
    }

    public Member getReleaseUser() {
        return releaseUser;
    }
    public void setReleaseUser(Member releaseUser) {
        this.releaseUser = releaseUser;
    }
    public Member getDevelopUser() {
        return developUser;
    }
    public void setDevelopUser(Member developUser) {
        this.developUser = developUser;
    }
    public Member getCreateUser() {
        return createUser;
    }
    public void setCreateUser(Member createUser) {
        this.createUser = createUser;
    }
    public CourseCategory getCategory() {
        return category;
    }
    public void setCategory(CourseCategory category) {
        this.category = category;
    }
    public List<CourseChapter> getCourseChapters() {
        return courseChapters;
    }
    public void setCourseChapters(List<CourseChapter> courseChapters) {
        this.courseChapters = courseChapters;
    }
    public List<CourseAttachment> getCourseAttachments() {
        return courseAttachments;
    }
    public void setCourseAttachments(List<CourseAttachment> courseAttachments) {
        this.courseAttachments = courseAttachments;
    }
    public List<AudienceItem> getAudienceItems() {
        return audienceItems;
    }
    public void setAudienceItems(List<AudienceItem> audienceItems) {
        this.audienceItems = audienceItems;
    }
    public List<AudienceItem> getAudienceOrgs() {
        return audienceOrgs;
    }
    public void setAudienceOrgs(List<AudienceItem> audienceOrgs) {
        this.audienceOrgs = audienceOrgs;
    }

    public List<CoursePhoto> getPhotos() {
        return photos;
    }

    public void setPhotos(List<CoursePhoto> photos) {
        this.photos = photos;
    }

    public List<CourseNote> getCourseNotes() {
        return courseNotes;
    }
    public void setCourseNotes(List<CourseNote> courseNotes) {
        this.courseNotes = courseNotes;
    }
    public CourseScore getCourseScore() {
        return courseScore;
    }
    public void setCourseScore(CourseScore courseScore) {
        this.courseScore = courseScore;
    }
    public boolean isRegister() {
        return isRegister;
    }
    public void setRegister(boolean isRegister) {
        this.isRegister = isRegister;
    }
    public CourseStudyProgress getStudyProgress() {
        return studyProgress;
    }
    public void setStudyProgress(CourseStudyProgress studyProgress) {
        this.studyProgress = studyProgress;
    }
    public String getCollectId() {
        return collectId;
    }
    public void setCollectId(String collectId) {
        this.collectId = collectId;
    }
    public boolean isOwned() {
        return isOwned;
    }
    public void setOwned(boolean isOwned) {
        this.isOwned = isOwned;
    }

    public List<BusinessTopic> getBusinessTopics() {
        return businessTopics;
    }

    public void setBusinessTopics(List<BusinessTopic> businessTopics) {
        this.businessTopics = businessTopics;
    }

    public Integer getFinishStatus() {
        return finishStatus;
    }

    public void setFinishStatus(Integer finishStatus) {
        this.finishStatus = finishStatus;
    }

    public List<SubjectAdvertising> getAdvertisings() {
        return advertisings;
    }

    public void setAdvertisings(List<SubjectAdvertising> advertisings) {
        this.advertisings = advertisings;
    }

    public List<SubjectTextArea> getTextAreas() {
        return textAreas;
    }

    public void setTextAreas(List<SubjectTextArea> textAreas) {
        this.textAreas = textAreas;
    }

    public GenseeWebCast getRelativeGensee() {
        return relativeGensee;
    }

    public void setRelativeGensee(GenseeWebCast relativeGensee) {
        this.relativeGensee = relativeGensee;
    }

    public int getTypeByBusiness(){
        if(this.getBusinessType()!=null && this.getBusinessType().equals(BUSINESS_TYPE_SUBJECT)){
            return BUSINESS_TYPE_SUBJECT;
        }
        return BUSINESS_TYPE_COURSE;
    }

//    public String getCertificateId() {
//        return certificateId;
//    }
//
//    public void setCertificateId(String certificateId) {
//        this.certificateId = certificateId;
//    }

    public Integer getIsCertificateRecord() {
        return isCertificateRecord;
    }

    public void setIsCertificateRecord(Integer isCertificateRecord) {
        this.isCertificateRecord = isCertificateRecord;
    }
    public Integer getCompletedSectionCount() {
        return completedSectionCount;
    }

    public void setCompletedSectionCount(Integer completedSectionCount) {
        this.completedSectionCount = completedSectionCount;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }
    public Integer getTotalSectionCount() {
        return totalSectionCount;
    }

    public void setTotalSectionCount(Integer totalSectionCount) {
        this.totalSectionCount = totalSectionCount;
    }

    public List<CourseCategory> getDeputyCategories() {
        return deputyCategories;
    }

    public void setDeputyCategories(List<CourseCategory> deputyCategories) {
        this.deputyCategories = deputyCategories;
    }

    public Boolean getStudyPlanAdded() {
        return studyPlanAdded;
    }

    public void setStudyPlanAdded(Boolean studyPlanAdded) {
        this.studyPlanAdded = studyPlanAdded;
    }

    public Integer getCourseType() {
        return courseType;
    }

    public void setCourseType(Integer courseType) {
        this.courseType = courseType;
    }

    public CourseRedShipAuditDetail getCourseRedShipAuditDetail() {
        return courseRedShipAuditDetail;
    }

    public void setCourseRedShipAuditDetail(CourseRedShipAuditDetail courseRedShipAuditDetail) {
        this.courseRedShipAuditDetail = courseRedShipAuditDetail;
    }

    public Caption getCaption() {
        return caption;
    }

    public void setCaption(Caption caption) {
        this.caption = caption;
    }

    public Boolean getExistedManager() {
        return existedManager;
    }

    public void setExistedManager(Boolean existedManager) {
        this.existedManager = existedManager;
    }

    public List<SubjectTopicManager> getSubjectTopicManager() {
        return subjectTopicManager;
    }

    public void setSubjectTopicManager(List<SubjectTopicManager> subjectTopicManager) {
        this.subjectTopicManager = subjectTopicManager;
    }

    public void setDisplay(Boolean display) {
        this.display = display;
    }

    public Boolean getDisplay() {
        return display;
    }

    public Boolean getCoursewareNoteFlag() {
        return coursewareNoteFlag;
    }

    public void setCoursewareNoteFlag(Boolean coursewareNoteFlag) {
        this.coursewareNoteFlag = coursewareNoteFlag;
    }
}
