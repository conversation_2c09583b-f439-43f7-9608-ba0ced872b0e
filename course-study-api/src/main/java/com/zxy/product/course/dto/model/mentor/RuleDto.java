package com.zxy.product.course.dto.model.mentor;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/14:18
 * @Description:
 */
public class RuleDto implements Serializable {
    private static final long serialVersionUID = -4125554845443027202L;

    private List<PreProcessingRuleDto> pre_processing_rules;
    private SegmentationDto segmentation;

    public List<PreProcessingRuleDto> getPre_processing_rules() {
        return pre_processing_rules;
    }

    public void setPre_processing_rules(List<PreProcessingRuleDto> pre_processing_rules) {
        this.pre_processing_rules = pre_processing_rules;
    }

    public SegmentationDto getSegmentation() {
        return segmentation;
    }

    public void setSegmentation(SegmentationDto segmentation) {
        this.segmentation = segmentation;
    }
}
