/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseQuestionRecommend;
import com.zxy.product.course.jooq.tables.interfaces.ICourseQuestionRecommend;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 推荐问题管理表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseQuestionRecommendRecord extends UpdatableRecordImpl<CourseQuestionRecommendRecord> implements Record10<String, String, String, String, String, Integer, Integer, Integer, Long, Long>, ICourseQuestionRecommend {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_question</code>. 问题
     */
    @Override
    public void setQuestion(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_question</code>. 问题
     */
    @Override
    public String getQuestion() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_answer</code>. 答案
     */
    @Override
    public void setAnswer(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_answer</code>. 答案
     */
    @Override
    public String getAnswer() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_question_third_id</code>. 第三方问题ID
     */
    @Override
    public void setQuestionThirdId(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_question_third_id</code>. 第三方问题ID
     */
    @Override
    public String getQuestionThirdId() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_answer_text</code>. 答案文本
     */
    @Override
    public void setAnswerText(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_answer_text</code>. 答案文本
     */
    @Override
    public String getAnswerText() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_recommend</code>. 是否为推荐问题 0 否 1 是
     */
    @Override
    public void setRecommend(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_recommend</code>. 是否为推荐问题 0 否 1 是
     */
    @Override
    public Integer getRecommend() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_status</code>. 状态 0 未发布 1 已发布
     */
    @Override
    public void setStatus(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_status</code>. 状态 0 未发布 1 已发布
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_sort</code>. 排序
     */
    @Override
    public void setSort(Integer value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_sort</code>. 排序
     */
    @Override
    public Integer getSort() {
        return (Integer) get(7);
    }

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_modify_date</code>. 更新时间
     */
    @Override
    public void setModifyDate(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_modify_date</code>. 更新时间
     */
    @Override
    public Long getModifyDate() {
        return (Long) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, String, String, Integer, Integer, Integer, Long, Long> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, String, String, Integer, Integer, Integer, Long, Long> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.QUESTION;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ANSWER;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.QUESTION_THIRD_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ANSWER_TEXT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.RECOMMEND;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.STATUS;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field8() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.SORT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getQuestion();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getAnswer();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getQuestionThirdId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getAnswerText();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getRecommend();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getStatus();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value8() {
        return getSort();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value2(String value) {
        setQuestion(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value3(String value) {
        setAnswer(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value4(String value) {
        setQuestionThirdId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value5(String value) {
        setAnswerText(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value6(Integer value) {
        setRecommend(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value7(Integer value) {
        setStatus(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value8(Integer value) {
        setSort(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value9(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord value10(Long value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommendRecord values(String value1, String value2, String value3, String value4, String value5, Integer value6, Integer value7, Integer value8, Long value9, Long value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseQuestionRecommend from) {
        setId(from.getId());
        setQuestion(from.getQuestion());
        setAnswer(from.getAnswer());
        setQuestionThirdId(from.getQuestionThirdId());
        setAnswerText(from.getAnswerText());
        setRecommend(from.getRecommend());
        setStatus(from.getStatus());
        setSort(from.getSort());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseQuestionRecommend> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseQuestionRecommendRecord
     */
    public CourseQuestionRecommendRecord() {
        super(CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND);
    }

    /**
     * Create a detached, initialised CourseQuestionRecommendRecord
     */
    public CourseQuestionRecommendRecord(String id, String question, String answer, String questionThirdId, String answerText, Integer recommend, Integer status, Integer sort, Long createTime, Long modifyDate) {
        super(CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND);

        set(0, id);
        set(1, question);
        set(2, answer);
        set(3, questionThirdId);
        set(4, answerText);
        set(5, recommend);
        set(6, status);
        set(7, sort);
        set(8, createTime);
        set(9, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseQuestionRecommendEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseQuestionRecommendEntity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseQuestionRecommendEntity)source;
        pojo.into(this);
        return true;
    }
}
