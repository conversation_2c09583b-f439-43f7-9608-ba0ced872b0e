/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;
import java.sql.Timestamp;


/**
 * 子认证-维度导入表-临时表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISubAuthenticatedTmp extends Serializable {

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_id</code>. id
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_id</code>. id
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_name</code>. 姓名
     */
    public void setName(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_name</code>. 姓名
     */
    public String getName();

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_code</code>. 编号
     */
    public void setCode(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_code</code>. 编号
     */
    public String getCode();

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_file_id</code>. 文件id
     */
    public void setFileId(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_file_id</code>. 文件id
     */
    public String getFileId();

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_sub_authenticated_id</code>. 子认证id
     */
    public void setSubAuthenticatedId(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_sub_authenticated_id</code>. 子认证id
     */
    public String getSubAuthenticatedId();

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_delete_flag</code>. 是否删除 0 否 1是
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_delete_flag</code>. 是否删除 0 否 1是
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_creator</code>. 创建人
     */
    public void setCreator(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_creator</code>. 创建人
     */
    public String getCreator();

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_sub_authenticated_tmp.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_tmp.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISubAuthenticatedTmp
     */
    public void from(ISubAuthenticatedTmp from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISubAuthenticatedTmp
     */
    public <E extends ISubAuthenticatedTmp> E into(E into);
}
