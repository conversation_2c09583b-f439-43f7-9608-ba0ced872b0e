/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseStudyProgressFj extends Serializable {

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_member_id</code>.
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_member_id</code>.
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_course_id</code>.
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_course_id</code>.
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_begin_time</code>.
     */
    public void setBeginTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_begin_time</code>.
     */
    public Long getBeginTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_type</code>.
     */
    public void setType(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_type</code>.
     */
    public Integer getType();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_is_required</code>.
     */
    public void setIsRequired(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_is_required</code>.
     */
    public Integer getIsRequired();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_finish_status</code>.
     */
    public void setFinishStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_finish_status</code>.
     */
    public Integer getFinishStatus();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_finish_time</code>.
     */
    public void setFinishTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_finish_time</code>.
     */
    public Long getFinishTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_study_total_time</code>.
     */
    public void setStudyTotalTime(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_study_total_time</code>.
     */
    public Integer getStudyTotalTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_register_time</code>.
     */
    public void setRegisterTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_register_time</code>.
     */
    public Long getRegisterTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_last_access_time</code>.
     */
    public void setLastAccessTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_last_access_time</code>.
     */
    public Long getLastAccessTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.t_course_version_id</code>.
     */
    public void setCourseVersionId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.t_course_version_id</code>.
     */
    public String getCourseVersionId();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_mark_member_id</code>.
     */
    public void setMarkMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_mark_member_id</code>.
     */
    public String getMarkMemberId();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_mark_time</code>.
     */
    public void setMarkTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_mark_time</code>.
     */
    public Long getMarkTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_completed_rate</code>.
     */
    public void setCompletedRate(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_completed_rate</code>.
     */
    public Integer getCompletedRate();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_current_chapter_id</code>.
     */
    public void setCurrentChapterId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_current_chapter_id</code>.
     */
    public String getCurrentChapterId();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_current_section_id</code>.
     */
    public void setCurrentSectionId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_current_section_id</code>.
     */
    public String getCurrentSectionId();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_push_id</code>.
     */
    public void setPushId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_push_id</code>.
     */
    public String getPushId();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_visits</code>.
     */
    public void setVisits(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_visits</code>.
     */
    public Integer getVisits();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_last_modify_time</code>.
     */
    public void setLastModifyTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_last_modify_time</code>.
     */
    public Long getLastModifyTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_subject_finish_time</code>. 专题必修课完成时间
     */
    public void setSubjectFinishTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_subject_finish_time</code>. 专题必修课完成时间
     */
    public Long getSubjectFinishTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_completion_times</code>. 完成次数
     */
    public void setCompletionTimes(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_completion_times</code>. 完成次数
     */
    public Integer getCompletionTimes();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_latest_completion_time</code>. 最新完成时间
     */
    public void setLatestCompletionTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_latest_completion_time</code>. 最新完成时间
     */
    public Long getLatestCompletionTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_study_hours_record_time</code>. 集中学时记录时间
     */
    public void setStudyHoursRecordTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_study_hours_record_time</code>. 集中学时记录时间
     */
    public Long getStudyHoursRecordTime();

    /**
     * Setter for <code>course-study.t_course_study_progress_fj.f_concentrate_study_hours</code>. 集中学时
     */
    public void setConcentrateStudyHours(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress_fj.f_concentrate_study_hours</code>. 集中学时
     */
    public Integer getConcentrateStudyHours();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseStudyProgressFj
     */
    public void from(com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProgressFj from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseStudyProgressFj
     */
    public <E extends com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProgressFj> E into(E into);
}
