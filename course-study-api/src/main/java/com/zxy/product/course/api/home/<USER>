package com.zxy.product.course.api.home;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.vo.ClassifiedCourseVo;
import com.zxy.product.course.vo.CustomizeParentVO;
import com.zxy.product.course.vo.SpecialVO;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 十万——首页学习模块聚合Service
 * <AUTHOR>
 * @date 2024年11月08日 15:07
 */
@RemoteService
public interface CoursePolymerService {

    /**
     * 扩展查询资源图片地址（课程|专题）
     * @param map 课程|专题图片地址Map
     * @param clientType 客户端类型 1PC 2App
     * @return 课程|专题的find-by-ids接口数据
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<? extends CustomizeParentVO> doCustomizeCourse(Map<String, SpecialVO> map, Integer clientType);

    /**
     * 扩展查询资源图片地址（直播）
     * @param map  直播图片地址Map
     * @param clientType 客户端类型 1PC 2App
     * @return 直播的find-by-ids接口数据
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<? extends CustomizeParentVO> doCustomizeBroadcast(Map<String, SpecialVO>map, Integer clientType);

    /**
     * 扩展查询资源Ext（课程|专题）
     * @param selectType 查询类型 1全部字数简介 2固定字数
     * @param selectIndex 查询索引 1纯文字 2富文本 3PC简介 4App简介
     * @param sizeOpt 若selectType为2时，此字段必填，若无，默认10
     * @param customizeIdCollect 需要查询的自定义资源属性集合
     * @return 填充资源Ext（简介）
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String,String> extendCourseExt(Integer selectType, Integer selectIndex, Optional<Integer> sizeOpt, List<String> customizeIdCollect);

    /**
     * 扩展查询资源Ext（直播）
     * @param selectType 查询类型 1全部字数简介 2固定字数
     * @param selectIndex 查询索引 1纯文字 2富文本 3PC简介 4App简介
     * @param sizeOpt 若selectType为2时，此字段必填，若无，默认10
     * @param customizeIdCollect 需要查询的自定义资源属性集合
     * @return 填充资源Ext（简介）
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    Map<String,String> extendBroadcastExt(Integer selectType, Integer selectIndex,Optional<Integer> sizeOpt, List<String> customizeIdCollect);


    /**
     * 分类课程查询
     * @param ids 分类课程ID集合
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    List<ClassifiedCourseVo> doClassifiedCoursesCourse(List<String> ids);
}
