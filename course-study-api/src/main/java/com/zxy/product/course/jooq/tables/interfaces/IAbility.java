/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;
import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 能力信息表
 */
@Generated(
        value = {
                "http://www.jooq.org",
                "jOOQ version:3.12.4"
        },
        comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IAbility extends Serializable {

    /**
     * Setter for <code>course-study.t_ability.f_id</code>. ID
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_ability.f_id</code>. ID
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_ability.f_level</code>. 能力级别
     */
    public void setLevel(Integer value);

    /**
     * Getter for <code>course-study.t_ability.f_level</code>. 能力级别
     */
    public Integer getLevel();

    /**
     * Setter for <code>course-study.t_ability.f_name</code>. 能力名称
     */
    public void setName(String value);

    /**
     * Getter for <code>course-study.t_ability.f_name</code>. 能力名称
     */
    public String getName();

    /**
     * Setter for <code>course-study.t_ability.f_code</code>. 能力编码
     */
    public void setCode(String value);

    /**
     * Getter for <code>course-study.t_ability.f_code</code>. 能力编码
     */
    public String getCode();

    /**
     * Setter for <code>course-study.t_ability.f_description</code>. 描述
     */
    public void setDescription(String value);

    /**
     * Getter for <code>course-study.t_ability.f_description</code>. 描述
     */
    public String getDescription();

    /**
     * Setter for <code>course-study.t_ability.f_status</code>. 能力状态 0:未发布,1:发布 2:取消发布
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>course-study.t_ability.f_status</code>. 能力状态 0:未发布,1:发布 2:取消发布
     */
    public Integer getStatus();

    /**
     * Setter for <code>course-study.t_ability.f_organization_id</code>. 部门id
     */
    public void setOrganizationId(String value);

    /**
     * Getter for <code>course-study.t_ability.f_organization_id</code>. 部门id
     */
    public String getOrganizationId();

    /**
     * Setter for <code>course-study.t_ability.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_ability.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_ability.f_modify_date</code>. 修改时间
     */
    public void setModifyDate(Timestamp value);

    /**
     * Getter for <code>course-study.t_ability.f_modify_date</code>. 修改时间
     */
    public Timestamp getModifyDate();

    /**
     * Setter for <code>course-study.t_ability.f_type</code>. 来源， 0：能力管理 1：学习地图
     */
    public void setType(Integer value);

    /**
     * Getter for <code>course-study.t_ability.f_type</code>. 来源， 0：能力管理 1：学习地图
     */
    public Integer getType();

    /**
     * Setter for <code>course-study.t_ability.f_ability_category</code>. 能力管理大类
     */
    public void setAbilityCategory(Integer value);

    /**
     * Getter for <code>course-study.t_ability.f_ability_category</code>. 能力管理大类
     */
    public Integer getAbilityCategory();

    /**
     * Setter for <code>course-study.t_ability.f_ability_sub_category</code>. 能力管理子类
     */
    public void setAbilitySubCategory(String value);

    /**
     * Getter for <code>course-study.t_ability.f_ability_sub_category</code>. 能力管理子类
     */
    public String getAbilitySubCategory();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IAbility
     */
    public void from(com.zxy.product.course.jooq.tables.interfaces.IAbility from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IAbility
     */
    public <E extends com.zxy.product.course.jooq.tables.interfaces.IAbility> E into(E into);
}
