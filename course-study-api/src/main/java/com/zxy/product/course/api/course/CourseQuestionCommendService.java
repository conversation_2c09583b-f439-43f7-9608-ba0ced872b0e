package com.zxy.product.course.api.course;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.CourseQuestionRecommend;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/18:02
 * @Description:
 */
@RemoteService
public interface CourseQuestionCommendService {

    /**
     * 添加推荐*
     * @param recommend
     */
    @Transactional
    void insert(CourseQuestionRecommend recommend);

    /**
     * 更新*
     * @param recommend
     */
    @Transactional
    void update(CourseQuestionRecommend recommend);

    /**
     * 获取推荐详细信息*
     * @param id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    CourseQuestionRecommend get(String id);

    /**
     * 调整排序*
     * @param id
     * @param order
     */
    @Transactional
    void sort(String id, Integer order);

    /**
     * 更新是否推荐*
     * @param id
     * @param recommend
     */
    @Transactional
    void updateRecommend(String id, Integer recommend);

    /**
     * 查询推荐列表*
     * @param page
     * @param pageSize
     * @param question
     * @param recommend
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    PagedResult<CourseQuestionRecommend> findPage(Integer page, Integer pageSize, Optional<String> question, Optional<Integer> recommend);
}
