package com.zxy.product.course.dto.model.mentor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/14:16
 * @Description:
 */
public class FileRuleDto implements Serializable {

    private static final long serialVersionUID = 7281567136332167838L;
    private String doc_form;
    private String indexing_technique;
    private Map<String, Object> process_rule;
    private String original_document_id;

    public String getOriginal_document_id() {
        return original_document_id;
    }

    public void setOriginal_document_id(String original_document_id) {
        this.original_document_id = original_document_id;
    }

    public String getDoc_form() {
        return doc_form;
    }

    public void setDoc_form(String doc_form) {
        this.doc_form = doc_form;
    }

    public String getIndexing_technique() {
        return indexing_technique;
    }

    public void setIndexing_technique(String indexing_technique) {
        this.indexing_technique = indexing_technique;
    }

    public Map<String, Object> getProcess_rule() {
        return process_rule;
    }

    public void setProcess_rule(Map<String, Object> process_rule) {
        this.process_rule = process_rule;
    }
}
