/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.ChatTimeRecord;
import com.zxy.product.course.jooq.tables.interfaces.IChatTimeRecord;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record5;
import org.jooq.Row5;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 回答问题时间差
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ChatTimeRecordRecord extends UpdatableRecordImpl<ChatTimeRecordRecord> implements Record5<String, String, Long, Long, Long>, IChatTimeRecord {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_chat_time_record.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_chat_time_record.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_chat_time_record.f_answer_id</code>. 回答id，第三方主键ID
     */
    @Override
    public void setAnswerId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_chat_time_record.f_answer_id</code>. 回答id，第三方主键ID
     */
    @Override
    public String getAnswerId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_chat_time_record.f_time</code>. 思考时间 时间毫秒
     */
    @Override
    public void setTime(Long value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_chat_time_record.f_time</code>. 思考时间 时间毫秒
     */
    @Override
    public Long getTime() {
        return (Long) get(2);
    }

    /**
     * Setter for <code>course-study.t_chat_time_record.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_chat_time_record.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>course-study.t_chat_time_record.f_modify_date</code>. 更新时间
     */
    @Override
    public void setModifyDate(Long value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_chat_time_record.f_modify_date</code>. 更新时间
     */
    @Override
    public Long getModifyDate() {
        return (Long) get(4);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record5 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, Long, Long, Long> fieldsRow() {
        return (Row5) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row5<String, String, Long, Long, Long> valuesRow() {
        return (Row5) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return ChatTimeRecord.CHAT_TIME_RECORD.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return ChatTimeRecord.CHAT_TIME_RECORD.ANSWER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field3() {
        return ChatTimeRecord.CHAT_TIME_RECORD.TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field4() {
        return ChatTimeRecord.CHAT_TIME_RECORD.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field5() {
        return ChatTimeRecord.CHAT_TIME_RECORD.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getAnswerId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value3() {
        return getTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value4() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value5() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChatTimeRecordRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChatTimeRecordRecord value2(String value) {
        setAnswerId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChatTimeRecordRecord value3(Long value) {
        setTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChatTimeRecordRecord value4(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChatTimeRecordRecord value5(Long value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChatTimeRecordRecord values(String value1, String value2, Long value3, Long value4, Long value5) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(IChatTimeRecord from) {
        setId(from.getId());
        setAnswerId(from.getAnswerId());
        setTime(from.getTime());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends IChatTimeRecord> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached ChatTimeRecordRecord
     */
    public ChatTimeRecordRecord() {
        super(ChatTimeRecord.CHAT_TIME_RECORD);
    }

    /**
     * Create a detached, initialised ChatTimeRecordRecord
     */
    public ChatTimeRecordRecord(String id, String answerId, Long time, Long createTime, Long modifyDate) {
        super(ChatTimeRecord.CHAT_TIME_RECORD);

        set(0, id);
        set(1, answerId);
        set(2, time);
        set(3, createTime);
        set(4, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.ChatTimeRecordEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.ChatTimeRecordEntity pojo = (com.zxy.product.course.jooq.tables.pojos.ChatTimeRecordEntity)source;
        pojo.into(this);
        return true;
    }
}
