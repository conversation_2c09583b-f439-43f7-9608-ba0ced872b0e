/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseKnowledgeRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 学习助手知识表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseKnowledge extends TableImpl<CourseKnowledgeRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_knowledge</code>
     */
    public static final CourseKnowledge COURSE_KNOWLEDGE = new CourseKnowledge();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseKnowledgeRecord> getRecordType() {
        return CourseKnowledgeRecord.class;
    }

    /**
     * The column <code>course-study.t_course_knowledge.f_id</code>.
     */
    public final TableField<CourseKnowledgeRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_knowledge.f_name</code>. 知识名称
     */
    public final TableField<CourseKnowledgeRecord, String> NAME = createField("f_name", org.jooq.impl.SQLDataType.VARCHAR.length(200).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "知识名称");

    /**
     * The column <code>course-study.t_course_knowledge.f_attachment_id</code>. 附件id
     */
    public final TableField<CourseKnowledgeRecord, String> ATTACHMENT_ID = createField("f_attachment_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "附件id");

    /**
     * The column <code>course-study.t_course_knowledge.f_attachment_name</code>. 附件名称
     */
    public final TableField<CourseKnowledgeRecord, String> ATTACHMENT_NAME = createField("f_attachment_name", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "附件名称");

    /**
     * The column <code>course-study.t_course_knowledge.f_attachment_type</code>. 0 word 1 pdf
     */
    public final TableField<CourseKnowledgeRecord, Integer> ATTACHMENT_TYPE = createField("f_attachment_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "0 word 1 pdf");

    /**
     * The column <code>course-study.t_course_knowledge.f_attachment_up_id</code>. 上传到成研的附件id
     */
    public final TableField<CourseKnowledgeRecord, String> ATTACHMENT_UP_ID = createField("f_attachment_up_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "上传到成研的附件id");

    /**
     * The column <code>course-study.t_course_knowledge.f_attachment_batch</code>. 上传到成研的附件批次号
     */
    public final TableField<CourseKnowledgeRecord, String> ATTACHMENT_BATCH = createField("f_attachment_batch", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "上传到成研的附件批次号");

    /**
     * The column <code>course-study.t_course_knowledge.f_status</code>. 状态 0 未发布 1 已发布
     */
    public final TableField<CourseKnowledgeRecord, Integer> STATUS = createField("f_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "状态 0 未发布 1 已发布");

    /**
     * The column <code>course-study.t_course_knowledge.f_delete</code>. 是否删除 0 未删除 1 已删除
     */
    public final TableField<CourseKnowledgeRecord, Integer> DELETE = createField("f_delete", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否删除 0 未删除 1 已删除");

    /**
     * The column <code>course-study.t_course_knowledge.f_finsh_status</code>. 向量化状态 0 未完成 1已完成
     */
    public final TableField<CourseKnowledgeRecord, Integer> FINSH_STATUS = createField("f_finsh_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "向量化状态 0 未完成 1已完成");

    /**
     * The column <code>course-study.t_course_knowledge.f_knowledge</code>. 知识简介
     */
    public final TableField<CourseKnowledgeRecord, String> KNOWLEDGE = createField("f_knowledge", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "知识简介");

    /**
     * The column <code>course-study.t_course_knowledge.f_knowledge_text</code>. 知识简介文本
     */
    public final TableField<CourseKnowledgeRecord, String> KNOWLEDGE_TEXT = createField("f_knowledge_text", org.jooq.impl.SQLDataType.VARCHAR.length(1000).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "知识简介文本");

    /**
     * The column <code>course-study.t_course_knowledge.f_shelve_time</code>. 首次发布时间
     */
    public final TableField<CourseKnowledgeRecord, Long> SHELVE_TIME = createField("f_shelve_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "首次发布时间");

    /**
     * The column <code>course-study.t_course_knowledge.f_create_time</code>. 创建时间
     */
    public final TableField<CourseKnowledgeRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_course_knowledge.f_modify_date</code>. 更新时间
     */
    public final TableField<CourseKnowledgeRecord, Long> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "更新时间");

    /**
     * Create a <code>course-study.t_course_knowledge</code> table reference
     */
    public CourseKnowledge() {
        this("t_course_knowledge", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_knowledge</code> table reference
     */
    public CourseKnowledge(String alias) {
        this(alias, COURSE_KNOWLEDGE);
    }

    private CourseKnowledge(String alias, Table<CourseKnowledgeRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseKnowledge(String alias, Table<CourseKnowledgeRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "学习助手知识表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseKnowledgeRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_KNOWLEDGE_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseKnowledgeRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseKnowledgeRecord>>asList(Keys.KEY_T_COURSE_KNOWLEDGE_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseKnowledge as(String alias) {
        return new CourseKnowledge(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseKnowledge rename(String name) {
        return new CourseKnowledge(name, null);
    }
}
