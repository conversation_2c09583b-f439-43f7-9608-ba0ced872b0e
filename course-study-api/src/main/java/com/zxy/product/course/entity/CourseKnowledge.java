package com.zxy.product.course.entity;


import com.zxy.product.course.jooq.tables.pojos.CourseKnowledgeEntity;



/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/10:36
 * @Description:
 */
public class CourseKnowledge extends CourseKnowledgeEntity {
    private static final long serialVersionUID = -126537821850684509L;


    public static Integer DELETE_YES =1;
    public static Integer DELETE_NO =0;

    public static Integer STATUS_UNPUBLISH =0;
    public static Integer STATUS_PUBLISH =1;

    public static Integer FEEDBACK_STATUS_UNPUBLISH =0;
    public static Integer FEEDBACK_STATUS_PUBLISH =1;


    public static String INDEXING_STATUS_WAITING ="waiting";
    public static String INDEXING_STATUS_COMPLETED ="completed";
}
