package com.zxy.product.course.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.CourseChapterSection;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

@RemoteService
public interface CourseChapterInfoService {
    /**
     * ①若该专题内包含子专题，则展示子专题封面、子专题名称。不展示除子专题外的其他资源；
     * ②若该专题内不包含子专题，则展示专题内包含的其他资源（课程、知识、作业、考试、URL、调研、评估、直播、短视频入口）。其中课程资源展示封面、标题、时长，其他资源展示封面、标题；若资源无封面则展示缺省封面。
     *
     */
    Map<String, List<CourseChapterSection>> findBySubjectId(List<String> subjectIds);

    List<String> initHomeCertifyCacheBySubjectId(List<String> subjectIds);

    /**
     * 查询专题下，所有课程名
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseChapterSection> getCourseNames(String courseId);


    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseChapterSection> getRequiredCourseByCourseId(List<String> courseIds);
}
