/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 推荐问题管理表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseQuestionRecommend extends Serializable {

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_question</code>. 问题
     */
    public void setQuestion(String value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_question</code>. 问题
     */
    public String getQuestion();

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_answer</code>. 答案
     */
    public void setAnswer(String value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_answer</code>. 答案
     */
    public String getAnswer();

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_question_third_id</code>. 第三方问题ID
     */
    public void setQuestionThirdId(String value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_question_third_id</code>. 第三方问题ID
     */
    public String getQuestionThirdId();

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_answer_text</code>. 答案文本
     */
    public void setAnswerText(String value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_answer_text</code>. 答案文本
     */
    public String getAnswerText();

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_recommend</code>. 是否为推荐问题 0 否 1 是
     */
    public void setRecommend(Integer value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_recommend</code>. 是否为推荐问题 0 否 1 是
     */
    public Integer getRecommend();

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_status</code>. 状态 0 未发布 1 已发布
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_status</code>. 状态 0 未发布 1 已发布
     */
    public Integer getStatus();

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_sort</code>. 排序
     */
    public void setSort(Integer value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_sort</code>. 排序
     */
    public Integer getSort();

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_question_recommend.f_modify_date</code>. 更新时间
     */
    public void setModifyDate(Long value);

    /**
     * Getter for <code>course-study.t_course_question_recommend.f_modify_date</code>. 更新时间
     */
    public Long getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseQuestionRecommend
     */
    public void from(ICourseQuestionRecommend from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseQuestionRecommend
     */
    public <E extends ICourseQuestionRecommend> E into(E into);
}
