/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseFeedback;

import javax.annotation.Generated;


/**
 * 意见反馈
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseFeedbackEntity extends BaseEntity implements ICourseFeedback {

    private static final long serialVersionUID = 1L;

    private String questionId;
    private String questionContent;
    private String answerContent;
    private String feedbackContent;
    private Integer   feedbackType;
    private Integer   like;
    private String createMemberId;
    private Long   modifyDate;

    public CourseFeedbackEntity() {}

    public CourseFeedbackEntity(CourseFeedbackEntity value) {
        this.questionId = value.questionId;
        this.questionContent = value.questionContent;
        this.answerContent = value.answerContent;
        this.feedbackContent = value.feedbackContent;
        this.feedbackType = value.feedbackType;
        this.like = value.like;
        this.createMemberId = value.createMemberId;
        this.modifyDate = value.modifyDate;
    }

    public CourseFeedbackEntity(
        String id,
        String questionId,
        String questionContent,
        String answerContent,
        String feedbackContent,
        Integer   feedbackType,
        Integer   like,
        String createMemberId,
        Long   createTime,
        Long   modifyDate
    ) {
        super.setId(id);
        this.questionId = questionId;
        this.questionContent = questionContent;
        this.answerContent = answerContent;
        this.feedbackContent = feedbackContent;
        this.feedbackType = feedbackType;
        this.like = like;
        this.createMemberId = createMemberId;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getQuestionId() {
        return this.questionId;
    }

    @Override
    public void setQuestionId(String questionId) {
        this.questionId = questionId;
    }

    @Override
    public String getQuestionContent() {
        return this.questionContent;
    }

    @Override
    public void setQuestionContent(String questionContent) {
        this.questionContent = questionContent;
    }

    @Override
    public String getAnswerContent() {
        return this.answerContent;
    }

    @Override
    public void setAnswerContent(String answerContent) {
        this.answerContent = answerContent;
    }

    @Override
    public String getFeedbackContent() {
        return this.feedbackContent;
    }

    @Override
    public void setFeedbackContent(String feedbackContent) {
        this.feedbackContent = feedbackContent;
    }

    @Override
    public Integer getFeedbackType() {
        return this.feedbackType;
    }

    @Override
    public void setFeedbackType(Integer feedbackType) {
        this.feedbackType = feedbackType;
    }

    @Override
    public Integer getLike() {
        return this.like;
    }

    @Override
    public void setLike(Integer like) {
        this.like = like;
    }

    @Override
    public String getCreateMemberId() {
        return this.createMemberId;
    }

    @Override
    public void setCreateMemberId(String createMemberId) {
        this.createMemberId = createMemberId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Long modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseFeedbackEntity (");

        sb.append(getId());
        sb.append(", ").append(questionId);
        sb.append(", ").append(questionContent);
        sb.append(", ").append(answerContent);
        sb.append(", ").append(feedbackContent);
        sb.append(", ").append(feedbackType);
        sb.append(", ").append(like);
        sb.append(", ").append(createMemberId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseFeedback from) {
        setId(from.getId());
        setQuestionId(from.getQuestionId());
        setQuestionContent(from.getQuestionContent());
        setAnswerContent(from.getAnswerContent());
        setFeedbackContent(from.getFeedbackContent());
        setFeedbackType(from.getFeedbackType());
        setLike(from.getLike());
        setCreateMemberId(from.getCreateMemberId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseFeedback> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseFeedbackEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseFeedbackRecord r = new com.zxy.product.course.jooq.tables.records.CourseFeedbackRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.QUESTION_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.QUESTION_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.QUESTION_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.QUESTION_CONTENT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.QUESTION_CONTENT, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.QUESTION_CONTENT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.ANSWER_CONTENT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.ANSWER_CONTENT, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.ANSWER_CONTENT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.FEEDBACK_CONTENT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.FEEDBACK_CONTENT, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.FEEDBACK_CONTENT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.FEEDBACK_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.FEEDBACK_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.FEEDBACK_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.LIKE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.LIKE, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.LIKE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.CREATE_MEMBER_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.CREATE_MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.CREATE_MEMBER_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.MODIFY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK.MODIFY_DATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
