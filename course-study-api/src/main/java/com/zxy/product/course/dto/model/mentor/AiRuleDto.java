package com.zxy.product.course.dto.model.mentor;

import java.io.Serializable;
import java.util.List;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/06/18/19:46
 * @Description:
 */
public class AiRuleDto implements Serializable {

    private static final long serialVersionUID = 6712689040845728446L;
    private List<AiPreProcessingRule> pre_processing_rules;
    private AiSegmentationDto segmentation;

    public List<AiPreProcessingRule> getPre_processing_rules() {
        return pre_processing_rules;
    }

    public void setPre_processing_rules(List<AiPreProcessingRule> pre_processing_rules) {
        this.pre_processing_rules = pre_processing_rules;
    }

    public AiSegmentationDto getSegmentation() {
        return segmentation;
    }

    public void setSegmentation(AiSegmentationDto segmentation) {
        this.segmentation = segmentation;
    }
}
