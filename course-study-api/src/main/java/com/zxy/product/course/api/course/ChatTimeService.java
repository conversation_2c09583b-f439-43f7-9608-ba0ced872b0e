package com.zxy.product.course.api.course;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.ChatTimeRecord;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/29/17:08
 * @Description:
 */
@RemoteService
public interface ChatTimeService {

    /**
     * 添加时间差*
     * @param record
     */
    @Transactional
    void add(ChatTimeRecord record);

    /**
     * 查询时间差*
     * @param ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<String, Long> findMap(List<String> ids);
}
