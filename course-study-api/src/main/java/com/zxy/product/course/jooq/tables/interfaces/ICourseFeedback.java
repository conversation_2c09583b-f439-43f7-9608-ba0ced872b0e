/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 意见反馈
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseFeedback extends Serializable {

    /**
     * Setter for <code>course-study.t_course_feedback.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_feedback.f_question_id</code>. 问题id
     */
    public void setQuestionId(String value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_question_id</code>. 问题id
     */
    public String getQuestionId();

    /**
     * Setter for <code>course-study.t_course_feedback.f_question_content</code>. 问题内容
     */
    public void setQuestionContent(String value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_question_content</code>. 问题内容
     */
    public String getQuestionContent();

    /**
     * Setter for <code>course-study.t_course_feedback.f_answer_content</code>.
     */
    public void setAnswerContent(String value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_answer_content</code>.
     */
    public String getAnswerContent();

    /**
     * Setter for <code>course-study.t_course_feedback.f_feedback_content</code>. 反馈内容
     */
    public void setFeedbackContent(String value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_feedback_content</code>. 反馈内容
     */
    public String getFeedbackContent();

    /**
     * Setter for <code>course-study.t_course_feedback.f_feedback_type</code>. 反馈类型 0 内容不全面、1观点有错误、2其他
     */
    public void setFeedbackType(Integer value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_feedback_type</code>. 反馈类型 0 内容不全面、1观点有错误、2其他
     */
    public Integer getFeedbackType();

    /**
     * Setter for <code>course-study.t_course_feedback.f_like</code>. 0 点踩 1 点赞
     */
    public void setLike(Integer value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_like</code>. 0 点踩 1 点赞
     */
    public Integer getLike();

    /**
     * Setter for <code>course-study.t_course_feedback.f_create_member_id</code>. 创建人
     */
    public void setCreateMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_create_member_id</code>. 创建人
     */
    public String getCreateMemberId();

    /**
     * Setter for <code>course-study.t_course_feedback.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_feedback.f_modify_date</code>. 更新时间
     */
    public void setModifyDate(Long value);

    /**
     * Getter for <code>course-study.t_course_feedback.f_modify_date</code>. 更新时间
     */
    public Long getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseFeedback
     */
    public void from(ICourseFeedback from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseFeedback
     */
    public <E extends ICourseFeedback> E into(E into);
}
