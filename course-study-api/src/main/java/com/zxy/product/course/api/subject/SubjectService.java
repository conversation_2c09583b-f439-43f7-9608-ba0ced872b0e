package com.zxy.product.course.api.subject;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.*;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 *  专题基本信息api
 * Created by TJ on 2017/9/6.
 */
@RemoteService(timeout = 100000)
public interface SubjectService {
    String URI = "course-study/subject-info";
    /**
     * 后台查询专题列表
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> find(int pageNum,
                                 int pageSize,
                                 String memberId,
                                 Optional<String> name,
                                 Optional<String> organizationId,
                                 Optional<String> code,
                                 Optional<Integer> status,
                                 Optional<String> releaseUserId,
                                 Optional<Integer> publishClient,
                                 Optional<Integer> subjectType,
                                 Optional<Long> beginBeginDate,
                                 Optional<Long> beginEndDate,
                                 Optional<Long> endBeginDate,
                                 Optional<Long> endEndDate,
                                 Optional<Long> shelveBeginTime,
                                 Optional<Long> shelveEndTime,
                                 Optional<Integer> isParty,
                                 Optional<String[]> selectIds,
                                 List<String> organizationIds, Optional<Integer> businessType,
                                 Optional<Integer> subjectTypeSelector);


    /**
     * 根据id查询专题信息
     * @param id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    CourseInfo get(String id);

    @Transactional
    CourseInfo add(String name,
                   String createUserId,
                   int publishClient,
                   String organizationId,
                   Optional<Integer> publishType,
                   Optional<Long> beginDate,
                   Optional<Long> endDate,
                   Optional<String> desc,
                   Optional<String> descText,
                   Optional<String> releaseUser,
                   Optional<String> releaseOrg,
                   Optional<String> code,
                   Optional<Integer> studyDays,
                   Optional<String> cover,
                   Optional<String> coverPath,
                   Optional<String> coverMaterialId,
                   List<CourseChapter> courseChapters,
                   List<CourseAttachment> courseAttachments,
                   List<AudienceItem> audienceItems,
                   Optional<CourseShelves> courseShelves,
                   Optional<String> styles,
                   Optional<String> topicIds,
                   List<SubjectAdvertising> advertisingList,
                   List<CoursePhoto> photos,
                   List<SubjectTextArea> textAreas,
                   Optional<String> url,
                   Optional<Integer> shareSub,
                   Optional<Integer> addType,
                   Optional<Integer> isPublic, Optional<String> descriptionApp, Optional<String> certificateId, Optional<Integer> isSign,
                   List<SubjectTopicManager> managerItems,
                   Optional<Integer> explicitLearningStatus, Optional<Integer> businessType,
                   Optional<Integer> learnSequence,
                   Optional<Integer> learningSituation, List<StudyReportAnalysisManagers> studyReportAnalysisManagers,
                   Optional<String> sponsoringOrg,Optional<String> subjectMisCode,Optional<Integer> subjectType,
                   Optional<Long> certificateTime,Optional<Integer> certificateType,Optional<Integer>skinType,Optional<String> pcBanner,Optional<String> pcBannerPath);

    @Transactional
    CourseInfo update(String id,
                      String memberId,
                      int publishClient,
                      Optional<Integer> publishType,
                      Optional<String> name,
                      Optional<Long> beginDate,
                      Optional<Long> endDate,
                      Optional<String> desc,
                      Optional<String> descText,
                      Optional<String> releaseUser,
                      Optional<String> releaseOrg,
                      Optional<String> organizationId,
                      Optional<String> code,
                      Optional<Integer> learnSequence,
                      Optional<Integer> studyDays,
                      Optional<String> cover,
                      Optional<String> coverPath,
                      Optional<String> coverMaterialId,
                      Optional<Integer> shareSub,
                      List<CourseChapter> courseChapters,
                      List<CourseAttachment> courseAttachments,
                      List<AudienceItem> audienceItems,
                      Optional<CourseShelves> courseShelves,
                      Optional<String> styles,
                      Optional<String> topicIds,
                      List<SubjectAdvertising> advertisingList,
                      List<CoursePhoto> photos,
                      List<SubjectTextArea> textAreas,
                      Optional<String> url,
                      Optional<Integer> addType,
                      Optional<Integer> isPublic, Optional<String> descriptionApp, Optional<String> certificateId, Optional<Integer> isSign,
                      List<SubjectTopicManager> managerItems, Optional<Integer> explicitLearningStatus,
                      Optional<Integer> learningSituation, List<StudyReportAnalysisManagers> studyReportAnalysisManagers,
                      Optional<String> sponsoringOrg,Optional<String> subjectMisCode,Optional<Integer> subjectType,
                      Optional<Long> certificateTime, Optional<Integer> certificateType,Optional<Integer>skinType,Optional<String> pcBanner,Optional<String> pcBannerPath);

    /**
     * delete 删除
     *
     * @param id
     *            课程id
     * @param currentUserId
     * @return 删除记录数
     */
    @Transactional
    int delete(String id, String currentUserId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> findBasicByIds(List<String> ids);

    /**
     * 查询专题热门标签
     * keeley
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> hotTopicIds();


    /**
     * 专题选择器
     * @param page
     * @param pageSize
     * @param client
     * @param name
     * @param subjectType
     * @param isParty
     * @param grantOrganizationIds
     * @param parentIds
     * @param selectIds
     * @return
     */
	PagedResult<CourseInfo> findSelect(Integer page, Integer pageSize, Optional<Integer> client,
                                       Optional<String> name, Optional<Integer> subjectType, Optional<Integer> isParty, List<String> grantOrganizationIds,
                                       Optional<List<String>> parentIds, Optional<String[]> selectIds, Optional<Long> shelveBeginTime, Optional<Long> shelveEndTime, Optional<List<Integer>> courseStatus);

	/**
	 * 专题重新发布时修改对应章节的referenceId
	 * @param courseChapters
	 * @param i
	 */
	@Transactional
	void addReferenceId(List<CourseChapter> courseChapters, int i);


    /**
     * 专题套用专题选择器，不涉及权限
     * @param page
     * @param pageSize
     * @param client
     * @param name
     * @param subjectType
     * @param selectIds
     * @param shelveBeginTime
     * @param shelveEndTime
     * @param organizationId
     * @param courseStatus
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> findSelectorSubject(Integer page, Integer pageSize, Optional<Integer> client,
                                                Optional<String> name, Optional<Integer> subjectType,
                                                Optional<String[]> selectIds, Optional<Long> shelveBeginTime, Optional<Long> shelveEndTime, Optional<String> organizationId, Optional<List<Integer>> courseStatus);


    /**
     * 查询被引用的专题的id
     * @param subjectId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> findReferencedSubjectIds(Optional<String> subjectId);


    /**
     * 根据ids查询专题信息
     * @param ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> findBySubjectIds(List<String> ids);

    /**
     * 根据ids查询专题信息
     * @param subjectId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    String findSubjectIsReferenced(String subjectId);

    /**
     * 验证专题引用的专题章节中是否存在已引用过专题的专题
     * @param subjectIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Optional<String> findResourceIdsBySubjectIds(List<String> subjectIds);

    /**
     * 开放接口,对外专题详情接口
     * @param code  专题编码
     * @param sectionStartTime  章节创建时间条件-开始
     * @param sectionEndTime    章节创建时间条件-结束
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    CourseInfo findSubjectDetailByCode(String code, Optional<Long> sectionStartTime, Optional<Long> sectionEndTime);

    /**
     * 首页banner-查询用户对应权限的专题
     * @param courseIds
     * @param currentUserId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    String findMemberRole(List<String> courseIds, String currentUserId);

    @Transactional
    void notificationScoreSheet(String subjectId, String scoringId, String currentUserId, Optional<String> oldTopicId, Optional<String> newTopicId);


    /**
     * 虚拟空间-专题资源池列表
     *
     * @param page
     * @param pageSize
     * @param currentUserId
     * @param name
     * @param organizationId
     * @param status
     * @param shelveBeginDate
     * @param shelveEndDate
     * @param grantOrganizationIds
     * @param virtualSpacesId
     * @param virtualSpacesOrganizationId
     * @param virtualSpacesGrantOrganizationIds
     * @param spacesStatus
     * @param spacesStatusAddTo
     * @param virtualSpacesStatus
     * @param subjectType
     * @return
     */
    @Transactional(readOnly = true)
    PagedResult<CourseInfo> findSubjectVirtualSpace(Integer page, Integer pageSize, String currentUserId, Optional<String> name, Optional<String> organizationId, Optional<Integer> status, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, List<String> grantOrganizationIds, String virtualSpacesId, String virtualSpacesOrganizationId, List<String> virtualSpacesGrantOrganizationIds, List<String> spacesStatus, List<String> spacesStatusAddTo, Optional<Integer> virtualSpacesStatus, Optional<Integer> subjectType);

    /**
     * 查询上级分享的专题
     *
     * @param byVirtualSpacesId
     * @param page
     * @param pageSize
     * @param name
     * @param categoryId
     * @param shelveBeginDate
     * @param shelveEndDate
     * @param allCategory
     * @param businessType
     * @param virtualSpacesId
     * @param status
     * @param virtualSpacesOrganizationId
     * @param subjectType
     * @return
     */
    @Transactional(readOnly = true)
    PagedResult<CourseInfo> findSuperiorSubject(List<String> byVirtualSpacesId, Integer page, Integer pageSize, Optional<String> name, Optional<String> categoryId, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, Optional<Integer> allCategory, Integer businessType, String virtualSpacesId, Optional<Integer> status, String virtualSpacesOrganizationId, Optional<Integer> subjectType);

    /**
     * 查询禁用
     *
     * @param page
     * @param pageSize
     * @param currentUserId
     * @param name
     * @param organizationId
     * @param status
     * @param shelveBeginDate
     * @param shelveEndDate
     * @param virtualSpacesId
     * @param spacesStatus
     * @param virtualSpacesStatus
     * @param grantOrganizationIds
     * @param subjectType
     * @return
     */
    @Transactional(readOnly = true)
    PagedResult<CourseInfo> findSubjectVirtualSpaceForbidden(Integer page, Integer pageSize, String currentUserId, Optional<String> name, Optional<String> organizationId, Optional<Integer> status, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, String virtualSpacesId, List<String> spacesStatus, Optional<Integer> virtualSpacesStatus, List<String> grantOrganizationIds, Optional<Integer> subjectType);

    /**
     *校验是否满足图谱专题更新条件
     *
     * @param subjectId 课程Id
     * @return 若满足图谱课程更新条件，则返回当前Id集合
     */
    @Transactional(readOnly = true)
    List<String> checkSubjectSynchronous(String subjectId);

    /**
     * 示范培训添加专题时使用
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> pageListForAddPartyTraining(int pageNum,
                                 int pageSize,
                                 String memberId,
                                 Optional<String> name,
                                 Optional<String> organizationId,
                                 Optional<String> code,
                                 Optional<Integer> status,
                                 Optional<String> releaseUserId,
                                 Optional<Integer> publishClient,
                                 Optional<Integer> subjectType,
                                 Optional<Long> beginBeginDate,
                                 Optional<Long> beginEndDate,
                                 Optional<Long> endBeginDate,
                                 Optional<Long> endEndDate,
                                 Optional<Long> shelveBeginTime,
                                 Optional<Long> shelveEndTime,
                                 Optional<String[]> selectIds,
                                 List<String> organizationIds);

    /**
     * 重点专题添加专题时使用
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> pageListForAddPartyFocus(int pageNum,
                                                        int pageSize,
                                                        String memberId,
                                                        Optional<String> name,
                                                        Optional<String> organizationId,
                                                        Optional<String> code,
                                                        Optional<Integer> status,
                                                        Optional<String> releaseUserId,
                                                        Optional<Integer> publishClient,
                                                        Optional<Integer> subjectType,
                                                        Optional<Long> beginBeginDate,
                                                        Optional<Long> beginEndDate,
                                                        Optional<Long> endBeginDate,
                                                        Optional<Long> endEndDate,
                                                        Optional<Long> shelveBeginTime,
                                                        Optional<Long> shelveEndTime,
                                                        Optional<String[]> selectIds,
                                                        List<String> organizationIds);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> findSubjectPage(int pageNum,
                                 int pageSize,
                                 Optional<String> name,
                                 Optional<String> organizationId,
                                 Optional<Integer> status,
                                 Optional<Integer> publishClient,
                                 Optional<Long> shelveBeginTime,
                                 Optional<Long> shelveEndTime,
                                 List<String> organizationIds,
                                 String planId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> findRelatedSubjectPage(int pageNum,
                                 int pageSize,
                                 Optional<String> name,
                                 Optional<String> organizationId,
                                 Optional<String> code,
                                 Optional<Integer> status,
                                 Optional<Integer> publishClient,
                                 Optional<Long> beginBeginDate,
                                 Optional<Long> beginEndDate,
                                 Optional<Long> endBeginDate,
                                 Optional<Long> endEndDate,
                                 Optional<Long> shelveBeginTime,
                                 Optional<Long> shelveEndTime,
                                 String planId);

    Map<String, String> getSubjectText(List<String> subjectIds);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Boolean isNetworkSubject(String subjectId);

    List<CourseInfo> getDisableCourse(List<String> ids);
}
