/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IDigitalIntelligenceMentor;

import javax.annotation.Generated;


/**
 * 数智导师结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DigitalIntelligenceMentorEntity extends BaseEntity implements IDigitalIntelligenceMentor {

    private static final long serialVersionUID = 1L;

    private String  memberId;
    private String  userQuery;
    private String  botResponse;

    public DigitalIntelligenceMentorEntity() {}

    public DigitalIntelligenceMentorEntity(IDigitalIntelligenceMentor value) {
        this.memberId = value.getMemberId();
        this.userQuery = value.getUserQuery();
        this.botResponse = value.getBotResponse();
    }

    public DigitalIntelligenceMentorEntity(
        String  id,
        String  memberId,
        String  userQuery,
        String  botResponse,
        Long    createTime
    ) {
        super.setId(id);
        this.memberId = memberId;
        this.userQuery = userQuery;
        this.botResponse = botResponse;
        super.setCreateTime(createTime);
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getMemberId() {
        return this.memberId;
    }

    @Override
    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    @Override
    public String getUserQuery() {
        return this.userQuery;
    }

    @Override
    public void setUserQuery(String userQuery) {
        this.userQuery = userQuery;
    }

    @Override
    public String getBotResponse() {
        return this.botResponse;
    }

    @Override
    public void setBotResponse(String botResponse) {
        this.botResponse = botResponse;
    }



    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("DigitalIntelligenceMentorEntity (");

        sb.append(getId());
        sb.append(", ").append(memberId);
        sb.append(", ").append(userQuery);
        sb.append(", ").append(botResponse);
        sb.append(", ").append(getCreateTime());

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IDigitalIntelligenceMentor from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setUserQuery(from.getUserQuery());
        setBotResponse(from.getBotResponse());
        setCreateTime(from.getCreateTime());
    }

    @Override
    public <E extends IDigitalIntelligenceMentor> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends DigitalIntelligenceMentorEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.DigitalIntelligenceMentorRecord r = new com.zxy.product.course.jooq.tables.records.DigitalIntelligenceMentorRecord();
                org.jooq.Row row = record.fieldsRow();
                    if(row.indexOf(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.ID, record.getValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID, record.getValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.USER_QUERY) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.USER_QUERY, record.getValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.USER_QUERY));
                    }
                    if(row.indexOf(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.BOT_RESPONSE) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.BOT_RESPONSE, record.getValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.BOT_RESPONSE));
                    }

                    if(row.indexOf(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.CREATE_TIME) > -1){
                        r.setValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR.CREATE_TIME));
                    }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
