package com.zxy.product.course.api.course;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.*;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created by keeley on 2017/9/8.
 */
@RemoteService(timeout = 50000)
public interface CourseInfoAdminService {
    @Transactional
    int update(CourseInfo courseInfo,Integer mentorState);
    @Transactional
    int update(CourseInfo courseInfo,
               List<CourseChapter> courseChapters,
               List<CourseAttachment> courseAttachments,
               List<AudienceItem> audienceItems,
               CourseShelves courseShelves,
               String [] topicIds,
               List<String> deputyCategoryIds);
    @Transactional
    CourseInfo insert(CourseInfo courseInfo);
    @Transactional
    CourseInfo insert(CourseInfo courseInfo,
               List<CourseChapter> courseChapters,
               List<CourseAttachment> courseAttachments,
               List<AudienceItem> audienceItems,
               CourseShelves courseShelves,
               String [] topicIds,
               List<String> deputyCategoryIds);
    @Transactional
    void shelves(CourseInfo courseInfo, CourseShelves courseShelves);
    @Transactional
    void theShelves(String courseId);
    @Transactional
    void deleteChapter(String courseId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    CourseInfo get(String id);
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> find(Integer page, Integer pageSize, String currentUserId, List<String> grantOrganizationIds, int businessTypeCourse, Optional<String> name, Optional<String> organizationId, Optional<String> categoryId, Optional<String> code, Optional<Integer> status, Optional<Integer> source, Optional<String> releaseUserId, Optional<Integer> publishClient, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, Optional<Long> releaseBeginDate, Optional<Long> releaseEndDate, Optional<Integer> isParty, Optional<Integer> allCategory, Optional<Integer> redAuditStatus, Optional<Integer> checkAuditStatus, Optional<Integer> captionOverallStatus,Optional<Integer> mentorState);
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> findSelect(Integer page, Integer pageSize, Optional<Integer> client, Optional<String> name, Optional<String> category, Optional<Integer> isParty, List<String> grantOrganizationIds, Optional<List<String>> parentIds, Optional<String[]> selectIds, Optional<List<Integer>> courseStatus,Optional<List<Integer>> captionOverallStatus);
    
    /**
     * 课程选择（与findSelect同名方法不同的是不用organization_id in 的方式查询，采用关联查询）
     * @param page
     * @param pageSize
     * @param client
     * @param name
     * @param category
     * @param isParty
     * @param grantOrganizationIds
     * @param parentIds
     * @param selectIds
     * @param courseStatus
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> findSelectNormal(Integer page, Integer pageSize, Optional<Integer> client, Optional<String> name, Optional<String> category, Optional<Integer> isParty, List<String> grantOrganizationIds, Optional<List<String>> parentIds, Optional<String[]> selectIds, Optional<List<Integer>> courseStatus,Optional<List<Integer>> captionOverallStatus);

    /**
     * 关联直播
     * @param id 课程id
     * @param relativeGenseeId 直播id
     * @param currentMemberId 当前用户id
     * @return 课程
     */
    @Transactional
    CourseInfo relateGensee(String id, String relativeGenseeId, String currentMemberId);

    /**
     * 取消关联直播
     * @param id 课程id
     * @param currentMemberId 当前用户id
     * @return 课程
     */
    @Transactional
    CourseInfo unrelateGensee(String id, String currentMemberId);

    /**
     * 查找已被关联的直播
     * @return 直播ids
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> findRelatedGenseeIds();

    /**
     * 根据课程id查询课程的状态
     * @param selectIds
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> findStatus(Optional<String[]> selectIds);

    /**
     * 讨论区总开关
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    Integer updateSwitchHide(Integer hide, Optional<Integer> history);

    /**
     * 讨论区单个课程开关
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    CourseInfo updateSwitchHideOne(String id, Integer hide, Optional<Integer> history);


    /**
     * 查询单个课程的讨论区and历史讨论记录
     * @param id
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    CourseInfo getSwitchHideAndHistoryHide(String id);

    /**
     * 查询，用于标签管理-课程标签管理
     * @param page
     * @param pageSize
     * @param currentUserId
     * @param businessTypeCourse
     * @param name
     * @param organizationId
     * @param categoryId
     * @param publishClient
     * @param shelveBeginDate
     * @param isParty
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> findBy(Integer page, Integer pageSize, List<String> grantOrganizationIds, String currentUserId, int businessTypeCourse, Optional<String> name, Optional<String> categoryId, Optional<Integer> publishClient, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, Optional<Integer> isParty);


    /**
     * @param page
     * @param pageSize
     * @param currentUserId
     * @param grantOrganizationIds
     * @param businessType
     * @param name
     * @param organizationId
     * @param categoryId
     * @param status
     * @param shelveBeginDate
     * @param shelveEndDate
     * @param virtualSpacesId
     * @param virtualSpacesOrganizationId
     * @param id
     * @param byVirtualSpacesId
     * @param virtualSpacesStatus
     * @param spacesStatusAddTo
     * @return
     */
    @Transactional(readOnly = true)
    PagedResult<CourseInfo> findCourseVirtualSpace(Integer page, Integer pageSize, String currentUserId, List<String> grantOrganizationIds, Integer businessType, Optional<String> name, Optional<String> organizationId, Optional<String> categoryId, Optional<Integer> status, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, String virtualSpacesId, List<String> virtualSpacesOrganizationId, Optional<String> id, List<String> byVirtualSpacesId, Optional<Integer> virtualSpacesStatus, List<String> spacesStatusAddTo);

    @Transactional(readOnly = true)
    PagedResult<CourseInfo> findSuperiorCourse(List<String> byVirtualSpacesId, Integer page, Integer pageSize, Optional<String> name, Optional<String> categoryId, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, Integer businessType, String virtualSpacesId, Optional<Integer> status, String virtualSpacesOrganizationId, Optional<Integer> source);

    /**
     * 查询禁用的课程
     *
     * @param page
     * @param pageSize
     * @param currentUserId
     * @param businessType
     * @param name
     * @param organizationId
     * @param categoryId
     * @param status
     * @param shelveBeginDate
     * @param shelveEndDate
     * @param virtualSpacesId
     * @param spacesStatus
     * @param virtualSpacesStatus
     * @param grantOrganizationIds
     * @return
     */
    @Transactional(readOnly = true)
    PagedResult<CourseInfo> findCourseVirtualSpaceForbidden(Integer page, Integer pageSize, String currentUserId, Integer businessType, Optional<String> name, Optional<String> organizationId, Optional<String> categoryId, Optional<Integer> status, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, String virtualSpacesId, List<String> spacesStatus, Optional<Integer> virtualSpacesStatus, List<String> grantOrganizationIds);

    @Transactional
    void updateConstructionType(String courseId, int constructionTypeYes, List<AudienceItem> audienceItems);
}
