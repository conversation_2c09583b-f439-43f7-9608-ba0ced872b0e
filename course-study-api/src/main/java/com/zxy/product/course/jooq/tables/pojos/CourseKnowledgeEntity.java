/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseKnowledge;

import javax.annotation.Generated;


/**
 * 学习助手知识表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseKnowledgeEntity extends BaseEntity implements ICourseKnowledge {

    private static final long serialVersionUID = 1L;

    private String name;
    private String attachmentId;
    private String attachmentName;
    private Integer   attachmentType;
    private String attachmentUpId;
    private String attachmentBatch;
    private Integer   status;
    private Integer   delete;
    private Integer   finshStatus;
    private String knowledge;
    private String knowledgeText;
    private Long   shelveTime;
    private Long   modifyDate;

    public CourseKnowledgeEntity() {}

    public CourseKnowledgeEntity(CourseKnowledgeEntity value) {
        this.name = value.name;
        this.attachmentId = value.attachmentId;
        this.attachmentName = value.attachmentName;
        this.attachmentType = value.attachmentType;
        this.attachmentUpId = value.attachmentUpId;
        this.attachmentBatch = value.attachmentBatch;
        this.status = value.status;
        this.delete = value.delete;
        this.finshStatus = value.finshStatus;
        this.knowledge = value.knowledge;
        this.knowledgeText = value.knowledgeText;
        this.shelveTime = value.shelveTime;
        this.modifyDate = value.modifyDate;
    }

    public CourseKnowledgeEntity(
        String id,
        String name,
        String attachmentId,
        String attachmentName,
        Integer   attachmentType,
        String attachmentUpId,
        String attachmentBatch,
        Integer   status,
        Integer   delete,
        Integer   finshStatus,
        String knowledge,
        String knowledgeText,
        Long   shelveTime,
        Long   createTime,
        Long   modifyDate
    ) {
        super.setId(id);
        this.name = name;
        this.attachmentId = attachmentId;
        this.attachmentName = attachmentName;
        this.attachmentType = attachmentType;
        this.attachmentUpId = attachmentUpId;
        this.attachmentBatch = attachmentBatch;
        this.status = status;
        this.delete = delete;
        this.finshStatus = finshStatus;
        this.knowledge = knowledge;
        this.knowledgeText = knowledgeText;
        this.shelveTime = shelveTime;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getAttachmentId() {
        return this.attachmentId;
    }

    @Override
    public void setAttachmentId(String attachmentId) {
        this.attachmentId = attachmentId;
    }

    @Override
    public String getAttachmentName() {
        return this.attachmentName;
    }

    @Override
    public void setAttachmentName(String attachmentName) {
        this.attachmentName = attachmentName;
    }

    @Override
    public Integer getAttachmentType() {
        return this.attachmentType;
    }

    @Override
    public void setAttachmentType(Integer attachmentType) {
        this.attachmentType = attachmentType;
    }

    @Override
    public String getAttachmentUpId() {
        return this.attachmentUpId;
    }

    @Override
    public void setAttachmentUpId(String attachmentUpId) {
        this.attachmentUpId = attachmentUpId;
    }

    @Override
    public String getAttachmentBatch() {
        return this.attachmentBatch;
    }

    @Override
    public void setAttachmentBatch(String attachmentBatch) {
        this.attachmentBatch = attachmentBatch;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getDelete() {
        return this.delete;
    }

    @Override
    public void setDelete(Integer delete) {
        this.delete = delete;
    }

    @Override
    public Integer getFinshStatus() {
        return this.finshStatus;
    }

    @Override
    public void setFinshStatus(Integer finshStatus) {
        this.finshStatus = finshStatus;
    }

    @Override
    public String getKnowledge() {
        return this.knowledge;
    }

    @Override
    public void setKnowledge(String knowledge) {
        this.knowledge = knowledge;
    }

    @Override
    public String getKnowledgeText() {
        return this.knowledgeText;
    }

    @Override
    public void setKnowledgeText(String knowledgeText) {
        this.knowledgeText = knowledgeText;
    }

    @Override
    public Long getShelveTime() {
        return this.shelveTime;
    }

    @Override
    public void setShelveTime(Long shelveTime) {
        this.shelveTime = shelveTime;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Long modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseKnowledgeEntity (");

        sb.append(getId());
        sb.append(", ").append(name);
        sb.append(", ").append(attachmentId);
        sb.append(", ").append(attachmentName);
        sb.append(", ").append(attachmentType);
        sb.append(", ").append(attachmentUpId);
        sb.append(", ").append(attachmentBatch);
        sb.append(", ").append(status);
        sb.append(", ").append(delete);
        sb.append(", ").append(finshStatus);
        sb.append(", ").append(knowledge);
        sb.append(", ").append(knowledgeText);
        sb.append(", ").append(shelveTime);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseKnowledge from) {
        setId(from.getId());
        setName(from.getName());
        setAttachmentId(from.getAttachmentId());
        setAttachmentName(from.getAttachmentName());
        setAttachmentType(from.getAttachmentType());
        setAttachmentUpId(from.getAttachmentUpId());
        setAttachmentBatch(from.getAttachmentBatch());
        setStatus(from.getStatus());
        setDelete(from.getDelete());
        setFinshStatus(from.getFinshStatus());
        setKnowledge(from.getKnowledge());
        setKnowledgeText(from.getKnowledgeText());
        setShelveTime(from.getShelveTime());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseKnowledge> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseKnowledgeEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseKnowledgeRecord r = new com.zxy.product.course.jooq.tables.records.CourseKnowledgeRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.NAME, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_NAME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_NAME, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_NAME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_TYPE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_TYPE, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_TYPE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_UP_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_UP_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_UP_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_BATCH.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_BATCH, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.ATTACHMENT_BATCH));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.DELETE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.DELETE, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.DELETE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.FINSH_STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.FINSH_STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.FINSH_STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.KNOWLEDGE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.KNOWLEDGE, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.KNOWLEDGE));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.KNOWLEDGE_TEXT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.KNOWLEDGE_TEXT, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.KNOWLEDGE_TEXT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.SHELVE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.SHELVE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.SHELVE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.MODIFY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE.MODIFY_DATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
