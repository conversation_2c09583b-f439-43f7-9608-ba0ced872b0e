/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseFeedbackRecord;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 意见反馈
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseFeedback extends TableImpl<CourseFeedbackRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_feedback</code>
     */
    public static final CourseFeedback COURSE_FEEDBACK = new CourseFeedback();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseFeedbackRecord> getRecordType() {
        return CourseFeedbackRecord.class;
    }

    /**
     * The column <code>course-study.t_course_feedback.f_id</code>.
     */
    public final TableField<CourseFeedbackRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_feedback.f_question_id</code>. 问题id
     */
    public final TableField<CourseFeedbackRecord, String> QUESTION_ID = createField("f_question_id", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "问题id");

    /**
     * The column <code>course-study.t_course_feedback.f_question_content</code>. 问题内容
     */
    public final TableField<CourseFeedbackRecord, String> QUESTION_CONTENT = createField("f_question_content", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "问题内容");

    /**
     * The column <code>course-study.t_course_feedback.f_answer_content</code>.
     */
    public final TableField<CourseFeedbackRecord, String> ANSWER_CONTENT = createField("f_answer_content", org.jooq.impl.SQLDataType.VARCHAR.length(500).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_feedback.f_feedback_content</code>. 反馈内容
     */
    public final TableField<CourseFeedbackRecord, String> FEEDBACK_CONTENT = createField("f_feedback_content", org.jooq.impl.SQLDataType.VARCHAR.length(300).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "反馈内容");

    /**
     * The column <code>course-study.t_course_feedback.f_feedback_type</code>. 反馈类型 0 内容不全面、1观点有错误、2其他
     */
    public final TableField<CourseFeedbackRecord, Integer> FEEDBACK_TYPE = createField("f_feedback_type", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "反馈类型 0 内容不全面、1观点有错误、2其他");

    /**
     * The column <code>course-study.t_course_feedback.f_like</code>. 0 点踩 1 点赞
     */
    public final TableField<CourseFeedbackRecord, Integer> LIKE = createField("f_like", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("NULL", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "0 点踩 1 点赞");

    /**
     * The column <code>course-study.t_course_feedback.f_create_member_id</code>. 创建人
     */
    public final TableField<CourseFeedbackRecord, String> CREATE_MEMBER_ID = createField("f_create_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "创建人");

    /**
     * The column <code>course-study.t_course_feedback.f_create_time</code>. 创建时间
     */
    public final TableField<CourseFeedbackRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_course_feedback.f_modify_date</code>. 更新时间
     */
    public final TableField<CourseFeedbackRecord, Long> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "更新时间");

    /**
     * Create a <code>course-study.t_course_feedback</code> table reference
     */
    public CourseFeedback() {
        this("t_course_feedback", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_feedback</code> table reference
     */
    public CourseFeedback(String alias) {
        this(alias, COURSE_FEEDBACK);
    }

    private CourseFeedback(String alias, Table<CourseFeedbackRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseFeedback(String alias, Table<CourseFeedbackRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "意见反馈");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseFeedbackRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_FEEDBACK_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseFeedbackRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseFeedbackRecord>>asList(Keys.KEY_T_COURSE_FEEDBACK_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedback as(String alias) {
        return new CourseFeedback(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseFeedback rename(String name) {
        return new CourseFeedback(name, null);
    }
}
