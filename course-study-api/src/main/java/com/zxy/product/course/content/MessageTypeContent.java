package com.zxy.product.course.content;

/**
 * @user tianjun 消息常量
 */
public class MessageTypeContent {

    /**
     * 人资模块(HR) 1 课程模块(COURSE) 2 system 3 exam 4 course...  8 log
     *
     * 01 courseinfo
     * 02 subject
     * 03 task
     * 04 study-push
     * 05 course-manager
     * 06 audience-item
     * 07 course-version
     * 08 course-process
     * 09 knowledge
     * 10 topic
     * 11 study-progress
     * 15 course-category
     * insert 01 delete 02 update 03
     */

    // 积分
    public static final int SYSTEM_SCORE_RESULT_CHANGE = 14004;  // 积分规则中不需要判断次数
    public static final int SYSTEM_SCORE_RESULT_CHANGE_OF_NUM = 14005;  // 积分规则中需要判断次数
    public static final int SYSTEM_SOCRE_RESULT_CHANGE_FOR_CONSUMER = 14006; // 直接消费积分

    // 课程笔记
    public static final int COURSE_NOTE_INSERT = 40701;
    public static final int COURSE_NOTE_UPDATE = 40702;
    public static final int COURSE_NOTE_DELETE = 40703;

    //课程版本 传递的id是课程id
    public static final int COURSE_VERSIN_CHANGE = 40701;
    public static final int COURSE_STUDY_PROGRESS_VERSION_CHANGE = 40704;

    //受众项
    public static final int AUDIENCE_ITEM_INSERT = 40601;
    public static final int AUDIENCE_ITEM_DELETE = 40602;
    public static final int AUDIENCE_ITEM_UPDATE = 40603;

    /**
     * 传 AUDIENCE_ITEM.id
     *  MessageHeaderContent.ID
     */
    public static final int AUDIENCE_ITEM_K_INSERT = 40604;
    /**
     * 传 1 受众项id 2 受众项类型，3 名称
     * MessageHeaderContent.ID
     * MessageHeaderContent.TYPE
     * MessageHeaderContent.NAME
     */
    public static final int AUDIENCE_ITEM_K_UPDATE = 40605;

    // 标记完成
    public static final int COURSE_MANAGER_STUDY_MARK_FINASH = 40501;

    // 删除
    public static final int COURSE_MANAGER_STUDY_DELETE = 40503;
    // 催学
    public static final int COURSE_MANAGER_STUDY_URGE= 40504;
    // 审核
    public static final int COURSE_STUDY_AUDIT = 40505;
    // 课程学习进度手工添加
    public static final int COURSE_MANAGER_STUDY_ADD = 40508;
    //课程上架
    public static final int COURSE_SHELVES = 40506;
    //课程下架
    public static final int COURSE_THE_SHELVES = 40507;

    public static final int SUBJECT_DISAPPEAR = 40509;

    public static final int COURSE_DISAPPEAR = 40509;
    public static final int STUDIO_COURSE_OFF_SHELVES = 40510;


    //推送
    public static final int COURSE_STUDY_PUSH_INSERT = 40401;
    public static final int COURSE_STUDY_PUSH_DELETE = 40402;
    public static final int COURSE_STUDY_PUSH_UPDATE = 40403;
    public static final int COURSE_STUDY_PUSH_PUBLISH = 40404;
    public static final int COURSE_STUDY_PUSH_NOTIC = 40405;

    // 学习专题
    public static final int COURSE_SUBJECT_TASK_UPDATE = 40301;
    public static final int COURSE_SUBJECT_TASK_DELETE = 40302;
    public static final int STUDY_TASK_SUBMIT_AUDIT = 40303;
    public static final int SUBJECT_KNOWLEDGE_UPDATE = 40304;

    // 专题
    public static final int COURSE_SUBJECT_INSERT = 40201;
    public static final int COURSE_SUBJECT_DELETE = 40202;
    public static final int COURSE_SUBJECT_UPDATE = 40203;
    //专题班
    public static final int THEMATIC_CLASS_COURSE = 40251;//课程完成后专题班判断
    public static final int THEMATIC_CLASS_REGISTER = 40252;//专题班注册成功后发送消息
    public static final int THEMATIC_CLASS_EXCELLENT = 40253;//专题班获得优秀证书后发送消息


    /**专题发布**/
    public static final int COURSE_SUBJECT_PUBLISH = 40204;
    /**专题下架**/
    public static final int COURSE_SUBJECT_CANCEL = 40205;
    /**
     * 通过课程学习更新专题的时候发的消息
     */
    public static final int SUBJECT_PROGRESS_UPDATE_NEW = 40206;
    public static final int SUBJECT_PROGRESS_UPDATE_SUB_NEW = 40207;
    // 课程
    public static final int COURSE_INFO_INSERT = 40101;
    public static final int COURSE_INFO_DELETE = 40102;
    public static final int COURSE_INFO_UPDATE = 40103;
    public static final int COURSE_STATISTICS_VISITS = 40105; // 浏览数累计
    public static final int COURSE_STATISTICS_STUDY_COUNT = 40106; // 学习人数累计。
    public static final int COURSE_STATISTICS_REGISTER_COUNT = 40107; // 注册人数累计
    public static final int COURSE_STATISTICS_SCORE = 40108; // 评分累计
    public static final int COURSE_STATISTICS_SHARE = 40109; // 分享累计
    public static final int COURSE_STATISTICS_COLLECTION = 40110; // 收藏累计

    // 课程进度更新
    public static final int COURSE_PROGRESS_UPDATE = 40801; // 需要异步实现更新所有包含该课程章节的专题的进度
    public static final int COURSE_PROGRESS_UPDATE_NEW = 40802; // 需要异步实现更新所有包含该课程章节的专题的进度
    public static final int COURSE_STUDY_PROGRESS_UPDATE = 40803; // 更新学习进度
    public static final int COURSE_BATCH_STUDY = 40804; // 批量处理学习进度
    public static final int COURSE_SCORM_UNZIP = 40805;    // 课程scorm附件处理
    public static final int COURSE_STUDY_PROGRESS_STUDY_PUSH = 40806; // 更新学习进度——学习推送
    public static final int SPLIT_COURSE_SECTION_STUDY_LOG_INSERT = 40807;  // 分表,新增课程log
    public static final int SPLIT_COURSE_SECTION_STUDY_LOG_UPDATE = 40808;  // 分表,更新课程log
    public static final int SPLIT_SUBJECT_SECTION_STUDY_LOG_INSERT = 40809;	// 分表,新增专题log
    public static final int SPLIT_SUBJECT_SECTION_STUDY_LOG_UPDATE = 40810;	// 分表,更新专题log
    public static final int COURSE_PROGRESS_UPDATE_NEW_STUDYTIME = 40811; // 需要异步实现更新课程时长

    // 知识
    public static final int KNOWLEDGE_INFO_INSERT = 40901;
    public static final int KNOWLEDGE_INFO_DELETE = 40902;
    public static final int KNOWLEDGE_INFO_UPDATE = 40903;
    public static final int KNOWLEDGE_INFO_SYNC = 40904;
    public static final int KNOWLEDGE_BROWSE_COUNT = 40905;  //统计浏览次数
    public static final int KNOWLEDGE_INFO_SYNC_DELETE = 40906;// 附件或课件删除知识时知识使用量更新
    public static final int KNOWLEDGE_INFO_UNPUBLISH=40907;//知识下架

    // 直播
    public static final int GESEE_PUBLISH = 41001; // 发布直播信息
    public static final int GESEE_CANCEL = 41002; // 取消发布直播信息
    public static final int GESEE_STATUS_UPDATE = 41003; // 直播状态更新
    public static final int GESEE_INSERT = 41004; // 直播增
    public static final int GESEE_UPDATE = 41005; // 直播改
    public static final int GESEE_DELETE = 41006; // 直播删
    public static final int GESEE_SUB = 41007; // 直播报名（预约）
    public static final int GESEE_SUB_CANCEL = 41008; // 直播取消报名（预约）
    public static final int GESEE_ACCESS = 41009; // 直播进行中，观看直播
    public static final int GESEE_HISTORY = 41010; // 直播历史记录
    public static final int GENSEE_START = 41011; // 直播开始
    public static final int CLOUD_CENTER_GESEE_INSERT = 50801;//云中心创建直播

    //咪咕直播
    public static final int MIGU_PUBLISH = 41021; // 发布直播信息
    public static final int MIGU_CANCEL = 41022; // 取消发布直播信息
    public static final int MIGU_STATUS_UPDATE = 41023; // 直播状态更新
    public static final int MIGU_INSERT = 41024; // 直播增
    public static final int MIGU_UPDATE = 41025; // 直播改
    public static final int MIGU_DELETE = 41026; // 直播删
    public static final int MIGU_SCORE_UPDATE = 41027; // 直播评分修改
    public static final int MIGU_SUB = 41028; // 直播订阅
    public static final int MIGU_SUB_CANCEL = 41029; // 直播取消订阅
    public static final int MIGU_TIME_UPDATE = 41030; // 直播时间修改

    public static final int STUDY_PLAN_ADD_NUM_CALCULATE =41012;//学习计划已添加数据统计
    public static final int STUDY_PLAN_CHALLENGE_INITIALIZE =41013;//学习计划竞赛初始化监听
    public static final int STUDY_PLAN_FINISH_RATE =41015;//学习计划完成率更新监听

    // 课程放弃
    public static final int COURSE_STUDY_PROGRESS_GIVE_UP = 41101;
    // 专题放弃
    public static final int SUBJECT_STUDY_PROGRESS_GIVE_UP = 41102;

    // MOOC
    public static final int MOOC_PUBLISH = 41201; // 发布MOOC信息
    public static final int MOOC_CANCEL = 41202; // 取消发布MOOC信息

    // 话题、标签
    public static final int TOPIC_INSERT = 41301; // 新增话题、标签
    public static final int TOPIC_DELETE = 41302; // 删除话题、标签
    // 更新标签点击次数（2020-09-18）
    public static final int TOPIC_COURSE_VISITS_UPDATE = 41303;
    // 数据手动回源
    public static final int HAND_BACK = 41304;

    public static final int NOTICE_INSERT = 41401; // 通知新增
    public static final int NOTICE_PARAMS_INSERT = 41402; // 通知新增-直接根据模板和参数发送消息
    public static final int YSX_NOTICE_PARAMS_INSERT = 41403; // 通知新增-直接根据模板和参数发送消息
    public static final int GB_NOTICE_PARAMS_INSERT = 41404; // 高标党建-通知新增-直接根据模板和参数发送消息
    public static final int GB_SATISFIED_NOTICE_PARAMS_INSERT = 41405; // 高标党建-通知新增-直接根据模板和参数发送消息

    public static final int COURSE_CATEGORY_INSTALL = 41501;
    public static final int COURSE_CATEGORY_DELETE = 41502;
    public static final int COURSE_CATEGORY_UPDATE = 41503;

    // 内训师投票
    public static final int COMPETE_COURSE_VOTE_INSTER = 41601;

    // 异步更新人-课-天
    public static final int COURSE_STUDY_LOG_DAY_SPLIT_TIME_UPDATE = 41701;
    // 进入专题时异步更新专题中引用的已学习过的课程的进度
    public static final int SUBJECT_SECTION_STUDY_PROGRESS_ENDTER = 41702;
    // 保存学习中出现的异常日志
    public static final int COURSE_EXCEPTION_INSTER = 41703;

    public static final int SUBJECT_REFERENCE_SUBJECT_PROGRESS_UPDATE = 41801;

    // 专题新增log单独发消息新增
    public static final int SUBJECT_INSERT_SECTION_STUDY_LOG_AND_DAY = 41901;

    // 发消息更新专题的完成状态
    public static final int SUBJECT_SECTION_PROGRESS_FINISH_STATUS_UPDATE = 41802;


    public static final int COURSE_RETREAT=40510;//退库
    public static final int COURSE_SHELVES_PARALLER=40511;//人工智能 --课程上架

    public static final int COURSE_SCORE_UPDATE=42001;//课程专题评分更新
    public static final int GESEE_SCORE_UPDATE=42002;//直播评分更新
    public static final int KNOWLEDGE_SCORE_UPDATE=42003;//知识评分更新

    public static final int ISSUE_CERTIFICATE_RECORD_BATCH = 43001;// 批量发证书专题颁发证书使用
    public static final int ISSUE_CERTIFICATE_RECORD = 43002;// 单独发证书专题颁发证书使用
    public static final int ISSUE_CERTIFICATE_RECORD_CHBN = 43003;// 新动能CHBN发放证书监听
    public static final int ISSUE_CERTIFICATE_RECORD_STANDARD = 43004; // 新动能CHBN学员已达标，待生成证书
    public static final int ISSUE_CERTIFICATE_RECORD_MANUAL = 43009; //手动发证书
    public static final int ISSUE_CERTIFICATE_RECORD_FIX = 43010; //手动发证书
    // 课程防刷课
    public static final int COURSE_MULTIPLE_CLIENT_STUDY = 43803;

    public static final int REMODELING_TRAIN_PLAN_STUDENT_SING_UP = 45001;// 重塑培训，学员报名学习专题
    public static final int REMODELING_TRAIN_ACQUIRE_CERTIFICATE_BATCH = 45002; // 多个学员同时获得证书时
    /**
     * 重塑专区外部平台数据汇总到网大专区内
     */
    public static final int REMODELING_EXTERNAL_COURSE_STUDY_DATA_INSERT = 45003;
    public static final int REMODELING_EXTERNAL_EXAM_DATA_INSERT = 45004;

    public static final int SPLIT_COURSE_STUDY_PROGRESS_INSERT = 46001;// 进度表新增更新分表
    public static final int SPLIT_COURSE_STUDY_PROGRESS_UPDATE = 46002;// 进度表修改更新分表

    public static final int FTP_UPLOAD_COURSE_STUDY_DATA_INCREMENT = 47001;//增量上传学习数据LOG
    public static final int FTP_UPLOAD_COURSE_LOG_DATA_INCREMENT = 47002;//增量上传学习数据COURSE_DAY
    public static final int FTP_UPLOAD_COURSE_PROGRESS_DATA_INCREMENT = 47003;//增量上传学习数据PROGRESS
    public static final int FTP_UPLOAD_SUBJECT_LOG_DATA_INCREMENT = 47004;//增量上传学习数据SUBJECT_DAY
    public static final int FTP_UPLOAD_COURSE_SCORE_DATA_INCREMENT = 47005;//增量上传学习数据SCORE
    public static final int FTP_UPLOAD_COURSE_INFO_DATA_ALL = 47006;//全量同步数据INFO
    public static final int FTP_UPLOAD_COURSE_PROGRESS_DATA_ALL = 47007;// 全量同步数据PROGRESS
    public static final int FTP_UPLOAD_COURSE_SCORE_DATA_ALL = 47008;// 全量同步数据SCORE
    public static final int FTP_UPLOAD_USER_DATA_ALL = 47009;// 全量同步数据USER
    public static final int FTP_UPLOAD_COURSE_LOG_DATA_ALL = 47010;//全量同步数据COURSE_DAY
    public static final int FTP_UPLOAD_SUBJECT_LOG_DATA_ALL = 47011;//全量同步数据SUBJECT_LOG
    public static final int FTP_UPLOAD_COURSE_STUDY_DATA_ALL = 47012;//全量同步数据SUBJECT_LOG
    public static final int FTP_UPLOAD_USER_DATA_ALL_COMMUNITY = 47013;// 全量同步数据USER-党建云
    public static final int FTP_UPLOAD_COURSE_INFO_DATA_ALL_COMMUNITY = 47014;//全量同步数据INFO-党建云
    public static final int FTP_UPLOAD_COURSE_LOG_DATA_ALL_COMMUNITY = 47015;//全量同步数据COURSE_DAY-党建云
    public static final int FTP_UPLOAD_COURSE_LOG_DATA_INCREMENT_COMMUNITY = 47016;//增量上传学习数据COURSE_DAY-党建云

    //工作室人气值统计
    public static final int BAR_STUDIO_POPULAR_COMPUTE = 71201;

    //课程举报,审核后发送消息
    public static final int COURSE_INFORM_SEND = 48001;//增量上传学习数据COURSE_DAY-党建云

    public static final int STUDY_DATA_DAY30_TASK_MESSAGE   = 91005;    //大屏30天学习数据
    public static final int STUDY_TIME_DAY14_TASK_MESSAGE   = 91006;    //大屏14天学习时长走势
    public static final int STUDY_ACTIVITY_TASK_MESSAGE     = 91007;    //大屏重点学习活动
    public static final int BUILT_RESOURCE_TASK_MESSAGE          = 91008;    //大屏在库资源

    public static final int HAND_PROVINCE_AND_OTHERS          = 92001;    //新大屏（各公司数据）
    public static final int HAND_CURRENT_YEAR          = 92002;    //新大屏（全年指标数据）
    public static final int HAND_LEARNING_TREND_CHART          = 92003;    //新大屏（学习趋势图）
    public static final int HAND_ACTIVITY_VIEW          = 92004;    //新大屏（活动状况）
    public static final int HAND_RESOURCE_VIEW          = 92005;    //新大屏（资源情况）
    public static final int HAND_RESOURCE_TOP3          = 92006;    //新大屏（热门学习）

    //学习卡片更新
    public static final int STUDY_CARD_UPDATE = 46991;// 最近学习数据更新

    //2024反腐将笔记更新移动到别的队列中
    public static final int INTELLIGENT_NOTE_SECTION=46992;


    public static final int SCORING_SHEET = 50001;// 多维度评分表

    public static final int CAPTION_INSERT = 50002;// 生成字幕
    public static final int CAPTION_DOWNLOAD_FTP = 50003;// 下载字幕
    public static final int CAPTION_DOWNLOAD_FTP_All = 50004;// 定时处理所有未下载的字幕
    public static final int CAPTION_INSERT_CHAPTER_SECTION = 50005;//课件-生成字幕

    //红船审核素材切割
    public static final int RED_SHIP_COURSE_MATERIAL_SPLIT = 80001;
    //智能播报
    public static final int BROADCAST_SYNC = 60000;//文档同步九天
    public static final int BROADCAST_SYNC_UPDATE_DURATION = 60001;//更新时长
    public static final int VOICE_TUNING_CALLBACK_PROCESSING=60002;//音配回调处理
    public static final int BROADCAST_CONVERSION = 60003;//查询播报是否转换
    public static final int OCR_CONVERSION = 60004;//查询ocr是否转换
    public static final int OCR_ADN_FPT = 60005;//ftp上传,并且通知ocr

    public static final int SUBJECT_RANK_DELETE  = 70000;//删除专题排行榜

    public static final int SYSTEM_TOPIC_REFERENCE_STATISTICS = 10910;

    //子认证学习管理页面异步导出
    public static final int SUB_AUTHENTICATED_STUDY_EXPORT = 70001;
    /**
     * 修改虚拟空间直播
     */
    public static final int LIVE_VIRTUAL_SPACE_ORG_UPDATE = 70003;

    /**
     * 咪咕直播访问记回调入库
     */
    public static final int MIGU_USER_ACCESS = 70010;
    public static final int MIGU_START_STOP = 70011;

    /**
     * 新积分分播队列
     */
    public static final int POINT_ADD_LIVE = 70011;
    public static final int POINT_ADD_COURSE = 70012;
    public static final int POINT_ADD_SHORT_VIDEO = 70013;
    public static final int POINT_ADD_SHORT_VIDEO_CREATE = 70014;
    public static final int POINT_ADD_SHORT_VIDEO_BUFF = 70015;

    //新积分队列
    public static final int SYSTEM_POINT_CHANGE = 14007;

    /**知识图谱：基础课程全量同步*/
    public static final int GRAPH_COURSE=800001;
    /**知识图谱：基础知识全量同步*/
    public static final int GRAPH_KNOWLEDGE=800002;
    /**知识图谱：基础专区全量同步*/
    public static final int GRAPH_ZONE=800003;
    /**知识图谱：基础标签全量同步*/
    public static final int GRAPH_TOPIC=800004;
    /**知识图谱：基础案例全量同步*/
    public static final int GRAPH_CASE=800005;
    /**知识图谱：基础课程节点同步*/
    public static final int GRAPH_COURSE_NODE=800006;
    /**知识图谱：基础知识节点同步*/
    public static final int GRAPH_KNOWLEDGE_NODE=800007;
    /**知识图谱：基础专区节点同步*/
    public static final int GRAPH_ZONE_NODE=800008;
    /**知识图谱：基础标签节点同步*/
    public static final int GRAPH_TOPIC_NODE=800009;
    /**知识图谱：基础案例节点同步*/
    public static final int GRAPH_CASE_NODE=800010;

    /** 播报统计次数的监听*/
    public static final int NUMBER_OF_STATISTICS = 800011;

    /**
     * 红船自动审核
     */
    public static final int RED_SHIP_AUTO_AUDIT = 800012;

    /**
     * IHR专题培训班数据同步
     */
    public static final int IHR_TOPIC_TRAIN_SYNC = 800013;


    // 岗位章进度记录计算
    public static final int POSITION_CHAPTER_PROGRESS_UPDATE = 42801;
    public static final int ABILITY_SECTION_UPDATE = 42802;
    public static final int POSITION_CHAPTER_SECTION_UPDATE = 42803;

    /**数智导师FTP传输成功后需要同步的课程队列MQ常量*/
    public static final int SYNCHRONOUS_COURSE=800014;

    /**限流前置通知MQ*/
    public static final int COURSE_DISTRIBUTE_VIEW=108100;
    public static final int COURSE_DISTRIBUTE_SURVIVAL=108101;
    public static final int COURSE_DISTRIBUTE_RELEASE=108102;

    /*数智导师-专题*/
    public static final int DIGITAL_MENTOR_TOPIC = 108103;

    /*数智导师-获取摘要*/
    public static final int DIGITAL_MENTOR_GET_SUMMARY = 108104;
    /*数智导师-获取摘要*/
    public static final int DIGITAL_MENTOR_GET_SUMMARY_NEW = 108105;

    /*集中学时计算*/
    public static final int CONCENTRATE_STUDY_HOURS = 108200;
    /*集中学时计算-人员*/
    public static final int CONCENTRATE_STUDY_HOURS_MEMBER = 108201;



}
