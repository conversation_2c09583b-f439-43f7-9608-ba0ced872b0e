/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 回答问题时间差
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface IChatTimeRecord extends Serializable {

    /**
     * Setter for <code>course-study.t_chat_time_record.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_chat_time_record.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_chat_time_record.f_answer_id</code>. 回答id，第三方主键ID
     */
    public void setAnswerId(String value);

    /**
     * Getter for <code>course-study.t_chat_time_record.f_answer_id</code>. 回答id，第三方主键ID
     */
    public String getAnswerId();

    /**
     * Setter for <code>course-study.t_chat_time_record.f_time</code>. 思考时间 时间毫秒
     */
    public void setTime(Long value);

    /**
     * Getter for <code>course-study.t_chat_time_record.f_time</code>. 思考时间 时间毫秒
     */
    public Long getTime();

    /**
     * Setter for <code>course-study.t_chat_time_record.f_create_time</code>. 创建时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_chat_time_record.f_create_time</code>. 创建时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_chat_time_record.f_modify_date</code>. 更新时间
     */
    public void setModifyDate(Long value);

    /**
     * Getter for <code>course-study.t_chat_time_record.f_modify_date</code>. 更新时间
     */
    public Long getModifyDate();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface IChatTimeRecord
     */
    public void from(IChatTimeRecord from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface IChatTimeRecord
     */
    public <E extends IChatTimeRecord> E into(E into);
}
