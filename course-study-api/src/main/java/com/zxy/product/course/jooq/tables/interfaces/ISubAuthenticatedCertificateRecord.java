/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import javax.annotation.Generated;
import java.io.Serializable;


/**
 * 子认证-学员-证书记录表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ISubAuthenticatedCertificateRecord extends Serializable {

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_id</code>. id
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_id</code>. id
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_create_time</code>. 发证时间
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_create_time</code>. 发证时间
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_sub_authenticated_id</code>. 子认证id
     */
    public void setSubAuthenticatedId(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_sub_authenticated_id</code>. 子认证id
     */
    public String getSubAuthenticatedId();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_member_id</code>. 学员id
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_member_id</code>. 学员id
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_cancel_operator_id</code>. 删除操作人的id
     */
    public void setCancelOperatorId(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_cancel_operator_id</code>. 删除操作人的id
     */
    public String getCancelOperatorId();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_operator_id</code>. 发证人id
     */
    public void setOperatorId(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_operator_id</code>. 发证人id
     */
    public String getOperatorId();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_cancel_time</code>. 删除时间
     */
    public void setCancelTime(Long value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_cancel_time</code>. 删除时间
     */
    public Long getCancelTime();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_attachement_id</code>. 证明材料id
     */
    public void setAttachementId(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_attachement_id</code>. 证明材料id
     */
    public String getAttachementId();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_reason</code>. 删除原因描述
     */
    public void setReason(String value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_reason</code>. 删除原因描述
     */
    public String getReason();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_delete_flag</code>. 删除状态 0-未删除，1-已删除
     */
    public void setDeleteFlag(Integer value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_delete_flag</code>. 删除状态 0-未删除，1-已删除
     */
    public Integer getDeleteFlag();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_import_time</code>. 导入时间
     */
    public void setImportTime(Long value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_import_time</code>. 导入时间
     */
    public Long getImportTime();

    /**
     * Setter for <code>course-study.t_sub_authenticated_certificate_record.f_status</code>. 发证状态 0 未发 1已发
     */
    public void setStatus(Integer value);

    /**
     * Getter for <code>course-study.t_sub_authenticated_certificate_record.f_status</code>. 发证状态 0 未发 1已发
     */
    public Integer getStatus();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ISubAuthenticatedCertificateRecord
     */
    public void from(ISubAuthenticatedCertificateRecord from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ISubAuthenticatedCertificateRecord
     */
    public <E extends ISubAuthenticatedCertificateRecord> E into(E into);
}
