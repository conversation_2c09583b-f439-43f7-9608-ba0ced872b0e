package com.zxy.product.course.vo;

import java.io.Serializable;

/**
 * (PC)课程 | 专题 | 直播聚合接口属性VO（原/find-by-id的属性返回值）
 * <AUTHOR>
 * @date 2024年11月13日 10:55
 */
public class CustomizePcVO extends CustomizeParentVO implements Serializable {
    private static final long serialVersionUID = -5452804351167896957L;

    /**考试|调研数据Id*/
    private String id;

    /**考试|调研数据图片地址*/
    private String imagePath;

    /**考试|调研数据浏览量*/
    private Integer browseCount;

    /*全员共建共享，0=不是，1=是*/
    private Integer constructionType;

    private String organizationName;

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public Integer getConstructionType() {
        return constructionType;
    }

    public void setConstructionType(Integer constructionType) {
        this.constructionType = constructionType;
    }

    public String getId() { return id; }

    public void setId(String id) { this.id = id; }

    public String getImagePath() { return imagePath; }

    public void setImagePath(String imagePath) { this.imagePath = imagePath; }

    public Integer getBrowseCount() { return browseCount; }

    public void setBrowseCount(Integer browseCount) { this.browseCount = browseCount; }

    @Override
    public String toString() {
        return "CustomizePcVO{" +
                "id='" + id + '\'' +
                ", imagePath='" + imagePath + '\'' +
                ", browseCount=" + browseCount +
                '}';
    }
}
