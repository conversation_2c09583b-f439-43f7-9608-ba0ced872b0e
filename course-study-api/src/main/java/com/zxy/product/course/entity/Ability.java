package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.AbilityEntity;
import com.google.common.collect.ImmutableMap;
import java.util.List;
import java.util.Map;

public class Ability extends AbilityEntity {

    /**
     * 能力配置菜单uri
     */
    public static final String ABILITY_URI = "course-study/ability-info";

    public static final String ABILITY_TWO_LEVEL_URI = "course-study/ability-level-two-info";

    public static final String LAST_PAGE = "lastPage";

    public static final String COPY_ABILITY_CODE_PREFIX = "nlpz";


    /**
     * 最多关联资源数量
     */
    public static final int MAX_QUERY_COUNT_LIMIT = 1000;

    /**
     * 草稿状态
     */
    public static final Integer DRAFT_STATUS = 0;
    /**
     * 发布状态
     */
    public static final Integer PUBLISH_STATUS = 1;
    /**
     * 取消发布状态
     */
    public static final Integer UN_PUBLISH_STATUS = 2;


    public static final Integer STUDY_MAP_TYPE = 1;

    /**
     * 状态 0-禁用 1-启用
     */
    public static final Integer ORG_NOT_CONTAIN = 0;
    public static final Integer ORG_CONTAIN = 1;

    /**
     * 能力级别，1级
     */
    public static final Integer ABILITY_LEVEL_ONE = 1;
    /**
     * 能力级别，2级
     */
    public static final Integer ABILITY_LEVEL_TWO = 2;
    public static final Map<Integer, String> abilityCategoryMap = ImmutableMap.of(1, "党建领导力", 2, "市场经营", 3, "专业技术", 4,"综合管理");


    /**
     * 归属部门
     */
    private Organization organization;
    /**
     * 内容列表
     */
    private List<AbilityBusiness> businesses;

    private List<AbilityBusiness> courseChapterSections;
    public Organization getOrganization() {
        return organization;
    }

    public void setOrganization(Organization organization) {
        this.organization = organization;
    }

    public List<AbilityBusiness> getBusinesses() {
        return businesses;
    }

    public void setBusinesses(List<AbilityBusiness> businesses) {
        this.businesses = businesses;
    }

    public List<AbilityBusiness> getCourseChapterSections() {
        return courseChapterSections;
    }

    public void setCourseChapterSections(List<AbilityBusiness> courseChapterSections) {
        this.courseChapterSections = courseChapterSections;
    }
}
