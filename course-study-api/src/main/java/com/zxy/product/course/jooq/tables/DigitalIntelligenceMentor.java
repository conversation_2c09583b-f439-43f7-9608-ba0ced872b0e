/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.DigitalIntelligenceMentorRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.ForeignKey;
import org.jooq.Name;
import org.jooq.Record;
import org.jooq.Row5;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;


/**
 * 数智导师结果表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.12.4"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class DigitalIntelligenceMentor extends TableImpl<DigitalIntelligenceMentorRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_digital_intelligence_mentor</code>
     */
    public static final DigitalIntelligenceMentor DIGITAL_INTELLIGENCE_MENTOR = new DigitalIntelligenceMentor();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<DigitalIntelligenceMentorRecord> getRecordType() {
        return DigitalIntelligenceMentorRecord.class;
    }

    /**
     * The column <code>course-study.t_digital_intelligence_mentor.f_id</code>. 主键ID
     */
    public final TableField<DigitalIntelligenceMentorRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR(40).nullable(false), this, "主键ID");

    /**
     * The column <code>course-study.t_digital_intelligence_mentor.f_member_id</code>. 用户ID，关联t_member表
     */
    public final TableField<DigitalIntelligenceMentorRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR(40).defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户ID，关联t_member表");

    /**
     * The column <code>course-study.t_digital_intelligence_mentor.f_user_query</code>. 用户问题
     */
    public final TableField<DigitalIntelligenceMentorRecord, String> USER_QUERY = createField("f_user_query", org.jooq.impl.SQLDataType.CLOB.defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "用户问题");

    /**
     * The column <code>course-study.t_digital_intelligence_mentor.f_bot_response</code>. 大模型响应
     */
    public final TableField<DigitalIntelligenceMentorRecord, String> BOT_RESPONSE = createField("f_bot_response", org.jooq.impl.SQLDataType.CLOB.defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "大模型响应");



    /**
     * The column <code>course-study.t_digital_intelligence_mentor.f_create_time</code>. 创建时间
     */
    public final TableField<DigitalIntelligenceMentorRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * Create a <code>course-study.t_digital_intelligence_mentor</code> table reference
     */
    public DigitalIntelligenceMentor() {
        this("t_digital_intelligence_mentor", null);
    }

    /**
     * Create an aliased <code>course-study.t_digital_intelligence_mentor</code> table reference
     */
    public DigitalIntelligenceMentor(String alias) {
        this(alias, DIGITAL_INTELLIGENCE_MENTOR);
    }

    private DigitalIntelligenceMentor(String alias, Table<DigitalIntelligenceMentorRecord> aliased) {
        this(alias, aliased, null);
    }

    private DigitalIntelligenceMentor(String alias, Table<DigitalIntelligenceMentorRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "数智导师结果表");
    }

    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    @Override
    public UniqueKey<DigitalIntelligenceMentorRecord> getPrimaryKey() {
        return Keys.KEY_T_DIGITAL_INTELLIGENCE_MENTOR_PRIMARY;
    }

    @Override
    public List<UniqueKey<DigitalIntelligenceMentorRecord>> getKeys() {
        return Arrays.<UniqueKey<DigitalIntelligenceMentorRecord>>asList(Keys.KEY_T_DIGITAL_INTELLIGENCE_MENTOR_PRIMARY);
    }

    @Override
    public DigitalIntelligenceMentor as(String alias) {
        return new DigitalIntelligenceMentor(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public DigitalIntelligenceMentor rename(String name) {
        return new DigitalIntelligenceMentor(name, null);
    }
}
