/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.interfaces;


import java.io.Serializable;

import javax.annotation.Generated;


/**
 * 课程学习进度表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public interface ICourseStudyProgress extends Serializable {

    /**
     * Setter for <code>course-study.t_course_study_progress.f_id</code>.
     */
    public void setId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_id</code>.
     */
    public String getId();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_member_id</code>. 用户ID
     */
    public void setMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_member_id</code>. 用户ID
     */
    public String getMemberId();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_course_id</code>. 课程ID
     */
    public void setCourseId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_course_id</code>. 课程ID
     */
    public String getCourseId();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_begin_time</code>. 学习开始时间
     */
    public void setBeginTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_begin_time</code>. 学习开始时间
     */
    public Long getBeginTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_type</code>. 注册类型，1-自主注册，2-学习推送，3-岗位推送，4-人员标签，5-班级，6-学习专题
     */
    public void setType(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_type</code>. 注册类型，1-自主注册，2-学习推送，3-岗位推送，4-人员标签，5-班级，6-学习专题
     */
    public Integer getType();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_is_required</code>. 元素性质，1-选修，2-必修
     */
    public void setIsRequired(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_is_required</code>. 元素性质，1-选修，2-必修
     */
    public Integer getIsRequired();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃 ，4-标记完成
     */
    public void setFinishStatus(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃 ，4-标记完成
     */
    public Integer getFinishStatus();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_finish_time</code>. 完成时间
     */
    public void setFinishTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_finish_time</code>. 完成时间
     */
    public Long getFinishTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_study_total_time</code>. 课程学习总时长，单位秒
     */
    public void setStudyTotalTime(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_study_total_time</code>. 课程学习总时长，单位秒
     */
    public Integer getStudyTotalTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_register_time</code>. 注册时间
     */
    public void setRegisterTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_register_time</code>. 注册时间
     */
    public Long getRegisterTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_last_access_time</code>. 最后访问时间
     */
    public void setLastAccessTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_last_access_time</code>. 最后访问时间
     */
    public Long getLastAccessTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_create_time</code>.
     */
    public void setCreateTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_create_time</code>.
     */
    public Long getCreateTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.t_course_version_id</code>. 课程版本id
     */
    public void setCourseVersionId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress.t_course_version_id</code>. 课程版本id
     */
    public String getCourseVersionId();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_mark_member_id</code>. 标记人
     */
    public void setMarkMemberId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_mark_member_id</code>. 标记人
     */
    public String getMarkMemberId();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_mark_time</code>. 标记时间
     */
    public void setMarkTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_mark_time</code>. 标记时间
     */
    public Long getMarkTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_completed_rate</code>. 完成进度(百分比)
     */
    public void setCompletedRate(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_completed_rate</code>. 完成进度(百分比)
     */
    public Integer getCompletedRate();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_current_chapter_id</code>. 当前需要学习的章id
     */
    public void setCurrentChapterId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_current_chapter_id</code>. 当前需要学习的章id
     */
    public String getCurrentChapterId();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_current_section_id</code>. 当前需要学习的节id
     */
    public void setCurrentSectionId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_current_section_id</code>. 当前需要学习的节id
     */
    public String getCurrentSectionId();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_push_id</code>. 推送id，用于关联最后一次必修的推送
     */
    public void setPushId(String value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_push_id</code>. 推送id，用于关联最后一次必修的推送
     */
    public String getPushId();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_visits</code>. 学习次数
     */
    public void setVisits(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_visits</code>. 学习次数
     */
    public Integer getVisits();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_last_modify_time</code>. 课程汇总表最后一次更新时间
     */
    public void setLastModifyTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_last_modify_time</code>. 课程汇总表最后一次更新时间
     */
    public Long getLastModifyTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_subject_finish_time</code>. 专题必修课完成时间
     */
    public void setSubjectFinishTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_subject_finish_time</code>. 专题必修课完成时间
     */
    public Long getSubjectFinishTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_completion_times</code>. 完成次数
     */
    public void setCompletionTimes(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_completion_times</code>. 完成次数
     */
    public Integer getCompletionTimes();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_latest_completion_time</code>. 最新完成时间
     */
    public void setLatestCompletionTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_latest_completion_time</code>. 最新完成时间
     */
    public Long getLatestCompletionTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_study_hours_record_time</code>. 集中学时记录时间
     */
    public void setStudyHoursRecordTime(Long value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_study_hours_record_time</code>. 集中学时记录时间
     */
    public Long getStudyHoursRecordTime();

    /**
     * Setter for <code>course-study.t_course_study_progress.f_concentrate_study_hours</code>. 集中学时
     */
    public void setConcentrateStudyHours(Integer value);

    /**
     * Getter for <code>course-study.t_course_study_progress.f_concentrate_study_hours</code>. 集中学时
     */
    public Integer getConcentrateStudyHours();

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * Load data from another generated Record/POJO implementing the common interface ICourseStudyProgress
     */
    public void from(com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProgress from);

    /**
     * Copy data into another generated Record/POJO implementing the common interface ICourseStudyProgress
     */
    public <E extends com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProgress> E into(E into);
}
