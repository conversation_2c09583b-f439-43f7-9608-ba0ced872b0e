package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedEntity;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/6
 */
public class SubAuthenticated extends SubAuthenticatedEntity {
    private static final long serialVersionUID = -6230083454508749623L;
    List<SubAuthenticatedContentConfigure> contentConfigureList;
    List<SubAuthenticatedMemberDimension> memberDimensions = new ArrayList<>();
    List<String> contentTypeList = new ArrayList<>();
    public static final int PUBLISH_NO = 0; //未发布

    public static final int PUBLISH_YES = 1; //发布
    public static final Integer SPLIT_TRAINING_FLAG_NO = 0; //不分离

    public static final Integer SPLIT_TRAINING_FLAG_YES = 1; //分离

    public static final Integer CERTIFICATED_EXIST_FLAG_NO = 0; //未获得证书

    public static final Integer CERTIFICATED_EXIST_FLAG_YES = 1; //获得证书

    /**
     * 不校验证书
     */
    public static final Integer CHECK_CERTIFICATE_FLAG_NO = 0;

    /**
     * 校验证书
     */
    public static final Integer CHECK_CERTIFICATE_FLAG_YES = 1;



    private String publishMember;//发布人
    private String organizationName;//归属部门
    private Integer registerNum;//注册人数
    private String certificatedRecordId;//认证证书
    private Integer certificatedExistFlag;//学员是否获得了证书标志 0：未获得，1：获得
    private String subCertificatedRecordId; //子认证记录id
    private Integer subCertificatedPublish;// 子认证证书是否已发放

    public Integer getSubCertificatedPublish() {
        return subCertificatedPublish;
    }

    public void setSubCertificatedPublish(Integer subCertificatedPublish) {
        this.subCertificatedPublish = subCertificatedPublish;
    }

    public String getSubCertificatedRecordId() {
        return subCertificatedRecordId;
    }

    public void setSubCertificatedRecordId(String subCertificatedRecordId) {
        this.subCertificatedRecordId = subCertificatedRecordId;
    }

    public Integer getCertificatedExistFlag() {
        return certificatedExistFlag;
    }

    public void setCertificatedExistFlag(Integer certificatedExistFlag) {
        this.certificatedExistFlag = certificatedExistFlag;
    }


    public String getCertificatedRecordId() {
        return certificatedRecordId;
    }

    public void setCertificatedRecordId(String certificatedRecordId) {
        this.certificatedRecordId = certificatedRecordId;
    }

    public List<String> getContentTypeList() {
        return contentTypeList;
    }

    public void setContentTypeList(List<String> contentTypeList) {
        this.contentTypeList = contentTypeList;
    }

    public Integer getRegisterNum() {
        return registerNum;
    }

    public void setRegisterNum(Integer registerNum) {
        this.registerNum = registerNum;
    }

    public String getPublishMember() {
        return publishMember;
    }

    public void setPublishMember(String publishMember) {
        this.publishMember = publishMember;
    }

    public String getOrganizationName() {
        return organizationName;
    }

    public void setOrganizationName(String organizationName) {
        this.organizationName = organizationName;
    }

    public List<SubAuthenticatedContentConfigure> getContentConfigureList() {
        return contentConfigureList;
    }

    public void setContentConfigureList(List<SubAuthenticatedContentConfigure> contentConfigureList) {
        this.contentConfigureList = contentConfigureList;
    }

    public List<SubAuthenticatedMemberDimension> getMemberDimensions() {
        return memberDimensions;
    }

    public void setMemberDimensions(List<SubAuthenticatedMemberDimension> memberDimensions) {
        this.memberDimensions = memberDimensions;
    }
}
