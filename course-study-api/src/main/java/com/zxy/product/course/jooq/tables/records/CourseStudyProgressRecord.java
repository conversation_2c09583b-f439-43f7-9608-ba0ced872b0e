/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseStudyProgress;
import com.zxy.product.course.jooq.tables.interfaces.ICourseStudyProgress;

import javax.annotation.Generated;

import org.jooq.Record1;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 课程学习进度表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudyProgressRecord extends UpdatableRecordImpl<CourseStudyProgressRecord> implements ICourseStudyProgress {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_study_progress.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_member_id</code>. 用户ID
     */
    @Override
    public void setMemberId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_member_id</code>. 用户ID
     */
    @Override
    public String getMemberId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_course_id</code>. 课程ID
     */
    @Override
    public void setCourseId(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_course_id</code>. 课程ID
     */
    @Override
    public String getCourseId() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_begin_time</code>. 学习开始时间
     */
    @Override
    public void setBeginTime(Long value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_begin_time</code>. 学习开始时间
     */
    @Override
    public Long getBeginTime() {
        return (Long) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_type</code>. 注册类型，1-自主注册，2-学习推送，3-岗位推送，4-人员标签，5-班级，6-学习专题
     */
    @Override
    public void setType(Integer value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_type</code>. 注册类型，1-自主注册，2-学习推送，3-岗位推送，4-人员标签，5-班级，6-学习专题
     */
    @Override
    public Integer getType() {
        return (Integer) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_is_required</code>. 元素性质，1-选修，2-必修
     */
    @Override
    public void setIsRequired(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_is_required</code>. 元素性质，1-选修，2-必修
     */
    @Override
    public Integer getIsRequired() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃 ，4-标记完成
     */
    @Override
    public void setFinishStatus(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃 ，4-标记完成
     */
    @Override
    public Integer getFinishStatus() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_finish_time</code>. 完成时间
     */
    @Override
    public void setFinishTime(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_finish_time</code>. 完成时间
     */
    @Override
    public Long getFinishTime() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_study_total_time</code>. 课程学习总时长，单位秒
     */
    @Override
    public void setStudyTotalTime(Integer value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_study_total_time</code>. 课程学习总时长，单位秒
     */
    @Override
    public Integer getStudyTotalTime() {
        return (Integer) get(8);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_register_time</code>. 注册时间
     */
    @Override
    public void setRegisterTime(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_register_time</code>. 注册时间
     */
    @Override
    public Long getRegisterTime() {
        return (Long) get(9);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_last_access_time</code>. 最后访问时间
     */
    @Override
    public void setLastAccessTime(Long value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_last_access_time</code>. 最后访问时间
     */
    @Override
    public Long getLastAccessTime() {
        return (Long) get(10);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_create_time</code>.
     */
    @Override
    public void setCreateTime(Long value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_create_time</code>.
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(11);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.t_course_version_id</code>. 课程版本id
     */
    @Override
    public void setCourseVersionId(String value) {
        set(12, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.t_course_version_id</code>. 课程版本id
     */
    @Override
    public String getCourseVersionId() {
        return (String) get(12);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_mark_member_id</code>. 标记人
     */
    @Override
    public void setMarkMemberId(String value) {
        set(13, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_mark_member_id</code>. 标记人
     */
    @Override
    public String getMarkMemberId() {
        return (String) get(13);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_mark_time</code>. 标记时间
     */
    @Override
    public void setMarkTime(Long value) {
        set(14, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_mark_time</code>. 标记时间
     */
    @Override
    public Long getMarkTime() {
        return (Long) get(14);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_completed_rate</code>. 完成进度(百分比)
     */
    @Override
    public void setCompletedRate(Integer value) {
        set(15, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_completed_rate</code>. 完成进度(百分比)
     */
    @Override
    public Integer getCompletedRate() {
        return (Integer) get(15);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_current_chapter_id</code>. 当前需要学习的章id
     */
    @Override
    public void setCurrentChapterId(String value) {
        set(16, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_current_chapter_id</code>. 当前需要学习的章id
     */
    @Override
    public String getCurrentChapterId() {
        return (String) get(16);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_current_section_id</code>. 当前需要学习的节id
     */
    @Override
    public void setCurrentSectionId(String value) {
        set(17, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_current_section_id</code>. 当前需要学习的节id
     */
    @Override
    public String getCurrentSectionId() {
        return (String) get(17);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_push_id</code>. 推送id，用于关联最后一次必修的推送
     */
    @Override
    public void setPushId(String value) {
        set(18, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_push_id</code>. 推送id，用于关联最后一次必修的推送
     */
    @Override
    public String getPushId() {
        return (String) get(18);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_visits</code>. 学习次数
     */
    @Override
    public void setVisits(Integer value) {
        set(19, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_visits</code>. 学习次数
     */
    @Override
    public Integer getVisits() {
        return (Integer) get(19);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_last_modify_time</code>. 课程汇总表最后一次更新时间
     */
    @Override
    public void setLastModifyTime(Long value) {
        set(20, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_last_modify_time</code>. 课程汇总表最后一次更新时间
     */
    @Override
    public Long getLastModifyTime() {
        return (Long) get(20);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_subject_finish_time</code>. 专题必修课完成时间
     */
    @Override
    public void setSubjectFinishTime(Long value) {
        set(21, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_subject_finish_time</code>. 专题必修课完成时间
     */
    @Override
    public Long getSubjectFinishTime() {
        return (Long) get(21);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_completion_times</code>. 完成次数
     */
    @Override
    public void setCompletionTimes(Integer value) {
        set(22, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_completion_times</code>. 完成次数
     */
    @Override
    public Integer getCompletionTimes() {
        return (Integer) get(22);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_latest_completion_time</code>. 最新完成时间
     */
    @Override
    public void setLatestCompletionTime(Long value) {
        set(23, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_latest_completion_time</code>. 最新完成时间
     */
    @Override
    public Long getLatestCompletionTime() {
        return (Long) get(23);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_study_hours_record_time</code>. 集中学时记录时间
     */
    @Override
    public void setStudyHoursRecordTime(Long value) {
        set(24, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_study_hours_record_time</code>. 集中学时记录时间
     */
    @Override
    public Long getStudyHoursRecordTime() {
        return (Long) get(24);
    }

    /**
     * Setter for <code>course-study.t_course_study_progress.f_concentrate_study_hours</code>. 集中学时
     */
    @Override
    public void setConcentrateStudyHours(Integer value) {
        set(25, value);
    }

    /**
     * Getter for <code>course-study.t_course_study_progress.f_concentrate_study_hours</code>. 集中学时
     */
    @Override
    public Integer getConcentrateStudyHours() {
        return (Integer) get(25);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseStudyProgress from) {
        setId(from.getId());
        setMemberId(from.getMemberId());
        setCourseId(from.getCourseId());
        setBeginTime(from.getBeginTime());
        setType(from.getType());
        setIsRequired(from.getIsRequired());
        setFinishStatus(from.getFinishStatus());
        setFinishTime(from.getFinishTime());
        setStudyTotalTime(from.getStudyTotalTime());
        setRegisterTime(from.getRegisterTime());
        setLastAccessTime(from.getLastAccessTime());
        setCreateTime(from.getCreateTime());
        setCourseVersionId(from.getCourseVersionId());
        setMarkMemberId(from.getMarkMemberId());
        setMarkTime(from.getMarkTime());
        setCompletedRate(from.getCompletedRate());
        setCurrentChapterId(from.getCurrentChapterId());
        setCurrentSectionId(from.getCurrentSectionId());
        setPushId(from.getPushId());
        setVisits(from.getVisits());
        setLastModifyTime(from.getLastModifyTime());
        setSubjectFinishTime(from.getSubjectFinishTime());
        setCompletionTimes(from.getCompletionTimes());
        setLatestCompletionTime(from.getLatestCompletionTime());
        setStudyHoursRecordTime(from.getStudyHoursRecordTime());
        setConcentrateStudyHours(from.getConcentrateStudyHours());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseStudyProgress> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseStudyProgressRecord
     */
    public CourseStudyProgressRecord() {
        super(CourseStudyProgress.COURSE_STUDY_PROGRESS);
    }

    /**
     * Create a detached, initialised CourseStudyProgressRecord
     */
    public CourseStudyProgressRecord(String id, String memberId, String courseId, Long beginTime, Integer type, Integer isRequired, Integer finishStatus, Long finishTime, Integer studyTotalTime, Long registerTime, Long lastAccessTime, Long createTime, String courseVersionId, String markMemberId, Long markTime, Integer completedRate, String currentChapterId, String currentSectionId, String pushId, Integer visits, Long lastModifyTime, Long subjectFinishTime, Integer completionTimes, Long latestCompletionTime, Long studyHoursRecordTime, Integer concentrateStudyHours) {
        super(CourseStudyProgress.COURSE_STUDY_PROGRESS);

        set(0, id);
        set(1, memberId);
        set(2, courseId);
        set(3, beginTime);
        set(4, type);
        set(5, isRequired);
        set(6, finishStatus);
        set(7, finishTime);
        set(8, studyTotalTime);
        set(9, registerTime);
        set(10, lastAccessTime);
        set(11, createTime);
        set(12, courseVersionId);
        set(13, markMemberId);
        set(14, markTime);
        set(15, completedRate);
        set(16, currentChapterId);
        set(17, currentSectionId);
        set(18, pushId);
        set(19, visits);
        set(20, lastModifyTime);
        set(21, subjectFinishTime);
        set(22, completionTimes);
        set(23, latestCompletionTime);
        set(24, studyHoursRecordTime);
        set(25, concentrateStudyHours);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressEntity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseStudyProgressEntity)source;
        pojo.into(this);
        return true;
    }
}
