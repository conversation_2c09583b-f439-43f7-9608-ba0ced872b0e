package com.zxy.product.course.api.ability;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.Ability;
import com.zxy.product.course.entity.AbilityBusiness;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@RemoteService
public interface AbilityService {

    /**
     * 获取能力对象
     *
     * @return 能力对象中嵌套着归属部门对象
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Ability get(String id);

    /**
     * 新增能力
     *
     * @param name           能力名称
     * @param code           能力编码
     * @param organizationId 归属部门
     * @param desc           描述
     * @param status         状态：1-启用 0-禁用（默认）
     * @param businesses     配置的内容列表，null-不处理内容
     */
    @Transactional
    Ability insert(String name, String code, String organizationId, Optional<String> rootOrgId, Optional<String> desc, Optional<Integer> status,Optional<Integer> type, Optional<List<AbilityBusiness>> businesses,Optional<Integer>abilityCategory,Optional<String>abilitySubCategory,Optional<Integer> level);

    /**
     * 更新能力
     *
     * @param id             能力主键id
     * @param name           能力名称
     * @param code           能力编码
     * @param organizationId 归属部门
     * @param desc           描述
     * @param status         状态：1-启用 0-禁用
     * @param businesses     配置的内容列表，null-不处理内容
     */
    @Transactional
    Ability update(String id, Optional<String> name, Optional<String> code, Optional<String> organizationId, Optional<String> desc, Optional<Integer> status, Optional<List<AbilityBusiness>> businesses,Optional<Integer>abilityCategory,Optional<String>abilitySubCategory,Optional<Integer> level);

    /**
     * 根据主键删除
     * 有关联内容或者有被岗位使用则不能删除
     *
     * @param id 能力主键id
     * @return 受影响行数
     */
    @Transactional
    Integer delete(String id);

    /**
     * 启用或禁用
     *
     * @param id     能力主键id
     * @param status 状态: 1-发布 其他-取消发布
     */
    @Transactional
    Ability changeStatus(String id, Integer status);

    /**
     * 批量启用或禁用
     *
     * @param ids    能力主键id
     * @param status 状态: 1-发布 其他-取消发布
     */
    @Transactional
    Map<String, String> batchChangeStatus(Collection<String> ids, Integer status);

    /**
     * 能力分页列表
     *
     * @param page           -1查询全部（最多1000条）
     * @param pageSize       -1查询全部（最多1000条）
     * @param name           能力名称，模糊查找
     * @param code           能力编码，全匹配
     * @param status         能力状态
     * @param orgIds 归属部门
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<Ability> findPage(int page, int pageSize, Optional<String> name, Optional<String> code, Optional<Integer> status, Set<String> orgIds,Optional<String[]> selectIds,Optional<Integer>level,Optional<Integer>abilityCategory,Optional<String>abilitySubCategory);

    /**
     * 能力分页列表
     *
     * @param page           -1查询全部（最多1000条）
     * @param pageSize       -1查询全部（最多1000条）
     * @param name           能力名称，模糊查找
     * @param code           能力编码，全匹配
     * @param status         能力状态
     * @param organizationId 归属部门
     * @param orgMap         用户权限节点
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<Ability> findPageNew(int page, int pageSize, Optional<String> path, Optional<String> name, Optional<String> code, Optional<Integer> status, Optional<String> organizationId, Optional<Integer> level,Map<String, Set<String>> orgMap, Integer contain, Boolean lastPageFlag,Optional<Integer>abilityCategory,Optional<String>abilitySubCategory);


    /**
     * 编码是否重复
     *
     * @param id        能力主键id，传了将排除此能力的编码
     * @param companyId 企业id，未传-取上下文中的企业id
     * @param code      能力编码
     * @return true-已被使用 false-未被使用
     */
    @Transactional(readOnly = true)
    Boolean codeUsed(Optional<String> id, Optional<String> companyId, String code);

    /**
     * 根据编码集查询能力列表
     * 导入功能 验证编码有效性
     *
     * @param companyId 企业id，未传-取上下文中的企业id
     * @param codes     能力编码集
     * @param orgMap    权限节点集, 空集-查所有
     * @return 返回数据库中存在的编码集
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<Ability> findByCodes(Optional<String> companyId, Collection<String> codes, Map<String, Set<String>> orgMap);

    /**
     * 加岗位关联数 或者 内容关联数
     *
     * @param id                能力主键id
     * @param positionIncrement 岗位增加数量
     * @param businessIncrement 内容增加数量
     */
    @Transactional
    void addRelationCount(String id, Optional<Integer> positionIncrement, Optional<Integer> businessIncrement);

    /**
     * 批量插入
     */
    @Transactional
    Integer batchInsert(Collection<Ability> abilities);

    /**
     * 根据当前用户权限节点获取能力列表
     *
     * @param orgMap 用户权限节点
     */
    @Transactional(readOnly = true)
    List<Ability> getCodeAndNameList(Map<String, Set<String>> orgMap);

    /**
     * 根据ids查询内容列表
     *
     * @param ids 能力id集
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<Ability> findBusinessByIds(Collection<String> ids);

    /**
     * 根据ids查询内容列表
     *
     * @param ids 能力id集
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<Ability> getBusinessByIds(Collection<String> ids);

    /**
     * 获取能力归属部门的path
     *
     * @return 部门的path路径
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    String getOrgPathById(String id);

    /**
     * 获取能力归属部门的path
     *
     * @return 部门的path路径
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    String getOrgPathByAbilityBusinessId(String abilityBusinessId);

    /**
     * 获取能力归属部门的path
     *
     * @return 部门的path路径
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> getBatchOrgPathByIds(String[] ids);

    /**
     * 更新能力关联岗位数
     */
    @Transactional
    Boolean updatePositionNum(List<String> abilityIds);

    /**
     *
     * @param abilityIds
     * @return
     */
    List<Ability> getByIds(String abilityIds);

    /**
     * 批量新增
     * @param abilityJson
     * @return
     */
    List<Ability> batchAdd(String abilityJson);

    /**
     *
     * @param id
     */
    @Transactional
    Ability copyOneLevelAbility(String id,String memberOrganizationId);

    /**
     * 能力分页列表
     *
     * @param page           -1查询全部（最多1000条）
     * @param pageSize       -1查询全部（最多1000条）
     * @param name           能力名称，模糊查找
     * @param code           能力编码，全匹配
     * @param status         能力状态
     * @param organizationId 归属部门
     * @param orgMap         用户权限节点
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<Ability> findLevelTwoPage(int page, int pageSize, Optional<String> path, Optional<String> name, Optional<String> code, Optional<Integer> status, Optional<String> organizationId, Optional<Integer> level,Map<String, Set<String>> orgMap, Integer contain, Boolean lastPageFlag,Optional<Integer>abilityCategory,Optional<String>abilitySubCategory);

}
