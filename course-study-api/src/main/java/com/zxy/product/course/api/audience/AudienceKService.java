package com.zxy.product.course.api.audience;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.product.course.entity.AudienceItem;
import com.zxy.product.course.entity.AudienceObject;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * Created by keeley on 2017/9/20.
 */
@RemoteService(timeout = 100000)
public interface AudienceKService {
    Integer[] simpleType = { AudienceItem.JOIN_TYPE_MEMBER, AudienceItem.JOIN_TYPE_ORG_EXCLUDE,AudienceItem.JOIN_TYPE_ORG_INCLUDE, AudienceItem.JOIN_TYPE_MEMBER_TAG, AudienceItem.JOIN_TYPE_POST, AudienceItem.JOIN_TYPE_JOB };

    /**
     * 根据业务ID、业务类型查询受众对象
     * @param id
     * @param businessType
     * @return
     */
    @Transactional(readOnly = true)
    List<AudienceItem> getAudienceItem(String id, int businessType);



    /**
     * 更新AudienceObject 对象
     * @return
     */
    int updateAudience(String bussinessId, Integer bussinessType, String memberId, String uri, List<AudienceItem> audienceItems);
    /**
     * 追加AudienceObject 对象
     * @return
     */
    int appendAudience(String bussinessId, Integer bussinessType, String memberId, String uri, List<AudienceItem> audienceItems);

    int deleteAudience(String businessId, int businessType);
    int insertAudience(List<AudienceObject> objects);


    /**
     * 校验用户是否有驾驶舱受众权限
     */
    boolean checkPermission(String memberId);

    void setDataScreenCache(Optional<String> businessId);

    @Transactional(readOnly = true)
    List<AudienceItem> getAudienceByBusinessId(String id);

    @Transactional
    void deleteAudienceConstructionType(String courseId);
}


