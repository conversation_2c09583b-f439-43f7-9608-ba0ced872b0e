/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.ICourseQuestionRecommend;

import javax.annotation.Generated;


/**
 * 推荐问题管理表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseQuestionRecommendEntity extends BaseEntity implements ICourseQuestionRecommend {

    private static final long serialVersionUID = 1L;

    private String  question;
    private String  answer;
    private String  questionThirdId;
    private String  answerText;
    private Integer    recommend;
    private Integer    status;
    private Integer sort;
    private Long    modifyDate;

    public CourseQuestionRecommendEntity() {}

    public CourseQuestionRecommendEntity(CourseQuestionRecommendEntity value) {
        this.question = value.question;
        this.answer = value.answer;
        this.questionThirdId = value.questionThirdId;
        this.answerText = value.answerText;
        this.recommend = value.recommend;
        this.status = value.status;
        this.sort = value.sort;
        this.modifyDate = value.modifyDate;
    }

    public CourseQuestionRecommendEntity(
        String  id,
        String  question,
        String  answer,
        String  questionThirdId,
        String  answerText,
        Integer    recommend,
        Integer    status,
        Integer sort,
        Long    createTime,
        Long    modifyDate
    ) {
        super.setId(id);
        this.question = question;
        this.answer = answer;
        this.questionThirdId = questionThirdId;
        this.answerText = answerText;
        this.recommend = recommend;
        this.status = status;
        this.sort = sort;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public String getQuestion() {
        return this.question;
    }

    @Override
    public void setQuestion(String question) {
        this.question = question;
    }

    @Override
    public String getAnswer() {
        return this.answer;
    }

    @Override
    public void setAnswer(String answer) {
        this.answer = answer;
    }

    @Override
    public String getQuestionThirdId() {
        return this.questionThirdId;
    }

    @Override
    public void setQuestionThirdId(String questionThirdId) {
        this.questionThirdId = questionThirdId;
    }

    @Override
    public String getAnswerText() {
        return this.answerText;
    }

    @Override
    public void setAnswerText(String answerText) {
        this.answerText = answerText;
    }

    @Override
    public Integer getRecommend() {
        return this.recommend;
    }

    @Override
    public void setRecommend(Integer recommend) {
        this.recommend = recommend;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public Integer getSort() {
        return this.sort;
    }

    @Override
    public void setSort(Integer sort) {
        this.sort = sort;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Long getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Long modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("CourseQuestionRecommendEntity (");

        sb.append(getId());
        sb.append(", ").append(question);
        sb.append(", ").append(answer);
        sb.append(", ").append(questionThirdId);
        sb.append(", ").append(answerText);
        sb.append(", ").append(recommend);
        sb.append(", ").append(status);
        sb.append(", ").append(sort);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseQuestionRecommend from) {
        setId(from.getId());
        setQuestion(from.getQuestion());
        setAnswer(from.getAnswer());
        setQuestionThirdId(from.getQuestionThirdId());
        setAnswerText(from.getAnswerText());
        setRecommend(from.getRecommend());
        setStatus(from.getStatus());
        setSort(from.getSort());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseQuestionRecommend> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends CourseQuestionRecommendEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.CourseQuestionRecommendRecord r = new com.zxy.product.course.jooq.tables.records.CourseQuestionRecommendRecord();
                org.jooq.Row row = record.fieldsRow();
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ID, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.QUESTION.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.QUESTION, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.QUESTION));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ANSWER.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ANSWER, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ANSWER));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.QUESTION_THIRD_ID.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.QUESTION_THIRD_ID, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.QUESTION_THIRD_ID));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ANSWER_TEXT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ANSWER_TEXT, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.ANSWER_TEXT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.RECOMMEND.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.RECOMMEND, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.RECOMMEND));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.STATUS.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.STATUS, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.STATUS));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.SORT.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.SORT, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.SORT));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.CREATE_TIME.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.CREATE_TIME));});
                row.fieldStream().filter(f -> { return f.toString().equals(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.MODIFY_DATE.toString()); }).findAny().ifPresent(field -> {
                    r.setValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND.MODIFY_DATE));});
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
