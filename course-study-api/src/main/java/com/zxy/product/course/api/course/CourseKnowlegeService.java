package com.zxy.product.course.api.course;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.CourseKnowledge;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/11:01
 * @Description:
 */
@RemoteService
public interface CourseKnowlegeService {

    /**
     * 添加知识*
     * @param courseKnowledge
     * @param file
     */
    @Transactional
    void insert(CourseKnowledge courseKnowledge);

    /**
     * 得到知识详细信息*
     * @param id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    Optional<CourseKnowledge> getOptional(String id);

    /**
     * 更新知识
     * @param courseKnowledge
     */
    @Transactional
    void update(CourseKnowledge courseKnowledge);

    /**
     * 删除知识
     * @param id
     */
    @Transactional
    void delete(String id);

    /**
     * * 发布，取消发布
     * @param id
     * @param status
     */
    @Transactional
    void publish(String id, Integer status, CourseKnowledge courseKnowledge);

    /**
     * 知识列表
     * @param page
     * @param pageSize
     * @param name
     * @param status
     * @param shelveTime
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.REQUIRES_NEW)
    PagedResult<CourseKnowledge> find(Integer page, Integer pageSize, Optional<String> name, Optional<Integer> status, Optional<Long> shelveTime, Optional<Long> shelveEndTime);
}
