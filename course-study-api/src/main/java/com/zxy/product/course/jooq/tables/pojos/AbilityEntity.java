/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.pojos;


import com.zxy.common.base.entity.BaseEntity;
import com.zxy.product.course.jooq.tables.interfaces.IAbility;

import java.sql.Timestamp;

import javax.annotation.Generated;


/**
 * 能力信息表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AbilityEntity extends BaseEntity implements IAbility {

    private static final long serialVersionUID = 1L;

    private Integer      level;
    private String    name;
    private String    code;
    private String    description;
    private Integer      status;
    private String    organizationId;
    private Timestamp modifyDate;
    private Integer      type;
    private Integer      abilityCategory;
    private String    abilitySubCategory;

    public AbilityEntity() {}

    public AbilityEntity(IAbility value) {
        this.level = value.getLevel();
        this.name = value.getName();
        this.code = value.getCode();
        this.description = value.getDescription();
        this.status = value.getStatus();
        this.organizationId = value.getOrganizationId();
        this.modifyDate = value.getModifyDate();
        this.type = value.getType();
        this.abilityCategory = value.getAbilityCategory();
        this.abilitySubCategory = value.getAbilitySubCategory();
    }

    public AbilityEntity(
            String    id,
            Integer      level,
            String    name,
            String    code,
            String    description,
            Integer      status,
            String    organizationId,
            Long      createTime,
            Timestamp modifyDate,
            Integer      type,
            Integer      abilityCategory,
            String    abilitySubCategory
    ) {
        super.setId(id);
        this.level = level;
        this.name = name;
        this.code = code;
        this.description = description;
        this.status = status;
        this.organizationId = organizationId;
        super.setCreateTime(createTime);
        this.modifyDate = modifyDate;
        this.type = type;
        this.abilityCategory = abilityCategory;
        this.abilitySubCategory = abilitySubCategory;
    }

    @Override
    public String getId() {
        return super.getId();
    }

    @Override
    public void setId(String id) {
        super.setId(id);
    }

    @Override
    public Integer getLevel() {
        return this.level;
    }

    @Override
    public void setLevel(Integer level) {
        this.level = level;
    }

    @Override
    public String getName() {
        return this.name;
    }

    @Override
    public void setName(String name) {
        this.name = name;
    }

    @Override
    public String getCode() {
        return this.code;
    }

    @Override
    public void setCode(String code) {
        this.code = code;
    }

    @Override
    public String getDescription() {
        return this.description;
    }

    @Override
    public void setDescription(String description) {
        this.description = description;
    }

    @Override
    public Integer getStatus() {
        return this.status;
    }

    @Override
    public void setStatus(Integer status) {
        this.status = status;
    }

    @Override
    public String getOrganizationId() {
        return this.organizationId;
    }

    @Override
    public void setOrganizationId(String organizationId) {
        this.organizationId = organizationId;
    }

    @Override
    public Long getCreateTime() {
        return super.getCreateTime();
    }

    @Override
    public void setCreateTime(Long createTime) {
        super.setCreateTime(createTime);
    }

    @Override
    public Timestamp getModifyDate() {
        return this.modifyDate;
    }

    @Override
    public void setModifyDate(Timestamp modifyDate) {
        this.modifyDate = modifyDate;
    }

    @Override
    public Integer getType() {
        return this.type;
    }

    @Override
    public void setType(Integer type) {
        this.type = type;
    }

    @Override
    public Integer getAbilityCategory() {
        return this.abilityCategory;
    }

    @Override
    public void setAbilityCategory(Integer abilityCategory) {
        this.abilityCategory = abilityCategory;
    }

    @Override
    public String getAbilitySubCategory() {
        return this.abilitySubCategory;
    }

    @Override
    public void setAbilitySubCategory(String abilitySubCategory) {
        this.abilitySubCategory = abilitySubCategory;
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("AbilityEntity (");

        sb.append(getId());
        sb.append(", ").append(level);
        sb.append(", ").append(name);
        sb.append(", ").append(code);
        sb.append(", ").append(description);
        sb.append(", ").append(status);
        sb.append(", ").append(organizationId);
        sb.append(", ").append(getCreateTime());
        sb.append(", ").append(modifyDate);
        sb.append(", ").append(type);
        sb.append(", ").append(abilityCategory);
        sb.append(", ").append(abilitySubCategory);

        sb.append(")");
        return sb.toString();
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IAbility from) {
        setId(from.getId());
        setLevel(from.getLevel());
        setName(from.getName());
        setCode(from.getCode());
        setDescription(from.getDescription());
        setStatus(from.getStatus());
        setOrganizationId(from.getOrganizationId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
        setType(from.getType());
        setAbilityCategory(from.getAbilityCategory());
        setAbilitySubCategory(from.getAbilitySubCategory());
    }

    @Override
    public <E extends IAbility> E into(E into) {
        into.from(this);
        return into;
    }

    public void forInsert() {
        this.setId(java.util.UUID.randomUUID().toString());
        this.setCreateTime(System.currentTimeMillis());
    }

    public static final <E extends AbilityEntity> org.jooq.RecordMapper<? extends org.jooq.Record, E> createMapper(Class<E> type) {
        return new org.jooq.RecordMapper<org.jooq.Record, E>() {
            @Override
            public E map(org.jooq.Record record) {
                com.zxy.product.course.jooq.tables.records.AbilityRecord r = new com.zxy.product.course.jooq.tables.records.AbilityRecord();
                org.jooq.Row row = record.fieldsRow();
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.ID) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.ID, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.ID));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.LEVEL) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.LEVEL, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.LEVEL));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.NAME) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.NAME, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.NAME));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.CODE) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.CODE, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.CODE));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.DESCRIPTION) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.DESCRIPTION, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.DESCRIPTION));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.STATUS) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.STATUS, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.STATUS));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.ORGANIZATION_ID) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.ORGANIZATION_ID, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.ORGANIZATION_ID));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.CREATE_TIME) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.CREATE_TIME, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.CREATE_TIME));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.MODIFY_DATE) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.MODIFY_DATE, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.MODIFY_DATE));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.TYPE) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.TYPE, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.TYPE));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.ABILITY_CATEGORY) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.ABILITY_CATEGORY, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.ABILITY_CATEGORY));
                }
                if(row.indexOf(com.zxy.product.course.jooq.tables.Ability.ABILITY.ABILITY_SUB_CATEGORY) > -1){
                    r.setValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.ABILITY_SUB_CATEGORY, record.getValue(com.zxy.product.course.jooq.tables.Ability.ABILITY.ABILITY_SUB_CATEGORY));
                }
                try {
                    E pojo = type.newInstance();
                    pojo.from(r);
                    return pojo;
                } catch (Exception e) {
                    e.printStackTrace(); // ignored
                }
                return null;
            }
        };
    }}
