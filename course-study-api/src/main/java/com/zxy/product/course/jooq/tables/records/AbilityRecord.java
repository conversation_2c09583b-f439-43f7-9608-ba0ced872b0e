/*
 * This file is generated by jOOQ.
 */
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.Ability;
import com.zxy.product.course.jooq.tables.interfaces.IAbility;

import java.sql.Timestamp;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record12;
import org.jooq.Row12;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 能力信息表
 */
@Generated(
        value = {
                "http://www.jooq.org",
                "jOOQ version:3.12.4"
        },
        comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class AbilityRecord extends UpdatableRecordImpl<AbilityRecord> implements Record12<String, Integer, String, String, String, Integer, String, Long, Timestamp, Integer, Integer, String>, IAbility {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_ability.f_id</code>. ID
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_id</code>. ID
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_ability.f_level</code>. 能力级别
     */
    @Override
    public void setLevel(Integer value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_level</code>. 能力级别
     */
    @Override
    public Integer getLevel() {
        return (Integer) get(1);
    }

    /**
     * Setter for <code>course-study.t_ability.f_name</code>. 能力名称
     */
    @Override
    public void setName(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_name</code>. 能力名称
     */
    @Override
    public String getName() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_ability.f_code</code>. 能力编码
     */
    @Override
    public void setCode(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_code</code>. 能力编码
     */
    @Override
    public String getCode() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_ability.f_description</code>. 描述
     */
    @Override
    public void setDescription(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_description</code>. 描述
     */
    @Override
    public String getDescription() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_ability.f_status</code>. 能力状态 0:未发布,1:发布 2:取消发布
     */
    @Override
    public void setStatus(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_status</code>. 能力状态 0:未发布,1:发布 2:取消发布
     */
    @Override
    public Integer getStatus() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_ability.f_organization_id</code>. 部门id
     */
    @Override
    public void setOrganizationId(String value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_organization_id</code>. 部门id
     */
    @Override
    public String getOrganizationId() {
        return (String) get(6);
    }

    /**
     * Setter for <code>course-study.t_ability.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(7);
    }

    /**
     * Setter for <code>course-study.t_ability.f_modify_date</code>. 修改时间
     */
    @Override
    public void setModifyDate(Timestamp value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_modify_date</code>. 修改时间
     */
    @Override
    public Timestamp getModifyDate() {
        return (Timestamp) get(8);
    }

    /**
     * Setter for <code>course-study.t_ability.f_type</code>. 来源， 0：能力管理 1：学习地图
     */
    @Override
    public void setType(Integer value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_type</code>. 来源， 0：能力管理 1：学习地图
     */
    @Override
    public Integer getType() {
        return (Integer) get(9);
    }

    /**
     * Setter for <code>course-study.t_ability.f_ability_category</code>. 能力管理大类
     */
    @Override
    public void setAbilityCategory(Integer value) {
        set(10, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_ability_category</code>. 能力管理大类
     */
    @Override
    public Integer getAbilityCategory() {
        return (Integer) get(10);
    }

    /**
     * Setter for <code>course-study.t_ability.f_ability_sub_category</code>. 能力管理子类
     */
    @Override
    public void setAbilitySubCategory(String value) {
        set(11, value);
    }

    /**
     * Getter for <code>course-study.t_ability.f_ability_sub_category</code>. 能力管理子类
     */
    @Override
    public String getAbilitySubCategory() {
        return (String) get(11);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record12 type implementation
    // -------------------------------------------------------------------------

    @Override
    public Row12<String, Integer, String, String, String, Integer, String, Long, Timestamp, Integer, Integer, String> fieldsRow() {
        return (Row12) super.fieldsRow();
    }

    @Override
    public Row12<String, Integer, String, String, String, Integer, String, Long, Timestamp, Integer, Integer, String> valuesRow() {
        return (Row12) super.valuesRow();
    }

    @Override
    public Field<String> field1() {
        return Ability.ABILITY.ID;
    }

    @Override
    public Field<Integer> field2() {
        return Ability.ABILITY.LEVEL;
    }

    @Override
    public Field<String> field3() {
        return Ability.ABILITY.NAME;
    }

    @Override
    public Field<String> field4() {
        return Ability.ABILITY.CODE;
    }

    @Override
    public Field<String> field5() {
        return Ability.ABILITY.DESCRIPTION;
    }

    @Override
    public Field<Integer> field6() {
        return Ability.ABILITY.STATUS;
    }

    @Override
    public Field<String> field7() {
        return Ability.ABILITY.ORGANIZATION_ID;
    }

    @Override
    public Field<Long> field8() {
        return Ability.ABILITY.CREATE_TIME;
    }

    @Override
    public Field<Timestamp> field9() {
        return Ability.ABILITY.MODIFY_DATE;
    }

    @Override
    public Field<Integer> field10() {
        return Ability.ABILITY.TYPE;
    }

    @Override
    public Field<Integer> field11() {
        return Ability.ABILITY.ABILITY_CATEGORY;
    }

    @Override
    public Field<String> field12() {
        return Ability.ABILITY.ABILITY_SUB_CATEGORY;
    }

    @Override
    public String value1() {
        return getId();
    }

    @Override
    public Integer value2() {
        return getLevel();
    }

    @Override
    public String value3() {
        return getName();
    }

    @Override
    public String value4() {
        return getCode();
    }

    @Override
    public String value5() {
        return getDescription();
    }

    @Override
    public Integer value6() {
        return getStatus();
    }

    @Override
    public String value7() {
        return getOrganizationId();
    }

    @Override
    public Long value8() {
        return getCreateTime();
    }

    @Override
    public Timestamp value9() {
        return getModifyDate();
    }

    @Override
    public Integer value10() {
        return getType();
    }

    @Override
    public Integer value11() {
        return getAbilityCategory();
    }

    @Override
    public String value12() {
        return getAbilitySubCategory();
    }

    @Override
    public AbilityRecord value1(String value) {
        setId(value);
        return this;
    }

    @Override
    public AbilityRecord value2(Integer value) {
        setLevel(value);
        return this;
    }

    @Override
    public AbilityRecord value3(String value) {
        setName(value);
        return this;
    }

    @Override
    public AbilityRecord value4(String value) {
        setCode(value);
        return this;
    }

    @Override
    public AbilityRecord value5(String value) {
        setDescription(value);
        return this;
    }

    @Override
    public AbilityRecord value6(Integer value) {
        setStatus(value);
        return this;
    }

    @Override
    public AbilityRecord value7(String value) {
        setOrganizationId(value);
        return this;
    }

    @Override
    public AbilityRecord value8(Long value) {
        setCreateTime(value);
        return this;
    }

    @Override
    public AbilityRecord value9(Timestamp value) {
        setModifyDate(value);
        return this;
    }

    @Override
    public AbilityRecord value10(Integer value) {
        setType(value);
        return this;
    }

    @Override
    public AbilityRecord value11(Integer value) {
        setAbilityCategory(value);
        return this;
    }

    @Override
    public AbilityRecord value12(String value) {
        setAbilitySubCategory(value);
        return this;
    }

    @Override
    public AbilityRecord values(String value1, Integer value2, String value3, String value4, String value5, Integer value6, String value7, Long value8, Timestamp value9, Integer value10, Integer value11, String value12) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        value11(value11);
        value12(value12);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    @Override
    public void from(IAbility from) {
        setId(from.getId());
        setLevel(from.getLevel());
        setName(from.getName());
        setCode(from.getCode());
        setDescription(from.getDescription());
        setStatus(from.getStatus());
        setOrganizationId(from.getOrganizationId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
        setType(from.getType());
        setAbilityCategory(from.getAbilityCategory());
        setAbilitySubCategory(from.getAbilitySubCategory());
    }

    @Override
    public <E extends IAbility> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached AbilityRecord
     */
    public AbilityRecord() {
        super(Ability.ABILITY);
    }

    /**
     * Create a detached, initialised AbilityRecord
     */
    public AbilityRecord(String id, Integer level, String name, String code, String description, Integer status, String organizationId, Long createTime, Timestamp modifyDate, Integer type, Integer abilityCategory, String abilitySubCategory) {
        super(Ability.ABILITY);

        set(0, id);
        set(1, level);
        set(2, name);
        set(3, code);
        set(4, description);
        set(5, status);
        set(6, organizationId);
        set(7, createTime);
        set(8, modifyDate);
        set(9, type);
        set(10, abilityCategory);
        set(11, abilitySubCategory);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.AbilityEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.AbilityEntity pojo = (com.zxy.product.course.jooq.tables.pojos.AbilityEntity)source;
        pojo.into(this);
        return true;
    }
}
