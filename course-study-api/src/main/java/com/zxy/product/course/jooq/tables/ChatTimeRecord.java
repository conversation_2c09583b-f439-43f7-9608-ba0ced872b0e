/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.ChatTimeRecordRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 回答问题时间差
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class ChatTimeRecord extends TableImpl<ChatTimeRecordRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_chat_time_record</code>
     */
    public static final ChatTimeRecord CHAT_TIME_RECORD = new ChatTimeRecord();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<ChatTimeRecordRecord> getRecordType() {
        return ChatTimeRecordRecord.class;
    }

    /**
     * The column <code>course-study.t_chat_time_record.f_id</code>.
     */
    public final TableField<ChatTimeRecordRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_chat_time_record.f_answer_id</code>. 回答id，第三方主键ID
     */
    public final TableField<ChatTimeRecordRecord, String> ANSWER_ID = createField("f_answer_id", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "回答id，第三方主键ID");

    /**
     * The column <code>course-study.t_chat_time_record.f_time</code>. 思考时间 时间毫秒
     */
    public final TableField<ChatTimeRecordRecord, Long> TIME = createField("f_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "思考时间 时间毫秒");

    /**
     * The column <code>course-study.t_chat_time_record.f_create_time</code>. 创建时间
     */
    public final TableField<ChatTimeRecordRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_chat_time_record.f_modify_date</code>. 更新时间
     */
    public final TableField<ChatTimeRecordRecord, Long> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "更新时间");

    /**
     * Create a <code>course-study.t_chat_time_record</code> table reference
     */
    public ChatTimeRecord() {
        this("t_chat_time_record", null);
    }

    /**
     * Create an aliased <code>course-study.t_chat_time_record</code> table reference
     */
    public ChatTimeRecord(String alias) {
        this(alias, CHAT_TIME_RECORD);
    }

    private ChatTimeRecord(String alias, Table<ChatTimeRecordRecord> aliased) {
        this(alias, aliased, null);
    }

    private ChatTimeRecord(String alias, Table<ChatTimeRecordRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "回答问题时间差");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<ChatTimeRecordRecord> getPrimaryKey() {
        return Keys.KEY_T_CHAT_TIME_RECORD_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<ChatTimeRecordRecord>> getKeys() {
        return Arrays.<UniqueKey<ChatTimeRecordRecord>>asList(Keys.KEY_T_CHAT_TIME_RECORD_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ChatTimeRecord as(String alias) {
        return new ChatTimeRecord(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public ChatTimeRecord rename(String name) {
        return new ChatTimeRecord(name, null);
    }
}
