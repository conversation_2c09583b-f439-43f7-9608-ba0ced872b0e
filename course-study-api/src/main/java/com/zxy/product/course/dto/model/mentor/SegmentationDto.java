package com.zxy.product.course.dto.model.mentor;

import java.io.Serializable;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/14:20
 * @Description:
 */
public class SegmentationDto implements Serializable {
    private static final long serialVersionUID = 1968075406854370536L;

    private String separator;
    private Integer max_tokens;
    private Integer chunk_overlap;

    public String getSeparator() {
        return separator;
    }

    public void setSeparator(String separator) {
        this.separator = separator;
    }

    public Integer getMax_tokens() {
        return max_tokens;
    }

    public void setMax_tokens(Integer max_tokens) {
        this.max_tokens = max_tokens;
    }

    public Integer getChunk_overlap() {
        return chunk_overlap;
    }

    public void setChunk_overlap(Integer chunk_overlap) {
        this.chunk_overlap = chunk_overlap;
    }
}
