package com.zxy.product.course.content;

import com.zxy.common.base.exception.Code;

/**
 * Created by keeley on 16/9/14.
 */
public enum ErrorCode implements Code{
    CanNotFindMember(20114, "can not find member"),

    ImportQuestionOptionsError(30101, "import options of question error"),
    ImportQuestionAnswerErrorRange(30102, "import answer of question error range"),
    ImportQuestionIndexError(30130,"import index of question error"),						// 序号格式错误
    ImportSentenceCompeletionWithChinesebracket(30103, "import sentenceCompetion with chinese bracket"),
    ImportSentenceCompletionWithOutEnglishBracket(30104, "import sentenceCompetion without english bracket"),
    ImportContentOverSize(30122, "import question content over size"),

    DataViolation(910000, "Data Violation Error"),
    requireViolation(900001, "属性值必填"),

    CourseProcessMemberExists(40101, "用户已经注册了"),
    CheckSourceFail(40102, "课程来源范围不正确"),
    CheckClientFail(40103, "发布终端范围不正确"),
    CheckCatalageExists(40104, "目录编码不存在"),
    CheckTopicExists(40105, "标签不存在"),
    CheckOrganizationsExists(40106, "发布部门不存在"),
    CheckRowTooLong(40107, "批量更新人数过多，请先进行条件筛选"),
    CheckDeputyCategorySize(40108,"deputyCategories over size"),

    CanNotFindStagesElements(40201, "can not find stages elements"),
    JsonTransformFail(40202, "json transform fail"),
    NoticeTextNotNull(40203, "notice text not null"),
    SubjectStyleNotNull(40204, "subject style not null"),
    GetCourseInfoIsNull(40205, "get course is null"),
    CourseInfoShelves(40206, "course shelves"),
    AudienceItemsNotNull(40207, "audience items not null"),
    AlreadyScore(40208, "has already score it"),
    AttachmentNotExtends(40209, "attachment not extends"),
    DocumentNoTranslateSuccess(40210, "document no translate success"),
    PublishFail(40211,"publish fail"),
    INTEGRALNOTENOUGH(40212,"积分不足"),
    ScormNoTranslateSuccess(40213, "scorm文件还没转换成功"),
    PushStartTimeLtCurrent(40214, "Push start time is not less than the current time"),
    CourseInfoNotInShelvesOrIsNull(40215,"课程未发布或者资源不存在"),
    DoesNotMeetExamOperation(40216,"课程不满足考试操作"),

    PushCantBeRemoved(40301, "push can't be removed"),
    PushNameRepeat(40302, "push name repeat"),
    NameAlreadyExists(40303,"name already exists"),
    GenseCreatedFailed(40304,"gensee created failed"),
    GenseExceedAttendNumber(40305,"gensee attend number greater than plan number"),
    CodeAlreadyExists(40306,"code already exists"),
    MaxnumError(40307,"目录最深为4级"),
    ParentNotExists(40310,"父目录不存在"),
    GenseNotPublish(40308,"gensee not publish"), // 直播没有发布或者已经撤销发布
    GenseAccessNotAllowed(40309,"gensee access not allowed"), // 没有权限访问
    GenseLecturerLimit(40311,"gensee lecturer gt limit"), // 直播讲师超出最大人数限制
    GenseNameExists(40312,"直播名称在系统内已存在，不允许重名"), // 直播名称在系统内已存在，不允许重名
    GenseeNotAllowedDelete(40313,"有人参与直播，不允许删除"), // 有人参与直播，不允许删除
    GenseeGetOnlineUsers(40314,"向云中心获取在线人数异常"),
    GenseeNotEnoughIntegral(40315,"积分不足"),
    GenseeHasBeenRelatedToCourse(40316,"该直播已关联课程"),
    MemberHasSubscribedToGensee(40317,"已订阅直播,请勿重复订阅"),
    GenseeNotStartStatus(40318, "直播不是未开始状态,不能取消发布"),
    GenseeNotPublishAgain(40319, "直播不是取消发布状态，重新发布失败"),
    NoPermissionToBroadcast(40320, "您暂无该直播学习权限"),

    NoRegister(40409, "member not regist course"),

    ImportSequenceError(40503,"排序格式错误"),
    KnowledgeCategoryCodeError(40502, "目录code格式错误"),
    CourseCategoryCodeExists(40501, "目录code不能重复"),
    ImportNullFile(40504, "导入文档不能为空"),
    EXCEL_NOTSUPPORT(40505, "文档不符合规范"),
    EXCEL_ERROR(40506, "模板错误，请导入正确模板"),
    IMPORT_RECORDS_OVER_SIZE(40507,"导入记录条数超过限制"),

    OrgCanNotDeleteSinceCourseReleated(40601, "can not delete, cause this org has course releated"),
    OrgCanNotDeleteSinceGenseeReleated(40602, "can not delete, cause this org has gensee releated"),
    OrgCanNotDeleteSincePushReleated(40603, "can not delete, cause this org has study push releated"),
    OrgCanNotDeleteSinceCategoryReleated(40604, "can not delete, cause this org has course category releated"),

    ImportCreateErrorFile(40701, "错误文件生成失败，您暂时不能下载"),
    HaveAuditAssignmentsYouSubmit(40702, "作业已审核，不可重复审核"),
    KnowledgeNotExists(40801, "该资源不存在"),
    KnowledgeNotAudience(40802, "您暂无该资源学习权限，去看看其它资源吧"),
    KnowledgeHasBeenDeleted(40803, "该知识已删除"),
    KnowledgeHasBeenEssence(40804, "该知识已加精"),
    KnowledgeHasBeenUnEssence(40805, "该知识已取消加精"),
    KnowledgeHasBeenTop(40806, "该知识已置顶"),
    KnowledgeHasBeenUnTop(40807, "该知识已取消置顶"),
    KnowledgeDownloadNotAllowed(40808, "该知识不允许下载"),
    KnowledgeTagHasBeenDeleted(40809, "该推荐标签已删除"),
    KnowledgeTagNotPublish(40810, "未发布的标签不能撤销"),
    KnowledgeTagCanNotDelete(40811, "已发布的标签不能删除"),
    ExampleCaseHasBeenDeleted(40812, "该案例已删除"),
    ExampleCaseNotPublish(40813, "未发布的案例不能撤销"),
    ExampleCaseCanNotDelete(40814, "已发布的案例不能删除"),

    CourseNotAudience(40901, "用户不在受众对象"),
    CourseNotRegist(40902, "课程没有注册"),
    CourseMulipleStudy(40903,"该课程已经在学习"),
    CourseUnsafe(40904,"该课程记录不安全"),
    CourseNotEnoughIntegral(40905,"积分不足"),
    CourseHasBeenRelateGensee(40906,"该课程已关联直播"),
    CourseHasBeenUnrelateGensee(40907,"该课程没有关联直播"),
    CourseStatusIsAnnouncing(40908, "该课程正在发布，请稍后再试"),


    ResearchDoesNotExist(41001, "调研活动不存在"),
    ExamDoesNotExist(41002, "考试不存在"),
    UnableToSignUpForStudyForTheTimeBeing(41003, "暂时无法报名学习"),
    FailureToSignUpForCertificate(41004, "未报名无法领取证书"),
    FailureToQualifyForCertificate(41005, "未达到领取证书资格"),
    SubjectIsReferenced(42001, "该专题已被引用，无法添加专题"),
    SubjectTypeChapterIsReferenced(42002, "无法添加引用过其他专题的专题"),
    SubjectChaptersSelectAtLeastOneCompulsoryChapter(42003, "至少选择一个必修章节"),

    ParamTypeAndSubjectIdNotAllNull(43001,"专区类型和专题id不能同时为空"),
    SubjectCertificateHasBeenIssuedCannotBeDeleted(43002, "该专题已发放证书无法删除证书"),
    ExportSignError(44001,"导出失败"),
    CHBNNotQualifiedToReceiveCertificate(44002, "CHBN未达到获取证书的资格"),
    ZHZTNotQualifiedToReceiveCertificate(44005, "智慧中台未达到获取证书的资格"),
    CHBNTheCertificateHasBeenReceivedPleaseDoNotRepeatTheOperation(44003, "CHBN证书已领取，请勿重复操作"),
    ZHZTTheCertificateHasBeenReceivedPleaseDoNotRepeatTheOperation(44006, "智慧中台证书已领取，请勿重复操作"),
    CHBNCertificateIsBeingGeneratedTryAgainLater(44004, "证书正在生成中，稍后重试"),
    ZHZTNotFindBanner(44007, "智慧中台未获取到banner相关信息"),
    DJYPDataNotNull(44100,"list不能为空"),
    DJYPExceedMax(44101,"超过最大限制数量"),
    DJDataNotVerify(44102,"验证数据合法性"),
    DJDataError(44103,"数据异常"),
    ScoreError(44104,"评分分数异常"),

    QuestionnaireIsSubmit(45001,"问卷已提交"),
    QuestionnaireFileIsEmpty(45002,"导出文件为空"),

    RemodelingRepeatRequestId(46001, "requestId重复"),
    OfflineClassMemberDataIsWrong(47001,"线下课程记录表人员数据错误"),
    OfflineClassSupplierCodeIsWrong(47002,"线下课程记录表供应商类型错误"),
    OfflineClassCourseDataIsWrong(47003,"线下课程记录表课程信息错误"),
    OfflineClassSubjectDataIsWrong(47004,"线下课程记录表专题数据错误"),
    OfflineClassRecordIsRepeat(47005,"线下课程记录表数据重复"),
    offlineClassExcelIsRepeat(47006,"线下课程记录excel数据重复"),
    SelectATimeAfterTheCurrentTime(47007,"请选择当前时间后的时间"),
    TheEndTimeCannotBeEarlierThanTheStartTime(47008,"结束时间不能早于开始时间"),
    EnterAnIntegerBetween0And300(47009,"计划人数不能大于300"),
    MeetingCreateFail(47010,"会议创建失败"),
    MeetingUpdateFail(47011,"会议更新失败"),
    MeetingDeleteFail(47012,"会议删除失败"),
    MeetingNotExist(47013,"会议不存在"),
    MeetingNoPermission(47014,"没有此会议的操作权限"),
    StartTimeLessThanCurrentTime(47015,"会议预约时间不能小于当前时间"),
    DurationLessThanThirtyMinute(47016,"会议时长不能小于 30 分钟"),
    DurationMoreThanSixHour(47017,"会议时长不能大于 8 个小时"),
    DurationMoreThanSixtyDay(47018,"会议预约时间必须在 60 天内"),

    //咪咕直播
    EnterAnIntegerBetween0And99999(47019,"计划人数不能大于99999"),
    MaxLecturersNumsLessThan10(47020,"讲师数量不能大于10"),
    OnlyAllowDeleteStatusNotPublish(47021,"只允许删除未发布的直播"),
    MiguPublishFailed(47022,"直播间发布失败"),
    NonPublicLivingNotAllowAudiencesIsNull(47023,"非公开直播受众项不能为空"),
    GetMiguSSOTicketFailed(47024,"获取咪咕单点登录凭证失败"),
    LiveStartTimeLessThanCurrentTime(47025,"开始和结束时间不能小于当前时间"),
    UsersDoNotHaveAccessThisLive(47026, "用户无权观看该直播"),
    LiveBroadcastNotYetPublish(47027, "直播尚未发布"),
    MiguUpdateFailed(47028,"修改咪咕直播间出错"),
    CourseHaveBeanRelatedMiGuLiving(47029,"已有课程关联直播"),
    PullStreamMustBeTurnedOffAfterFifteenSeconds(47030,"拉流直播开启15秒后才可关闭"),
    FailedToEnablePullStreamLive(47031,"开启拉流直播失败"),
    FailedToDisablePullStreamLive(47032,"关闭拉流直播失败"),
    MiGuLivingHaveBeanRelatedCourse(47033,"直播已关联相关课程"),
    MemberNotInGrantOrg(47010,"人员归属单位错误"),
    //课程举报
    hasBeenAudit(49001, "audit has been audited"),//已经审核过了
    AuditNotExist(49002, "The audit resource does not exist"),//审核资源不存在
    AccuseDuplicate(49003,"you accuse duplicate"), //重复举报
    AccuseNotExist(49004, "The reported resource does not exist"),//举报的课程不存在
    ReportRecordDoesNotExist(49005,"Report record does not exist"),//举报记录不存在
    //云视讯
    FailedToAccessCloudVideoInterface(49006,"Failed to access cloud video interface"),//访问云视讯接口失败

    ActivityTitleExists(48001,"学习活动标题已存在,请重新输入"),
    ActivityNotAllowedDelete(48002,"无法删除，请下线后再操作"),
    ActivityConfigNotExists(48003,"学习活动配置记录不存在"),
    ActivityConfigStatus(48004,"学习活动配置状态未改变，不可重复操作"),
    NetworkFluctuations(48005,"网络波动，请刷新页面重试"),

    //重塑专区添加黑名单
    TheBlackListAlreadyExists(49101,"黑名单中存在在!"),

    // 证书导入
    CertificateMemberNotExists(49201, "import member not exists"), // 人员不存在
    SubjectNotExists(49202, "subject not exists"), // 专题不存在
    SubjectNotHaveCertificate(49203, "subject not have certificate"), // 专题未配置证书
    SubjectNotFinish(49204, "subject not finish"), // 该专题未完成学习
    CertificateHaveExists(49205, "certificate have exists"), // 证书重复
    CertificateCodeGeneratError(49206, "import certificate code generate error"),// 证书编号生成失败

    // 红船课程审核
    CheckAuditStatusError(49401,"复核状态异常"),
    CheckAuditStatusIsOver(49402,"管理员已进行复核"),
    RedAuditStatusError(49403,"红船审核状态异常，无法复核"),
    CourseRedShipAuditClosed(49404,"红船课程审核未开启"),
    CourseRedShipAuditSectionNotChoice(49405,"未选择需要审核的课件"),
    CourseRedShipAuditContentError(49406,"审核内容选择错误"),
    CourseRedShipAuditStatusIsAuditing(49407,"该课程上次红船审核状态为审核中，不允许再次审核。"),
    CourseRedShipAuditStatusCheckStatusError(49408,"未对红船审核不通过的内容进行复核，不允许再次审核。"),
    CourseRedShipAuditStatusIsPreparing(49409,"该课程上次红船审核状态为准备中，不允许再次审核。"),
    CourseRedShipAuditStatusIsPreparationFailed(49410,"该课程上次红船审核状态为准备失败，不允许再次审核。"),
    CourseRedShipAuditStatusError(49411,"该课程上次红船审核状态异常，无法重新发起"),

    //多维度评分
    NameRepetition(49301,"评分表名字重复!"),
    HasBeenUsed(49302,"该评分表正在使用中，无法修改/删除!"),
    TheModifiedDataDoesNotExist(49303,"修改数据不存在!"),

    MemberDisabled(46100, "member is disabled"),//用户不可用
    MemberNotFound(46101, "member not found"),//用户不存在
    // IHR验证ticket接口异常
    IhrCheckTicketError(46102, "Ihr check ticket error"),
    ExportNoDataError(46103, "no data"),//多维度评分导出数据为空
    ExportExceptionalError(46104, "Export the Exceptional"),//多维度评分导出异常
    //学习计划
    PushingTryAgainLater(44008, "学习计划推送中，稍后重试"),
    UpdatingTryAgainLater(44009, "学习计划更新中，稍后重试"),
    revokeingTryAgainLater(44010, "学习计划撤销中，稍后重试"),


    FileDoesNotExist(49001,"文件不存在"),
    TheFileTypeDoesNotMatch(49002,"文件不存在"),
    ThereIsNoText(49003,"没有文本信息"),
    NotesIsNotExist(42005,"笔记不存在"),
    NameOrFullNameError(30608,"员工姓名或者编码填写错误"),
    RecordOfRepetition(42006,"一个课件每人只能发布一条笔记"),
    NotForAFeatureAudience(42007,"不是该专题编码的受众"),
    NotesNonNull(42008,"笔记不能为空"),
    ExportMoreThan5000(5000,"单次最多可导出5000条数据，请修改筛选条件后重试"),

    SubtitleUpdateFailed(50007,"字幕更新失败"),
    SmartDigestIsNotPublished(50009,"智能摘要未发布"),
    SmartSummaryIsUnchecked(50010,"智能摘要不能发布未选中的当前版本"),

    SubAuthenticatedConfigContentTypeIsNull(51001,"contentType为空或类型错误"),
    SubAuthenticatedConfigContentNameIsNull(51002,"contentName为空"),
    SubAuthenticatedConfigDimensionNameIsNull(51003,"dimensionName为空"),
    SubAuthenticatedConfigDimensionCodeIsNull(51004,"dimensionCode为空"),
    SubAuthenticatedConfigOrderIsNull(51005,"order为空"),
    SubAuthenticatedConfigBusinessIdIsNull(51006,"businessId为空"),
    SubAuthenticatedConfigBusinessTypeIsNull(51007,"businessType为空"),
    SubAuthenticatedConfigBusinessNameIsNull(51080,"businessName为空"),
    HaveRepeatedExamsInTheExaminationGroup(51081,"考试组内有重复考试"),
    HaveRepeatedExamsInTheSubAuthenticated(51082,"子认证内有重复考试"),
    ExamsInSubCertificationsHaveBeenReferencedByOtherSubCertifications(51083,"子认证中的考试已被其他子认证引用"),
    SubAuthenticatedPublishError(51008,"发布状态错误"),
    SubAuthenticatedNotExistError(51009,"子认证不存在"),
    TheEmployeeNumberDoesNotExist(51010,"员工编号不存在"),
    TheEmployeeNumberOrNameIsIncorrect(51011,"员工编号或姓名填写错误"),
    CertificateValidationFailed(51025,"证书校验未通过"),
    TheAuthenticationDimensionStatusIsIncorrect(51012,"认证维度状态填写错误"),
    SubAuthenticatedIsRegistering(51013,"正在注册请稍等"),
    SubAuthenticatedIsNull(51014,"子认证不存在"),
    SubAuthenticatedIsExporting(51015,"导出中，请在队列查看。"),
    PublicationRestriction(5001, "开关开启,白天不允许发布"),
    SubAuthenticatedOnlineIsNull(51016,"请添加学习资源。"),
    SubAuthenticatedDimensionIsNull(51017,"请添加认证维度资源。"),
    AuthenticatedGroupIsNull(51018,"分组为空"),
    TheLearningStatusIsIncomplete(51019,"学习状态为未完成。"),
    TheLearningStatusDoesNotExist(510120,"学习状态不存在。"),
    CertificateImporting(51021,"证书导入中。"),
    YouDoNotHavePermissionToTheLearningResource(51022,"您无该学习资源权限。"),
    YouDoNotHavePermissionToImportResource (51024,"您无该资源导入权限"),
    TheNumberOfGroupsExceedsTheMaximum(50002, "最多添加50个分组"),
    GroupNameIsEmpty(50003, "组名称不能为空"),
    GroupNameIsExist(50004, "组名称重复"),
    GroupNotifyToLong(50005, "组通告超级长"),
    GroupAudiencesIsEmpty(50005, "组受众不能为空"),
    TheContentOfTheCertificationAreaIsNotVisible(60000, "认证专区内容不可见"),
    ContentNotCompleted(50006, "组内容未完成"),
    reportRedlistMemberIsAlreadyExists(58001,"该人员已存在"),
    IntelligentNoteMemberNotMatch(52001,"笔记归属人不匹配"),
    IntelligentNoteContentEmpty(52002,"笔记内容为空"),
    IntelligentNoteNotExsit(52003,"笔记不存在"),
    IntelligentNoteOutLength(52004,"笔记内容长度超出范围"),
    IntelligentNoteInAudit(52005,"笔记处于审核中"),
    IntelligentNoteTopNum(52006,"置顶笔记已经存在5条"),
    NoUpdate(53000,"没有更新内容"),
    KNOWLEDGE_UNDER_REVIEW(53001,"知识审核中"),

    BANNER_NOT_EXIST(54001,"当前banner对象不存在"),
    BANNER_STATE_PUBLISHED(54002,"banner为已发布时不可修改"),
    BANNER_STATE_UNPUBLISHED(54003,"banner为未发布时不可排序"),

    NEWS_NOT_EXIST(55001,"当前党校动态对象不存在"),
    NEWS_STATE_PUBLISHED(55002,"当前动态为已发布时不可修改"),


    /**预设问题主键不能为空*/
    PresetQuestionsPrimaryKeyCannotBeEmpty(56000,"PresetQuestionsPrimaryKeyCannotBeEmpty"),
    /**预设问题不能为空*/
    PresetQuestionsCannotBeEmpty(56001,"PresetQuestionsCannotBeEmpty"),
    /**预设问题最大长度超限*/
    MaximumLengthPresetQuestionExceeded(56002,"MaximumLengthPresetQuestionExceeded"),
    /**预设答案不能为空*/
    PresetAnswerCannotBeEmpty(56003,"PresetAnswerCannotBeEmpty"),
    /**预设答案最大长度超限*/
    MaximumLengthPresetAnswerExceeded(56004,"MaximumLengthPresetAnswerExceeded"),
    /**调整类型不能为空*/
    AdjustTypeCannotBeEmpty(56005,"AdjustTypeCannotBeEmpty"),
    /**无法识别的调整类型*/
    AdjustmentTypeIsNotRecognized(56006,"AdjustmentTypeIsNotRecognized"),
    /**调整理由不能为空*/
    AdjustReasonCannotBeEmpty(56007,"AdjustReasonCannotBeEmpty"),
    /**调整理由最大长度超限*/
    MaximumLengthAdjustReasonExceeded(56008,"MaximumLengthAdjustExceeded"),
    /**预设问答主键查询无数据*/
    ThereIsNoDataForPresetQaPrimaryKeyQuery(56009,"MaximumLengthAdjustExceeded"),
    /**数智导师主键不能为空*/
    MentorPrimaryKeyCannotBeEmpty(56010,"MentorPrimaryKeyCannotBeEmpty"),
    /**资源Id不能为空*/
    ResourceIdCannotBeEmpty(56011,"ResourceIdCannotBeEmpty"),
    /**用户提问问题不能为空*/
    MemberAskQuestionCannotBeEmptyOrAskQuestionTheThresholdIsExceeded(56012,"MemberAskQuestionCannotBeEmptyOrAskQuestionTheThresholdIsExceeded"),
    /**用户反馈内容最大长度超限*/
    MaximumLengthFeedbackContentExceeded(56013,"MaximumLengthFeedbackContentExceeded"),
    /**无法识别的体验类型*/
    ExperienceIsNotRecognized(56014,"ExperienceIsNotRecognized"),
    /**无法识别的反馈类型*/
    FeedbackTypeIsNotRecognized(56015,"FeedbackTypeIsNotRecognized"),
    /**反馈主键不能为空*/
    FeedbackPrimaryKeyCannotBeEmpty(56016,"FeedbackPrimaryKeyCannotBeEmpty"),
    /**反馈主键查询无数据*/
    ThereIsNoDataForFeedbackPrimaryKeyQuery(56017,"ThereIsNoDataForFeedbackPrimaryKeyQuery"),
    /**数智导师关联主键查询无数据*/
    ThereIsNoDataForMentorPrimaryKeyQuery(56018,"ThereIsNoDataForMentorPrimaryKeyQuery"),
    /**点赞点踩等操作已经超出阈值*/
    TheLikesAndDislikesHaveExceededTheOperationThreshold(56019,"TheLikesAndDislikesHaveExceededTheOperationThreshold"),
    /**用户提问答案主键查询无数据*/
    ThereIsNoDataForAnswerPrimaryKeyQuery(56020,"ThereIsNoDataForAnswerPrimaryKeyQuery"),
    /**数智导师答案主键为空或者入参参数不等于答案主键*/
    MentorAnswerPrimaryKeyIsEmptyOrPrimaryKeyIsNotEqualToTheInputParam(56021,"MentorAnswerPrimaryKeyIsEmptyOrPrimaryKeyIsNotEqualToTheInputParam"),
    /**
     * 学习助手应用配置最多可激活12个
     */
    APPLICATION_CONFIG_ON_EXCEED_MAX(56022,"application config on exceed max"),
    /**
     * 学习助手应用配置至少有2个激活
     */
    APPLICATION_CONFIG_OFF_EXCEED_MAX(56023,"application config off exceed max"),


    CourseNameCanNotRepeat(57001, "Course name can not repeat"),
    ThisCourseAlreadyHasAssociatedDataCannotBeDeleted(57002, "This course already has associated data cannot be deleted"),
    LecturerCodeCanNotRepeat(57003, "Lecturer code can not repeat"),
    ThisLecturerAlreadyHasAssociatedDataCannotBeDeleted(57004, "This lecturer already has associated data cannot be deleted"),
    ClassificationNameCanNotRepeat(57005, "Course classification name can not repeat"),
    ThisClassificationAlreadyHasAssociatedDataCannotBeDeleted(57006, "This classification already has associated data cannot be deleted"),
    CourseMiddleCanNotRepeat(57007, "Course middle can not repeat"),
    CourseNameRequired(57008, "课程库课程名称为必填"),
    InvalidExcelTemplate(57009, "Invalid excel template"),
    ExcelRowsRepeatCourseName(57010, "导入表格存在相同的课程库课程名称"),
    LecturerNameRequired(57011, "讲师库讲师名称为必填"),
    ExcelRowsRepeatLecturerCode(57012, "导入表格存在相同的讲师库讲师编码"),
    LecturerCodeRepeat(57013, "讲师编码不存在"),
    LecturerCodeRequired(57014, "讲师库讲师编码为必填"),
    LecturerUnitRequired(57015, "讲师库讲师单位为必填"),
    CourseCodeRepeat(57016, "课程编码不存在"),
    ClassificationCodeRequired(57017, "课程分类编码为必填"),
    ClassificationCodeRepeat(57018, "课程分类编码不存在"),
    MoveContentNonentity(57019,"移动内容不存在"),
    GbMemberNonentity(57020,"当前学员不是管理员"),
    CourseCodeRequired(57021, "课程编码为必填"),
    CourseMiddleRepeat(57022, "课程关联重复"),
    LecturerCodeAlphaNumeric(57024, "讲师编码只能填写数字或者字母"),
    PlanHasBeanSubjectRelated(57025, "该计划已被专题关联，无法删除"),
    lecturerSatisfiedError(57026, "授课满意度格式错误"),
    memberNameNotExists(57027, "约课人员工编号不存在"),
    courseDateStrError(57028, "授课日期格式错误"),
    createBeginDateError(57029, "约课时间格式错误"),
    courseRecordDataRepeat(57030, "约课记录数据重复"),
    MemberNameRequired(57031, "课程编码为必填"),
    MemberNotAuthorization(57032, "员工编号不在权限范围内"),
    MemberNotExists(57033, "员工编号不存在"),
    OrgnizationNotExists(57034, "管理单位编码不存在"),
    DateStrNotExists(57035, "必填项未填写"),
    NotPermission(57036, "当前用户无导入权限"),
    NumberError(57037, "培训人数格式错误"),
    DataError(57038, "数据格式错误"),





    // 短视频运营错误码开始
    MemberIdIsNull(57101,"MemberIdIsNull"),
    TypeIsNull(57102,"TypeIsNull"),
    ShortVideosNotBeAdded(57103,"最多可添加20个短视频"),
    CategoryNamesCannotBeRepeated(57104,"分类名称不能重复"),
    CategoryNumbersCannotBeRepeated(57105,"分类编号不能重复"),
    UpTo20CategoriesCanBeAdded(57026,"最多可添加20个分类"),
    ContainsQuestionsAndCannotBeDeleted(57107,"该分类下有痛点问题，不可删除"),
    YouDoNotHavePermissionToReview(57108,"您没有权限审核"),
    YouDoNotHavePermission(57109,"您没有权限提交问题"),
    // 短视频运营错误码结束

    //能力管理
    CODE_ALREADY_EXISTS(40306,"code already exists"),
    ABILITY_NO_RELATION_BUSINESS(40140, "能力未关联必修内容，无法发布"),
    ABILITY_BUSINESS_REPEAT(40141, "能力关联的内容有重复"),
    NUMBER_EXCEEDED_LIMIT(40118, "数量超出限制"),
    ABILITY_TO_APPLY_CANNOT_BE_DELETED(40212, "能力有应用无法删除"),

    /**在线学习人数过多，保证您的学习体验，平台采取定向分流措施，请稍后再来*/
    TooManyLearnersPleaseComeBackLaterEnsureYourLearningExperience(58000,"TooManyLearnersPleaseComeBackLaterEnsureYourLearningExperience"),

    DataDuplication(59000,"数据重复"),

    deleteFileFail(40299, "删除网大文件失败"),
    uploadFileFail(402100, "添加网大文件失败"),
    entityIsNull(402101, "查询对象为空"),
    entityForbbidUpdate(402102, "改对象已有反馈评论，不能修改"),
    ModelMentorTutorAIQANetworkFailure(402199,"AI问答网络故障"),
    ;

    private final int code;
    private final String desc;

    ErrorCode(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    @Override
    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
