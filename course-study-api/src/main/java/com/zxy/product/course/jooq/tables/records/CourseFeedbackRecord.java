/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables.records;


import com.zxy.product.course.jooq.tables.CourseFeedback;
import com.zxy.product.course.jooq.tables.interfaces.ICourseFeedback;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Record1;
import org.jooq.Record10;
import org.jooq.Row10;
import org.jooq.impl.UpdatableRecordImpl;


/**
 * 意见反馈
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseFeedbackRecord extends UpdatableRecordImpl<CourseFeedbackRecord> implements Record10<String, String, String, String, String, Integer, Integer, String, Long, Long>, ICourseFeedback {

    private static final long serialVersionUID = 1L;

    /**
     * Setter for <code>course-study.t_course_feedback.f_id</code>.
     */
    @Override
    public void setId(String value) {
        set(0, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_id</code>.
     */
    @Override
    public String getId() {
        return (String) get(0);
    }

    /**
     * Setter for <code>course-study.t_course_feedback.f_question_id</code>. 问题id
     */
    @Override
    public void setQuestionId(String value) {
        set(1, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_question_id</code>. 问题id
     */
    @Override
    public String getQuestionId() {
        return (String) get(1);
    }

    /**
     * Setter for <code>course-study.t_course_feedback.f_question_content</code>. 问题内容
     */
    @Override
    public void setQuestionContent(String value) {
        set(2, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_question_content</code>. 问题内容
     */
    @Override
    public String getQuestionContent() {
        return (String) get(2);
    }

    /**
     * Setter for <code>course-study.t_course_feedback.f_answer_content</code>.
     */
    @Override
    public void setAnswerContent(String value) {
        set(3, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_answer_content</code>.
     */
    @Override
    public String getAnswerContent() {
        return (String) get(3);
    }

    /**
     * Setter for <code>course-study.t_course_feedback.f_feedback_content</code>. 反馈内容
     */
    @Override
    public void setFeedbackContent(String value) {
        set(4, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_feedback_content</code>. 反馈内容
     */
    @Override
    public String getFeedbackContent() {
        return (String) get(4);
    }

    /**
     * Setter for <code>course-study.t_course_feedback.f_feedback_type</code>. 反馈类型 0 内容不全面、1观点有错误、2其他
     */
    @Override
    public void setFeedbackType(Integer value) {
        set(5, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_feedback_type</code>. 反馈类型 0 内容不全面、1观点有错误、2其他
     */
    @Override
    public Integer getFeedbackType() {
        return (Integer) get(5);
    }

    /**
     * Setter for <code>course-study.t_course_feedback.f_like</code>. 0 点踩 1 点赞
     */
    @Override
    public void setLike(Integer value) {
        set(6, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_like</code>. 0 点踩 1 点赞
     */
    @Override
    public Integer getLike() {
        return (Integer) get(6);
    }

    /**
     * Setter for <code>course-study.t_course_feedback.f_create_member_id</code>. 创建人
     */
    @Override
    public void setCreateMemberId(String value) {
        set(7, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_create_member_id</code>. 创建人
     */
    @Override
    public String getCreateMemberId() {
        return (String) get(7);
    }

    /**
     * Setter for <code>course-study.t_course_feedback.f_create_time</code>. 创建时间
     */
    @Override
    public void setCreateTime(Long value) {
        set(8, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_create_time</code>. 创建时间
     */
    @Override
    public Long getCreateTime() {
        return (Long) get(8);
    }

    /**
     * Setter for <code>course-study.t_course_feedback.f_modify_date</code>. 更新时间
     */
    @Override
    public void setModifyDate(Long value) {
        set(9, value);
    }

    /**
     * Getter for <code>course-study.t_course_feedback.f_modify_date</code>. 更新时间
     */
    @Override
    public Long getModifyDate() {
        return (Long) get(9);
    }

    // -------------------------------------------------------------------------
    // Primary key information
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Record1<String> key() {
        return (Record1) super.key();
    }

    // -------------------------------------------------------------------------
    // Record10 type implementation
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, String, String, Integer, Integer, String, Long, Long> fieldsRow() {
        return (Row10) super.fieldsRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Row10<String, String, String, String, String, Integer, Integer, String, Long, Long> valuesRow() {
        return (Row10) super.valuesRow();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field1() {
        return CourseFeedback.COURSE_FEEDBACK.ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field2() {
        return CourseFeedback.COURSE_FEEDBACK.QUESTION_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field3() {
        return CourseFeedback.COURSE_FEEDBACK.QUESTION_CONTENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field4() {
        return CourseFeedback.COURSE_FEEDBACK.ANSWER_CONTENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field5() {
        return CourseFeedback.COURSE_FEEDBACK.FEEDBACK_CONTENT;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field6() {
        return CourseFeedback.COURSE_FEEDBACK.FEEDBACK_TYPE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Integer> field7() {
        return CourseFeedback.COURSE_FEEDBACK.LIKE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<String> field8() {
        return CourseFeedback.COURSE_FEEDBACK.CREATE_MEMBER_ID;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field9() {
        return CourseFeedback.COURSE_FEEDBACK.CREATE_TIME;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Field<Long> field10() {
        return CourseFeedback.COURSE_FEEDBACK.MODIFY_DATE;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value1() {
        return getId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value2() {
        return getQuestionId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value3() {
        return getQuestionContent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value4() {
        return getAnswerContent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value5() {
        return getFeedbackContent();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value6() {
        return getFeedbackType();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Integer value7() {
        return getLike();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String value8() {
        return getCreateMemberId();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value9() {
        return getCreateTime();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Long value10() {
        return getModifyDate();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value1(String value) {
        setId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value2(String value) {
        setQuestionId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value3(String value) {
        setQuestionContent(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value4(String value) {
        setAnswerContent(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value5(String value) {
        setFeedbackContent(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value6(Integer value) {
        setFeedbackType(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value7(Integer value) {
        setLike(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value8(String value) {
        setCreateMemberId(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value9(Long value) {
        setCreateTime(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord value10(Long value) {
        setModifyDate(value);
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseFeedbackRecord values(String value1, String value2, String value3, String value4, String value5, Integer value6, Integer value7, String value8, Long value9, Long value10) {
        value1(value1);
        value2(value2);
        value3(value3);
        value4(value4);
        value5(value5);
        value6(value6);
        value7(value7);
        value8(value8);
        value9(value9);
        value10(value10);
        return this;
    }

    // -------------------------------------------------------------------------
    // FROM and INTO
    // -------------------------------------------------------------------------

    /**
     * {@inheritDoc}
     */
    @Override
    public void from(ICourseFeedback from) {
        setId(from.getId());
        setQuestionId(from.getQuestionId());
        setQuestionContent(from.getQuestionContent());
        setAnswerContent(from.getAnswerContent());
        setFeedbackContent(from.getFeedbackContent());
        setFeedbackType(from.getFeedbackType());
        setLike(from.getLike());
        setCreateMemberId(from.getCreateMemberId());
        setCreateTime(from.getCreateTime());
        setModifyDate(from.getModifyDate());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <E extends ICourseFeedback> E into(E into) {
        into.from(this);
        return into;
    }

    // -------------------------------------------------------------------------
    // Constructors
    // -------------------------------------------------------------------------

    /**
     * Create a detached CourseFeedbackRecord
     */
    public CourseFeedbackRecord() {
        super(CourseFeedback.COURSE_FEEDBACK);
    }

    /**
     * Create a detached, initialised CourseFeedbackRecord
     */
    public CourseFeedbackRecord(String id, String questionId, String questionContent, String answerContent, String feedbackContent, Integer feedbackType, Integer like, String createMemberId, Long createTime, Long modifyDate) {
        super(CourseFeedback.COURSE_FEEDBACK);

        set(0, id);
        set(1, questionId);
        set(2, questionContent);
        set(3, answerContent);
        set(4, feedbackContent);
        set(5, feedbackType);
        set(6, like);
        set(7, createMemberId);
        set(8, createTime);
        set(9, modifyDate);
    }

    public boolean fromPojo(Object source) {
        if (!(source instanceof com.zxy.product.course.jooq.tables.pojos.CourseFeedbackEntity)) {
            return false;
        }
        com.zxy.product.course.jooq.tables.pojos.CourseFeedbackEntity pojo = (com.zxy.product.course.jooq.tables.pojos.CourseFeedbackEntity)source;
        pojo.into(this);
        return true;
    }
}
