package com.zxy.product.course.entity;

import com.zxy.product.course.jooq.tables.pojos.SubAuthenticatedCertificateRecordEntity;

public class SubAuthenticatedCertificateRecord extends SubAuthenticatedCertificateRecordEntity {
    private static final long serialVersionUID = -5564879764107946555L;
    public static final Integer IS_DELETE = 1;
    public static final Integer NOT_DELETE = 0;

    public static final int TEMPLATE_MEMBER_NAME = 0;
    public static final int TEMPLATE_MEMBER_CODE = 1;
    public static final int TEMPLATE_MEMBER_OTHER = 2; // 用来放非单元格错误的异常(规则异常)


    public static final Integer STATUS_NO = 0;
    public static final Integer STATUS_YES = 1;
}
