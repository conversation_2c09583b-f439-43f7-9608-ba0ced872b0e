/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseStudyProgressRecord;

import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * 课程学习进度表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudyProgress extends TableImpl<CourseStudyProgressRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_study_progress</code>
     */
    public static final CourseStudyProgress COURSE_STUDY_PROGRESS = new CourseStudyProgress();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseStudyProgressRecord> getRecordType() {
        return CourseStudyProgressRecord.class;
    }

    /**
     * The column <code>course-study.t_course_study_progress.f_id</code>.
     */
    public final TableField<CourseStudyProgressRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_study_progress.f_member_id</code>. 用户ID
     */
    public final TableField<CourseStudyProgressRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "用户ID");

    /**
     * The column <code>course-study.t_course_study_progress.f_course_id</code>. 课程ID
     */
    public final TableField<CourseStudyProgressRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程ID");

    /**
     * The column <code>course-study.t_course_study_progress.f_begin_time</code>. 学习开始时间
     */
    public final TableField<CourseStudyProgressRecord, Long> BEGIN_TIME = createField("f_begin_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "学习开始时间");

    /**
     * The column <code>course-study.t_course_study_progress.f_type</code>. 注册类型，1-自主注册，2-学习推送，3-岗位推送，4-人员标签，5-班级，6-学习专题
     */
    public final TableField<CourseStudyProgressRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "注册类型，1-自主注册，2-学习推送，3-岗位推送，4-人员标签，5-班级，6-学习专题");

    /**
     * The column <code>course-study.t_course_study_progress.f_is_required</code>. 元素性质，1-选修，2-必修
     */
    public final TableField<CourseStudyProgressRecord, Integer> IS_REQUIRED = createField("f_is_required", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "元素性质，1-选修，2-必修");

    /**
     * The column <code>course-study.t_course_study_progress.f_finish_status</code>. 学习状态，0-未开始，1-学习中，2-已完成，3-已放弃 ，4-标记完成
     */
    public final TableField<CourseStudyProgressRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习状态，0-未开始，1-学习中，2-已完成，3-已放弃 ，4-标记完成");

    /**
     * The column <code>course-study.t_course_study_progress.f_finish_time</code>. 完成时间
     */
    public final TableField<CourseStudyProgressRecord, Long> FINISH_TIME = createField("f_finish_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "完成时间");

    /**
     * The column <code>course-study.t_course_study_progress.f_study_total_time</code>. 课程学习总时长，单位秒
     */
    public final TableField<CourseStudyProgressRecord, Integer> STUDY_TOTAL_TIME = createField("f_study_total_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "课程学习总时长，单位秒");

    /**
     * The column <code>course-study.t_course_study_progress.f_register_time</code>. 注册时间
     */
    public final TableField<CourseStudyProgressRecord, Long> REGISTER_TIME = createField("f_register_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "注册时间");

    /**
     * The column <code>course-study.t_course_study_progress.f_last_access_time</code>. 最后访问时间
     */
    public final TableField<CourseStudyProgressRecord, Long> LAST_ACCESS_TIME = createField("f_last_access_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "最后访问时间");

    /**
     * The column <code>course-study.t_course_study_progress.f_create_time</code>.
     */
    public final TableField<CourseStudyProgressRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress.t_course_version_id</code>. 课程版本id
     */
    public final TableField<CourseStudyProgressRecord, String> COURSE_VERSION_ID = createField("t_course_version_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "课程版本id");

    /**
     * The column <code>course-study.t_course_study_progress.f_mark_member_id</code>. 标记人
     */
    public final TableField<CourseStudyProgressRecord, String> MARK_MEMBER_ID = createField("f_mark_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "标记人");

    /**
     * The column <code>course-study.t_course_study_progress.f_mark_time</code>. 标记时间
     */
    public final TableField<CourseStudyProgressRecord, Long> MARK_TIME = createField("f_mark_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "标记时间");

    /**
     * The column <code>course-study.t_course_study_progress.f_completed_rate</code>. 完成进度(百分比)
     */
    public final TableField<CourseStudyProgressRecord, Integer> COMPLETED_RATE = createField("f_completed_rate", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "完成进度(百分比)");

    /**
     * The column <code>course-study.t_course_study_progress.f_current_chapter_id</code>. 当前需要学习的章id
     */
    public final TableField<CourseStudyProgressRecord, String> CURRENT_CHAPTER_ID = createField("f_current_chapter_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "当前需要学习的章id");

    /**
     * The column <code>course-study.t_course_study_progress.f_current_section_id</code>. 当前需要学习的节id
     */
    public final TableField<CourseStudyProgressRecord, String> CURRENT_SECTION_ID = createField("f_current_section_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "当前需要学习的节id");

    /**
     * The column <code>course-study.t_course_study_progress.f_push_id</code>. 推送id，用于关联最后一次必修的推送
     */
    public final TableField<CourseStudyProgressRecord, String> PUSH_ID = createField("f_push_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "推送id，用于关联最后一次必修的推送");

    /**
     * The column <code>course-study.t_course_study_progress.f_visits</code>. 学习次数
     */
    public final TableField<CourseStudyProgressRecord, Integer> VISITS = createField("f_visits", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "学习次数");

    /**
     * The column <code>course-study.t_course_study_progress.f_last_modify_time</code>. 课程汇总表最后一次更新时间
     */
    public final TableField<CourseStudyProgressRecord, Long> LAST_MODIFY_TIME = createField("f_last_modify_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "课程汇总表最后一次更新时间");

    /**
     * The column <code>course-study.t_course_study_progress.f_subject_finish_time</code>. 专题必修课完成时间
     */
    public final TableField<CourseStudyProgressRecord, Long> SUBJECT_FINISH_TIME = createField("f_subject_finish_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "专题必修课完成时间");

    /**
     * The column <code>course-study.t_course_study_progress.f_completion_times</code>. 完成次数
     */
    public final TableField<CourseStudyProgressRecord, Integer> COMPLETION_TIMES = createField("f_completion_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "完成次数");

    /**
     * The column <code>course-study.t_course_study_progress.f_latest_completion_time</code>. 最新完成时间
     */
    public final TableField<CourseStudyProgressRecord, Long> LATEST_COMPLETION_TIME = createField("f_latest_completion_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "最新完成时间");

    /**
     * The column <code>course-study.t_course_study_progress.f_study_hours_record_time</code>. 集中学时记录时间
     */
    public final TableField<CourseStudyProgressRecord, Long> STUDY_HOURS_RECORD_TIME = createField("f_study_hours_record_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "集中学时记录时间");

    /**
     * The column <code>course-study.t_course_study_progress.f_concentrate_study_hours</code>. 集中学时
     */
    public final TableField<CourseStudyProgressRecord, Integer> CONCENTRATE_STUDY_HOURS = createField("f_concentrate_study_hours", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "集中学时");

    /**
     * Create a <code>course-study.t_course_study_progress</code> table reference
     */
    public CourseStudyProgress() {
        this("t_course_study_progress", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_study_progress</code> table reference
     */
    public CourseStudyProgress(String alias) {
        this(alias, COURSE_STUDY_PROGRESS);
    }

    private CourseStudyProgress(String alias, Table<CourseStudyProgressRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseStudyProgress(String alias, Table<CourseStudyProgressRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "课程学习进度表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseStudyProgressRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_STUDY_PROGRESS_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseStudyProgressRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseStudyProgressRecord>>asList(Keys.KEY_T_COURSE_STUDY_PROGRESS_PRIMARY, Keys.KEY_T_COURSE_STUDY_PROGRESS_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseStudyProgress as(String alias) {
        return new CourseStudyProgress(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseStudyProgress rename(String name) {
        return new CourseStudyProgress(name, null);
    }
}
