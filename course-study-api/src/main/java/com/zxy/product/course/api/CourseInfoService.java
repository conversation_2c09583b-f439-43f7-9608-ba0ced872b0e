package com.zxy.product.course.api;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.*;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Created by <PERSON><PERSON><PERSON> on 16/10/12.
 */
@RemoteService(timeout = 100000)
public interface CourseInfoService {

  String URI = "course-study/course-info";
  // 课件类型 - URL
  int SECTION_TYPE_URL = 3;
  // 课件类型 - 任务
  int SECTION_TYPE_TASK = 8;

  // 默认模板
  int TEMPLATE_IS_DEFAULT = 1;
  // 自定义模板
  int TEMPLATE_CUSTOM = 0;

  /**
   * 后台查询课程列表
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  PagedResult<CourseInfo> find(int pageNum, int pageSize, String memberId, int businessType,
      Optional<String> name,
      Optional<String> organizationId, Optional<String> categoryId, Optional<String> code,
      Optional<Integer> status,
      Optional<Integer> source, Optional<String> releaseUserId, Optional<Integer> publishClient,
      Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate,
      Optional<Integer> subjectType, Optional<Long> beginBeginDate, Optional<Long> beginEndDate,
      Optional<Long> endBeginDate, Optional<Long> endEndDate,
      Optional<Long> shelveBeginTime, Optional<Long> shelveEndTime, Optional<String[]> selectIds,
      List<String> GrantedOrganizationIds);

  /**
   * 查询器用 增加了查询上级分享的
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  PagedResult<CourseInfo> findSelect(Integer pageNum, Integer pageSize, String memberId,
      Optional<Integer> client,
      Optional<String> name, Optional<String> organizationId,
      Optional<String> category, Optional<String> uri, Optional<String[]> selectIds,
      List<String> GrantedOrganizationIds);

  /**
   * 根据ID查询详情
   *
   * @param id 课程ID
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  CourseInfo get(String id);

  /**
   * 根据ID查询name
   *
   * @param id 课程ID
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  String getName(String id);

  /**
   * 根据ID查询详情
   *
   * @param id 课程ID
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  CourseInfo getCourse(String id);

  /**
   * 根据ID查询基本信息
   *
   * @param id 课程ID
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  CourseInfo getCourseInfo(String id);

  void save(List<CourseInfo> courseInfos);

  /**
   * @param name 课程名称
   * @param beginDate 开始时间
   * @param endDate 结束时间
   * @param courseHour 课时
   * @param credit 学分
   * @param courseTime 课程时长
   * @param desc 课程描述
   * @param releaseUser 发布人
   * @param releaseOrg 发布部门
   * @param devUser 开发人
   * @param devTime 开发时间
   * @param certificateId 授予证书
   */
  @Transactional
  CourseInfo save(String name, String createUserId, int businessType, int publishClient,
      String organizationId,
      Integer source, Optional<Integer> publishType, Optional<String> certificateId,
      Optional<Long> beginDate,
      Optional<Long> endDate, Optional<Integer> courseHour, Optional<Integer> credit,
      Optional<Integer> courseTime, Optional<String> desc, Optional<String> descText,
      Optional<String> releaseUser, Optional<String> releaseOrg, Optional<String> devUser,
      Optional<Long> devTime,
      Optional<String> code, Optional<Integer> price, Optional<Integer> courseSecond,
      Optional<String> categoryId,
      Optional<Integer> addType, Optional<Integer> learnSequence, Optional<Integer> studyDays,
      Optional<String> cover, Optional<String> coverPath, Optional<String> sourceDetail,
      Optional<String> lecturer, Optional<Integer> shareSub,
      List<CourseChapter> courseChapters, List<CourseAttachment> courseAttachments,
      List<AudienceItem> audienceItems, Optional<CourseShelves> courseShelves,
      Optional<String> styles, Optional<String> topicIds, Optional<Integer> isPublic,
      List<SubjectAdvertising> advertisingList,
      List<CoursePhoto> photos, List<SubjectTextArea> textAreas, Optional<String> url);

  /**
   * @param name 课程名称
   * @param beginDate 开始时间
   * @param endDate 结束时间
   * @param courseHour 课时
   * @param credit 学分
   * @param courseTime 课程时长
   * @param desc 课程描述
   * @param releaseUser 发布人
   * @param releaseOrg 发布部门
   * @param devUser 开发人
   * @param devTime 开发时间
   * @param certificateId 授予证书
   */
  @Transactional
  CourseInfo update(String id, int publishClient, int source, Optional<Integer> publishType,
      Optional<String> name,
      Optional<Long> beginDate, Optional<Long> endDate, Optional<Integer> courseHour,
      Optional<Integer> credit,
      Optional<Integer> courseTime, Optional<String> desc, Optional<String> descText,
      Optional<String> releaseUser, Optional<String> releaseOrg,
      Optional<String> organizationId, Optional<String> devUser, Optional<Long> devTime,
      Optional<String> code,
      Optional<Integer> price, Optional<Integer> courseSecond, Optional<String> certificateId,
      Optional<String> categoryId, Optional<Integer> learnSequence, Optional<Integer> addType,
      Optional<Integer> studyDays, Optional<String> cover, Optional<String> coverPath,
      Optional<String> sourceDetail, Optional<String> lecturer, Optional<Integer> shareSub,
      List<CourseChapter> courseChapters, List<CourseAttachment> courseAttachments,
      List<AudienceItem> audienceItems, Optional<CourseShelves> courseShelves,
      Optional<String> styles, Optional<String> topicIds, Optional<Integer> isPublic,
      List<SubjectAdvertising> advertisingList,
      List<CoursePhoto> photos, List<SubjectTextArea> textAreas, Optional<String> url);

  /**
   * 修改专题
   */
  @Transactional
  CourseInfo updateSubject(String id, int publishClient, Optional<Integer> publishType,
      Optional<String> name,
      Optional<Long> beginDate, Optional<Long> endDate, Optional<String> desc,
      Optional<String> descText,
      Optional<String> releaseUser, Optional<String> releaseOrg, Optional<String> organizationId,
      Optional<String> code,
      Optional<Integer> learnSequence, Optional<Integer> studyDays, Optional<String> cover,
      Optional<String> coverPath, Optional<Integer> shareSub,
      List<CourseChapter> courseChapters, List<CourseAttachment> courseAttachments,
      List<AudienceItem> audienceItems, Optional<CourseShelves> courseShelves,
      Optional<String> styles, Optional<String> topicIds, List<SubjectAdvertising> advertisingList,
      List<CoursePhoto> photos, List<SubjectTextArea> textAreas, Optional<String> url);

  /**
   * 保存课程章节
   */
  @Transactional
  int saveCourseChapters(String courseId, List<CourseChapter> courseChapters);

  @Transactional
  int saveCourseChapters(String courseId, List<CourseChapter> courseChapters,String memberId);
  /**
   * 保存附件
   */
  @Transactional
  int saveCourseAttachs(String courseId, List<CourseAttachment> courseAttachs);

  /**
   * 保存专题管理员
   */
  @Transactional
  int saveSubjectManager(String courseId, List<SubjectTopicManager> managerItems);

  /**
   * 保存专题管理员
   */
  @Transactional
  int updateSubjectManager(String courseId, List<SubjectTopicManager> managerItems);

  @Transactional
  void deleteCourseAttachs(String courseId);

  /**
   * delete 删除
   *
   * @param id 课程id
   * @return 删除记录数
   */
  int delete(String id);

  /**
   * delete 删除
   *
   * @param id 课程id
   * @return 删除记录数
   */
  int delete(String id,Integer historyCount);

  /**
   * 根据课程ID查询所有附件
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseAttachment> findCourseAttachmentByCourseId(String courseId);

  /**
   * 查询当前版本课程的章节项目
   */
  List<CourseChapter> findCourseChapterByCourseId(String courseId);

  /**
   * 红船课程审核查询课件
   * @param courseId
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseChapter> findRedShipCourseChapterByCourseId(String courseId);

  /**
   * 保存课程受众对象
   */
  @Transactional
  int saveCourseAudienceObject(String courseId, List<AudienceItem> audienceItems, int businessType);

  /**
   * 课程上下架
   */
  @Transactional
  CourseInfo editCourseShelves(CourseInfo courseInfo, Integer status, CourseShelves courseShelves,
                               Optional<Integer> businessType, String currentUserId, Optional<Integer> constructionType);

  /**
   * 工作室课程上下架
   */
  @Transactional
  CourseInfo studioCourseShelves(Boolean approveSwitch, CourseInfo courseInfo, Integer status, CourseShelves courseShelves, String currentUserId);


  /**
   * 根据章节类型返回课程信息.
   *
   * @param resourceId 引用资源id
   * @param type 章节类型
   * @return 课程信息.
   */
  @Transactional(readOnly = true)
  CourseInfo getCourseInfoBySectionType(String resourceId, int type);

  /**
   * 根据章节类型返回课程信息.
   *
   * @param resourceIds 引用资源id
   * @param type 章节类型
   * @return 课程信息.
   */
  @Transactional(readOnly = true)
  List<CourseChapterSection> getCourseSectionList(List<String> resourceIds, int type);

    /**
     * 课程退库
     *
     * @param courseInfo
     * @param status
     * @param currentUserId
     * @return
     */
    @Transactional
    CourseInfo editCourseRetreat(CourseInfo courseInfo, Integer status, String currentUserId);

    /**
     * 根据课程ID,查询节内容
     *
     * @param courseId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseChapterSection> findCourseChapterSectionList(String courseId, String versionId);

    /**
     * 根据专题ID，查询相册
     *
     * @param subjectId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CoursePhoto> findPhotosBySubjectId(String subjectId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CoursePhoto> findPhotosPageBySubjectId(String subjectId, int pageNum, int pageSize);

    /**
     * 新增照片
     *
     * @param subjectId
     * @return
     */
    @Transactional
    List<CoursePhoto> savePhotos(String subjectId, List<CoursePhoto> photos);

    /**
     * 批量删除照片
     *
     * @param ids
     * @return
     */
    @Transactional
    List<String> deletePhotos(List<String> ids);

    /**
     * 修改照片名称
     *
     * @param id
     * @param name
     * @return
     */
    @Transactional
    CoursePhoto updatePhoto(String id, String name);

    /**
     * [学员端]根据条件查询课程
     *
     * @param currentUserId
     * @param page
     * @param pageSize
     * @param orderBy
     * @param order
     * @param searchContent
     * @param categoryId
     * @param topicId
     * @param type
     *            // 0:课程 1:专题
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    PagedResult<CourseInfo> find(String currentUserId, Integer page, Integer pageSize, Optional<Integer> orderBy,
            Optional<Integer> order, Optional<String> searchContent, Optional<String> categoryId,
            Optional<String> topicId, Integer type,Optional<Integer>  publishClient,Optional<Integer> companyType);

    /**
     * 查询前端
     * @param courseId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
    CourseInfo getFrontNew(String courseId, String memberId);

    /**
     * 查询前端 将受众判断放到一起 优化给反腐倡廉
     * @param courseId
     * @param memberId
     * @return
     */
   @Transactional(readOnly = true,propagation = Propagation.SUPPORTS)
   CourseInfo getFrontNew2(String courseId, String memberId, Integer from);
    /**
     * 查询预览
     * @param id
     * @param memberId
     * @return
     */
    CourseInfo getPerview(String id, String memberId);
    /**
     * 查询每个章节的进度
     * @param memberId
     * @param ids
     * @return
     */
    @Transactional
    List<CourseSectionStudyProgress> selectProgress(String memberId, List<String> ids);

    /**
     * 查询每个课程的进度
     * @param memberId
     * @param ids
     * @return
     */
    List<CourseStudyProgress> selectCourseProgress(String memberId, List<String> ids);

    Map<String, Integer> courseInfoProgressesCache2(String memberId, List<String> ids,Integer type,Optional<String> configString);
    /**
     * 添加访问次数
     */
    @Transactional
    void addVisits(String courseId, String memberId);


    /**
     * 验证是否能学习
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    void checkStudy(String id, String memberId);
    /**
     * 新增课程笔记
     *
     * @param memberId
     * @param courseId
     * @param content
     * @return
     */
    @Transactional
    CourseNote insertCourseNote(String memberId, String courseId, String content);

    /**
     * 查询当前用户的课程笔记
     *
     * @param memberId
     * @param courseId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseNote> findCourseNotes(String memberId, String courseId);

    /**
     * 修改风格
     *
     * @param id
     * @param styles
     * @return
     */
    @Transactional
    CourseInfo updateStyles(String id, String styles);

    /**
     * 获取相关课程/专题方法 根据多个话题查询
     *
     * @param topicIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> getRelated(int page, int pageSize, Integer businessType, String currentUserId,
                                String[] topicIds, Optional<String> categoryId, Optional<String> courseId, Optional<Integer> publishClient);


    /**
     * 分页获取相关课程/专题方法 根据多个话题查询
     *
     * @param topicIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> getRelatedPage(int page, int pageSize, Integer businessType, String currentUserId,
                                String[] topicIds, Optional<String> categoryId, Optional<String> courseId, Optional<Integer> publishClient);


    /**
     * 删除课程笔记
     *
     * @param id
     * @return
     */
    @Transactional
    String deleteCourseNote(String id);

    /**
     * 修改课程笔记
     *
     * @param memberId
     * @param id
     * @param content
     * @return
     */
    @Transactional
    CourseNote updateCourseNote(String memberId, String id, String content);

    /**
     * 判断用户是否有课程的学习权限
     *
     * @param courseId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    public Boolean judgeMemberHasAuthority(String courseId, String memberId);

    /**
     * 通过ids查找课程基本信息
     *
     * @param ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> findByIds(List<String> ids);

    /**
     * 通过ids查找课程基本信息(返回部分字段)
     *
     * @param ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> findByIds(List<String> ids, Boolean needDescription, Boolean needDescriptionApp, Boolean needDescriptionText);

  /**
     * 通过ids查找课程基本信息
     *
     * @param ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> findByIds(List<String> ids, String memberId);

    /**
     * 根据节ID、当前用户获取节信息、节进度、节资源信息
     *
     * @param sectionId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    CourseChapterSection getSectionByIds(String sectionId, String memberId);

    /**
     * 查询最近学习过该课程的用户
     *
     * @param memberId
     * @param courseId
     * @param page
     * @param pageSize
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<Member> findLastestUser(String memberId, String courseId, Integer page, Integer pageSize);

    /**
     * 学习业务评分
     *
     * @param memberId
     * @param businessId
     * @param businessType
     *            1课程 2专题 3....
     * @param score
     * @return
     */
    @Transactional
    Integer insertCourseScore(String memberId, String businessId, Integer businessType, Integer score);

    /**
     * 判断课程名称是否存在
     *
     * @param courseName
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    boolean judgeCourseNameIsExists(String courseName);

    /**
     * 个人中心首页推荐
     * @param page
     * @param currentUserId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> personIndex(Integer page, Integer pageSize, String currentUserId, List<String> topicIds);

    /**
     * 获取上架详情
     * @param id
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Optional<CourseShelves> getShelves(String id);

    /**
     * 查询课程热门标签
     * keeley
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> hotTopicIds();

    /**
     * 统计提交人数
     * @param referenceIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<String, Integer> countSubmitPerson(String[] referenceIds,String userId);

    /**
     * 根据资源id查询章节信息中包含评估调研的数据
     * @param id
     * @return 评估调研的ID集合
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseChapterSection> findResearchFromSection(String id);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Optional<CourseInfo> getOptional(String id);


    /**
     * 培训课程选择器。 1 归属部门是集团 2 所在机构d的所有课程
     * @param rootId
     * @param companyId
     * @param selectedIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> selectForTrain(Integer page,Integer pageNum,String rootId,
                                    Optional<String> companyId,Optional<String> organizationId,
                                           Optional<String> categoryId, Optional<String> name, Optional<String> selectedIds);


    /**
     * 根据考试id 查询所属课程的基本信息
     * @param examId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Optional<CourseInfo> getCourseBasicByExamId(String examId);

    /**
     * 根据课程ID、版本、资源类型查询章节信息
     * @param courseId
     * @param versionId
     * @param sectionType
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseChapterSection> getChapterSectionBySectionType(String courseId, Optional<String> versionId, int sectionType);
    /**
     * 根据课程ID、查询每个课程对应的章节数量
     * @param courseIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<String, Integer> getChapterSectionNumberByCourseIds(List<String> courseIds);

  /**
     * 获取受众内的专题信息
     * @param courseId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    CourseInfo getSubjectFrontAudience(Integer from,String courseId, String memberId);

    /**
     * 获取无受众校验的专题信息
     * @param courseId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    CourseInfo getSubjectFrontNoAudience(String courseId, String memberId);

    /**
     * 不包含退库课程的节查询
     * @param courseId
     * @param memberId
     * @param versionId
     * @param isRegister
     * @param domains
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseChapter> findSubjectChapterNoContainReturn(String courseId, String memberId, String versionId,
                                                          boolean isRegister, List<String> domains);

    /**
     * 查询专题章节信息包含进度
     * @param courseId
     * @param memberId
     * @return
     */
    @Transactional
    List<CourseChapter> findSubjectChapterForFront(String courseId, String memberId, String versionId, boolean isRegister,List<String> domains);

    /**
     * 复制findSubjectChapterForFront并作修改
     *
     * @param courseId
     * @param memberId
     * @return
     */
    @Transactional
    List<CourseChapter> findSubjectSimpleChapterForFront(String courseId, String memberId);


    /**
     * 查询学习地图章节信息，app
     * @param memberId
     * @param domains
     * @param chapterId
     * @return
     */
    @Transactional
    List<CourseChapter> findStudyMapChapterForFrontApp(String courseId,String memberId,List<String> domains,String chapterId);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseChapterSection> findCourseChapterById(String courseId, Optional<Integer> sectionType);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseChapter> findCourseChapterByCourseId(String courseId, Optional<String> courseVersion);

  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseChapterSection> findCourseChapterSectionByCourseId(String courseId, Optional<String> courseVersion);

    /**
     * 查询可用列表清单
     * @param page
     * @param pageSize
     * @param client
     * @param name
     * @param category
     * @param code
     * @param shelveBeginDate
     * @param shelveEndDate
     * @param grantOrganizationIds
     * @param parentIds
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> findAvailableCourseList(Integer page, Integer pageSize,
            Optional<Integer> client, Optional<String> name, Optional<String> category,
            Optional<String> code, Optional<Long> shelveBeginDate,
            Optional<Long> shelveEndDate, List<String> grantOrganizationIds,
            Optional<List<String>> parentIds);

    /**
     * 学员端全局搜索（包括非公开课程）
     * @param currentUserId
     * @param page
     * @param pageSize
     * @param orderBy
     * @param order
     * @param searchContent
     * @param categoryId
     * @param topicId
     * @param type
     * @param publishClient
     * @param companyType
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
	PagedResult<CourseInfo> findFullSearch(String currentUserId, Integer page, Integer pageSize, Optional<Integer> orderBy,
            Optional<Integer> order, Optional<String> searchContent, Optional<String> categoryId,
            Optional<String> topicId, Integer type,Optional<Integer>  publishClient,Optional<Integer> companyType);

    /**
     * 学员端全局搜索（包括非公开课程）去掉count查询
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Map<String, Object> findFullSearchMap(String currentUserId, Integer page, Integer pageSize,
                                          Optional<Integer> orderBy,
                                          Optional<Integer> order, Optional<String> searchContent, Optional<String> categoryId,
                                          Optional<String> topicId, Integer type, Optional<Integer> publishClient,
                                          Optional<Integer> companyType);
    /**
     * 根据学员ID集合和课程ID查询评分
     *
     * @param memberIds
     * @param businessId
     * @param businessType
     *            1课程 2专题 3....
     * @param score
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseScore> findCourseScore(String[] memberIds, String businessId, Integer businessType);

    /**
     * 只查询专题的基本信息courseInfo表中信息
     * @param subjectId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
	CourseInfo getSubjectInfoDetailById(String subjectId);

    /**
     * 专题、课程分页列表
     *
     * @param currentUserId
     * @param page
     * @param pageSize
     * @param orderBy
     * @param order
     * @param searchContent
     * @param categoryId
     * @param topicId
     * @param type
     * @param publishClient
     * @param companyType
     * @param internalSwitchStatus
     * @return
     */
    Map<String, Object> findMap(String currentUserId, Integer page, Integer pageSize, Optional<Integer> orderBy, Optional<Integer> order, Optional<String> searchContent, Optional<String> categoryId, Optional<String> topicId, Integer type,
                                Optional<Integer> publishClient, Optional<Integer> companyType, boolean internalSwitchStatus,boolean pageSwitch,boolean cacheOrder);


    /**
     * 查询专题的章节信息
     * @param subjectId
     * @return
     */
    List<CourseChapter> findSubjectChapterSections(String subjectId);

    /**
     * 根据专题id查询课程数量
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Integer findCourseStudyCountBySubjectId(String subjectId);

  /**
   * 人工智能--初始化课程专题
   * @param start
   * @param limit
   * @return
   */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<String> getAllCourseIds(int start,int limit);

  /**
   * 查询所有课程*
   * @param start
   * @param limit
   * @param businessType
   * @return
   */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> getAllCourseInfo(int start, int limit, Optional<Integer> businessType);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> getCourseInfoByTime(int start, int limit, Optional<Integer> businessType, Optional<Long> startTime, Optional<Long> endTime);

    /**
     * 党校-最新上线
     * @param page      页码
     * @param pageSize  每页行数
     * @param businessType 业务类型
     * @return  result
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    PagedResult<CourseInfo> findNewForParty(int page, int pageSize, Optional<Integer> businessType);

    /**
     * 查询传入的courseIds中业务类型
     * @param ids ids
     * @return list
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<CourseInfo> findBusinessTypeByIds(Collection<String> ids);



  /**
   * 根据受众查询课程列表-用于团队学习班学习课程列表
   * @param currentUserId 当前登录用户id
   * @param page
   * @param pageSize
   * @param type 类型 0：课程 1：专题 ，一期暂时只做课程查询
   * @param courseIds 课程ids，用于过滤已经存在活动中的课程
   * @param courseName 课程名称，用于查询
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  PagedResult<CourseInfo> findStudyTeamCourseInfos(String currentUserId, Integer page, Integer pageSize, Integer type,
                                                   Optional<String> courseName, List<String> courseIds,
                                                   String leaderMemberId, Long beginTime);



  /**
   * 根据课程id查询课程详情信息，包含学习时长学习状态等-该接口用于团队学习班
   * @param currentUserId 当前登录用户id
   * @param businessIds  课程id
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> findCoursesByTeamBusinessIds(String currentUserId, List<String> businessIds);

  /**
   * 查询授权范围内的课程、专题
   * @param currentUserId 当前登录用户id
   * @param businessIds  课程id
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> findCourseAudience(String currentUserId, List<String> businessIds);


  /**
   * 查询未退库未取消发布的课程课程、专题
   * @param currentUserId 当前登录用户id
   * @param businessIds  课程id
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> findCourse(String currentUserId, List<String> businessIds);

  /**
   * 查询课程对应的 人——课——天 学习记录
   * 供给九天课程学习同步使用
   * @param isFirst 当为null的时候的时候 是首次同步
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseSectionStudyLogAhDay> findCourseSectionDay(int offset, int pageSize ,List<String> courseIds ,
                                                        Optional<Long> startTime, Optional<Long> endTime,
                                                        String tableName, String progressTableName,
                                                        Optional<Object> isFirst);
  /**
   * 查询授权范围内的课程
   * @param page 当前页数
   * @param pageSize 每页大小
   * @param currentUserId 当前登录用户id
   * @param topicId 标签id
   * @param name 课程名称
   * @param shelveTimeStart 首次发布开始时间
   * @param shelveTimeEnd 首次发布结束时间
   * @param category 分类/序列
   * @param isParty 是否为党课
   * @param organization 归属部门
   * @param publishClient 适用终端
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  PagedResult<CourseInfo> findCourseInfo(Integer page,Integer pageSize,String currentUserId,String topicId,
                                         Optional<String> name,Optional<Long> shelveTimeStart,Optional<Long> shelveTimeEnd,
                                         Optional<String> category,Optional<Integer> isParty,Optional<String> organization,Optional<Integer> publishClient);

  /**
   *  根据节id查询未发布的课程数量
   * @param sectionIds  章节ids
   * @return
   */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    Long getChapterSectionStatus(Optional<List<String>> sectionIds);

  /**
   * 根据课程id获取含此课程的专题信息
   * @param id 章节id
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> getSubjectsBySectionId(Optional<String> id);

  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<String> getCourseOrSubjectAllIds(int offset, int limit, int isSubject);
  /**
   * 查询授权范围内的专题
   * @param page 当前页数
   * @param pageSize 每页大小
   * @param currentUserId 当前登录用户id
   * @param topicId 标签id
   * @param name 专题名称
   * @param organization 归属部门
   * @param publishClient 适用终端
   * @param type 类型:1是普通，2是个性
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  PagedResult<CourseInfo> findSubjectInfo(Integer page,Integer pageSize,String currentUserId,String topicId,
                                           Optional<String> name,Optional<String> organization,Optional<Integer> publishClient,Optional<Integer> type);
  /**
   * 根据标签查询关联的课程
   * @param page 当前页
   * @param pageSize 每页条数
   * @param currentUserId 当前登录用户id
   * @param topicId 标签id
   * @param name 课程名称
   * @param shelveTimeStart 首次发布开始时间
   * @param shelveTimeEnd 首次发布结束时间
   * @param category 分类/序列
   * @param isParty 是否为党课
   * @param organization 归属部门
   * @param publishClient 适用终端
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  PagedResult<CourseInfo> findCourseInfoByTopic(Integer page,Integer pageSize,String currentUserId,String topicId,
                                                Optional<String> name,Optional<Long> shelveTimeStart,Optional<Long> shelveTimeEnd,
                                                Optional<String> category,Optional<Integer> isParty,Optional<String> organization,Optional<Integer> publishClient);

  /**
   * 根据标签查询关联的专题
   * @param page 当前页
   * @param pageSize 每页条数
   * @param currentUserId 当前登录用户id
   * @param topicId 标签id
   * @param name 专题名称
   * @param organization 归属部门
   * @param publishClient 适用终端
   * @param type 类型:1是普通，2是个性
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  PagedResult<CourseInfo> findSubjectInfoByTopic(Integer page,Integer pageSize,String currentUserId,String topicId,
                                                  Optional<String> name,Optional<String> organization,Optional<Integer> publishClient,Optional<Integer> type);

  /**
   * 批量新增课程标签
   * @param topicId 标签id
   * @param businessIds 课程id
   * @return
   */
  @Transactional
  void insertCourseTopicInfo(String topicId,String[] businessIds);

  /**
   * 批量删除课程标签
   * @param topicId 标签id
   * @param businessIds 课程id
   * @return
   */
  @Transactional
  void deleteCourseTopicInfo(String topicId,String[] businessIds);

  /**
   * 批量新增专题标签
   * @param topicId 标签id
   * @param businessIds 专题id
   * @return
   */
  @Transactional
  void insertSubjectTopicInfo(String topicId,String[] businessIds);

  /**
   * 批量删除专题标签
   * @param topicId 标签id
   * @param businessIds 专题id
   * @return
   */
  @Transactional
  void deleteSubjectTopicInfo(String topicId,String[] businessIds);

  /**
   * 获取九天兜底数据
   * @param offset
   * @param limit
   * @return
   */

  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> getBottomDataCourseOrSubjectIds(int offset, int limit, String currentUserId);

  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  Long sumVisits(List<String> ids);

  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> getCoverPaths(List<String> ids);

  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  Map<String, String> getMapCoverPaths(List<String> ids);

  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> getCountInformationIds(List<String> ids);

  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> getVisitsByIds(List<String> ids);

  /**
   *根据businessType获取热门内容
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> hotVisitsList(String organizationId, Integer size, Integer businessType, Integer clientType);

  /**
   *根据businessType获取热门内容接口(新)
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> hotVisitsListNew(List<String> courseListContentId);

  /**
   * 获取九天推荐课程
   *
   * @param courseIds
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> findByRecommendCourseIds(List<String> courseIds, Integer page, Integer pageSize, Integer publishClient, String currentUserId);

//  /**
//   * 更新热门内容 -- 改造 -- LJY -- 2022/05/20
//   */
//  @Transactional
//  void updatehotVisitsList();

  /**
   *根据businessType获取热门内容 -- 改造 -- LJY -- 2022/05/20
   */
  List<CourseInfo> getHotVisitsCache(String key);

  /**
   * 设置热门内容缓存 -- 改造 -- LJY -- 2022/06/10
   */
  void setHotVisitsCache(String key,List<CourseInfo> hotLists);

  /**
   * 设置String缓存 LJY -- 2022/06/13
   */
  void setStringCache(String key,String value);

  /**
   *获取String缓存 LJY -- 2022/06/13
   */
  String getStringCache(String key);

  /**
   * 查询领学人在本次活动范围内,所有的课程都学习总和
   * @param leaderMemberId 领学人id
   * @param courseIds 课程id集合
   * @param beginTime 开始时间
   * @param endTime 结束时间
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  Map<String, Object> findByLeaderStudyTime(String leaderMemberId,List<String> courseIds,Long beginTime,Long endTime);

    /**
     * 查询领学人在本次活动范围内,每门课程都学习时长
     * @param leaderMemberId 领学人id
     * @param courseIds 课程id集合
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<Map<String,Object>> findByLeaderStudyTime1(String leaderMemberId,List<String> courseIds,Long beginTime,Long endTime);
  /**
   * 查询领学人在本次活动范围内,每门课程都学习时长
   * 区间为学习日志的 活动开始时间大于提交时间,活动结束时间小于数据的创建时间
   * @param leaderMemberId 领学人id
   * @param courseIds 课程id集合
   * @param beginTime 开始时间
   * @param endTime 结束时间
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  List<Map<String,Object>> findByLeaderStudyTime2(String leaderMemberId,List<String> courseIds,Long beginTime,Long endTime);
  /**
   * 学习计划添加内容推荐
   *
   * @param currentUserId 当前用户id
   * @param type  1:课程 2：专题
   * @param page  页码
   * @param pageSize  每页大小
   * @return
   */
  @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
  PagedResult<CourseInfo> findStudyPlanRecommend(Integer page,Integer pageSize,String currentUserId, Integer type);

  /**
   * 根据课程id查询对应的章节信息
   * @param courseIds 课程ids
   * @param memberId 用户id
   * @return 章节信息
   */
  @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
  Map<String,List<CourseChapterSection>> findChapterSectionByCourseIds(String memberId,List<String> courseIds);

  /**
   * 根据课程id查找对应的学习计划添加数量
   * @param courseIds 课程ids
   * @return 映射值
   */
  @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
  Map<String,Integer> findCourseStudyPlanAddedCount(List<String> courseIds);

    /**
     * 根据课程id查找课程名称
     * @param courseIds 课程ids
     * @return 映射值
     */
    @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
    String findCourseNameById(String courseIds);


  /**
   * 获取所有智能播报列表
   * @param courseId
   * @param memberId
   * @param businessType
   * @return
   */
  List<CourseInfo> getAllCourseChapterInfo(String courseId,String memberId,Integer businessType);

  Boolean checkArchived(String memberId, String courseId);

  /**
   * 检查当前课程或者专题是否存在versionId
   * @param courseId 课程id
   * @return
   */
  Boolean existedVersionId(String courseId);

  /**
   * 虚拟空间前端页面展示
   *
   * @param currentUserId
   * @param page
   * @param pageSize
   * @param orderBy
   * @param order
   * @param searchContent
   * @param categoryId
   * @param topicId
   * @param type
   * @param publishClient
   * @param companyType
   * @param showField
   * @param businessId
   * @param isAdd
   * @param virtualSpacesId
   * @param virtualSpacesOrganizationId
   * @param byVirtualSpacesForbidden
   * @return
   */
  @Transactional(readOnly = true)
  Map<String, Object> findCourseVirtualSpaceList(String currentUserId, Integer page, Integer pageSize, Optional<Integer> orderBy, Optional<Integer> order, Optional<String> searchContent, Optional<String> categoryId, Optional<String> topicId, Optional<Integer> type, Optional<Integer> publishClient, Optional<Integer> companyType, Integer showField, List<String> businessId, Optional<Integer> isAdd, String virtualSpacesId, String virtualSpacesOrganizationId, List<String> byVirtualSpacesForbidden);

  /**
   * 检查当前专题是否存在该管理员
   * @param courseId
   * @param memberId
   * @return
   */
  Boolean existedManager(String courseId, String memberId);

  /**
   * 虚拟空间-推荐列表-获取课程或专题信息
   * @param courseIds
   * @return
   */
  @Transactional(readOnly = true,propagation = Propagation.NOT_SUPPORTED)
  List<CourseInfo> getCourseOrSubject(List<String> courseIds, String memberId);

  @Transactional(readOnly = true)
  List<String> findVirtualSpacesAndOrgId(List<String> spacesStatusAddTo, Optional<Integer> status, List<String> superiorIds, Optional<Integer> subjectType, Optional<String> name, Optional<String> organizationId, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate);
  @Transactional(readOnly = true)
  List<String> findVirtualSpacesAndOrgId(Optional<String> organizationId, List<String> superiorIds, List<String> spacesStatusAddTo, Optional<Integer> status, Optional<String> name, Optional<String> categoryId, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate);

  @Transactional(readOnly = true)
  List<CourseInfo> findResourceTop3(int businessType);

  /**
   * 查询智能标签课程同步给九天
   * @param startTime 开始时间
   * @param endTime 结束时间
   * @return
   */
  @Transactional(readOnly = true)
  List<CourseInfo>  findIntellectTopicCourse(Long startTime, Long endTime);

  @Transactional
  void update(CourseInfo courseInfo);

  /**
   * 初始化课程总字幕状态
   */
  @Transactional(readOnly = true)
  List<CourseInfo> findAll();

  /**
   * 校验是否匹配考试操作（例如 重新考试）
   *
   * @param examId 考试记录主键
   * @param memberId 当前登陆用户id
   * @return 课程详情
   */
  @Transactional(readOnly = true)
  String  checkMatchingExamOperation(String examId, String memberId);

  /**
   * 验证收藏是否失效
   * @param businessId
   * @param memberId
   * @return
   */
  @Transactional(readOnly = true)
  Integer loseEfficacy(String businessId, String memberId, Integer businessType);

  /**
   *校验是否满足图谱课程同步条件
   *
   * @param courseId 课程Id
   * @return 若满足图谱课程同步条件，则返回当前Id集合
   */
  @Transactional(readOnly = true)
  List<String> checkCourseSynchronous(String courseId);

  /*
    课程详情页查询当前课程，用户可以查看的所属专题
   */
  @Transactional(readOnly = true)
  List<CourseInfo> findSubjectsByCourse(String courseId, String currentUserId);

  /**
   * 系统配置修改了开关后，用系统的开关状态覆盖修改所有的已经设置了的 课程/专题显性学习开关状态
   * type 0：课程 2：专题
   */
  @Transactional
  void editAllExplicitLearningStatus(Integer type, Integer status);
    /*
      推荐相关课程,第一优先级查询
     */
    @Transactional(readOnly = true)
    List<CourseInfo> findSubjectsByCourse(String courseId, String currentUserId,Integer skipSize,Integer size);

    @Transactional(readOnly = true)
  Map<String,Integer> getRequiredSectionProgress(String courseId,String memberId);

  @Transactional(readOnly = true)
  List<Map<String, Object>> getNewRequiredSectionId(String courseId, String memberId);

    @Transactional(readOnly = true)
    public Map<String, List<Map<String, Object>>> getNewRequiredSectionId2(List<String> courseIds, String memberId, Integer type, Optional<String> config);

    @Transactional(readOnly = true)
    public List<String> getNewRequiredList(List<String> courseIds, String memberId, Integer type, Optional<String> config);

  @Transactional(readOnly = true)
  Map<String, Integer> findCourseChapterSectionByCourseIds(List<String> courseIds, Optional<String> versionId);

  @Transactional(readOnly = true)
  List<CourseInfo> findSubjectsByCourseNoAudience(String courseId);

  /**
   * 根据附件查询课程id
   * @param id 附件id
   */
  @Transactional(readOnly = true)
  String getCourseIdByAttachmentId(String id);

  /**
   * 查询学习地图关联能力，包含章节
   */
  @Transactional(readOnly = true)
  List<CourseAbility> findCourseAbilityByCourseId(String id);

  /**
   * 查询学习地图关联能力
   */
  @Transactional(readOnly = true)
  List<CourseAbility> findCourseAbilitySimpleByCourseId(String id);

  /**
   * 根据章节拼装学习地图关联能力
   */
  @Transactional(readOnly = true)
  List<CourseAbility> findCourseAbilityByCourseChapters(String courseId, List<CourseChapter> courseChapters);

  /**
   * 保存学习地图关联能力
   */
  @Transactional()
  int saveCourseAbilities(String courseId, List<CourseAbility> courseAbilities);

  @Transactional()
  Boolean copyStudyMap(String id);

  /**
   * 个人中心查询学习地图
   */
  @Transactional(readOnly = true)
  Map<String, Object> findPersonCourseStudyMap(int pageNum, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy,
                                Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired, Optional<String> studyTimeOrder,
                                boolean pageSwitch, Optional<Long> startTime, Optional<Long> endTime);

  @Transactional(readOnly = true)
  Boolean whetherAddExam(String courseId);

    /**
     * 专题附件id
     */
  @Transactional(readOnly = true)
  List<String> getAttachmentIds(String courseId);

  Map<String,Integer> getExplicitLearningStatus(List<String> ids);

    /**
     * 前一天发布的专题
     */
  @Transactional(readOnly = true)
  List<String> publishedTheDayBeforeSubject(long startTime, long endTime);

  @Transactional(readOnly = true)
  Map<String, Integer> getSwitchMentor(String courseIds);

    @Transactional(readOnly = true)
    Map<String,Integer> getRequiredSectionProgress(List<String> courseIds,String memberId);

  @Transactional(readOnly = true)
  String getNameById(Integer type ,String id);

  @Transactional(readOnly = true)
  Map<String,String> getNameByIds(Integer type ,List<String> ids);

}
