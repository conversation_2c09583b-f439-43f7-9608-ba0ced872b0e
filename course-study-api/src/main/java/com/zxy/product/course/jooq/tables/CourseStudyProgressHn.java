/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseStudyProgressHnRecord;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

import javax.annotation.Generated;

import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;


/**
 * This class is generated by jOOQ.
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseStudyProgressHn extends TableImpl<CourseStudyProgressHnRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_study_progress_hn</code>
     */
    public static final CourseStudyProgressHn COURSE_STUDY_PROGRESS_HN = new CourseStudyProgressHn();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseStudyProgressHnRecord> getRecordType() {
        return CourseStudyProgressHnRecord.class;
    }

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_id</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_member_id</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, String> MEMBER_ID = createField("f_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_course_id</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, String> COURSE_ID = createField("f_course_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_begin_time</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Long> BEGIN_TIME = createField("f_begin_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_type</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Integer> TYPE = createField("f_type", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_is_required</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Integer> IS_REQUIRED = createField("f_is_required", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_finish_status</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Integer> FINISH_STATUS = createField("f_finish_status", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_finish_time</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Long> FINISH_TIME = createField("f_finish_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_study_total_time</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Integer> STUDY_TOTAL_TIME = createField("f_study_total_time", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_register_time</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Long> REGISTER_TIME = createField("f_register_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_last_access_time</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Long> LAST_ACCESS_TIME = createField("f_last_access_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_create_time</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.t_course_version_id</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, String> COURSE_VERSION_ID = createField("t_course_version_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_mark_member_id</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, String> MARK_MEMBER_ID = createField("f_mark_member_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_mark_time</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Long> MARK_TIME = createField("f_mark_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_completed_rate</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Integer> COMPLETED_RATE = createField("f_completed_rate", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_current_chapter_id</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, String> CURRENT_CHAPTER_ID = createField("f_current_chapter_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_current_section_id</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, String> CURRENT_SECTION_ID = createField("f_current_section_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_push_id</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, String> PUSH_ID = createField("f_push_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_visits</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Integer> VISITS = createField("f_visits", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_last_modify_time</code>.
     */
    public final TableField<CourseStudyProgressHnRecord, Long> LAST_MODIFY_TIME = createField("f_last_modify_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_modify_date</code>. 修改时间
     */
    public final TableField<CourseStudyProgressHnRecord, Timestamp> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.TIMESTAMP.nullable(false).defaultValue(org.jooq.impl.DSL.inline("current_timestamp()", org.jooq.impl.SQLDataType.TIMESTAMP)), this, "修改时间");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_subject_finish_time</code>. 专题必修课完成时间
     */
    public final TableField<CourseStudyProgressHnRecord, Long> SUBJECT_FINISH_TIME = createField("f_subject_finish_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "专题必修课完成时间");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_completion_times</code>. 完成次数
     */
    public final TableField<CourseStudyProgressHnRecord, Integer> COMPLETION_TIMES = createField("f_completion_times", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("0", org.jooq.impl.SQLDataType.INTEGER)), this, "完成次数");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_latest_completion_time</code>. 最新完成时间
     */
    public final TableField<CourseStudyProgressHnRecord, Long> LATEST_COMPLETION_TIME = createField("f_latest_completion_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "最新完成时间");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_study_hours_record_time</code>. 集中学时记录时间
     */
    public final TableField<CourseStudyProgressHnRecord, Long> STUDY_HOURS_RECORD_TIME = createField("f_study_hours_record_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "集中学时记录时间");

    /**
     * The column <code>course-study.t_course_study_progress_hn.f_concentrate_study_hours</code>. 集中学时
     */
    public final TableField<CourseStudyProgressHnRecord, Integer> CONCENTRATE_STUDY_HOURS = createField("f_concentrate_study_hours", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "集中学时");

    /**
     * Create a <code>course-study.t_course_study_progress_hn</code> table reference
     */
    public CourseStudyProgressHn() {
        this("t_course_study_progress_hn", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_study_progress_hn</code> table reference
     */
    public CourseStudyProgressHn(String alias) {
        this(alias, COURSE_STUDY_PROGRESS_HN);
    }

    private CourseStudyProgressHn(String alias, Table<CourseStudyProgressHnRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseStudyProgressHn(String alias, Table<CourseStudyProgressHnRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseStudyProgressHnRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_STUDY_PROGRESS_HN_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseStudyProgressHnRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseStudyProgressHnRecord>>asList(Keys.KEY_T_COURSE_STUDY_PROGRESS_HN_PRIMARY, Keys.KEY_T_COURSE_STUDY_PROGRESS_HN_UNIQUE_T_COURSE_PROGRESS_MEMBER_COURSE);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseStudyProgressHn as(String alias) {
        return new CourseStudyProgressHn(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseStudyProgressHn rename(String name) {
        return new CourseStudyProgressHn(name, null);
    }
}
