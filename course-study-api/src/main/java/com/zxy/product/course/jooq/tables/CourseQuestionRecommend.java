/*
 * This file is generated by jOOQ.
*/
package com.zxy.product.course.jooq.tables;


import com.zxy.product.course.jooq.CourseStudy;
import com.zxy.product.course.jooq.Keys;
import com.zxy.product.course.jooq.tables.records.CourseQuestionRecommendRecord;
import org.jooq.Field;
import org.jooq.Schema;
import org.jooq.Table;
import org.jooq.TableField;
import org.jooq.UniqueKey;
import org.jooq.impl.TableImpl;

import javax.annotation.Generated;
import java.util.Arrays;
import java.util.List;


/**
 * 推荐问题管理表
 */
@Generated(
    value = {
        "http://www.jooq.org",
        "jOOQ version:3.9.6"
    },
    comments = "This class is generated by jOOQ"
)
@SuppressWarnings({ "all", "unchecked", "rawtypes" })
public class CourseQuestionRecommend extends TableImpl<CourseQuestionRecommendRecord> {

    private static final long serialVersionUID = 1L;

    /**
     * The reference instance of <code>course-study.t_course_question_recommend</code>
     */
    public static final CourseQuestionRecommend COURSE_QUESTION_RECOMMEND = new CourseQuestionRecommend();

    /**
     * The class holding records for this type
     */
    @Override
    public Class<CourseQuestionRecommendRecord> getRecordType() {
        return CourseQuestionRecommendRecord.class;
    }

    /**
     * The column <code>course-study.t_course_question_recommend.f_id</code>.
     */
    public final TableField<CourseQuestionRecommendRecord, String> ID = createField("f_id", org.jooq.impl.SQLDataType.VARCHAR.length(40).nullable(false), this, "");

    /**
     * The column <code>course-study.t_course_question_recommend.f_question</code>. 问题
     */
    public final TableField<CourseQuestionRecommendRecord, String> QUESTION = createField("f_question", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "问题");

    /**
     * The column <code>course-study.t_course_question_recommend.f_answer</code>. 答案
     */
    public final TableField<CourseQuestionRecommendRecord, String> ANSWER = createField("f_answer", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "答案");

    /**
     * The column <code>course-study.t_course_question_recommend.f_question_third_id</code>. 第三方问题ID
     */
    public final TableField<CourseQuestionRecommendRecord, String> QUESTION_THIRD_ID = createField("f_question_third_id", org.jooq.impl.SQLDataType.VARCHAR.length(100).defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.VARCHAR)), this, "第三方问题ID");

    /**
     * The column <code>course-study.t_course_question_recommend.f_answer_text</code>. 答案文本
     */
    public final TableField<CourseQuestionRecommendRecord, String> ANSWER_TEXT = createField("f_answer_text", org.jooq.impl.SQLDataType.CLOB.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.CLOB)), this, "答案文本");

    /**
     * The column <code>course-study.t_course_question_recommend.f_recommend</code>. 是否为推荐问题 0 否 1 是
     */
    public final TableField<CourseQuestionRecommendRecord, Integer> RECOMMEND = createField("f_recommend", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "是否为推荐问题 0 否 1 是");

    /**
     * The column <code>course-study.t_course_question_recommend.f_status</code>. 状态 0 未发布 1 已发布
     */
    public final TableField<CourseQuestionRecommendRecord, Integer> STATUS = createField("f_status", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint").defaultValue(org.jooq.impl.DSL.inline("0", new org.jooq.impl.DefaultDataType((org.jooq.SQLDialect)null, Integer.class, "tinyint"))), this, "状态 0 未发布 1 已发布");

    /**
     * The column <code>course-study.t_course_question_recommend.f_sort</code>. 排序
     */
    public final TableField<CourseQuestionRecommendRecord, Integer> SORT = createField("f_sort", org.jooq.impl.SQLDataType.INTEGER.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.INTEGER)), this, "排序");

    /**
     * The column <code>course-study.t_course_question_recommend.f_create_time</code>. 创建时间
     */
    public final TableField<CourseQuestionRecommendRecord, Long> CREATE_TIME = createField("f_create_time", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "创建时间");

    /**
     * The column <code>course-study.t_course_question_recommend.f_modify_date</code>. 更新时间
     */
    public final TableField<CourseQuestionRecommendRecord, Long> MODIFY_DATE = createField("f_modify_date", org.jooq.impl.SQLDataType.BIGINT.defaultValue(org.jooq.impl.DSL.inline("NULL", org.jooq.impl.SQLDataType.BIGINT)), this, "更新时间");

    /**
     * Create a <code>course-study.t_course_question_recommend</code> table reference
     */
    public CourseQuestionRecommend() {
        this("t_course_question_recommend", null);
    }

    /**
     * Create an aliased <code>course-study.t_course_question_recommend</code> table reference
     */
    public CourseQuestionRecommend(String alias) {
        this(alias, COURSE_QUESTION_RECOMMEND);
    }

    private CourseQuestionRecommend(String alias, Table<CourseQuestionRecommendRecord> aliased) {
        this(alias, aliased, null);
    }

    private CourseQuestionRecommend(String alias, Table<CourseQuestionRecommendRecord> aliased, Field<?>[] parameters) {
        super(alias, null, aliased, parameters, "推荐问题管理表");
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Schema getSchema() {
        return CourseStudy.COURSE_STUDY_SCHEMA;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public UniqueKey<CourseQuestionRecommendRecord> getPrimaryKey() {
        return Keys.KEY_T_COURSE_QUESTION_RECOMMEND_PRIMARY;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public List<UniqueKey<CourseQuestionRecommendRecord>> getKeys() {
        return Arrays.<UniqueKey<CourseQuestionRecommendRecord>>asList(Keys.KEY_T_COURSE_QUESTION_RECOMMEND_PRIMARY);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public CourseQuestionRecommend as(String alias) {
        return new CourseQuestionRecommend(alias, this);
    }

    /**
     * Rename this table
     */
    @Override
    public CourseQuestionRecommend rename(String name) {
        return new CourseQuestionRecommend(name, null);
    }
}
