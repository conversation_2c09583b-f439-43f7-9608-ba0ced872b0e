package com.zxy.product.course.api.certificate;

import com.zxy.common.base.annotation.RemoteService;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.product.course.entity.*;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Map;
import java.util.Optional;

@RemoteService(timeout = 50000)
public interface CertificateRecordService {

    /**
     * 通过证书id查询证书是否被使用
     * @param certificateId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    int getCertificateUseNumber(String certificateId);


    /**
     * 新增关联关系表
     * @param businessId
     * @param certificateId
     * @param businessType
     * @return
     */
    @Transactional
    BusinessCertificate insert(String businessId, String certificateId, Integer businessType);

    @Transactional
    BusinessCertificate insert(String businessId, String certificateId, Integer businessType, CourseInfo courseInfo);

    /**
     * 删除证书与业务关联关系
     * @param businessId
     * @return
     */
    @Transactional
    int delete(String businessId);

    /**
     * 通过业务id查询是否配置证书
     * @param businessId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    BusinessCertificate getByBusinessId(String businessId);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    String getIdByBusinessId(String businessId);
    /**
     * 查询当前学员在该业务中是否有证书
     * @param businessId
     * @param memberId
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, Object> getRecordByBusinessIdAndMemberId(String businessId, String memberId);

    /**
     * 查询子认证证书
     * @param businessId
     * @param member
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, Object> getSubAuthByBusinessIdAndMemberId(String businessId, Member member);

    /**
     * 批量新增证书记录
     * @param certificateRecordList
     */
    @Transactional
    void insertRecordList(List<CertificateRecord> certificateRecordList);

    /**
     * 根据业务id查询是否有学员获得证书
     * @param businessId
     * @return
     */
    Optional<String> findWhetherIssueCertificateByBusniessId(String businessId);

    /**
     * 查询用户是否拥有指定专题的证书
     * @param businessId
     * @param currentUserId
     * @return
     */
    boolean whetherHavCertificate(String businessId, String currentUserId);

    /**
     * 获得证书排名和领先百分比
     * @param businessId
     * @param member
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Map<String, Object> getCertificateOrder(String businessId, Member member);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<CertificateRecord> getCertificateRecordsByType(Integer page, Integer pageSize, Optional<Integer> businessType,
                                                        Optional<Long> startTime, Optional<Long> endTime);

    /**
     * 查询人员信息
     * @param memberNames
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<Member> findByMemberNames(List<String> memberNames);

    /** 根据员工id查找证书发放记录 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<CertificateRecord> findByMemberIds(List<String> ids);

    /** 根据专题编码查询证书id */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<CourseInfo> findSubjectCertificateByCodes(List<String> codes);

    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    Optional<CertificateRecord> findSubjectCertificateByCourseId(String id);

    /**
     * 根据业务id查询证书，只用于数据修复
     * @param ids
     * @return
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<CertificateRecord> findSubjectCertificateByCourseIds(List<String> ids);

    /** 根据courseIds和memberIds查询已完成的course */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    List<CourseStudyProgress> findFinishSubject(List<String> memberIds, List<String> courseIds);

    /** 查询重塑专区手动发放证书列表 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<CertificateRecord> findSelect(Integer page, Integer pageSize, Optional<String> fullName, Optional<String> name, Optional<String> courseName, Optional<String> courseCode, Integer type, Optional<Long> createBeginTime, Optional<Long> createEndTime);
    /** 查询重塑专区手动发放证书列表 */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<CertificateRecord> findSelect(Optional<String> fullName, Optional<String> name, Optional<String> courseName, Optional<String> courseCode, Optional<Long> createBeginTime, Optional<Long> createEndTime);

    /**
     * 删除学员证书记录
     * @return
     */
    @Transactional
    int deleteCertificateRecord(String id);

    @Transactional(readOnly = true, propagation = Propagation.NOT_SUPPORTED)
    List<AudienceObject> findByBusinessByMember(List<String> memberId, List<String> businessId, int businessType);

    List<CertificateRecord> findCertificateRecord(List<String> memberIds, List<String> businessIds, int type);

    void deleteCertificateRecord(List<String> ids);
    @Transactional
    void insertRecords(List<CertificateRecord> certificateRecordList);

    /**
     * 个人中心-我的证书（专题）
     *
     * @param page  当前页
     * @param pageSize  每页展示数据条数
     * @param currentUserId 当前用户id
     * @param businessType 业务类型
     * @param startTime
     * @param endTime
     * @return 出参
     */
    @Transactional(readOnly = true, propagation = Propagation.SUPPORTS)
    PagedResult<CertificateRecord> otherCertificateRecords(Integer page,
                                                           Integer pageSize,
                                                           String currentUserId,
                                                           Optional<Integer> businessType, Optional<Long> startTime, Optional<Long> endTime);


    /**
     * 查询证书详情
     *
     * @param id 证书id
     * @return 出参
     */
    CertificateRecord singleCertificate(String id);
}
