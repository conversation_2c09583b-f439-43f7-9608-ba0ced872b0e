package com.zxy.product.course.service.support;

import com.alibaba.fastjson.JSON;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.DigitalIntelligenceMentorService;
import com.zxy.product.course.entity.DigitalIntelligenceMentor;
import com.zxy.product.course.service.dto.ModelRequestDto;
import com.zxy.product.course.service.dto.ModelResponseDto;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.tables.DigitalIntelligenceMentor.DIGITAL_INTELLIGENCE_MENTOR;

/**
 * 数智导师对话记录服务实现类
 */
@Service
public class DigitalIntelligenceMentorServiceSupport implements DigitalIntelligenceMentorService {

    private static final Logger logger = LoggerFactory.getLogger(DigitalIntelligenceMentorServiceSupport.class);

    /**
     * 默认回答内容
     */
    private static final String DEFAULT_ANSWER = "您提问的问题暂时无法回答，AI数智导师将继续学习。";

    private CommonDao<DigitalIntelligenceMentor> digitalIntelligenceMentorDao;

    @Value("${digital.intelligence.mentor.model.url}")
    private String modelUrl;

    @Value("${digital.intelligence.mentor.model.timeout:8000}")
    private Integer modelTimeout;

    @Value("${digital.intelligence.mentor.conversation.limit:30}")
    private Integer conversationLimit;

    @Value("${digital.intelligence.mentor.group.env:}")
    private String groupEnvEnabled;

    @Autowired
    public void setDigitalIntelligenceMentorDao(CommonDao<DigitalIntelligenceMentor> digitalIntelligenceMentorDao) {
        this.digitalIntelligenceMentorDao = digitalIntelligenceMentorDao;
    }

    @Override
    public List<DigitalIntelligenceMentor> findLatestConversationsByMemberId(String memberId) {
        return digitalIntelligenceMentorDao.execute(context ->
            context.select()
                .from(DIGITAL_INTELLIGENCE_MENTOR)
                .where(DIGITAL_INTELLIGENCE_MENTOR.MEMBER_ID.eq(memberId))
                .orderBy(DIGITAL_INTELLIGENCE_MENTOR.CREATE_TIME.desc())
                .limit(conversationLimit)
                .fetch(r -> r.into(DigitalIntelligenceMentor.class))
        ).stream()
         .sorted((a, b) -> a.getCreateTime().compareTo(b.getCreateTime())) // 按时间升序排列，符合交流展示顺序
         .collect(Collectors.toList());
    }

    @Override
    public DigitalIntelligenceMentor insert(DigitalIntelligenceMentor digitalIntelligenceMentor) {
        digitalIntelligenceMentorDao.insert(digitalIntelligenceMentor);
        return digitalIntelligenceMentor;
    }

    @Override
    public void updateBotResponse(String id, String botResponse) {
        digitalIntelligenceMentorDao.execute(context ->
            context.update(DIGITAL_INTELLIGENCE_MENTOR)
                .set(DIGITAL_INTELLIGENCE_MENTOR.BOT_RESPONSE, botResponse)
                .where(DIGITAL_INTELLIGENCE_MENTOR.ID.eq(id))
                .execute()
        );
    }

    @Override
    public DigitalIntelligenceMentor askModel(String memberId, String userQuery) {
        logger.info("用户{}提交问题给大模型: {}", memberId, userQuery);

        // 1. 先写入数据库，生成记录ID作为msgId
        DigitalIntelligenceMentor record = new DigitalIntelligenceMentor();
        record.forInsert(); // 生成ID和创建时间
        record.setMemberId(memberId);
        record.setUserQuery(userQuery);

        // 插入数据库
        this.insert(record);
        String msgId = record.getId();

        logger.info("已创建对话记录，ID: {}", msgId);

        String finalAnswer = DEFAULT_ANSWER; // 默认回答
        boolean callSuccess = false;

        try {
            // 2. 调用大模型接口
            ModelRequestDto modelRequest;
            if (groupEnvEnabled != null && !groupEnvEnabled.trim().isEmpty()) {
                // 如果配置了groupEnv且不为空，则使用配置的参数值
                modelRequest = new ModelRequestDto(msgId, userQuery, groupEnvEnabled);
            } else {
                // 如果没有配置或者配置为空，则不添加groupEnv参数
                modelRequest = new ModelRequestDto(msgId, userQuery);
            }
            String requestJson = JSON.toJSONString(modelRequest);

            logger.info("调用大模型接口: {}, 超时时间: {}ms, 请求参数: {}", modelUrl, modelTimeout, requestJson);

            // 使用自定义超时配置发送POST请求
            String responseBody = httpPostWithTimeout(modelUrl, requestJson, modelTimeout);

            if (responseBody != null && !responseBody.isEmpty()) {
                logger.info("大模型响应: {}", responseBody);

                // 3. 解析响应
                ModelResponseDto modelResponse = JSON.parseObject(responseBody, ModelResponseDto.class);

                if ("0".equals(modelResponse.getCode()) && msgId.equals(modelResponse.getMsgId())) {
                    // 调用成功，使用大模型的回答
                    finalAnswer = modelResponse.getAnswer();
                    callSuccess = true;
                    logger.info("大模型调用成功，ID: {}", msgId);
                } else {
                    logger.error("大模型响应异常，code: {}, msgId: {}, 期望msgId: {}, 使用默认回答",
                        modelResponse.getCode(), modelResponse.getMsgId(), msgId);
                }
            } else {
                logger.error("调用大模型接口失败，响应为空，使用默认回答");
            }

        } catch (Exception e) {
            logger.error("调用大模型接口异常，msgId: {}, 使用默认回答，异常信息: {}", msgId, e.getMessage());
        }

        // 4. 更新数据库中的bot_response字段（无论成功还是失败都要更新）
        this.updateBotResponse(msgId, finalAnswer);
        record.setBotResponse(finalAnswer);

        if (callSuccess) {
            logger.info("已更新对话记录的大模型响应，ID: {}", msgId);
        } else {
            logger.info("已更新对话记录的默认响应，ID: {}", msgId);
        }

        return record;
    }

    /**
     * 发送POST请求，支持自定义超时时间
     * @param url 请求URL
     * @param jsonString JSON请求体
     * @param timeoutMs 超时时间（毫秒）
     * @return 响应内容
     */
    private String httpPostWithTimeout(String url, String jsonString, int timeoutMs) {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(url);

        try {
            // 设置超时配置
            RequestConfig requestConfig = RequestConfig.custom()
                .setSocketTimeout(timeoutMs)
                .setConnectTimeout(timeoutMs)
                .setConnectionRequestTimeout(timeoutMs)
                .build();
            httpPost.setConfig(requestConfig);

            // 设置请求头和请求体
            httpPost.setHeader("Content-Type", "application/json");
            if (jsonString != null) {
                StringEntity entity = new StringEntity(jsonString, ContentType.APPLICATION_JSON);
                httpPost.setEntity(entity);
            }

            // 执行请求
            return httpClient.execute(httpPost, response -> {
                int statusCode = response.getStatusLine().getStatusCode();
                if (statusCode >= 200 && statusCode < 300) {
                    return EntityUtils.toString(response.getEntity(), "UTF-8");
                } else {
                    logger.error("HTTP请求失败，状态码: {}", statusCode);
                    return null;
                }
            });

        } catch (Exception e) {
            logger.error("HTTP请求异常: {}", e.getMessage());
            throw new RuntimeException("调用大模型接口失败: " + e.getMessage(), e);
        } finally {
            httpPost.releaseConnection();
            try {
                httpClient.close();
            } catch (Exception e) {
                logger.warn("关闭HttpClient异常: {}", e.getMessage());
            }
        }
    }
}
