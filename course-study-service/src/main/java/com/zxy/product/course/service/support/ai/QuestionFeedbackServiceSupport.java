package com.zxy.product.course.service.support.ai;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.course.QuestionFeedbackService;
import com.zxy.product.course.entity.CourseFeedback;
import com.zxy.product.course.entity.Member;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.SelectJoinStep;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.MEMBER;
import static com.zxy.product.course.jooq.tables.CourseFeedback.COURSE_FEEDBACK;
import static org.jooq.impl.DSL.partitionBy;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/29/9:31
 * @Description:
 */
@Service
public class QuestionFeedbackServiceSupport implements QuestionFeedbackService {

    private CommonDao<CourseFeedback> courseFeedbackDao;

    @Autowired
    public void setcourseFeedbackDao(CommonDao<CourseFeedback> courseFeedbackDao) {
        this.courseFeedbackDao = courseFeedbackDao;
    }


    @Override
    public void add(CourseFeedback feedback){
        courseFeedbackDao.insert(feedback);
    }

    @Override
    public void update(CourseFeedback feedback){
        courseFeedbackDao.update(feedback);
    }

    @Override
    public void updateCreateTime(String questionId, Long createTime){
        courseFeedbackDao.execute(e->e.update(COURSE_FEEDBACK).set(COURSE_FEEDBACK.CREATE_TIME, createTime).where(COURSE_FEEDBACK.QUESTION_ID.eq(questionId))
                .execute());
    }


    @Override
    public Optional<CourseFeedback> getOptional(String id){
        return courseFeedbackDao.execute(e-> e.select(Fields.start().add(COURSE_FEEDBACK.ID,COURSE_FEEDBACK.FEEDBACK_CONTENT,COURSE_FEEDBACK.FEEDBACK_TYPE,COURSE_FEEDBACK.LIKE,COURSE_FEEDBACK.QUESTION_CONTENT,
                                COURSE_FEEDBACK.QUESTION_ID,COURSE_FEEDBACK.MODIFY_DATE,COURSE_FEEDBACK.CREATE_TIME, COURSE_FEEDBACK.ANSWER_CONTENT)
                        .add(MEMBER.ID,MEMBER.NAME,MEMBER.FULL_NAME)
                        .end())
                .from(COURSE_FEEDBACK)
                .leftJoin(MEMBER).on(COURSE_FEEDBACK.CREATE_MEMBER_ID.eq(MEMBER.ID))
                .where(COURSE_FEEDBACK.ID.eq(id))
                .fetchOptional(r->{
                    CourseFeedback recommend = r.into(CourseFeedback.class);
                    Member member = r.into(Member.class);
                    recommend.setMember(member);
                    return recommend;
                })
        );
    }

    @Override
    public Optional<CourseFeedback> getOptionalByQuestionId(String questionContent, String memberId){
        return courseFeedbackDao.execute(e-> e.select(Fields.start().add(COURSE_FEEDBACK.ID,COURSE_FEEDBACK.CREATE_TIME)
                        .end())
                .from(COURSE_FEEDBACK)
                .where(COURSE_FEEDBACK.QUESTION_CONTENT.eq(questionContent), COURSE_FEEDBACK.CREATE_MEMBER_ID.eq(memberId))
                .orderBy(COURSE_FEEDBACK.CREATE_TIME.asc()) //查询最新创建得那条
                .limit(1)
                .fetchOptional(r->{
                    CourseFeedback recommend = r.into(CourseFeedback.class);
                    Member member = r.into(Member.class);
                    recommend.setMember(member);
                    return recommend;
                })
        );
    }

    @Override
    public PagedResult<CourseFeedback> findPage(Integer page, Integer pageSize, Optional<String> content, Optional<String> name, Optional<String> fullName){
        List<Condition> conditions = Stream.of(
                content.map(COURSE_FEEDBACK.QUESTION_CONTENT::contains),
                name.map(MEMBER.NAME::contains),
                fullName.map(MEMBER.FULL_NAME::contains),
                Optional.of(CourseFeedback.CALCEL_LIKE_).map(COURSE_FEEDBACK.LIKE::notEqual)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        return courseFeedbackDao.fetchPage(page, pageSize, e->{
            SelectJoinStep<Record> step =
                    e.select(Fields.start().add(COURSE_FEEDBACK.ID,COURSE_FEEDBACK.FEEDBACK_CONTENT,COURSE_FEEDBACK.FEEDBACK_TYPE,COURSE_FEEDBACK.LIKE,COURSE_FEEDBACK.QUESTION_CONTENT,
                                    COURSE_FEEDBACK.QUESTION_ID,COURSE_FEEDBACK.MODIFY_DATE,COURSE_FEEDBACK.ANSWER_CONTENT,COURSE_FEEDBACK.CREATE_MEMBER_ID,
                                            COURSE_FEEDBACK.CREATE_TIME)
                                    .add(MEMBER.ID.as("memberId"),MEMBER.NAME,MEMBER.FULL_NAME)
                                    .add(DSL.max(COURSE_FEEDBACK.MODIFY_DATE)
                                            .over(partitionBy(COURSE_FEEDBACK.QUESTION_CONTENT))
                                            .as("modifydate"))
                                    .end())
                            .from(COURSE_FEEDBACK)
                            .leftJoin(MEMBER).on(COURSE_FEEDBACK.CREATE_MEMBER_ID.eq(MEMBER.ID));
            return step.where(conditions).orderBy(DSL.field(DSL.name("modifydate")).desc(),
                    COURSE_FEEDBACK.QUESTION_CONTENT.asc(),
                    COURSE_FEEDBACK.CREATE_MEMBER_ID.asc(),
                    COURSE_FEEDBACK.CREATE_TIME.desc());
        },r->{
            CourseFeedback recommend = r.into(CourseFeedback.class);
            Member member = r.into(Member.class);
            member.setId(r.getValue(MEMBER.ID.as("memberId")));
            recommend.setMember(member);
            return recommend;
        });

    }


    @Override
    public List<CourseFeedback> findList(List<String> ids){
       return courseFeedbackDao.execute(e->e.select(COURSE_FEEDBACK.ID,COURSE_FEEDBACK.FEEDBACK_CONTENT,COURSE_FEEDBACK.FEEDBACK_TYPE,COURSE_FEEDBACK.LIKE,COURSE_FEEDBACK.QUESTION_CONTENT,
                COURSE_FEEDBACK.QUESTION_ID,COURSE_FEEDBACK.MODIFY_DATE,COURSE_FEEDBACK.ANSWER_CONTENT).from(COURSE_FEEDBACK)
                .where(COURSE_FEEDBACK.QUESTION_ID.in(ids))
                .fetchInto(CourseFeedback.class)
        );

    }
}
