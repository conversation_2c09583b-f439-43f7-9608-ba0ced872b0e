package com.zxy.product.course.service.support.audience;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.audience.AudienceKService;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.AudienceItem;
import com.zxy.product.course.entity.AudienceObject;
import com.zxy.product.course.entity.DeleteDataCourse;
import org.jooq.Record1;
import org.jooq.SelectConditionStep;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.*;

/**
 * Created by keeley on 2017/9/20.
 */
@Service
public class AudienceKServiceSupport implements AudienceKService {
    public static final Logger logger = LoggerFactory.getLogger(AudienceKServiceSupport.class);

    private static final String CACHE_KEY_PREFIX = "data-screen-permitted";

    private static final String BUSINESS_ID = "921af2f3-7dab-0612-e439-54c9515c1a0f";

    private CommonDao<AudienceItem> audienceItemCommonDao;
    private CommonDao<AudienceObject> audienceObjectCommonDao;
    private MessageSender messageSender;
    private CommonDao<DeleteDataCourse> dataCourseCommonDao;

    private Redis redis;

    @Autowired
    public void setDataCourseCommonDao(CommonDao<DeleteDataCourse> dataCourseCommonDao){
        this.dataCourseCommonDao = dataCourseCommonDao;
    }


    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setAudienceItemCommonDao(CommonDao<AudienceItem> audienceItemCommonDao) {
        this.audienceItemCommonDao = audienceItemCommonDao;
    }
    @Autowired
    public void setAudienceObjectCommonDao(CommonDao<AudienceObject> audienceObjectCommonDao) {
        this.audienceObjectCommonDao = audienceObjectCommonDao;
    }

    @Autowired
    public void setRedis(Redis redis) {
        this.redis = redis;
    }

    private static <T> Predicate<T> distinctByKey(Function<? super T, Object> keyExtractor) {
        Map<Object, Boolean> map = new ConcurrentHashMap<>();
        return t -> map.putIfAbsent(keyExtractor.apply(t), Boolean.TRUE) == null;
    }
    public List<AudienceItem> getAudienceItem(String id, int businessType) {

        return audienceItemCommonDao.execute(e -> e.select(Fields.start()
                .add(AUDIENCE_ITEM.JOIN_ID,AUDIENCE_ITEM.JOIN_NAME,AUDIENCE_ITEM.JOIN_TYPE,
                        AUDIENCE_ITEM.ORGANIZATION_ID,AUDIENCE_ITEM.MEMBER_ID,AUDIENCE_ITEM.URI).end())
                .from(AUDIENCE_OBJECT).leftJoin(AUDIENCE_ITEM)
                .on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_ITEM.ID).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(businessType)))
                .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(id).and(AUDIENCE_ITEM.ID.isNotNull()))
                .fetchInto(AudienceItem.class))
                .stream().filter(distinctByKey(x->x.getJoinType()+'-'+x.getJoinId())).collect(Collectors.toList());
    }

    public int deleteAudience(String businessId, int businessType) {
        List<String> audienceObjectIds = audienceItemCommonDao.execute(e -> e.select(AUDIENCE_OBJECT.ID).from(AUDIENCE_OBJECT)
                .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(businessId)).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(businessType)).fetch(AUDIENCE_OBJECT.ID));
        Integer flag = audienceItemCommonDao.execute(e -> e.deleteFrom(AUDIENCE_OBJECT)
                .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(businessId), AUDIENCE_OBJECT.BUSINESS_TYPE.eq(businessType)).execute());
        dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(DeleteDataCourse.AUDIENCE_OBJECT, audienceObjectIds, ""));
        return flag;
    }

    @Override
    public void deleteAudienceConstructionType(String businessId) {
        audienceItemCommonDao.execute(e -> e.deleteFrom(AUDIENCE_OBJECT)
                .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(businessId)).execute());
    }

    public int insertAudience(List<AudienceObject> objects){
        audienceObjectCommonDao.insert(objects);
        return objects.size();
    }

    @Override
    public boolean checkPermission(String memberId) {
        Integer count = audienceObjectCommonDao.execute(ctx -> {
                SelectConditionStep<Record1<String>> step = ctx.select(AUDIENCE_MEMBER.MEMBER_ID)
                    .from(AUDIENCE_OBJECT).innerJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                    .where(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COCKPIT).and(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId)));
                return ctx.fetchCount(step);
            }
        );
        return count > 0;
    }

    private List<AudienceItem> findExist(List<AudienceItem> items) {
        if(items.isEmpty()) {
            return items;
        }
        List<AudienceItem> result = new ArrayList<>();
//        List<String> ids, orgs;
        //包含
//        Predicate<AudienceItem> isSimpleItem = x-> Stream.of(simpleType).anyMatch(y -> x.getJoinType().equals(y));

        // 普通类型
//        ids = items.stream().filter(isSimpleItem).map(AudienceItemEntity::getJoinId).collect(Collectors.toList());
        // updated by wangdongyan 2018-12-28  人员类型
        Optional<List<String>> memberItemsOptional = Optional.ofNullable(items.stream().filter(x -> x.getJoinType().equals(AudienceItem.JOIN_TYPE_MEMBER)).map(AudienceItem::getJoinId).collect(Collectors.toList()));
        memberItemsOptional.ifPresent(x -> {
            result.addAll(audienceItemCommonDao.fetch(AUDIENCE_ITEM.JOIN_TYPE.eq(AudienceItem.JOIN_TYPE_MEMBER).and(AUDIENCE_ITEM.JOIN_ID.in(x))));
        });


        // 不包含子部门
        Optional<List<String>> org1ItemsOptional = Optional.ofNullable(items.stream().filter(x -> x.getJoinType().equals(AudienceItem.JOIN_TYPE_ORG_EXCLUDE)).map(AudienceItem::getJoinId).collect(Collectors.toList()));
        org1ItemsOptional.ifPresent(x -> {
            result.addAll(audienceItemCommonDao.fetch(AUDIENCE_ITEM.JOIN_TYPE.eq(AudienceItem.JOIN_TYPE_ORG_EXCLUDE).and(AUDIENCE_ITEM.JOIN_ID.in(x))));
        });

        // 包含子部门
        Optional<List<String>> org2ItemsOptional = Optional.ofNullable(items.stream().filter(x -> x.getJoinType().equals(AudienceItem.JOIN_TYPE_ORG_INCLUDE)).map(AudienceItem::getJoinId).collect(Collectors.toList()));
        org2ItemsOptional.ifPresent(x -> {
            result.addAll(audienceItemCommonDao.fetch(AUDIENCE_ITEM.JOIN_TYPE.eq(AudienceItem.JOIN_TYPE_ORG_INCLUDE).and(AUDIENCE_ITEM.JOIN_ID.in(x))));
        });

        // updated by wdy 异步消费过慢 职位
        Optional<List<String>> positionItemsOptional = Optional.ofNullable(items.stream().filter(x -> x.getJoinType().equals(AudienceItem.JOIN_TYPE_POST)).map(AudienceItem::getJoinId).collect(Collectors.toList()));
        positionItemsOptional.ifPresent(x -> {
            result.addAll(audienceItemCommonDao.fetch(AUDIENCE_ITEM.JOIN_TYPE.eq(AudienceItem.JOIN_TYPE_POST).and(AUDIENCE_ITEM.JOIN_ID.in(x))));
        });

        // updated by wdy 异步消费过慢 职务
        Optional<List<String>> jobItemsOptional = Optional.ofNullable(items.stream().filter(x -> x.getJoinType().equals(AudienceItem.JOIN_TYPE_JOB)).map(AudienceItem::getJoinId).collect(Collectors.toList()));
        jobItemsOptional.ifPresent(x -> {
            result.addAll(audienceItemCommonDao.fetch(AUDIENCE_ITEM.JOIN_TYPE.eq(AudienceItem.JOIN_TYPE_JOB).and(AUDIENCE_ITEM.JOIN_ID.in(x))));
        });

        // 群组，职务，职位
//        Condition condition4 = items.stream().filter(isSimpleItem.negate())
//                .map(x-> AUDIENCE_ITEM.JOIN_TYPE.eq(x.getJoinType())
//                        .and(AUDIENCE_ITEM.JOIN_ID.eq(x.getJoinId()))
//                        .and(AUDIENCE_ITEM.ORGANIZATION_ID.eq(x.getOrganizationId()))
//                        .and(AUDIENCE_ITEM.MEMBER_ID.eq(x.getMemberId()))
//                        .and(AUDIENCE_ITEM.URI.eq(x.getUri()))
//                )
//                .reduce((x,y)->x.or(y)).orElse(DSL.falseCondition());
//
//        result.addAll(audienceItemCommonDao.fetch(condition4));

        // add by wdy 群组
        Optional<List<String>> tagItemsOptional = Optional.ofNullable(items.stream().filter(x -> x.getJoinType().equals(AudienceItem.JOIN_TYPE_MEMBER_TAG)).map(AudienceItem::getJoinId).collect(Collectors.toList()));
        tagItemsOptional.ifPresent(x -> {
            result.addAll(audienceItemCommonDao.fetch(AUDIENCE_ITEM.JOIN_TYPE.eq(AudienceItem.JOIN_TYPE_MEMBER_TAG).and(AUDIENCE_ITEM.JOIN_ID.in(x))));

        });

        return result;
    }
    private List<AudienceItem> replaceItems(List<AudienceItem> audienceItems) {
        if (audienceItems == null || audienceItems.isEmpty())   return audienceItems;

        Predicate<AudienceItem> isSimpleItem = x-> Stream.of(simpleType).anyMatch(y -> x.getJoinType().equals(y));
        // 过滤去重受众项
        Map<String, AudienceItem> resultMap = audienceItems.stream()
                .collect(Collectors.toMap((i) -> i.getJoinType() + i.getJoinId(), item -> item, (o, n) -> n));
        List<AudienceItem> resultItemList = resultMap.values().stream().collect(Collectors.toList());

        // 数据库中已有的受众项
        List<AudienceItem> existList = this.findExist(resultItemList);
        Predicate<AudienceItem> existFlag = x-> existList.stream().anyMatch(y-> {
            boolean itemFlag = y.getJoinType().equals(x.getJoinType()) && y.getJoinId().equals(x.getJoinId());
            boolean extFlag =
                    y.getOrganizationId()!=null && y.getOrganizationId().equals(x.getOrganizationId()) &&
                    y.getMemberId()!=null && y.getMemberId().equals(x.getMemberId()) &&
                    y.getUri().equals(x.getUri());
            return isSimpleItem.test(y)? itemFlag : (itemFlag && extFlag);
        });
        // 数据库对比当前新增的 存在的。
        List<AudienceItem> ownedList = existList.stream().filter(existFlag).collect(Collectors.toList());
        // 当前新增对比数据库 不存在的
        List<AudienceItem> noneList = resultItemList.stream().filter(existFlag.negate()).map(r -> {
            r.forInsert();
            return r;
        }).collect(Collectors.toList());

        audienceItemCommonDao.insert(noneList);
        // 对于数据库中没有的受众项,需要发消息,计算受众人
        noneList.forEach(x->{
            x.setHandleStatus(0);//没有的数据设置为需要修改状态
             messageSender.send(MessageTypeContent.AUDIENCE_ITEM_K_INSERT, MessageHeaderContent.ID, x.getId());
                }
        );

        // 得到所有的受众项,用于返回
        ownedList.addAll(noneList);
        return ownedList;
    }

    /**
     * 重设课程和分享组织的受众对象,update受众项
     *
     **/
    @Override
    public int updateAudience(String bussinessId, Integer bussinessType, String memberId, String uri, List<AudienceItem> audienceItems) {
        this.deleteAudience(bussinessId, bussinessType);
        return appendAudience(bussinessId, bussinessType, memberId, uri, audienceItems);
    }

    /**
     * 重设课程和分享组织的受众对象,update受众项
     **/
    @Override
    public int appendAudience(String bussinessId, Integer bussinessType, String memberId, String uri, List<AudienceItem> audienceItems) {
        if (audienceItems.isEmpty()) return 0;
        // 重新建立关系
        audienceItems = audienceItems.stream().map(x -> {
            x.setMemberId(memberId);
            x.setUri(uri);
            return x;
        }).collect(Collectors.toList());
        List<AudienceItem> items = this.replaceItems(audienceItems);

        List<AudienceObject> audienceObjects = items.stream().map(x -> {
            AudienceObject ao = new AudienceObject();
            ao.forInsert();
            ao.setBusinessId(bussinessId);
            ao.setBusinessType(bussinessType);
            ao.setItemId(x.getId());
            if (AudienceObject.PUSH_DEFULT.equals(x.getHandleStatus())) {
                ao.setHandleStatus(AudienceObject.PUSH_DEFULT);
            } else {
                ao.setHandleStatus(AudienceObject.PUSH_TRUE);
            }
            return ao;
        }).collect(Collectors.toList());
        this.insertAudience(audienceObjects);

        setDataScreenCache(Optional.of(bussinessId));

        return audienceObjects.size();

    }

    @Override
    public void setDataScreenCache(Optional<String> businessId) {
        if (businessId.isPresent() &&  BUSINESS_ID.equals(businessId.get())) {
            redis.process(jedis -> jedis.del(CACHE_KEY_PREFIX));
            List<String> memberIds = audienceItemCommonDao.execute(x -> x.selectDistinct(AUDIENCE_MEMBER.MEMBER_ID).from(AUDIENCE_OBJECT)
                                                                         .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                                                                         .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(BUSINESS_ID))
                                                                         .fetch(AUDIENCE_MEMBER.MEMBER_ID));


            if (CollectionUtils.isNotEmpty(memberIds)) {
                // 添加memberId缓存
                String[] ids = memberIds.stream().filter(StringUtils::isNotEmpty).toArray(String[]::new);
                redis.process(jedis -> jedis.sadd(CACHE_KEY_PREFIX, ids));
            }
        }
    }

    @Override
    public List<AudienceItem> getAudienceByBusinessId(String id) {
        return audienceItemCommonDao.execute(r->
                r.select(AUDIENCE_ITEM.JOIN_ID)
                        .from(AUDIENCE_OBJECT)
                        .leftJoin(AUDIENCE_ITEM).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_ITEM.ID))
                        .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(id))
                        .fetch(o ->{
                            AudienceItem item = new AudienceItem();
                            item.setJoinId(o.getValue(AUDIENCE_ITEM.JOIN_ID));
                            return item;
                        }));
    }
}
