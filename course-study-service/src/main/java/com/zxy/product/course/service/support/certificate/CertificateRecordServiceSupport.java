package com.zxy.product.course.service.support.certificate;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.SubjectMemberBlacklistService;
import com.zxy.product.course.api.certificate.CertificateRecordService;
import com.zxy.product.course.content.*;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.jooq.Tables;
import com.zxy.product.course.util.DesensitizationUtil;
import com.zxy.product.course.util.EncryptUtil;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.*;
import static com.zxy.product.course.jooq.Tables.CERTIFICATE_RECORD_CHBN;
import static com.zxy.product.course.jooq.tables.BusinessCertificate.BUSINESS_CERTIFICATE;
import static com.zxy.product.course.jooq.tables.CertificateRecord.CERTIFICATE_RECORD;
import static com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO;
import static com.zxy.product.course.jooq.tables.CourseStudyProgress.COURSE_STUDY_PROGRESS;
import static com.zxy.product.course.jooq.tables.Member.MEMBER;
import static com.zxy.product.course.jooq.tables.RemodelingRoleDetail.REMODELING_ROLE_DETAIL;
import static com.zxy.product.course.jooq.tables.RemodelingRoleIssueTimeConfig.REMODELING_ROLE_ISSUE_TIME_CONFIG;

@Service
public class CertificateRecordServiceSupport implements CertificateRecordService {

    private static final Logger log = LoggerFactory.getLogger(CertificateRecordServiceSupport.class);
    private CommonDao<BusinessCertificate> businessCertificateCommonDao;
    private MessageSender messageSender;
    private CommonDao<CertificateRecord> certificateRecordCommonDao;
    private CommonDao<SubAuthenticated> subAuthenticatedCommonDao;
    private CommonDao<CourseInfo> infoCommonDao;
    private CommonDao<Member> memberDao;
    private CommonDao<AudienceObject> audienceObjectCommonDao;
    private CommonDao<CertificateRecordChbn> recordChbnCommonDao;
    private static final BigDecimal STATIC_NUMBER = new BigDecimal(300000);
    @Resource
    private SubjectMemberBlacklistService subjectMemberBlacklistService;
    @Resource
    private CommonDao<DeleteDataCourse> dataCourseCommonDao;

    @Autowired
    public void setRecordChbnCommonDao(CommonDao<CertificateRecordChbn> recordChbnCommonDao) {
        this.recordChbnCommonDao = recordChbnCommonDao;
    }

    @Autowired
    public void setSubAuthenticatedCommonDao(CommonDao<SubAuthenticated> subAuthenticatedCommonDao) {
        this.subAuthenticatedCommonDao = subAuthenticatedCommonDao;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Autowired
    public void setBusinessCertificateCommonDao(CommonDao<BusinessCertificate> businessCertificateCommonDao) {
        this.businessCertificateCommonDao = businessCertificateCommonDao;
    }

    @Override
    public int getCertificateUseNumber(String certificateId) {
        return businessCertificateCommonDao.execute(x -> x.select(BUSINESS_CERTIFICATE.ID.count()).from(BUSINESS_CERTIFICATE).where(BUSINESS_CERTIFICATE.CERTIFICATE_ID.eq(certificateId)).fetchOne(BUSINESS_CERTIFICATE.ID.count()));
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setCertificateRecordCommonDao(CommonDao<CertificateRecord> certificateRecordCommonDao) {
        this.certificateRecordCommonDao = certificateRecordCommonDao;
    }

    @Autowired
    public void setInfoCommonDao(CommonDao<CourseInfo> infoCommonDao) {
        this.infoCommonDao = infoCommonDao;
    }

    @Autowired
    public void setAudienceObjectCommonDao(CommonDao<AudienceObject> audienceObjectCommonDao) {
        this.audienceObjectCommonDao = audienceObjectCommonDao;
    }
    @Override
    public BusinessCertificate insert(String businessId, String certificateId, Integer businessType) {
        return insert(businessId, certificateId, businessType, null);
    }

    @Override
    public BusinessCertificate insert(String businessId, String certificateId, Integer businessType, CourseInfo courseInfo) {
        // 如果业务关联表中存在业务id关联的certificateId，直接修改关联的证书模板即可
        Optional<BusinessCertificate> certificateOptional = businessCertificateCommonDao.execute(x -> x.select(BUSINESS_CERTIFICATE.ID,BUSINESS_CERTIFICATE.CERTIFICATE_ID).from(BUSINESS_CERTIFICATE).where(BUSINESS_CERTIFICATE.BUSINESS_ID.eq(businessId), BUSINESS_CERTIFICATE.BUSINESS_TYPE.eq(businessType)).limit(1)).fetchOptional(r -> r.into(BusinessCertificate.class));
        //如果不相等，说明证书被更新了，更新证书的时间--只限专题
        if(Objects.equals(businessType, BusinessCertificate.BUSINESS_TYPE_SUBJECT) && (!certificateOptional.isPresent() || !Objects.equals(certificateId, certificateOptional.get().getCertificateId()))){
            log.info("更新证书时间" + businessId);
            if(Objects.nonNull(courseInfo)){
                courseInfo.setCertificateUpdateTime(System.currentTimeMillis());
            }
        }
        if (certificateOptional.isPresent()) {
            businessCertificateCommonDao.execute(x -> x.update(BUSINESS_CERTIFICATE)
                    .set(BUSINESS_CERTIFICATE.CERTIFICATE_ID, certificateId)
                    .where(BUSINESS_CERTIFICATE.ID.eq(certificateOptional.get().getId()))
                    .execute()
            );
            return certificateOptional.get();
        }
        BusinessCertificate businessCertificate = new BusinessCertificate();
        businessCertificate.forInsert();
        businessCertificate.setBusinessId(businessId);
        businessCertificate.setCertificateId(certificateId);
        businessCertificate.setBusinessType(businessType);
        businessCertificateCommonDao.insert(businessCertificate);
        // 如果是新添加的证书可能存在已完成的学员未获得证书，需要异步处理一下
        messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD_BATCH, MessageHeaderContent.BUSINESS_ID, businessId);
        return businessCertificate;
    }

    @Override
    public int delete(String businessId) {
        Optional<String> idOptional = businessCertificateCommonDao.execute(x -> x.select(BUSINESS_CERTIFICATE.ID).from(BUSINESS_CERTIFICATE).where(BUSINESS_CERTIFICATE.BUSINESS_ID.eq(businessId)).fetchOptional(BUSINESS_CERTIFICATE.ID));
        if (idOptional.isPresent()) {
            return businessCertificateCommonDao.delete(idOptional.get());
        }
        return 0;
    }

    @Override
    public BusinessCertificate getByBusinessId(String businessId) {
        return businessCertificateCommonDao.execute(x -> x.select(Fields.start().add(BUSINESS_CERTIFICATE).end()).from(BUSINESS_CERTIFICATE).where(BUSINESS_CERTIFICATE.BUSINESS_ID.eq(businessId))).fetchOptional(r -> r.into(BusinessCertificate.class)).orElse(null);
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public String getIdByBusinessId(String businessId) {
        return businessCertificateCommonDao.execute(x -> x.select(Fields.start().add(BUSINESS_CERTIFICATE.CERTIFICATE_ID).end()).from(BUSINESS_CERTIFICATE)
                .where(BUSINESS_CERTIFICATE.BUSINESS_ID.eq(businessId))).fetchOptional(BUSINESS_CERTIFICATE.CERTIFICATE_ID).orElse(null);
    }

    @Override
    public Map<String, Object> getRecordByBusinessIdAndMemberId(String businessId, String memberId) {
        String courseName = infoCommonDao.execute(x -> x.select(COURSE_INFO.NAME).from(COURSE_INFO).where(COURSE_INFO.ID.eq(businessId)).fetchOptional(COURSE_INFO.NAME)).orElse(null);
        if (courseName == null) return null;
        Record certificateRecord = certificateRecordCommonDao.execute(x -> x.select(Fields.start().add(CERTIFICATE_RECORD).end())
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.BUSINESS_ID.eq(businessId).and(CERTIFICATE_RECORD.MEMBER_ID.eq(memberId))).fetchOne());

        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "error");
        resultMap.put("message", "您未获得此证书");
        if (certificateRecord != null) {
            List<String> businessIdS = new ArrayList<>();
            businessIdS.add(businessId);
            if (subjectMemberBlacklistService.doesItExist(memberId, businessIdS).contains(businessId)) {
                resultMap.put("code", "error");
                resultMap.put("message", "您未获得此证书");
            } else {
                String fullName = memberDao.execute(x -> x.select(MEMBER.FULL_NAME)
                        .from(MEMBER)
                        .where(MEMBER.ID.eq(memberId))
                        .limit(1))
                        .fetchOne(Record1::value1);
                Optional<RemodelingRoleDetail> roleDetailOptional = infoCommonDao.execute(e ->
                        e.select(REMODELING_ROLE_DETAIL.ROLE_NAME, REMODELING_ROLE_DETAIL.CHAPTER_TYPE, REMODELING_ROLE_DETAIL.SECTION_TYPE, REMODELING_ROLE_DETAIL.ROLE_LEVEL)
                                .from(REMODELING_ROLE_DETAIL)
                                .where(REMODELING_ROLE_DETAIL.SUBJECT_ID.eq(businessId))
                                .fetchOptionalInto(RemodelingRoleDetail.class));

                resultMap = certificateRecord.intoMap();
                resultMap.put("userName", fullName);
                resultMap.put("subjectName", courseName);
                resultMap.put("code", "success");
                resultMap.put("message", "获取证书信息成功");

                if (roleDetailOptional.isPresent()) {
                    RemodelingRoleDetail roleDetail = roleDetailOptional.get();
                    resultMap.put("project_name", RemodelingRoleDetail.ChapterAbbreviation.getName(Integer.parseInt(roleDetail.getChapterType())));
                    resultMap.put("major_name", RemodelingRoleDetail.SectionAbbreviation.getName(Integer.parseInt(roleDetail.getSectionType())));
                    resultMap.put("level", RemodelingRoleDetail.RoleLevel.getName(roleDetail.getRoleLevel()));
                    resultMap.put("role_name", roleDetail.getRoleName());
                }
            }
        }
        return resultMap;
    }

    @Override
    public Map<String, Object> getSubAuthByBusinessIdAndMemberId(String businessId, Member member) {
        SubAuthenticated authenticatedInfo = subAuthenticatedCommonDao.getOptional(businessId).orElse(null);
        if (authenticatedInfo == null) return new HashMap<>();
        Record certificateRecord = certificateRecordCommonDao.execute(x -> x.select(Fields.start().add(CERTIFICATE_RECORD).add(MEMBER.FULL_NAME).end())
                .from(CERTIFICATE_RECORD)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(CERTIFICATE_RECORD.MEMBER_ID))
                .where(CERTIFICATE_RECORD.BUSINESS_ID.eq(businessId).and(CERTIFICATE_RECORD.MEMBER_ID.eq(member.getId()))).fetchOne());
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("code", "error");
        resultMap.put("message", "您未获得此证书");
        if (certificateRecord != null) {
            List<String> businessIds = new ArrayList<>();
            businessIds.add(businessId);
            resultMap = certificateRecord.intoMap();
            resultMap.put("userName", member.getFullName());
            resultMap.put("subjectName", authenticatedInfo.getName());
            resultMap.put("code", "success");
            resultMap.put("message", "获取证书信息成功");
        }
        return resultMap;
    }

    /**
     * 修正证书颁发时间
     *
     * @param resultMap
     */
    public void handleRecordIssueTime(Map<String, Object> resultMap) {
        infoCommonDao.execute(e -> e.select(REMODELING_ROLE_ISSUE_TIME_CONFIG.ISSUE_TIME)
                .from(REMODELING_ROLE_ISSUE_TIME_CONFIG).where(REMODELING_ROLE_ISSUE_TIME_CONFIG.SUBJECT_ID.eq(resultMap.get("f_business_id").toString())))
                .fetchOptionalInto(String.class).ifPresent(s -> resultMap.put("f_issue_time", s));
    }

    @Override
    public void insertRecordList(List<CertificateRecord> certificateRecordList) {
        if (CollectionUtils.isNotEmpty(certificateRecordList)) {
            certificateRecordList.forEach(certificateRecord -> {
                List<String> list = certificateRecordCommonDao.execute(x -> x.select(Fields.start().add(CERTIFICATE_RECORD.ID).end())
                        .from(CERTIFICATE_RECORD)
                        .where(CERTIFICATE_RECORD.BUSINESS_ID.eq(certificateRecord.getBusinessId())
                                .and(CERTIFICATE_RECORD.MEMBER_ID.eq(certificateRecord.getMemberId()))).fetch(CERTIFICATE_RECORD.ID));
                if (!CollectionUtils.isNotEmpty(list)) {
                    certificateRecordCommonDao.insert(certificateRecord);
                    messageSender.send(MessageTypeContent.REMODELING_TRAIN_ACQUIRE_CERTIFICATE_BATCH,
                            MessageHeaderContent.IDS, certificateRecord.getMemberId(),
                            MessageHeaderContent.BUSINESS_ID, certificateRecord.getBusinessId());
                }
            });
        }
    }

    @Override
    public Optional<String> findWhetherIssueCertificateByBusniessId(String businessId) {
        return certificateRecordCommonDao.execute(x -> x.select(CERTIFICATE_RECORD.ID).from(CERTIFICATE_RECORD).where(CERTIFICATE_RECORD.BUSINESS_ID.eq(businessId)).limit(1).fetchOptional(CERTIFICATE_RECORD.ID));
    }

    @Override
    public boolean whetherHavCertificate(String businessId, String currentUserId) {
        Integer sum = certificateRecordCommonDao.execute(o ->
                o.select(CERTIFICATE_RECORD.ID.count())
                        .from(CERTIFICATE_RECORD)
                        .where(CERTIFICATE_RECORD.BUSINESS_ID.eq(businessId))
                        .and(CERTIFICATE_RECORD.MEMBER_ID.eq(currentUserId))
                        .and(CERTIFICATE_RECORD.CERTIFICATE_NUMBER.isNotNull())
                        .fetchOne(CERTIFICATE_RECORD.ID.count(), Integer.class)
        );
        return sum > 0;
    }

    @Override
    public Map<String, Object> getCertificateOrder(String businessId, Member member) {
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("name", member.getName());
        resultMap.put("fullName", member.getFullName());
        List<CertificateRecord> certificateRecords = getCertificateRecords(member, businessId);
        if (certificateRecords == null || certificateRecords.isEmpty()) {
            return resultMap;
        }
        CertificateRecord certificateRecord = certificateRecords.get(0);
        // 测试通过证书编号核对名次是否准确
        if (org.springframework.util.StringUtils.isEmpty(certificateRecord.getCertificateNumber())) {
            return resultMap;
        }
        // 如果证书编号转换的名次无效，查询数据库
        String certificateNumber = certificateRecord.getCertificateNumber();
        int order = Integer.valueOf(certificateNumber.substring(certificateNumber.length() - 6));
        if (order <= 10) {
            order = certificateRecordCommonDao.execute(
                    o -> o.select(CERTIFICATE_RECORD.ID.count())
                            .from(CERTIFICATE_RECORD)
                            .where(CERTIFICATE_RECORD.BUSINESS_ID.eq(businessId))
                            .and(CERTIFICATE_RECORD.FINISH_TIME.le(certificateRecord.getFinishTime()))
                            .fetchOne().value1()
            );
        }
        resultMap.put("order", order);
        BigDecimal percent = ((STATIC_NUMBER.subtract(new BigDecimal(order))).multiply(new BigDecimal(100))).divide(STATIC_NUMBER, 0, 4);
        resultMap.put("passNumber", percent.toString() + "%");
        return resultMap;
    }

    /**
     * 查询证书记录
     *
     * @param member
     * @param businessId
     * @return
     */
    private List<CertificateRecord> getCertificateRecords(Member member, String businessId) {

        return certificateRecordCommonDao.execute(dao -> dao
                .select(
                        CERTIFICATE_RECORD.CREATE_TIME,
                        CERTIFICATE_RECORD.ID,
                        CERTIFICATE_RECORD.FINISH_TIME,
                        CERTIFICATE_RECORD.CERTIFICATE_NUMBER
                )
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.MEMBER_ID.eq(member.getId()))
                .and(CERTIFICATE_RECORD.BUSINESS_ID.eq(businessId))
                .fetch(r -> {
                    CertificateRecord record = new CertificateRecord();
                    record.setCreateTime(r.get(CERTIFICATE_RECORD.CREATE_TIME, Long.class));
                    record.setId(r.get(CERTIFICATE_RECORD.ID, String.class));
                    record.setFinishTime(r.get(CERTIFICATE_RECORD.FINISH_TIME));
                    record.setCertificateNumber(r.getValue(CERTIFICATE_RECORD.CERTIFICATE_NUMBER));
                    return record;
                }));

    }


    /**
     * 查询证书记录 sftp接口用
     * @return
     */
    @Override
    public List<CertificateRecord> getCertificateRecordsByType(Integer page, Integer pageSize, Optional<Integer> businessType,
                                                               Optional<Long> startTime, Optional<Long> endTime) {
        List<Condition> conditions = Stream.of(
                        businessType.map(CERTIFICATE_RECORD.BUSINESS_TYPE::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        if(startTime.isPresent() && endTime.isPresent()){
            conditions.add(Tables.CERTIFICATE_RECORD.MODIFY_DATE.between(new Timestamp(startTime.get()), new Timestamp(endTime.get())));
        }
        return certificateRecordCommonDao.execute(dao -> dao
                .select(
                        CERTIFICATE_RECORD.ID,CERTIFICATE_RECORD.MEMBER_ID,CERTIFICATE_RECORD.CERTIFICATE_NUMBER,
                        CERTIFICATE_RECORD.BUSINESS_ID,CERTIFICATE_RECORD.TYPE,CERTIFICATE_RECORD.MODIFY_DATE
                )
                .from(CERTIFICATE_RECORD)
                .where(conditions)
                .orderBy(CERTIFICATE_RECORD.MODIFY_DATE.desc())
                .limit((page-1)*pageSize, pageSize)
                .fetchInto(CertificateRecord.class));

    }

    @Override
    public List<Member> findByMemberNames(List<String> memberNames) {
        return memberDao.execute(dao -> dao
                .select(
                        MEMBER.ID,
                        MEMBER.NAME,
                        MEMBER.FULL_NAME
                )
                .from(MEMBER)
                .where(MEMBER.NAME.in(memberNames))
                .fetch(r -> {
                    Member member = new Member();
                    member.setId(r.get(MEMBER.ID, String.class));
                    member.setName(r.get(MEMBER.NAME, String.class));
                    member.setFullName(r.get(MEMBER.FULL_NAME, String.class));
                    return member;
                }));
    }


    @Override
    public List<CertificateRecord> findByMemberIds(List<String> ids) {
        return certificateRecordCommonDao.execute(dao -> dao
                .select(
                        CERTIFICATE_RECORD.ID,
                        CERTIFICATE_RECORD.MEMBER_ID,
                        CERTIFICATE_RECORD.BUSINESS_ID
                )
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.MEMBER_ID.in(ids))
                .fetch(r -> {
                    CertificateRecord certificateRecord = new CertificateRecord();
                    certificateRecord.setId(r.get(CERTIFICATE_RECORD.ID));
                    certificateRecord.setMemberId(r.get(CERTIFICATE_RECORD.MEMBER_ID));
                    certificateRecord.setBusinessId(r.get(CERTIFICATE_RECORD.BUSINESS_ID));
                    return certificateRecord;
                }));
    }

    @Override
    public List<CourseInfo> findSubjectCertificateByCodes(List<String> codes) {
        return certificateRecordCommonDao.execute(dao -> dao
                .select(
                        COURSE_INFO.ID,
                        COURSE_INFO.CODE,
                        BUSINESS_CERTIFICATE.CERTIFICATE_ID
                )
                .from(COURSE_INFO)
                .leftJoin(BUSINESS_CERTIFICATE).on(COURSE_INFO.ID.eq(BUSINESS_CERTIFICATE.BUSINESS_ID))
                .where(COURSE_INFO.CODE.in(codes))
                .and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT))
                .fetch(r -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(r.get(COURSE_INFO.ID, String.class));
                    courseInfo.setCode(r.get(COURSE_INFO.CODE, String.class));
                    courseInfo.setCertificateId(r.get(BUSINESS_CERTIFICATE.CERTIFICATE_ID, String.class));
                    return courseInfo;
                }));
    }

    @Override
    public Optional<CertificateRecord> findSubjectCertificateByCourseId(String id) {
        //一个专题发证书可能多条，为了更新性能，暂用distinct
        return certificateRecordCommonDao.execute(dao -> dao
                .select(
                        CERTIFICATE_RECORD.BUSINESS_ID,
                        CERTIFICATE_RECORD.FINISH_TIME
                )
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.BUSINESS_ID.eq(id))
                .orderBy(CERTIFICATE_RECORD.FINISH_TIME.asc())
                .limit(1)
                .fetchOptionalInto(CertificateRecord.class));
    }

    @Override
    public List<CertificateRecord> findSubjectCertificateByCourseIds(List<String> ids) {
        //一个专题发证书可能多条，为了更新性能，暂用distinct
        return certificateRecordCommonDao.execute(dao -> dao
                .selectDistinct(
                        CERTIFICATE_RECORD.BUSINESS_ID,
                        CERTIFICATE_RECORD.FINISH_TIME
                )
                .from(CERTIFICATE_RECORD)
                .where(CERTIFICATE_RECORD.BUSINESS_ID.in(ids))
                .fetchInto(CertificateRecord.class));
    }

    @Override
    public List<CourseStudyProgress> findFinishSubject(List<String> memberIds, List<String> courseIds) {
        return certificateRecordCommonDao.execute(dao -> dao
                .select(
                        COURSE_STUDY_PROGRESS.COURSE_ID,
                        COURSE_STUDY_PROGRESS.MEMBER_ID,
                        COURSE_STUDY_PROGRESS.FINISH_TIME
                )
                .from(COURSE_STUDY_PROGRESS)
                .where(COURSE_STUDY_PROGRESS.COURSE_ID.in(courseIds))
                .and(COURSE_STUDY_PROGRESS.MEMBER_ID.in(memberIds))
                .and(COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(CourseStudyProgress.FINISH_STATUS_FINISH)
                        .or(COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                .fetch(r -> {
                    CourseStudyProgress courseStudyProgress = new CourseStudyProgress();
                    courseStudyProgress.setCourseId(r.get(COURSE_STUDY_PROGRESS.COURSE_ID, String.class));
                    courseStudyProgress.setMemberId(r.get(COURSE_STUDY_PROGRESS.MEMBER_ID, String.class));
                    courseStudyProgress.setFinishTime(r.getValue(COURSE_STUDY_PROGRESS.FINISH_TIME, Long.class));
                    return courseStudyProgress;
                }));
    }

    @Override
    public PagedResult<CertificateRecord> findSelect(Integer page, Integer pageSize, Optional<String> fullName, Optional<String> name, Optional<String> courseName, Optional<String> courseCode, Integer type, Optional<Long> createBeginTime, Optional<Long> createEndTime) {
        return certificateRecordCommonDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start()
                            .add(CERTIFICATE_RECORD.ID)
                            .add(CERTIFICATE_RECORD.CREATE_TIME)
                            .add(MEMBER.FULL_NAME)
                            .add(MEMBER.NAME)
                            .add(COURSE_INFO.NAME)
                            .add(COURSE_INFO.CODE)
                            .end());

            SelectSelectStep<Record> selectCountField = e.select(Fields.start().add(CERTIFICATE_RECORD.ID.count()).end());

            List<Condition> conditions = Stream.of(
                    Optional.of(CERTIFICATE_RECORD.ACCESS_TYPE.eq(CertificateRecord.ACCESS_TYPE_IMPORT)),
                    fullName.map(MEMBER.FULL_NAME::contains),
                    name.map(MEMBER.NAME::contains),
                    courseName.map(COURSE_INFO.NAME::contains),
                    courseCode.map(COURSE_INFO.CODE::eq),
                    createBeginTime.map(CERTIFICATE_RECORD.CREATE_TIME::greaterOrEqual),
                    createEndTime.map(CERTIFICATE_RECORD.CREATE_TIME::lessOrEqual)

            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            if (type == 1) {
                conditions.add(CERTIFICATE_RECORD.BUSINESS_ID.in(CourseInfo.IMPORT_CERTIFICATE_SUBJECT_IDS_QJ));
            } else {
                conditions.add(CERTIFICATE_RECORD.BUSINESS_ID.in(CourseInfo.IMPORT_CERTIFICATE_SUBJECT_IDS));
            }

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(CERTIFICATE_RECORD)
                        .leftJoin(COURSE_INFO).on(CERTIFICATE_RECORD.BUSINESS_ID.eq(COURSE_INFO.ID))
                        .leftJoin(MEMBER).on(CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                        .where(conditions);
                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(CERTIFICATE_RECORD.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();
            // 增加单次导出超5000数据的错误提示
            // Optional.ofNullable(record).ifPresent(r -> ErrorCode.ExportMoreThan5000.throwIf(r.size() > 5_000));

            return PagedResult.create(count, record.stream().map(t -> {
                CertificateRecord certificateRecord = new CertificateRecord();
                certificateRecord.setId(t.getValue(CERTIFICATE_RECORD.ID));
                certificateRecord.setCreateTime(t.getValue(CERTIFICATE_RECORD.CREATE_TIME));
                Member member = new Member();
                member.setFullName(EncryptUtil.aesEncrypt(t.getValue(MEMBER.FULL_NAME), null));
                member.setName(DesensitizationUtil.desensitizeEmployeeId(t.getValue(MEMBER.NAME)));
                certificateRecord.setMember(member);
                CourseInfo courseInfo = new CourseInfo();
                courseInfo.setName(t.getValue(COURSE_INFO.NAME));
                courseInfo.setCode(t.getValue(COURSE_INFO.CODE));
                certificateRecord.setCourseInfo(courseInfo);
                return certificateRecord;
            }).collect(Collectors.toList()));
        });
    }

    @Override
    public PagedResult<CertificateRecord> findSelect( Optional<String> fullName, Optional<String> name, Optional<String> courseName, Optional<String> courseCode, Optional<Long> createBeginTime, Optional<Long> createEndTime) {
        return certificateRecordCommonDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start()
                            .add(CERTIFICATE_RECORD.ID)
                            .add(CERTIFICATE_RECORD.CREATE_TIME)
                            .add(MEMBER.FULL_NAME)
                            .add(MEMBER.NAME)
                            .add(COURSE_INFO.NAME)
                            .add(COURSE_INFO.CODE)
                            .end());

            List<Condition> conditions = Stream.of(
                    Optional.of(CERTIFICATE_RECORD.ACCESS_TYPE.eq(CertificateRecord.ACCESS_TYPE_IMPORT)),
                    Optional.of(CERTIFICATE_RECORD.BUSINESS_ID.in(CourseInfo.IMPORT_CERTIFICATE_SUBJECT_IDS)),
                    fullName.map(MEMBER.FULL_NAME::contains),
                    name.map(MEMBER.NAME::contains),
                    courseName.map(COURSE_INFO.NAME::contains),
                    courseCode.map(COURSE_INFO.CODE::eq),
                    createBeginTime.map(CERTIFICATE_RECORD.CREATE_TIME::greaterOrEqual),
                    createEndTime.map(CERTIFICATE_RECORD.CREATE_TIME::lessOrEqual)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(CERTIFICATE_RECORD)
                        .leftJoin(COURSE_INFO).on(CERTIFICATE_RECORD.BUSINESS_ID.eq(COURSE_INFO.ID))
                        .leftJoin(MEMBER).on(CERTIFICATE_RECORD.MEMBER_ID.eq(MEMBER.ID))
                        .where(conditions);
                return select;
            };


            Result<Record> record  = stepFunc.apply(selectListField).orderBy(CERTIFICATE_RECORD.CREATE_TIME.desc()).fetch();

            return PagedResult.create(0, record.stream().map(t -> {
                CertificateRecord certificateRecord = new CertificateRecord();
                certificateRecord.setId(t.getValue(CERTIFICATE_RECORD.ID));
                certificateRecord.setCreateTime(t.getValue(CERTIFICATE_RECORD.CREATE_TIME));
                Member member = new Member();
                member.setFullName(t.getValue(MEMBER.FULL_NAME));
                member.setName(t.getValue(MEMBER.NAME));
                certificateRecord.setMember(member);
                CourseInfo courseInfo = new CourseInfo();
                courseInfo.setName(t.getValue(COURSE_INFO.NAME));
                courseInfo.setCode(t.getValue(COURSE_INFO.CODE));
                certificateRecord.setCourseInfo(courseInfo);
                return certificateRecord;
            }).collect(Collectors.toList()));
        });
    }

    @Override
    public int deleteCertificateRecord(String id) {
        Optional<String> idOptional = certificateRecordCommonDao.execute(x ->
                x.select(CERTIFICATE_RECORD.ID)
                        .from(CERTIFICATE_RECORD)
                        .where(CERTIFICATE_RECORD.ID.eq(id))
                        .fetchOptional(CERTIFICATE_RECORD.ID));
        if (idOptional.isPresent()) {
            dataCourseCommonDao.insert(DeleteDataCourse
                    .getDeleteDataCourse(DeleteDataCourse.CERTIFICATE_RECORD, idOptional.get(), ""));
            return certificateRecordCommonDao.delete(idOptional.get());
        }
        return 0;
    }


    @Override
    public List<AudienceObject> findByBusinessByMember(List<String> memberId, List<String> businessId, int businessType) {
        return audienceObjectCommonDao.execute(context -> context
                .select(AUDIENCE_OBJECT.ID, AUDIENCE_OBJECT.BUSINESS_ID, AUDIENCE_MEMBER.MEMBER_ID).from(AUDIENCE_OBJECT)
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .where(AUDIENCE_OBJECT.BUSINESS_ID.in(businessId))
                .and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(businessType))
                .and(AUDIENCE_MEMBER.MEMBER_ID.in(memberId))
                .fetch(r -> {
                    AudienceObject ao = new AudienceObject();
                    ao.setId(r.getValue(AUDIENCE_OBJECT.ID));
                    ao.setBusinessId(r.getValue(AUDIENCE_OBJECT.BUSINESS_ID));
                    ao.setMemberId(r.getValue(AUDIENCE_MEMBER.MEMBER_ID));
                    return ao;
                })
        );
    }

    @Override
    public List<CertificateRecord> findCertificateRecord(List<String> memberIds, List<String> businessIds, int type) {
        return Optional.ofNullable(certificateRecordCommonDao.fetch(
                CERTIFICATE_RECORD.MEMBER_ID.in(memberIds)
                        .and(CERTIFICATE_RECORD.BUSINESS_ID.in(businessIds))
                        .and(CERTIFICATE_RECORD.BUSINESS_TYPE.eq(type))
        )).orElse(new ArrayList<>());
    }

    @Override
    public void deleteCertificateRecord(List<String> ids) {
        List<String> deleteIds = certificateRecordCommonDao.execute(x ->
                x.select(CERTIFICATE_RECORD.ID)
                        .from(CERTIFICATE_RECORD)
                        .where(CERTIFICATE_RECORD.ID.in(ids))
                        .fetch(CERTIFICATE_RECORD.ID));
        if (Objects.nonNull(deleteIds) && !deleteIds.isEmpty()) {
            dataCourseCommonDao.insert(DeleteDataCourse
                    .getDeleteDataCourseList(DeleteDataCourse.CERTIFICATE_RECORD, deleteIds, ""));
            certificateRecordCommonDao.delete(deleteIds);
        }
    }

    @Override
    public void insertRecords(List<CertificateRecord> certificateRecordList) {
        if (CollectionUtils.isNotEmpty(certificateRecordList)) {
            certificateRecordCommonDao.insert(certificateRecordList);
        }
    }

    /**
     * 个人中心-我的证书（专题）
     *
     * @param page          当前页
     * @param pageSize      每页展示数据条数
     * @param currentUserId 当前用户id
     * @param businessType  条件查询构造业务类型 businessType 入参
     * @param startTime
     * @param endTime
     * @return Page数据出参
     */
    @Override
    public PagedResult<CertificateRecord> otherCertificateRecords(Integer page,
                                                                  Integer pageSize,
                                                                  String currentUserId,
                                                                  Optional<Integer> businessType, Optional<Long> startTime, Optional<Long> endTime) {
        return certificateRecordCommonDao.execute(ew0 -> {
            Condition condition = Stream.of(businessType.map(CERTIFICATE_RECORD.BUSINESS_TYPE::eq),
                                            startTime.map(CERTIFICATE_RECORD.FINISH_TIME::ge),
                                            endTime.map(CERTIFICATE_RECORD.FINISH_TIME::le))
                    .filter(Optional::isPresent)
                    .map(Optional::get)
                    .reduce(Condition::and)
                    .orElse(DSL.trueCondition());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> generalFunc = ew1 ->
                    ew1.from(CERTIFICATE_RECORD)
                            .leftJoin(COURSE_INFO)
                            .on(COURSE_INFO.ID.eq(CERTIFICATE_RECORD.BUSINESS_ID))
                            .leftJoin(BUSINESS_CERTIFICATE)
                            .on(BUSINESS_CERTIFICATE.BUSINESS_ID.eq(COURSE_INFO.ID))
                            .where(condition)
                            .and(CERTIFICATE_RECORD.MEMBER_ID.eq(currentUserId));

            SelectConditionStep<Record> subjectStep = generalFunc.apply(otherCertificateRecordSql(ew0));

            SelectConditionStep<Record> chbnStep = recordChbnCommonDao.execute(dao -> dao.select(Fields.start()
                            .add(CERTIFICATE_RECORD_CHBN.ID.as("id"))
                            .add(CERTIFICATE_RECORD_CHBN.MEMBER_ID.as("memberId"))
                            .add(CERTIFICATE_RECORD_CHBN.CERTIFICATE_NUMBER.as("certificateNumber"))
                            .add(CERTIFICATE_RECORD_CHBN.BUSINESS_ID.as("businessId"))
                            .add(CERTIFICATE_RECORD_CHBN.ACTIVITY_TYPE.as("businessType"))
                            .add(CERTIFICATE_RECORD_CHBN.ACTIVITY_TYPE.as("type"))
                            .add(CERTIFICATE_RECORD_CHBN.FINISH_TIME.as("finishTime"))
                            .add(CERTIFICATE_RECORD_CHBN.ISSUE_TIME.as("issueTime"))
                            .add(CERTIFICATE_RECORD_CHBN.ISSUE_AGENCY.as("issueAgency"))
                            .add(CERTIFICATE_RECORD_CHBN.ISSUE_AGENCY.as("courseName"))
                            .add(CERTIFICATE_RECORD_CHBN.CERTIFICATE_ID.as("certificateId"))
                            .end())
                    .from(CERTIFICATE_RECORD_CHBN)
                    .where(CERTIFICATE_RECORD_CHBN.MEMBER_ID.eq(currentUserId))
                    .and(CERTIFICATE_RECORD_CHBN.CERTIFICATE_ID.in(CertificateRecordChbn.CERTIFICATE_ID_CHBN, CertificateRecordChbn.CERTIFICATE_ID_ZHZT))
                    .and(CERTIFICATE_RECORD_CHBN.STATE.eq(CertificateRecordChbn.CERTIFICATE_STATE_RECEIVERD))
                    .and(startTime.map(CERTIFICATE_RECORD_CHBN.FINISH_TIME::ge).orElse(DSL.trueCondition()))
                    .and(endTime.map(CERTIFICATE_RECORD_CHBN.FINISH_TIME::le).orElse(DSL.trueCondition()))
                    .and(CERTIFICATE_RECORD_CHBN.ACTIVITY_TYPE.in(1, 2)));

            SelectOrderByStep<Record> records = subjectStep.unionAll(chbnStep);
            Result<Record> recordResult = records
                    .orderBy(CERTIFICATE_RECORD_CHBN.FINISH_TIME.as("finishTime").desc())
                    .limit((page - 1) * pageSize, pageSize)
                    .fetch();

            Integer count = generalFunc.apply(ew0.select(Fields.start().add(CERTIFICATE_RECORD.ID.count()).end()))
                    .fetchOne().getValue(0,Integer.class);
            List<CertificateRecordChbn> certificateRecordChbns = chbnStep.fetchInto(CertificateRecordChbn.class);
            if (CollectionUtils.isNotEmpty(certificateRecordChbns)) {
                count = count + certificateRecordChbns.size();
            }
            return PagedResult.create(count, recordResult.parallelStream().map(ew1 -> {
                CertificateRecord certificateRecord = new CertificateRecord();
                certificateRecord.setId(ew1.getValue(CERTIFICATE_RECORD.ID.as("id")));
                certificateRecord.setMemberId(ew1.getValue(CERTIFICATE_RECORD.MEMBER_ID.as("memberId")));
                certificateRecord.setCertificateNumber(ew1.getValue(CERTIFICATE_RECORD.CERTIFICATE_NUMBER.as("certificateNumber")));
                certificateRecord.setBusinessId(ew1.getValue(CERTIFICATE_RECORD.BUSINESS_ID.as("businessId")));
                String courseName = ew1.getValue(COURSE_INFO.NAME.as("courseName"));
                boolean flag = false;
                if (certificateRecord.getBusinessId() != null &&
                        (certificateRecord.getBusinessId().equals(CertificateRecordChbn.BUSINESS_ID_CHBN) || certificateRecord.getBusinessId().equals(CertificateRecordChbn.BUSINESS_ID_ZHZT))) {
                    flag = true;
                    if (certificateRecord.getBusinessId().equals(CertificateRecordChbn.BUSINESS_ID_CHBN)) {
                        courseName = "“全员CHBN知识赋能行动”学习专区";
                    }
                    if (certificateRecord.getBusinessId().equals(CertificateRecordChbn.BUSINESS_ID_ZHZT)) {
                        courseName = "\"全员智慧中台知识赋能行动\"学习专区";
                    }
                }
                certificateRecord.setType(flag?1:ew1.getValue(CERTIFICATE_RECORD.TYPE.as("type")));
                certificateRecord.setBusinessType(flag?1:ew1.getValue(CERTIFICATE_RECORD.BUSINESS_TYPE.as("businessType")));
                certificateRecord.setFinishTime(ew1.getValue(CERTIFICATE_RECORD.FINISH_TIME.as("finishTime")));
                certificateRecord.setIssueTime(ew1.getValue(CERTIFICATE_RECORD.ISSUE_TIME.as("issueTime")));
                certificateRecord.setIssueAgency(ew1.getValue(CERTIFICATE_RECORD.ISSUE_AGENCY.as("issueAgency")));
                CourseInfo courseInfo = new CourseInfo();
                courseInfo.setName(courseName);
                certificateRecord.setCourseInfo(courseInfo);
                BusinessCertificate businessCertificate=new BusinessCertificate();
                businessCertificate.setCertificateId(ew1.getValue(BUSINESS_CERTIFICATE.CERTIFICATE_ID.as("certificateId")));
                certificateRecord.setBusinessCertificate(businessCertificate);
                return certificateRecord;
            }).collect(Collectors.toList()));
        });
    }

    /**
     * 查询证书详情
     *
     * @param id 证书id
     * @return 出参
     */
    @Override
    public CertificateRecord singleCertificate(String id) {
        CertificateRecord certificateRecord = certificateRecordCommonDao.get(id);
        String memberId = certificateRecord.getMemberId();
        Optional<Member> member = memberDao.getOptional(memberId);
        member.ifPresent(certificateRecord::setMember);
        return certificateRecord;
    }

    /**
     * 个人中心-我的证书（专题）SQL片段
     *
     * @return 查询的Sql片段
     */
    private SelectSelectStep<Record> otherCertificateRecordSql(DSLContext dslContext) {
        return dslContext.select(Fields.start().add(
                CERTIFICATE_RECORD.ID.as("id"),
                CERTIFICATE_RECORD.MEMBER_ID.as("memberId"),
                CERTIFICATE_RECORD.CERTIFICATE_NUMBER.as("certificateNumber"),
                CERTIFICATE_RECORD.BUSINESS_ID.as("businessId"),
                CERTIFICATE_RECORD.BUSINESS_TYPE.as("businessType"),
                CERTIFICATE_RECORD.TYPE.as("type"),
                CERTIFICATE_RECORD.FINISH_TIME.as("finishTime"),
                CERTIFICATE_RECORD.ISSUE_TIME.as("issueTime"),
                CERTIFICATE_RECORD.ISSUE_AGENCY.as("issueAgency"),
                COURSE_INFO.NAME.as("courseName"),
                BUSINESS_CERTIFICATE.CERTIFICATE_ID.as("certificateId")
        ).end());
    }
}
