package com.zxy.product.course.service.support.ai;

import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.course.ChatTimeService;
import com.zxy.product.course.entity.ChatTimeRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

import static com.zxy.product.course.jooq.Tables.CHAT_TIME_RECORD;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/29/17:07
 * @Description:
 */
@Service
public class ChatTimeServiceSupport implements ChatTimeService {

    private CommonDao<ChatTimeRecord> chatTimeRecordDao;

    @Autowired
    public void setChatTimeRecordDao(CommonDao<ChatTimeRecord> chatTimeRecordDao) {
        this.chatTimeRecordDao = chatTimeRecordDao;
    }

    @Override
    public void add(ChatTimeRecord record){
        chatTimeRecordDao.insert(record);
    }

    @Override
    public Map<String, Long> findMap(List<String> ids){
        return chatTimeRecordDao.execute(e->e.select(CHAT_TIME_RECORD.ANSWER_ID,CHAT_TIME_RECORD.TIME).from(CHAT_TIME_RECORD)
                .where(CHAT_TIME_RECORD.ANSWER_ID.in(ids)).fetchMap(CHAT_TIME_RECORD.ANSWER_ID, CHAT_TIME_RECORD.TIME));
    }

}
