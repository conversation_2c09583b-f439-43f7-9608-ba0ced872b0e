package com.zxy.product.course.service.support;

//import static com.zxy.product.course.jooq.Tables.GRANT_DETAIL;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.base.helper.Pair;
import com.zxy.common.cache.Cache;
import com.zxy.common.cache.CacheService;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.*;
import com.zxy.product.course.api.archived.CourseStudyProgressArchivedService;
import com.zxy.product.course.api.certificate.CertificateRecordService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.course.CourseProcessService;
import com.zxy.product.course.api.sharding.ShardingConfigService;
import com.zxy.product.course.api.subject.SubjectService;
import com.zxy.product.course.content.*;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.jooq.tables.pojos.*;
import com.zxy.product.course.service.util.DateUtil;
import com.zxy.product.course.util.CourseSectionStudyProgressUtil;
import com.zxy.product.course.util.CourseStudyProgressArchivedUtil;
import com.zxy.product.course.util.SplitTableName;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Comparator;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.*;
import static com.zxy.product.course.service.util.SecurePathCdnUtils.generateSecurePathCdn;
import static java.util.stream.Collectors.*;

////热门内容更新 -- LJY 2022/05/17
//import com.zxy.product.log.entity.ResourceVisit;
//import com.zxy.product.log.api.DisplayService;

/**
 * Created by keeley on 16/10/12.
 */
@Service
public class CourseInfoServiceSupport implements CourseInfoService {
    private static Logger logger = LoggerFactory.getLogger(CourseInfoServiceSupport.class);
    private CommonDao<CourseInfo> courseInfoCommonDao;
    private CommonDao<CourseAbility> courseAbilityDao;
    private CommonDao<CourseChapter> courseChapterCommonDao;
    private CommonDao<CourseChapterSection> courseChapterSectionCommonDao;
    private CommonDao<CourseAttachment> courseAttachmentCommonDao;
    private CommonDao<AudienceObject> audienceObjectCommonDao;
    private CommonDao<CourseShelves> shelvesDao;
    private CommonDao<CoursePhoto> photoDao;
    private CommonDao<CourseScore> courseScoreDao;
    private CommonDao<CourseNote> courseNoteDao;
    private CommonDao<Member> memberDao;
    private CommonDao<CourseSectionProgressAttachment> sectionProgressAttachmentDao;
    private CommonDao<Organization> orgDao;
    private CommonDao<CourseSequence> seqDao;
    private CommonDao<SubjectTextArea> textAreaDao;
    private CommonDao<GenseeWebCast> genseeWebCastDao;
    private CommonDao<StudyPushObject> pushObjectDao;
    private CommonDao<CourseStudyProgress> progressDao;
    private CommonDao<CourseChapterQuestionnaire> courseChapterQuestionnaireCommonDao;
    private CommonDao<RemodelingExternalPassbackBusiness> remodelingExternalPassbackBusinessCommonDao;
    private CommonDao<CourseSectionStudyLogAhDay> courseSectionStudyLogDayCommonDao;
    private CommonDao<BusinessTopic> businessTopicCommonDao;
    private CommonDao<IntelligentBroadcast> intelligentBroadcastCommonDao;
    private CommonDao<SplitTableConfig> splitTableConfigDao;
    private CommonDao<SubjectTopicManager> subjectTopicManagerCommonDao;
    private CommonDao<IntelligentNote> intelligentNoteCommonDao;
    private CommonDao<DeleteDataCourse> dataCourseCommonDao;
    private CommonDao<AudienceMember> audienceMemberCommonDao;
    private CommonDao<KnowledgeInfo> knowledgeInfoCommonDao;
    private CommonDao<GenseeUserAccess> userAccessCommonDao;
    private CommonDao<GenseeLecturer> genseeLecturerDao;


    private StudyTaskService studyTaskService;
    private AudienceItemService audienceItemService;
    private CourseCategoryService courseCategoryService;
    private CourseStudyProgressService courseStudyProgressService;

    private OrganizationService organizationService;
    private SubjectAdvertisingService subjectAdvertisingService;
    private BusinessTopicService businessTopicService;
    private CourseCacheService courseCacheService;
    private CourseProcessService courseProcessService;
    private MessageSender messageSender;
    private SubjectService subjectService;
    private ShardingConfigService shardingConfigService;
    private Cache cache;
    private Cache sectionNoteCache;
    private Cache courseIdsCache;
    private Cache checkArchivedCache;
    private CertificateRecordService certificateRecordService;
    private CourseInfoCategoryService courseInfoCategoryService;
    private MultidimensionalScoringService multidimensionalScoringService;
    private CommonDao<Caption> captionCommonDao;
    private IntelligentNoteService intelligentNoteService;
    private CourseStudyProgressArchivedService courseStudyProgressArchivedService;
//    //热门内容更新 -- LJY 2022/05/17
//    private DisplayService displayService;
//    private static final String HOT_VISITS_KEY = "hot-visits-key-";
//    private static final String courseWebKey = HOT_VISITS_KEY + "course" + "-" + 1;
//    private static final String courseAPPKey = HOT_VISITS_KEY + "course" + "-" + 2;
//    private static final String subjectWebKey = HOT_VISITS_KEY + "subject" + "-" + 1;
//    private static final String subjectAPPKey = HOT_VISITS_KEY + "subject" + "-" + 2;
//    private static final String HOT_VISITS_SERVICE_SWITCH = "hot-visits-service-switch";
//    private static final String HOT_VISITS_KEY_NEW = "hot-visits-key-1-";

    // 默认取2020-01-01 00:00:00之后发布的数据
    @Value("${hot.visit.rank.start.timestamp:1577808000000}")
    private Long hotVistRankStartTime;

    @Autowired
    public void setCourseAbilityDao(CommonDao<CourseAbility> courseAbilityDao) {
        this.courseAbilityDao = courseAbilityDao;
    }

    @Autowired
    public void setAudienceMemberCommonDao(CommonDao<AudienceMember> audienceMemberCommonDao){
        this.audienceMemberCommonDao=audienceMemberCommonDao;
    }

    @Autowired
    public void setRemodelingExternalPassbackBusinessCommonDao(CommonDao<RemodelingExternalPassbackBusiness> remodelingExternalPassbackBusinessCommonDao) {
        this.remodelingExternalPassbackBusinessCommonDao = remodelingExternalPassbackBusinessCommonDao;
    }

    @Autowired
    public void setIntelligentBroadcastCommonDao(CommonDao<IntelligentBroadcast> intelligentBroadcastCommonDao) {
        this.intelligentBroadcastCommonDao = intelligentBroadcastCommonDao;
    }

    @Autowired
    public void setSplitTableConfigDao(CommonDao<SplitTableConfig> splitTableConfigDao) {
        this.splitTableConfigDao = splitTableConfigDao;
    }

    @Autowired
    public void setCourseChapterQuestionnaireCommonDao(CommonDao<CourseChapterQuestionnaire> courseChapterQuestionnaireCommonDao) {
        this.courseChapterQuestionnaireCommonDao = courseChapterQuestionnaireCommonDao;
    }

    @Autowired
    public void setDataCourseCommonDao(CommonDao<DeleteDataCourse> dataCourseCommonDao){
        this.dataCourseCommonDao = dataCourseCommonDao;
    }

    @Autowired
    public void setCache(Cache cache) {
        this.cache = cache;
    }

    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }


    @Autowired
    public void setCacheService(CacheService cacheService) {
        this.sectionNoteCache = cacheService.create(CacheKeyConstant.COURSE_STUDY,CacheKeyConstant.SECTION_NOTE_COUNT);
        this.courseIdsCache = cacheService.create("course-page-list");
        this.checkArchivedCache = cacheService.create("check-archived-member-course");
    }

    @Autowired
    public void setCourseInfoCommonDao(CommonDao<CourseInfo> courseInfoCommonDao) {
        this.courseInfoCommonDao = courseInfoCommonDao;
    }

    @Autowired
    public void setCourseChapterCommonDao(CommonDao<CourseChapter> courseChapterCommonDao) {
        this.courseChapterCommonDao = courseChapterCommonDao;
    }

    @Autowired
    public void setCourseChapterSectionCommonDao(CommonDao<CourseChapterSection> courseChapterSectionCommonDao) {
        this.courseChapterSectionCommonDao = courseChapterSectionCommonDao;
    }

    @Autowired
    public void setCourseAttachmentCommonDao(CommonDao<CourseAttachment> courseAttachmentCommonDao) {
        this.courseAttachmentCommonDao = courseAttachmentCommonDao;
    }

    @Autowired
    public void setPushObjectDao(CommonDao<StudyPushObject> pushObjectDao) {
        this.pushObjectDao = pushObjectDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setStudyTaskService(StudyTaskService studyTaskService) {
        this.studyTaskService = studyTaskService;
    }

    @Autowired
    public void setAudienceObjectCommonDao(CommonDao<AudienceObject> audienceObjectCommonDao) {
        this.audienceObjectCommonDao = audienceObjectCommonDao;
    }

    @Autowired
    public void setGenseeWebCastDao(CommonDao<GenseeWebCast> genseeWebCastDao) {
        this.genseeWebCastDao = genseeWebCastDao;
    }

    @Autowired
    public void setBusinessTopicCommonDao(CommonDao<BusinessTopic> businessTopicCommonDao) {
        this.businessTopicCommonDao = businessTopicCommonDao;
    }
    @Autowired
    public void setAudienceItemService(AudienceItemService audienceItemService) {
        this.audienceItemService = audienceItemService;
    }

    @Autowired
    public void setShelvesDao(CommonDao<CourseShelves> shelvesDao) {
        this.shelvesDao = shelvesDao;
    }

    @Autowired
    public void setPhotoDao(CommonDao<CoursePhoto> photoDao) {
        this.photoDao = photoDao;
    }

    @Autowired
    public void setCourseScoreDao(CommonDao<CourseScore> courseScoreDao) {
        this.courseScoreDao = courseScoreDao;
    }

    @Autowired
    public void setCourseNoteDao(CommonDao<CourseNote> courseNoteDao) {
        this.courseNoteDao = courseNoteDao;
    }

    @Autowired
    public void setProgressDao(CommonDao<CourseStudyProgress> progressDao) {
        this.progressDao = progressDao;
    }

    @Autowired
    public void setCourseCategoryService(CourseCategoryService courseCategoryService) {
        this.courseCategoryService = courseCategoryService;
    }


    @Autowired
    public void setCourseStudyProgressService(CourseStudyProgressService courseStudyProgressService) {
        this.courseStudyProgressService = courseStudyProgressService;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Autowired
    public void setSectionProgressAttachmentDao(
            CommonDao<CourseSectionProgressAttachment> sectionProgressAttachmentDao) {
        this.sectionProgressAttachmentDao = sectionProgressAttachmentDao;
    }

    @Autowired
    public void setSeqDao(CommonDao<CourseSequence> seqDao) {
        this.seqDao = seqDao;
    }

    @Autowired
    public void setOrgDao(CommonDao<Organization> orgDao) {
        this.orgDao = orgDao;
    }

    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Autowired
    public void setSubjectAdvertisingService(SubjectAdvertisingService subjectAdvertisingService) {
        this.subjectAdvertisingService = subjectAdvertisingService;
    }

    @Autowired
    public void setTextAreaDao(CommonDao<SubjectTextArea> textAreaDao) {
        this.textAreaDao = textAreaDao;
    }

    @Autowired
    public void setBusinessTopicService(BusinessTopicService businessTopicService) {
        this.businessTopicService = businessTopicService;
    }

    @Autowired
    public void setCourseProcessService(CourseProcessService courseProcessService) {
        this.courseProcessService = courseProcessService;
    }

    @Autowired
    public void setSubjectService(SubjectService subjectService) {
        this.subjectService = subjectService;
    }

    @Autowired
    public void setShardingConfigService(ShardingConfigService shardingConfigService) {
        this.shardingConfigService = shardingConfigService;
    }

    @Autowired
    public void setCertificateRecordService(CertificateRecordService certificateRecordService) {
        this.certificateRecordService = certificateRecordService;
    }

    @Autowired
    public void setCourseSectionStudyLogDayCommonDao(CommonDao<CourseSectionStudyLogAhDay> courseSectionStudyLogDayCommonDao) {
        this.courseSectionStudyLogDayCommonDao = courseSectionStudyLogDayCommonDao;
    }
    @Autowired
    public void setCourseInfoCategoryService(CourseInfoCategoryService courseInfoCategoryService) {
        this.courseInfoCategoryService = courseInfoCategoryService;
    }
    @Autowired
    public void setMultidimensionalScoringService(MultidimensionalScoringService multidimensionalScoringService) {
        this.multidimensionalScoringService = multidimensionalScoringService;
    }

    @Autowired
    public void setIntelligentNoteCommonDao(CommonDao<IntelligentNote> intelligentNoteCommonDao) {
        this.intelligentNoteCommonDao = intelligentNoteCommonDao;
    }

    @Autowired
    public void setSubjectTopicManagerCommonDao(CommonDao<SubjectTopicManager> subjectTopicManagerCommonDao) {
        this.subjectTopicManagerCommonDao = subjectTopicManagerCommonDao;
    }


    @Autowired
    public void setCaptionCommonDao(CommonDao<Caption> captionCommonDao) {
        this.captionCommonDao = captionCommonDao;
    }

    @Autowired
    public void setIntelligentNoteService(IntelligentNoteService intelligentNoteService) {
        this.intelligentNoteService = intelligentNoteService;
    }

    @Autowired
    public void setKnowledgeInfoCommonDao(CommonDao<KnowledgeInfo> knowledgeInfoCommonDao) {
        this.knowledgeInfoCommonDao = knowledgeInfoCommonDao;
    }

    @Autowired
    public void setUserAccessCommonDao(CommonDao<GenseeUserAccess> userAccessCommonDao) {
        this.userAccessCommonDao = userAccessCommonDao;
    }

    @Autowired
    public void setGenseeLecturerDao(CommonDao<GenseeLecturer> genseeLecturerDao) {
        this.genseeLecturerDao = genseeLecturerDao;
    }
    @Autowired
    public void setCourseStudyProgressArchivedService(CourseStudyProgressArchivedService courseStudyProgressArchivedService) {
        this.courseStudyProgressArchivedService = courseStudyProgressArchivedService;
    }

    //    //热门内容更新 -- LJY 2022/05/17
//    @Autowired
//    public void setDisplayService(DisplayService displayService) {
//        this.displayService = displayService;
//    }
    // 分页查询
    @Override
    public PagedResult<CourseInfo> find(int pageNum, int pageSize, String memberId, int businessType,
                                        Optional<String> name,
                                        Optional<String> organizationId, Optional<String> categoryId,
                                        Optional<String> code, Optional<Integer> status,
                                        Optional<Integer> source, Optional<String> releaseUserId,
                                        Optional<Integer> publishClient, Optional<Long> shelveBeginDate,
                                        Optional<Long> shelveEndDate,
                                        Optional<Integer> subjectType, Optional<Long> beginBeginDate,
                                        Optional<Long> beginEndDate, Optional<Long> endBeginDate,
                                        Optional<Long> endEndDate,
                                        Optional<Long> shelveBeginTime, Optional<Long> shelveEndTime,
                                        Optional<String[]> selectIds, List<String> GrantedOrganizationIds) {

        return courseInfoCommonDao.execute(x -> {

            com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
            com.zxy.product.course.jooq.tables.Organization releaseOrganizationTable = ORGANIZATION.as(
                    "releaseOrganization"); // 发布部门
            com.zxy.product.course.jooq.tables.Member createUserTable = MEMBER.as("createUser"); // 创建人
            com.zxy.product.course.jooq.tables.Member releaseUserTable = MEMBER.as("releaseUser"); // 发布人


            SelectSelectStep<Record> selectListField = x
                    .selectDistinct(Fields.start().add(COURSE_INFO)
                            .add(organizationTable.ID, organizationTable.NAME)
                            .add(releaseOrganizationTable.NAME, releaseOrganizationTable.ID)
                            .add(createUserTable.ID, createUserTable.NAME, createUserTable.FULL_NAME)
                            .add(releaseUserTable.ID, releaseUserTable.NAME, releaseUserTable.FULL_NAME)
                            .add(COURSE_CATEGORY.ID, COURSE_CATEGORY.NAME).end()); // 查询list
            SelectSelectStep<Record> selectCountField = x.select(Fields.start()
                    .add(COURSE_INFO.ID.countDistinct()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                    name.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    code.map(COURSE_INFO.CODE::contains),
                    categoryId.map(COURSE_CATEGORY.PATH::contains),
                    status.map(COURSE_INFO.STATUS::eq),
                    source.map(COURSE_INFO.SOURCE::eq),
                    releaseUserId.map(COURSE_INFO.RELEASE_MEMBER_ID::eq),
                    publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq),
                    shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                    shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le),
                    beginBeginDate.map(COURSE_INFO.BEGIN_DATE::ge),
                    beginEndDate.map(COURSE_INFO.BEGIN_DATE::le),
                    endBeginDate.map(COURSE_INFO.END_DATE::ge),
                    endEndDate.map(COURSE_INFO.END_DATE::le),
                    shelveBeginTime.map(COURSE_INFO.SHELVE_TIME::ge),
                    shelveEndTime.map(COURSE_INFO.SHELVE_TIME::le),
//                    organizationId.map(ORGANIZATION_DETAIL.ROOT::eq),
                    subjectType.map(t -> t == 1 ? COURSE_INFO.URL.isNull() : COURSE_INFO.URL.isNotNull()))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(businessType));
            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
//            param.add(GRANT_DETAIL.MEMBER_ID.eq(memberId).and(GRANT_DETAIL.URI.eq(CourseInfoService.URI)));
            param.add(selectIds.map(COURSE_INFO.ID::notIn).orElse(DSL.trueCondition()));

            // 替换权限 调用系统权限
            param.add(COURSE_INFO.ORGANIZATION_ID.in(GrantedOrganizationIds));

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .leftJoin(createUserTable).on(createUserTable.ID.eq(COURSE_INFO.CREATE_MEMBER_ID))
                        .leftJoin(releaseUserTable).on(releaseUserTable.ID.eq(COURSE_INFO.RELEASE_MEMBER_ID))
                        .leftJoin(releaseOrganizationTable).on(releaseOrganizationTable.ID.eq(COURSE_INFO.RELEASE_ORG_ID))
                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
//                        .leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.ORGANIZATION_ID.eq(COURSE_INFO.ORGANIZATION_ID))
//                        .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(GRANT_DETAIL.ORGANIZATION_ID))
                        .where(param);
                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((pageNum - 1) * pageSize, pageSize).fetch();

            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);
            List<Organization> releaseOrganizationList = record.into(releaseOrganizationTable).into(Organization.class);
            List<Member> createUserList = record.into(createUserTable).into(Member.class);
            List<Member> releaseUserList = record.into(releaseUserTable).into(Member.class);
            List<CourseCategory> courseCategoryList = record.into(COURSE_CATEGORY).into(CourseCategory.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setReleaseOrg(releaseOrganizationList.get(i));
                courseInfoList.get(i).setCreateUser(createUserList.get(i));
                courseInfoList.get(i).setReleaseUser(releaseUserList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    /**
     * @param pageNum
     * @param pageSize
     * @param memberId 人员
     * @param category 目录
     * @param client   适用终端
     * @return
     */
    @Override
    public PagedResult<CourseInfo> findSelect(Integer pageNum, Integer pageSize, String memberId,
                                              Optional<Integer> client,
                                              Optional<String> name, Optional<String> organizationId,
                                              Optional<String> category, Optional<String> uri,
                                              Optional<String[]> selectIds, List<String> GrantedOrganizationIds) {
        List<Condition> where = new ArrayList<>();
        List<Condition> where1 = new ArrayList<>();
        List<Condition> where2 = new ArrayList<>();


        where.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
        where.add(COURSE_INFO.BUSINESS_TYPE.eq(0));
        where.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));//选择器只查询已发布的课程
        where.add(selectIds.map(COURSE_INFO.ID::notIn).orElse(DSL.trueCondition()));
        category.map(x -> where.add(COURSE_CATEGORY.PATH.contains(x)));
        name.map(x -> where.add(COURSE_INFO.NAME.contains(x)));
        client.map(x -> where.add(COURSE_INFO.PUBLISH_CLIENT.eq(x)));

//        where1.add(GRANT_DETAIL.MEMBER_ID.eq(memberId));
//        where1.add(GRANT_DETAIL.URI.eq(uri.orElse(CourseInfoService.URI)));
        where1.addAll(where);
//        organizationId.map(x-> where1.add(ORGANIZATION_DETAIL.ROOT.eq(x)));
        where2.addAll(where);
        where2.add(COURSE_INFO.SHARE_SUB.eq(1));


        return courseInfoCommonDao.execute(x -> {
            SelectOrderByStep<Record> select = x
                    .selectDistinct(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
                            .add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
                    .from(COURSE_INFO).leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
//                    .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(COURSE_INFO.ORGANIZATION_ID))
//                    .leftJoin(GRANT_DETAIL).on(GRANT_DETAIL.ORGANIZATION_ID.eq(COURSE_INFO.ORGANIZATION_ID))
                    .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                    .where(where1);
            if (!organizationId.isPresent()) {
                select = select.union(
                        x.select(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
                                .add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
                                .from(COURSE_INFO).leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                                .where(where2).and(COURSE_INFO.ORGANIZATION_ID.in(GrantedOrganizationIds)
// 替换权限 调用系统权限
//                                .where(where2).and(COURSE_INFO.ORGANIZATION_ID.in(
//                                x.select(ORGANIZATION_DETAIL.ROOT).from(GRANT_DETAIL)
//                                        .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.SUB.eq(GRANT_DETAIL.ORGANIZATION_ID))
//                                        .where(GRANT_DETAIL.MEMBER_ID.eq(memberId))
//                                        .and(GRANT_DETAIL.URI.eq(uri.orElse(CourseInfoService.URI)))
//                                        .groupBy(ORGANIZATION_DETAIL.ROOT)
//                                  )
                        ));
            }

            int count = x.fetchCount(select);
            Result<Record> record = select.orderBy(COURSE_INFO.CREATE_TIME.desc()).limit((pageNum - 1) * pageSize,
                    pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);

            List<Organization> organizationList = record.map(r -> {
                Organization org = new Organization();
                org.setName(r.getValue("oname", String.class));
                return org;
            });
            List<CourseCategory> courseCategoryList = record.map(r -> {
                CourseCategory cc = new CourseCategory();
                cc.setId(r.getValue("cid", String.class));
                cc.setName(r.getValue("cname", String.class));
                return cc;
            });

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
            });

            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public void save(List<CourseInfo> courseInfos) {
        courseInfoCommonDao.insert(courseInfos);
    }

    @Override
    public CourseInfo getCourseInfo(String id) {
        return courseInfoCommonDao.get(id);
    }

    @Override
    public CourseInfo get(String id) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        com.zxy.product.course.jooq.tables.Organization releaseOrgTable = ORGANIZATION.as("releaseOrg"); // 发布部门
        com.zxy.product.course.jooq.tables.Member releaseUserTable = MEMBER.as("releaseUser"); // 发布人
        com.zxy.product.course.jooq.tables.Member developUserTable = MEMBER.as("developUser"); // 开发人
        com.zxy.product.course.jooq.tables.Member createUserTable = MEMBER.as("createUser"); // 创建人

        Record result = courseInfoCommonDao.execute(x -> x
                .select(Fields.start()
                        .add(COURSE_INFO)
                        .add(organizationTable.ID, organizationTable.NAME)
                        .add(releaseOrgTable.ID, releaseOrgTable.NAME)
                        .add(releaseUserTable.ID, releaseUserTable.NAME, releaseUserTable.FULL_NAME)
                        .add(developUserTable.ID, developUserTable.NAME, developUserTable.FULL_NAME)
                        .add(createUserTable.ID, createUserTable.NAME, createUserTable.FULL_NAME)
                        .add(COURSE_CATEGORY.ID, COURSE_CATEGORY.NAME)
                        .end())
                .from(COURSE_INFO).leftJoin(organizationTable).on(COURSE_INFO.ORGANIZATION_ID.eq(organizationTable.ID))
                .leftJoin(releaseOrgTable).on(COURSE_INFO.RELEASE_ORG_ID.eq(releaseOrgTable.ID))
                .leftJoin(releaseUserTable).on(COURSE_INFO.RELEASE_MEMBER_ID.eq(releaseUserTable.ID))
                .leftJoin(developUserTable).on(COURSE_INFO.DEVELOP_MEMBER_ID.eq(developUserTable.ID))
                .leftJoin(createUserTable).on(COURSE_INFO.CREATE_MEMBER_ID.eq(createUserTable.ID))
                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                .where(COURSE_INFO.ID.eq(id)).fetchOne());
        if (result == null) return null;
        CourseInfo course = result.into(COURSE_INFO).into(CourseInfo.class);
        Organization organization = result.into(organizationTable).into(Organization.class);
        Organization releaseOrg = result.into(releaseOrgTable).into(Organization.class);
        Member releaseUser = result.into(releaseUserTable).into(Member.class);
        Member developUser = result.into(developUserTable).into(Member.class);
        Member createUser = result.into(createUserTable).into(Member.class);
        CourseCategory courseCategory = result.into(COURSE_CATEGORY).into(CourseCategory.class);

        int businessType = AudienceObject.BUSINESS_TYPE_COURSE;
        if (course.getBusinessType() != null && (course.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_SUBJECT) || course.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_STUDY_MAP))) {
            businessType = AudienceObject.BUSINESS_TYPE_SUBJECT;
        }

        course.setOrganization(organization);
        course.setReleaseOrg(releaseOrg);
        course.setReleaseUser(releaseUser);
        course.setDevelopUser(developUser);
        course.setCreateUser(createUser);
        course.setCategory(courseCategory);

        // 查询章节和章节详情
        course.setCourseChapters(this.findCourseChapterByCourseId(id, Optional.ofNullable(course.getVersionId())));
        // 查询附件
        course.setCourseAttachments(this.findCourseAttachmentByCourseId(id));
        // 设置受众项
        course.setAudienceItems(audienceItemService.getAudienceItem(id, businessType));
        // 查询话题
        course.setBusinessTopics(businessTopicService.findTopicByBusinessId(id));
        // 查询专题相册、广告、文字区域
        if (course.getBusinessType() != null && course.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
            course.setPhotos(findPhotosBySubjectId(id));
            course.setAdvertisings(subjectAdvertisingService.findAdvertisingBySubjectId(id));
            course.setTextAreas(textAreaDao.fetch(SUBJECT_TEXT_AREA.SUBJECT_ID.eq(id)));
        }

        // 查询专题是否关联证书
        BusinessCertificate businessCertificate = certificateRecordService.getByBusinessId(course.getId());
        if (businessCertificate != null) {
            course.setCertificateId(businessCertificate.getCertificateId());
        }
        return course;
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public String getName(String id) {
        List<String> list = courseInfoCommonDao.execute(x -> x.select(COURSE_INFO.NAME)
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.eq(id))
                .fetch(COURSE_INFO.NAME));
        return com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(list) ? list.get(0) : "";
    }

    private String calCourseTime(Integer courseTime) {
        BigDecimal clock = new BigDecimal(60);
        if (null != courseTime && courseTime > 0) {
            String courseTimeString;
            BigDecimal minute = new BigDecimal(courseTime).divide(clock, 0,
                    BigDecimal.ROUND_HALF_UP);
            if (minute.compareTo(clock) >= 0) {
                BigDecimal[] hourAndMinute = minute.divideAndRemainder(clock);
                courseTimeString =
                        String.valueOf(hourAndMinute[0]).concat("小时").concat(String.valueOf(hourAndMinute[1]))
                                .concat("分钟");
            } else {
                courseTimeString = String.valueOf(minute).concat("分钟");
            }
            return courseTimeString;
        }
        return null;
    }

    @Override
    public CourseInfo getCourse(String id) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        com.zxy.product.course.jooq.tables.Organization releaseOrgTable = ORGANIZATION.as("releaseOrg"); // 发布部门
        com.zxy.product.course.jooq.tables.Member releaseUserTable = MEMBER.as("releaseUser"); // 发布人
        com.zxy.product.course.jooq.tables.Member developUserTable = MEMBER.as("developUser"); // 开发人
        com.zxy.product.course.jooq.tables.Member createUserTable = MEMBER.as("createUser"); // 创建人

        Record result = courseInfoCommonDao.execute(x -> x
                .select(Fields.start()
                        .add(COURSE_INFO)
                        .add(organizationTable.ID, organizationTable.NAME)
                        .add(releaseOrgTable.ID, releaseOrgTable.NAME)
                        .add(releaseUserTable.ID, releaseUserTable.NAME, releaseUserTable.FULL_NAME)
                        .add(developUserTable.ID, developUserTable.NAME, developUserTable.FULL_NAME)
                        .add(createUserTable.ID, createUserTable.NAME, createUserTable.FULL_NAME)
                        .add(COURSE_CATEGORY.ID, COURSE_CATEGORY.NAME)
                        .add(GENSEE_WEB_CAST.ID, GENSEE_WEB_CAST.SUBJECT)
                        .end())
                .from(COURSE_INFO)
                .leftJoin(organizationTable).on(COURSE_INFO.ORGANIZATION_ID.eq(organizationTable.ID))
                .leftJoin(releaseOrgTable).on(COURSE_INFO.RELEASE_ORG_ID.eq(releaseOrgTable.ID))
                .leftJoin(releaseUserTable).on(COURSE_INFO.RELEASE_MEMBER_ID.eq(releaseUserTable.ID))
                .leftJoin(developUserTable).on(COURSE_INFO.DEVELOP_MEMBER_ID.eq(developUserTable.ID))
                .leftJoin(createUserTable).on(COURSE_INFO.CREATE_MEMBER_ID.eq(createUserTable.ID))
                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                .leftJoin(GENSEE_WEB_CAST).on(GENSEE_WEB_CAST.ID.eq(COURSE_INFO.RELATIVE_GENSEE_ID))
                .where(COURSE_INFO.ID.eq(id)).fetchOne());
        if (result == null) return null;
        CourseInfo course = result.into(COURSE_INFO).into(CourseInfo.class);
        Organization organization = result.into(organizationTable).into(Organization.class);
        Organization releaseOrg = result.into(releaseOrgTable).into(Organization.class);
        Member releaseUser = result.into(releaseUserTable).into(Member.class);
        Member developUser = result.into(developUserTable).into(Member.class);
        Member createUser = result.into(createUserTable).into(Member.class);
        CourseCategory courseCategory = result.into(COURSE_CATEGORY).into(CourseCategory.class);
        GenseeWebCast relativeGensee = result.into(GENSEE_WEB_CAST).into(GenseeWebCast.class);

        List<CourseCategory> deputyCategories = courseInfoCategoryService.findDeputyCourseCategories(id);

        course.setDeputyCategories(deputyCategories);
        course.setOrganization(organization);
        course.setReleaseOrg(releaseOrg);
        course.setReleaseUser(releaseUser);
        course.setDevelopUser(developUser);
        course.setCreateUser(createUser);
        course.setCategory(courseCategory);
        course.setRelativeGensee(relativeGensee);

        // 查询附件
        course.setCourseAttachments(this.findCourseAttachmentByCourseId(id));
        return course;
    }


    @Override
    public CourseInfo save(String name, String createUserId, int businessType, int publishClient, String organizationId,
                           Integer source, Optional<Integer> publishType, Optional<String> certificateId,
                           Optional<Long> beginDate,
                           Optional<Long> endDate, Optional<Integer> courseHour, Optional<Integer> credit,
                           Optional<Integer> courseTime, Optional<String> desc, Optional<String> descText,
                           Optional<String> releaseUser, Optional<String> releaseOrg, Optional<String> devUser,
                           Optional<Long> devTime,
                           Optional<String> code, Optional<Integer> price, Optional<Integer> courseSecond,
                           Optional<String> categoryId,
                           Optional<Integer> addType, Optional<Integer> learnSequence, Optional<Integer> studyDays,
                           Optional<String> cover, Optional<String> coverPath, Optional<String> sourceDetail,
                           Optional<String> lecturer,
                           Optional<Integer> shareSub, List<CourseChapter> courseChapters,
                           List<CourseAttachment> courseAttachments,
                           List<AudienceItem> audienceItems, Optional<CourseShelves> courseShelves,
                           Optional<String> styles, Optional<String> topicIds, Optional<Integer> isPublic,
                           List<SubjectAdvertising> advertisingList,
                           List<CoursePhoto> photos, List<SubjectTextArea> textAreas, Optional<String> url) {

        CourseInfo c = new CourseInfo();
        c.forInsert();
        c.setName(name);
        c.setCreateMemberId(createUserId);
        c.setStatus(CourseInfo.STATUS_UNPUBLISHED);
        c.setBusinessType(businessType);
        c.setPublishClient(publishClient);
        c.setDeleteFlag(CourseInfo.DELETE_FLAG_NO);
        c.setOrganizationId(organizationId);
        c.setSource(source);

        publishType.ifPresent(c::setPublishType);
        beginDate.ifPresent(c::setBeginDate);
        endDate.ifPresent(c::setEndDate);
        courseHour.ifPresent(c::setCourseHour);
        credit.ifPresent(c::setCredit);
        courseTime.ifPresent(c::setCourseTime);
        desc.ifPresent(c::setDescription);
        descText.ifPresent(c::setDescriptionText);
        releaseUser.ifPresent(c::setReleaseMemberId);
        releaseOrg.ifPresent(c::setReleaseOrgId);
        devTime.ifPresent(c::setDevelopTime);
        devUser.ifPresent(c::setDevelopMemberId);
        price.ifPresent(c::setPrice);
        certificateId.ifPresent(c::setCertificateId);
        courseSecond.ifPresent(c::setCourseSecond);
        categoryId.ifPresent(c::setCategoryId);
        addType.ifPresent(c::setAddType);
        learnSequence.ifPresent(c::setLearnSequence);
        studyDays.ifPresent(c::setStudyDays);
        cover.ifPresent(c::setCover);
        coverPath.ifPresent(c::setCoverPath);
        sourceDetail.ifPresent(c::setSourceDetail);
        lecturer.ifPresent(c::setLecturer);
        shareSub.ifPresent(c::setShareSub);
        url.ifPresent(c::setUrl);
        isPublic.ifPresent(c::setIsPublic);
        styles.ifPresent(s -> {
            c.setStyles(s);
            subjectAdvertisingService.save(c.getId(), createUserId, advertisingList);
            this.savePhotos(c.getId(), photos);
            this.saveTextArea(c.getId(), textAreas);
        });
        this.saveCourseChapters(c.getId(), courseChapters);
        this.saveCourseAttachs(c.getId(), courseAttachments);
        int audienceBusinessType = businessType == CourseInfo.BUSINESS_TYPE_SUBJECT
                ? AudienceObject.BUSINESS_TYPE_SUBJECT : AudienceObject.BUSINESS_TYPE_COURSE;
        this.saveCourseAudienceObject(c.getId(), audienceItems, audienceBusinessType);
        int topicBusinessType = businessType == CourseInfo.BUSINESS_TYPE_SUBJECT ?
                BusinessTopic.BUSINESS_TYPE_SUBJECT : BusinessTopic.BUSINESS_TYPE_COURSE;
        topicIds.ifPresent(ids -> businessTopicService.saveBusinessTopic(c.getId(), topicBusinessType, ids.split(",")));

        c.setType(this.getTypeByCourseId(c.getId()));

        // 上架(发布)
        courseShelves.ifPresent(s -> {
            // shelves(c.getId(), s);

            c.setReleaseTime(System.currentTimeMillis());
            publishType.ifPresent(type -> {
                // c.setStatus(CourseInfo.STATUS_THE_TEST);
                //xwolf 修改为发布中
                c.setStatus(CourseInfo.STATUS_IN_SHELVES);
                if (type == CourseInfo.PUBLISH_TYPE_FORMAL) {
                    c.setShelveTime(System.currentTimeMillis());
                    //c.setStatus(CourseInfo.STATUS_SHELVES);
                }
            });
            //xwolf 上架保存
            shelves(c.getId(), s, CourseInfo.STATUS_NO, c.getPublishType());
            this.syncKnowledge(courseAttachments, c);
            this.addReferenceId(courseChapters);
        });

        courseInfoCommonDao.insert(c);
        updateCourseCode(c);
        messageSender.send(MessageTypeContent.COURSE_INFO_INSERT, MessageHeaderContent.ID, c.getId());
        return c;
    }

    @Override
    public int saveCourseChapters(String courseId, List<CourseChapter> courseChapters) {
        courseChapters.forEach(courseChapter -> {
            courseChapter.forInsert();
            courseChapter.setCourseId(courseId);
            courseChapter.setVersionId(null);
            courseChapter.setModifyDate(null);
            courseChapterCommonDao.insert(courseChapter);
            extracted(courseId, courseChapter);
        });
        return courseChapters.size();
    }
    public int saveCourseChapters(String courseId, List<CourseChapter> courseChapters,String memberId) {

        courseChapters.forEach(courseChapter -> {
            String oldId = courseChapter.getId();
            courseChapter.forInsert();
            courseChapter.setCourseId(courseId);
            courseChapter.setVersionId(null);
            courseChapter.setModifyDate(null);
            subjectService.notificationScoreSheet(courseId,courseChapter.getScoringId(),memberId,Optional.ofNullable(oldId),Optional.ofNullable(courseChapter.getId()));
            courseChapterCommonDao.insert(courseChapter);
            extracted(courseId, courseChapter);
        });
        return courseChapters.size();
    }

    private void extracted(String courseId, CourseChapter courseChapter) {
        List<CourseChapterSection> courseChapterSections = courseChapter.getCourseChapterSections();
        if (courseChapterSections != null) {
            courseChapterSectionCommonDao.insert(courseChapterSections.stream().map(x -> {
                x.forInsert();
                x.setChapterId(courseChapter.getId());
                x.setCourseId(courseId);
                x.setModifyDate(null);

                if (x.getSectionType() == SECTION_TYPE_TASK) {// 保存作业
                    if (x.getStudyTask() != null) {
                        StudyTask task = x.getStudyTask();
                        if (StringUtils.isEmpty(task.getId())) {
                            task = studyTaskService.insert(x.getName(), task.getDescription(), task.getAuditType(),
                                    Optional.ofNullable(task.getAuditMemberIds()),
                                    Optional.ofNullable(task.getAttachments()));
                            x.setResourceId(task.getId());
                        } else {
                            studyTaskService.update(task.getId(), x.getName(), task.getDescription(),
                                    task.getAuditType(), Optional.ofNullable(task.getAuditMemberIds()),
                                    Optional.ofNullable(task.getAttachments()));
                        }
                    } else {
                        studyTaskService.updateName(x.getResourceId(), x.getName());
                    }
                }
                return x;
            }).collect(Collectors.toList()));

            //新增调查问卷逻辑
            courseChapterQuestionnaireCommonDao.insert(courseChapterSections.stream()
                    .filter(e -> CourseChapterSection.QUESTIONNAIRE_FLAG_YES.equals(e.getQuestionnaireFlag())
                            && (CourseChapterSection.SECTION_TYPE_COURSE == e.getSectionType() || CourseChapterSection.SECTION_TYPE_URL == e.getSectionType()))
                    .map(courseChapterSection -> {
                        CourseChapterQuestionnaire courseChapterQuestionnaire = new CourseChapterQuestionnaire();
                        courseChapterQuestionnaire.forInsert();
                        courseChapterQuestionnaire.setReferenceId(StringUtils.isEmpty(courseChapterSection.getReferenceId()) ? courseChapterSection.getId() : courseChapterSection.getReferenceId());
                        courseChapterQuestionnaire.setType(CourseChapterQuestionnaire.COURSE_TYPE_ONLINE);
                        courseChapterQuestionnaire.setMouldId(String.valueOf(QuestionnaireMould.MOULD_TYPE_ONLINE));
                        return courseChapterQuestionnaire;
                    }).collect(Collectors.toList()));
        }
    }

    @Override
    public int saveCourseAttachs(String courseId, List<CourseAttachment> courseAttachs) {
        if (courseAttachs == null) return 0;
        courseAttachs.forEach(courseAttachment -> {
            courseAttachment.forInsert();
            courseAttachment.setCourseId(courseId);
            courseAttachmentCommonDao.insert(courseAttachment);
        });
        return courseAttachs.size();
    }

    @Override
    public int saveSubjectManager(String courseId, List<SubjectTopicManager> managerItems) {
        if (managerItems == null) return 0;
        managerItems.forEach(subjectManager -> {
            subjectManager.setCourseId(courseId);
            subjectManager.forInsert();
        });
        subjectTopicManagerCommonDao.insert(managerItems);
        return managerItems.size();
    }

    @Override
    public int updateSubjectManager(String courseId, List<SubjectTopicManager> managerItems) {
        subjectTopicManagerCommonDao.execute(e -> e.deleteFrom(SUBJECT_TOPIC_MANAGER).where(SUBJECT_TOPIC_MANAGER.COURSE_ID.eq(courseId)).execute());
        return saveSubjectManager(courseId, managerItems);
    }

    @Override
    public void deleteCourseAttachs(String courseId) {
        // add by wangdongyan 2018-10-15删除附件时同时更新知识的使用量
        List<String> knowledgeIds = courseAttachmentCommonDao.fetch(COURSE_ATTACHMENT.COURSE_ID.eq(courseId),
                COURSE_ATTACHMENT.SOURCE.eq(CourseAttachment.ATTACHMENT_SOURCE_KNOWLEDGE)).stream().map(CourseAttachment::getKnowledgeId).collect(Collectors.toList());
        if (knowledgeIds != null && !knowledgeIds.isEmpty()) {
            messageSender.send(MessageTypeContent.KNOWLEDGE_INFO_SYNC_DELETE,
                    MessageHeaderContent.ID, courseId,
                    MessageHeaderContent.BUSINESS_TYPE, 1 + "",
                    MessageHeaderContent.IDS, String.join(",", knowledgeIds));
        }
        courseAttachmentCommonDao.delete(COURSE_ATTACHMENT.COURSE_ID.eq(courseId));
    }

    @Override
    public List<CourseAttachment> findCourseAttachmentByCourseId(String courseId) {
        List<CourseAttachment> result = courseAttachmentCommonDao.fetch(Stream.of(COURSE_ATTACHMENT.COURSE_ID.eq(courseId)));
        result.sort(Comparator.comparingInt(CourseAttachmentEntity::getSequence));
        return result;
    }

    @Override
    public List<CourseChapter> findCourseChapterByCourseId(String courseId) {
        CourseInfo courseInfo = courseInfoCommonDao.get(courseId);
        return findCourseChapterByCourseId(courseId, Optional.ofNullable(courseInfo.getVersionId()));

    }

    @Override
    public List<CourseChapterSection> findCourseChapterById(String courseId, Optional<Integer> sectionType) {
        CourseInfo courseInfo = courseInfoCommonDao.get(courseId);
        return findCourseChapterById(courseId, Optional.ofNullable(courseInfo.getVersionId()),sectionType);

    }

    public List<CourseChapterSection> findCourseChapterById(String courseId, Optional<String> courseVersion, Optional<Integer> sectionType) {
        List<Condition> where = Stream
                .of(Optional.of(COURSE_CHAPTER.COURSE_ID.eq(courseId)),
                        courseVersion.map(COURSE_CHAPTER.VERSION_ID::eq),
                        sectionType.map(COURSE_CHAPTER_SECTION.SECTION_TYPE::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        if (!courseVersion.isPresent())
            where.add(COURSE_CHAPTER.VERSION_ID.isNull());

        Result<Record> record = courseChapterCommonDao.execute(x -> x
                .select(Fields.start().add(COURSE_CHAPTER).add(COURSE_CHAPTER_SECTION).end()).from(COURSE_CHAPTER)
                .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .where(where).orderBy(COURSE_CHAPTER.SEQUENCE.desc(), COURSE_CHAPTER_SECTION.SEQUENCE.asc()).fetch());

        Map<String, List<CourseChapterSection>> sectionMap = record
                .into(CourseChapterSection.class).stream().filter(x -> x.getId() != null)
                .collect(groupingBy(CourseChapterSection::getChapterId));

        List<CourseChapter> courseChapters = new ArrayList<>(record.into(CourseChapter.class)
                .stream().collect(toMap(CourseChapter::getId, p -> p, (p, q) -> q)).values());

        courseChapters.forEach(x -> x.setCourseChapterSections(sectionMap.get(x.getId())));

        courseChapters.sort(Comparator.comparingInt(CourseChapterEntity::getSequence)); // 倒序

        List<CourseChapterSection> sections = new ArrayList<>();
        courseChapters.forEach(item->{
            if(CollectionUtils.isEmpty(sections) || sections.size() < CommonConstant.FIVE){
                if(Objects.nonNull(item.getCourseChapterSections())){
                    sections.addAll(item.getCourseChapterSections());
                }
            }
        });

        return sections;
    }


    @Override
    public List<CourseChapter> findCourseChapterByCourseId(String courseId, Optional<String> courseVersion) {
        List<Condition> where = Stream
                .of(Optional.of(COURSE_CHAPTER.COURSE_ID.eq(courseId)),
                        courseVersion.map(COURSE_CHAPTER.VERSION_ID::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        if (!courseVersion.isPresent())
            where.add(COURSE_CHAPTER.VERSION_ID.isNull());

        Result<Record> record = courseChapterCommonDao.execute(x -> x
                .select(Fields.start().add(COURSE_CHAPTER).add(COURSE_CHAPTER_SECTION).end()).from(COURSE_CHAPTER)
                .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .where(where).orderBy(COURSE_CHAPTER.SEQUENCE.desc(), COURSE_CHAPTER_SECTION.SEQUENCE.asc()).fetch());

        List<String> courseIds = record.into(CourseChapterSection.class).stream().filter(x -> Objects.equals(CourseChapterSection.SECTION_TYPE_COURSE, x.getSectionType())).map(CourseChapterSectionEntity::getResourceId).collect(toList());

        List<String> listCourseConstructionType = Collections.emptyList();
        if (!CollectionUtils.isEmpty(courseIds)) {
            listCourseConstructionType = courseInfoCommonDao.execute(
                    r -> r.select(COURSE_INFO.ID)
                            .from(COURSE_INFO)
                            .where(COURSE_INFO.CONSTRUCTION_TYPE.eq(CourseInfo.CONSTRUCTION_TYPE_YES))
                            .and(COURSE_INFO.ID.in(courseIds))
                            .fetch(COURSE_INFO.ID));
        }

        Map<String, List<CourseChapterSection>> sectionMap = record
                .into(CourseChapterSection.class).stream().filter(x -> x.getId() != null)
                .collect(groupingBy(CourseChapterSection::getChapterId));

        List<CourseChapter> courseChapters = new ArrayList<>(record.into(CourseChapter.class)
                .stream().collect(toMap(CourseChapter::getId, p -> p, (p, q) -> q)).values());

        setSectionCourse(sectionMap);
        courseChapters.forEach(x -> x.setCourseChapterSections(sectionMap.get(x.getId())));
        List<String> finalListCourseConstructionType = listCourseConstructionType;
        courseChapters.forEach(x -> {
            List<CourseChapterSection> courseChapterSections = sectionMap.get(x.getId());
            if (!CollectionUtils.isEmpty(finalListCourseConstructionType)){
                courseChapterSections.forEach(r->{
                    if (finalListCourseConstructionType.contains(r.getResourceId())){
                        r.setConstructionType(CourseInfo.CONSTRUCTION_TYPE_YES);
                    }
                });
            }
            x.setCourseChapterSections(courseChapterSections);
        });




        courseChapters.sort(Comparator.comparingInt(CourseChapterEntity::getSequence)); // 倒序

        return courseChapters;
    }



    private List<CourseInfo> findSimpleByIds(List<String> ids) {
        return courseInfoCommonDao.execute(e -> e.select(COURSE_INFO.CODE,COURSE_INFO.ID,COURSE_INFO.STATUS)
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.in(ids))).fetch(r -> {
            CourseInfo courseInfo = r.into(COURSE_INFO).into(CourseInfo.class);
            return courseInfo;
        });
    }
    /**
     * 设置节中课程的信息
     * @param sectionMap
     */
    private void setSectionCourse(Map<String, List<CourseChapterSection>> sectionMap){
        List<String> sectionCourseId = new ArrayList<>();
        //得到所有节中的课程id
        sectionMap.forEach((k,v)->{
            sectionCourseId.addAll(v.stream().filter(m->Objects.equals(m.getSectionType(), CourseChapterSection.SECTION_TYPE_COURSE) || Objects.equals(m.getSectionType(), CourseChapterSection.SECTION_TYPE_SUBJECT))
                    .map(CourseChapterSection::getResourceId).collect(Collectors.toList()));
        });
        //查询课程信息
        List<CourseInfo> courseInfos = findSimpleByIds(sectionCourseId);
        Map<String, CourseInfo> map = courseInfos.stream().collect(toMap(CourseInfo::getId, Function.identity(), (v1, v2) -> v1));
        sectionMap.forEach((k,v)->{
            v.forEach(t->{
                CourseInfo info = map.get(t.getResourceId());
                t.setCourseInfo(info);
            });
        });
    }


    @Override
    public List<CourseChapterSection> findCourseChapterSectionByCourseId(String courseId, Optional<String> courseVersion) {
        List<Condition> where = Stream
                .of(Optional.of(COURSE_CHAPTER.COURSE_ID.eq(courseId)),
                        courseVersion.map(COURSE_CHAPTER.VERSION_ID::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        if (!courseVersion.isPresent()){
            where.add(COURSE_CHAPTER.VERSION_ID.isNull());
        }

        return courseChapterCommonDao.execute(r ->
                r.select(COURSE_CHAPTER_SECTION.SECTION_TYPE, COURSE_CHAPTER_SECTION.TIME_SECOND, COURSE_CHAPTER_SECTION.ATTACHMENT_ID, COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.TIME_MINUTE)
                        .from(COURSE_CHAPTER)
                        .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                        .where(where)
                        .fetch(o -> {
                            CourseChapterSection courseChapterSection = new CourseChapterSection();
                            courseChapterSection.setSectionType(o.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                            courseChapterSection.setTimeMinute(o.getValue(COURSE_CHAPTER_SECTION.TIME_MINUTE));
                            courseChapterSection.setTimeSecond(o.getValue(COURSE_CHAPTER_SECTION.TIME_SECOND));
                            courseChapterSection.setAttachmentId(o.getValue(COURSE_CHAPTER_SECTION.ATTACHMENT_ID));
                            courseChapterSection.setResourceId(o.getValue(COURSE_CHAPTER_SECTION.RESOURCE_ID));
                            return courseChapterSection;
                        }));

    }

    @Override
    public List<CourseChapter> findRedShipCourseChapterByCourseId(String courseId) {
        CourseInfo courseInfo = courseInfoCommonDao.get(courseId);
        return findRedShipCourseChapterByCourseId(courseId, Optional.ofNullable(courseInfo.getVersionId()));
    }

    public List<CourseChapter> findRedShipCourseChapterByCourseId(String courseId, Optional<String> courseVersion) {
        List<Condition> where = Stream
                .of(Optional.of(COURSE_CHAPTER.COURSE_ID.eq(courseId)),
                        courseVersion.map(COURSE_CHAPTER.VERSION_ID::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        if (!courseVersion.isPresent())
            where.add(COURSE_CHAPTER.VERSION_ID.isNull());

        // 查询章节类型为文档1音频5视频6
        where.add(COURSE_CHAPTER_SECTION.SECTION_TYPE.in(CourseChapterSection.SECTION_TYPE_DOC,
                CourseChapterSection.SECTION_TYPE_AUDIO,
                CourseChapterSection.SECTION_TYPE_VIDEO));

        return courseChapterCommonDao.execute(x -> x
                .select(Fields.start().add(COURSE_CHAPTER).add(COURSE_CHAPTER_SECTION).end()).from(COURSE_CHAPTER)
                .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .where(where).orderBy(COURSE_CHAPTER.SEQUENCE.asc(), COURSE_CHAPTER_SECTION.SEQUENCE.asc()).fetch(r -> {
                    CourseChapter courseChapter = new CourseChapter();
                    courseChapter.setId(r.getValue(COURSE_CHAPTER.ID));
                    courseChapter.setCourseId(r.getValue(COURSE_CHAPTER.COURSE_ID));
                    courseChapter.setName(r.getValue(COURSE_CHAPTER.NAME));
                    courseChapter.setSequence(r.getValue(COURSE_CHAPTER.SEQUENCE));
                    CourseChapterSection courseChapterSection = new CourseChapterSection();
                    courseChapterSection.setId(r.getValue(COURSE_CHAPTER_SECTION.ID));
                    courseChapterSection.setName(r.getValue(COURSE_CHAPTER_SECTION.NAME));
                    courseChapterSection.setSectionType(r.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                    courseChapterSection.setSequence(r.getValue(COURSE_CHAPTER_SECTION.SEQUENCE));
                    courseChapterSection.setAttachmentId(r.getValue(COURSE_CHAPTER_SECTION.ATTACHMENT_ID));
                    courseChapter.setCourseChapterSection(courseChapterSection);
                    return courseChapter;
                }));
    }

    private int getTypeByCourseId(String courseId) {
        Result<Record1<Integer>> recond = courseChapterSectionCommonDao
                .execute(x -> x.selectDistinct(COURSE_CHAPTER_SECTION.SECTION_TYPE).from(COURSE_CHAPTER_SECTION)
                        .where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(courseId)).fetch());
        if (recond.isEmpty() || recond.size() > 1)
            return 0;
        return recond.get(0).value1();
    }

    @Override
    public CourseInfo update(String id, int publishClient, int source, Optional<Integer> publishType,
                             Optional<String> name, Optional<Long> beginDate, Optional<Long> endDate,
                             Optional<Integer> courseHour,
                             Optional<Integer> credit, Optional<Integer> courseTime, Optional<String> desc,
                             Optional<String> descText,
                             Optional<String> releaseUser, Optional<String> releaseOrg, Optional<String> organizationId,
                             Optional<String> devUser, Optional<Long> devTime, Optional<String> code,
                             Optional<Integer> price,
                             Optional<Integer> courseSecond, Optional<String> certificateId,
                             Optional<String> categoryId,
                             Optional<Integer> learnSequence, Optional<Integer> addType, Optional<Integer> studyDays,
                             Optional<String> cover, Optional<String> coverPath, Optional<String> sourceDetail,
                             Optional<String> lecturer,
                             Optional<Integer> shareSub, List<CourseChapter> courseChapters,
                             List<CourseAttachment> courseAttachments,
                             List<AudienceItem> audienceItems, Optional<CourseShelves> courseShelves,
                             Optional<String> styles, Optional<String> topicIds, Optional<Integer> isPublic,
                             List<SubjectAdvertising> advertisingList,
                             List<CoursePhoto> photos, List<SubjectTextArea> textAreas, Optional<String> url) {
        CourseInfo c = courseInfoCommonDao.getOptional(id).orElse(null);
        if (c == null)
            return null;

        c.setSource(source);
        c.setPublishClient(publishClient);
        c.setPublishType(publishType.orElse(null));
        c.setName(name.orElse(null));
        beginDate.ifPresent(c::setBeginDate);
        endDate.ifPresent(c::setEndDate);
        courseHour.ifPresent(c::setCourseHour);
        credit.ifPresent(c::setCredit);
        courseTime.ifPresent(c::setCourseTime);
        c.setDescription(desc.orElse(null));
        c.setDescriptionText(descText.orElse(null));
        c.setReleaseMemberId(releaseUser.orElse(null));
        c.setReleaseOrgId(releaseOrg.orElse(null));
        devTime.ifPresent(c::setDevelopTime);
        devUser.ifPresent(c::setDevelopMemberId);
        code.ifPresent(c::setCode);
        price.ifPresent(c::setPrice);
        certificateId.ifPresent(c::setCertificateId);
        organizationId.ifPresent(c::setOrganizationId);
        courseSecond.ifPresent(c::setCourseSecond);
        categoryId.ifPresent(c::setCategoryId);
        learnSequence.ifPresent(c::setLearnSequence);
        studyDays.ifPresent(c::setStudyDays);
        cover.ifPresent(c::setCover);
        coverPath.ifPresent(c::setCoverPath);
        addType.ifPresent(c::setAddType);
        c.setSourceDetail(sourceDetail.orElse(null));
        c.setLecturer(lecturer.orElse(null));
        shareSub.ifPresent(c::setShareSub);
        url.ifPresent(c::setUrl);
        isPublic.ifPresent(c::setIsPublic);
        styles.ifPresent(s -> {
            c.setStyles(s);
            subjectAdvertisingService.save(c.getId(), c.getCreateMemberId(), advertisingList);
            this.savePhotos(c.getId(), photos);
            this.saveTextArea(c.getId(), textAreas);
        });
        // updated by wangdongyan 2018-06-20 查询课程id对应的所有section的id集合，用于清除缓存
        List<String> sectionIds = courseCacheService.getSectionIds(id);
        List<String> versionIds = courseCacheService.getVersionIds(id);
        for (String sectionId : sectionIds) {
            courseCacheService.clearChapterSection(sectionId);
        }
        for (String versionId : versionIds) {
            courseCacheService.clearCourseChapter(id, versionId);
        }

        // 下架状态下才走章节修改逻辑
        if (c.getStatus() != null && !c.getStatus().equals(CourseInfo.STATUS_SHELVES) && !c.getStatus().equals(CourseInfo.STATUS_THE_TEST)) {
            List<CourseChapter> courseChapterList = courseChapterCommonDao
                    .fetch(COURSE_CHAPTER.COURSE_ID.eq(id).and(COURSE_CHAPTER.VERSION_ID.isNull()));
            courseChapterList.stream().forEach(chapter -> {
                courseChapterCommonDao.delete(COURSE_CHAPTER.ID.eq(chapter.getId()));
                courseChapterSectionCommonDao.delete(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(chapter.getId()));
                List<String> courseChapterIds = courseChapterSectionCommonDao
                        .execute(e -> e
                                .select(COURSE_CHAPTER_SECTION.ID)
                                .from(COURSE_CHAPTER_SECTION)
                                .where(COURSE_CHAPTER_SECTION.CHAPTER_ID
                                        .eq(chapter.getId()))
                                .fetch(COURSE_CHAPTER_SECTION.ID));
                dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourse(DeleteDataCourse.getTableName(COURSE_CHAPTER), chapter.getId(), ""));
                dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(DeleteDataCourse.COURSE_CHAPTER_SECTION, courseChapterIds, ""));
            });
            this.saveCourseChapters(id, courseChapters);
        }

        courseAttachmentCommonDao.delete(COURSE_ATTACHMENT.COURSE_ID.eq(id));

        // 重设课程和分享组织的受众对象,update受众项
        if (!audienceItems.isEmpty()) {
            // 获取旧的受众项id
            List<String> itemIds = audienceObjectCommonDao
                    .fetch(AUDIENCE_OBJECT.BUSINESS_ID.eq(id)
                            .and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COURSE)
                                    .or(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SHARE))))
                    .stream().map(x -> x.getItemId()).collect(Collectors.toList());
            // 旧受众项引用次数-1,或者删除受众项
            audienceItemService.deleteList(itemIds);
            // 删除课程受众对象,以及课程要分享组织的受众对象
            List<String> objectIds = audienceObjectCommonDao.execute(e -> e
                    .select(AUDIENCE_OBJECT.ID)
                    .from(AUDIENCE_OBJECT)
                    .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(id))
                    .and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT)
                            .or(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SHARE)))
                    .fetch(AUDIENCE_OBJECT.ID));
            List<String> ids = audienceObjectCommonDao.execute(dsl -> dsl
                    .select(AUDIENCE_OBJECT.ID)
                    .from(AUDIENCE_OBJECT)
                    .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(id)
                            .and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COURSE)
                                    .or(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SHARE)))
                    )
                    .fetch(Record1::value1)
            );
            audienceObjectCommonDao.delete(ids);
            dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(AUDIENCE_OBJECT.getName(), ids, ""));
            objectIds.forEach(objectId->dataCourseCommonDao.insert(DeleteDataCourse
                    .getDeleteDataCourse(DeleteDataCourse.AUDIENCE_OBJECT,objectId,"")));
            // 保存课程和分享组织的受众对象
            int audienceBusinessType = AudienceObject.BUSINESS_TYPE_COURSE;
            if (c.getBusinessType() != null && c.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
                audienceBusinessType = AudienceObject.BUSINESS_TYPE_SUBJECT;
            }
            this.saveCourseAudienceObject(id, audienceItems, audienceBusinessType);
        }


        this.saveCourseAttachs(id, courseAttachments);
        int topicBusinessType = c.getBusinessType() == CourseInfo.BUSINESS_TYPE_SUBJECT ?
                BusinessTopic.BUSINESS_TYPE_SUBJECT : BusinessTopic.BUSINESS_TYPE_COURSE;
        topicIds.ifPresent(ids -> businessTopicService.saveBusinessTopic(c.getId(), topicBusinessType, ids.split(",")));


        c.setType(this.getTypeByCourseId(id));

        // 上架(发布)
        courseShelves.ifPresent(s -> {
            c.setReleaseTime(System.currentTimeMillis());
            publishType.ifPresent(type -> {
                //c.setStatus(CourseInfo.STATUS_THE_TEST);
                c.setStatus(CourseInfo.STATUS_IN_SHELVES);
                if (type == CourseInfo.PUBLISH_TYPE_FORMAL) {
                    //c.setStatus(CourseInfo.STATUS_SHELVES);
                    if (c.getShelveTime() == null) c.setShelveTime(System.currentTimeMillis());
                }
            });
            this.syncKnowledge(courseAttachments, c);
            this.addReferenceId(courseChapters);
        });
        c.setModifyDate(null);
        courseInfoCommonDao.update(c);
        courseShelves.ifPresent(s -> shelves(c.getId(), s, CourseInfo.STATUS_NO, c.getPublishType()));
        messageSender.send(MessageTypeContent.COURSE_INFO_UPDATE, MessageHeaderContent.ID, c.getId(),
                MessageHeaderContent.NAME, c.getName());
        return c;
    }

    @Override
    public CourseInfo updateSubject(String id, int publishClient, Optional<Integer> publishType, Optional<String> name,
                                    Optional<Long> beginDate, Optional<Long> endDate, Optional<String> desc,
                                    Optional<String> descText,
                                    Optional<String> releaseUser, Optional<String> releaseOrg,
                                    Optional<String> organizationId,
                                    Optional<String> code, Optional<Integer> learnSequence,
                                    Optional<Integer> studyDays, Optional<String> cover, Optional<String> coverPath,
                                    Optional<Integer> shareSub, List<CourseChapter> courseChapters,
                                    List<CourseAttachment> courseAttachments,
                                    List<AudienceItem> audienceItems, Optional<CourseShelves> courseShelves,
                                    Optional<String> styles, Optional<String> topicIds,
                                    List<SubjectAdvertising> advertisingList,
                                    List<CoursePhoto> photos, List<SubjectTextArea> textAreas, Optional<String> url) {
        CourseInfo c = courseInfoCommonDao.getOptional(id).orElse(null);
        if (c == null)
            return null;

        c.setPublishClient(publishClient);
        publishType.ifPresent(c::setPublishType);
        name.ifPresent(c::setName);
        beginDate.ifPresent(c::setBeginDate);
        endDate.ifPresent(c::setEndDate);
        desc.ifPresent(c::setDescription);
        descText.ifPresent(c::setDescriptionText);
        releaseUser.ifPresent(c::setReleaseMemberId);
        releaseOrg.ifPresent(c::setReleaseOrgId);
        code.ifPresent(c::setCode);
        organizationId.ifPresent(c::setOrganizationId);
        learnSequence.ifPresent(c::setLearnSequence);
        studyDays.ifPresent(c::setStudyDays);
        cover.ifPresent(c::setCover);
        coverPath.ifPresent(c::setCoverPath);
        shareSub.ifPresent(c::setShareSub);
        url.ifPresent(c::setUrl);
        styles.ifPresent(s -> {
            c.setStyles(s);
            subjectAdvertisingService.save(c.getId(), c.getCreateMemberId(), advertisingList);
            this.savePhotos(c.getId(), photos);
            this.saveTextArea(c.getId(), textAreas);
        });
        // 下架状态编辑主题逻辑
        if (c.getStatus() != null && !c.getStatus().equals(CourseInfo.STATUS_SHELVES) && !c.getStatus().equals(CourseInfo.STATUS_THE_TEST)) {
            List<CourseChapter> courseChapterList = courseChapterCommonDao
                    .fetch(COURSE_CHAPTER.COURSE_ID.eq(id).and(COURSE_CHAPTER.VERSION_ID.isNull()));
            // updated by wangdongyan 2018-06-20 查询课程id对应的所有section的id集合，用于清除缓存
            List<String> sectionIds = courseCacheService.getSectionIds(id);
            List<String> versionIds = courseCacheService.getVersionIds(id);
            courseChapterList.stream().forEach(chapter -> {
                courseChapterCommonDao.delete(COURSE_CHAPTER.ID.eq(chapter.getId()));
                List<String> courseChapterIds = courseChapterSectionCommonDao
                        .execute(e -> e
                        .select(COURSE_CHAPTER_SECTION.ID)
                        .from(COURSE_CHAPTER_SECTION)
                        .where(COURSE_CHAPTER_SECTION.CHAPTER_ID
                                .eq(chapter.getId()))
                                .fetch(COURSE_CHAPTER_SECTION.ID));
                courseChapterSectionCommonDao.delete(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(chapter.getId()));
                dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourse(DeleteDataCourse.getTableName(COURSE_CHAPTER), chapter.getId(), ""));
                dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(DeleteDataCourse.COURSE_CHAPTER_SECTION, courseChapterIds, ""));
            });
            // updated by wangdongyan 2018-06-20
            for (String sectionId : sectionIds) {
                courseCacheService.clearChapterSection(sectionId);
            }
            for (String versionId : versionIds) {
                courseCacheService.clearCourseChapter(id, versionId);
            }
        } else {
            // 发布状态下编辑，清除专题版本
            c.setVersionId(null);
        }


        courseAttachmentCommonDao.delete(COURSE_ATTACHMENT.COURSE_ID.eq(id));

        // 重设课程和分享组织的受众对象,update受众项
        if (!audienceItems.isEmpty()) {
            // 获取旧的受众项id
            List<String> itemIds = audienceObjectCommonDao
                    .fetch(AUDIENCE_OBJECT.BUSINESS_ID.eq(id)
                            .and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT)
                                    .or(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SHARE))))
                    .stream().map(x -> x.getItemId()).collect(Collectors.toList());
            // 旧受众项引用次数-1,或者删除受众项
            audienceItemService.deleteList(itemIds);
            // 删除课程受众对象,以及课程要分享组织的受众对象
            List<String> objectIds = audienceObjectCommonDao.execute(e -> e
                    .select(AUDIENCE_OBJECT.ID)
                    .from(AUDIENCE_OBJECT)
                    .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(id))
                    .and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT)
                            .or(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SHARE)))
                    .fetch(AUDIENCE_OBJECT.ID));
            List<String> ids = audienceObjectCommonDao.execute(dsl -> dsl
                    .select(AUDIENCE_OBJECT.ID)
                    .from(AUDIENCE_OBJECT)
                    .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(id)
                            .and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT)
                                    .or(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SHARE)))).fetch(Record1::value1)
            );
            audienceObjectCommonDao.delete(ids);
            dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(AUDIENCE_OBJECT.getName(), ids, ""));
            objectIds.forEach(objectId->dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourse(DeleteDataCourse.AUDIENCE_OBJECT,objectId,"")));
            // 保存课程和分享组织的受众对象
            int audienceBusinessType = AudienceObject.BUSINESS_TYPE_COURSE;
            if (c.getBusinessType() != null && c.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
                audienceBusinessType = AudienceObject.BUSINESS_TYPE_SUBJECT;
            }
            this.saveCourseAudienceObject(id, audienceItems, audienceBusinessType);
        }

        this.saveCourseChapters(id, courseChapters);
        this.saveCourseAttachs(id, courseAttachments);
        int topicBusinessType = c.getBusinessType() == CourseInfo.BUSINESS_TYPE_SUBJECT ?
                BusinessTopic.BUSINESS_TYPE_SUBJECT : BusinessTopic.BUSINESS_TYPE_COURSE;
        topicIds.ifPresent(ids -> businessTopicService.saveBusinessTopic(c.getId(), topicBusinessType, ids.split(",")));
        c.setType(this.getTypeByCourseId(id));

        // 上架(发布)
        courseShelves.ifPresent(s -> {
            c.setReleaseTime(System.currentTimeMillis());

            publishType.ifPresent(type -> {
                //c.setStatus(CourseInfo.STATUS_THE_TEST);
                //xwolf
                c.setStatus(CourseInfo.STATUS_IN_SHELVES);
                if (type == CourseInfo.PUBLISH_TYPE_FORMAL) {
                    c.setShelveTime(System.currentTimeMillis());
                    // c.setStatus(CourseInfo.STATUS_SHELVES);
                }
            });
            shelves(c.getId(), s, CourseInfo.STATUS_NO, c.getPublishType());
            this.syncKnowledge(courseAttachments, c);
            this.addReferenceId(courseChapters);
        });
        c.setModifyDate(null);
        courseInfoCommonDao.update(c);
        if (!courseShelves.isPresent()) { // 此次修改不是发布动作
            int status = c.getStatus() == null ? 0 : c.getStatus();
            if (status == CourseInfo.STATUS_SHELVES || status == CourseInfo.STATUS_THE_TEST) { // 此次修改是在发布状态下修改
                // 发布状态下修改,重新走一遍上架逻辑
                this.addReferenceId(courseChapters);
                shelvesDao.execute(d -> d.update(COURSE_SHELVES).set(COURSE_SHELVES.STATUS,
                        CourseShelves.STATUS_FAILURE)
                        .where(COURSE_SHELVES.COURSE_ID.eq(c.getId())).execute());
                CourseShelves shelves = new CourseShelves();
                shelves.forInsert();
                shelves.setCourseId(c.getId());
                shelves.setIsFirst(1);
                shelves.setNoticeUser(0);
                shelves.setNoticeManager(0);
                shelves.setStatus(1);
                shelves.setRule(2);
                shelvesDao.insert(shelves);
                messageSender.send(MessageTypeContent.COURSE_SHELVES, MessageHeaderContent.ID, shelves.getId());
                this.syncKnowledge(courseAttachments, c);
            }
        }
        messageSender.send(MessageTypeContent.COURSE_INFO_UPDATE, MessageHeaderContent.ID, c.getId(),
                MessageHeaderContent.NAME, c.getName());
        return c;
    }
    public int delete(String id,Integer history) {
        CourseInfo course = courseInfoCommonDao.get(id);
        int businessType = course.getBusinessType().intValue();
        int result = 0;
        Integer count = courseStudyProgressService.totalStudyMember(id);
        // 查询归档库的count
        count += history;
        // 被人学过的则不能删除
        if (count != null && count > 0) {
            throw new UnprocessableException(ErrorCode.DataViolation);
        }
        // 加到推送的不能删除
        if (pushObjectDao.fetchOne(STUDY_PUSH_OBJECT.BUSINESS_ID.eq(id)).isPresent()) {
            throw new UnprocessableException(ErrorCode.DataViolation);
        }

        // 添加到专题中的不能删除
        List<CourseChapterSection> sectionList = getSubjectSectionBasicByCourseId(id);
        if (sectionList != null && !sectionList.isEmpty()) {
            throw new UnprocessableException(ErrorCode.DataViolation);
        }
        // 添加到学习地图中的不能删除
        List<String> studyMapBasicByCourseId = getStudyMapBasicByCourseId(id);
        if (studyMapBasicByCourseId != null && !studyMapBasicByCourseId.isEmpty()) {
            throw new UnprocessableException(ErrorCode.DataViolation);
        }


        if (course.getStatus() == CourseInfo.STATUS_UNPUBLISHED) {
            result = courseInfoCommonDao.delete(id);
            dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourse(DeleteDataCourse.COURSE_INFO,id,""));
            // updated by wangdongyan 2018-06-20 获取课程对应的所有章节的id集合用于下边清除章节缓存
            List<String> sectionIds = courseCacheService.getSectionIds(id);
            List<String> versionIds = courseCacheService.getVersionIds(id);
            // 删除章节、附件
            courseChapterCommonDao.delete(COURSE_CHAPTER.COURSE_ID.eq(id));
            List<String> courseChapters = courseChapterSectionCommonDao.execute(e -> e
                    .select(COURSE_CHAPTER_SECTION.ID)
                    .from(COURSE_CHAPTER_SECTION)
                    .where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(id))
                    .fetch(COURSE_CHAPTER_SECTION.ID));
            courseChapterSectionCommonDao.delete(COURSE_CHAPTER_SECTION.COURSE_ID.eq(id));
            dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourse(DeleteDataCourse.getTableName(COURSE_CHAPTER), id, ""));
            dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(DeleteDataCourse.COURSE_CHAPTER_SECTION, courseChapters, ""));
            courseAttachmentCommonDao.delete(COURSE_ATTACHMENT.COURSE_ID.eq(id));
            // updated by wangdongyan 2018-06-20
            for (String sectionId : sectionIds) {
                courseCacheService.clearChapterSection(sectionId);
            }
            for (String versionId : versionIds) {
                courseCacheService.clearCourseChapter(id, versionId);
            }
        } else {
            course.setDeleteFlag(CourseInfo.DELETE_FLAG_YES);
            course.setRelativeGenseeId(null);// 假删清除关联直播
            course.setModifyDate(null);
            courseInfoCommonDao.update(course);
            result = 1;
        }
        //目录关联表删除
        courseInfoCategoryService.deleteAllCategories(id);
        courseAbilityDao.delete(COURSE_ABILITY.COURSE_ID.eq(id));

        int topicBusinessType = businessType == CourseInfo.BUSINESS_TYPE_SUBJECT ?
                BusinessTopic.BUSINESS_TYPE_SUBJECT : BusinessTopic.BUSINESS_TYPE_COURSE;
        businessTopicService.delete(id, topicBusinessType);
        messageSender.send(MessageTypeContent.COURSE_INFO_DELETE, MessageHeaderContent.ID, id);
        return result;
    }
    @Override
    public int delete(String id) {
        return delete(id, 0);
    }

    /**
     * 统计参与人数
     *
     * @param referenceIds
     * @return
     */
    @Override
    public Map<String, Integer> countSubmitPerson(String[] referenceIds,String userId) {
        // update for xdn 课程-作业管理
        if (referenceIds == null || referenceIds.length == 0)
            return new HashMap<>();
        CourseInfo courseInfo = courseCacheService.getCourseByReferenceId(referenceIds[0]);
//        TableImpl<?> csspTable = shardingConfigService.getTableName(courseInfo.getId(),
//                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil
                .getTable(userId,courseInfo.getId());
        Field<Integer> personCount = csspTable.field("f_member_id", String.class).countDistinct().as("personCount");
        return courseChapterSectionCommonDao.execute(d -> d
                .select(Fields.start().add(csspTable.field("f_section_id"), personCount).end())
                .from(csspTable).where(csspTable.field("f_section_id", String.class).in(referenceIds)
                        .and(csspTable.field("f_finish_status", Integer.class).in(CourseSectionStudyProgress.FINISH_STATUS_AUDIT,
                                CourseSectionStudyProgress.FINISH_STATUS_FINISH,
                                CourseSectionStudyProgress.FINISH_STATUS_NOT_THROUGH)))
                .groupBy(csspTable.field("f_section_id")).fetch()
                .intoMap(csspTable.field("f_section_id", String.class), personCount));
    }

    @Override
    public List<CourseChapterSection> findResearchFromSection(String id) {
        return courseInfoCommonDao.getOptional(id).map(course -> courseChapterSectionCommonDao.execute(dao ->
                dao.selectDistinct(Fields.start().add(COURSE_CHAPTER_SECTION).end())
                        .from(COURSE_CHAPTER_SECTION)
                        .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                        .where(COURSE_CHAPTER.COURSE_ID.eq(id))
                        .and(course.getVersionId() == null ? COURSE_CHAPTER.VERSION_ID.isNull() :
                                COURSE_CHAPTER.VERSION_ID.eq(course.getVersionId()))
                        .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.in(CourseChapterSection.SECTION_TYPE_RESEARCH,
                                CourseChapterSection.SECTION_TYPE_EVALUATION))
                        .fetchInto(CourseChapterSection.class)
        )).orElse(new ArrayList<>());

    }

    @Override
    public Optional<CourseInfo> getOptional(String id) {
        return courseInfoCommonDao.getOptional(id).map(course -> {
            // 查询章节和章节详情
            course.setCourseChapters(this.findCourseChapterByCourseId(id, Optional.ofNullable(course.getVersionId())));
            return course;
        });
    }

    @Override
    public int saveCourseAudienceObject(String courseId, List<AudienceItem> audienceItems, int businessType) {
        audienceItems = audienceItemService.insertList(audienceItems);
        List<AudienceObject> audienceObjects = audienceItems.stream().map(x -> {
            AudienceObject ao = new AudienceObject();
            ao.forInsert();
            ao.setBusinessId(courseId);
            ao.setBusinessType(businessType);
            ao.setItemId(x.getId());
            return ao;
        }).collect(Collectors.toList());
        audienceObjectCommonDao.insert(audienceObjects);
        return audienceItems.size();
    }

    @Override
    public CourseInfo editCourseShelves(CourseInfo courseInfo, Integer status, CourseShelves courseShelves,
                                        Optional<Integer> businessType, String currentUserId, Optional<Integer> constructionType) {
        // 上架
        if (status == CourseInfo.STATUS_SHELVES) {
            // 课程或者专题正在发布中，不支持取消发布动作
            courseCacheService.setSubjectPublishStatus(courseInfo.getId());
            //xwolf 发布中
            courseInfo.setStatus(CourseInfo.STATUS_IN_SHELVES);
            courseInfo.setReleaseTime(System.currentTimeMillis());
            this.syncKnowledge(courseInfo.getCourseAttachments(), courseInfo);
            // updated by wangdongyan 修改专题章节重复记录时长的问题，重新发布时专题章节的referenceId需特殊处理
            logger.info("操作业务类型，businessType={}", businessType);
            if (businessType.isPresent() && CourseInfo.BUSINESS_TYPE_SUBJECT.equals(businessType.get())) {
                subjectService.addReferenceId(courseInfo.getCourseChapters(), 1);
            } else {
                courseInfoCommonDao.execute(dslContext ->
                        dslContext.update(COURSE_CHAPTER_SECTION).set(COURSE_CHAPTER_SECTION.REFERENCE_ID,
                                COURSE_CHAPTER_SECTION.ID)
                                .where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(courseInfo.getId()).and(COURSE_CHAPTER_SECTION.REFERENCE_ID.isNull()))
                                .execute());
            }
            courseInfo.setModifyDate(null);
            courseInfo.setUpdateDate(System.currentTimeMillis());
            CourseInfo c = courseInfoCommonDao.update(courseInfo);
            //xwolf 正式发布
            shelves(courseInfo.getId(), courseShelves, status, CourseInfo.PUBLISH_TYPE_FORMAL);
            //上架发消息 用于人工智能推送
//      messageSender.send(MessageTypeContent.COURSE_SHELVES_OPERATION_UP, MessageHeaderContent.ID, courseInfo.getId());
            return c;
        } else if (status == CourseInfo.STATUS_THE_SHELVES) {
            if (courseInfo.getVersionId() != null) { // 如果版本空的话 下架会重复章节
                List<CourseChapter> courseChapters = this.findCourseChapterByCourseId(courseInfo.getId(),
                        Optional.of(courseInfo.getVersionId()));
                // 专题多维度评分：处理老主题对应的评分表,仅限于专题
                if(CourseInfo.BUSINESS_TYPE_SUBJECT.equals(courseInfo.getBusinessType())){
                    List<String> courseChapterIds =courseChapters.stream().map(x -> x.getId()).collect(Collectors.toList());
                    if(!CollectionUtils.isEmpty(courseChapterIds)){
                        Map<String, String> oldMultiScorings = multidimensionalScoringService.findBySubjectIdAndTopices(courseInfo.getId(),courseChapterIds);
                        courseChapters.forEach(courseChapter -> {
                            courseChapter.setScoringId(oldMultiScorings.get(courseChapter.getId()));
                        });
                    }

                }
                this.saveCourseChapters(courseInfo.getId(), courseChapters,currentUserId);
            }
            if (courseInfo.getStatus() == CourseInfo.STATUS_THE_TEST)
                courseInfo.setStatus(CourseInfo.STATUS_UNPUBLISHED);
            else  {
                courseInfo.setStatus(CourseInfo.STATUS_THE_SHELVES);
            }
            courseInfo.setOffTime(System.currentTimeMillis());
            courseInfo.setVersionId(null);
            courseInfo.setModifyDate(null);
            if (constructionType.isPresent() && courseInfo.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_COURSE)){
                courseInfo.setStatus(CourseInfo.STATUS_UNPUBLISHED);
                courseInfo.setConstructionType(constructionType.get());
            }
            courseInfoCommonDao.update(courseInfo);
            // 下架
            theShelves(courseInfo.getId(), currentUserId);

        }
        return courseInfo;
    }


    @Override
    public CourseInfo studioCourseShelves(Boolean approveSwitch, CourseInfo courseInfo, Integer status,
                                          CourseShelves courseShelves, String currentUserId) {
        // 上架
        if (status == CourseInfo.STATUS_SHELVES) {

            // 发布中
            courseInfo.setStatus(approveSwitch ? CourseInfo.STATUS_APPROVE : CourseInfo.STATUS_IN_SHELVES);
            courseInfo.setReleaseTime(System.currentTimeMillis());
            this.syncKnowledge(courseInfo.getCourseAttachments(), courseInfo);
            courseInfoCommonDao.execute(dslContext ->
                    dslContext.update(COURSE_CHAPTER_SECTION).set(COURSE_CHAPTER_SECTION.REFERENCE_ID,
                            COURSE_CHAPTER_SECTION.ID)
                            .where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(courseInfo.getId()).and(COURSE_CHAPTER_SECTION.REFERENCE_ID.isNull()))
                            .execute());
            courseInfo.setModifyDate(null);
            CourseInfo c = courseInfoCommonDao.update(courseInfo);
            if (!approveSwitch) {
                courseCacheService.setSubjectPublishStatus(courseInfo.getId());
                shelves(courseInfo.getId(), courseShelves, status, CourseInfo.PUBLISH_TYPE_FORMAL);
            }
            return c;
        } else if (status == CourseInfo.STATUS_THE_SHELVES) {
            if (courseInfo.getVersionId() != null) { // 如果版本空的话 下架会重复章节
                List<CourseChapter> courseChapters = this.findCourseChapterByCourseId(courseInfo.getId(),
                        Optional.of(courseInfo.getVersionId()));
                this.saveCourseChapters(courseInfo.getId(), courseChapters);
            }
            if (courseInfo.getStatus() == CourseInfo.STATUS_THE_TEST)
                courseInfo.setStatus(CourseInfo.STATUS_UNPUBLISHED);
            else {
                courseInfo.setStatus(CourseInfo.STATUS_THE_SHELVES);
            }
            courseInfo.setOffTime(System.currentTimeMillis());
            courseInfo.setVersionId(null);
            courseInfo.setModifyDate(null);
            courseInfoCommonDao.update(courseInfo);
            // 下架
            theShelves(courseInfo.getId(), currentUserId);
        }
        return courseInfo;
    }

    @Override
    public CourseInfo editCourseRetreat(CourseInfo courseInfo, Integer status, String currentUserId) {
        courseInfo.setStatus(status);
        //课程退库发消息  用于人工智能推送
        messageSender.send(MessageTypeContent.COURSE_RETREAT, MessageHeaderContent.ID, courseInfo.getId());

        //通知相关专题创建人
        messageSender.send(MessageTypeContent.COURSE_DISAPPEAR, MessageHeaderContent.ID, courseInfo.getId(),
                MessageHeaderContent.NAME, currentUserId, MessageHeaderContent.COURSE_STATUS, String.valueOf(status));
        courseInfo.setModifyDate(null);
        return courseInfoCommonDao.update(courseInfo);
    }

    @Override
    public CourseChapterSection getSectionByIds(String sectionId, String memberId) {
        // update for xdn 查看作业详情
        CourseInfo courseInfo = courseCacheService.getCourseBySectionId(sectionId);
//        TableImpl<?> csspTable = shardingConfigService.getTableName(courseInfo.getId(),
//                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil
                .getTable(memberId,courseInfo.getId());
        com.zxy.product.course.jooq.tables.Member auditMember = MEMBER.as("auditMember"); // 审核人
        com.zxy.product.course.jooq.tables.Member commitMember = MEMBER.as("commitMember"); // 审核人
        CourseChapterSection courseChapterSection = courseChapterSectionCommonDao.execute(dao -> dao
                .select(Fields.start().add(COURSE_CHAPTER_SECTION).add(csspTable)
                        .add(commitMember.FULL_NAME).add(auditMember.FULL_NAME).end())
                .from(COURSE_CHAPTER_SECTION).leftJoin(csspTable)
                .on(COURSE_CHAPTER_SECTION.REFERENCE_ID.eq(csspTable.field("f_section_id", String.class))
                        .and(csspTable.field("f_member_id", String.class).eq(memberId)))
                .leftJoin(commitMember).on(csspTable.field("f_member_id", String.class).eq(commitMember.ID))
                .leftJoin(auditMember).on(csspTable.field("f_audit_member_id", String.class).eq(auditMember.ID))
                .where(COURSE_CHAPTER_SECTION.ID.eq(sectionId)).fetchOne().map(record -> {
                    CourseChapterSection section = record.into(COURSE_CHAPTER_SECTION).into(CourseChapterSection.class);
                    CourseSectionStudyProgress progress = new CourseSectionStudyProgress().fill(csspTable, record);
//                    CourseSectionStudyProgress progress = record.into(COURSE_SECTION_STUDY_PROGRESS)
//                            .into(CourseSectionStudyProgress.class);
                    progress.setCommitMemberName(record.getValue(commitMember.FULL_NAME));
                    progress.setAuditMemberName(record.getValue(auditMember.FULL_NAME));
                    int sectionType = section.getSectionType() != null ? section.getSectionType() : 0;
                    if (sectionType == CourseChapterSection.SECTION_TYPE_TASK) {
                        section.setStudyTask(studyTaskService.get(section.getResourceId()));
                        progress.setSectionAttachments(sectionProgressAttachmentDao
                                .fetch(COURSE_SECTION_PROGRESS_ATTACHMENT.SECTION_PROGRESS_ID.eq(progress.getId())));
                    }
                    section.setProgress(progress);
                    return section;
                }));

        return courseChapterSection;
    }

    /**
     * 上架操作
     *
     * @param courseId
     * @param courseShelves
     */
    private void shelves(String courseId, CourseShelves courseShelves, Integer status, Integer publishType) {
        courseShelves.setCourseId(courseId);
        courseShelves.setStatus(CourseShelves.STATUS_OK);
        courseShelves.forInsert();
        shelvesDao.insert(courseShelves);
        messageSender.send(MessageTypeContent.COURSE_SHELVES,
                MessageHeaderContent.ID, courseShelves.getId(),
                MessageHeaderContent.BUSINESS_ID, courseId,
                MessageHeaderContent.COURSE_STATUS, String.valueOf(status),// 课程状态，上下架
                MessageHeaderContent.COURSE_PUBLISH_TYPE, String.valueOf(publishType)// 发布类型 1:正式 其他 测试发布
        );
    }

    /**
     * 下架操作
     *
     * @param
     * @param courseId
     * @param currentUserId
     */
    private void theShelves(String courseId, String currentUserId) {
        shelvesDao.execute(d -> d.update(COURSE_SHELVES).set(COURSE_SHELVES.STATUS, CourseShelves.STATUS_FAILURE)
                .where(COURSE_SHELVES.COURSE_ID.eq(courseId)).execute());

        messageSender.send(MessageTypeContent.COURSE_THE_SHELVES, MessageHeaderContent.ID, courseId);
        //通知相关专题创建人
        messageSender.send(MessageTypeContent.COURSE_DISAPPEAR, MessageHeaderContent.ID, courseId,
                MessageHeaderContent.NAME, currentUserId);
        messageSender.send(MessageTypeContent.STUDIO_COURSE_OFF_SHELVES, MessageHeaderContent.ID, courseId);

    }

    @Override
    public List<CourseChapterSection> findCourseChapterSectionList(String courseId, String versionId) {
        List<CourseChapterSection> sectionList = courseChapterSectionCommonDao
                .execute(d -> d.select(COURSE_CHAPTER_SECTION.fields()).from(COURSE_INFO).innerJoin(COURSE_CHAPTER)
                        .on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_INFO.ID.eq(courseId))
                                .and(COURSE_CHAPTER.VERSION_ID.eq(versionId)))
                        .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                        .where(COURSE_INFO.ID.eq(courseId)).fetchInto(CourseChapterSection.class));
        return sectionList;
    }

    @Override
    public List<CoursePhoto> findPhotosBySubjectId(String subjectId) {
        return photoDao.fetch(COURSE_PHOTO.SUBJECT_ID.eq(subjectId)).stream().sorted((s1, s2) -> s1.getCreateTime().compareTo(s2.getCreateTime())).collect(Collectors.toList());
    }

    @Override
    public PagedResult<CoursePhoto> findPhotosPageBySubjectId(String subjectId, int pageNum, int pageSize) {
        return photoDao.execute(x -> {
            SelectSelectStep<Record> selectListField = x.selectDistinct(COURSE_PHOTO.fields()); // 查询list
            SelectSelectStep<Record> selectCountField = x
                    .select(Fields.start().add(COURSE_PHOTO.ID.countDistinct()).end()); // 查询总条数
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_PHOTO).where(COURSE_PHOTO.SUBJECT_ID.eq(subjectId));
                return select;
            };
            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);
            SelectConditionStep<Record> listStep = stepFunc.apply(selectListField);
            listStep.orderBy(COURSE_PHOTO.CREATE_TIME.desc());
            Result<Record> record = listStep.limit(pageNum - 1, pageSize).fetch();
            List<CoursePhoto> coursePhotoList = record.into(COURSE_PHOTO).into(CoursePhoto.class);
            return PagedResult.create(count, coursePhotoList);
        });
    }

    @Override
    public List<CoursePhoto> savePhotos(String subjectId, List<CoursePhoto> photos) {
        photoDao.delete(COURSE_PHOTO.SUBJECT_ID.eq(subjectId));
        return photos.stream().map(photo -> {
            photo.forInsert();
            photo.setSubjectId(subjectId);
            return photoDao.insert(photo);
        }).collect(Collectors.toList());
    }

    @Override
    public List<String> deletePhotos(List<String> ids) {
        photoDao.delete(ids);
        return ids;
    }

    @Override
    public CoursePhoto updatePhoto(String id, String name) {
        CoursePhoto photo = photoDao.get(id);
        photo.setName(name);
        return photoDao.update(photo);
    }

    @Override
    public PagedResult<CourseInfo> find(String currentUserId, Integer page, Integer pageSize, Optional<Integer> orderBy,
                                        Optional<Integer> order, Optional<String> searchContent,
                                        Optional<String> categoryId,
                                        Optional<String> topicId, Integer type, Optional<Integer> publishClient,
                                        Optional<Integer> companyType) {
        PagedResult<CourseInfo> pageList = courseInfoCommonDao.execute(context -> {
            final int defaultOrderBy = 0;
            final int defaultOrder = 2;
            //logger.info("page start step1");
            // 1 课程 2 专题
            int audienceType = type.intValue();
            if (audienceType == 0) {
                audienceType = 1;
            }

            List<Condition> conditions = Stream.of(
                    searchContent.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    categoryId.map(COURSE_CATEGORY.PATH::contains),
                    publishClient.map(p -> COURSE_INFO.PUBLISH_CLIENT.eq(p).or(COURSE_INFO.PUBLISH_CLIENT.eq(0)))
            )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            conditions.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()));
            conditions.add(COURSE_INFO.VERSION_ID.isNotNull());
            conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
            conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(type));
            conditions.add(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));
            conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
            conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(audienceType));
            conditions.add(COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE).or(COURSE_CATEGORY.STATE.isNull()));
            //logger.info("查询条件拼装完毕");
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select =
                        a.from(COURSE_INFO).leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
//                        .leftJoin(COURSE_STUDY_PROGRESS).on(COURSE_INFO.ID.eq(COURSE_STUDY_PROGRESS.COURSE_ID).and(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(currentUserId)))
                                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                                .where(conditions);

                if (topicId.isPresent()) {
                    select = select.andExists(context.select(BUSINESS_TOPIC.ID).from(BUSINESS_TOPIC)
                            .where(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID).and(BUSINESS_TOPIC.TOPIC_ID.eq(topicId.get()))));
                }
                if (companyType.isPresent() && companyType.get() != 0) {
                    // 计算公司类型
                    // 当前集团
                    Optional<String> l1 = organizationService.getLever(currentUserId, 2);
                    //当前公司
                    Optional<String> l2 = organizationService.getLever(currentUserId, 3);
                    l2 = l2.isPresent() ? l2 : l1;
                    // 其他公司(不在公司和集团)
                    List<String> list =
                            Stream.of(l1, l2).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
                    if (companyType.get().equals(1))
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l1.orElse("")));
                    if (companyType.get().equals(2))
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l2.orElse("")));
                    if (companyType.get().equals(3) && list.size() > 0) {
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.notIn(list));
                    }
                }
                return select;
            };
            SelectSelectStep<Record> listSelect = context.select(Fields.start()
                    .add(COURSE_INFO.ID)
                    .add(COURSE_INFO.NAME)
                    .add(COURSE_INFO.TYPE)
                    .add(COURSE_INFO.CREATE_TIME)
                    .add(COURSE_INFO.RELEASE_TIME)
                    .add(COURSE_INFO.SOURCE)
                    .add(COURSE_INFO.STATUS)
                    .add(COURSE_INFO.COVER)
                    .add(COURSE_INFO.COVER_PATH)
                    .add(COURSE_INFO.BEGIN_DATE)
                    .add(COURSE_INFO.END_DATE)
                    .add(COURSE_INFO.DESCRIPTION)
                    .add(COURSE_INFO.VISITS)
                    .add(COURSE_INFO.AVG_SCORE)
                    .add(COURSE_INFO.URL)
                    .add(COURSE_INFO.DESCRIPTION_TEXT)
                    .add(COURSE_INFO.INTEGRAL)
//                    .add(COURSE_STUDY_PROGRESS.FINISH_STATUS)
                    .end());
            SelectSelectStep<Record> countSelect =
                    context.select(Fields.start().add(COURSE_INFO.ID.countDistinct()).end());
            SelectSelectStep<Record> idsSelect = context.selectDistinct(Fields.start().add(COURSE_INFO.ID).end());

            SortField<?> sf = getSortField(orderBy.orElse(defaultOrderBy), order.orElse(defaultOrder));
            int firstResult = (page - 1) * pageSize;
            //logger.info("查询主体定义完毕");
            Integer count = stepFunc.apply(countSelect).fetchOne(0, Integer.class);
            //logger.info("查询总数完毕");
            Result<Record> idsResult = stepFunc.apply(idsSelect).orderBy(sf).limit(firstResult, pageSize).fetch();
            List<String> ids =
                    idsResult.into(CourseInfo.class).stream().map(x -> x.getId()).collect(Collectors.toList());
//            Result<Record> result = stepFunc.apply(listSelect).orderBy(sf).limit(firstResult, pageSize).fetch();
            //logger.info("查询列表完毕");
            List<CourseInfo> courseInfoList = listSelect.from(COURSE_INFO).where(COURSE_INFO.ID.in(ids)).orderBy(sf)
                    .fetch(iteam -> {
                        CourseInfo courseInfo = iteam.into(CourseInfo.class);
                        courseInfo.setIntegral(iteam.get(COURSE_INFO.INTEGRAL));
                        return courseInfo;
                    });
            //logger.info("序列化成list完毕");
            // result.
//            List<Integer> finishStatusList = result.getValues(COURSE_STUDY_PROGRESS.FINISH_STATUS);

//            IntStream.range(0, courseInfoList.size())
//                    .forEach(index -> courseInfoList.get(index).setFinishStatus(finishStatusList.get(index)));

            PagedResult<CourseInfo> r = PagedResult.create(count, courseInfoList);
            //logger.info("拼装pagedresult类完毕");
            return r;
        });
        //logger.info("总共耗时 "+ (System.currentTimeMillis() - beginTime));
        return pageList;
    }

    /**
     * 课程、专题学员端分页
     *
     * @param currentUserId
     * @param page
     * @param pageSize
     * @param orderBy
     * @param order
     * @param searchContent
     * @param categoryId
     * @param topicId
     * @param type
     * @param publishClient
     * @param companyType
     * @param internalSwitchStatus
     * @return
     */
    @Override
    @Transactional(readOnly = true)
    @DataSource(type = DataSourceEnum.SLAVE)
    public Map<String, Object> findMap(String currentUserId, Integer page, Integer pageSize,
                                       Optional<Integer> orderBy, Optional<Integer> order,
                                       Optional<String> searchContent,
                                       Optional<String> categoryId, Optional<String> topicId, Integer type,
                                       Optional<Integer> publishClient, Optional<Integer> companyType, boolean internalSwitchStatus,boolean pageSwitch,
                                       boolean cacheOrder) {

        // 缓存查询课程列表ids(key为：人-终端-类型-页)，只有默认页面才走缓存
        List<String> ids;
        if (!searchContent.isPresent() && !categoryId.isPresent() && !topicId.isPresent()
                && (orderBy.isPresent() && orderBy.get() == 0)
                && (!companyType.isPresent() || companyType.get() == 0) && internalSwitchStatus) {
            ids = courseIdsCache.get(currentUserId + "#" + publishClient+ "#" + type + "#" + page, () ->
//                            findIds4Cache(currentUserId, page, pageSize, orderBy, order, searchContent, categoryId, topicId, type, publishClient, companyType),
                    findIds4Sql(currentUserId,page,type),
                    60 * 5);
        } else {
            ids = findIds4Cache(currentUserId, page, pageSize, orderBy, order, searchContent, categoryId, topicId, type, publishClient, companyType,cacheOrder);
        }

        //是否还有下一页
        final int[] more = {0};
        if (com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(ids)) {
            if (pageSize.intValue() < ids.size()) {
                more[0] = 1;
                ids.remove(pageSize.intValue());
            }
        }
        List<CourseInfo> courseInfoList = findCourseInfoList(orderBy, order, ids);
        PagedResult<CourseInfo> pageList = PagedResult.create(0, courseInfoList);

        Long count = 0L;

        if(pageSwitch){
            count = countCourseIds(currentUserId, page, pageSize, orderBy, order, searchContent, categoryId, topicId, type, publishClient, companyType);
        }

        Map<String, Object> result = Maps.newHashMap();
        result.put("items", pageList.getItems());
        result.put("recordCount", count);
        result.put("more", more[0]);
        return result;
    }

    private List<CourseInfo> findCourseInfoList(Optional<Integer> orderBy, Optional<Integer> order, List<String> ids) {
        return courseInfoCommonDao.execute(context -> {
            int defaultOrderBy = 0;
            int defaultOrder = 2;
            SelectSelectStep<Record> listSelect = context.select(Fields.start()
                    .add(COURSE_INFO.ID)
                    .add(COURSE_INFO.NAME)
                    .add(COURSE_INFO.TYPE)
                    .add(COURSE_INFO.CREATE_TIME)
                    .add(COURSE_INFO.RELEASE_TIME)
                    .add(COURSE_INFO.SOURCE)
                    .add(COURSE_INFO.STATUS)
                    .add(COURSE_INFO.COVER)
                    .add(COURSE_INFO.COVER_PATH)
                    .add(COURSE_INFO.BEGIN_DATE)
                    .add(COURSE_INFO.END_DATE)
                    .add(COURSE_INFO.DESCRIPTION)
                    .add(COURSE_INFO.VISITS)
                    .add(COURSE_INFO.AVG_SCORE)
                    .add(COURSE_INFO.URL)
                    .add(COURSE_INFO.DESCRIPTION_TEXT)
                    .add(COURSE_INFO.INTEGRAL)
                    .end());

            SortField<?> sf = getSortField(orderBy.orElse(defaultOrderBy), order.orElse(defaultOrder));
            return listSelect.from(COURSE_INFO).where(COURSE_INFO.ID.in(ids)).orderBy(sf)
                    .fetch(item -> {
                        CourseInfo courseInfo = new CourseInfo();
                        courseInfo.setId(item.getValue(COURSE_INFO.ID));
                        courseInfo.setName(item.getValue(COURSE_INFO.NAME));
                        courseInfo.setType(item.getValue(COURSE_INFO.TYPE));
                        courseInfo.setCreateTime(item.getValue(COURSE_INFO.CREATE_TIME));
                        courseInfo.setReleaseTime(item.getValue(COURSE_INFO.RELEASE_TIME));
                        courseInfo.setSource(item.getValue(COURSE_INFO.SOURCE));
                        courseInfo.setStatus(item.getValue(COURSE_INFO.STATUS));
                        courseInfo.setCover(item.getValue(COURSE_INFO.COVER));
                        courseInfo.setCoverPath(generateSecurePathCdn(item.getValue(COURSE_INFO.COVER_PATH)));
                        courseInfo.setBeginDate(item.getValue(COURSE_INFO.BEGIN_DATE));
                        courseInfo.setEndDate(item.getValue(COURSE_INFO.END_DATE));
                        courseInfo.setDescription(item.getValue(COURSE_INFO.DESCRIPTION));
                        courseInfo.setVisits(item.getValue(COURSE_INFO.VISITS));
                        courseInfo.setAvgScore(item.getValue(COURSE_INFO.AVG_SCORE));
                        courseInfo.setUrl(item.getValue(COURSE_INFO.URL));
                        courseInfo.setDescriptionText(item.getValue(COURSE_INFO.DESCRIPTION_TEXT));
                        courseInfo.setIntegral(item.get(COURSE_INFO.INTEGRAL));
                        return courseInfo;
                    });

        });

    }

    private List<String> findIds4Sql(String currentUserId, Integer page, Integer type) {
        // 查询受众items
        List<Param<String>> itemIds = courseInfoCommonDao.execute(e -> e.selectDistinct(AUDIENCE_MEMBER.ITEM_ID)
                .from(AUDIENCE_MEMBER)
                .where(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                .fetch(AUDIENCE_MEMBER.ITEM_ID)).stream().map(DSL::val).collect(Collectors.toList());

        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(itemIds)) {
            return new ArrayList<>();
        }

        String sql = "SELECT course.f_id\n" +
        "FROM `course-study`.`t_course_info_txf` course\n" +
        "WHERE EXISTS (\n" +
                "SELECT f_id FROM `course-study`.`t_audience_object`\n" +
        "WHERE f_business_id=course.f_id\n" +
        "AND f_item_id IN ({0}) LIMIT 1)\n" +
                "AND course.f_business_type = {1}\n"+
        "ORDER BY course.f_release_time DESC\n" +
        "LIMIT {2},21" ;

        List<String> ids = courseInfoCommonDao.execute(dsl -> dsl.fetch(sql, DSL.list(itemIds), DSL.val(type),DSL.inline((page-1)*21)).intoMaps())
                .stream().map(x -> x.get("f_id").toString()).collect(toList());
        return ids;
    }

    /**
     * 查询当前用户的Item数据集合
     * 注：课程Or专题列表因为涉及业务高频访问，并发比较大，而Item表数据数量较庞大
     *        LeftJoin导致性能损耗较多，So根据用户过滤后，在原SQL执行In操作。
     *
     * @param currentUserId 当前登录用户
     * @return 课程Or专题列表的Item集合数据
     */
    private List<String> courseOrSubjectItem(String currentUserId){
        return courseInfoCommonDao.execute(e -> e.selectDistinct(AUDIENCE_MEMBER.ITEM_ID)
                .from(AUDIENCE_MEMBER)
                .where(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                .fetch(AUDIENCE_MEMBER.ITEM_ID));
    }

    private List<String> findIds4Cache(String currentUserId, Integer page, Integer pageSize, Optional<Integer> orderBy, Optional<Integer> order, Optional<String> searchContent, Optional<String> categoryId, Optional<String> topicId, Integer type, Optional<Integer> publishClient, Optional<Integer> companyType,boolean cacheOrder) {
        List<String> itemIdCollect = this.courseOrSubjectItem(currentUserId);
        return courseInfoCommonDao.execute(context -> {
            int defaultOrderBy = 0;
            int defaultOrder = 2;
            // 1 课程 2 专题
            int audienceType = type.intValue();
            if (audienceType == 0) {
                audienceType = 1;
            }
            List<Condition> conditions = Stream.of(
                    searchContent.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    categoryId.map(COURSE_CATEGORY.PATH::contains),
                    publishClient.map(p -> COURSE_INFO.PUBLISH_CLIENT.eq(p).or(COURSE_INFO.PUBLISH_CLIENT.eq(0)))
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            conditions.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()));
            conditions.add(COURSE_INFO.VERSION_ID.isNotNull());
            conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
            conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(type));
            conditions.add(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));
            conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(audienceType));
            conditions.add(AUDIENCE_OBJECT.ITEM_ID.in(itemIdCollect));
            conditions.add(COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE).or(COURSE_CATEGORY.STATE.isNull()));
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select =
                        a.from(COURSE_INFO)
                                .leftJoin(COURSE_INFO_CATEGORY).on(COURSE_INFO_CATEGORY.COURSE_INFO_ID.eq(COURSE_INFO.ID))
                                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO_CATEGORY.COURSE_CATEGORY_ID))
//                        .leftJoin(COURSE_STUDY_PROGRESS).on(COURSE_INFO.ID.eq(COURSE_STUDY_PROGRESS.COURSE_ID).and(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(currentUserId)))
                                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                                .where(conditions);
                if (topicId.isPresent()) {
                    select = select.andExists(context.select(BUSINESS_TOPIC.ID).from(BUSINESS_TOPIC)
                            .where(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID).and(BUSINESS_TOPIC.TOPIC_ID.eq(topicId.get()))));
                }
                if (companyType.isPresent() && companyType.get() != 0) {
                    // 计算公司类型
                    // 当前集团
                    Optional<String> l1 = organizationService.getLever(currentUserId, 2);
                    //当前公司
                    Optional<String> l2 = organizationService.getLever(currentUserId, 3);
                    l2 = l2.isPresent() ? l2 : l1;
                    // 其他公司(不在公司和集团)
                    List<String> list =
                            Stream.of(l1, l2).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
                    if (companyType.get().equals(1))
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l1.orElse("")));
                    if (companyType.get().equals(2))
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l2.orElse("")));
                    if (companyType.get().equals(3) && list.size() > 0) {
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.notIn(list));
                    }
                }
                return select;
            };

            SelectSelectStep<Record> idsSelect = context.selectDistinct(Fields.start().add(COURSE_INFO.ID).end());

            SortField<?> sf = getSortField(orderBy.orElse(defaultOrderBy), order.orElse(defaultOrder));
            int firstResult = (page - 1) * pageSize;
            Result<Record> idsResult;
            if(!cacheOrder){
                idsResult = stepFunc.apply(idsSelect).orderBy(sf).limit(firstResult, pageSize + 1).fetch();
            }else{
                idsResult = stepFunc.apply(idsSelect).limit(firstResult, pageSize + 1).fetch();
            }
            List<String> ids = idsResult.into(CourseInfo.class).stream().map(x -> x.getId()).collect(Collectors.toList());
            return ids;
        });
    }

    private Long countCourseIds(String currentUserId, Integer page, Integer pageSize, Optional<Integer> orderBy, Optional<Integer> order, Optional<String> searchContent, Optional<String> categoryId, Optional<String> topicId, Integer type, Optional<Integer> publishClient, Optional<Integer> companyType){
        List<String> itemIdCollect = this.courseOrSubjectItem(currentUserId);
        return courseInfoCommonDao.execute(context -> {
            int defaultOrderBy = 0;
            int defaultOrder = 2;
            // 1 课程 2 专题
            int audienceType = type.intValue();
            if (audienceType == 0) {
                audienceType = 1;
            }
            List<Condition> conditions = Stream.of(
                    searchContent.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    categoryId.map(COURSE_CATEGORY.PATH::contains),
                    publishClient.map(p -> COURSE_INFO.PUBLISH_CLIENT.eq(p).or(COURSE_INFO.PUBLISH_CLIENT.eq(0)))
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            conditions.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()));
            conditions.add(COURSE_INFO.VERSION_ID.isNotNull());
            conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
            conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(type));
            conditions.add(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));
            conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(audienceType));
            conditions.add(AUDIENCE_OBJECT.ITEM_ID.in(itemIdCollect));
            conditions.add(COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE).or(COURSE_CATEGORY.STATE.isNull()));
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select =
                        a.from(COURSE_INFO)
                                .leftJoin(COURSE_INFO_CATEGORY).on(COURSE_INFO_CATEGORY.COURSE_INFO_ID.eq(COURSE_INFO.ID))
                                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO_CATEGORY.COURSE_CATEGORY_ID))
                                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                                .where(conditions);
                if (topicId.isPresent()) {
                    select = select.andExists(context.select(BUSINESS_TOPIC.ID).from(BUSINESS_TOPIC)
                            .where(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID).and(BUSINESS_TOPIC.TOPIC_ID.eq(topicId.get()))));
                }
                if (companyType.isPresent() && companyType.get() != 0) {
                    // 计算公司类型
                    // 当前集团
                    Optional<String> l1 = organizationService.getLever(currentUserId, 2);
                    //当前公司
                    Optional<String> l2 = organizationService.getLever(currentUserId, 3);
                    l2 = l2.isPresent() ? l2 : l1;
                    // 其他公司(不在公司和集团)
                    List<String> list =
                            Stream.of(l1, l2).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
                    if (companyType.get().equals(1))
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l1.orElse("")));
                    if (companyType.get().equals(2))
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l2.orElse("")));
                    if (companyType.get().equals(3) && list.size() > 0) {
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.notIn(list));
                    }
                }
                return select;
            };
            SelectSelectStep<Record> idsSelect = context.select(Fields.start().add(COURSE_INFO.ID.countDistinct()).end());
            return stepFunc.apply(idsSelect).fetchOne().getValue(0, Long.class);
        });
    }

    @Override
    public void checkStudy(String id, String memberId) {
        courseInfoCommonDao.execute(dls ->
                dls.select(COURSE_INFO.VERSION_ID).from(COURSE_INFO).where(COURSE_INFO.ID.eq(id))
                        .fetchOptional(COURSE_INFO.VERSION_ID))
                .orElseThrow(() -> new UnprocessableException(ErrorCode.CourseInfoShelves));

        // update by wdy 课程已注册的用户扔需要进行受众过滤
        // 判断用户是否注册过
//        boolean isRegist = this.isRegist(id,memberId);
        // 判断用户是否注册过
        boolean isAudience = this.isAudience(id, memberId);
        if (!isAudience) {
            throw new UnprocessableException(ErrorCode.CourseNotAudience);
        }
    }

    @Override
    public void addVisits(String courseId, String memberId) {
        // 访问次数+1
//        return courseInfoCommonDao.execute(x -> x.update(COURSE_INFO).set(COURSE_INFO.VISITS, DSL.when(COURSE_INFO.VISITS.isNull(), 0).otherwise(COURSE_INFO.VISITS).add(1))
//                .where(COURSE_INFO.ID.eq(courseId)).execute());
        messageSender.send(MessageTypeContent.COURSE_STATISTICS_VISITS,
                MessageHeaderContent.ID, courseId, MessageHeaderContent.MEMBER_ID, memberId);
        messageSender.send(MessageTypeContent.COURSE_STATISTICS_STUDY_COUNT,
                MessageHeaderContent.ID, courseId, MessageHeaderContent.MEMBER_ID, memberId);
        // 专家工作室人气值统计
        messageSender.send(MessageTypeContent.BAR_STUDIO_POPULAR_COMPUTE, MessageHeaderContent.BUSINESS_ID, courseId);

    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public CourseInfo getFrontNew(String id, String memberId) {
        // 进入课程后设置当前课程信息,缓存10min,判断单窗口限制
//       String limitKey = String.join("_","limit",memberId);
//       cache.set(limitKey,memberId,10 * 60);

        CourseInfo course = courseInfoCommonDao.get(id);
        getFront(id, memberId, course);
        return course;
    }
    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public CourseInfo getFrontNew2(String id, String memberId, Integer from) {
        // 判断用户是否注册过
        CourseInfo course = courseInfoCommonDao.get(id);
        ErrorCode.CourseInfoShelves.throwIf(Objects.isNull(course));

        // 如果课程受众是非全员 或 （课程受众是内部组织但当前人不是内部人员）需要进一步校验受众
        Integer open = course.getOpen(); // 课程是否全员受众(全员受众, 走该字段：0=非全员、1=全员、2=内部人员)
        if (open == null || open == CourseInfo.OPEN_0  || (open == CourseInfo.OPEN_2 && !Objects.equals(from, Member.FROM_INSIDE))) {
            boolean isAudience = this.isAudience(id, memberId);
            if (!isAudience) {
                throw new UnprocessableException(ErrorCode.CourseNotAudience);
            }
        }

        getFront(id, memberId, course);

        return course;
    }

    private void getFront(String id, String memberId, CourseInfo course) {
        String courseVersion =  Optional.ofNullable(courseStudyProgressService.findCourseVersionIds(memberId, Collections.singletonList(id)).get(id)).orElse(course.getVersionId());
        // 查询章节和章节详情
        List<CourseChapter> courseChapters = courseCacheService.getCourseChapter(id, courseVersion);
        // 反腐倡廉优化 先给个0
        courseChapters.forEach(x->x.getCourseChapterSections().forEach(y->y.setNoteNumber(0)));


        setIntelligentBroadcast(courseChapters);
        course.setCourseChapters(courseChapters);
        // 查询附件
        course.setCourseAttachments(this.findCourseAttachmentByCourseId(id));
        // 查询发布部门
        if (course.getReleaseOrgId() != null)
            course.setReleaseOrg(orgDao.getOptional(course.getReleaseOrgId()).orElse(new Organization()));

        Organization execute = orgDao.execute(r ->
                r.select(ORGANIZATION.NAME, ORGANIZATION.ID).
                        from(ORGANIZATION).
                        where(ORGANIZATION.ID.eq(course.getOrganizationId())).
                        and(ORGANIZATION.STATUS.eq(Organization.ORGANIZATION_STATUS)).
                        fetchOne(o -> {
                            Organization organization = new Organization();
                            organization.setId(o.getValue(ORGANIZATION.ID));
                            organization.setName(o.getValue(ORGANIZATION.NAME));
                            return organization;
                        }));
        course.setOrganizationName(execute.getName());
        course.setOrganizationId(execute.getId());
        //查询归属部门虚拟空间
        //计算课程总时长
        List<CourseChapterSection> sectionList = new ArrayList<CourseChapterSection>();
        Integer time = 0;
        course.getCourseChapters().stream().filter(chapter -> chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()).forEach(c -> sectionList.addAll(c.getCourseChapterSections()));

        time = sectionList.stream()
                .filter(section -> section.getSectionType() != 8 && section.getSectionType() != 9 && section.getSectionType() != 13 && section.getSectionType() != 12)
                .map(s -> Optional.ofNullable(s.getTimeSecond()).orElse(0) + Optional.ofNullable(s.getTimeMinute()).orElse(0) * 60)
                .reduce(0, (a, b) -> a + b);
        String allCourseTime = calCourseTime(time);
        course.setAllCourseTime(allCourseTime);
    }

    @Override
    public CourseInfo getPerview(String id, String memberId) {
        CourseInfo course = courseInfoCommonDao.get(id);
        String courseVersion = course.getVersionId();
        // 查询章节和章节详情
        course.setCourseChapters(courseCacheService.getCourseChapter(id, courseVersion));
        // 查询附件
        course.setCourseAttachments(this.findCourseAttachmentByCourseId(id));
        // 查询发布部门
        if (course.getReleaseOrgId() != null)
            course.setReleaseOrg(orgDao.get(course.getReleaseOrgId()));
        //查询课程时长
        Integer time = 0;
        if (course.getReleaseOrgId() != null)
            course.setReleaseOrg(orgDao.get(course.getReleaseOrgId()));
        List<CourseChapterSection> sectionList = new ArrayList<CourseChapterSection>();
        course.getCourseChapters().stream().filter(chapter -> chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()).forEach(c -> sectionList.addAll(c.getCourseChapterSections()));

        time =
                sectionList.stream().filter(section -> section.getSectionType() != 8 && section.getSectionType() != 9 && section.getSectionType() != 12 && section.getSectionType() != 13).map(s -> s.getTimeSecond() + s.getTimeMinute() * 60).reduce(0, (a, b) -> a + b);
        String allCourseTime = calCourseTime(time);
        course.setAllCourseTime(allCourseTime);

        return course;
    }

    public boolean isRegist(String courseId, String memberId) {
        Integer registSize = courseInfoCommonDao.execute(dslContext ->
                dslContext.select(COURSE_STUDY_PROGRESS.ID.count())
                        .from(COURSE_STUDY_PROGRESS)
                        .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(courseId).and(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(memberId))))
                .fetchOne(COURSE_STUDY_PROGRESS.ID.count());
        return registSize > 0;
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    public boolean isAudience(String courseId, String memberId) {
        // 判断是否在受众范围内

        List<String> ids = courseInfoCommonDao.execute(dslContext ->
                        dslContext.select(AUDIENCE_MEMBER.ID).from(AUDIENCE_OBJECT).innerJoin(AUDIENCE_MEMBER)
                                .on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                                .where(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId)).and(AUDIENCE_OBJECT.BUSINESS_ID.eq(courseId))
                                .and(AUDIENCE_OBJECT.BUSINESS_TYPE.notEqual(AudienceObject.BUSINESS_TYPE_COURSE_STUDY_PLAN)))
                .limit(1)
                .fetch(AUDIENCE_MEMBER.ID);
        return com.alibaba.dubbo.common.utils.CollectionUtils.isNotEmpty(ids);

    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public List<CourseSectionStudyProgress> selectProgress(String memberId, List<String> ids) {
//    	return courseInfoCommonDao.execute(x -> x.select(Fields.start()
//    			.add(COURSE_SECTION_STUDY_PROGRESS.ID,COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID,COURSE_SECTION_STUDY_PROGRESS.COURSE_ID)
//    			.add(COURSE_SECTION_STUDY_PROGRESS.COMPLETED_RATE, COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS, COURSE_SECTION_STUDY_PROGRESS.SCORE)
//    			.add(COURSE_SECTION_STUDY_PROGRESS.LESSON_LOCATION, COURSE_SECTION_STUDY_PROGRESS.SECTION_ID)
//    			.end()).from(COURSE_SECTION_STUDY_PROGRESS)
//    			.where(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.in(ids).and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId)))
//    			.fetchInto(CourseSectionStudyProgress.class));
        List<CourseChapterSection> ccsList =
                courseChapterSectionCommonDao.fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID.eq(ids.get(0)));
        logger.error("查到的数据:{}", ccsList == null ? -1 : ccsList.size());
        if (ccsList != null && ccsList.size() > 0) {
            // update for xdn
/*            TableImpl<?> csspTable = shardingConfigService.getTableName(ccsList.get(0).getCourseId(),
                    ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
                    ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);*/
            TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, ccsList.get(0).getCourseId());
            logger.error("当前表:{}", csspTable.getName());
            Map<String, CourseSectionStudyProgress> csspMap = courseInfoCommonDao.execute(x -> x.select(Fields.start()
                    .add(csspTable.field("f_id"), csspTable.field("f_member_id"),
                            csspTable.field("f_course_id"))
                    .add(csspTable.field("f_completed_rate"), csspTable.field("f_finish_status"),
                            csspTable.field("f_score"))
                    .add(csspTable.field("f_lesson_location"), csspTable.field("f_section_id"))
                    .end()).from(csspTable)
                    .where(csspTable.field("f_member_id", String.class).eq(memberId))
                    .and(csspTable.field("f_course_id", String.class).eq(ccsList.get(0).getCourseId()))
                    .fetch(r -> {
                        CourseSectionStudyProgress cssp = new CourseSectionStudyProgress();
                        cssp.setId(r.get("f_id", String.class));
                        cssp.setMemberId(r.get("f_member_id", String.class));
                        cssp.setCourseId(r.get("f_course_id", String.class));
                        cssp.setCompletedRate(r.get("f_completed_rate", Integer.class));
                        cssp.setFinishStatus(r.get("f_finish_status", Integer.class));
                        cssp.setScore(r.get("f_score", Integer.class));
                        cssp.setLessonLocation(r.get("f_lesson_location", String.class));
                        cssp.setSectionId(r.get("f_section_id", String.class));
                        return cssp;
                    }))
                    .stream().collect(Collectors.toMap(CourseSectionStudyProgress::getSectionId, s -> s));
            List<CourseSectionStudyProgress> csspList = new ArrayList<CourseSectionStudyProgress>();
            ids.stream().forEach(x -> {
                if (csspMap.get(x) != null) {
                    csspList.add(csspMap.get(x));
                }
            });
            return csspList;
        }
        return null;
//    	return courseInfoCommonDao.execute(x ->
//                        x.select(COURSE_SECTION_STUDY_PROGRESS.fields()).from(COURSE_SECTION_STUDY_PROGRESS)
//                                .where(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.in(ids)).and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
//                                .fetchInto(CourseSectionStudyProgress.class)
//        );
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public List<CourseStudyProgress> selectCourseProgress(String memberId, List<String> ids) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);

        //todo 反腐暂时关闭查询归档当前记录
        //return getCourseStudyProgresses(memberId, ids, progress);
        return courseInfoCommonDao.execute(x ->

                x.select(cacheTable.field("f_course_id",String.class), cacheTable.field("f_finish_status",Integer.class)).from(cacheTable)
                        .where(cacheTable.field("f_course_id",String.class).in(ids).and(cacheTable.field("f_member_id",String.class).eq(memberId)))
                        .fetchInto(CourseStudyProgress.class));
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    public Map<String, Integer> selectCourseProgress2(String memberId, List<String> ids) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);

        return courseInfoCommonDao.execute(x ->
                x.select(cacheTable.field("f_course_id",String.class), cacheTable.field("f_finish_status",Integer.class)).from(cacheTable)
                        .where(cacheTable.field("f_course_id",String.class).in(ids).and(cacheTable.field("f_member_id",String.class).eq(memberId)))
                        .fetchMap(cacheTable.field("f_course_id",String.class),cacheTable.field("f_finish_status",Integer.class)));
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public Map<String, Integer> courseInfoProgressesCache2(String memberId, List<String> ids, Integer type, Optional<String> configString) {
        List<String> findIds = new ArrayList<>();
        Map<String, Boolean> map = Optional.of(checkExplicitLearningStatus2(type, ids, configString)).orElse(new HashMap<>());
        ids.forEach(id->{
            if (map.get(id)) {
                findIds.add(id);
            }
        });
        return selectCourseProgress2(memberId, findIds);
    }

    private ArrayList<CourseStudyProgress> getCourseStudyProgresses(String memberId, List<String> ids, List<CourseStudyProgress> progress) {
        // 判断是否被归档 当前记录
        Map<String, Integer> archivedStatus = courseStudyProgressArchivedService.getArchivedStatus(memberId, ids);
        archivedStatus.forEach((id,status)->{
            CourseStudyProgress courseStudyProgress = new CourseStudyProgress();
            courseStudyProgress.setCourseId(id);
            if (Objects.nonNull(status)&& (CourseStudyProgress.FINISH_STATUS_FINISH == status || CourseStudyProgress.FINISH_STATUS_MARKSUCCESS == status)){
                courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
            }else {
                courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_STUDY);
            }
            progress.add(courseStudyProgress);
        });

        return progress.stream().collect(
                collectingAndThen(
                        toCollection(() -> new TreeSet<>(Comparator.comparing(CourseStudyProgress::getCourseId))), ArrayList::new)
        );
    }

    @Override
    public CourseNote insertCourseNote(String memberId, String courseId, String content) {
        CourseNote courseNote = new CourseNote();
        courseNote.forInsert();
        courseNote.setMemberId(memberId);
        courseNote.setCourseId(courseId);
        courseNote.setContent(content);
        return courseNoteDao.insert(courseNote);
    }

    @Override
    public List<CourseNote> findCourseNotes(String memberId, String courseId) {
        return courseNoteDao.fetch(Stream.of(COURSE_NOTE.COURSE_ID.eq(courseId), COURSE_NOTE.MEMBER_ID.eq(memberId)),
                COURSE_NOTE.CREATE_TIME.desc());
    }

    /**
     * @param orderBy //0:上架时间 1:学习人数 2:评分
     * @param order   //1:asc 2:desc
     * @return
     */
    private SortField<?> getSortField(int orderBy, int order) {
        TableField<?, ?>[] fields = {COURSE_INFO.RELEASE_TIME, COURSE_INFO.VISITS, COURSE_INFO.AVG_SCORE};
        return order == 1 ? fields[orderBy].asc() : fields[orderBy].desc();

    }

    @Override
    public String deleteCourseNote(String id) {
        courseNoteDao.delete(id);

        messageSender.send(MessageTypeContent.COURSE_NOTE_DELETE, MessageHeaderContent.ID, id);
        return id;
    }

    @Override
    public CourseInfo updateStyles(String id, String styles) {
        CourseInfo courseInfo = courseInfoCommonDao.get(id);
        courseInfo.setStyles(styles);
        courseInfo.setModifyDate(null);
        return courseInfoCommonDao.update(courseInfo);
    }


    @Override
    public CourseNote updateCourseNote(String memberId, String id, String content) {
        CourseNote courseNote = courseNoteDao.get(id);
        courseNote.setContent(content);
        return courseNoteDao.update(courseNote);
    }

    @Override
    public Boolean judgeMemberHasAuthority(String courseId, String memberId) {
        int count = courseInfoCommonDao.execute(d -> {
            return d.fetchCount(d.select(COURSE_INFO.ID).from(COURSE_INFO).leftJoin(AUDIENCE_OBJECT)
                    .on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID)).leftJoin(AUDIENCE_ITEM)
                    .on(AUDIENCE_ITEM.ID.eq(AUDIENCE_OBJECT.ITEM_ID)).leftJoin(AUDIENCE_MEMBER)
                    .on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_ITEM.ID).and(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId)))
                    .where(COURSE_INFO.ID.eq(courseId)));
        });
        return count > 0;
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public List<CourseInfo> findByIds(List<String> ids) {
        SelectConditionStep<Record> step = courseInfoCommonDao.execute(e -> e.select(Fields.start()
                .add(COURSE_INFO)
                .add(ORGANIZATION.NAME.as("oname"), ORGANIZATION.ID.as("oid")).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .where(COURSE_INFO.ID.in(ids)));
        return step.fetch().map(r -> {
            CourseInfo courseInfo = r.into(CourseInfo.class);
            courseInfo.setCoverPath(generateSecurePathCdn(courseInfo.getCoverPath()));
            Organization organization = new Organization();
            organization.setId(r.getValue("oid", String.class));
            organization.setName(r.getValue("oname", String.class));
            courseInfo.setOrganization(organization);
            return courseInfo;
        });
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public List<CourseInfo> findByIds(List<String> ids, Boolean needDescription, Boolean needDescriptionApp, Boolean needDescriptionText) {
        Collection<Field<?>> fields = new ArrayList<>();
        fields.add(COURSE_INFO.ID);
        fields.add(COURSE_INFO.NAME);
        fields.add(COURSE_INFO.VISITS);
        fields.add(COURSE_INFO.BEGIN_DATE);
        fields.add(COURSE_INFO.END_DATE);
        fields.add(COURSE_INFO.COVER_PATH);
        fields.add(COURSE_INFO.COVER);
        fields.add(COURSE_INFO.URL);
        fields.add(COURSE_INFO.RELEASE_TIME);
        fields.add(COURSE_INFO.SOURCE);
        fields.add(COURSE_INFO.AVG_SCORE);
        fields.add(COURSE_INFO.INTEGRAL);

        if (needDescription) fields.add(COURSE_INFO.DESCRIPTION);
        if (needDescriptionApp) fields.add(COURSE_INFO.DESCRIPTION_APP);
        if (needDescriptionText) fields.add(COURSE_INFO.DESCRIPTION_TEXT);

        return courseInfoCommonDao.execute(e -> e.select(fields)
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.in(ids))).fetch(r -> {
            CourseInfo courseInfo = r.into(COURSE_INFO).into(CourseInfo.class);
            return courseInfo;
        });
    }

    @Override
    public List<CourseInfo> findByIds(List<String> ids, String memberId) {
        SelectHavingStep<Record> step = courseInfoCommonDao.execute(e -> e.select(Fields.start()
                .add(COURSE_INFO)
                .add(ORGANIZATION.NAME.as("oname"), ORGANIZATION.ID.as("oid")).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                .where(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId))
                .and(COURSE_INFO.ID.in(ids)))
                .groupBy(COURSE_INFO.ID);
        return step.fetch().map(r -> {
            CourseInfo courseInfo = r.into(COURSE_INFO).into(CourseInfo.class);
            Organization organization = new Organization();
            organization.setId(r.getValue("oid", String.class));
            organization.setName(r.getValue("oname", String.class));
            courseInfo.setOrganization(organization);
            return courseInfo;
        });
    }

    @Override
    public PagedResult<Member> findLastestUser(String memberId, String courseId, Integer page, Integer pageSize) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return memberDao.execute(d -> {
            SelectOnConditionStep<Record> step = d
                    .selectDistinct(Fields.start().add(MEMBER).add(cacheTable.field("f_last_access_time",Long.class)).end())
                    .from(cacheTable).innerJoin(MEMBER)
                    .on(cacheTable.field("f_member_id",String.class).eq(MEMBER.ID).and(cacheTable.field("f_member_id",String.class).eq(memberId))
                            .and(cacheTable.field("f_course_id",String.class).eq(courseId)));

            int firstResult = (page - 1) * pageSize;
            Integer count = d.fetchCount(step);

            List<Member> memberList = step.orderBy(cacheTable.field("f_last_access_time",Long.class).desc())
                    .limit(firstResult, pageSize).fetchInto(Member.class);
            return PagedResult.create(count, memberList);
        });
    }

    @Override
    public Integer insertCourseScore(String memberId, String businessId, Integer businessType, Integer score) {
        // 如果已经评分了,抛出异常
        courseScoreDao.fetchOne(COURSE_SCORE.MEMBER_ID.eq(memberId).and(COURSE_SCORE.BUSINESS_ID.eq(businessId))
                .and(COURSE_SCORE.BUSINESS_TYPE.eq(businessType))).ifPresent(s -> {
            throw new UnprocessableException(com.zxy.product.course.content.ErrorCode.AlreadyScore);
        });
        // 评分
        CourseScore courseScore = new CourseScore();
        courseScore.forInsert();
        courseScore.setBusinessId(businessId);
        courseScore.setBusinessType(businessType);
        courseScore.setMemberId(memberId);
        courseScore.setScore(score);
        courseScoreDao.insert(courseScore);

        // 如果是课程和专题,更新他们的评分
        Integer avgScore = 0;
        if (CourseScore.BUSINESS_TYPE_COURSE.equals(businessType)
                || CourseScore.BUSINESS_TYPE_SUBJECT.equals(businessType)) {
            CourseInfo courseInfo = courseInfoCommonDao.get(businessId);
            Integer totalScore = courseInfo.getTotalScore() == null ? 0 : courseInfo.getTotalScore();
            Integer scoreMemberCount = courseInfo.getScoreMemberCount() == null ? 0 : courseInfo.getScoreMemberCount();

            totalScore = totalScore + score;
            scoreMemberCount = scoreMemberCount + 1;
            avgScore = Double.valueOf(Math.ceil(totalScore * 10 / scoreMemberCount)).intValue();

            courseInfo.setTotalScore(totalScore);
            courseInfo.setScoreMemberCount(scoreMemberCount);
            courseInfo.setAvgScore(avgScore);

            courseScore.setAvgScore(avgScore);

            courseInfo.setModifyDate(null);
            courseInfoCommonDao.update(courseInfo);
        }
        //如果是直播
        if (CourseScore.BUSINESS_TYPE_GENSEE.equals(businessType)) {
            GenseeWebCast genseeWebCast = genseeWebCastDao.get(businessId);
            Integer totalScore = genseeWebCast.getTotalScore() == null ? 0 : genseeWebCast.getTotalScore();
            Integer scoreMemberCount = genseeWebCast.getScoreMemberCount() == null ? 0 :
                    genseeWebCast.getScoreMemberCount();
            totalScore = totalScore + score;
            scoreMemberCount = scoreMemberCount + 1;
            avgScore = Double.valueOf(Math.ceil(totalScore * 10 / scoreMemberCount)).intValue();

            genseeWebCast.setTotalScore(totalScore);
            genseeWebCast.setScoreMemberCount(scoreMemberCount);
            genseeWebCast.setAvgScore(avgScore);

            courseScore.setAvgScore(avgScore);
            genseeWebCast.setModifyDate(null);
            genseeWebCastDao.update(genseeWebCast);
            //更新直播平均分消息推送  用于人工智能数据同步
            messageSender.send(MessageTypeContent.GESEE_SCORE_UPDATE,
                    MessageHeaderContent.ID, businessId);
        }

        return avgScore;
    }


    @Override
    public boolean judgeCourseNameIsExists(String courseName) {
        int count = courseInfoCommonDao
                .count(COURSE_INFO.NAME.eq(courseName).and(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO)));

        if (count > 0) {
            throw new UnprocessableException(com.zxy.product.course.content.ErrorCode.NameAlreadyExists);
        }
        return true;
    }

    /**
     * 生成课程编码 日期(例：170220)+部门编码(例：888)+三位自动增长序列号(001) 例:170220888001
     *
     * @param course
     * @return
     */

    private synchronized void updateCourseCode(CourseInfo course) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        String prelude = dateFormat.format(new Date()); // 初始化编码前缀
        Organization organization = orgDao.get(course.getOrganizationId());
        String orgCode = organization.getCode() == null ? "000" : organization.getCode(); // 组织编码

        Optional<CourseSequence> csOptional = seqDao.fetchOne(COURSE_SEQUENCE.ORGANIZATION_ID
                .eq(course.getOrganizationId()).and(COURSE_SEQUENCE.BUSINESS_TYPE.eq(course.getBusinessType()))
                .and(COURSE_SEQUENCE.PRELUDE.eq(prelude)));

        int newSeq = 1;
        // 无则新增，有则更新，每天同一业务类型同一部门下只生成一条序列记录
        if (csOptional.isPresent()) {
            CourseSequence cs = csOptional.get();
            newSeq = cs.getSequence() + 1;
            cs.setSequence(newSeq);
            seqDao.update(cs);
        } else {
            // 初始化课程编码序列信息
            CourseSequence courseSequence = new CourseSequence();
            courseSequence.setSequence(1);
            courseSequence.setBusinessType(course.getBusinessType());
            courseSequence.setOrganizationId(course.getOrganizationId());
            courseSequence.setPrelude(prelude);
            courseSequence.forInsert();
            seqDao.insert(courseSequence);
        }
        String code = prelude + orgCode + String.format("%03d", newSeq);
        courseInfoCommonDao.execute(dao -> dao.update(COURSE_INFO).set(COURSE_INFO.CODE, code)
                .where(COURSE_INFO.ID.eq(course.getId())).execute());
    }

    /**
     * 查询相关课程/专题
     *
     * @param page
     * @param pageSize
     * @param businessType
     * @param currentUserId
     * @param courseId
     * @param categoryId
     * @param topicIds
     * @return
     */
    @Override
    public List<CourseInfo> getRelated(int page, int pageSize, Integer businessType, String currentUserId,
                                       String[] topicIds, Optional<String> categoryId, Optional<String> courseId,
                                       Optional<Integer> publishClient) {
        String id = courseId.orElse("courseId");
        return cache.get(CacheKeyConstant.RELATED_COURSE_OR_SUBJECT_BY_TOPICIDS + "-" + id, () -> {
            return courseInfoCommonDao.execute(context -> {
                int audienceBusinessType = AudienceObject.BUSINESS_TYPE_COURSE;
                if (businessType != null && businessType.equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
                    audienceBusinessType = AudienceObject.BUSINESS_TYPE_SUBJECT;
                }
//            int courseBusinessType = CourseInfo.BUSINESS_TYPE_COURSE;
//            if (businessType != null && businessType.equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
//                courseBusinessType = CourseInfo.BUSINESS_TYPE_SUBJECT;
//            }
                // updated by wdy 20190926 获取受众为中国移动以及内部组织的受众项
                List<String> itemIds = courseCacheService.getCacheAudienceItemIds();

                Condition conditions = Stream.of(
//                    Optional.of(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId)),
                        Optional.of(COURSE_INFO.STATUS.in(CourseInfo.STATUS_SHELVES,
                                CourseInfo.STATUS_THE_TEST)),
                        Optional.of(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(audienceBusinessType)),
                        Optional.of(AUDIENCE_OBJECT.ITEM_ID.in(itemIds)),
                        Optional.of(BUSINESS_TOPIC.TOPIC_ID.in(topicIds)),
                        categoryId.map(s -> {
                            List<String> ids = courseCategoryService.findChildrenIdsById(s, Boolean.TRUE);
                            return COURSE_INFO.CATEGORY_ID.in(ids);
                        }),
                        courseId.map(COURSE_INFO.ID::ne),
                        publishClient.map(c -> COURSE_INFO.PUBLISH_CLIENT.in(c,
                                CourseInfo.PUBLISH_TYPE_MEMBER)))
                        .filter(Optional::isPresent).map(Optional::get)
                        .reduce((a, b) -> a.and(b)).get();

                Function<SelectSelectStep<Record>, SelectConditionStep<Record>> process =
                        x -> x.from(COURSE_INFO).innerJoin(BUSINESS_TOPIC).on(COURSE_INFO.ID.eq(BUSINESS_TOPIC.BUSINESS_ID))
                                .innerJoin(AUDIENCE_OBJECT).on(COURSE_INFO.ID.eq(AUDIENCE_OBJECT.BUSINESS_ID))
//                    .innerJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                                .where(conditions)
                                .and(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));


                SelectSelectStep<Record> listSelect = context.selectDistinct(Fields.start()
                        .add(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.COVER, COURSE_INFO.COVER_PATH,
                                COURSE_INFO.CREATE_TIME, COURSE_INFO.URL).end());
//            int firstResult = (page - 1) * pageSize;
                List<CourseInfo> courseInfoList =
                        process.apply(listSelect).fetchInto(CourseInfo.class);
//            		process.apply(listSelect).limit(firstResult, pageSize).fetchInto(CourseInfo.class);
                return courseInfoList;
            });
        }, DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
    }

    @Override
    public List<CourseInfo> getRelatedPage(int page, int pageSize, Integer businessType, String currentUserId,
                                           String[] topicIds, Optional<String> categoryId, Optional<String> courseId,
                                           Optional<Integer> publishClient) {
        String id = courseId.orElse("courseId");
        return cache.get(CacheKeyConstant.RELATED_COURSE_OR_SUBJECT_BY_TOPICIDS + "-" + page + "-" + id, () -> {
            return courseInfoCommonDao.execute(context -> {
                int audienceBusinessType = AudienceObject.BUSINESS_TYPE_COURSE;
                if (businessType != null && businessType.equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
                    audienceBusinessType = AudienceObject.BUSINESS_TYPE_SUBJECT;
                }

                // updated by wdy 20190926 获取受众为中国移动以及内部组织的受众项
                List<String> itemIds = courseCacheService.getCacheAudienceItemIds();


                Condition conditions = Stream.of(
//                    Optional.of(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId)),
                        Optional.of(COURSE_INFO.STATUS.in(CourseInfo.STATUS_SHELVES,
                                CourseInfo.STATUS_THE_TEST)),
                        Optional.of(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(audienceBusinessType)),
                        Optional.of(AUDIENCE_OBJECT.ITEM_ID.in(itemIds)),
                        Optional.of(BUSINESS_TOPIC.TOPIC_ID.in(topicIds)),
                        categoryId.map(s -> {
                            List<String> ids = courseCategoryService.findChildrenIdsById(s, Boolean.TRUE);
                            return COURSE_INFO.CATEGORY_ID.in(ids);
                        }),
                        courseId.map(COURSE_INFO.ID::ne),
                        publishClient.map(c -> COURSE_INFO.PUBLISH_CLIENT.in(c,
                                CourseInfo.PUBLISH_TYPE_MEMBER)))
                        .filter(Optional::isPresent).map(Optional::get)
                        .reduce((a, b) -> a.and(b)).get();

                Function<SelectSelectStep<Record>, SelectConditionStep<Record>> process =
                        x -> x.from(COURSE_INFO).innerJoin(BUSINESS_TOPIC).on(COURSE_INFO.ID.eq(BUSINESS_TOPIC.BUSINESS_ID))
                                .innerJoin(AUDIENCE_OBJECT).on(COURSE_INFO.ID.eq(AUDIENCE_OBJECT.BUSINESS_ID))
//                    .innerJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                                .where(conditions)
                                .and(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));


                SelectSelectStep<Record> listSelect = context.selectDistinct(Fields.start()
                        .add(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.COVER, COURSE_INFO.COVER_PATH,
                                COURSE_INFO.CREATE_TIME, COURSE_INFO.URL, COURSE_INFO.STUDY_MEMBER_COUNT).end());
                int firstResult = (page - 1) * pageSize;
                List<CourseInfo> courseInfoList =
                        process.apply(listSelect).limit(firstResult, pageSize).fetchInto(CourseInfo.class);
                return courseInfoList;
            });
        }, DateUtil.timeLeftInSeconds(System.currentTimeMillis()));
    }

    /**
     * 保存专题文字区域
     *
     * @param subjectId
     * @param textAreas
     */
    private void saveTextArea(String subjectId, List<SubjectTextArea> textAreas) {
        textAreaDao.delete(SUBJECT_TEXT_AREA.SUBJECT_ID.eq(subjectId));
        textAreas.stream().forEach(obj -> {
            obj.forInsert();
            obj.setSubjectId(subjectId);
            textAreaDao.insert(obj);
        });
    }

    /**
     * 个人中心首页推荐
     */
    @Override
    public PagedResult<CourseInfo> personIndex(Integer page, Integer pageSize, String currentUserId,
                                               List<String> topicIds) {
        // 添加缓存，缓存一天
        return cache.get(CacheKeyConstant.GUESS_INTERESTED_COURSE_AND_SUBJECT + "-" + currentUserId + "-" + page, () -> {
            //注册了的课程
            // updated by wdy 20190926 猜你喜欢只查询受众全员以及内部组织的课程或者专题
//        List<String> registerList = courseInfoCommonDao.execute(e -> e.selectDistinct(
//                Fields.start().add(COURSE_REGISTER.COURSE_ID).end())
//                .from(COURSE_REGISTER).where(COURSE_REGISTER.MEMBER_ID.eq(currentUserId))).fetch(r -> {
//            return r.getValue(COURSE_REGISTER.COURSE_ID);
//        });
            //查询全员受众以及内部组织的itemIds
            List<String> itemIds = courseCacheService.getCacheAudienceItemIds();
            SelectConditionStep<Record> step = courseInfoCommonDao.execute(e -> e
                            .selectDistinct(Fields.start().add(COURSE_INFO.ID).add(COURSE_INFO.NAME).add(COURSE_INFO.COVER_PATH)
                                    .add(COURSE_INFO.COVER).add(COURSE_INFO.BUSINESS_TYPE).end()).from(COURSE_INFO)
                            .leftJoin(BUSINESS_TOPIC).on(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID))
                            .innerJoin(AUDIENCE_OBJECT).on(COURSE_INFO.ID.eq(AUDIENCE_OBJECT.BUSINESS_ID))
//                .innerJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                            .where(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()))
                            .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                            .and(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)))
                            .and(AUDIENCE_OBJECT.ITEM_ID.in(itemIds))
//                        .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                            .and(Optional.of(BUSINESS_TOPIC.TOPIC_ID.in(topicIds)).orElse(DSL.trueCondition()))
//                        .and(COURSE_INFO.ID.notIn(registerList))
//                .and(DSL.notExists(e.select(COURSE_STUDY_PROGRESS.COURSE_ID).from(COURSE_STUDY_PROGRESS).where(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(currentUserId).and(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID)))))
            );

            int firstResult = (page - 1) * pageSize;
            Integer count = courseInfoCommonDao.execute(e -> e.fetchCount(step));
            List<CourseInfo> items = step.limit(firstResult, pageSize).fetch(r -> {
                CourseInfo courseInfo = r.into(CourseInfo.class);
                return courseInfo;
            });
            return PagedResult.create(count, items);
        }, 24 * 60 * 60);
    }

    @Override
    public Optional<CourseShelves> getShelves(String id) {
        return shelvesDao.getOptional(id);
    }

    /**
     * 同步知识库
     *
     * @param courseAttachments
     * @param course
     */
    private void syncKnowledge(List<CourseAttachment> courseAttachments, CourseInfo course) {
        if (courseAttachments != null && courseAttachments.size() > 0) {
            // 同步知识库
            String type = KnowledgeInfo.BUSINESS_TYPE_COURSE + "";
            if (course.getBusinessType() != null && course.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_SUBJECT)) {
                type = KnowledgeInfo.BUSINESS_TYPE_SUBJECT + "";
            }

            messageSender.send(MessageTypeContent.KNOWLEDGE_INFO_SYNC, MessageHeaderContent.ID, course.getId(),
                    MessageHeaderContent.BUSINESS_TYPE, type);
        }
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public List<String> hotTopicIds() {
        return courseInfoCommonDao.execute(x ->
                x.select(BUSINESS_TOPIC.TOPIC_ID).from(BUSINESS_TOPIC)
                        .leftJoin(TOPIC).on(BUSINESS_TOPIC.TOPIC_ID.eq(TOPIC.ID))
                        .where(BUSINESS_TOPIC.BUSINESS_TYPE.eq(BusinessTopic.BUSINESS_TYPE_COURSE))
                        .and(TOPIC.GROUP.eq(Topic.GROUP_STANDARD))
                        .groupBy(BUSINESS_TOPIC.TOPIC_ID)
                        .orderBy(BUSINESS_TOPIC.TOPIC_ID.count().desc())
                        .limit(8).fetch(BUSINESS_TOPIC.TOPIC_ID)
        );
    }

    /**
     * 如果是首次上架，给参考Id赋值
     *
     * @param courseChapters
     */
    private void addReferenceId(List<CourseChapter> courseChapters) {
        courseChapters.forEach(courseChapter -> {
            if (courseChapter.getCourseChapterSections() != null) {
                courseChapter.getCourseChapterSections().stream().forEach(x -> {
                    if (StringUtils.isEmpty(x.getReferenceId())) {
                        x.setReferenceId(x.getId());
                        x.setModifyDate(null);
                        courseChapterSectionCommonDao.update(x);
                    }
                });
            }
        });
    }

    @Override
    public PagedResult<CourseInfo> selectForTrain(Integer pageNum, Integer pageSize, String rootId,
                                                  Optional<String> companyId, Optional<String> organizationId,
                                                  Optional<String> categoryId, Optional<String> name,
                                                  Optional<String> selectedIds) {
        List<Condition> where = new ArrayList<>();
        Condition condition = COURSE_INFO.ORGANIZATION_ID.eq(rootId);
        where.add(companyId.map(
                cid -> condition.or(ORGANIZATION_DETAIL.ROOT.eq(cid))
        ).orElse(condition));

        categoryId.map(caid -> where.add(COURSE_CATEGORY.PATH.contains(caid)));
        name.map(n -> where.add(COURSE_INFO.NAME.contains(n)));

        where.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));
        where.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE));

        organizationId.map(oid -> where.add(ORGANIZATION_DETAIL.ROOT.eq(oid)));

        // 添加已选择在线课程的id
        selectedIds.map(ids -> where.add(COURSE_INFO.ID.notIn(ids.split(","))));

        int count = courseInfoCommonDao.execute(dslContext ->
                dslContext.select(COURSE_INFO.ID.count()).from(COURSE_INFO)
                        .leftJoin(ORGANIZATION_DETAIL).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB))
                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .where(where)).fetchOne(COURSE_INFO.ID.count());

        SelectForUpdateStep<Record> record = courseInfoCommonDao.execute(dslContext ->
                dslContext.selectDistinct(Fields.start()
                        .add(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.PUBLISH_CLIENT)
                        .add(COURSE_CATEGORY.NAME, COURSE_CATEGORY.ID)
                        .add(ORGANIZATION.NAME, ORGANIZATION.ID).end()).
                        from(COURSE_INFO)
                        .leftJoin(ORGANIZATION_DETAIL).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION_DETAIL.SUB))
                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .where(where)
                        .orderBy(COURSE_INFO.CREATE_TIME.desc())
                        .limit((pageNum - 1) * pageSize, pageSize)
        );

        List<CourseInfo> courseInfos = record.fetchInto(COURSE_INFO).into(CourseInfo.class);
        List<CourseCategory> courseCategories = record.fetchInto(COURSE_CATEGORY).into(CourseCategory.class);
        List<Organization> organizations = record.fetchInto(ORGANIZATION).into(Organization.class);
        IntStream.range(0, courseInfos.size()).forEach(i -> {
            courseInfos.get(i).setCategory(courseCategories.get(i));
            courseInfos.get(i).setOrganization(organizations.get(i));
        });
        return PagedResult.create(count, courseInfos);
    }

    @Override
    public Optional<CourseInfo> getCourseBasicByExamId(String examId) {
        return courseChapterSectionCommonDao.execute(dao -> dao.selectDistinct(COURSE_INFO.fields())
                .from(COURSE_CHAPTER_SECTION)
                .leftJoin(COURSE_INFO).on(COURSE_CHAPTER_SECTION.COURSE_ID.eq(COURSE_INFO.ID))
                .where(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(examId)
                        .and(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull())))
                .limit(1)
                .fetchOptional(r -> r.into(CourseInfo.class)));
    }

    @Override
    public List<CourseChapterSection> getChapterSectionBySectionType(String courseId, Optional<String> versionId,
                                                                     int sectionType) {
        return courseChapterSectionCommonDao.execute(dao -> dao.select(COURSE_CHAPTER_SECTION.fields())
                .from(COURSE_CHAPTER_SECTION)
                .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                .where(COURSE_CHAPTER.COURSE_ID.eq(courseId)
                        .and(versionId.map(COURSE_CHAPTER.VERSION_ID::eq).orElse(DSL.trueCondition()))
                        .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(sectionType)))
                .fetch(r -> r.into(CourseChapterSection.class)));
    }

    @Override
    public Map<String, Integer> getChapterSectionNumberByCourseIds(List<String> courseIds) {
        return courseChapterSectionCommonDao.execute(dslContext -> dslContext
                .select(Fields.start().add(COURSE_CHAPTER_SECTION.COURSE_ID)
                        .add(DSL.count(COURSE_CHAPTER_SECTION.ID)).end())
                .from(COURSE_CHAPTER_SECTION)
                .where(COURSE_CHAPTER_SECTION.COURSE_ID.in(courseIds))
                .groupBy(COURSE_CHAPTER_SECTION.COURSE_ID)
                .fetchMap(COURSE_CHAPTER_SECTION.COURSE_ID, DSL.count(COURSE_CHAPTER_SECTION.ID)));
    }

    @Override
    public CourseInfo getSubjectFrontAudience(Integer from, String id, String memberId) {
        List<Integer> open = courseInfoCommonDao.execute(dsl -> dsl.select(COURSE_INFO.OPEN)
                                                                   .from(COURSE_INFO)
                                                                   .where(COURSE_INFO.ID.eq(id))).fetch(COURSE_INFO.OPEN);


        // 如果当前课程课程是公开课（全员发布），则不查询受众，直接查询课程信息返回
        if (!CollectionUtils.isEmpty(open) && Objects.equals(open.get(0), CourseInfo.OPEN_1)) {
            return getCourseInfo(id, memberId);
        }

        // 如果当前课程发布范围是内部组织 && 当前学员也是内部人员，则不查询受众，直接查询课程信息返回
        if (!CollectionUtils.isEmpty(open) && Objects.equals(open.get(0), CourseInfo.OPEN_2) && Objects.equals(from, Member.FROM_INSIDE)) {
            return getCourseInfo(id, memberId);
        }
        // 否则，判断当前人是否在该课程的受众中
        if (isAudience(id, memberId)) {
            return getCourseInfo(id, memberId);
        } else {
            throw new UnprocessableException(ErrorCode.CourseNotAudience);
        }
    }

    private CourseInfo getCourseInfo(String id, String memberId) {
        return courseInfoCommonDao.execute(context -> context
                                          .selectDistinct(Fields.start().add(
                                                  COURSE_INFO.ID,
                                                  COURSE_INFO.BUSINESS_TYPE,
                                                  COURSE_INFO.URL,
                                                  COURSE_INFO.COVER,
                                                  COURSE_INFO.BEGIN_DATE,
                                                  COURSE_INFO.END_DATE,
                                                  COURSE_INFO.CODE,
                                                  COURSE_INFO.COVER_PATH,
                                                  COURSE_INFO.DESCRIPTION,
                                                  COURSE_INFO.DESCRIPTION_TEXT,
                                                  COURSE_INFO.NAME,
                                                  COURSE_INFO.ORGANIZATION_ID,
                                                  COURSE_INFO.PUBLISH_CLIENT,
                                                  COURSE_INFO.STUDY_DAYS,
                                                  COURSE_INFO.VERSION_ID,
                                                  COURSE_INFO.STYLES,
                                                  COURSE_INFO.SCORE_MEMBER_COUNT,
                                                  COURSE_INFO.AVG_SCORE,
                                                  COURSE_INFO.STATUS,
                                                  COURSE_INFO.REGISTER_MEMBER_COUNT,
                                                  COURSE_INFO.ADD_TYPE,
                                                  COURSE_INFO.DESCRIPTION_APP, // updated by wangdongyan 专题简介-app用到
                                                  COURSE_INFO.IS_SIGN,
                                                  COURSE_INFO.SWITCH_MENTOR,
                                                  COURSE_INFO.PC_BANNER_PATH,
                                                  COURSE_INFO.SKIN_TYPE,
                                                  COURSE_INFO.RELEASE_TIME
                                          ).end())
                                          .from(COURSE_INFO))
                                  .where(COURSE_INFO.ID.eq(id))
                                  .fetchOptional(r -> {
                                      CourseInfo course = new CourseInfo();
                                      course.setId(r.getValue(COURSE_INFO.ID));
                                      course.setBusinessType(r.getValue(COURSE_INFO.BUSINESS_TYPE));
                                      course.setUrl(r.getValue(COURSE_INFO.URL));
                                      course.setCover(r.getValue(COURSE_INFO.COVER));
                                      course.setBeginDate(r.getValue(COURSE_INFO.BEGIN_DATE));
                                      course.setEndDate(r.getValue(COURSE_INFO.END_DATE));
                                      course.setCode(r.getValue(COURSE_INFO.CODE));
                                      course.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH));
                                      course.setDescription(r.getValue(COURSE_INFO.DESCRIPTION));
                                      course.setDescriptionText(r.getValue(COURSE_INFO.DESCRIPTION_TEXT));
                                      course.setName(r.getValue(COURSE_INFO.NAME));
                                      course.setOrganizationId(r.getValue(COURSE_INFO.ORGANIZATION_ID));
                                      course.setPublishClient(r.getValue(COURSE_INFO.PUBLISH_CLIENT));
                                      course.setStudyDays(r.getValue(COURSE_INFO.STUDY_DAYS));
                                      course.setVersionId(r.getValue(COURSE_INFO.VERSION_ID));
                                      course.setStyles(r.getValue(COURSE_INFO.STYLES));
                                      course.setScoreMemberCount(r.getValue(COURSE_INFO.SCORE_MEMBER_COUNT));
                                      course.setAvgScore(r.getValue(COURSE_INFO.AVG_SCORE));
                                      course.setStatus(r.getValue(COURSE_INFO.STATUS));
                                      course.setRegisterMemberCount(r.getValue(COURSE_INFO.REGISTER_MEMBER_COUNT));
                                      course.setAddType(r.getValue(COURSE_INFO.ADD_TYPE));
                                      course.setDescriptionApp(r.getValue(COURSE_INFO.DESCRIPTION_APP));
                                      course.setIsSign(r.getValue(COURSE_INFO.IS_SIGN));
                                      course.setSwitchMentor(r.getValue(COURSE_INFO.SWITCH_MENTOR));
                                      course.setPcBannerPath(r.getValue(COURSE_INFO.PC_BANNER_PATH));
                                      course.setSkinType(r.getValue(COURSE_INFO.SKIN_TYPE));

                                      course.setPcBannerPath(r.getValue(COURSE_INFO.PC_BANNER_PATH));
                                      course.setSkinType(r.getValue(COURSE_INFO.SKIN_TYPE));
                                      course.setReleaseTime(r.getValue(COURSE_INFO.RELEASE_TIME));
                                      this.getSubjectDetail(course, memberId);
                                      return course;
                                  }).orElseThrow(() -> new UnprocessableException(ErrorCode.GetCourseInfoIsNull));
    }

    @Override
    public CourseInfo getSubjectFrontNoAudience(String courseId, String memberId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return courseInfoCommonDao.execute(context -> context
                .selectDistinct(Fields.start().add(
                        COURSE_INFO.ID,
                        COURSE_INFO.BUSINESS_TYPE,
                        COURSE_INFO.URL,
                        COURSE_INFO.COVER,
                        COURSE_INFO.BEGIN_DATE,
                        COURSE_INFO.END_DATE,
                        COURSE_INFO.CODE,
                        COURSE_INFO.COVER_PATH,
                        COURSE_INFO.DESCRIPTION,
                        COURSE_INFO.DESCRIPTION_TEXT,
                        COURSE_INFO.NAME,
                        COURSE_INFO.ORGANIZATION_ID,
                        COURSE_INFO.PUBLISH_CLIENT,
                        COURSE_INFO.STUDY_DAYS,
                        COURSE_INFO.VERSION_ID,
                        COURSE_INFO.STYLES,
                        COURSE_INFO.SCORE_MEMBER_COUNT,
                        COURSE_INFO.AVG_SCORE,
                        COURSE_INFO.STATUS,
                        COURSE_INFO.REGISTER_MEMBER_COUNT,
                        COURSE_INFO.ADD_TYPE,
                        COURSE_INFO.DESCRIPTION_APP,  // updated by wangdongyan 专题简介--app用
                        COURSE_INFO.IS_SIGN
                )
                        .add(
                                cacheTable.field("f_id",String.class),
                                cacheTable.field("f_create_time",Long.class),
                                cacheTable.field("f_study_total_time",Integer.class),
                                cacheTable.field("f_register_time",Long.class),
                                cacheTable.field("f_current_section_id",String.class)).end())
                .from(COURSE_INFO)
                .leftJoin(cacheTable).on(cacheTable.field("f_course_id",String.class).eq(COURSE_INFO.ID))
                .where(cacheTable.field("f_member_id",String.class).eq(memberId).and(COURSE_INFO.ID.eq(courseId).and(cacheTable.field("f_course_id",String.class).eq(courseId))))
                .limit(1)
                .fetchOptional(r -> {
                    CourseInfo course = r.into(COURSE_INFO).into(CourseInfo.class);
                    course.setStudyProgress(r.into(cacheTable).into(CourseStudyProgress.class));
                    this.getSubjectDetail(course, memberId);
                    return course;
                })).orElseThrow(() -> new UnprocessableException(com.zxy.product.course.content.ErrorCode.CourseInfoShelves));
    }

    /**
     * 查询专题其它信息
     *
     * @param course
     */
    private void getSubjectDetail(CourseInfo course, String memberId) {
        // 检查专题状态是否已发布
        if ((!new Integer(CourseInfo.STATUS_SHELVES).equals(course.getStatus()) &&
                !new Integer(CourseInfo.STATUS_THE_TEST).equals(course.getStatus())) ||
                StringUtils.isEmpty(course.getVersionId())) {
            throw new UnprocessableException(ErrorCode.CourseInfoShelves);
        }

        // 专题主题信息-包含学习进度
//        course.setCourseChapters(findCourseChapterForFront(course.getId(), memberId));
        // 专题附件
        course.setCourseAttachments(findCourseAttachmentByCourseId(course.getId()));
        // 专题标签
        course.setBusinessTopics(businessTopicService.findTopicByBusinessId(course.getId()));
        // 专题相册
        course.setPhotos(findPhotosBySubjectId(course.getId()));
        // 专题广告
        course.setAdvertisings(subjectAdvertisingService.findAdvertisingBySubjectId(course.getId()));
        // 专题文字
        course.setTextAreas(textAreaDao.fetch(SUBJECT_TEXT_AREA.SUBJECT_ID.eq(course.getId())));
        // 用户评分
        course.setCourseScore(courseScoreDao.fetchOneWithOptional(Stream.of(COURSE_SCORE.MEMBER_ID.eq(memberId),
                COURSE_SCORE.BUSINESS_ID.eq(course.getId())).map(c -> Optional.of(c))).orElse(null));
    }

    /**
     * 查询包含该课程的专题章节
     *
     * @param courseId
     * @return
     */
    private List<CourseChapterSection> getSubjectSectionBasicByCourseId(String courseId) {
        return courseChapterSectionCommonDao.execute(dao -> dao.selectDistinct(COURSE_CHAPTER_SECTION.fields())
                .from(COURSE_CHAPTER_SECTION)
                .where(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(courseId))
                .fetch(r -> r.into(CourseChapterSection.class)));
    }

    /**
     * 查询包含该课程的学习地图id
     *
     * @param courseId
     * @return
     */
    private List<String> getStudyMapBasicByCourseId(String courseId) {
        return Optional.ofNullable(courseChapterSectionCommonDao.execute(dao -> dao.selectDistinct(ABILITY_BUSINESS.ID)
                .from(ABILITY_BUSINESS)
                .where(ABILITY_BUSINESS.BUSINESS_ID.eq(courseId))
                .fetch(r -> r.getValue(ABILITY_BUSINESS.ID)))).orElse(new ArrayList<>());
    }


    @Override
    public List<CourseChapter> findSubjectChapterNoContainReturn(String courseId, String memberId, String versionId,
                                                                 boolean isRegister, List<String> domains) {
        // 首次注册查询章节进度时需要查询专题所包含的课程是否已经学过
        List<CourseChapter> courseChapterList = findCourseChapterByCourseId(courseId, Optional.ofNullable(versionId));
        List<RemodelingExternalPassbackBusiness> remodelingExternalPassbackBusinessList =
                findRemodelingExternalPassbackList(courseId, memberId);

        courseChapterList.forEach(r-> {
            List<CourseChapterSection> courseChapterSections = r.getCourseChapterSections();
            if(!CollectionUtils.isEmpty(courseChapterSections)){
                //过滤出类型为课程，切课程不为退库的数据
                List<CourseChapterSection> chapterSections = courseChapterSections.stream().filter(t -> Objects.isNull(t.getCourseInfo())
                        || (!Objects.equals(CourseInfo.STATUS_THE_SHELVES, t.getCourseInfo().getStatus()) && !Objects.equals(CourseInfo.STATUS_FIVE_SHELVES, t.getCourseInfo().getStatus())) ).collect(toList());
                //重新给数据赋值
                r.setCourseChapterSections(chapterSections);
            }

        });
        // 取出包含课程的节
        List<CourseChapterSection> chapterSectionList = getCourseSection(courseChapterList, domains,
                remodelingExternalPassbackBusinessList);
        if (!chapterSectionList.isEmpty()) {
            // 取出所有课程ids
            Optional<String> courseIds =
                    chapterSectionList.stream().map(CourseChapterSection::getResourceId).reduce((a, b) -> a + "," + b);
            // 获取所有课程学习进度
            TableImpl<?> table = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
            List<CourseStudyProgress> courseStudyProgressList = courseIds.map(ids -> progressDao.execute(x ->
                    x.select(table.field("f_is_required",Integer.class),
                                    table.field("f_course_id",String.class),
                                    table.field("f_member_id",String.class),
                                    table.field("f_begin_time",Long.class),
                                    table.field("f_finish_status",Integer.class),
                                    table.field("f_finish_time",Long.class),
                                    table.field("f_study_total_time",Integer.class),
                                    table.field("f_visits",Integer.class))
                            .from(table).where(table.field("f_course_id",String.class).in(ids.split(",")).and(table.field("f_member_id",String.class).eq(memberId))).fetch(r -> {
                                CourseStudyProgress p = new CourseStudyProgress();
                                p.setIsRequired(r.getValue(table.field("f_is_required",Integer.class)));
                                p.setCourseId(r.getValue(table.field("f_course_id",String.class)));
                                p.setMemberId(r.getValue(table.field("f_member_id",String.class)));
                                p.setBeginTime(r.getValue(table.field("f_begin_time",Long.class)));
                                p.setFinishStatus(r.getValue(table.field("f_finish_status",Integer.class)));
                                p.setFinishTime(r.getValue(table.field("f_finish_time",Long.class)));
                                p.setStudyTotalTime(r.getValue(table.field("f_study_total_time",Integer.class)));
                                p.setVisits(r.getValue(table.field("f_visits",Integer.class)));
                                return p;
                            }))).orElse(null);
            // archived  如果当前专题或者课程被归档了就不发消息更新后续的逻辑(子课程是否被归档了不管)
            if (courseStudyProgressList != null && !courseStudyProgressList.isEmpty()) {
                // 创建包含课程的专题节进度
                List<CourseSectionStudyProgress> sectionProgressList = createSubjectSectionProgress(courseId,
                        memberId, chapterSectionList, courseStudyProgressList);
                courseChapterList = getCourseChapters(courseChapterList, sectionProgressList);
            }
            //if(checkArchived(memberId, courseId)){
            AtomicReference<List<CourseStudyProgress>> archivedProgress = new AtomicReference<>();
            courseIds.ifPresent(ids ->
                    archivedProgress.set(
                            getCourseStudyProgresses(memberId,
                                    Arrays.asList(ids.split(",")),
                                    Optional.ofNullable(courseStudyProgressList).orElse(new ArrayList<>())
                            )
                    )
            );
            if (archivedProgress.get() != null && !archivedProgress.get().isEmpty()) {
                // 创建包含课程的专题节进度 归档的不发消息更新进度
                List<CourseSectionStudyProgress> sectionProgressList = createSubjectSectionProgress(courseId,
                        memberId, chapterSectionList, archivedProgress.get());
                courseChapterList = getCourseChapters(courseChapterList, sectionProgressList);
            }
        }
        if (!isRegister) {
            // 非首次注册专题需要查询专题中其他类型的章节的进度
            List<CourseSectionStudyProgress> sectionProgressByCourseId =
                    courseStudyProgressService.findSectionProgressByCourseId(courseId, memberId);
            if (!sectionProgressByCourseId.isEmpty()) {
                Map<String, CourseSectionStudyProgress> sectionProgressMap =
                        sectionProgressByCourseId.stream().collect(Collectors.toMap(CourseSectionStudyProgress::getSectionId
                                , r -> r));
                courseChapterList = courseChapterList.stream().map(chapter -> {
                    if (chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()) {
                        chapter.setCourseChapterSections(chapter.getCourseChapterSections().stream().map(section -> {
                            if (section.getSectionType() != null && !(section.getSectionType().equals(CourseChapterSection.SECTION_TYPE_COURSE) || section.getSectionType().equals(CourseChapterSection.SECTION_TYPE_SUBJECT))) {
                                if (sectionProgressMap.get(section.getReferenceId()) != null) {
                                    section.setProgress(sectionProgressMap.get(section.getReferenceId()));
                                }
                            }
                            return section;
                        }).collect(Collectors.toList()));
                    }
                    return chapter;
                }).collect(Collectors.toList());
            }

        }
        return courseChapterList;
//        }
//        return findChapterProgress(courseId, memberId);
    }


    @Override
    public List<CourseChapter> findSubjectChapterForFront(String courseId, String memberId, String versionId,
                                                          boolean isRegister, List<String> domains) {
        // 首次注册查询章节进度时需要查询专题所包含的课程是否已经学过
        List<CourseChapter> courseChapterList = findCourseChapterByCourseId(courseId, Optional.ofNullable(versionId));
        List<RemodelingExternalPassbackBusiness> remodelingExternalPassbackBusinessList =
                findRemodelingExternalPassbackList(courseId, memberId);
        // 取出包含课程的节
        List<CourseChapterSection> chapterSectionList = getCourseSection(courseChapterList, domains,
                remodelingExternalPassbackBusinessList);
        if (!chapterSectionList.isEmpty()) {
            // 取出所有课程ids
            Optional<String> courseIds =
                    chapterSectionList.stream().map(CourseChapterSection::getResourceId).reduce((a, b) -> a + "," + b);
            // 获取所有课程学习进度
            TableImpl<?> table = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
            List<CourseStudyProgress> courseStudyProgressList = courseIds.map(ids -> progressDao.execute(x ->
                    x.select(table.field("f_is_required",Integer.class),
                                    table.field("f_course_id",String.class),
                                    table.field("f_member_id",String.class),
                                    table.field("f_begin_time",Long.class),
                                    table.field("f_finish_status",Integer.class),
                                    table.field("f_finish_time",Long.class),
                                    table.field("f_study_total_time",Integer.class),
                                    table.field("f_visits",Integer.class))
                            .from(table).where(table.field("f_course_id",String.class).in(ids.split(",")).and(table.field("f_member_id",String.class).eq(memberId))).fetch(r -> {
                        CourseStudyProgress p = new CourseStudyProgress();
                        p.setIsRequired(r.getValue(table.field("f_is_required",Integer.class)));
                        p.setCourseId(r.getValue(table.field("f_course_id",String.class)));
                        p.setMemberId(r.getValue(table.field("f_member_id",String.class)));
                        p.setBeginTime(r.getValue(table.field("f_begin_time",Long.class)));
                        p.setFinishStatus(r.getValue(table.field("f_finish_status",Integer.class)));
                        p.setFinishTime(r.getValue(table.field("f_finish_time",Long.class)));
                        p.setStudyTotalTime(r.getValue(table.field("f_study_total_time",Integer.class)));
                        p.setVisits(r.getValue(table.field("f_visits",Integer.class)));
                        return p;
                    }))).orElse(null);
            // archived  如果当前专题或者课程被归档了就不发消息更新后续的逻辑(子课程是否被归档了不管)
            if (courseStudyProgressList != null && !courseStudyProgressList.isEmpty()) {
                // 创建包含课程的专题节进度
                List<CourseSectionStudyProgress> sectionProgressList = createSubjectSectionProgress(courseId,
                        memberId, chapterSectionList, courseStudyProgressList);
                courseChapterList = getCourseChapters(courseChapterList, sectionProgressList);
            }
            //if(checkArchived(memberId, courseId)){
            AtomicReference<List<CourseStudyProgress>> archivedProgress = new AtomicReference<>();
            courseIds.ifPresent(ids ->
                    archivedProgress.set(
                            getCourseStudyProgresses(memberId,
                                    Arrays.asList(ids.split(",")),
                                    Optional.ofNullable(courseStudyProgressList).orElse(new ArrayList<>())
                            )
                    )
            );
            if (archivedProgress.get() != null && !archivedProgress.get().isEmpty()) {
                // 创建包含课程的专题节进度 归档的不发消息更新进度
                List<CourseSectionStudyProgress> sectionProgressList = createSubjectSectionProgress(courseId,
                        memberId, chapterSectionList, archivedProgress.get());
                courseChapterList = getCourseChapters(courseChapterList, sectionProgressList);
            }
        }
        if (!isRegister) {
            // 非首次注册专题需要查询专题中其他类型的章节的进度
            List<CourseSectionStudyProgress> sectionProgressByCourseId =
                    courseStudyProgressService.findSectionProgressByCourseId(courseId, memberId);
            if (!sectionProgressByCourseId.isEmpty()) {
                Map<String, CourseSectionStudyProgress> sectionProgressMap =
                        sectionProgressByCourseId.stream().collect(Collectors.toMap(CourseSectionStudyProgress::getSectionId
                                , r -> r));
                courseChapterList = courseChapterList.stream().map(chapter -> {
                    if (chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()) {
                        chapter.setCourseChapterSections(chapter.getCourseChapterSections().stream().map(section -> {
                            if (section.getSectionType() != null && !(section.getSectionType().equals(CourseChapterSection.SECTION_TYPE_COURSE) || section.getSectionType().equals(CourseChapterSection.SECTION_TYPE_SUBJECT))) {
                                if (sectionProgressMap.get(section.getReferenceId()) != null) {
                                    section.setProgress(sectionProgressMap.get(section.getReferenceId()));
                                }
                            }
                            return section;
                        }).collect(Collectors.toList()));
                    }
                    return chapter;
                }).collect(Collectors.toList());
            }

        }
        return courseChapterList;
//        }
//        return findChapterProgress(courseId, memberId);
    }

    @Override
    public List<CourseChapter> findSubjectSimpleChapterForFront(String courseId, String memberId) {
        String versionId = courseInfoCommonDao.execute(dls ->
                        dls.select(COURSE_INFO.VERSION_ID).from(COURSE_INFO).where(COURSE_INFO.ID.eq(courseId))
                                .fetchOptional(COURSE_INFO.VERSION_ID))
                .orElseThrow(() -> new UnprocessableException(ErrorCode.CourseInfoShelves));

        // 首次注册查询章节进度时需要查询专题所包含的课程是否已经学过
        List<CourseChapter> courseChapterList = findCourseChapterByCourseId(courseId, Optional.ofNullable(versionId));
        // 取出包含课程的节
        List<CourseChapterSection> chapterSectionList = courseChapterList.stream().map(CourseChapter::getCourseChapterSections).filter(Objects::nonNull).flatMap(Collection::stream).collect(toList());
        // 取出所有课程ids
        Optional<String> courseIds = chapterSectionList.stream().map(CourseChapterSection::getResourceId).reduce((a, b) -> a + "," + b);
        // 获取所有课程学习进度
        TableImpl<?> table = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        List<CourseStudyProgress> courseStudyProgressList = courseIds.map(ids -> progressDao.execute(x ->
                x.select(
                                table.field("f_course_id", String.class),
                                table.field("f_member_id", String.class),
                                table.field("f_finish_status", Integer.class),
                                table.field("f_study_total_time", Integer.class))
                        .from(table).where(
                                table.field("f_course_id", String.class).in(ids.split(","))
                                        .and(table.field("f_member_id", String.class).eq(memberId))
                        ).fetch(r -> {
                            CourseStudyProgress p = new CourseStudyProgress();
                            p.setCourseId(r.getValue(table.field("f_course_id", String.class)));
                            p.setMemberId(r.getValue(table.field("f_member_id", String.class)));
                            p.setFinishStatus(r.getValue(table.field("f_finish_status", Integer.class)));
                            p.setStudyTotalTime(r.getValue(table.field("f_study_total_time", Integer.class)));
                            return p;
                        }))).orElse(null);

        courseIds.ifPresent(ids ->
                getCourseStudyProgresses(memberId,
                        Arrays.asList(ids.split(",")),
                        Optional.ofNullable(courseStudyProgressList).orElse(new ArrayList<>())
                )
        );
        Map<String, CourseStudyProgress> courseStudyProgressMap = new HashMap<>();
        if (courseStudyProgressList != null) {
            courseStudyProgressMap = courseStudyProgressList.stream().collect(toMap(CourseStudyProgress::getCourseId, r -> r));
        }

        // 非首次注册专题需要查询专题中其他类型的章节的进度
        List<CourseSectionStudyProgress> sectionProgressByCourseId = Optional.ofNullable(courseStudyProgressService.findSectionProgressByCourseId(courseId, memberId)).orElse(new ArrayList<>());
        Map<String, CourseSectionStudyProgress> sectionProgressMap = sectionProgressByCourseId.stream().collect(Collectors.toMap(CourseSectionStudyProgress::getSectionId, r -> r));
        AtomicReference<Map<String, Integer>> requiredSectionProgress = new AtomicReference<>(new HashMap<>());

        courseIds.map(ids -> {
            requiredSectionProgress.set(getRequiredSectionProgress(Arrays.asList(ids.split(",")), memberId));
            return null;
        });


        Map<String, CourseStudyProgress> finalCourseStudyProgressMap = courseStudyProgressMap;
        courseChapterList = courseChapterList.stream().peek(chapter -> {
            List<CourseChapterSection> courseChapterSections = chapter.getCourseChapterSections();
            if (courseChapterSections != null && !courseChapterSections.isEmpty()) {
                chapter.setCourseChapterSections(courseChapterSections.stream().peek(section -> {
                    Integer sectionType = section.getSectionType();
                    if (sectionType != null && !(sectionType.equals(CourseChapterSection.SECTION_TYPE_COURSE) || sectionType.equals(CourseChapterSection.SECTION_TYPE_SUBJECT))) {
                        if (sectionProgressMap.get(section.getReferenceId()) != null) {
                            section.setProgress(sectionProgressMap.get(section.getReferenceId()));
                        }
                    } else {
                        CourseStudyProgress courseStudyProgress = finalCourseStudyProgressMap.get(section.getResourceId());
                        if (courseStudyProgress != null) {
                            CourseSectionStudyProgress sectionProgress = new CourseSectionStudyProgress();
                            sectionProgress.setSectionId(section.getReferenceId());
                            sectionProgress.setFinishStatus(courseStudyProgress.getFinishStatus());
                            sectionProgress.setStudyTotalTime(courseStudyProgress.getStudyTotalTime());
                            sectionProgress.setProgressPercentage(requiredSectionProgress.get().get(section.getResourceId()));
                            section.setProgress(sectionProgress);
                        }

                    }
                }).collect(Collectors.toList()));
            }
        }).collect(Collectors.toList());

        return courseChapterList;
    }

    @Override
    public List<CourseChapter> findStudyMapChapterForFrontApp(String courseId,String memberId,List<String> domains,String chapterId) {
        Result<Record> record = courseChapterCommonDao.execute(x -> x
                .select(Fields.start().add(COURSE_CHAPTER).add(COURSE_CHAPTER_SECTION).end()).from(COURSE_CHAPTER)
                .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .where(COURSE_CHAPTER_SECTION.ID.eq(chapterId)).fetch());

        Map<String, List<CourseChapterSection>> sectionMap = record
                .into(CourseChapterSection.class).stream().filter(x -> x.getId() != null)
                .collect(groupingBy(CourseChapterSection::getChapterId));

        List<CourseChapter> courseChapterList = new ArrayList<>(record.into(CourseChapter.class)
                .stream().collect(toMap(CourseChapter::getId, p -> p, (p, q) -> q)).values());

        courseChapterList.forEach(x -> x.setCourseChapterSections(sectionMap.get(x.getId())));


//        List<RemodelingExternalPassbackBusiness> remodelingExternalPassbackBusinessList =
//                findRemodelingExternalPassbackList(courseId, memberId);
        // 取出包含课程的节
        List<CourseChapterSection> chapterSectionList = getCourseSection(courseChapterList, domains,
                null);
        if (!chapterSectionList.isEmpty()) {
            // 取出所有课程ids
            Optional<String> courseIds =
                    chapterSectionList.stream().map(CourseChapterSection::getResourceId).reduce((a, b) -> a + "," + b);
            // 获取所有课程学习进度
            // updated 2019-11-21 优化改为查询分表
            TableImpl<?> table = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
            List<CourseStudyProgress> courseStudyProgressList = courseIds.map(ids -> progressDao.execute(x ->
                    x.select(table.field("f_is_required",Integer.class),
                                    table.field("f_course_id",String.class),
                                    table.field("f_member_id",String.class),
                                    table.field("f_begin_time",Long.class),
                                    table.field("f_finish_status",Integer.class),
                                    table.field("f_finish_time",Long.class),
                                    table.field("f_study_total_time",Integer.class),
                                    table.field("f_visits",Integer.class))
                            .from(table).where(table.field("f_course_id",String.class).in(ids.split(",")).and(table.field("f_member_id",String.class).eq(memberId))).fetch(r -> {
                                CourseStudyProgress p = new CourseStudyProgress();
                                p.setIsRequired(r.getValue(table.field("f_is_required",Integer.class)));
                                p.setCourseId(r.getValue(table.field("f_course_id",String.class)));
                                p.setMemberId(r.getValue(table.field("f_member_id",String.class)));
                                p.setBeginTime(r.getValue(table.field("f_begin_time",Long.class)));
                                p.setFinishStatus(r.getValue(table.field("f_finish_status",Integer.class)));
                                p.setFinishTime(r.getValue(table.field("f_finish_time",Long.class)));
                                p.setStudyTotalTime(r.getValue(table.field("f_study_total_time",Integer.class)));
                                p.setVisits(r.getValue(table.field("f_visits",Integer.class)));
                                return p;
                            }))).orElse(null);
            // archived  如果当前专题或者课程被归档了就不发消息更新后续的逻辑(子课程是否被归档了不管)
            if (courseStudyProgressList != null && !courseStudyProgressList.isEmpty()) {
                // 创建包含课程的专题节进度
                List<CourseSectionStudyProgress> sectionProgressList = createSubjectSectionProgress(courseId,
                        memberId, chapterSectionList, courseStudyProgressList);
                courseChapterList = getCourseChapters(courseChapterList, sectionProgressList);
            }
            //if(checkArchived(memberId, courseId)){
            AtomicReference<List<CourseStudyProgress>> archivedProgress = new AtomicReference<>();
            courseIds.ifPresent(ids ->
                    archivedProgress.set(
                            getCourseStudyProgresses(memberId,
                                    Arrays.asList(ids.split(",")),
                                    Optional.ofNullable(courseStudyProgressList).orElse(new ArrayList<>())
                            )
                    )
            );
            if (archivedProgress.get() != null && !archivedProgress.get().isEmpty()) {
                // 创建包含课程的专题节进度 归档的不发消息更新进度
                List<CourseSectionStudyProgress> sectionProgressList = createSubjectSectionProgress(courseId,
                        memberId, chapterSectionList, archivedProgress.get());
                courseChapterList = getCourseChapters(courseChapterList, sectionProgressList);
            }
        }
        // 非首次注册专题需要查询专题中其他类型的章节的进度
        List<CourseSectionStudyProgress> sectionProgressByCourseId =
                courseStudyProgressService.findSectionProgressByCourseId(courseId, memberId);
        if (!sectionProgressByCourseId.isEmpty()) {
            Map<String, CourseSectionStudyProgress> sectionProgressMap =
                    sectionProgressByCourseId.stream().collect(Collectors.toMap(CourseSectionStudyProgress::getSectionId
                            , r -> r));
            courseChapterList = courseChapterList.stream().map(chapter -> {
                if (chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()) {
                    chapter.setCourseChapterSections(chapter.getCourseChapterSections().stream().map(section -> {
                        if (section.getSectionType() != null && !(section.getSectionType().equals(CourseChapterSection.SECTION_TYPE_COURSE) || section.getSectionType().equals(CourseChapterSection.SECTION_TYPE_SUBJECT))) {
                            if (sectionProgressMap.get(section.getReferenceId()) != null) {
                                section.setProgress(sectionProgressMap.get(section.getReferenceId()));
                            }
                        }
                        return section;
                    }).collect(Collectors.toList()));
                }
                return chapter;
            }).collect(Collectors.toList());
        }
        return courseChapterList;
    }

    private static List<CourseChapter> getCourseChapters(List<CourseChapter> courseChapterList, List<CourseSectionStudyProgress> sectionProgressList) {
        // 组装章节进度
        courseChapterList = courseChapterList.stream().map(chapter -> {
            if (chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()) {
                chapter.setCourseChapterSections(chapter.getCourseChapterSections().stream().map(section -> {
                    section.setProgress(sectionProgressList.stream().filter(pro -> pro.getSectionId().equals(section.getReferenceId())).findFirst().orElse(null));
                    if (CourseChapterSection.QUESTIONNAIRE_FLAG_YES.equals(section.getQuestionnaireFlag()) && CourseChapterSection.SECTION_TYPE_COURSE == section.getSectionType()
                            && section.getProgress() != null && (CourseStudyProgress.FINISH_STATUS_MARKSUCCESS == section.getProgress().getFinishStatus() || CourseStudyProgress.FINISH_STATUS_FINISH == section.getProgress().getFinishStatus())) {
                        section.setQuestionnaireButtonFlag(CourseChapterSection.QUESTIONNAIRE_BUTTON_FLAG_DISPLAY);
                    }
                    return section;
                }).collect(Collectors.toList()));
            }
            return chapter;
        }).collect(Collectors.toList());
        return courseChapterList;
    }

    /**
     * 根据课程id和用户id查询外部学习回传状态
     *
     * @param courseId 课程id
     * @param memberId 用户id
     * @return 外部学习回传状态列表
     */
    private List<RemodelingExternalPassbackBusiness> findRemodelingExternalPassbackList(String courseId,
                                                                                        String memberId) {
        return remodelingExternalPassbackBusinessCommonDao.fetch(REMODELING_EXTERNAL_PASSBACK_BUSINESS.COURSE_ID.eq(courseId)
                .and(REMODELING_EXTERNAL_PASSBACK_BUSINESS.MEMBER_ID.eq(memberId)
                        .and(REMODELING_EXTERNAL_PASSBACK_BUSINESS.EXTERNAL_STATUS.eq(RemodelingExternalPassbackBusiness.EXTERNAL_STATUS_YES))));
    }

    /**
     * 专题主题抽取包含课程的节以及专题的章节
     *
     * @param courseChapterList                      课程章节列表
     * @param remodelingExternalPassbackBusinessList 外部课程回传状态列表
     */
    private List<CourseChapterSection> getCourseSection(List<CourseChapter> courseChapterList, List<String> domains,
                                                        List<RemodelingExternalPassbackBusiness> remodelingExternalPassbackBusinessList) {
        Map<String, List<RemodelingExternalPassbackBusiness>> passbackBusinessListMap = null;
        if (remodelingExternalPassbackBusinessList != null && !remodelingExternalPassbackBusinessList.isEmpty()) {
            passbackBusinessListMap =
                    remodelingExternalPassbackBusinessList.stream().collect(groupingBy(RemodelingExternalPassbackBusiness::getSectionId));
        }
        List<CourseChapterSection> chapterSectionList = new ArrayList<>();
        for (CourseChapter chapter : courseChapterList) {
            if (chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()) {
                Map<String, List<RemodelingExternalPassbackBusiness>> finalPassbackBusinessListMap =
                        passbackBusinessListMap;
                chapter.getCourseChapterSections().forEach(section -> {
                    for (String domain : domains) {
                        if (section.getUrl() != null && section.getUrl().contains(domain)) {
                            section.setIsExternalChongSu(1);
                            break;
                        }
                    }
                    if (CourseChapterSection.QUESTIONNAIRE_FLAG_YES.equals(section.getQuestionnaireFlag())) {
                        if (CourseChapterSection.SECTION_TYPE_COURSE == section.getSectionType()) {
                            section.setQuestionnaireButtonFlag(CourseChapterSection.QUESTIONNAIRE_BUTTON_FLAG_GREY);
                        } else if (CourseChapterSection.SECTION_TYPE_URL == section.getSectionType()) {
                            section.setQuestionnaireButtonFlag(CourseChapterSection.QUESTIONNAIRE_BUTTON_FLAG_GREY);
                            if ((section.getIsExternalChongSu().equals(CourseChapterSection.QUESTIONNAIRE_FLAG_NO)) ||
                                    (section.getIsExternalChongSu().equals(CourseChapterSection.QUESTIONNAIRE_FLAG_YES)
                                            && finalPassbackBusinessListMap != null
                                            && finalPassbackBusinessListMap.get(section.getReferenceId()) != null
                                            && !finalPassbackBusinessListMap.get(section.getReferenceId()).isEmpty())) {
                                section.setQuestionnaireButtonFlag(CourseChapterSection.QUESTIONNAIRE_BUTTON_FLAG_DISPLAY);
                            }
                        } else {
                            section.setQuestionnaireButtonFlag(CourseChapterSection.QUESTIONNAIRE_BUTTON_FLAG_HIDE);
                        }
                    } else {
                        section.setQuestionnaireButtonFlag(CourseChapterSection.QUESTIONNAIRE_BUTTON_FLAG_HIDE);
                    }
                    section.setCoverPath(generateSecurePathCdn(section.getCoverPath()));
                });
                chapterSectionList.addAll(chapter.getCourseChapterSections()
                        .stream().filter(s -> s.getSectionType() != null && (s.getSectionType().equals(CourseChapterSection.SECTION_TYPE_COURSE) || s.getSectionType().equals(CourseChapterSection.SECTION_TYPE_SUBJECT)))
                        .collect(Collectors.toList()));
            }
        }
        return chapterSectionList;
    }


    /**
     * 查询专题主题信息-包含进度（已弃用）
     *
     * @param courseId
     * @param memberId
     * @return
     */
    @Deprecated
    private List<CourseChapter> findChapterProgress(String courseId, String memberId) {
        String courseVersion = courseStudyProgressService.findByMemberIdAndCourseId(memberId, courseId)
                .map(r -> r.getCourseVersionId()).orElseGet(() -> courseInfoCommonDao.get(courseId).getVersionId());

        Result<Record> record = courseChapterCommonDao.execute(x -> x
                .select(Fields.start()
                        .add(
                                COURSE_CHAPTER.ID,
                                COURSE_CHAPTER.COURSE_ID,
                                COURSE_CHAPTER.NAME,
                                COURSE_CHAPTER.TITLE,
                                COURSE_CHAPTER.SEQUENCE,
                                COURSE_CHAPTER.VERSION_ID
                        )
                        .add(
                                COURSE_CHAPTER_SECTION.ID,
                                COURSE_CHAPTER_SECTION.REFERENCE_ID,
                                COURSE_CHAPTER_SECTION.RESOURCE_ID,
                                COURSE_CHAPTER_SECTION.NAME,
                                COURSE_CHAPTER_SECTION.CHAPTER_ID,
                                COURSE_CHAPTER_SECTION.COURSE_ID,
                                COURSE_CHAPTER_SECTION.SECTION_TYPE,
                                COURSE_CHAPTER_SECTION.SEQUENCE,
                                COURSE_CHAPTER_SECTION.REQUIRED,
                                COURSE_CHAPTER_SECTION.URL,
                                COURSE_CHAPTER_SECTION.TIME_MINUTE,
                                COURSE_CHAPTER_SECTION.TIME_SECOND,
                                COURSE_CHAPTER_SECTION.COVER_PATH
                        )
                        .end())
                .from(COURSE_CHAPTER)
                .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID).and(COURSE_CHAPTER_SECTION.COURSE_ID.eq(courseId)))
                .where(COURSE_CHAPTER.COURSE_ID.eq(courseId).and(COURSE_CHAPTER.VERSION_ID.eq(courseVersion)))
                .orderBy(COURSE_CHAPTER.SEQUENCE.asc(), COURSE_CHAPTER_SECTION.SEQUENCE.asc()).fetch());
        // 节进度
        Map<String, CourseSectionStudyProgress> sectionProgressMap =
                courseStudyProgressService.findSectionProgressByCourseId(courseId, memberId)
                        .stream().collect(toMap(CourseSectionStudyProgress::getSectionId, v -> v, (p, q) -> p));
        // 章节
        Map<String, List<CourseChapterSection>> sectionMap = record.into(COURSE_CHAPTER_SECTION)
                .intoGroups(COURSE_CHAPTER_SECTION.CHAPTER_ID, CourseChapterSection.class);

        // 章
        List<CourseChapter> courseChapters = new ArrayList<>(record.into(COURSE_CHAPTER).into(CourseChapter.class)
                .stream().collect(toMap(CourseChapter::getId, p -> p, (p, q) -> q)).values());

        courseChapters.forEach(x -> {
            List<CourseChapterSection> sectionList = sectionMap.get(x.getId());
            if (sectionList != null && !sectionList.isEmpty()) {
                sectionList.forEach(s -> {
                    CourseSectionStudyProgress progress = sectionProgressMap.get(s.getReferenceId());
                    if (progress != null) {
                        s.setProgress(progress);
                    } else if (s.getSectionType() != null && s.getSectionType().equals(CourseChapterSection.SECTION_TYPE_COURSE)) {
                        // 专题中的课程如果没有进度则去查课程的学习进度
                        progress = createSubjectSectionProgress(s, memberId, System.currentTimeMillis());
                        s.setProgress(progress);
                    }
                });
                x.setCourseChapterSections(sectionMap.get(x.getId()));
            }
        });

        courseChapters.sort(Comparator.comparingInt(CourseChapterEntity::getSequence)); // 倒序

        return courseChapters;
    }


    /**
     * 创建专题节进度
     *
     * @param courseId
     * @param memberId
     * @param chapterSectionList
     * @param courseStudyProgressList
     */
    private List<CourseSectionStudyProgress> createSubjectSectionProgress(String courseId, String memberId,
                                                                          List<CourseChapterSection> chapterSectionList, List<CourseStudyProgress> courseStudyProgressList) {
        List<CourseSectionStudyProgress> sectionProgressList = courseStudyProgressList.stream().map(p -> {
            String sectionId = chapterSectionList
                    .stream()
                    .filter(s -> s.getResourceId().equals(p.getCourseId()))
                    .findFirst()
                    .map(CourseChapterSection::getReferenceId).orElse(null);
            CourseSectionStudyProgress sectionProgress = new CourseSectionStudyProgress();
            sectionProgress.setSectionId(sectionId);
            sectionProgress.setFinishStatus(p.getFinishStatus());
            sectionProgress.setStudyTotalTime(p.getStudyTotalTime());
            return sectionProgress;
        }).collect(Collectors.toList());
        // 进入专题时需将专题中已学习过的引用的课程发消息异步处理添加章节进度并更新专题总进度
        messageSender.send(MessageTypeContent.SUBJECT_SECTION_STUDY_PROGRESS_ENDTER,
                MessageHeaderContent.COURSE_ID, courseId,
                MessageHeaderContent.MEMBER_ID, memberId,
                MessageHeaderContent.FINISHSTIME, System.currentTimeMillis() + "");
        return sectionProgressList;
    }

    /**
     * 检查专题内课程的进度，如存在则同步到专题中（暂时无用，courseStudyProgress更新分表未修改）
     *
     * @param section
     * @param memberId
     * @return
     */
    private CourseSectionStudyProgress createSubjectSectionProgress(CourseChapterSection section, String memberId,
                                                                    Long createTime) {
        List<CourseStudyProgress> courseStudyProgressList =
                courseStudyProgressService.findProgressByCourseIds(section.getResourceId(), memberId);
        if (courseStudyProgressList != null && courseStudyProgressList.size() > 0) {
            CourseStudyProgress p = courseStudyProgressList.get(0);
            CourseSectionStudyProgress sectionProgress = new CourseSectionStudyProgress();
            sectionProgress.setRequired(p.getIsRequired());
            sectionProgress.setCourseId(section.getCourseId());
            sectionProgress.setMemberId(memberId);
            sectionProgress.setSectionId(section.getReferenceId());
            sectionProgress.setBeginTime(p.getBeginTime());
            sectionProgress.setFinishStatus(p.getFinishStatus());
            sectionProgress.setFinishTime(p.getFinishTime());
            sectionProgress.setStudyTotalTime(p.getStudyTotalTime());
            sectionProgress.forInsert();
            sectionProgress.setLastAccessTime(sectionProgress.getCreateTime());
            courseStudyProgressService.insertSectionProgress(sectionProgress);

            int studyTotalTime = sectionProgress.getStudyTotalTime() != null ?
                    sectionProgress.getStudyTotalTime().intValue() : 0;
            int visits = p.getVisits() != null ? p.getVisits().intValue() : 0;

            //记录专题log updated 2019-11-20 log异步更新
            messageSender.send(MessageTypeContent.SUBJECT_INSERT_SECTION_STUDY_LOG_AND_DAY,
                    MessageHeaderContent.MEMBER_ID, memberId,
                    MessageHeaderContent.COURSE_ID, section.getCourseId(),
                    MessageHeaderContent.BUSINESS_ID, section.getReferenceId(),
                    MessageHeaderContent.PARAMS, sectionProgress.getFinishStatus() + "",
                    MessageHeaderContent.STUDYTIME, studyTotalTime + "",
                    MessageHeaderContent.FINISHSTIME, createTime + ""
            );
//      courseProcessService.insertSubjectLog(memberId, section.getCourseId(), section.getReferenceId(), sectionProgress.getFinishStatus(), studyTotalTime);
            // 更新专题时长、状态
            progressDao.execute(d -> d.update(COURSE_STUDY_PROGRESS)
                    .set(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME,
                            DSL.when(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.isNull(), 0).otherwise(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME).add(studyTotalTime))
                    .set(COURSE_STUDY_PROGRESS.VISITS,
                            DSL.when(COURSE_STUDY_PROGRESS.VISITS.isNull(), 0).otherwise(COURSE_STUDY_PROGRESS.VISITS).add(visits))
                    .set(COURSE_STUDY_PROGRESS.LAST_MODIFY_TIME, System.currentTimeMillis()) // add by wangdongyan  分表使用 最后一次修改时间
                    .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(section.getCourseId()).and(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(memberId)))
                    .execute());
            return sectionProgress;
        }
        return null;
    }

    /**
     * 查询可用资源清单
     */
    @DataSource(type= DataSourceEnum.SLAVE)
    @Override
    public PagedResult<CourseInfo> findAvailableCourseList(Integer page, Integer pageSize,
                                                           Optional<Integer> client, Optional<String> name,
                                                           Optional<String> category,
                                                           Optional<String> code, Optional<Long> shelveBeginDate,
                                                           Optional<Long> shelveEndDate,
                                                           List<String> grantOrganizationIds,
                                                           Optional<List<String>> parentOrganizationIds) {
        List<Condition> where = new ArrayList<>();

        where.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
        where.add(COURSE_INFO.BUSINESS_TYPE.eq(0));
        where.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));//选择器只查询已发布的课程
        category.map(x -> where.add(COURSE_CATEGORY.PATH.contains(x)));
        name.map(x -> where.add(COURSE_INFO.NAME.contains(x)));
        client.map(x -> where.add(COURSE_INFO.PUBLISH_CLIENT.eq(x)));
        code.map(x -> where.add(COURSE_INFO.CODE.contains(x)));
        shelveBeginDate.map(x -> where.add(COURSE_INFO.SHELVE_TIME.ge(x)));
        shelveEndDate.map(x -> where.add(COURSE_INFO.SHELVE_TIME.le(x)));
        return courseInfoCommonDao.execute(x -> {
            SelectOrderByStep<Record> select = x
                    .selectDistinct(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
                            .add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
                    .from(COURSE_INFO)
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                    .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                    .where(where).and(COURSE_INFO.ORGANIZATION_ID.in(grantOrganizationIds));
            if (parentOrganizationIds.isPresent()) {
                select = select.union(
                        x.selectDistinct(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
                                .add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
                                .from(COURSE_INFO)
                                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                                .where(where)
                                .and(COURSE_INFO.SHARE_SUB.eq(1).and(COURSE_INFO.ORGANIZATION_ID.in(parentOrganizationIds.get()))
                                ));
            }

            int count = x.fetchCount(select);
            Result<Record> record = select.orderBy(COURSE_INFO.SHELVE_TIME.desc()).limit((page - 1) * pageSize,
                    pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);

            List<Organization> organizationList = record.map(r -> {
                Organization org = new Organization();
                org.setName(r.getValue("oname", String.class));
                return org;
            });
            List<CourseCategory> courseCategoryList = record.map(r -> {
                CourseCategory cc = new CourseCategory();
                cc.setId(r.getValue("cid", String.class));
                cc.setName(r.getValue("cname", String.class));
                return cc;
            });

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
            });

            return PagedResult.create(count, courseInfoList);
        });
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public PagedResult<CourseInfo> findFullSearch(String currentUserId, Integer page, Integer pageSize,
                                                  Optional<Integer> orderBy, Optional<Integer> order,
                                                  Optional<String> searchContent,
                                                  Optional<String> categoryId, Optional<String> topicId, Integer type
            , Optional<Integer> publishClient,
                                                  Optional<Integer> companyType) {
        long beginTime = System.currentTimeMillis();
        PagedResult<CourseInfo> pageList = courseInfoCommonDao.execute(context -> {
            final int defaultOrderBy = 0;
            final int defaultOrder = 2;
            //logger.info("page start step1");
            // 1 课程 2 专题
            int audienceType = type.intValue();
            if (audienceType == 0) {
                audienceType = 1;
            }

            List<Condition> conditions = Stream.of(
                    searchContent.map(n -> {
                        String str = n.replace(" ", "");
                        String flag =
                                Optional.ofNullable(cache.get(CacheKeyConstant.COURSE_OR_SUBJECT_FULL_SEARCH_SELECT_RULE, String.class)).orElse("1");
                        if (flag.equals(CacheKeyConstant.COURSE_OR_SUBJECT_FULL_SEARCH_SELECT_RULE_TRUE)) {
                            return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                        }
                        return DSL.replace(COURSE_INFO.NAME, " ", "").startsWith(str);
                    }),
                    categoryId.map(COURSE_CATEGORY.PATH::contains),
                    publishClient.map(p -> COURSE_INFO.PUBLISH_CLIENT.eq(p).or(COURSE_INFO.PUBLISH_CLIENT.eq(0)))
            )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            conditions.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()));
            conditions.add(COURSE_INFO.VERSION_ID.isNotNull());
            conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
            conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(type));
//            conditions.add(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));
            conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
            conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(audienceType));
            conditions.add(COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE).or(COURSE_CATEGORY.STATE.isNull()));
            //logger.info("查询条件拼装完毕");
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select =
                        a.from(COURSE_INFO).leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
//                        .leftJoin(COURSE_STUDY_PROGRESS).on(COURSE_INFO.ID.eq(COURSE_STUDY_PROGRESS.COURSE_ID).and(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(currentUserId)))
                                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                                .where(conditions);

                if (topicId.isPresent()) {
                    select = select.andExists(context.select(BUSINESS_TOPIC.ID).from(BUSINESS_TOPIC)
                            .where(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID).and(BUSINESS_TOPIC.TOPIC_ID.eq(topicId.get()))));
                }
                if (companyType.isPresent() && companyType.get() != 0) {
                    // 计算公司类型
                    // 当前集团
                    Optional<String> l1 = organizationService.getLever(currentUserId, 2);
                    //当前公司
                    Optional<String> l2 = organizationService.getLever(currentUserId, 3);
                    l2 = l2.isPresent() ? l2 : l1;
                    // 其他公司(不在公司和集团)
                    List<String> list =
                            Stream.of(l1, l2).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
                    if (companyType.get().equals(1))
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l1.orElse("")));
                    if (companyType.get().equals(2))
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l2.orElse("")));
                    if (companyType.get().equals(3) && list.size() > 0) {
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.notIn(list));
                    }
                }
                return select;
            };

            SelectSelectStep<Record> listSelect = context.select(Fields.start()
                    .add(COURSE_INFO.ID)
                    .add(COURSE_INFO.NAME)
                    .add(COURSE_INFO.TYPE)
                    .add(COURSE_INFO.CREATE_TIME)
                    .add(COURSE_INFO.RELEASE_TIME)
                    .add(COURSE_INFO.SOURCE)
                    .add(COURSE_INFO.STATUS)
                    .add(COURSE_INFO.COVER)
                    .add(COURSE_INFO.COVER_PATH)
                    .add(COURSE_INFO.BEGIN_DATE)
                    .add(COURSE_INFO.END_DATE)
                    .add(COURSE_INFO.DESCRIPTION)
                    .add(COURSE_INFO.VISITS)
                    .add(COURSE_INFO.AVG_SCORE)
                    .add(COURSE_INFO.URL)
                    .add(COURSE_INFO.DESCRIPTION_TEXT)
                    .add(COURSE_INFO.INTEGRAL)
//                    .add(COURSE_STUDY_PROGRESS.FINISH_STATUS)
                    .end());
            SelectSelectStep<Record> countSelect =
                    context.select(Fields.start().add(COURSE_INFO.ID.countDistinct()).end());
            SelectSelectStep<Record> idsSelect = context.selectDistinct(Fields.start().add(COURSE_INFO.ID).end());

            SortField<?> sf = getSortField(orderBy.orElse(defaultOrderBy), order.orElse(defaultOrder));
            int firstResult = (page - 1) * pageSize;
            //logger.info("查询主体定义完毕");
            Integer count = stepFunc.apply(countSelect).fetchOne(0, Integer.class);
            //logger.info("查询总数完毕");
            Result<Record> idsResult = stepFunc.apply(idsSelect).orderBy(sf).limit(firstResult, pageSize).fetch();
            List<String> ids =
                    idsResult.into(CourseInfo.class).stream().map(x -> x.getId()).collect(Collectors.toList());
//            Result<Record> result = stepFunc.apply(listSelect).orderBy(sf).limit(firstResult, pageSize).fetch();
            //logger.info("查询列表完毕");
            List<CourseInfo> courseInfoList =
                    listSelect.from(COURSE_INFO).where(COURSE_INFO.ID.in(ids)).orderBy(sf).fetchInto(CourseInfo.class);
            //logger.info("序列化成list完毕");
            // result.
//            List<Integer> finishStatusList = result.getValues(COURSE_STUDY_PROGRESS.FINISH_STATUS);

//            IntStream.range(0, courseInfoList.size())
//                    .forEach(index -> courseInfoList.get(index).setFinishStatus(finishStatusList.get(index)));

            PagedResult<CourseInfo> r = PagedResult.create(count, courseInfoList);
            //logger.info("拼装pagedresult类完毕");
            return r;
        });
        //logger.info("总共耗时 "+ (System.currentTimeMillis() - beginTime));
        return pageList;
    }

    @Override
    public Map<String, Object> findFullSearchMap(String currentUserId, Integer page,
                                                 Integer pageSize,
                                                 Optional<Integer> orderBy, Optional<Integer> order,
                                                 Optional<String> searchContent,
                                                 Optional<String> categoryId, Optional<String> topicId, Integer type,
                                                 Optional<Integer> publishClient,
                                                 Optional<Integer> companyType) {
        long beginTime = System.currentTimeMillis();
        int[] more = {0};
        List<CourseInfo> courseList = courseInfoCommonDao.execute(context -> {
            final int defaultOrderBy = 0;
            final int defaultOrder = 2;
            //logger.info("page start step1");
            // 1 课程 2 专题
            int audienceType = type.intValue();
            if (audienceType == 0) {
                audienceType = 1;
            }

            List<Condition> conditions = Stream.of(
                    searchContent.map(n -> {
                        String str = n.replace(" ", "");
                        String flag =
                                Optional.ofNullable(cache.get(CacheKeyConstant.COURSE_OR_SUBJECT_FULL_SEARCH_SELECT_RULE, String.class)).orElse("1");
                        if (flag.equals(CacheKeyConstant.COURSE_OR_SUBJECT_FULL_SEARCH_SELECT_RULE_TRUE)) {
                            return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                        }
                        return DSL.replace(COURSE_INFO.NAME, " ", "").startsWith(str);
                    }),
                    categoryId.map(COURSE_CATEGORY.PATH::contains),
                    publishClient
                            .map(p -> COURSE_INFO.PUBLISH_CLIENT.eq(p).or(COURSE_INFO.PUBLISH_CLIENT.eq(0)))
            )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            conditions.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO)
                    .or(COURSE_INFO.DELETE_FLAG.isNull()));
            conditions.add(COURSE_INFO.VERSION_ID.isNotNull());
            conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES)
                    .or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
            conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(type));
            //            conditions.add(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));
            conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
            conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(audienceType));
            conditions.add(
                    COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE).or(COURSE_CATEGORY.STATE.isNull()));
            //logger.info("查询条件拼装完毕");
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO).leftJoin(COURSE_CATEGORY)
                        .on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                        //                        .leftJoin(COURSE_STUDY_PROGRESS).on(COURSE_INFO.ID.eq(COURSE_STUDY_PROGRESS.COURSE_ID).and(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(currentUserId)))
                        .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                        .where(conditions);

                if (topicId.isPresent()) {
                    select = select.andExists(context.select(BUSINESS_TOPIC.ID).from(BUSINESS_TOPIC)
                            .where(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID)
                                    .and(BUSINESS_TOPIC.TOPIC_ID.eq(topicId.get()))));
                }
                if (companyType.isPresent() && companyType.get() != 0) {
                    // 计算公司类型
                    // 当前集团
                    Optional<String> l1 = organizationService.getLever(currentUserId, 2);
                    //当前公司
                    Optional<String> l2 = organizationService.getLever(currentUserId, 3);
                    l2 = l2.isPresent() ? l2 : l1;
                    // 其他公司(不在公司和集团)
                    List<String> list = Stream.of(l1, l2).filter(Optional::isPresent).map(Optional::get)
                            .collect(Collectors.toList());
                    if (companyType.get().equals(1)) {
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l1.orElse("")));
                    }
                    if (companyType.get().equals(2)) {
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.eq(l2.orElse("")));
                    }
                    if (companyType.get().equals(3) && list.size() > 0) {
                        select = select.and(COURSE_INFO.ORGANIZATION_ID.notIn(list));
                    }
                }
                return select;
            };

            SelectSelectStep<Record> listSelect = context.select(Fields.start()
                    .add(COURSE_INFO.ID)
                    .add(COURSE_INFO.NAME)
                    .add(COURSE_INFO.TYPE)
                    .add(COURSE_INFO.CREATE_TIME)
                    .add(COURSE_INFO.RELEASE_TIME)
                    .add(COURSE_INFO.SOURCE)
                    .add(COURSE_INFO.STATUS)
                    .add(COURSE_INFO.COVER)
                    .add(COURSE_INFO.COVER_PATH)
                    .add(COURSE_INFO.BEGIN_DATE)
                    .add(COURSE_INFO.END_DATE)
                    .add(COURSE_INFO.DESCRIPTION)
                    .add(COURSE_INFO.VISITS)
                    .add(COURSE_INFO.AVG_SCORE)
                    .add(COURSE_INFO.URL)
                    .add(COURSE_INFO.DESCRIPTION_TEXT)
                    .add(COURSE_INFO.INTEGRAL)
                    //                    .add(COURSE_STUDY_PROGRESS.FINISH_STATUS)
                    .end());
            SelectSelectStep<Record> countSelect = context
                    .select(Fields.start().add(COURSE_INFO.ID.countDistinct()).end());
            SelectSelectStep<Record> idsSelect = context
                    .selectDistinct(Fields.start().add(COURSE_INFO.ID).end());

            SortField<?> sf = getSortField(orderBy.orElse(defaultOrderBy), order.orElse(defaultOrder));
            int firstResult = (page - 1) * pageSize;
            //logger.info("查询主体定义完毕");
            //      Integer count = stepFunc.apply(countSelect).fetchOne(0, Integer.class);
            //logger.info("查询总数完毕");
            Result<Record> idsResult = stepFunc.apply(idsSelect).orderBy(sf).limit(firstResult, pageSize + 1)
                    .fetch();
            List<String> ids = idsResult.into(CourseInfo.class).stream().map(x -> x.getId())
                    .collect(Collectors.toList());
            //            Result<Record> result = stepFunc.apply(listSelect).orderBy(sf).limit(firstResult, pageSize).fetch();
            //logger.info("查询列表完毕");
            if (Objects.nonNull(ids)) {
                if (pageSize.intValue() < ids.size()) {
                    more[0] = 1;
                    ids.remove(pageSize.intValue());
                }
            }
            List<CourseInfo> courseInfoList = listSelect.from(COURSE_INFO).where(COURSE_INFO.ID.in(ids))
                    .orderBy(sf).fetchInto(CourseInfo.class);
            //logger.info("序列化成list完毕");
            // result.
            //            List<Integer> finishStatusList = result.getValues(COURSE_STUDY_PROGRESS.FINISH_STATUS);

            //            IntStream.range(0, courseInfoList.size())
            //                    .forEach(index -> courseInfoList.get(index).setFinishStatus(finishStatusList.get(index)));

            //logger.info("拼装pagedresult类完毕");
            return courseInfoList;
        });
        Map<String, Object> results = new HashMap<>();

        results.put("items", courseList);
        results.put("recordCount", 0);
        results.put("more", more[0]);
        //logger.info("总共耗时 "+ (System.currentTimeMillis() - beginTime));
        return results;
    }

    @Override
    public List<CourseScore> findCourseScore(String[] memberIds, String businessId, Integer businessType) {
        return courseScoreDao.fetch(COURSE_SCORE.MEMBER_ID.in(memberIds), COURSE_SCORE.BUSINESS_ID.eq(businessId),
                COURSE_SCORE.BUSINESS_TYPE.eq(businessType));
    }

    @Override
    public CourseInfo getSubjectInfoDetailById(String subjectId) {
        return courseInfoCommonDao.getOptional(subjectId).orElse(null);
    }

    /**
     * 根据章节类型返回课程信息.
     *
     * @param resourceId 引用资源id
     * @param type       章节类型
     */
    @Override
    public CourseInfo getCourseInfoBySectionType(String resourceId, int type) {

        return courseChapterSectionCommonDao.execute(x ->
                x.select(COURSE_INFO.ID, COURSE_INFO.NAME).from(COURSE_INFO)
                        .leftJoin(COURSE_CHAPTER_SECTION)
                        .on(COURSE_INFO.ID.eq(COURSE_CHAPTER_SECTION.COURSE_ID))
                        .where(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(type)
                                .and(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(resourceId)))
                        .limit(1).fetchOne().map(r -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(r.get(COURSE_INFO.ID));
                    courseInfo.setName(r.get(COURSE_INFO.NAME));
                    return courseInfo;
                }));
    }


    @Override
    public List<CourseChapterSection> getCourseSectionList(List<String> resourceIds, int type) {
        List<CourseChapterSection> courseChapterSectionList = courseChapterSectionCommonDao.execute(x ->
                x.select(COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.ID).from(COURSE_CHAPTER_SECTION)
                        .where(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(type)
                                .and(COURSE_CHAPTER_SECTION.RESOURCE_ID.in(resourceIds)))
                        .fetch().into(CourseChapterSection.class));
        return courseChapterSectionList;
    }

    @Override
    public List<CourseChapter> findSubjectChapterSections(String subjectId) {
        Optional<String> courseVersion =
                courseInfoCommonDao.execute(x -> x.select(COURSE_INFO.VERSION_ID).from(COURSE_INFO).where(COURSE_INFO.ID.eq(subjectId))).fetchOptional(COURSE_INFO.VERSION_ID);
        return this.findCourseChapterByCourseId(subjectId, courseVersion);
    }

    @Override
    public Integer findCourseStudyCountBySubjectId(String subjectId) {
        return courseInfoCommonDao.execute(e -> e.select(DSL.count(COURSE_CHAPTER_SECTION.ID))
                .from(COURSE_INFO)
                .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER.COURSE_ID.eq(COURSE_INFO.ID))
                .and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID)
                        .or(COURSE_CHAPTER.VERSION_ID.isNull()))
                .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                .where(COURSE_INFO.ID.eq(subjectId))
                .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_COURSE))
                .fetchOne(DSL.count(COURSE_CHAPTER_SECTION.ID)));

    }

    @Override
    public List<String> getAllCourseIds(int start, int limit) {

        return courseInfoCommonDao.execute(dslContext -> {
            List<Condition> statusList = new ArrayList<>();
            statusList.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()));
            statusList.add(COURSE_INFO.VERSION_ID.isNotNull());
            statusList.add(COURSE_INFO.NAME.isNotNull());
            statusList.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
            statusList.add(COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE).or(COURSE_CATEGORY.STATE.isNull()));

            return dslContext.select(COURSE_INFO.ID)
                    .from(COURSE_INFO)
                    .leftJoin(COURSE_CATEGORY)
                    .on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                    .where(statusList)
                    .limit(start, limit)
                    .fetch(COURSE_INFO.ID);
        });
    }


    @Override
    public List<CourseInfo> getAllCourseInfo(int start, int limit, Optional<Integer> businessType) {

        return courseInfoCommonDao.execute(dslContext -> {
            List<Condition> statusList = new ArrayList<>();
            statusList.add(businessType.isPresent() ? COURSE_INFO.BUSINESS_TYPE.eq(businessType.get()) : DSL.trueCondition());
            return dslContext.select(COURSE_INFO.ID,COURSE_INFO.NAME,COURSE_INFO.CODE, COURSE_INFO.COURSE_TIME,
                            COURSE_INFO.BUSINESS_TYPE, COURSE_INFO.TYPE,COURSE_INFO.PUBLISH_CLIENT,COURSE_INFO.LECTURER,
                            COURSE_INFO.DESCRIPTION_APP,COURSE_INFO.STUDY_MEMBER_COUNT,COURSE_INFO.SEQUENCE,COURSE_INFO.VISITS)
                    .from(COURSE_INFO)
                    .where(statusList)
                    .orderBy(COURSE_INFO.CREATE_TIME.desc())
                    .limit(start, limit)
                    .fetchInto(CourseInfo.class);
        });
    }

    @Override
    public List<CourseInfo> getCourseInfoByTime(int start, int limit, Optional<Integer> businessType, Optional<Long> startTime, Optional<Long> endTime) {

        return courseInfoCommonDao.execute(dslContext -> {
            List<Condition> statusList = new ArrayList<>();
            statusList.add(businessType.isPresent() ? COURSE_INFO.BUSINESS_TYPE.eq(businessType.get()) : DSL.trueCondition());
            if(startTime.isPresent() && endTime.isPresent()){
                statusList.add(COURSE_INFO.MODIFY_DATE.between(new Timestamp(startTime.get()), new Timestamp(endTime.get())));
            }
            return dslContext.select(COURSE_INFO.ID,COURSE_INFO.NAME,COURSE_INFO.CODE, COURSE_INFO.COURSE_TIME,
                            COURSE_INFO.BUSINESS_TYPE, COURSE_INFO.TYPE,COURSE_INFO.PUBLISH_CLIENT,COURSE_INFO.LECTURER,
                            COURSE_INFO.DESCRIPTION_APP,COURSE_INFO.STUDY_MEMBER_COUNT,COURSE_INFO.SEQUENCE,COURSE_INFO.VISITS)
                    .from(COURSE_INFO)
                    .where(statusList)
                    .orderBy(COURSE_INFO.MODIFY_DATE.desc())
                    .limit(start, limit)
                    .fetchInto(CourseInfo.class);
        });
    }


    @Override
    public PagedResult<CourseInfo> findNewForParty(int page, int pageSize, Optional<Integer> businessType) {
        return courseInfoCommonDao.execute(context -> {
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> a.from(COURSE_INFO)
                    .where(COURSE_INFO.IS_PARTY.eq(CourseInfo.PARTY_BUILING_COURSE_TRUE),
                            COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES),
                            COURSE_INFO.PUBLISH_CLIENT.ne(CourseInfo.PUBLIST_CLIENT_APP),
                            businessType.map(COURSE_INFO.BUSINESS_TYPE::eq).orElse(DSL.trueCondition())
                    );

            SelectSelectStep<Record> countSelect = context.select(Fields.start().add(COURSE_INFO.ID.count()).end());

            Integer count = stepFunc.apply(countSelect).fetchOne(COURSE_INFO.ID.count());

            SelectSelectStep<Record> select = context.select(Fields.start().add(
                    COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.BUSINESS_TYPE,
                    COURSE_INFO.DESCRIPTION, COURSE_INFO.DESCRIPTION_TEXT, COURSE_INFO.VISITS,
                    COURSE_INFO.COVER_PATH, COURSE_INFO.COVER, COURSE_INFO.URL).end());

            List<CourseInfo> list =
                    stepFunc.apply(select).orderBy(COURSE_INFO.SHELVE_TIME.desc()).limit((page - 1) * pageSize, pageSize)
                            .fetch(r -> {
                                CourseInfo info = new CourseInfo();
                                info.setId(r.getValue(COURSE_INFO.ID));
                                info.setName(r.getValue(COURSE_INFO.NAME));
                                info.setBusinessType(r.getValue(COURSE_INFO.BUSINESS_TYPE));
                                info.setDescription(r.getValue(COURSE_INFO.DESCRIPTION));
                                info.setDescriptionText(r.getValue(COURSE_INFO.DESCRIPTION_TEXT));
                                info.setVisits(r.getValue(COURSE_INFO.VISITS));
                                info.setCover(r.getValue(COURSE_INFO.COVER));
                                info.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH));
                                info.setUrl(r.getValue(COURSE_INFO.URL));
                                return info;
                            });

            return PagedResult.create(count, list);
        });

    }

    @Override
    public List<CourseInfo> findBusinessTypeByIds(Collection<String> ids) {
        return courseInfoCommonDao.execute(e -> e.select(COURSE_INFO.ID, COURSE_INFO.BUSINESS_TYPE)
                .from(COURSE_INFO).where(COURSE_INFO.ID.in(ids))).fetch(r -> {
            CourseInfo courseInfo = new CourseInfo();
            courseInfo.setId(r.get(COURSE_INFO.ID));
            courseInfo.setBusinessType(r.get(COURSE_INFO.BUSINESS_TYPE));
            return courseInfo;
        });
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public PagedResult<CourseInfo> findStudyTeamCourseInfos(String currentUserId, Integer page, Integer pageSize,
                                                            Integer type,
                                                            Optional<String> courseName, List<String> courseIds,
                                                            String leaderMemberId, Long beginTime) {
        if (CourseInfo.BUSINESS_TYPE_SUBJECT.equals(type)) {
            return getSubjects(currentUserId, page, pageSize, courseName, courseIds, leaderMemberId, beginTime);
        }
        //查詢課程列表
        return getCourseInfos(currentUserId, page, pageSize, courseName, courseIds, leaderMemberId, beginTime);
    }



    private PagedResult<CourseInfo> getSubjects(String currentUserId, Integer page, Integer pageSize,
                                                Optional<String> courseName, List<String> courseIds, String leaderMemberId, Long beginTime) {
        com.zxy.product.course.jooq.tables.CourseInfo subject = COURSE_INFO.as("subject");
        com.zxy.product.course.jooq.tables.CourseInfo course = COURSE_INFO.as("course");
        com.zxy.product.course.jooq.tables.AudienceObject audienceObject = AUDIENCE_OBJECT.as("audienceObject");
        com.zxy.product.course.jooq.tables.AudienceMember audienceMember = AUDIENCE_MEMBER.as("audienceMember");
        //组装条件
        List<Condition> conditions = Stream.of(courseName.map(subject.NAME::contains))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(subject.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(subject.DELETE_FLAG.isNull()));
        conditions.add(subject.VERSION_ID.isNotNull());
        conditions.add(subject.STATUS.eq(CourseInfo.STATUS_SHELVES).or(subject.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
        conditions.add(subject.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
        conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
        conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT));
        PagedResult<CourseInfo> courseInfoPagedResult = courseInfoCommonDao.execute(x -> {
            com.zxy.product.course.jooq.tables.CourseChapterSection ccs = COURSE_CHAPTER_SECTION.as("ccs");
            com.zxy.product.course.jooq.tables.CourseChapterSection ccs2 = COURSE_CHAPTER_SECTION.as("ccs2");
            com.zxy.product.course.jooq.tables.CourseChapterSection ccs3 = COURSE_CHAPTER_SECTION.as("ccs3");
            com.zxy.product.course.jooq.tables.CourseInfo cs2 = COURSE_INFO.as("cs2");
            com.zxy.product.course.jooq.tables.CourseChapter cc2 = COURSE_CHAPTER.as("cc2");
            int typeNumber = 0;// 数据中还存在的课程类型的 章节数量
            SelectOrderByStep<Record> select = x
                    .select(Fields.start()
                            .add(subject.ID)
                            .end())
                    .from(subject)
                    .leftJoin(ORGANIZATION).on(subject.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(subject.ID))
                    .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                    .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER.COURSE_ID.eq(subject.ID)
                            .and(COURSE_CHAPTER.VERSION_ID.eq(subject.VERSION_ID).or(COURSE_CHAPTER.VERSION_ID.isNull())))
                    .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)
                            .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.in(CourseChapterSection.SECTION_TYPE_COURSE,CourseChapterSection.SECTION_TYPE_SUBJECT)))
                    .leftJoin(course).on(course.ID.eq(COURSE_CHAPTER_SECTION.RESOURCE_ID))
                    .leftJoin(audienceObject).on(audienceObject.BUSINESS_ID.eq(course.ID))
                    .leftJoin(audienceMember).on(audienceMember.ITEM_ID.eq(audienceObject.ITEM_ID))
                    .where(conditions)
                    .and(COURSE_CHAPTER_SECTION.RESOURCE_ID.notIn(courseIds))
                    .and(course.BUSINESS_TYPE.in(CourseInfo.BUSINESS_TYPE_COURSE,CourseInfo.BUSINESS_TYPE_SUBJECT))
                    .and(course.STATUS.eq(CourseInfo.STATUS_SHELVES).or(course.STATUS.eq(CourseInfo.STATUS_THE_TEST)))
                    .and(course.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(course.DELETE_FLAG.isNull()))
                    .and(audienceMember.MEMBER_ID.eq(currentUserId))
                    .and(DSL.exists(x
                                            .select(ccs.ID)
                                            .from(ccs)
                                            .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(ccs.RESOURCE_ID))
                                            .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                                            .where(
                                                    COURSE_CHAPTER_SECTION.ID.eq(ccs.ID)
                                                            .and(ccs.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_COURSE))
                                                            .and(ccs.RESOURCE_ID.notIn(courseIds))
                                                            .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                                            )
                                    )
                                    .or(
                                            DSL.exists(x
                                                    .select(ccs2.ID)
                                                    .from(ccs2)
                                                    .leftJoin(cs2).on(cs2.ID.eq(ccs2.RESOURCE_ID))
                                                    .leftJoin(cc2).on(cc2.COURSE_ID.eq(cs2.ID).and(cs2.VERSION_ID.eq(cc2.VERSION_ID).or(cc2.VERSION_ID.isNull())))
                                                    .leftJoin(ccs3).on(ccs3.CHAPTER_ID.eq(cc2.ID))
                                                    .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(ccs3.RESOURCE_ID))
                                                    .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                                                    .where(
                                                            COURSE_CHAPTER_SECTION.ID.eq(ccs2.ID)
                                                                    .and(ccs2.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_SUBJECT))
                                                                    .and(ccs3.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_COURSE))
                                                                    .and(ccs3.RESOURCE_ID.notIn(courseIds))
                                                                    .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                                                    )
                                            )
                                    )
                    )
                    .groupBy(subject.ID);
            //组装查询结果-专题
            List<CourseInfo> subjectList = select
                    .orderBy(subject.CREATE_TIME.desc())
                    .fetch(r -> {
                                CourseInfo info = new CourseInfo();
                                info.setId(r.getValue(subject.ID, String.class));
                                return info;
                            }
                    );
            return PagedResult.create(subjectList.size(),
                    subjectList.stream().skip(pageSize * (page - 1)).limit(pageSize).collect(Collectors.toList()));
        });
        List<String> subjectIds =
                courseInfoPagedResult.getItems().stream().map(r -> r.getId()).collect(Collectors.toList());
        // 查询专题下的课程id
        Map<String, List<CourseChapterSection>> sectionMap = getCourseChapterSections(currentUserId, subjectIds,
                courseIds);
        sectionMap.forEach((k,v)->{
            List<String> secondSubjectIds =  v.stream().filter(x -> x.getSectionType().equals(CourseChapterSection.SECTION_TYPE_SUBJECT)).map(CourseChapterSection::getResourceId).collect(toList());
            if (!secondSubjectIds.isEmpty()){
                Map<String, List<CourseChapterSection>> secondSectionMap = getCourseChapterSections(currentUserId, secondSubjectIds, courseIds);
                v.forEach(section-> section.setCourseChapterSections(secondSectionMap.get(section.getResourceId())));
                v.removeIf(c -> secondSubjectIds.contains(c.getResourceId()) && !secondSectionMap.containsKey(c.getResourceId()));
            }
        });
        //查询专题详细信息
        Map<String, CourseInfo> courseInfoMap = getSubjectInfos(subjectIds);
        courseInfoPagedResult.getItems().forEach(courseInfo -> {
            courseInfo.setCourseChapterSections(sectionMap.get(courseInfo.getId()));
            courseInfo.setName(courseInfoMap.get(courseInfo.getId()) == null ? "" :
                    courseInfoMap.get(courseInfo.getId()).getName());
            courseInfo.setBusinessType(courseInfoMap.get(courseInfo.getId()) == null ? null :
                    courseInfoMap.get(courseInfo.getId()).getBusinessType());
            courseInfo.setOrganization(courseInfoMap.get(courseInfo.getId()) == null ? null :
                    courseInfoMap.get(courseInfo.getId()).getOrganization());
            courseInfo.setCoverPath(courseInfoMap.get(courseInfo.getId()) == null ? "" :
                    courseInfoMap.get(courseInfo.getId()).getCoverPath());
        });
        getLeaderSubjectStudyStatus(leaderMemberId,  courseInfoPagedResult.getItems(), beginTime);

        return courseInfoPagedResult;
    }

    private void getLeaderStudyStatus(String leaderMemberId, DSLContext x, List<CourseInfo> infoList, Long beginTime) {
        if (Objects.nonNull(leaderMemberId) && Objects.nonNull(beginTime) &&
                !(DateUtil.offset(new Date(), Calendar.YEAR, -1).getTime() > beginTime)){
            // 查询团队中开始时间在当前时间前一年内的，的活动领学人课程的完成状态
            TableImpl<?> cspTable = courseCacheService.getCacheTable(leaderMemberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
            Map<String, Integer> leaderStudyStatus = x.select(cspTable.field("f_course_id", String.class), cspTable.field("f_finish_status", Integer.class))
                    .from(cspTable)
                    .where(cspTable.field("f_course_id", String.class).in(infoList.stream().map(CourseInfoEntity::getId).collect(toList()))
                            .and(cspTable.field("f_member_id", String.class).eq(leaderMemberId)))
                    .fetchMap(cspTable.field("f_course_id", String.class), cspTable.field("f_finish_status", Integer.class));

            infoList.forEach(info->{
                if (Objects.nonNull(leaderStudyStatus.get(info.getId()))) {
                    info.setFinishStatus(leaderStudyStatus.get(info.getId()));
                }
            });
        }
    }

    private void getLeaderSubjectStudyStatus(String leaderMemberId, List<CourseInfo> subjectList, Long beginTime) {
        if (Objects.nonNull(leaderMemberId) && Objects.nonNull(beginTime) && !(DateUtil.offset(new Date(), Calendar.YEAR, -1).getTime() > beginTime)) {
            // 查询团队中开始时间在当前时间前一年内的，的活动领学人的专题和课程的完成状态
            TableImpl<?> cspTable = courseCacheService.getCacheTable(leaderMemberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
            List<String> resourceIds = subjectList.stream().map(x -> {
                List<CourseChapterSection> courseChapterSections = Optional.ofNullable(x.getCourseChapterSections()).orElse(new ArrayList<>());
                List<String> collect = courseChapterSections
                        .stream()
                        .map(CourseChapterSection::getCourseChapterSections)
                        .filter(Objects::nonNull)
                        .flatMap(Collection::stream)
                        .map(CourseChapterSectionEntity::getResourceId)
                        .collect(Collectors.toList());
                collect.addAll(
                        courseChapterSections
                                .stream()
                                .map(CourseChapterSectionEntity::getResourceId)
                                .filter(Objects::nonNull)
                                .collect(Collectors.toList())
                );
                return collect;
            }).flatMap(Collection::stream).collect(Collectors.toList());

            Map<String, Integer> leaderStudyStatus = dataCourseCommonDao.execute(dsl -> dsl.select(cspTable.field("f_course_id", String.class), cspTable.field("f_finish_status", Integer.class))
                    .from(cspTable)
                    .where(
                            cspTable.field("f_course_id", String.class).in(resourceIds).and(cspTable.field("f_member_id", String.class).eq(leaderMemberId))
                    )
                    .fetchMap(cspTable.field("f_course_id", String.class), cspTable.field("f_finish_status", Integer.class)));

            // 处理专题内的数据的状态
            subjectList.forEach(subject -> {
                if (subject.getCourseChapterSections() != null) {
                    subject.getCourseChapterSections().forEach(courseOrSubject ->
                    {
                        courseOrSubject.setFinishStatus(leaderStudyStatus.get(courseOrSubject.getResourceId()));
                        Optional.ofNullable(courseOrSubject.getCourseChapterSections()).ifPresent(x ->
                                x.forEach(secondCourse ->
                                        secondCourse.setFinishStatus(leaderStudyStatus.get(secondCourse.getResourceId()))
                                )
                        );
                    });
                }
            });
        }
    }

    /**
     * 查询专题信息
     *
     * @param ids 专题id
     * @return
     */
    private Map<String, CourseInfo> getSubjectInfos(List<String> ids) {
        com.zxy.product.course.jooq.tables.CourseInfo subject = COURSE_INFO.as("subject");
        return courseChapterSectionCommonDao.execute(x -> x
                .select(
                        subject.ID,
                        subject.NAME,
                        subject.ORGANIZATION_ID,
                        ORGANIZATION.NAME,
                        ORGANIZATION.ID,
                        subject.BUSINESS_TYPE,
                        subject.COVER_PATH
                )
                .from(subject)
                .leftJoin(ORGANIZATION).on(subject.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(subject.ID.in(ids))
                .fetch(r -> {
                            CourseInfo info = new CourseInfo();
                            info.setId(r.getValue(subject.ID, String.class));
                            info.setName(r.getValue(subject.NAME, String.class));
                            info.setBusinessType(r.getValue(subject.BUSINESS_TYPE, Integer.class));
                            Organization org = new Organization();
                            org.setName(r.getValue(ORGANIZATION.NAME, String.class));
                            org.setId(r.getValue(subject.ORGANIZATION_ID, String.class));
                            info.setOrganization(org);
                            info.setCoverPath(r.getValue(subject.COVER_PATH, String.class));
                            return info;
                        }
                ).stream().collect(Collectors.toMap(CourseInfo::getId, e -> e, (o, v) -> o)));
    }

    /**
     * 根据专题查询课程id
     *
     * @param subjectIds 专题id
     * @param courseIds  已选的课程id - 需要过滤
     * @return <专题id，课程列表>
     */
    private Map<String, List<CourseChapterSection>> getCourseChapterSections(String currentUserId,
                                                                             List<String> subjectIds, List<String> courseIds) {
        com.zxy.product.course.jooq.tables.CourseInfo subject = COURSE_INFO.as("subject");
        com.zxy.product.course.jooq.tables.CourseInfo course = COURSE_INFO.as("course");
        return courseChapterSectionCommonDao.execute(x -> x
                .selectDistinct(COURSE_CHAPTER_SECTION.RESOURCE_ID,
                        COURSE_CHAPTER_SECTION.COURSE_ID,
                        COURSE_CHAPTER_SECTION.SECTION_TYPE,
                        course.ID,
                        course.NAME,
                        course.TYPE,
                        course.CATEGORY_ID,
                        course.PUBLISH_CLIENT,
                        course.COURSE_TIME,
                        course.COVER_PATH)
                .from(subject)
                .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER.COURSE_ID.eq(subject.ID)
                        .and(COURSE_CHAPTER.VERSION_ID.eq(subject.VERSION_ID).or(COURSE_CHAPTER.VERSION_ID.isNull())))
                .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)
                        .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.in(CourseChapterSection.SECTION_TYPE_COURSE,CourseChapterSection.SECTION_TYPE_SUBJECT)))
                .leftJoin(course).on(course.ID.eq(COURSE_CHAPTER_SECTION.RESOURCE_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(course.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .where(subject.ID.in(subjectIds))
                .and(COURSE_CHAPTER_SECTION.RESOURCE_ID.notIn(courseIds))
                .and(course.BUSINESS_TYPE.in(CourseInfo.BUSINESS_TYPE_COURSE,CourseInfo.BUSINESS_TYPE_SUBJECT))
                .and(course.STATUS.eq(CourseInfo.STATUS_SHELVES).or(course.STATUS.eq(CourseInfo.STATUS_THE_TEST)))
                .and(course.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(course.DELETE_FLAG.isNull()))
                .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                .fetch(r -> {
                            CourseChapterSection courseChapterSection = new CourseChapterSection();
                            courseChapterSection.setResourceId(r.getValue(COURSE_CHAPTER_SECTION.RESOURCE_ID));
                            courseChapterSection.setCourseId(r.getValue(COURSE_CHAPTER_SECTION.COURSE_ID));
                            courseChapterSection.setName(r.getValue(course.NAME, String.class));
                            courseChapterSection.setPublishClient(r.getValue(course.PUBLISH_CLIENT, Integer.class));
                            String allCourseTime = calCourseTime(r.getValue(course.COURSE_TIME, Integer.class));
                            courseChapterSection.setAllCourseTime(allCourseTime);
                            courseChapterSection.setCoverPath(r.getValue(course.COVER_PATH, String.class));
                            courseChapterSection.setSectionType(r.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                            return courseChapterSection;
                        }
                ).stream().filter(s -> !StringUtils.isEmpty(s.getCourseId())).collect(groupingBy(CourseChapterSection::getCourseId))
        );
    }

    private PagedResult<CourseInfo> getCourseInfos(String currentUserId, Integer page, Integer pageSize,
                                                   Optional<String> courseName, List<String> courseIds, String leaderMemberId, Long beginTime) {
        //组装条件
        List<Condition> conditions = Stream.of(courseName.map(COURSE_INFO.NAME::contains))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()));
        conditions.add(COURSE_INFO.VERSION_ID.isNotNull());
        conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
        conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE));
        conditions.add(COURSE_INFO.ID.notIn(courseIds));
        conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
        conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COURSE));
        conditions.add(COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE).or(COURSE_CATEGORY.STATE.isNull()));
        return courseInfoCommonDao.execute(x -> {
            SelectOrderByStep<Record> select = x
                    .selectDistinct(Fields.start()
                            .add(COURSE_INFO.ID)
                            .add(COURSE_INFO.NAME)
                            .add(COURSE_INFO.TYPE)
                            .add(COURSE_INFO.CATEGORY_ID)
                            .add(COURSE_INFO.ORGANIZATION_ID)
                            .add(ORGANIZATION.NAME)
                            .add(ORGANIZATION.ID)
                            .add(COURSE_CATEGORY.NAME)
                            .add(COURSE_CATEGORY.ID)
                            .add(COURSE_INFO.PUBLISH_CLIENT)
                            .add(COURSE_INFO.COURSE_TIME)
                            .add(COURSE_INFO.COVER_PATH)
                            .end())
                    .from(COURSE_INFO)
                    .leftJoin(ORGANIZATION).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                    .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                    .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                    .where(conditions);
            SelectSelectStep<Record> selectCount = x.select(Fields.start().add(COURSE_INFO.ID.count()).end());
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> selectFields = a
                        .from(COURSE_INFO)
                        .leftJoin(ORGANIZATION).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                        .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                        .where(conditions);
                return selectFields;
            };
            int count = stepFunc.apply(selectCount).fetchOne().getValue(0, Integer.class);
            //组装查询结果
            List<CourseInfo> courseInfoList = select
                    .orderBy(COURSE_INFO.CREATE_TIME.desc())
                    .limit((page - 1) * pageSize, pageSize)
                    .fetch(r -> {
                                CourseInfo info = new CourseInfo();
                                info.setId(r.getValue(COURSE_INFO.ID, String.class));
                                info.setName(r.getValue(COURSE_INFO.NAME, String.class));
                                info.setPublishClient(r.getValue(COURSE_INFO.PUBLISH_CLIENT, Integer.class));
                                String allCourseTime = calCourseTime(r.getValue(COURSE_INFO.COURSE_TIME,
                                        Integer.class));
                                info.setAllCourseTime(allCourseTime);
                                CourseCategory category = new CourseCategory();
                                category.setId(r.getValue(COURSE_INFO.CATEGORY_ID, String.class));
                                category.setName(r.getValue(COURSE_CATEGORY.NAME, String.class));
                                info.setCategory(category);
                                Organization org = new Organization();
                                org.setName(r.getValue(ORGANIZATION.NAME, String.class));
                                org.setId(r.getValue(COURSE_INFO.ORGANIZATION_ID, String.class));
                                info.setOrganization(org);
                                info.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH, String.class));
                                return info;
                            }
                    );
            getLeaderStudyStatus(leaderMemberId,x,courseInfoList, beginTime);
            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public List<CourseInfo> findCoursesByTeamBusinessIds(String currentUserId, List<String> businessIds) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(currentUserId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        //查询课程的学习进度
        List<CourseStudyProgress> progresses = courseInfoCommonDao.execute(x ->
                x.select(cacheTable.field("f_course_id",String.class), cacheTable.field("f_finish_status",Integer.class)).from(cacheTable)
                        .where(cacheTable.field("f_course_id",String.class).in(businessIds).and(cacheTable.field("f_member_id",String.class).eq(currentUserId)))
                        .fetchInto(CourseStudyProgress.class));
        Map<String, CourseStudyProgress> progressMap =
                progresses.stream().collect(Collectors.toMap(CourseStudyProgress::getCourseId, a -> a, (k1, k2) -> k1));
        SelectHavingStep<Record> step = courseInfoCommonDao.execute(e -> e.selectDistinct(Fields.start()
                .add(COURSE_INFO.ID).add(COURSE_INFO.NAME).add(COURSE_INFO.COURSE_TIME)
                .add(COURSE_INFO.COVER_PATH).end())
                .from(COURSE_INFO)
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                .where(COURSE_INFO.ID.in(businessIds))
                .and(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()))
                .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)))
                .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
        );
        List<CourseInfo> courseInfos = step.fetch().map(r -> {
            CourseInfo courseInfo = new CourseInfo();
            courseInfo.setName(r.getValue(COURSE_INFO.NAME, String.class));
            courseInfo.setId(r.getValue(COURSE_INFO.ID, String.class));
            String allCourseTime = calCourseTime(r.getValue(COURSE_INFO.COURSE_TIME, Integer.class));
            courseInfo.setAllCourseTime(allCourseTime);
            courseInfo.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH, String.class));
            return courseInfo;
        });
        courseInfos.forEach(course -> {
            if (!CollectionUtils.isEmpty(progressMap) && !ObjectUtils.isEmpty(progressMap.get(course.getId()))) {
                course.setFinishStatus(progressMap.get(course.getId()).getFinishStatus());
            }
        });
        return courseInfos;
    }

    @Override
    public List<CourseInfo> findCourseAudience(String currentUserId, List<String> businessIds) {
        SelectHavingStep<Record> step = courseInfoCommonDao.execute(e -> e.selectDistinct(Fields.start()
                .add(COURSE_INFO.ID).end())
                .from(COURSE_INFO)
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                .where(COURSE_INFO.ID.in(businessIds))
                .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
        );
        List<CourseInfo> courseInfos = step.fetch().map(r -> {
            CourseInfo courseInfo = new CourseInfo();
            courseInfo.setId(r.getValue(COURSE_INFO.ID, String.class));
            return courseInfo;
        });
        return courseInfos;
    }

    @Override
    public List<CourseInfo> findCourse(String currentUserId, List<String> businessIds) {
        SelectHavingStep<Record> step = courseInfoCommonDao.execute(e -> e.selectDistinct(Fields.start()
                .add(COURSE_INFO).end())
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.in(businessIds))
//                .and(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()))
//                .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)))
        );
        List<CourseInfo> courseInfos = step.fetch().map(r -> r.into(COURSE_INFO).into(CourseInfo.class));
        return courseInfos;
    }

    @Override
    public Long getChapterSectionStatus(Optional<List<String>> sectionIds) {

        return sectionIds.map(ids -> {

            return courseInfoCommonDao.execute(e -> e.select(COURSE_INFO.STATUS)
                    .from(COURSE_INFO).where(COURSE_INFO.ID.in(ids))
                    .fetchInto(Integer.class)
                    .stream().filter(s -> !Objects.equals(s, CourseInfo.STATUS_SHELVES)).count()
            );

        }).orElse(null);

    }

    @Override
    public List<CourseInfo> getSubjectsBySectionId(Optional<String> optionalId) {

        return optionalId.map(id ->
                courseInfoCommonDao.execute(e ->
                        e.select(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.CREATE_MEMBER_ID,COURSE_INFO.BUSINESS_TYPE)
                                .from(COURSE_INFO)
                                .leftJoin(COURSE_CHAPTER)
                                .on(COURSE_CHAPTER.COURSE_ID.eq(COURSE_INFO.ID))
                                .and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID)
                                        .or(COURSE_CHAPTER.VERSION_ID.isNull()))
                                .leftJoin(COURSE_CHAPTER_SECTION)
                                .on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                                .where(COURSE_CHAPTER_SECTION.RESOURCE_ID.in(id))
                                .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                                .fetchInto(CourseInfo.class)
                )).orElse(null);
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public List<CourseInfo> hotVisitsList(String organizationId, Integer size, Integer businessType, Integer clientType) {
        return courseInfoCommonDao.execute(e->
                e.select(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.VISITS,COURSE_INFO.BUSINESS_TYPE,COURSE_INFO.COVER_PATH)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ORGANIZATION_ID.eq(organizationId))
                        .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                        .and(COURSE_INFO.BUSINESS_TYPE.eq(businessType))
                        .and(COURSE_INFO.PUBLISH_CLIENT.eq(CourseInfo.PUBLISH_CLIENT_ALL)
                                .or(COURSE_INFO.PUBLISH_CLIENT.eq(clientType))
                        )
                        .and(COURSE_INFO.RELEASE_TIME.ge(hotVistRankStartTime))
                        .orderBy(COURSE_INFO.VISITS.desc())
                        .limit(0, size).fetchInto(CourseInfo.class));
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public List<CourseSectionStudyLogAhDay> findCourseSectionDay(int offset, int pageSize, List<String> courseIds,
                                                                 Optional<Long> startTime,
                                                                 Optional<Long> endTime, String tableName,
                                                                 String progressTableName, Optional<Object> isFirst) {
        TableImpl<?> studyLogTable = SplitTableName.getTableNameByCode(tableName);
        TableImpl<?> progressTable = SplitTableName.getTableNameByCode(progressTableName);
        Map<String, String> courseNameMap =
                courseInfoCommonDao.execute(dslContext -> dslContext.select(COURSE_INFO.ID, COURSE_INFO.NAME)
                        .from(COURSE_INFO)
                        .where(isFirst.map(noFirst -> COURSE_INFO.ID.in(courseIds)).orElse(DSL.trueCondition()))
                        .fetchMap(COURSE_INFO.ID, COURSE_INFO.NAME));
        return courseSectionStudyLogDayCommonDao.execute(x -> x.select(
                studyLogTable.field("f_member_id", String.class),
                studyLogTable.field("f_course_id", String.class),
                studyLogTable.field("f_day", Integer.class),
                studyLogTable.field("f_month", Integer.class),
                studyLogTable.field("f_year", Integer.class),
                DSL.ifnull(studyLogTable.field("f_pc_study_time", Integer.class), 0).as("pcStudyTime"),
                DSL.ifnull(studyLogTable.field("f_app_study_time", Integer.class), 0).as("appStudyTime"),
                studyLogTable.field("f_study_time", Integer.class),
                studyLogTable.field("f_id", String.class),
                studyLogTable.field("f_create_time", String.class),
                progressTable.field("f_finish_status", Integer.class))
                .from(studyLogTable)
                .leftJoin(progressTable)
                .on(studyLogTable.field("f_member_id", String.class).eq(progressTable.field("f_member_id",
                        String.class)))
                .and(studyLogTable.field("f_course_id", String.class).eq(progressTable.field("f_course_id",
                        String.class)))
                .where(studyLogTable.field("f_study_time", Integer.class).gt(0)
                        .and(startTime.map(start -> {
                            String minDay = new SimpleDateFormat("yyyyMMdd").format(new Date(start));
                            return studyLogTable.field("f_day", Integer.class).ge(Integer.valueOf(minDay));
                        }).orElse(DSL.trueCondition()))
                        .and(endTime.map(end -> {
                            String maxDay = new SimpleDateFormat("yyyyMMdd").format(new Date(end));
                            return studyLogTable.field("f_day", Integer.class).lt(Integer.valueOf(maxDay));
                        }).orElse(DSL.trueCondition()))
                        .and(isFirst.map(noFirst -> studyLogTable.field("f_course_id", String.class).in(courseIds)).orElse(DSL.trueCondition()))
                )
                .limit(offset, pageSize)
                .fetch(r -> {
                    CourseSectionStudyLogAhDay day = new CourseSectionStudyLogAhDay();
                    day.setAppStudyTime(r.getValue(6, Integer.class));
                    day.setPcStudyTime(r.getValue(5, Integer.class));
                    day.setCourseId(r.getValue(studyLogTable.field("f_course_id", String.class)));
                    day.setMemberId(r.getValue(studyLogTable.field("f_member_id", String.class)));
                    day.setDay(r.getValue(studyLogTable.field("f_day", Integer.class)));
                    day.setMonth(r.getValue(studyLogTable.field("f_month", Integer.class)));
                    day.setYear(r.getValue(studyLogTable.field("f_year", Integer.class)));
                    day.setStudyTime(r.getValue(studyLogTable.field("f_study_time", Integer.class)));
                    day.setId(r.getValue(studyLogTable.field("f_id", String.class)));
                    day.setCourseName(courseNameMap.getOrDefault(r.getValue(studyLogTable.field("f_course_id",
                            String.class)), ""));
                    Optional.ofNullable(r.getValue(studyLogTable.field("f_create_time", String.class))).ifPresent(time -> day.setCreateTime(Long.parseLong(time)));
                    day.setCourseStatus(r.getValue(progressTable.field("f_finish_status", Integer.class)));
                    return day;
                })
        );
    }


    @Override
    public List<String> getCourseOrSubjectAllIds(int offset, int limit, int isSubject) {
        return courseInfoCommonDao.execute(dsl ->
                dsl.select(COURSE_INFO.ID)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.IS_SUBJECT.eq(isSubject))
                        .and(COURSE_INFO.BUSINESS_TYPE.eq(isSubject == 1 ? CourseInfo.BUSINESS_TYPE_SUBJECT :
                                CourseInfo.BUSINESS_TYPE_COURSE))
                        .and(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()))
                        .orderBy(COURSE_INFO.CREATE_TIME.sort(SortOrder.DESC))
                        .limit(offset, limit).fetch(record -> record.getValue(COURSE_INFO.ID)));
    }

    /**
     * 查询授权范围内的课程
     *
     * @param page
     * @param pageSize
     * @param currentUserId   当前登录用户id
     * @param topicId         标签id
     * @param name            课程名称
     * @param shelveTimeStart 首次发布开始时间
     * @param shelveTimeEnd   首次发布结束时间
     * @param category        分类/序列
     * @param isParty         是否为党课
     * @param organization    归属部门
     * @param publishClient   适用终端
     * @return
     */
    @Override
    public PagedResult<CourseInfo> findCourseInfo(Integer page, Integer pageSize, String currentUserId, String topicId,
                                                  Optional<String> name, Optional<Long> shelveTimeStart,
                                                  Optional<Long> shelveTimeEnd,
                                                  Optional<String> category, Optional<Integer> isParty,
                                                  Optional<String> organization, Optional<Integer> publishClient) {
        //根据标签id查询课程id列表
        List<String> ids = businessTopicCommonDao.fetch(BUSINESS_TOPIC.TOPIC_ID.eq(topicId)
                .and(BUSINESS_TOPIC.BUSINESS_TYPE.eq(BusinessTopic.BUSINESS_TYPE_COURSE)))
                .stream().map(BusinessTopic::getBusinessId).collect(Collectors.toList());

        List<Condition> conditions = Stream.of(name.map(COURSE_INFO.NAME::contains),
                shelveTimeStart.map(COURSE_INFO.SHELVE_TIME::ge),
                shelveTimeEnd.map(COURSE_INFO.SHELVE_TIME::le),
                category.map(COURSE_INFO.CATEGORY_ID::eq),
                organization.map(COURSE_INFO.ORGANIZATION_ID::eq),
                publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE));
        conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
        conditions.add(COURSE_INFO.ID.notIn(ids));
        conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));
        if (isParty.isPresent()) {
            if (isParty.get().equals(CourseInfo.IS_NO_PART)) {
                //非党课
                conditions.add(COURSE_INFO.IS_PARTY.eq(isParty.get()).or(COURSE_INFO.IS_PARTY.isNull()));
            } else {
                //党课
                conditions.add(COURSE_INFO.IS_PARTY.eq(isParty.get()));
            }
        }

        SelectOnConditionStep<Record> stepSelect = courseInfoCommonDao.execute(e -> e.selectDistinct(Fields.start()
                .add(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.SHELVE_TIME, COURSE_INFO.IS_PARTY, COURSE_INFO.PUBLISH_CLIENT)
                .add(ORGANIZATION.NAME)
                .add(COURSE_CATEGORY.NAME).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COURSE)))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))

        );

        SelectOnConditionStep<Record> stepCount = courseInfoCommonDao.execute(e -> e.select(Fields.start()
                .add(COURSE_INFO.ID.countDistinct()).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COURSE)))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
        );

        int firstResult = (page - 1) * pageSize;
        Integer count = stepCount.where(conditions).fetchOne().getValue(0, Integer.class);
        List<CourseInfo> items = new ArrayList<>();
        items = stepSelect.where(conditions).orderBy(COURSE_INFO.CREATE_TIME.desc()).limit(firstResult, pageSize)
                .fetch(item -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(item.getValue(COURSE_INFO.ID));
                    courseInfo.setName(item.getValue(COURSE_INFO.NAME));
                    courseInfo.setShelveTime(item.getValue(COURSE_INFO.SHELVE_TIME));
                    Integer party = item.getValue(COURSE_INFO.IS_PARTY);
                    if (party == null) {
                        //==null 非党课
                        courseInfo.setIsParty(CourseInfo.IS_NO_PART);
                    } else {
                        courseInfo.setIsParty(party);
                    }
                    courseInfo.setPublishClient(item.getValue(COURSE_INFO.PUBLISH_CLIENT));
                    courseInfo.setOrganizationName(item.getValue(ORGANIZATION.NAME));
                    courseInfo.setCategoryName(item.getValue(COURSE_CATEGORY.NAME));
                    return courseInfo;
                });
        return PagedResult.create(count, items);
    }

    /**
     * 查询授权范围内的专题
     *
     * @param page          当前页数
     * @param pageSize      每页大小
     * @param currentUserId 当前登录用户id
     * @param topicId       标签id
     * @param name          专题名称
     * @param organization  归属部门
     * @param publishClient 适用终端
     * @param type          类型:1是普通，2是个性
     * @return
     */
    @Override
    public PagedResult<CourseInfo> findSubjectInfo(Integer page, Integer pageSize, String currentUserId, String topicId,
                                                   Optional<String> name, Optional<String> organization,
                                                   Optional<Integer> publishClient, Optional<Integer> type) {
        //根据标签id查询专题id列表
        List<String> ids = businessTopicCommonDao.fetch(BUSINESS_TOPIC.TOPIC_ID.eq(topicId)
                .and(BUSINESS_TOPIC.BUSINESS_TYPE.eq(BusinessTopic.BUSINESS_TYPE_SUBJECT)))
                .stream().map(BusinessTopic::getBusinessId).collect(Collectors.toList());

        List<Condition> conditions = Stream.of(name.map(COURSE_INFO.NAME::contains),
                organization.map(COURSE_INFO.ORGANIZATION_ID::eq),
                publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
        conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
        conditions.add(COURSE_INFO.ID.notIn(ids));
        conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));

        if (type.isPresent()) {
            if (type.get() == CourseInfo.GENERAL) {//普通
                conditions.add(COURSE_INFO.URL.isNull());
            } else if (type.get() == CourseInfo.PERSONALIZATION) {//个性化
                conditions.add(COURSE_INFO.URL.isNotNull());
            }
        }

        SelectOnConditionStep<Record> stepSelect = courseInfoCommonDao.execute(e -> e.selectDistinct(Fields.start()
                .add(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.PUBLISH_CLIENT, COURSE_INFO.URL)
                .add(ORGANIZATION.NAME).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT)))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
        );

        SelectOnConditionStep<Record> stepCount = courseInfoCommonDao.execute(e -> e.select(Fields.start()
                .add(COURSE_INFO.ID.countDistinct()).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT)))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
        );

        int firstResult = (page - 1) * pageSize;
        Integer count = stepCount.where(conditions).fetchOne().getValue(0, Integer.class);
        List<CourseInfo> items = new ArrayList<>();
        items = stepSelect.where(conditions).orderBy(COURSE_INFO.CREATE_TIME.desc()).limit(firstResult, pageSize)
                .fetch(item -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(item.getValue(COURSE_INFO.ID));
                    courseInfo.setName(item.getValue(COURSE_INFO.NAME));
                    courseInfo.setPublishClient(item.getValue(COURSE_INFO.PUBLISH_CLIENT));
                    courseInfo.setOrganizationName(item.getValue(ORGANIZATION.NAME));
                    courseInfo.setUrl(item.getValue(COURSE_INFO.URL));
                    return courseInfo;
                });
        return PagedResult.create(count, items);
    }

    /**
     * 根据标签查询关联的课程
     *
     * @param page            当前页
     * @param pageSize        每页条数
     * @param currentUserId   当前登录用户id
     * @param topicId         标签id
     * @param name            课程名称
     * @param shelveTimeStart 首次发布开始时间
     * @param shelveTimeEnd   首次发布结束时间
     * @param category        分类/序列
     * @param isParty         是否为党课
     * @param organization    归属部门
     * @param publishClient   适用终端
     * @return
     */
    @Override
    public PagedResult<CourseInfo> findCourseInfoByTopic(Integer page, Integer pageSize, String currentUserId,
                                                         String topicId,
                                                         Optional<String> name, Optional<Long> shelveTimeStart, Optional<Long> shelveTimeEnd,
                                                         Optional<String> category, Optional<Integer> isParty, Optional<String> organization, Optional<Integer> publishClient) {
        List<Condition> conditions = Stream.of(name.map(COURSE_INFO.NAME::contains),
                shelveTimeStart.map(COURSE_INFO.SHELVE_TIME::ge),
                shelveTimeEnd.map(COURSE_INFO.SHELVE_TIME::le),
                category.map(COURSE_INFO.CATEGORY_ID::eq),
                organization.map(COURSE_INFO.ORGANIZATION_ID::eq),
                publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE));
        conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
        conditions.add(BUSINESS_TOPIC.TOPIC_ID.eq(topicId));
        if (isParty.isPresent()) {
            if (isParty.get().equals(CourseInfo.IS_NO_PART)) {
                //非党课
                conditions.add(COURSE_INFO.IS_PARTY.eq(isParty.get()).or(COURSE_INFO.IS_PARTY.isNull()));
            } else {
                //党课
                conditions.add(COURSE_INFO.IS_PARTY.eq(isParty.get()));
            }
        }

        SelectOnConditionStep<Record> stepSelect = courseInfoCommonDao.execute(e -> e.selectDistinct(Fields.start()
                .add(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.SHELVE_TIME, COURSE_INFO.IS_PARTY, COURSE_INFO.PUBLISH_CLIENT)
                .add(ORGANIZATION.NAME)
                .add(COURSE_CATEGORY.NAME).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COURSE)))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .leftJoin(BUSINESS_TOPIC).on(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID).and(BUSINESS_TOPIC.BUSINESS_TYPE.eq(BusinessTopic.BUSINESS_TYPE_COURSE)))
        );

        SelectOnConditionStep<Record> stepCount = courseInfoCommonDao.execute(e -> e.select(Fields.start()
                .add(COURSE_INFO.ID.countDistinct()).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COURSE)))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .leftJoin(BUSINESS_TOPIC).on(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID).and(BUSINESS_TOPIC.BUSINESS_TYPE.eq(BusinessTopic.BUSINESS_TYPE_COURSE)))
        );

        int firstResult = (page - 1) * pageSize;
        Integer count = stepCount.where(conditions).fetchOne().getValue(0, Integer.class);
        List<CourseInfo> items = new ArrayList<>();
        items = stepSelect.where(conditions).orderBy(COURSE_INFO.CREATE_TIME.desc()).limit(firstResult, pageSize)
                .fetch(item -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(item.getValue(COURSE_INFO.ID));
                    courseInfo.setName(item.getValue(COURSE_INFO.NAME));
                    courseInfo.setShelveTime(item.getValue(COURSE_INFO.SHELVE_TIME));
                    Integer party = item.getValue(COURSE_INFO.IS_PARTY);
                    if (party == null) {
                        //==null 非党课
                        courseInfo.setIsParty(CourseInfo.IS_NO_PART);
                    } else {
                        courseInfo.setIsParty(party);
                    }
                    courseInfo.setPublishClient(item.getValue(COURSE_INFO.PUBLISH_CLIENT));
                    courseInfo.setOrganizationName(item.getValue(ORGANIZATION.NAME));
                    courseInfo.setCategoryName(item.getValue(COURSE_CATEGORY.NAME));
                    return courseInfo;
                });
        return PagedResult.create(count, items);
    }

    /**
     * 根据标签查询关联的专题
     *
     * @param page          当前页
     * @param pageSize      每页条数
     * @param currentUserId 当前登录用户id
     * @param topicId       标签id
     * @param name          专题名称
     * @param organization  归属部门
     * @param publishClient 适用终端
     * @param type          类型:1是普通，2是个性
     * @return
     */
    @Override
    public PagedResult<CourseInfo> findSubjectInfoByTopic(Integer page, Integer pageSize, String currentUserId,
                                                          String topicId,
                                                          Optional<String> name, Optional<String> organization, Optional<Integer> publishClient, Optional<Integer> type) {
        List<Condition> conditions = Stream.of(name.map(COURSE_INFO.NAME::contains),
                organization.map(COURSE_INFO.ORGANIZATION_ID::eq),
                publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
        conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
        conditions.add(BUSINESS_TOPIC.TOPIC_ID.eq(topicId));
        if (type.isPresent()) {
            if (type.get() == CourseInfo.GENERAL) {//普通
                conditions.add(COURSE_INFO.URL.isNull());
            } else if (type.get() == CourseInfo.PERSONALIZATION) {//个性化
                conditions.add(COURSE_INFO.URL.isNotNull());
            }
        }

        SelectOnConditionStep<Record> stepSelect = courseInfoCommonDao.execute(e -> e.selectDistinct(Fields.start()
                .add(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.PUBLISH_CLIENT, COURSE_INFO.URL)
                .add(ORGANIZATION.NAME).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT)))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .leftJoin(BUSINESS_TOPIC).on(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID).and(BUSINESS_TOPIC.BUSINESS_TYPE.eq(BusinessTopic.BUSINESS_TYPE_SUBJECT)))
        );

        SelectOnConditionStep<Record> stepCount = courseInfoCommonDao.execute(e -> e.select(Fields.start()
                .add(COURSE_INFO.ID.countDistinct()).end())
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID).and(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT)))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .leftJoin(BUSINESS_TOPIC).on(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID).and(BUSINESS_TOPIC.BUSINESS_TYPE.eq(BusinessTopic.BUSINESS_TYPE_SUBJECT)))
        );

        int firstResult = (page - 1) * pageSize;
        Integer count = stepCount.where(conditions).fetchOne().getValue(0, Integer.class);
        List<CourseInfo> items = new ArrayList<>();
        items = stepSelect.where(conditions).orderBy(COURSE_INFO.CREATE_TIME.desc()).limit(firstResult, pageSize)
                .fetch(item -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(item.getValue(COURSE_INFO.ID));
                    courseInfo.setName(item.getValue(COURSE_INFO.NAME));
                    courseInfo.setPublishClient(item.getValue(COURSE_INFO.PUBLISH_CLIENT));
                    courseInfo.setOrganizationName(item.getValue(ORGANIZATION.NAME));
                    courseInfo.setUrl(item.getValue(COURSE_INFO.URL));
                    return courseInfo;
                });
        return PagedResult.create(count, items);
    }

    /**
     * 批量新增课程标签
     *
     * @param topicId     标签id
     * @param businessIds 课程id
     * @return
     */
    public void insertCourseTopicInfo(String topicId, String[] businessIds) {
        for (String businessId : businessIds) {
            businessTopicService.insertBusinessTopic(businessId, BusinessTopic.BUSINESS_TYPE_COURSE, new String[]{topicId});
        }
    }

    /**
     * 批量删除课程标签
     *
     * @param topicId     标签id
     * @param businessIds 课程id
     * @return
     */
    public void deleteCourseTopicInfo(String topicId, String[] businessIds) {
        for (String businessId : businessIds) {
            businessTopicService.deleteBusinessTopic(businessId, BusinessTopic.BUSINESS_TYPE_COURSE, new String[]{topicId});
        }
    }

    /**
     * 批量新增专题标签
     *
     * @param topicId     标签id
     * @param businessIds 专题id
     * @return
     */
    public void insertSubjectTopicInfo(String topicId, String[] businessIds) {
        for (String businessId : businessIds) {
            businessTopicService.insertBusinessTopic(businessId, BusinessTopic.BUSINESS_TYPE_SUBJECT, new String[]{topicId});
        }
    }

    /**
     * 批量删除专题标签
     *
     * @param topicId     标签id
     * @param businessIds 专题id
     * @return
     */
    public void deleteSubjectTopicInfo(String topicId, String[] businessIds) {
        for (String businessId : businessIds) {
            businessTopicService.deleteBusinessTopic(businessId, BusinessTopic.BUSINESS_TYPE_SUBJECT, new String[]{topicId});
        }
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public List<CourseInfo> getBottomDataCourseOrSubjectIds(int offset, int limit, String currentUserId) {
        int audienceCourseType = 1;
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        com.zxy.product.course.jooq.tables.Organization releaseOrganizationTable = ORGANIZATION.as(
                "releaseOrganization"); // 发布部门
        com.zxy.product.course.jooq.tables.Member createUserTable = MEMBER.as("createUser"); // 创建人
        com.zxy.product.course.jooq.tables.Member releaseUserTable = MEMBER.as("releaseUser"); // 发布人
        return courseInfoCommonDao.execute(dslContext -> dslContext.selectDistinct(COURSE_INFO.ID,
                COURSE_INFO.BUSINESS_TYPE).from(COURSE_INFO)
                .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                .leftJoin(createUserTable).on(createUserTable.ID.eq(COURSE_INFO.CREATE_MEMBER_ID))
                .leftJoin(releaseUserTable).on(releaseUserTable.ID.eq(COURSE_INFO.RELEASE_MEMBER_ID))
                .leftJoin(releaseOrganizationTable).on(releaseOrganizationTable.ID.eq(COURSE_INFO.RELEASE_ORG_ID))
                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .where(COURSE_INFO.BUSINESS_TYPE.in(CourseInfo.BUSINESS_TYPE_COURSE,
                        CourseInfo.BUSINESS_TYPE_SUBJECT)
                        .and(COURSE_INFO.PUBLISH_TYPE.in(CourseInfo.PUBLISH_TYPE_MEMBER,
                                CourseInfo.PUBLISH_TYPE_FORMAL))
                        .and(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()))
                        .and(COURSE_INFO.STATUS.in(CourseInfo.STATUS_SHELVES, CourseInfo.STATUS_THE_TEST)))
                .and(organizationTable.ID.isNotNull())
                .and(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)))
                .and(COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE).or(COURSE_CATEGORY.STATE.isNull()))
                .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                .and(AUDIENCE_OBJECT.BUSINESS_TYPE.in(audienceCourseType, CourseInfo.BUSINESS_TYPE_SUBJECT))
        ).orderBy(COURSE_INFO.RELEASE_TIME.desc())
                .limit(offset, limit).fetch(record -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(record.getValue(COURSE_INFO.ID));
                    courseInfo.setBusinessType(record.getValue(COURSE_INFO.BUSINESS_TYPE));
                    return courseInfo;
                });

    }

    @Override
    public Long sumVisits(List<String> ids) {
        return courseInfoCommonDao.execute(dsl ->
                dsl.select(DSL.sum(DSL.ifnull(COURSE_INFO.VISITS, 0)))
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ID.in(ids))
                        .fetchOne()
                        .getValue(0, Long.class)
        );
    }

    @Override
    public List<CourseInfo> getCoverPaths(List<String> ids) {
        return courseInfoCommonDao.execute(dsl ->
                dsl.select(COURSE_INFO.ID, COURSE_INFO.COVER_PATH)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ID.in(ids))
                        .fetchInto(CourseInfo.class)
        );
    }
    @Override
    public Map<String, String> getMapCoverPaths(List<String> ids) {
        return courseInfoCommonDao.execute(dsl ->
                dsl.select(COURSE_INFO.ID, COURSE_INFO.COVER_PATH)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ID.in(ids)
                                .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_ENABLED))
                        )
                        .fetchMap(COURSE_INFO.ID,COURSE_INFO.COVER_PATH)
        );
    }

    @Override
    public List<CourseInfo> getVisitsByIds(List<String> ids) {
        return courseInfoCommonDao.execute(dsl ->
                dsl.select(COURSE_INFO.ID, COURSE_INFO.VISITS)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ID.in(ids))
                        .fetchInto(CourseInfo.class)
        );
    }

    @Override
    public List<CourseInfo> getCountInformationIds(List<String> ids) {
        return courseInfoCommonDao.execute(dsl ->
                dsl.select(COURSE_INFO.ID, COURSE_INFO.VISITS, COURSE_INFO.AVG_SCORE, MEMBER.FULL_NAME, MEMBER.NAME)
                        .from(COURSE_INFO).leftJoin(MEMBER).on(COURSE_INFO.CREATE_MEMBER_ID.eq(MEMBER.ID))
                        .where(COURSE_INFO.ID.in(ids))
                        .fetch(f -> {
                            CourseInfo courseInfo = f.into(COURSE_INFO).into(CourseInfo.class);
                            courseInfo.setCreateUser(f.into(MEMBER).into(Member.class));
                            return courseInfo;
                        })
        );
    }

    @Override
    public List<CourseInfo> findByRecommendCourseIds(List<String> courseIds, Integer page, Integer pageSize, Integer publishClient, String currentUserId) {

        return courseInfoCommonDao.execute(e -> {
            // 获取受众为中国移动以及内部组织的受众项
            List<String> itemIds = courseCacheService.getCacheAudienceItemIds();
            Condition conditions = Stream.of(
                            Optional.of(COURSE_INFO.ID.in(courseIds)),
                            Optional.of(COURSE_INFO.STATUS.in(CourseInfo.STATUS_SHELVES,
                                    CourseInfo.STATUS_THE_TEST)),
                            Optional.of(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES))),
                            Optional.of(COURSE_INFO.PUBLISH_CLIENT.eq(publishClient)),
                            Optional.of(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COURSE)),
                            Optional.of(AUDIENCE_OBJECT.ITEM_ID.in(itemIds)))
                    .filter(Optional::isPresent).map(Optional::get)
                    .reduce((a, b) -> a.and(b)).get();

            return e.select(Fields.start()
                            .add(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.COVER, COURSE_INFO.COVER_PATH,
                                    COURSE_INFO.CREATE_TIME, COURSE_INFO.URL).end())
                    .from(COURSE_INFO).innerJoin(AUDIENCE_OBJECT).on(COURSE_INFO.ID.eq(AUDIENCE_OBJECT.BUSINESS_ID))
                    .where(conditions)
                    .fetchInto(CourseInfo.class);

        });
    }

    private static int timeLeftInSeconds() {
        long now = System.currentTimeMillis();
        long today24 = DateUtil.getTimesnightByLong(now);
        return (int)(today24 - now) / 1000;
    }


    /**
     * 查询领学人在本次活动范围内,所有的课程都学习总和
     * @param leaderMemberId 领学人id
     * @param courseIds 课程id集合
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    @Override
    public Map<String, Object> findByLeaderStudyTime(String leaderMemberId, List<String> courseIds, Long beginTime, Long endTime) {
        String path = orgDao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).leftJoin(MEMBER).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(MEMBER.ID.eq(leaderMemberId)).fetchOne(ORGANIZATION.PATH));
        Map<String,Object> map=new HashMap<>();
        if (ObjectUtils.isEmpty(path)) {
            logger.error("根据memberID无法找到对应的组织信息，无法获取领学人学习时长，memberId={}", leaderMemberId);
            map.put("error",-1);
            return map;
        }
        logger.info("根据leaderMemberId查询出来的path={}, memberId={}", path, leaderMemberId);

        //查询表名
        TableImpl<?> table = courseCacheService.getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
        logger.info("表名:{}", table);
        List<CourseSectionStudyLog> execute = courseInfoCommonDao.execute(o ->
                o.select(table.field("f_study_time").sum(),table.field("f_course_id"))
                        .from(table)
                        .where(table.field("f_member_id", String.class).eq(leaderMemberId))
                        .and(table.field("f_course_id").in(courseIds))
                        .and(table.field("f_create_time", Long.class).between(beginTime).and(endTime))
                        .groupBy(table.field("f_course_id"))
                        .fetch(r->{
                            CourseSectionStudyLog courseSectionStudyLog=new CourseSectionStudyLog();
                            courseSectionStudyLog.setCourseId(r.getValue(table.field("f_course_id"),String.class));
                            courseSectionStudyLog.setStudyTime(r.getValue(table.field("f_study_time").sum(),Integer.class));
                           return  courseSectionStudyLog;
                        })
        );

        Integer studyTime=0;

        for (CourseSectionStudyLog courseSectionStudyLog : execute) {
            if (courseSectionStudyLog.getStudyTime() != null){
                studyTime+=courseSectionStudyLog.getStudyTime();
            }
        }

        map.put("course",execute);
        map.put("totalStudyTime",studyTime);
       return map;
    }

    /**
     * 查询领学人在本次活动范围内,每门课程都学习时长
     * @param leaderMemberId 领学人id
     * @param courseIds 课程id集合
     * @param beginTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    @Override
    public List<Map<String,Object>> findByLeaderStudyTime1(String leaderMemberId, List<String> courseIds, Long beginTime, Long endTime) {
        String path = orgDao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).leftJoin(MEMBER).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(MEMBER.ID.eq(leaderMemberId)).fetchOne(ORGANIZATION.PATH));

        List<Map<String, Object>> result = new ArrayList<>();
        Map<String,Object> map=new HashMap<>();
        if (ObjectUtils.isEmpty(path)) {
            logger.error("根据memberID无法找到对应的组织信息，无法获取领学人学习时长，memberId={}", leaderMemberId);
            map.put("error",-1);
            result.add(map);
            return result;
        }
        logger.info("根据leaderMemberId查询出来的path={}, memberId={}", path, leaderMemberId);

        //查询表名
        TableImpl<?> table = courseCacheService.getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
        logger.info("表名:{}", table);
        result = courseInfoCommonDao.execute(o ->
                o.select(table.field("f_study_time").sum(),table.field("f_course_id"))
                        .from(table)
                        .where(table.field("f_member_id", String.class).eq(leaderMemberId))
                        .and(table.field("f_course_id").in(courseIds))
                        .and(table.field("f_create_time", Long.class).between(beginTime).and(endTime))
                        .groupBy(table.field("f_course_id"))
                        .fetch(r->{
                            Map<String,Object> courseSectionStudyLogMap = new HashMap();
                            courseSectionStudyLogMap.put("courseId",r.getValue(table.field("f_course_id"),String.class));
                            courseSectionStudyLogMap.put("studyTime",r.getValue(table.field("f_study_time").sum(),Integer.class));
                            return  courseSectionStudyLogMap;
                        })
        );
        return result;
    }
    @Override
    public List<Map<String,Object>> findByLeaderStudyTime2(String leaderMemberId, List<String> courseIds, Long beginTime, Long endTime) {
        String path = orgDao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).leftJoin(MEMBER).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(MEMBER.ID.eq(leaderMemberId)).fetchOne(ORGANIZATION.PATH));

        List<Map<String, Object>> result = new ArrayList<>();
        Map<String,Object> map=new HashMap<>();
        if (ObjectUtils.isEmpty(path)) {
            logger.error("根据memberID无法找到对应的组织信息，无法获取领学人学习时长，memberId={}", leaderMemberId);
            map.put("error",-1);
            result.add(map);
            return result;
        }

        //查询表名
        TableImpl<?> table = courseCacheService.getCacheTable2(path, SplitTableConfig.COURSE_SECTION_STUDY_LOG);
        result = courseInfoCommonDao.execute(o ->
                o.select(table.field("f_study_time").sum(),table.field("f_course_id"))
                        .from(table)
                        .where(table.field("f_member_id", String.class).eq(leaderMemberId))
                        .and(table.field("f_course_id").in(courseIds))
                        .and(table.field("f_create_time", Long.class).lt(endTime))
                        .and(table.field("f_commit_time", Long.class).gt(beginTime))
                        .groupBy(table.field("f_course_id"))
                        .fetch(r->{
                            Map<String,Object> courseSectionStudyLogMap = new HashMap();
                            courseSectionStudyLogMap.put("courseId",r.getValue(table.field("f_course_id"),String.class));
                            courseSectionStudyLogMap.put("studyTime",r.getValue(table.field("f_study_time").sum(),Integer.class));
                            return  courseSectionStudyLogMap;
                        })
        );
        return result;
    }

    //热门内容更新
    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public List<CourseInfo> hotVisitsListNew(List<String> courseListContentId){
        return courseInfoCommonDao.execute(e ->
                    e.select(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.VISITS, COURSE_INFO.BUSINESS_TYPE, COURSE_INFO.COVER_PATH, COURSE_INFO.PUBLISH_CLIENT)
                            .from(COURSE_INFO)
                            .where(COURSE_INFO.ORGANIZATION_ID.eq("1"))
                            .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                            .and(COURSE_INFO.ID.in(courseListContentId))
                            .limit(0, 10).fetchInto(CourseInfo.class));
    }

    @Override
    public void setHotVisitsCache(String key,List<CourseInfo> hotLists){
        cache.set(key, hotLists);
    }

    @Override
    public List<CourseInfo> getHotVisitsCache(String key){
        return cache.get(key,List.class);
    }

    @Override
    public void setStringCache(String key,String value){
        cache.set(key, value);
    }

    @Override
    public String getStringCache(String key){
        return cache.get(key, String.class);
    }

    @Override
    public PagedResult<CourseInfo> findStudyPlanRecommend(Integer page,Integer pageSize,String currentUserId, Integer type) {
        //生成查询条件
        List<Condition> conditions = createStudyPlanRecommendConditions(currentUserId, type);
        //查询课程\专题
        List<CourseInfo> courseInfos = findStudyPlanRecommendCourses(page, pageSize, conditions);
        Integer count = countStudyPlanRecommendCourses(conditions);

        List<String> courseIds = courseInfos.stream().map(CourseInfo::getId).collect(Collectors.toList());
        //查询当前用户课程版本
        Map<String, String> courseVersionsMap = courseStudyProgressService.findCourseVersionIds(currentUserId, courseIds);
        //查询章节
        Map<String, List<CourseChapterSection>> sectionMap = findCourseSectionMap(courseVersionsMap.keySet(),courseVersionsMap.values());
        //查询标签
        Map<String, List<CourseTopic>> topicMap = findCourseTopicMap(courseIds);

        courseInfos.forEach(c->{
            List<CourseChapterSection> sections = sectionMap.get(c.getId());
            Optional.ofNullable(sections).ifPresent(s->s.sort(Comparator.comparingInt(CourseChapterSection::getSequence)));
            c.setCourseChapterSections(sections);
            c.setTopicNames( Optional.ofNullable(topicMap.get(c.getId())).map(courseTopics -> courseTopics.stream().map(CourseTopic::getTopicName).reduce((a,b)->a+","+b).orElse("")).orElse(""));
        });

        return PagedResult.create(count,courseInfos);
    }

    private Map<String, List<CourseTopic>> findCourseTopicMap(List<String> courseIds) {
        return businessTopicCommonDao.execute(dsl -> dsl.select(COURSE_TOPIC.COURSE_ID,COURSE_TOPIC.TOPIC_NAME)
                .from(COURSE_TOPIC)
                .where(COURSE_TOPIC.COURSE_ID.in(courseIds))
                .fetch(r -> {
                    CourseTopic topic = new CourseTopic();
                    topic.setCourseId(r.getValue(COURSE_TOPIC.COURSE_ID));
                    topic.setTopicName(r.getValue(COURSE_TOPIC.TOPIC_NAME));
                    return topic;
                })).stream().collect(groupingBy(CourseTopic::getCourseId));
    }

    private Map<String, List<CourseChapterSection>> findCourseSectionMap(Collection<String> courseIds, Collection<String> versionIds) {
        return courseChapterCommonDao.execute(dsl -> dsl.select(Fields.start()
                        .add(COURSE_CHAPTER_SECTION.ID, COURSE_CHAPTER_SECTION.COURSE_ID,COURSE_CHAPTER_SECTION.REFERENCE_ID,
                                COURSE_CHAPTER_SECTION.NAME, COURSE_CHAPTER_SECTION.SECTION_TYPE, COURSE_CHAPTER_SECTION.REQUIRED,COURSE_CHAPTER_SECTION.SEQUENCE)
                        .end())
                .from(COURSE_CHAPTER)
                .rightJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .where(COURSE_CHAPTER.COURSE_ID.in(courseIds))
                .and(COURSE_CHAPTER.VERSION_ID.in(versionIds))
                .fetch(r -> {
                    CourseChapterSection section = new CourseChapterSection();
                    section.setId(r.getValue(COURSE_CHAPTER_SECTION.ID));
                    section.setCourseId(r.getValue(COURSE_CHAPTER_SECTION.COURSE_ID));
                    section.setReferenceId(r.getValue(COURSE_CHAPTER_SECTION.REFERENCE_ID));
                    section.setName(r.getValue(COURSE_CHAPTER_SECTION.NAME));
                    section.setSectionType(r.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                    section.setRequired(r.getValue(COURSE_CHAPTER_SECTION.REQUIRED));
                    section.setSequence(r.getValue(COURSE_CHAPTER_SECTION.SEQUENCE));
                    return section;
                })).stream().collect(groupingBy(CourseChapterSection::getCourseId));
    }

    private Integer countStudyPlanRecommendCourses(List<Condition> conditions) {
        return courseInfoCommonDao.execute(dsl -> dsl.selectCount().from(COURSE_INFO)
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                .where(conditions).fetchOne().value1());
    }

    private List<CourseInfo> findStudyPlanRecommendCourses(Integer page, Integer pageSize, List<Condition> conditions) {
        return courseInfoCommonDao.execute(dsl ->
                dsl.selectDistinct(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.BUSINESS_TYPE,
                                COURSE_INFO.COVER, COURSE_INFO.COVER_PATH, COURSE_INFO.ADD_PLAN_MEMBER_COUNT, COURSE_INFO.VERSION_ID)
                        .from(COURSE_INFO)
                        .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                        .where(conditions)
                        .orderBy(COURSE_INFO.ADD_PLAN_MEMBER_COUNT.desc(), COURSE_INFO.STUDY_MEMBER_COUNT.desc())
                        .limit((page - 1) * pageSize, pageSize)
                        .fetch(r -> {
                            CourseInfo info = new CourseInfo();
                            info.setId(r.getValue(COURSE_INFO.ID));
                            info.setName(r.getValue(COURSE_INFO.NAME));
                            info.setBusinessType(r.getValue(COURSE_INFO.BUSINESS_TYPE));
                            info.setCover(r.getValue(COURSE_INFO.COVER));
                            info.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH));
                            info.setAddPlanMemberCount(r.getValue(COURSE_INFO.ADD_PLAN_MEMBER_COUNT));
                            info.setVersionId(r.getValue(COURSE_INFO.VERSION_ID));
                            return info;
                        }));
    }

    private List<Condition> createStudyPlanRecommendConditions(String currentUserId, Integer type) {
        //type转化为课程businessType
        int businessType = Objects.equals(type, AudienceObject.BUSINESS_TYPE_COURSE) ? CourseInfo.BUSINESS_TYPE_COURSE:CourseInfo.BUSINESS_TYPE_SUBJECT;

        List<Condition> conditions = Lists.newArrayList();
        conditions.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()));
        conditions.add(COURSE_INFO.VERSION_ID.isNotNull());
        conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
        conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(businessType));
        conditions.add(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));
        conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
        conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(type));
        return conditions;
    }

    @Override
    public Map<String, List<CourseChapterSection>> findChapterSectionByCourseIds(String memberId,List<String> courseIds) {

        //查询当前用户课程版本
        Map<String, String> courseVersionsMap = courseStudyProgressService.findCourseVersionIds(memberId, courseIds);

        return courseChapterCommonDao.execute(dsl -> dsl.select(Fields.start()
                        .add(COURSE_CHAPTER_SECTION.ID, COURSE_CHAPTER_SECTION.REFERENCE_ID, COURSE_CHAPTER_SECTION.COURSE_ID,COURSE_CHAPTER_SECTION.NAME, COURSE_CHAPTER_SECTION.SECTION_TYPE, COURSE_CHAPTER_SECTION.REQUIRED)
                        .end())
                .from(COURSE_INFO)
                .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER.COURSE_ID.eq(COURSE_INFO.ID))
                .innerJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .where(COURSE_INFO.ID.in(courseVersionsMap.keySet()))
                .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                .and(COURSE_CHAPTER.VERSION_ID.in(courseVersionsMap.values()))
                .orderBy(COURSE_CHAPTER.SEQUENCE)
                .fetch(r -> {
                    CourseChapterSection section = new CourseChapterSection();
                    section.setId(r.getValue(COURSE_CHAPTER_SECTION.ID));
                    section.setCourseId(r.getValue(COURSE_CHAPTER_SECTION.COURSE_ID));
                    section.setReferenceId(r.getValue(COURSE_CHAPTER_SECTION.REFERENCE_ID));
                    section.setName(r.getValue(COURSE_CHAPTER_SECTION.NAME));
                    section.setSectionType(r.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                    section.setRequired(r.getValue(COURSE_CHAPTER_SECTION.REQUIRED));
                    return section;
                })).stream().collect(groupingBy(CourseChapterSection::getCourseId));
    }

    @Override
    public Map<String, Integer> findCourseStudyPlanAddedCount(List<String> courseIds) {
        return courseInfoCommonDao.execute(dsl-> dsl.select(COURSE_INFO.ID,COURSE_INFO.ADD_PLAN_MEMBER_COUNT)
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.in(courseIds))
                .fetchMap(COURSE_INFO.ID,COURSE_INFO.ADD_PLAN_MEMBER_COUNT));
    }

    @Override
    public String findCourseNameById(String courseId) {
        return courseInfoCommonDao.execute(dsl-> dsl.select(COURSE_INFO.NAME)
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.eq(courseId))
                .fetchOne(COURSE_INFO.NAME));
    }

    @Override
    public List<CourseInfo> getAllCourseChapterInfo(String courseId,String memberId,Integer businessType){
        List<CourseInfo> list = new ArrayList<>();
        if (businessType.equals(CourseInfo.BUSINESS_TYPE_SUBJECT)){
            CourseInfo course = courseInfoCommonDao.execute(dslContext -> dslContext
                    .select(COURSE_INFO.VERSION_ID)
                    .from(COURSE_INFO)
                    .where(COURSE_INFO.ID.eq(courseId))
                    .fetchOne(r->{
                        CourseInfo courseInfo = new CourseInfo();
                        courseInfo.setVersionId(r.getValue(COURSE_INFO.VERSION_ID));
                        return courseInfo;
                    })
            );
            courseChapterCommonDao.execute(x -> x
                    .select(COURSE_CHAPTER_SECTION.RESOURCE_ID).from(COURSE_CHAPTER)
                    .innerJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                    .where(COURSE_CHAPTER.COURSE_ID.eq(courseId).and(COURSE_CHAPTER.VERSION_ID.eq(course.getVersionId())))
                    .orderBy(COURSE_CHAPTER.SEQUENCE.desc(), COURSE_CHAPTER_SECTION.SEQUENCE.asc()))
                    .fetch(COURSE_CHAPTER_SECTION.RESOURCE_ID)
                    .forEach( id -> {
                        if (id!=null){
                            CourseInfo courseSlat = getSimpleInfo(id);
                            CourseInfo courseInfo =  getCourseChapterInfo(courseSlat,memberId);
                            if (courseInfo!=null){
                                list.add(courseInfo);
                            }
                        }
                    });
        }else{
            CourseInfo course = getSimpleInfo(courseId);
            CourseInfo courseInfo = getCourseChapterInfo(course,memberId);
            if (courseInfo!=null){
                list.add(courseInfo);
            }
        }
        return list;
    }

    private CourseInfo getSimpleInfo(String courseId) {
        return courseInfoCommonDao.execute(dslContext -> dslContext
                .select(COURSE_INFO.NAME, COURSE_INFO.VERSION_ID, COURSE_INFO.SEQUENCE)
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.eq(courseId))
                .fetchOne(r -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(courseId);
                    courseInfo.setName(r.getValue(COURSE_INFO.NAME));
                    courseInfo.setVersionId(r.getValue(COURSE_INFO.VERSION_ID));
                    courseInfo.setSequence(r.getValue(COURSE_INFO.SEQUENCE));
                    return courseInfo;
                })
        );
    }


    private CourseInfo getCourseChapterInfo(CourseInfo course,String memberId){
        //课的map
        if (!this.isAudience(course.getId(),memberId)){
            return null;
        }
        CourseInfo courseInfo = new CourseInfo();
        String courseVersion;
        if (course.getVersionId() == null){
            //按照正常的查课的结构去查
            courseVersion = courseStudyProgressService.findByMemberIdAndCourseId(memberId, course.getId())
                    .map(CourseStudyProgressEntity::getCourseVersionId).orElse(course.getVersionId());
        }else{
            courseVersion = course.getVersionId();
        }
        // 查询章节和章节详情
        List<CourseChapter> courseChapters = courseCacheService.getCourseChapter(course.getId(), courseVersion);
        courseInfo.setId(course.getId());
        courseInfo.setName(course.getName());
        courseInfo.setSequence(course.getSequence());
        //课程下的课件组
        List<CourseChapterSection> courseChapterList = new ArrayList<>();
        courseChapters.forEach(courseChapter -> {
            courseChapter.getCourseChapterSections().forEach(courseChapterSection -> {
                //响应信息map，以下key均为春丽定义的
                CourseChapterSection chapterSection = new CourseChapterSection();
                //章节信息
                chapterSection.setName(courseChapterSection.getName());
                // 文档的资源id
                chapterSection.setResourceId(courseChapterSection.getResourceId());
                //课件id
                chapterSection.setId(courseChapterSection.getId());

                chapterSection.setSequence(courseChapterSection.getSequence());

                chapterSection.setReferenceId(courseChapterSection.getReferenceId());
                chapterSection.setSectionType(courseChapterSection.getSectionType());
                courseChapterList.add(chapterSection);

            });
        });
        List<CourseChapterSection> findByAttachment = intelligentBroadcastCommonDao.execute(r ->
                r.select(INTELLIGENT_BROADCAST.ATTACHMENT_AUDIO_ID, INTELLIGENT_BROADCAST.ATTACHMENT_ID, INTELLIGENT_BROADCAST.TEXT_ID)
                        .from(INTELLIGENT_BROADCAST)
                        .where(INTELLIGENT_BROADCAST.ATTACHMENT_ID.in(courseChapterList.stream().map(CourseChapterSectionEntity::getResourceId).collect(Collectors.toList()))
                                .and(INTELLIGENT_BROADCAST.FORBIDDEN.eq(IntelligentBroadcast.TO_ENABLE_THE)))
                        .fetch(o -> {
                            CourseChapterSection intelligentBroadcast = new CourseChapterSection();
                            intelligentBroadcast.setAttachmentAudioId(o.getValue(INTELLIGENT_BROADCAST.ATTACHMENT_AUDIO_ID));
                            intelligentBroadcast.setAttachmentId(o.getValue(INTELLIGENT_BROADCAST.ATTACHMENT_ID));
                            intelligentBroadcast.setIntelligentBroadcastTxtId(o.getValue(INTELLIGENT_BROADCAST.TEXT_ID));
                            return intelligentBroadcast;
                        })
        );
        if (findByAttachment.isEmpty()){
            return null;
        }
        List<CourseChapterSection> courseChapterSections = courseChapterList.stream().filter(courseChapter -> courseChapter.getSectionType() != CourseChapterSection.SECTION_TYPE_URL).collect(toList());
        Map<String,List<CourseChapterSection>> courseChapterSectionMap = courseChapterSections.stream().collect(groupingBy(CourseChapterSection::getResourceId));
        findByAttachment.forEach(fba->{
            if (courseChapterSectionMap.get(fba.getAttachmentId())!=null){
                fba.setName(courseChapterSectionMap.get(fba.getAttachmentId()).get(0).getName());
                fba.setResourceId(courseChapterSectionMap.get(fba.getAttachmentId()).get(0).getResourceId());
                fba.setReferenceId(courseChapterSectionMap.get(fba.getAttachmentId()).get(0).getReferenceId());
                fba.setId(courseChapterSectionMap.get(fba.getAttachmentId()).get(0).getId());
                fba.setSequence(courseChapterSectionMap.get(fba.getAttachmentId()).get(0).getSequence());
                fba.setSectionType(courseChapterSectionMap.get(fba.getAttachmentId()).get(0).getSectionType());
            }
        });
        findByAttachment.sort(Comparator.comparing(CourseChapterSection::getSequence));
        courseInfo.setCourseChapterSections(findByAttachment);
        return courseInfo;

    }

    private void setIntelligentBroadcast(List<CourseChapter> courseChapters){
        List<String> resourceIds = courseChapters.stream()
                .flatMap(courseChapter -> courseChapter.getCourseChapterSections().stream())
                .map(CourseChapterSectionEntity::getResourceId).distinct()
                .collect(Collectors.toList());

        Map<String, String> auuachmentAudioMap = intelligentBroadcastCommonDao.execute(r ->
                r.select(INTELLIGENT_BROADCAST.ATTACHMENT_AUDIO_ID, INTELLIGENT_BROADCAST.ATTACHMENT_ID)
                        .from(INTELLIGENT_BROADCAST)
                        .where(INTELLIGENT_BROADCAST.ATTACHMENT_ID.in(resourceIds)
                                .and(INTELLIGENT_BROADCAST.FORBIDDEN.eq(IntelligentBroadcast.TO_ENABLE_THE)))
                        .fetchMap(INTELLIGENT_BROADCAST.ATTACHMENT_ID, INTELLIGENT_BROADCAST.ATTACHMENT_AUDIO_ID)
        );

        courseChapters.forEach(courseChapter -> {
            courseChapter.getCourseChapterSections().forEach(courseChapterSection -> {
                courseChapterSection.setAttachmentAudioId(auuachmentAudioMap.get(courseChapterSection.getResourceId()));
            });
        });
    }


    // 判断是否被归档 当前记录
    public Boolean checkArchived(String memberId, String courseId) {
        return courseInfoCommonDao.execute(dsl -> {
            TableImpl<?> table = CourseStudyProgressArchivedUtil.getTable(memberId, courseId);
            Integer count = dsl
                    .select(DSL.count())
                    .from(table)
                    .where(CourseStudyProgressArchivedUtil.MEMBER_ID(table)
                            .eq(memberId).and(CourseStudyProgressArchivedUtil.COURSE_ID(table).eq(courseId)))
                    .limit(1).fetchOne(r -> r.value1());
            return count > 0;
        });
    }
    @Override
    public Boolean existedVersionId(String courseId) {
        return courseInfoCommonDao.execute(dsl ->
                dsl.select(DSL.count())
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ID.eq(courseId).and(COURSE_INFO.VERSION_ID.isNotNull()))
                        .limit(1)
                        .fetchOne(Record1::value1)) > 0;
    }


    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public Map<String, Object> findCourseVirtualSpaceList(String currentUserId,
                                                          Integer page,
                                                          Integer pageSize,
                                                          Optional<Integer> orderBy,
                                                          Optional<Integer> order,
                                                          Optional<String> searchContent,
                                                          Optional<String> categoryId,
                                                          Optional<String> topicId,
                                                          Optional<Integer> type,
                                                          Optional<Integer> publishClient,
                                                          Optional<Integer> companyType,
                                                          Integer showField,
                                                          List<String> businessId,
                                                          Optional<Integer> isAdd,
                                                          String virtualSpacesId,
                                                          String virtualSpacesOrganizationId,
                                                          List<String> byVirtualSpacesForbidden) {
        Map<String, Object> result = Maps.newHashMap();
        //是否还有下一页
        final int[] more = {0};
        PagedResult<CourseInfo> pageList = courseInfoCommonDao.execute(context -> {
            int defaultOrderBy = 0;
            int defaultOrder = 2;

            List<Condition> conditions = Stream.of(
                    searchContent.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    categoryId.map(COURSE_CATEGORY.PATH::contains),
                    publishClient.map(p -> COURSE_INFO.PUBLISH_CLIENT.eq(p).or(COURSE_INFO.PUBLISH_CLIENT.eq(0)))
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            conditions.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()));
            conditions.add(COURSE_INFO.VERSION_ID.isNotNull());
            conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
            conditions.add(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));
            conditions.add(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId));
            conditions.add(COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE).or(COURSE_CATEGORY.STATE.isNull()));
            conditions.add(COURSE_INFO.IS_PARTY.eq(CourseInfo.PARTY_BUILING_COURSE_FLASE).or(COURSE_INFO.IS_PARTY.isNull()));
            conditions.add(COURSE_INFO.ID.notIn(byVirtualSpacesForbidden));


            //专题还是课程
            if (type.isPresent()) {
                if (Objects.equals(type.get(),CourseInfo.BUSINESS_TYPE_SUBJECT)){
                    conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(type.get()));
                    conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_SUBJECT));
                }else {
                    conditions.add(COURSE_INFO.BUSINESS_TYPE.eq(type.get()));
                    conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.eq(AudienceObject.BUSINESS_TYPE_COURSE));
                }
            }else {
                conditions.add(COURSE_INFO.BUSINESS_TYPE.in(CourseInfo.BUSINESS_TYPE_COURSE,CourseInfo.BUSINESS_TYPE_SUBJECT));
                conditions.add(AUDIENCE_OBJECT.BUSINESS_TYPE.in(AudienceObject.BUSINESS_TYPE_COURSE,AudienceObject.BUSINESS_TYPE_SUBJECT));
            }


            List<String> courseInfoIds = null;

            //查询数据范围
            if (isAdd.isPresent()){
                conditions.add(COURSE_INFO.ID.in(businessId));
            } else if (companyType.isPresent() && Objects.equals(companyType.get(),1)) {
                //集团 - 只查询中国移动分享的课程,并且已经加入资源池,且是启用状态
                conditions.add(COURSE_INFO.ID.in(businessId).and(COURSE_INFO.ORGANIZATION_ID.eq("1")));
            } else if (companyType.isPresent() && Objects.equals(companyType.get(),3)) {
                //排除中国移动,只查询属于虚拟空间的上级组织分享的课程,并且已经加入资源池,且是启用状态
                conditions.add(COURSE_INFO.ID.in(businessId).and(COURSE_INFO.ORGANIZATION_ID.ne("1")));
            } else{
                //根据当前组织的id,查询path
                Optional<Organization> organization = organizationService.get(virtualSpacesOrganizationId);
                //查询全部
                if (companyType.isPresent() && Objects.equals(companyType.get(), 0)) {
                    courseInfoIds = findIds(conditions, businessId);
                }
                if (organization.isPresent()) {
                    conditions.add(ORGANIZATION.ID.isNotNull().and(ORGANIZATION.PATH.like(organization.get().getPath() + "%")).and(ORGANIZATION.STATUS.eq(Organization.ORGANIZATION_STATUS)));
                } else {
                    //默认包含子部门和追加的课程
                    conditions.add(ORGANIZATION.ID.isNotNull().and(ORGANIZATION.PATH.like("1,%")).and(ORGANIZATION.STATUS.eq(Organization.ORGANIZATION_STATUS)));
                }
            }

            if (topicId.isPresent()){
                conditions.add(BUSINESS_TOPIC.TOPIC_ID.eq(topicId.get()));
            }

//            if (topicId.isPresent()){
//                List<String> ids = context.select(COURSE_INFO.ID)
//                        .from(BUSINESS_TOPIC)
//                        .leftJoin(COURSE_INFO).on(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID))
//                        .leftJoin(COURSE_INFO_CATEGORY).on(COURSE_INFO_CATEGORY.COURSE_INFO_ID.eq(COURSE_INFO.ID))
//                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO_CATEGORY.COURSE_CATEGORY_ID))
//                        .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
//                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
//                        .leftJoin(ORGANIZATION).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION.ID))
//                        .where(conditions)
//                        .fetch(COURSE_INFO.ID);
//
//                if (ObjectUtils.isEmpty(ids)){
//                    return PagedResult.create(0, Lists.newArrayList());
//                }
//                conditions.add(COURSE_INFO.ID.in(ids));
//            }


            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                return a.from(COURSE_INFO)
                                .leftJoin(COURSE_INFO_CATEGORY).on(COURSE_INFO_CATEGORY.COURSE_INFO_ID.eq(COURSE_INFO.ID))
                                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO_CATEGORY.COURSE_CATEGORY_ID))
                                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                                .leftJoin(ORGANIZATION).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                                .leftJoin(BUSINESS_TOPIC).on(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID))
                                .where(conditions);
              /*  if (topicId.isPresent()) {
                    select = select.andExists(context.select(BUSINESS_TOPIC.ID).from(BUSINESS_TOPIC)
                            .where(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID).and(BUSINESS_TOPIC.TOPIC_ID.eq(topicId.get()))));
                }*/
            };

            SelectSelectStep<Record> listSelect = context.selectDistinct(Fields.start()
                    .add(COURSE_INFO.ID)
                    .add(COURSE_INFO.NAME)
                    .add(COURSE_INFO.TYPE)
                    .add(COURSE_INFO.CREATE_TIME)
                    .add(COURSE_INFO.RELEASE_TIME)
                    .add(COURSE_INFO.SOURCE)
                    .add(COURSE_INFO.STATUS)
                    .add(COURSE_INFO.COVER)
                    .add(COURSE_INFO.COVER_PATH)
                    .add(COURSE_INFO.BEGIN_DATE)
                    .add(COURSE_INFO.END_DATE)
                    .add(COURSE_INFO.VISITS)
                    .add(COURSE_INFO.AVG_SCORE)
                    .add(COURSE_INFO.URL)
                    .add(COURSE_INFO.INTEGRAL)
                    .add(COURSE_INFO.BUSINESS_TYPE)
                    .end());
            SelectSelectStep<Record> idsSelect = context.selectDistinct(Fields.start().add(COURSE_INFO.ID).end());

            SortField<?> sf = getSortField(orderBy.orElse(defaultOrderBy), order.orElse(defaultOrder));
            int firstResult = (page - 1) * pageSize;
            Integer count = 0; //stepFunc.apply(countSelect).fetchOne(0, Integer.class);
            Result<Record> idsResult = stepFunc.apply(idsSelect).orderBy(sf).limit(firstResult, pageSize).fetch();


            List<String> ids =
                    idsResult.into(CourseInfo.class).stream().map(x -> x.getId()).collect(Collectors.toList());
           if (!ObjectUtils.isEmpty(courseInfoIds)){
               ids.addAll(courseInfoIds);
           }
            List<CourseInfo> courseInfoList = listSelect.from(COURSE_INFO).where(COURSE_INFO.ID.in(ids)).orderBy(sf).limit(firstResult, pageSize)
                    .fetch(iteam -> {
                        CourseInfo courseInfo = iteam.into(CourseInfo.class);
                        courseInfo.setIntegral(iteam.get(COURSE_INFO.INTEGRAL));
                        return courseInfo;
                    });

            more[0] = 1;
            if (Objects.nonNull(courseInfoList)) {
                if (pageSize.intValue() > courseInfoList.size()) {
                    more[0] = 0;
                }
            }
            return PagedResult.create(count, courseInfoList);

        });
        result.put("items", pageList.getItems());
        result.put("recordCount", 0);
        result.put("more", more[0]);
        return result;
    }


    @DataSource(type = DataSourceEnum.SLAVE)
    private List<String> findIds(List<Condition> conditions, List<String> ids){
        List<Condition> conditions1= Lists.newArrayList();
        conditions1.addAll(conditions);
        conditions1.add(COURSE_INFO.ID.in(ids));
       return courseInfoCommonDao.execute(r->r.select(COURSE_INFO.ID)
                .from(COURSE_INFO)
                .leftJoin(COURSE_INFO_CATEGORY).on(COURSE_INFO_CATEGORY.COURSE_INFO_ID.eq(COURSE_INFO.ID))
                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO_CATEGORY.COURSE_CATEGORY_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .leftJoin(ORGANIZATION).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(conditions1)
                .fetch(COURSE_INFO.ID));
    }



    @Override
    public Boolean existedManager(String courseId, String memberId) {
        return subjectTopicManagerCommonDao.execute(dsl ->
                dsl.select(DSL.count())
                        .from(SUBJECT_TOPIC_MANAGER)
                        .where(SUBJECT_TOPIC_MANAGER.COURSE_ID.eq(courseId).and(SUBJECT_TOPIC_MANAGER.MEMBER_ID.eq(memberId)))
                        .limit(1)
                        .fetchOne(Record1::value1)) > 0;
    }

    @Override
    public List<CourseInfo> getCourseOrSubject(List<String> courseIds, String memberId) {

        List<CourseInfo> courseInfoList = courseInfoCommonDao.execute(w -> w.select(Fields.start().add(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.COVER_PATH, COURSE_INFO.COURSE_TIME,
                        COURSE_INFO.STUDY_MEMBER_COUNT, COURSE_INFO.AVG_SCORE, COURSE_INFO.VERSION_ID, COURSE_INFO.DESCRIPTION_TEXT, COURSE_INFO.DESCRIPTION).end())
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.in(courseIds))
                .fetch()).into(CourseInfo.class);

        courseInfoList.forEach(
                w -> {
                    CourseInfo course = courseInfoCommonDao.get(w.getId());
                    String courseVersion = courseStudyProgressService.findByMemberIdAndCourseId(memberId, course.getId())
                            .map(r -> r.getCourseVersionId()).orElse(course.getVersionId());
                    // 查询章节和章节详情
                    List<CourseChapter> courseChapters = courseCacheService.getCourseChapter(course.getId(), courseVersion);
                    if (ObjectUtils.isEmpty(courseChapters)){
                        return;
                    }

//                    setIntelligentBroadcast(courseChapters);
                    course.setCourseChapters(courseChapters);

                    //计算课程总时长
                    List<CourseChapterSection> sectionList = new ArrayList<CourseChapterSection>();
                    Integer time = 0;
                    course.getCourseChapters().stream().filter(chapter -> chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()).forEach(c -> sectionList.addAll(c.getCourseChapterSections()));

                    time = sectionList.stream()
                            .filter(section -> section.getSectionType() != 8 && section.getSectionType() != 9 && section.getSectionType() != 13 && section.getSectionType() != 12)
                            .map(s -> Optional.ofNullable(s.getTimeSecond()).orElse(0) + Optional.ofNullable(s.getTimeMinute()).orElse(0) * 60)
                            .reduce(0, (a, b) -> a + b);
                    String allCourseTime = calCourseTime(time);
                    course.setAllCourseTime(allCourseTime);
                }
        );

        return courseInfoList;
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public List<String> findVirtualSpacesAndOrgId(Optional<String> organizationId, List<String> superiorIds, List<String> spacesStatusAddTo, Optional<Integer> status, Optional<String> name, Optional<String> categoryId, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate) {

        List<Condition> conditions = Stream.of(name.map(COURSE_INFO.NAME::contains),
                        status.map(COURSE_INFO.STATUS::eq),
                        categoryId.map(COURSE_CATEGORY.PATH::contains),
                        shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                        shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le)
                        )
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        if (organizationId.isPresent()) {
            conditions.add(COURSE_INFO.ORGANIZATION_ID.in(superiorIds));
        }

        conditions.add(COURSE_INFO.ID.in(spacesStatusAddTo));


        return courseInfoCommonDao.execute(r -> r.select(COURSE_INFO.ID)
                .from(COURSE_INFO)
                .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                .where(conditions)
                .fetch(COURSE_INFO.ID));
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public List<CourseInfo> findResourceTop3(int businessType) {
        return courseInfoCommonDao.execute(r ->
                r.select(COURSE_INFO.NAME, COURSE_INFO.VISITS)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.BUSINESS_TYPE.eq(businessType))
                        .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                        .and(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES))
                        .and(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES))
                        .orderBy(COURSE_INFO.VISITS.desc())
                        .limit(3)
                        .fetch(o -> {
                            CourseInfo courseInfo = new CourseInfo();
                            courseInfo.setContentName(o.getValue(COURSE_INFO.NAME));
                            courseInfo.setVisit(o.getValue(COURSE_INFO.VISITS));
                            return courseInfo;
                        }));
    }


    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public List<String> findVirtualSpacesAndOrgId(List<String> spacesStatusAddTo, Optional<Integer> status, List<String> superiorIds, Optional<Integer> subjectType, Optional<String> name, Optional<String> organizationId, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate) {

        List<Condition> conditions = Stream.of(name.map(COURSE_INFO.NAME::contains),
                        status.map(COURSE_INFO.STATUS::eq),
                        shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                        shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        if (organizationId.isPresent()) {
            conditions.add(COURSE_INFO.ORGANIZATION_ID.in(superiorIds));
        }
        if (subjectType.isPresent() && Objects.equals(subjectType.get(), CourseInfo.PERSONALIZATION)) {
            conditions.add(COURSE_INFO.URL.isNotNull());
        } else if (subjectType.isPresent() && Objects.equals(subjectType.get(), CourseInfo.GENERAL)) {
            conditions.add(COURSE_INFO.URL.isNull());
        }
        conditions.add(COURSE_INFO.ID.in(spacesStatusAddTo));
        return courseInfoCommonDao.execute(r -> r.select(COURSE_INFO.ID)
                .from(COURSE_INFO)
                .where(conditions)
                .fetch(COURSE_INFO.ID));
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public List<CourseInfo> findIntellectTopicCourse(Long startTime, Long endTime) {
        //获取课程信息
        List<CourseInfo> courseInfoList = getCourseInfo(startTime, endTime);
        //获取课程id
        List<String> courseIds = courseInfoList.stream().map(CourseInfo::getId).collect(Collectors.toList());
        //获取标签
        Map<String, List<Topic>> topicMap = getBusinessTopic(courseIds);
        courseInfoList.stream().filter(r -> topicMap.containsKey(r.getId())).forEach(r -> r.setTopicList(topicMap.get(r.getId())));
        //获取字幕
        Map<String, List<Caption>> captionMap = getCaption(courseIds);
        courseInfoList.stream().filter(r -> captionMap.containsKey(r.getId())).forEach(r -> r.setCaptionList(captionMap.get(r.getId())));
        return courseInfoList;
    }

    @Override
    public void update(CourseInfo courseInfo) {
        courseInfoCommonDao.update(courseInfo);
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public List<CourseInfo> findAll() {
        return courseInfoCommonDao.execute(r ->
                r.select(COURSE_INFO.ID, COURSE_INFO.CAPTION_FLAG)
                        .from(COURSE_INFO)
                        .fetch(o -> {
                            CourseInfo courseInfo = new CourseInfo();
                            courseInfo.setId(o.getValue(COURSE_INFO.ID));
                            courseInfo.setCaptionFlag(o.getValue(COURSE_INFO.CAPTION_FLAG));
                            return courseInfo;
                        }));
    }

    /**
     * 校验是否匹配考试操作
     *
     * @param examId 考试记录主键
     * @param memberId 当前用户主键
     * @return 课程Or专题详情
     */
    @Override
    public String checkMatchingExamOperation(String examId, String memberId) {
        //1、根据考试Id逆推课程或者专题信息
        List<String> idCollect = courseInfoCommonDao.execute(ew1 ->
                ew1.select(COURSE_INFO.ID)
                        .from(COURSE_INFO)
                        .leftJoin(COURSE_CHAPTER)
                        .on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID))
                        .and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID).or(COURSE_CHAPTER.VERSION_ID.isNull()))
                        .leftJoin(COURSE_CHAPTER_SECTION)
                        .on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                        .where(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_EXAM))
                        .and(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(examId))
                        .and(COURSE_INFO.STATUS.in(Arrays.asList(CourseInfo.STATUS_SHELVES, CourseInfo.STATUS_THE_TEST)))
                        .and(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()))
                        .fetch(COURSE_INFO.ID));

        //2、若1的操作为空，抛出异常
        if (CollectionUtils.isEmpty(idCollect)) {
            throw new UnprocessableException(ErrorCode.DoesNotMeetExamOperation);
        }

        //3、将1的出参的专题OR课程主键与当前登录用户条件查询是否匹配（受众）
        List<String> memberIdCollect = audienceObjectCommonDao.execute(ew3 ->
                ew3.select(AUDIENCE_MEMBER.ID)
                    .from(AUDIENCE_MEMBER)
                    .leftJoin(AUDIENCE_OBJECT)
                    .on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                    .where(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId))
                    .and(AUDIENCE_OBJECT.BUSINESS_ID.eq(idCollect.get(0)))
                    .limit(1)
                    .fetch(AUDIENCE_MEMBER.ID));

        //4、若3的操作为空，抛出异常
        if(CollectionUtils.isEmpty(memberIdCollect)){
            throw new UnprocessableException(ErrorCode.DoesNotMeetExamOperation);
        }

        //5、返回当前课程id
        return idCollect.get(0);
    }

    /**
     * 获取课程
     * @param startTime
     * @param endTime
     * @return
     */

    private List<CourseInfo> getCourseInfo(Long startTime, Long endTime) {
        return courseInfoCommonDao.execute(r ->
                r.select(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.DESCRIPTION, COURSE_INFO.TYPE, COURSE_CATEGORY.NAME)
                        .from(COURSE_INFO)
                        .leftJoin(COURSE_CATEGORY).on(COURSE_INFO.CATEGORY_ID.eq(COURSE_CATEGORY.ID))
                        .where(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                        .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                        .and(COURSE_INFO.CREATE_TIME.between(startTime).and(endTime))
                        .fetch(o -> {
                            CourseInfo courseInfo = new CourseInfo();
                            courseInfo.setId(o.getValue(COURSE_INFO.ID));
                            courseInfo.setName(o.getValue(COURSE_INFO.NAME));
                            courseInfo.setDescription(o.getValue(COURSE_INFO.DESCRIPTION));
                            courseInfo.setType(o.getValue(COURSE_INFO.TYPE));
                            courseInfo.setCategoryName(o.getValue(COURSE_CATEGORY.NAME));
                            return courseInfo;
                        })
        );
    }

    /**
     * 获取字幕
     * @param courseIds
     * @return
     */
    private Map<String, List<Caption>> getCaption(List<String> courseIds) {
        Map<String, List<Caption>> map = Maps.newHashMap();
        captionCommonDao.execute(r->
                r.select(COURSE_INFO.ID,CAPTION.CAPTION_CONTENT,COURSE_CHAPTER_SECTION.NAME)
                        .from(COURSE_INFO)
                        .leftJoin(COURSE_CHAPTER)
                        .on(COURSE_CHAPTER.COURSE_ID.eq(COURSE_INFO.ID))
                        .and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID))
                        .leftJoin(COURSE_CHAPTER_SECTION)
                        .on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                        .leftJoin(CAPTION)
                        .on(CAPTION.COURSE_ID.eq(COURSE_INFO.ID))
                        .and(CAPTION.ATTACHMENT_ID.eq(COURSE_CHAPTER_SECTION.ATTACHMENT_ID))
                        .where(COURSE_INFO.ID.in(courseIds))
                        .fetch(o->{
                            Caption caption = new Caption();
                            caption.setSectionName(o.getValue(COURSE_CHAPTER_SECTION.NAME));
                            caption.setCourseId(o.getValue(COURSE_INFO.ID));
                            caption.setCaptionContent(o.getValue(CAPTION.CAPTION_CONTENT));

                            String key = caption.getCourseId();
                            List<Caption> captionList = Lists.newArrayList();
                            if (map.containsKey(key)){
                                captionList = map.get(key);
                            }
                            captionList.add(caption);
                            map.put(key, captionList);
                            return null;
                        }));
        return map;
    }


    /**
     * 获取课程的标签
     * @param courseIds
     * @return
     */
    private Map<String, List<Topic>> getBusinessTopic(List<String> courseIds) {
        Map<String, List<Topic>> map = Maps.newHashMap();
        businessTopicCommonDao.execute(r ->
                        r.select(TOPIC.ID, BUSINESS_TOPIC.BUSINESS_ID, TOPIC.NAME, TOPIC.TYPE_ID))
                .from(BUSINESS_TOPIC)
                .leftJoin(TOPIC).on(TOPIC.ID.eq(BUSINESS_TOPIC.TOPIC_ID))
                .where(BUSINESS_TOPIC.BUSINESS_ID.in(courseIds))
                .fetch(o -> {
                    Topic topic = new Topic();
                    topic.setId(o.getValue(TOPIC.ID));
                    topic.setName(o.getValue(TOPIC.NAME));
                    topic.setTypeId(o.getValue(TOPIC.TYPE_ID));
                    topic.setBusinessId(o.getValue(BUSINESS_TOPIC.BUSINESS_ID));

                    String key = topic.getBusinessId();
                    List<Topic> topicList = Lists.newArrayList();;
                    if (map.containsKey(key)) {
                        topicList = map.get(key);
                    }
                    topicList.add(topic);
                    map.put(key, topicList);
                    return null;
                });
        return map;
    }

    @Override
    public Integer loseEfficacy(String businessId, String memberId, Integer businessType) {

        if (businessType == 1 || businessType == 2){// 校验课程or专题是否失效
            return existedCourseOrSubject(businessId, memberId);
        }
        if (businessType == 7){// 校验知识是否失效
            return existedKnowledgeInfo(businessId, memberId);
        }

        if (businessType == 5){// 校验直播是否失效
            return existedGenseeWebCast(businessId, memberId);
        }

        return 1;
    }

    /**
     *校验是否满足图谱课程同步条件
     *
     * @param courseId 课程Id
     * @return 若满足图谱课程同步条件，则返回当前Id集合
     */
    @Override
    public List<String> checkCourseSynchronous(String courseId) {
        return courseInfoCommonDao.execute(ew1 ->
                ew1.select(COURSE_INFO.ID)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ID.eq(courseId))
                        .and(COURSE_INFO.ORGANIZATION_ID.eq("1"))
                        .and(COURSE_INFO.STATUS.ne(0))
                        .fetch(COURSE_INFO.ID)
        );
    }


    private int existedCourseOrSubject(String businessId, String memberId){

        Optional<String> optional = courseInfoCommonDao.execute(dls ->
                dls.select(COURSE_INFO.VERSION_ID).from(COURSE_INFO).where(COURSE_INFO.ID.eq(businessId))
                        .fetchOptional(COURSE_INFO.VERSION_ID));

        if (!optional.isPresent()){
            return 0;
        }
        if (!this.existedVersionId(businessId)){// 校验课程or专题是否失效
            return 0;
        }
        if (!this.isAudience(businessId, memberId)) {// 校验用户是否有课程or专题or知识的权限
            return 0;
        }
        return 1;
    }

    private int existedKnowledgeInfo(String businessId, String memberId){
        Optional<KnowledgeInfo> knowledgeInfoOptional = knowledgeInfoCommonDao.getOptional(businessId);// 校验知识是否失效

        if (knowledgeInfoOptional.isPresent()){
            KnowledgeInfo knowledgeInfo = knowledgeInfoOptional.get();

            if (knowledgeInfo.getReleaseStatus() != null
                    && knowledgeInfo.getReleaseStatus().equals(KnowledgeInfo.RELEASE_STATUS_PUBLISH_NOT)
                    && !knowledgeInfo.getUploadMemberId().equals(memberId)) {
                return 0;
            }
            if (!this.isAudience(businessId, memberId)) {// 校验用户是否有课程or专题or知识的权限
                return 0;
            }
            return 1;
        }
        return 0;
    }

    private int existedGenseeWebCast(String businessId, String memberId){
        Optional<GenseeWebCast> genseeWebCastOptional = genseeWebCastDao.getOptional(businessId);// 校验直播是否失效

        if (genseeWebCastOptional.isPresent()){
            GenseeWebCast genseeWebCast = genseeWebCastOptional.get();

            // 判断是否或者撤销
            if(GenseeWebCast.STATUS_NOT_PUBLISH.equals(genseeWebCast.getStatus()) || GenseeWebCast.STATUS_CANCEL.equals(genseeWebCast.getStatus())) {
                return 0;
            }

            if (genseeWebCast.getScene() == 2 && genseeWebCast.getPublicLiving() == 1){
                return 1;
            }

            // 判断用户是否是否有权限参加该直播：1、受众对象，2、讲师
            if(!genseeIsAudience(businessId, memberId)) {
                return 0;
            }

            return 1;
        }
        return 0;
    }

    private boolean genseeIsAudience(String genseeId, String memberId){
        // 判断是否在受众范围内
        int size = userAccessCommonDao.execute(dslContext ->
                        dslContext.select(AUDIENCE_MEMBER.ID.count()).from(AUDIENCE_OBJECT).innerJoin(AUDIENCE_MEMBER)
                                .on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                                .where(AUDIENCE_MEMBER.MEMBER_ID.eq(memberId)).and(AUDIENCE_OBJECT.BUSINESS_ID.eq(genseeId)))
                .fetchOne(AUDIENCE_MEMBER.ID.count());
        int size2 = genseeLecturerDao.execute(dslContext ->
                dslContext.select(GENSEE_LECTURER.ID.count())
                        .from(GENSEE_LECTURER)
                        .where(GENSEE_LECTURER.LECTURER_ID.eq(memberId)).and(GENSEE_LECTURER.GENSEE_ID.eq(genseeId))
                        .fetchOne(GENSEE_LECTURER.ID.count()));
        return size + size2 > 0;
    }

    @Override
    public List<CourseInfo> findSubjectsByCourse(String courseId, String currentUserId) {
        Map<String,CourseInfo>  map = new HashMap<>();
        courseInfoCommonDao.execute(dsl -> dsl
                .select(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.COVER, COURSE_INFO.COVER_PATH)
                .from(COURSE_CHAPTER_SECTION)
                .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID))
                .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_CHAPTER_SECTION.COURSE_ID))
                .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                .where(
                        COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(courseId)
                                .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                                .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_ENABLED))
                                .and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT))
                                .and(COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID))
                ).fetch(record -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(record.get(COURSE_INFO.ID));
                    courseInfo.setName(record.get(COURSE_INFO.NAME));
                    courseInfo.setCover(record.get(COURSE_INFO.COVER));
                    courseInfo.setCoverPath(record.get(COURSE_INFO.COVER_PATH));
                    map.putIfAbsent(courseInfo.getId(), courseInfo);
                    return courseInfo;
                })
        );
        return new ArrayList<>(map.values());
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public List<CourseInfo> findSubjectsByCourse(String courseId, String currentUserId, Integer skipSize, Integer size) {
        // 第1优先级：若该课程有被专题所引用，则展示对应专题下的课程。课程若被多个专题引用，则先根据专题的首次发布时间排序，再根据专题内课程的最新发布时间展示。例如A课程被b专题和c专题引用，b专题首次发布时间早于c，则优先推荐b专题里的课程，b专题内课程再按照最新发布时间进行推荐。
        // 说明：专题是根据首次发布时间，课程则是根据最新的发布时间。排序都是进行顺序排序，即先展示时间更早的。
        com.zxy.product.course.jooq.tables.CourseInfo cs2 = COURSE_INFO.as("cs2");
        com.zxy.product.course.jooq.tables.CourseChapter cc2 = COURSE_CHAPTER.as("cc2");
        com.zxy.product.course.jooq.tables.CourseChapterSection ccs = COURSE_CHAPTER_SECTION.as("ccs");
        com.zxy.product.course.jooq.tables.AudienceObject audobj2 = AUDIENCE_OBJECT.as("audobj2");
        com.zxy.product.course.jooq.tables.AudienceMember audmem2 = AUDIENCE_MEMBER.as("audmem2");
        return courseInfoCommonDao.execute(dsl -> {
            Table<Record4<String, String, String, String>> table = dsl
                    .select(cs2.ID, cs2.COVER_PATH, cs2.NAME, cs2.COVER)
                    .from(COURSE_CHAPTER_SECTION)
                    .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                    .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID))
                    .leftJoin(AUDIENCE_OBJECT).on(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_CHAPTER_SECTION.COURSE_ID))
                    .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_MEMBER.ITEM_ID.eq(AUDIENCE_OBJECT.ITEM_ID))
                    .leftJoin(cc2).on(COURSE_INFO.ID.eq(cc2.COURSE_ID)).and(COURSE_INFO.VERSION_ID.eq(cc2.VERSION_ID))
                    .leftJoin(ccs).on(ccs.CHAPTER_ID.eq(cc2.ID)).and(ccs.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_COURSE))
                    .leftJoin(cs2).on(cs2.ID.eq(ccs.RESOURCE_ID))
                    .leftJoin(audobj2).on(audobj2.BUSINESS_ID.eq(cs2.ID))
                    .leftJoin(audmem2).on(audmem2.ITEM_ID.eq(audobj2.ITEM_ID))
                    .where(
                            COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(courseId)
                                    .and(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                                    .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_ENABLED))
                                    .and(COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID))
                                    .and(COURSE_INFO.URL.isNull())
                                    .and(audmem2.MEMBER_ID.eq(currentUserId))
                                    .and(ccs.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_COURSE))
                                    .and(cs2.ID.notEqual(courseId))
                    ).orderBy(COURSE_INFO.SHELVE_TIME.asc(), cs2.RELEASE_TIME.asc())
                    .limit(skipSize, size).asTable();
            return dsl.selectDistinct(table.fields())
                    .from(table).fetch(record -> {
                        CourseInfo courseInfo = new CourseInfo();
                        courseInfo.setId(record.getValue(0, String.class));
                        courseInfo.setCoverPath(generateSecurePathCdn(record.getValue(1, String.class)));
                        courseInfo.setName(record.getValue(2, String.class));
                        courseInfo.setCover(record.getValue(3, String.class));
                        return courseInfo;
                    });
        });

    }
    @Override
    public void editAllExplicitLearningStatus(Integer type, Integer status) {
        Condition condition;
        if (type == CourseInfo.BUSINESS_TYPE_COURSE) {
            condition = COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE);
        } else if (CourseInfo.BUSINESS_TYPE_SUBJECT.equals(type)) {
            condition = COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT);
        } else {
            return;
        }
        courseInfoCommonDao.execute(dslContext ->
                dslContext.update(COURSE_INFO)
                        .set(COURSE_INFO.EXPLICIT_LEARNING_STATUS, status)
                        .where(condition.and(COURSE_INFO.EXPLICIT_LEARNING_STATUS.eq(1 - status)))
                        .execute());
    }

    @Override
    public Map<String, Integer> getRequiredSectionProgress(String courseId, String memberId) {
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, courseId);
        TableImpl<?> progressTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        List<String> finishList = new ArrayList<>();

        Map<String, String> finishSectionIdsMap = courseInfoCommonDao.execute(x ->
                        x.select(csspTable.field("f_section_id", String.class)).from(csspTable)
                                .where(csspTable.field("f_member_id", String.class).eq(memberId))
                                .and(csspTable.field("f_course_id", String.class).eq(courseId))
                                .and(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
                                        .or(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                                .fetch(csspTable.field("f_section_id", String.class)))
                .stream().collect(Collectors.toMap(r -> r, r -> r));

        Condition versionCond = COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID);
        Optional<CourseStudyProgress> memberVersionId = courseStudyProgressService.findByMemberIdAndCourseId(memberId, courseId);
        if (memberVersionId.isPresent() && memberVersionId.get().getCourseVersionId() != null) {
            versionCond = COURSE_CHAPTER.VERSION_ID.eq(memberVersionId.get().getCourseVersionId());
        }
        List<Map<String, Object>> fetch = courseInfoCommonDao.execute(x -> x
                        .select(COURSE_CHAPTER_SECTION.REFERENCE_ID,
                                COURSE_CHAPTER_SECTION.SECTION_TYPE,
                                COURSE_CHAPTER_SECTION.RESOURCE_ID,
                                COURSE_CHAPTER_SECTION.ID
                        ).from(COURSE_CHAPTER_SECTION)
                        .innerJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
                .innerJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID))
                .where(COURSE_CHAPTER.COURSE_ID.eq(courseId)
                        .and(versionCond)
                        .and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED)))
                .fetch(result -> createMapFromArgs(
                        "referenceId", result.get(COURSE_CHAPTER_SECTION.REFERENCE_ID),
                        "sectionType", result.get(COURSE_CHAPTER_SECTION.SECTION_TYPE),
                        "resourceId", result.get(COURSE_CHAPTER_SECTION.RESOURCE_ID),
                        "id", result.get(COURSE_CHAPTER_SECTION.ID)
                ));
        List<String> referenceIds = fetch.stream().map(r -> r.get("referenceId").toString()).collect(Collectors.toList());

        // 处理新加的历史已经完成的课程或者专题
        List<String> courseIds = fetch.stream()
                .filter(r -> !finishSectionIdsMap.containsKey(r.get("referenceId").toString()))
                .filter(r -> CourseChapterSection.SECTION_TYPE_COURSE == Integer.parseInt(r.get("sectionType").toString()) || CourseChapterSection.SECTION_TYPE_SUBJECT == Integer.parseInt(r.get("sectionType").toString()))
                .filter(r -> Objects.nonNull(r.get("resourceId")))
                .map(r -> r.get("resourceId").toString()).collect(toList());
        Integer oldFinishCount = 0;
        AtomicInteger archivedFinishCount = new AtomicInteger(0);

        if (!courseIds.isEmpty()) {
            oldFinishCount =  courseInfoCommonDao.execute(dslContext -> dslContext
                    .selectCount()
                    .from(progressTable)
                    .where(
                            progressTable.field("f_member_id", String.class).eq(memberId)
                                    .and(progressTable.field("f_course_id", String.class).in(courseIds))
                                    .and(progressTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
                                            .or(progressTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                    ).fetchOne().value1()
            );

            Map<String, Integer> archivedStatus = Optional.ofNullable(courseStudyProgressArchivedService.getArchivedStatus(memberId, courseIds)).orElse(new HashMap<>());
            Optional.of(archivedStatus.values()).orElse(new ArrayList<>()).forEach(x -> {
                if (CourseSectionStudyProgress.FINISH_STATUS_FINISH.equals(x) || CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS.equals(x)) {
                    archivedFinishCount.set(archivedFinishCount.get() + 1);
                }
            });
        }

        referenceIds.forEach(r -> {
            if (!StringUtils.isEmpty(finishSectionIdsMap.get(r))) {
                finishList.add(r);
            }
        });
        Map<String, Integer> resMap = new HashMap<>();
        resMap.put("finish", finishList.size() + oldFinishCount + archivedFinishCount.get());
        resMap.put("required", referenceIds.size());
        return resMap;
    }


    @Override
    public List<Map<String, Object>> getNewRequiredSectionId(String courseId, String memberId) {
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, courseId);
        TableImpl<?> progressTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        Integer finishStatus = courseInfoCommonDao.execute(dslContext -> dslContext
                .select(progressTable.field("f_finish_status", Integer.class))
                .from(progressTable)
                .where(
                        progressTable.field("f_member_id", String.class).eq(memberId)
                                .and(progressTable.field("f_course_id", String.class).eq(courseId)))
                .limit(1).fetchOne(Record1::value1)
        );

        if (!(CourseSectionStudyProgress.FINISH_STATUS_FINISH.equals(finishStatus) || CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS.equals(finishStatus))) {
            return new ArrayList<>();
        }
        List<Map<Object, Object>> maps = Optional.ofNullable(courseInfoCommonDao.execute(x ->
                x.select(csspTable.field("f_last_access_time", Long.class),
                                csspTable.field("f_section_id", String.class),
                                csspTable.field("f_finish_status", Integer.class)
                        )
                        .from(csspTable)
                        .where(csspTable.field("f_member_id", String.class).eq(memberId))
                        .and(csspTable.field("f_course_id", String.class).eq(courseId))
                        .fetch(result -> {
                            Integer sectionFinishStatus = result.get(csspTable.field("f_finish_status", Integer.class));
                            if (CourseSectionStudyProgress.FINISH_STATUS_FINISH.equals(sectionFinishStatus) || CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS.equals(sectionFinishStatus)){
                                Map<Object, Object> map = new HashMap<>();
                                map.put("lastAccessTime", result.get(csspTable.field("f_last_access_time", Long.class)));
                                map.put("sectionId", result.get(csspTable.field("f_section_id", String.class)));
                                return map;
                            }
                            return null;
                        }))).orElse(new ArrayList<>()).stream().filter(Objects::nonNull).collect(toList());

        Long lastAccessTime = maps.stream().map(c -> {
            Object o = c.get("lastAccessTime");
            if (Objects.isNull(o)) {
                return Long.valueOf("0");
            } else {
                return Long.valueOf(o.toString());
            }
        }).max(Long::compare).orElse(System.currentTimeMillis());
        List<String> sectionIds = maps.stream().filter(c -> Objects.nonNull(c.get("sectionId"))).map(c -> c.get("sectionId").toString()).collect(toList());
        String versionId = courseInfoCommonDao.execute(dslContext -> dslContext.select(COURSE_INFO.VERSION_ID).from(COURSE_INFO).where(COURSE_INFO.ID.eq(courseId)).fetchOne(Record1::value1));
        com.zxy.product.course.jooq.tables.CourseChapterSection cs2 = COURSE_CHAPTER_SECTION.as("cs2");
        return courseInfoCommonDao.execute(x -> x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID,cs2.CREATE_TIME)
                        .from(COURSE_CHAPTER_SECTION)
                        .innerJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
                .innerJoin(cs2).on(cs2.ID.eq(COURSE_CHAPTER_SECTION.REFERENCE_ID))
                .where(COURSE_CHAPTER.COURSE_ID.eq(courseId)
                        .and(COURSE_CHAPTER.VERSION_ID.eq(versionId))
                        .and(cs2.REQUIRED.eq(CourseChapterSection.IS_REQUIRED))
                        // .and(cs2.CREATE_TIME.gt(lastAccessTime))
                        .and(COURSE_CHAPTER_SECTION.REFERENCE_ID.notIn(sectionIds))
                )
                .fetch(record -> {
                    ImmutableMap<String, ? extends Serializable> map = ImmutableMap.of(
                            "referenceId", record.get(COURSE_CHAPTER_SECTION.REFERENCE_ID),
                            "createTime", record.get(cs2.CREATE_TIME)
                    );
                    HashMap<String, Object> hashMap = Maps.newHashMap();
                    hashMap.putAll(map);
                    if (hashMap.get("createTime") != null && Long.parseLong(hashMap.get("createTime").toString()) > lastAccessTime){
                        hashMap.put("isUpdate", true);
                    }
                    return hashMap;
                });
    }
    @Override
    public Map<String,List<Map<String,Object>>> getNewRequiredSectionId2(List<String> courseIds, String memberId,Integer type,Optional<String> config) {
        Map<String,List<Map<String,Object>>> res = new HashMap<>();

        Map<String, Boolean> explicitLearningStatusMap = checkExplicitLearningStatus2(type,courseIds, config);
        for (String courseId : courseIds) {
            List<Map<String,Object>> newRequiredSectionId;
            if (!explicitLearningStatusMap.get(courseId)){
                newRequiredSectionId =  null;
            }else {
                newRequiredSectionId = this.getNewRequiredSectionId(courseId, memberId);
            }
            res.put(courseId, newRequiredSectionId);
        }
        return res;
    }

    public List<String> getNewRequiredSectionIdNew(List<String> courseIds, String memberId, TableImpl<?> progressTable, Integer type, Optional<String> config) {
        List<String> result = new ArrayList<>();

        Map<String, Boolean> explicitLearningStatusMap = checkExplicitLearningStatus2(type,courseIds, config);
        List<String> enableCourseIds = courseIds.stream().filter(explicitLearningStatusMap::get).collect(toList());


        List<Pair<String, Integer>> progressList = courseInfoCommonDao.execute(dslContext -> dslContext
                .select(progressTable.field("f_course_id", String.class), progressTable.field("f_finish_status", Integer.class))
                .from(progressTable)
                .where(
                        progressTable.field("f_member_id", String.class).eq(memberId)
                                .and(progressTable.field("f_course_id", String.class).in(enableCourseIds)))
                .fetch(r -> Pair.create(r.getValue(progressTable.field("f_course_id", String.class)), r.getValue(progressTable.field("f_finish_status", Integer.class))))
        );

        List<String> finisCourseIds = progressList.stream().filter(p -> (CourseSectionStudyProgress.FINISH_STATUS_FINISH.equals(p.getSecond()) || CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS.equals(p.getSecond()))).map(Pair::getFirst).collect(toList());

        for (String finisCourseId : finisCourseIds) {
            TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, finisCourseId);

            Boolean isUpdate = courseInfoCommonDao.execute(x -> {

                List<String> sectionIds = Optional.ofNullable(x
                        .select(COURSE_CHAPTER_SECTION.ID)
                        .from(COURSE_INFO)
                        .join(COURSE_CHAPTER).on(COURSE_CHAPTER.COURSE_ID.eq(COURSE_INFO.ID).and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID)))
                        .join(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                        .where(COURSE_INFO.ID.eq(finisCourseId)).fetch(COURSE_CHAPTER_SECTION.ID)).orElse(new ArrayList<>());


                Integer finishCount = x.select(DSL.count(csspTable.field("f_id", String.class)))
                        .from(csspTable)
                        .where(csspTable.field("f_member_id", String.class).eq(memberId))
                        .and(csspTable.field("f_section_id", String.class).in(sectionIds))
                        .and(csspTable.field("f_finish_status", Integer.class).in(CourseSectionStudyProgress.FINISH_STATUS_FINISH, CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS))
                        .fetchOne(Record1::value1);
                return sectionIds.size() > finishCount;
            });
            // 将有更新的课程ID放到数组中返回
            if (isUpdate){
                result.add(finisCourseId);
            }
        }
        return result;

    }

    @Override
    public List<String> getNewRequiredList(List<String> courseIds, String memberId, Integer type, Optional<String> config) {
        TableImpl<?> progressTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return this.getNewRequiredSectionIdNew(courseIds,memberId,progressTable,type,config);
    }




    @Override
    public Map<String, Integer> findCourseChapterSectionByCourseIds(List<String> courseIds, Optional<String> courseVersion) {
        List<Condition> where = Stream
                .of(Optional.of(COURSE_CHAPTER.COURSE_ID.in(courseIds)),
                        courseVersion.map(COURSE_CHAPTER.VERSION_ID::eq))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        if (!courseVersion.isPresent()){
            where.add(COURSE_CHAPTER.VERSION_ID.isNull());
        }
        where.add(COURSE_CHAPTER_SECTION.SECTION_TYPE.in(CourseChapterSection.SECTION_TYPE_AUDIO, CourseChapterSection.SECTION_TYPE_VIDEO));

        return courseChapterCommonDao.execute(r ->
                r.select(COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.TIME_SECOND)
                        .from(COURSE_CHAPTER)
                        .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                        .where(where)
                        .fetchMap(COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.TIME_SECOND));

    }

    private Map<String, Object> createMapFromArgs(Object... args) {
        if (args.length % 2 != 0) {
            throw new IllegalArgumentException("Number of arguments must be even.");
        }

        Map<String, Object> map = Maps.newHashMap();
        for (int i = 0; i < args.length; i += 2) {
            String key = String.valueOf(args[i]);
            Object value = args[i + 1];
            map.put(key, value);
        }
        return map;
    }


    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public List<CourseInfo> findSubjectsByCourseNoAudience(String courseId) {
        return courseInfoCommonDao.execute(dsl -> dsl
                .selectDistinct(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.COVER_PATH)
                .from(COURSE_CHAPTER_SECTION)
                .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID))
                .where(
                        COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(courseId)
                                .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_ENABLED))
                                .and(COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID))
                ).orderBy(COURSE_INFO.RELEASE_TIME.desc()).fetch(record -> {
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setId(record.get(COURSE_INFO.ID));
                    courseInfo.setName(record.get(COURSE_INFO.NAME));
                    courseInfo.setCoverPath(record.get(COURSE_INFO.COVER_PATH));
                    return courseInfo;
                })
        );
    }

    @Override
    public String getCourseIdByAttachmentId(String id) {
        return courseInfoCommonDao.execute(r->
                r.selectDistinct(COURSE_INFO.ID)
                        .from(COURSE_INFO)
                        .leftJoin(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID)))
                        .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                        .where(COURSE_CHAPTER_SECTION.ATTACHMENT_ID.eq(id))
                        .fetchOne(COURSE_INFO.ID));
    }

    @Override
    public List<CourseAbility> findCourseAbilityByCourseId(String id) {
        List<CourseAbility> courseAbilities = courseAbilityDao.execute(d -> d.select(ABILITY.NAME, COURSE_ABILITY.ORDER, COURSE_ABILITY.LEARN_SEQUENCE, COURSE_ABILITY.ABILITY_ID)
                                                                             .from(COURSE_ABILITY)
                                                                             .innerJoin(ABILITY).on(ABILITY.ID.eq(COURSE_ABILITY.ABILITY_ID))
                                                                             .where(COURSE_ABILITY.COURSE_ID.eq(id))
                                                                             .orderBy(COURSE_ABILITY.ORDER.asc())
                                                                             .fetch(r -> {
                                                                                 CourseAbility courseAbility = new CourseAbility();
                                                                                 courseAbility.setName(r.get(ABILITY.NAME));
                                                                                 courseAbility.setOrder(r.get(COURSE_ABILITY.ORDER));
                                                                                 courseAbility.setLearnSequence(r.get(COURSE_ABILITY.LEARN_SEQUENCE));
                                                                                 courseAbility.setAbilityId(r.get(COURSE_ABILITY.ABILITY_ID));
                                                                                 return courseAbility;
                                                                             }));
        List<CourseChapter> courseChapters = findCourseChapterByCourseId(id);
        Map<Object, List<CourseChapter>> chapterMap = courseChapters.stream().filter(c -> c.getAbilityId() != null).collect(groupingBy(cc -> cc.getAbilityId()));
        courseAbilities.forEach(c -> c.setCourseChapters(chapterMap.get(c.getAbilityId())));
        return courseAbilities;
    }

    @Override
    public List<CourseAbility> findCourseAbilitySimpleByCourseId(String id) {
        return courseAbilityDao.execute(d -> d.select(ABILITY.NAME, COURSE_ABILITY.ORDER, COURSE_ABILITY.LEARN_SEQUENCE, COURSE_ABILITY.ABILITY_ID)
                                              .from(COURSE_ABILITY)
                                              .innerJoin(ABILITY).on(ABILITY.ID.eq(COURSE_ABILITY.ABILITY_ID))
                                              .where(COURSE_ABILITY.COURSE_ID.eq(id))
                                              .orderBy(COURSE_ABILITY.ORDER.asc())
                                              .fetch(r -> {
                                                  CourseAbility courseAbility = new CourseAbility();
                                                  courseAbility.setName(r.get(ABILITY.NAME));
                                                  courseAbility.setOrder(r.get(COURSE_ABILITY.ORDER));
                                                  courseAbility.setLearnSequence(r.get(COURSE_ABILITY.LEARN_SEQUENCE));
                                                  courseAbility.setAbilityId(r.get(COURSE_ABILITY.ABILITY_ID));
                                                  return courseAbility;
                                              }));
    }

    @Override
    public List<CourseAbility> findCourseAbilityByCourseChapters(String courseId, List<CourseChapter> courseChapters) {
        List<CourseAbility> courseAbilities = courseAbilityDao.execute(d -> d.select(ABILITY.NAME, COURSE_ABILITY.ORDER, COURSE_ABILITY.LEARN_SEQUENCE, COURSE_ABILITY.ABILITY_ID)
                                                                             .from(COURSE_ABILITY)
                                                                             .innerJoin(ABILITY).on(ABILITY.ID.eq(COURSE_ABILITY.ABILITY_ID))
                                                                             .where(COURSE_ABILITY.COURSE_ID.eq(courseId))
                                                                             .orderBy(COURSE_ABILITY.ORDER.asc())
                                                                             .fetch(r -> {
                                                                                 CourseAbility courseAbility = new CourseAbility();
                                                                                 courseAbility.setName(r.get(ABILITY.NAME));
                                                                                 courseAbility.setOrder(r.get(COURSE_ABILITY.ORDER));
                                                                                 courseAbility.setLearnSequence(r.get(COURSE_ABILITY.LEARN_SEQUENCE));
                                                                                 courseAbility.setAbilityId(r.get(COURSE_ABILITY.ABILITY_ID));
                                                                                 return courseAbility;
                                                                             }));
        if (CollectionUtils.isEmpty(courseChapters)) {
            return courseAbilities;
        }
        Map<Object, List<CourseChapter>> chapterMap = courseChapters.stream().filter(c -> c.getAbilityId() != null).collect(groupingBy(cc -> cc.getAbilityId()));
        courseAbilities.forEach(c -> c.setCourseChapters(chapterMap.get(c.getAbilityId())));
        return courseAbilities;
    }

    @Override
    public int saveCourseAbilities(String courseId, List<CourseAbility> courseAbilities) {
        courseChapterCommonDao.delete(COURSE_CHAPTER.COURSE_ID.eq(courseId));
        courseChapterSectionCommonDao.delete(COURSE_CHAPTER_SECTION.COURSE_ID.eq(courseId));
        courseAbilityDao.delete(COURSE_ABILITY.COURSE_ID.eq(courseId));

        courseAbilities.forEach(ability -> {
            List<CourseChapter> courseChapters = ability.getCourseChapters();
            courseChapters.forEach(courseChapter -> {
                courseChapter.forInsert();
                courseChapter.setCourseId(courseId);
                courseChapter.setVersionId(null);
                courseChapter.setModifyDate(null);
                courseChapterCommonDao.insert(courseChapter);
                extracted(courseId, courseChapter);
            });
            if (ability.getId() == null) {
                ability.forInsert();
            }
            ability.setCourseId(courseId);
            courseAbilityDao.insert(ability);
        });
        return courseAbilities.size();
    }

    @Override
    public Boolean copyStudyMap(String id) {
        CourseInfo oldCourseInfo = courseInfoCommonDao.get(id);
        CourseInfo newCourseInfo = new CourseInfo();
        BeanUtils.copyProperties(oldCourseInfo, newCourseInfo);
        newCourseInfo.forInsert();
        String name = oldCourseInfo.getName().trim().length() >= 100 ? oldCourseInfo.getName().trim().substring(0,96) + "-复制":oldCourseInfo.getName().trim() + "-复制";
        newCourseInfo.setName(name);
        newCourseInfo.setStatus(CourseInfo.STATUS_UNPUBLISHED);
        newCourseInfo.setReleaseTime(null);
        newCourseInfo.setShelveTime(null);
        newCourseInfo.setVersionId(null);
        courseInfoCommonDao.insert(newCourseInfo);
        updateCourseCode(newCourseInfo);

        List<CourseChapter> courseChapters = courseChapterCommonDao.execute(d -> d.select(COURSE_CHAPTER.fields())
                                                                           .from(COURSE_CHAPTER)
                                                                           .where(COURSE_CHAPTER.COURSE_ID.eq(id)
                                                                                   .and(COURSE_CHAPTER.VERSION_ID.eq(oldCourseInfo.getVersionId())))
                                                                           .fetchInto(CourseChapter.class));
        HashMap<String, String> chapterMap = new HashMap<>();
        List<CourseChapter> insertCourseChapters = new ArrayList<>();
        courseChapters.forEach(courseChapter -> {
            CourseChapter newCourseChapter = new CourseChapter();
            BeanUtils.copyProperties(courseChapter, newCourseChapter);
            newCourseChapter.forInsert();
            newCourseChapter.setCourseId(newCourseInfo.getId());
            newCourseChapter.setVersionId(null);
            insertCourseChapters.add(newCourseChapter);
            chapterMap.put(courseChapter.getId(), newCourseChapter.getId());
        });
        courseChapterCommonDao.insert(insertCourseChapters);

        List<CourseChapterSection> courseChapterSections = courseChapterSectionCommonDao.execute(d -> d.select(COURSE_CHAPTER_SECTION.fields())
                                                                                               .from(COURSE_CHAPTER_SECTION)
                                                                                               .where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(id)
                                                                                                       .and(COURSE_CHAPTER_SECTION.CHAPTER_ID.in(chapterMap.keySet())))
                                                                                               .fetchInto(CourseChapterSection.class));
        List<CourseChapterSection> insertCourseChapterSections = new ArrayList<>();
        courseChapterSections.forEach(courseChapterSection -> {
            CourseChapterSection newCourseChapterSection = new CourseChapterSection();
            BeanUtils.copyProperties(courseChapterSection, newCourseChapterSection);
            newCourseChapterSection.forInsert();
            newCourseChapterSection.setCourseId(newCourseInfo.getId());
            newCourseChapterSection.setChapterId(chapterMap.get(courseChapterSection.getChapterId()));
            newCourseChapterSection.setReferenceId(newCourseChapterSection.getId());
            insertCourseChapterSections.add(newCourseChapterSection);
        });
        courseChapterSectionCommonDao.insert(insertCourseChapterSections);

        return true;
    }

    @Override
    public Map<String, Object> findPersonCourseStudyMap(int pageNum, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy, Optional<String> name,
                                                        Optional<Integer> finishStatus, Optional<Integer> isRequired, Optional<String> studyTimeOrder, boolean pageSwitch,
                                                        Optional<Long> startTime, Optional<Long> endTime) {
        // 查询所有发布的学习地图，调用学习进度接口，根据学习进度返回值拼接最后结果
        List<Param<String>> itemIds = courseInfoCommonDao.execute(e -> e.selectDistinct(AUDIENCE_MEMBER.ITEM_ID)
                                                                        .from(AUDIENCE_MEMBER)
                                                                        .where(AUDIENCE_MEMBER.MEMBER_ID.in(currentMemberId))
                                                                        .fetch(AUDIENCE_MEMBER.ITEM_ID)).stream().map(DSL::val).collect(Collectors.toList());

        if (com.alibaba.dubbo.common.utils.CollectionUtils.isEmpty(itemIds)) {
            Map<String, Object> map = new HashMap<>();
            map.put("items", new ArrayList<>());
            if (pageSwitch){
                map.put("recordCount", 0);
            }
            map.put("more", 0);
            return map;
        }
        List<String> nids= new ArrayList<>();
        if (finishStatus.isPresent() && finishStatus.get()==0) {
            nids =  getNotIds(itemIds,businessType,currentMemberId);
        }
        nids.add(UUID.randomUUID().toString()); // 随便给的一个id防止sql结构有问题
        List<Param<String>> notIds  = nids.stream().map(DSL::val).collect(Collectors.toList());

        String sql = "SELECT course.f_id\n" +
            "FROM `course-study`.`t_course_info` course\n" +
            "WHERE EXISTS (\n" +
            "SELECT f_id FROM `course-study`.`t_audience_object`\n" +
            "WHERE f_business_id=course.f_id\n" +
            "AND f_item_id IN ({0}) LIMIT 1)\n" +
            "AND course.f_business_type = {1}\n"+
            "AND course.f_status in (1,3)\n"+
            "AND course.f_id not in ({2})\n";
        if(studyTimeOrder.isPresent()){
            sql += "ORDER BY course.f_release_time DESC\n";
        }else{
            sql += "ORDER BY course.f_create_time DESC\n";
        }

        String finalSql  = sql;
        List<String> ids = courseInfoCommonDao.execute(dsl -> dsl.fetch(finalSql, DSL.list(itemIds),DSL.val(businessType),DSL.list(notIds)).intoMaps())
                                              .stream().map(x -> x.get("f_id").toString()).collect(toList());
        int progressPageNum = pageNum;
        int progressPageSize =  pageSize;
        if(!finishStatus.isPresent()){  // 默认状态下第二页会把学习数据页一并跳过
            progressPageNum = 1;
            progressPageSize = 100000;
        }

        Map<String, Object> progressMap = courseStudyProgressService.personCourseUnionHistoryMap(progressPageNum, progressPageSize, businessType, currentMemberId,
            findStudy, name, finishStatus, isRequired, studyTimeOrder, pageSwitch, startTime, endTime, Optional.of(ids));
        // 传了学习状态则以学习进度为准，否则以课程信息为准
        if (finishStatus.isPresent() && finishStatus.get()!=0) {
            return progressMap;
        }

        Object progressList = progressMap.get("items");
        Map<String, Object> progressCourseMap = new HashMap<>();
        if (Objects.nonNull(progressList)) {
            ((List<CourseStudyProgress>) progressList).forEach(progress -> {
                progressCourseMap.put(progress.getCourseId(), progress);
            });

            if (studyTimeOrder.isPresent()) { //重新定义排序
                Set<String> keySet = progressCourseMap.keySet();
                ids.removeAll(keySet);
                if (studyTimeOrder.get().equals("asc")) { //将有记录的数据添加到列表最后
                    ids.addAll(keySet);
                } else { // 将有记录的数据添加到列表最前
                    List<String> list = new ArrayList<>(keySet);
                    list.addAll(ids);
                    ids = list;
                }
            }

        }

        ids = ids.subList(Math.min((pageNum - 1) * pageSize,ids.size()), Math.min(((pageNum - 1) * pageSize)+pageSize + 1, ids.size()));
        List<String> finalIds = ids;
        List<CourseStudyProgress> courseList = courseInfoCommonDao.execute(d -> d.select(COURSE_INFO.fields())
                                                                     .from(COURSE_INFO)
                                                                     .where(COURSE_INFO.ID.in(finalIds)).orderBy(COURSE_INFO.CREATE_TIME.desc()).fetch(r->{
                CourseInfo courseInfo = r.into(COURSE_INFO).into(CourseInfo.class);
                if (progressCourseMap.containsKey(courseInfo.getId())) {
                    return (CourseStudyProgress)progressCourseMap.get(courseInfo.getId());
                }
                CourseStudyProgress courseStudyProgress = new CourseStudyProgress();
                courseStudyProgress.setCourseInfo(courseInfo);
                courseStudyProgress.setCourseId(courseInfo.getId());
                courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_DEFAULT);
                return courseStudyProgress;
            }));

        Map<String, Object> map = new HashMap<>();
        if (pageSwitch){
            map.put("recordCount", 0);
        }
        Integer more = 0;
        if(Objects.nonNull(courseList)) {
            if(pageSize < courseList.size()) {
                String removeId  = finalIds.get(pageSize);
                more = 1;
                courseList = courseList.stream().filter(x -> !x.getCourseId().equals(removeId)).collect(toList());
            }
        }
        map.put("items", courseList);
        map.put("more", more);
        return map;
    }

    private  List<String> getNotIds(List<Param<String>> itemIds, Integer businessType, List<String> currentMemberId) {
        String sql = "SELECT course.f_id\n" +
                "FROM `course-study`.`t_course_info` course\n" +
                "WHERE EXISTS (\n" +
                "SELECT f_id FROM `course-study`.`t_audience_object`\n" +
                "WHERE f_business_id=course.f_id\n" +
                "AND f_item_id IN ({0}) LIMIT 1)\n" +
                "AND course.f_business_type = {1}\n"+
                "AND course.f_status in (1,3)\n"+
                "ORDER BY course.f_release_time DESC\n";
        List<String> ids = courseInfoCommonDao.execute(dsl -> dsl.fetch(sql, DSL.list(itemIds),DSL.val(businessType)).intoMaps())
                .stream().map(x -> x.get("f_id").toString()).collect(toList());
        return courseStudyProgressService.getStatusNe(currentMemberId.get(0), ids);
    }


    @Override
    public Boolean whetherAddExam(String courseId) {
        List<String> list = courseInfoCommonDao.execute(r ->
                r.select(COURSE_INFO.ID)
                        .from(COURSE_INFO)
                        .leftJoin(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID)).and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID))
                        .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                        .where(COURSE_INFO.ID.eq(courseId))
                        .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_EXAM))
                        .fetch(COURSE_INFO.ID));
        return CollectionUtils.isEmpty(list);
    }

    @Override
    public List<String> getAttachmentIds(String courseId) {
        return  courseAttachmentCommonDao.execute(r ->
                r.select(COURSE_ATTACHMENT.ATTACHMENT_ID)
                        .from(COURSE_ATTACHMENT)
                        .where(COURSE_ATTACHMENT.COURSE_ID.eq(courseId))
                        .fetch(COURSE_ATTACHMENT.ATTACHMENT_ID));

    }

    @Override
    public Map<String, Integer> getExplicitLearningStatus(List<String> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return courseInfoCommonDao.execute(dsl ->
                    dsl.select(COURSE_INFO.ID, COURSE_INFO.EXPLICIT_LEARNING_STATUS)
                            .from(COURSE_INFO)
                            .where(COURSE_INFO.ID.in(ids))
                            .fetchMap(COURSE_INFO.ID, COURSE_INFO.EXPLICIT_LEARNING_STATUS)
            );
        }
        return Collections.emptyMap();
    }

    @Override
    public String getNameById(Integer type, String id) {
        PointBusinessTypeEnum businessTypeEnum = PointBusinessTypeEnum.of(type);

        List<String> businessName = courseInfoCommonDao.execute(e ->
                                                                        e.select(businessTypeEnum.getField())
                                                                         .from(businessTypeEnum.getTable())
                                                                         .where(businessTypeEnum.getTable().field("f_id", String.class).eq(id))
                                                                         .limit(1)
                                                                         .fetch(businessTypeEnum.getField()));

        return businessTypeEnum.getDesc() + businessName.get(0);
    }

    @Override
    public Map<String, String> getNameByIds(Integer type, List<String> ids) {
        PointBusinessTypeEnum businessType = PointBusinessTypeEnum.of(type);
        return courseInfoCommonDao.execute(e ->
                                                   e.select(businessType.getField(), businessType.getTable().field("f_id", String.class))
                                                    .from(businessType.getTable())
                                                    .where(businessType.getTable().field("f_id", String.class).in(ids))
                                                    .fetchMap(businessType.getTable().field("f_id", String.class), businessType.getField()));

    }


    @Override
    public Map<String, Integer> getSwitchMentor(String courseIds) {
        if (Objects.nonNull(courseIds)) {
            return courseInfoCommonDao.execute(dsl ->
                    dsl.select(COURSE_INFO.ID, COURSE_INFO.SWITCH_MENTOR)
                            .from(COURSE_INFO)
                            .where(COURSE_INFO.ID.in(courseIds.split(",")))
                            .fetchMap(
                                    COURSE_INFO.ID, COURSE_INFO.SWITCH_MENTOR
                            ));
        }
        return Collections.emptyMap();
    }

    @Override
    public List<String> publishedTheDayBeforeSubject(long startTime, long endTime) {
        return courseInfoCommonDao.execute(r->
                r.select(COURSE_INFO.ID)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                        .and(COURSE_INFO.UPDATE_DATE.between(startTime, endTime))
                        .and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT))
                        .fetch(COURSE_INFO.ID));
    }

    @Override
    public Map<String, Integer> getRequiredSectionProgress(List<String> courseIds, String memberId) {
        Map<String, Integer> resultMap = new HashMap<>();
        for (String courseId : courseIds) {
            Map<String, Integer> requiredSectionProgress = getRequiredSectionProgress(courseId, memberId);
            Integer finish = requiredSectionProgress.get("finish");
            Integer required = requiredSectionProgress.get("required");
            double result = (double) finish / required;
            Integer round = Math.toIntExact(Math.round(result * 100));
            resultMap.put(courseId, round);
        }
        return resultMap;
    }

    private Map<String, Boolean> checkExplicitLearningStatus2(Integer type, List<String> businessId,Optional<String> configString) {
        Map<String, Boolean> resMap = new HashMap<>();
        Integer enableStatus = 1;
        Integer subject = 0;
        Integer course = 0;
        Map<String, Integer> learningStatusMap = getExplicitLearningStatus(businessId);
        if (configString.isPresent()) {
            JSONObject configObject = JSONObject.parseObject(configString.get());
            subject = configObject.getInteger("subject");
            course = configObject.getInteger("course");
        }
        for (String id : businessId) {
            if (!learningStatusMap.isEmpty() && Objects.nonNull(learningStatusMap.get(id))) {
                subject = learningStatusMap.get(id);
                course = subject;
            }
            Boolean status = CourseInfo.BUSINESS_TYPE_SUBJECT.equals(type) ? enableStatus.equals(subject) : enableStatus.equals(course);
            resMap.put(id, status);
        }
        return resMap;
    }
}
