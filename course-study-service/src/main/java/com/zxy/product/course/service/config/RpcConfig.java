package com.zxy.product.course.service.config;

import com.zxy.common.rpc.spring.config.RemoteServicePackageConfig;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class RpcConfig {

    @Bean
    public RemoteServicePackageConfig remoteServicePackageConfig() {
        return new RemoteServicePackageConfig(new String[] {
                "com.zxy.product.course.api.course",
                "com.zxy.product.course.api.subject",
                "com.zxy.product.course.api.other",
                "com.zxy.product.course.api.audience",
                "com.zxy.product.course.api.grant",
                "com.zxy.product.course.api.sharding",
                "com.zxy.product.course.api.annualBill",
                "com.zxy.product.course.api.certificate",
                "com.zxy.product.course.api.remodeling",
                "com.zxy.product.course.api.chbn",
                "com.zxy.product.course.api.party",
                "com.zxy.product.course.api.pccw",
				"com.zxy.product.course.api.customize",
				"com.zxy.product.course.api.supplier",
				"com.zxy.product.course.api.offlineClass",
				"com.zxy.product.course.api.plan",
				"com.zxy.product.course.api.archived",
				"com.zxy.product.course.api.subAuthenticated",
                "com.zxy.product.course.api",
				"com.zxy.product.course.api.model.mentor",
				"com.zxy.product.course.api.gbdj",
				"com.zxy.product.course.api.home",
				"com.zxy.product.course.api.ishow",
				"com.zxy.product.course.api.ability",
				"com.zxy.product.course.api.ai"

		});
    }

}
