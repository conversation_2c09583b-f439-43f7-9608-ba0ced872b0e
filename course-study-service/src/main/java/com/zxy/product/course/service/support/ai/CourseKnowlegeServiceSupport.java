package com.zxy.product.course.service.support.ai;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.course.CourseKnowlegeService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.entity.CourseKnowledge;
import com.zxy.product.course.service.helper.CourseKnowledgeHelper;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.SelectJoinStep;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.tables.CourseKnowledge.COURSE_KNOWLEDGE;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/11:00
 * @Description:
 */
@Service
public class CourseKnowlegeServiceSupport implements CourseKnowlegeService {

    private static Logger logger = LoggerFactory.getLogger(CourseKnowlegeServiceSupport.class);



    private CommonDao<CourseKnowledge> courseKnowledgeCommonDao;

    private CourseKnowledgeHelper courseKnowledgeHelper;


    @Autowired
    public void setCourseKnowledgeHelper(CourseKnowledgeHelper courseKnowledgeHelper) {
        this.courseKnowledgeHelper = courseKnowledgeHelper;
    }

    @Autowired
    public void setCourseKnowledgeCommonDao(CommonDao<CourseKnowledge> courseKnowledgeCommonDao) {
        this.courseKnowledgeCommonDao = courseKnowledgeCommonDao;
    }


    @Override
    public void insert(CourseKnowledge courseKnowledge){
//        courseKnowledgeHelper.sendFile(file,courseKnowledge, Optional.empty());
        courseKnowledgeCommonDao.insert(courseKnowledge);
    }

    @Override
    public Optional<CourseKnowledge> getOptional(String id){
       return courseKnowledgeCommonDao.getOptional(id);
    }

    @Override
    public void update(CourseKnowledge courseKnowledge){
//        courseKnowledgeHelper.sendFile(file,courseKnowledge, Optional.ofNullable(courseKnowledge.getAttachmentUpId()));
        courseKnowledgeCommonDao.update(courseKnowledge);
    }

    @Override
    public void delete(String id){
        Optional<CourseKnowledge> courseKnowledge = getOptional(id);
        if(courseKnowledge.isPresent() && Objects.nonNull(courseKnowledge.get().getAttachmentUpId())){
            //删除第三方的文件
            boolean b = courseKnowledgeHelper.deleteFile(courseKnowledge.get().getAttachmentUpId());
            if(!b){
                throw new UnprocessableException(ErrorCode.deleteFileFail);
            }
        }
        courseKnowledgeCommonDao.execute(e->e.update(COURSE_KNOWLEDGE).set(COURSE_KNOWLEDGE.DELETE,CourseKnowledge.DELETE_YES)
                .where(COURSE_KNOWLEDGE.ID.eq(id)).execute());
    }

    @Override
    public void publish(String id, Integer status, CourseKnowledge courseKnowledge){
        Optional<CourseKnowledge> knowledge = getOptional(id);
        if(Objects.equals(status, CourseKnowledge.STATUS_PUBLISH)){
            courseKnowledge.setStatus(status);
            //如果没有发布时间，添加发布时间，有发布时间，不更新
            if(knowledge.isPresent() && Objects.isNull(knowledge.get().getShelveTime())){
                courseKnowledge.setShelveTime(System.currentTimeMillis());
            }
            courseKnowledgeCommonDao.update(courseKnowledge);
            return;
        }
        courseKnowledgeCommonDao.execute(e->e.update(COURSE_KNOWLEDGE).set(COURSE_KNOWLEDGE.STATUS,status)
                .where(COURSE_KNOWLEDGE.ID.eq(id)).execute());
    }


    @Override
    public PagedResult<CourseKnowledge> find(Integer page, Integer pageSize, Optional<String> name, Optional<Integer> status, Optional<Long> shelveTime, Optional<Long> shelveEndTime){
        List<Condition> conditions = Stream.of(name.map(COURSE_KNOWLEDGE.NAME::contains),
                status.map(COURSE_KNOWLEDGE.STATUS::eq),
                shelveTime.map(COURSE_KNOWLEDGE.SHELVE_TIME::ge),
                shelveEndTime.map(COURSE_KNOWLEDGE.SHELVE_TIME::le),
                Optional.of(CourseKnowledge.DELETE_YES).map(COURSE_KNOWLEDGE.DELETE::notEqual)
                ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

       return courseKnowledgeCommonDao.fetchPage(page,pageSize,e->{
            SelectJoinStep<Record> step =
                    e.select(Fields.start().add(COURSE_KNOWLEDGE.ID,COURSE_KNOWLEDGE.ATTACHMENT_ID,COURSE_KNOWLEDGE.ATTACHMENT_NAME,COURSE_KNOWLEDGE.ATTACHMENT_TYPE,
                                    COURSE_KNOWLEDGE.STATUS,COURSE_KNOWLEDGE.FINSH_STATUS,COURSE_KNOWLEDGE.SHELVE_TIME,COURSE_KNOWLEDGE.NAME,COURSE_KNOWLEDGE.KNOWLEDGE,
                                    COURSE_KNOWLEDGE.MODIFY_DATE).end())
                            .from(COURSE_KNOWLEDGE);
                return step.where(conditions).orderBy(COURSE_KNOWLEDGE.SHELVE_TIME.desc());
            }, r->{
                return r.into(CourseKnowledge.class);
            });
    }


}
