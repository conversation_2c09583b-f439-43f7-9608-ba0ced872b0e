package com.zxy.product.course.service.support.subject;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.*;
import com.zxy.product.course.api.audience.AudienceKService;
import com.zxy.product.course.api.certificate.CertificateRecordService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.remodeling.RemodelingTrainService;
import com.zxy.product.course.api.sharding.ShardingConfigService;
import com.zxy.product.course.api.subject.SubjectService;
import com.zxy.product.course.content.*;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.jooq.Tables;
import com.zxy.product.course.jooq.tables.pojos.CourseChapterEntity;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.*;
import static com.zxy.product.course.jooq.tables.CourseShelves.COURSE_SHELVES;
import static com.zxy.product.course.jooq.tables.SubjectTextArea.SUBJECT_TEXT_AREA;

/**
 * Created by TJ on 2017/9/6.
 */
@Service
public class SubjectServiceSupport implements SubjectService {
	public static final Logger logger = LoggerFactory.getLogger(SubjectServiceSupport.class);
    private static final int INSERT = 0;
    private static final int UPDATE = 1;
    /** service **/
    private CourseInfoService courseService;
    private BusinessTopicService businessTopicService;
    private SubjectAdvertisingService subjectAdvertisingService;
    private CourseCacheService courseCacheService;
    private StudyTaskService studyTaskService;
    private AudienceKService audienceKService;
    private CourseStudyProgressService progressSevice;
    private MessageSender messageSender;
    private StudyReportAnalysisManagersService studyReportAnalysisManagersService;



    /** dao **/
    private CommonDao<CourseInfo> dao;
    private CommonDao<KnowledgeInfo> knowledgeInfoCommonDao;
    private CommonDao<GenseeWebCast> genseeWebCastDao;
    private CommonDao<SubjectTextArea> textAreaDao;
    private CommonDao<CoursePhoto> photoDao;
    private CommonDao<CourseChapter> chapterDao;
    private CommonDao<CourseChapterSection> chapterSectionDao;
    private CommonDao<CourseAttachment> attachmentDao;
    private CommonDao<CourseShelves> shelvesDao;
    private CommonDao<Organization> organizationDao;
    private CommonDao<CourseSequence> sequenceDao;
    private CommonDao<StudyPushObject> pushObjectDao;
    private CommonDao<CourseChapterSection> courseChapterSectionCommonDao;
    private CertificateRecordService certificateRecordService;
    private Cache cache;
    private CommonDao<CourseChapterQuestionnaire> courseChapterQuestionnaireCommonDao;
    private CommonDao<SubjectTopicManager> subjectTopicManagerCommonDao;
    private CommonDao<PartyFocusConfig> partyFocusConfigDao;
    private CommonDao<PartyDemonstrationTrainingConfig> partyTrainingConfigDao;
    @Resource
    private MultidimensionalScoringService multidimensionalScoringService;

    private final String COURSE_PREFIX = "/study/course/detail/";
    private final String SUBJECT_PREFIX = "/study/subject/detail/";
    private final String KNOWLEDGE_PREFIX = "/knowledge/detail/";
    private final String GENSEE_PREFIX = "/activity/gensee/detail/";
    private final String DEFAULT_IMG_URL = "/images/default-cover/default_course.jpg";
    private RemodelingTrainService trainService;
    private ShardingConfigService tableConfigService;
    @Resource
    private CommonDao<DeleteDataCourse> dataCourseCommonDao;
    @Autowired
    public void setCourseChapterQuestionnaireCommonDao(CommonDao<CourseChapterQuestionnaire> courseChapterQuestionnaireCommonDao) {
        this.courseChapterQuestionnaireCommonDao = courseChapterQuestionnaireCommonDao;
    }

    @Autowired
    public void setCache(Cache cache) {
		this.cache = cache;
	}

	@Autowired
    public void setAudienceKService(AudienceKService audienceKService) {
        this.audienceKService = audienceKService;
    }

    @Autowired
    public void setCourseService(CourseInfoService courseService) {
        this.courseService = courseService;
    }

    @Autowired
    public void setBusinessTopicService(BusinessTopicService businessTopicService) {
        this.businessTopicService = businessTopicService;
    }

    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }

    @Autowired
    public void setSubjectAdvertisingService(SubjectAdvertisingService subjectAdvertisingService) {
        this.subjectAdvertisingService = subjectAdvertisingService;
    }

    @Autowired
    public void setDao(CommonDao<CourseInfo> dao) {
        this.dao = dao;
    }

    @Autowired
    public void setTextAreaDao(CommonDao<SubjectTextArea> textAreaDao) {
        this.textAreaDao = textAreaDao;
    }

    @Autowired
    public void setPhotoDao(CommonDao<CoursePhoto> photoDao) {
        this.photoDao = photoDao;
    }

    @Autowired
    public void setChapterDao(CommonDao<CourseChapter> chapterDao) {
        this.chapterDao = chapterDao;
    }

    @Autowired
    public void setChapterSectionDao(CommonDao<CourseChapterSection> chapterSectionDao) {
        this.chapterSectionDao = chapterSectionDao;
    }

    @Autowired
    public void setAttachmentDao(CommonDao<CourseAttachment> attachmentDao) {
        this.attachmentDao = attachmentDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setStudyReportAnalysisManagersService(StudyReportAnalysisManagersService studyReportAnalysisManagersService) {
        this.studyReportAnalysisManagersService = studyReportAnalysisManagersService;
    }

    @Autowired
    public void setShelvesDao(CommonDao<CourseShelves> shelvesDao) {
        this.shelvesDao = shelvesDao;
    }

    @Autowired
    public void setOrganizationDao(CommonDao<Organization> organizationDao) {
        this.organizationDao = organizationDao;
    }

    @Autowired
    public void setSequenceDao(CommonDao<CourseSequence> sequenceDao) {
        this.sequenceDao = sequenceDao;
    }

    @Autowired
    public void setPushObjectDao(CommonDao<StudyPushObject> pushObjectDao) {
        this.pushObjectDao = pushObjectDao;
    }

    @Autowired
    public void setCourseChapterSectionCommonDao(CommonDao<CourseChapterSection> courseChapterSectionCommonDao) {
        this.courseChapterSectionCommonDao = courseChapterSectionCommonDao;
    }

    @Autowired
    public void setStudyTaskService(StudyTaskService studyTaskService) {
        this.studyTaskService = studyTaskService;
    }

    @Autowired
    public void setProgressSevice(CourseStudyProgressService progressSevice) {
		this.progressSevice = progressSevice;
	}

    @Autowired
    public void setCertificateRecordService(CertificateRecordService certificateRecordService) {
        this.certificateRecordService = certificateRecordService;
    }

    @Autowired
    public void setKnowledgeInfoCommonDao(CommonDao<KnowledgeInfo> knowledgeInfoCommonDao) {
        this.knowledgeInfoCommonDao = knowledgeInfoCommonDao;
    }

    @Autowired
    public void setGenseeWebCastDao(CommonDao<GenseeWebCast> genseeWebCastDao) {
        this.genseeWebCastDao = genseeWebCastDao;
    }
    @Autowired
    public SubjectServiceSupport setTrainService(RemodelingTrainService trainService) {
        this.trainService = trainService;
        return this;
    }
    @Autowired
    public SubjectServiceSupport setTableConfigService(ShardingConfigService tableConfigService) {
        this.tableConfigService = tableConfigService;
        return this;
    }

    @Autowired
    public void setSubjectTopicManagerCommonDao(CommonDao<SubjectTopicManager> subjectTopicManagerCommonDao) {
        this.subjectTopicManagerCommonDao = subjectTopicManagerCommonDao;
    }

    @Autowired
    public void setPartyFocusConfigDao(CommonDao<PartyFocusConfig> partyFocusConfigDao) {
        this.partyFocusConfigDao = partyFocusConfigDao;
    }

    @Autowired
    public void setPartyTrainingConfigDao(CommonDao<PartyDemonstrationTrainingConfig> partyTrainingConfigDao) {
        this.partyTrainingConfigDao = partyTrainingConfigDao;
    }

    /**
     * 组装党校渠道字段
     */
    private void assemblePartyClient(Optional<Integer> publishClient, Optional<Integer> isParty, List<Condition> param) {
        isParty.map(t -> param.add(t == 0 ? COURSE_INFO.IS_PARTY.isNull().or(COURSE_INFO.IS_PARTY.eq(t)) : COURSE_INFO.IS_PARTY.eq(t)));
        if (isParty.isPresent() && CourseInfo.PARTY_BUILING_COURSE_TRUE.equals(isParty.get())) {
            if (publishClient.isPresent()) {
                param.add(COURSE_INFO.PUBLISH_CLIENT.eq(publishClient.get()));
            } else {
                param.add(COURSE_INFO.PUBLISH_CLIENT.in(CourseInfo.PUBLISH_CLIENT_ALL, CourseInfo.PUBLISH_CLIENT_PC));
            }
        } else {
            publishClient.ifPresent(t -> param.add(COURSE_INFO.PUBLISH_CLIENT.eq(t)));
        }
    }

	@Override
    public PagedResult<CourseInfo> find(int pageNum,
                                        int pageSize,
                                        String memberId,
                                        Optional<String> name,
                                        Optional<String> organizationId,
                                        Optional<String> code,
                                        Optional<Integer> status,
                                        Optional<String> releaseUserId,
                                        Optional<Integer> publishClient,
                                        Optional<Integer> subjectType,
                                        Optional<Long> beginBeginDate,
                                        Optional<Long> beginEndDate,
                                        Optional<Long> endBeginDate,
                                        Optional<Long> endEndDate,
                                        Optional<Long> shelveBeginTime,
                                        Optional<Long> shelveEndTime,
                                        Optional<Integer> isParty,
                                        Optional<String[]> selectIds,
                                        List<String> organizationIds, Optional<Integer> businessType,
                                        Optional<Integer> subjectTypeSelector) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门

        return dao.execute(x -> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(
                            COURSE_INFO.ID,
                            COURSE_INFO.NAME,
                            COURSE_INFO.CODE,
                            COURSE_INFO.BEGIN_DATE,
                            COURSE_INFO.END_DATE,
                            COURSE_INFO.PUBLISH_CLIENT,
                            COURSE_INFO.STATUS,
                            COURSE_INFO.URL,
                            COURSE_INFO.SHELVE_TIME,
                            COURSE_INFO.STYLES,
                            COURSE_INFO.SKIN_TYPE,
                            COURSE_INFO.SUBJECT_TYPE)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .end()); // 查询list

            SelectSelectStep<Record> selectCountField = x.select(Fields.start()
                    .add(COURSE_INFO.ID.countDistinct()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                    name.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    code.map(COURSE_INFO.CODE::contains),
                    status.map(COURSE_INFO.STATUS::eq),
                    releaseUserId.map(COURSE_INFO.RELEASE_MEMBER_ID::eq),
                    //publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq),
                    beginBeginDate.map(COURSE_INFO.BEGIN_DATE::ge),
                    beginEndDate.map(COURSE_INFO.BEGIN_DATE::le),
                    endBeginDate.map(COURSE_INFO.END_DATE::ge),
                    endEndDate.map(COURSE_INFO.END_DATE::le),
                    shelveBeginTime.map(COURSE_INFO.SHELVE_TIME::ge),
                    shelveEndTime.map(COURSE_INFO.SHELVE_TIME::le),
                    Optional.ofNullable(organizationIds).map(COURSE_INFO.ORGANIZATION_ID::in),
                    subjectType.map(t -> t == 1 ? COURSE_INFO.URL.isNull() : COURSE_INFO.URL.isNotNull()),
                    subjectTypeSelector.map(COURSE_INFO.SUBJECT_TYPE::eq))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(selectIds.map(COURSE_INFO.ID::notIn).orElse(DSL.trueCondition()));
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(businessType.orElse(CourseInfo.BUSINESS_TYPE_SUBJECT)));
            assemblePartyClient(publishClient, isParty, param);

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .where(param);
                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((pageNum - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public CourseInfo get(String id) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        com.zxy.product.course.jooq.tables.Organization releaseOrgTable = ORGANIZATION.as("releaseOrg"); // 发布部门
        com.zxy.product.course.jooq.tables.Organization sponsoringOrg = ORGANIZATION.as("sponsoringOrg"); // 主办部门
        com.zxy.product.course.jooq.tables.Member releaseUserTable = MEMBER.as("releaseUser"); // 发布人

        Record result = dao.execute(x -> x
                .select(Fields.start().add(COURSE_INFO)
                        .add(organizationTable)
                        .add(releaseOrgTable)
                        .add(releaseUserTable)
                        .add(sponsoringOrg.ID, sponsoringOrg.NAME)
                        .end())
                .from(COURSE_INFO).leftJoin(organizationTable).on(COURSE_INFO.ORGANIZATION_ID.eq(organizationTable.ID))
                .leftJoin(releaseOrgTable).on(COURSE_INFO.RELEASE_ORG_ID.eq(releaseOrgTable.ID))
                .leftJoin(releaseUserTable).on(COURSE_INFO.RELEASE_MEMBER_ID.eq(releaseUserTable.ID))
                .leftJoin(sponsoringOrg).on(COURSE_INFO.SPONSORING_ORG.eq(sponsoringOrg.ID))
                .where(COURSE_INFO.ID.eq(id)).fetchOne());

        List<SubjectTopicManager> topicManagers = subjectTopicManagerCommonDao.execute(w -> w.select(Fields.start()
                                .add(SUBJECT_TOPIC_MANAGER)
                                .add(MEMBER.FULL_NAME).end())
                        .from(SUBJECT_TOPIC_MANAGER)
                        .leftJoin(MEMBER).on(MEMBER.ID.eq(SUBJECT_TOPIC_MANAGER.MEMBER_ID))
                        .where(SUBJECT_TOPIC_MANAGER.COURSE_ID.eq(id)))
                .fetch(w -> {
                    SubjectTopicManager into = w.into(SUBJECT_TOPIC_MANAGER).into(SubjectTopicManager.class);
                    into.setMemberName(w.getValue(MEMBER.FULL_NAME));
                    return into;
                });

        if(result ==null) return null;

        CourseInfo course = result.into(COURSE_INFO).into(CourseInfo.class);
        course.setOrganization(result.into(organizationTable).into(Organization.class));
        course.setReleaseOrg(result.into(releaseOrgTable).into(Organization.class));
        course.setSponsoringOrganization(result.into(sponsoringOrg).into(Organization.class));
        course.setReleaseUser(result.into(releaseUserTable).into(Member.class));
        // 查询附件
        course.setCourseAttachments(courseService.findCourseAttachmentByCourseId(id));
        // 查询专题相册、广告、文字区域
        course.setPhotos(courseService.findPhotosBySubjectId(id));
        course.setAdvertisings(subjectAdvertisingService.findAdvertisingBySubjectId(id));
        course.setTextAreas(textAreaDao.fetch(SUBJECT_TEXT_AREA.SUBJECT_ID.eq(id)));
        course.setSubjectTopicManager(topicManagers);
        // 返回之中添加证书id
        BusinessCertificate businessCertificate = certificateRecordService.getByBusinessId(id);
        if (businessCertificate != null) {
            course.setCertificateId(businessCertificate.getCertificateId());
            // 如果专题关联了证书查询一下专题中是否有学员获得证书
            Optional<String> recordId = certificateRecordService.findWhetherIssueCertificateByBusniessId(course.getId());
            course.setIsCertificateRecord(recordId.isPresent() && recordId.get() != null ? 1 : 0);
        }
        //专题排行榜 新增年度样式，2024年12月后的都无法设置样式5
        if(course.getCreateTime() < LocalDate.of(2024, 12, 13).atStartOfDay(ZoneOffset.ofHours(8)).toInstant().toEpochMilli()){
            course.setStyleHistoryFlag(1);
        }

        // 增加返回关联能力
        course.setCourseAbilities(courseService.findCourseAbilitySimpleByCourseId(id));
        return course;
    }

    @Override
    public CourseInfo add(String name,
                          String createUserId,
                          int publishClient,
                          String organizationId,
                          Optional<Integer> publishType,
                          Optional<Long> beginDate,
                          Optional<Long> endDate,
                          Optional<String> desc,
                          Optional<String> descText,
                          Optional<String> releaseUser,
                          Optional<String> releaseOrg,
                          Optional<String> code,
                          Optional<Integer> studyDays,
                          Optional<String> cover,
                          Optional<String> coverPath,
                          Optional<String> coverMaterialId,
                          List<CourseChapter> courseChapters,
                          List<CourseAttachment> courseAttachments,
                          List<AudienceItem> audienceItems,
                          Optional<CourseShelves> courseShelves,
                          Optional<String> styles,
                          Optional<String> topicIds,
                          List<SubjectAdvertising> advertisingList,
                          List<CoursePhoto> photos,
                          List<SubjectTextArea> textAreas,
                          Optional<String> url,
                          Optional<Integer> shareSub,
                          Optional<Integer> addType,
                          Optional<Integer> isPublic, Optional<String> descriptionApp, Optional<String> certificateId, Optional<Integer> isSign,
                          List<SubjectTopicManager> managerItems, Optional<Integer> explicitLearningStatus, Optional<Integer> businessType,
                          Optional<Integer> learnSequence,
                          Optional<Integer> learningSituation, List<StudyReportAnalysisManagers> studyReportAnalysisManagers, Optional<String> sponsoringOrg, Optional<String> subjectMisCode, Optional<Integer> subjectType,
                          Optional<Long> certificateTime,Optional<Integer> certificateType,Optional<Integer>skinType,Optional<String> pcBanner,Optional<String> pcBannerPath) {
        CourseInfo c = new CourseInfo();
        c.forInsert();
        c.setName(name);
        c.setCreateMemberId(createUserId);
        c.setStatus(CourseInfo.STATUS_UNPUBLISHED);
        c.setPublishClient(publishClient);
        c.setDeleteFlag(CourseInfo.DELETE_FLAG_NO);
        c.setOrganizationId(organizationId);
        c.setBusinessType(businessType.orElse(CourseInfo.BUSINESS_TYPE_SUBJECT));
        c.setSwitchMentor(CourseInfo.SWITCH_MENTOR_OFF);


        learnSequence.ifPresent(c::setLearnSequence);
        publishType.ifPresent(c::setPublishType);
        beginDate.ifPresent(c::setBeginDate);
        endDate.ifPresent(c::setEndDate);
        desc.ifPresent(c::setDescription);
        descText.ifPresent(c::setDescriptionText);
        releaseUser.ifPresent(c::setReleaseMemberId);
        releaseOrg.ifPresent(c::setReleaseOrgId);
        studyDays.ifPresent(c::setStudyDays);
        cover.ifPresent(c::setCover);
        coverPath.ifPresent(c::setCoverPath);
        coverMaterialId.ifPresent(c::setCoverMaterialId);
        url.ifPresent(c::setUrl);
        shareSub.ifPresent(c::setShareSub);
        addType.ifPresent(c::setAddType);
        isPublic.ifPresent(c::setIsPublic);
        descriptionApp.ifPresent(c::setDescriptionApp);// updated by wangdongyan 简介--app加粗换行样式
        explicitLearningStatus.ifPresent(c::setExplicitLearningStatus);
        learningSituation.ifPresent(c::setLearningSituation);
        sponsoringOrg.ifPresent(c::setSponsoringOrg);
        skinType.ifPresent(c::setSkinType);
        pcBanner.ifPresent(c::setPcBanner);
        pcBannerPath.ifPresent(c::setPcBannerPath);

        subjectMisCode.ifPresent(c::setSubjectMisCode);
        subjectType.ifPresent(c::setSubjectType);

        styles.ifPresent(s -> {
            c.setStyles(s);
            subjectAdvertisingService.save(c.getId(), createUserId, advertisingList);
            this.savePhotos(c.getId(), photos);
            this.saveTextArea(c.getId(), textAreas);
        });
        // 如果专题配置证书，需添加业务关联关系表
        certificateId.ifPresent(id -> {
            certificateRecordService.insert(c.getId(), id, BusinessCertificate.BUSINESS_TYPE_SUBJECT);
            //如果存在证书，则添加证书更新时间，否则为空
            c.setCertificateUpdateTime(System.currentTimeMillis());
            certificateType.ifPresent(c::setCertificateType);
            certificateTime.ifPresent(c::setCertificateTime);
        });
        //专题是否需要报名
        isSign.ifPresent(c::setIsSign);
        courseService.saveCourseChapters(c.getId(), courseChapters,createUserId);
        courseService.saveCourseAttachs(c.getId(), courseAttachments);
        courseService.saveSubjectManager(c.getId(), managerItems);
        audienceKService.updateAudience(c.getId(),AudienceObject.BUSINESS_TYPE_SUBJECT,createUserId, URI, audienceItems);

        c.setOpen(CourseInfo.OPEN_0);

        // 【学情分析添加管理员】
        if(!studyReportAnalysisManagers.isEmpty()) {
            studyReportAnalysisManagersService.saveOrUpdate(c.getId(), studyReportAnalysisManagers);
        }

        if (CollectionUtils.isNotEmpty(audienceItems)) {
            // 查询当前课程是否是全员发布（中国移动包含子节点）
            long rootCount = audienceItems.stream().filter(item -> "1".equals(item.getJoinId()) && item.getJoinType() == 2).count();
            // 查询当前课程是否是内部发布（内部组织包含子节点）
            long insideCount = audienceItems.stream().filter(item -> "10000001".equals(item.getJoinId()) && item.getJoinType() == 2).count();

            if (rootCount > 0) {
                c.setOpen(CourseInfo.OPEN_1);
            } else if (insideCount > 0) {
                c.setOpen(CourseInfo.OPEN_2);
            } else {
                c.setOpen(CourseInfo.OPEN_0);
            }
        }

        topicIds.ifPresent(ids -> businessTopicService.saveBusinessTopic(c.getId(), BusinessTopic.BUSINESS_TYPE_SUBJECT, ids.split(",")));

        // 上架(发布)
        courseShelves.ifPresent(s -> {
            c.setReleaseTime(System.currentTimeMillis());
            publishType.ifPresent(type -> {
                //xwolf
                c.setStatus(CourseInfo.STATUS_IN_SHELVES);
                //c.setStatus(CourseInfo.STATUS_THE_TEST);
                if (type == CourseInfo.PUBLISH_TYPE_FORMAL) {
                    c.setShelveTime(System.currentTimeMillis());
                   // c.setStatus(CourseInfo.STATUS_SHELVES);
                }
                // add by wdy 专题发布状态下编辑专题走上架逻辑时，将缓存设为发布中，防止连续点击
                courseCacheService.setSubjectPublishStatus(c.getId());
            });
        });

        c.setUpdateDate(System.currentTimeMillis());
        dao.insert(c);
        updateCourseCode(c);
        messageSender.send(MessageTypeContent.COURSE_INFO_INSERT, MessageHeaderContent.ID, c.getId());
        courseShelves.ifPresent(s -> {
            shelves(c.getId(), s, c.getPublishType());
            this.syncKnowledge(courseAttachments, c);
            this.addReferenceId(courseChapters, INSERT);
        });
        // 新增专题清除缓存，并重新添加
        courseCacheService.clearCacheCourseInfoMap(c.getBusinessType());
        return c;
    }

    public int judgeSwitchMentor(String  jsonStr) {
        try {
            ObjectMapper mapper = new ObjectMapper();
            JsonNode rootNode = mapper.readTree(jsonStr);
            JsonNode regionsNode = rootNode.path("regions");

            for (JsonNode region : regionsNode) {
                if ("region13".equals(region.path("code").asText())) {
                    JsonNode moduleNode = region.path("regionModule");
                    boolean display = moduleNode.path("display").asBoolean();
                    if (display){
                        return CourseInfo.SWITCH_MENTOR_ON;
                    }else {
                        return CourseInfo.SWITCH_MENTOR_OFF;
                    }
                }
            }
            return CourseInfo.SWITCH_MENTOR_NULL;
        } catch (IOException e) {
            return CourseInfo.SWITCH_MENTOR_NULL;
        }
    }

    @Override
    public CourseInfo update(String id,
                             String memberId,
                             int publishClient,
                             Optional<Integer> publishType,
                             Optional<String> name,
                             Optional<Long> beginDate,
                             Optional<Long> endDate,
                             Optional<String> desc,
                             Optional<String> descText,
                             Optional<String> releaseUser,
                             Optional<String> releaseOrg,
                             Optional<String> organizationId,
                             Optional<String> code,
                             Optional<Integer> learnSequence,
                             Optional<Integer> studyDays,
                             Optional<String> cover,
                             Optional<String> coverPath,
                             Optional<String> coverMaterialId,
                             Optional<Integer> shareSub,
                             List<CourseChapter> courseChapters,
                             List<CourseAttachment> courseAttachments,
                             List<AudienceItem> audienceItems,
                             Optional<CourseShelves> courseShelves,
                             Optional<String> styles,
                             Optional<String> topicIds,
                             List<SubjectAdvertising> advertisingList,
                             List<CoursePhoto> photos,
                             List<SubjectTextArea> textAreas,
                             Optional<String> url,
                             Optional<Integer> addType,
                             Optional<Integer> isPublic, Optional<String> descriptionApp, Optional<String> certificateId, Optional<Integer> isSign,
                             List<SubjectTopicManager> managerItems, Optional<Integer> explicitLearningStatus,
                             Optional<Integer> learningSituation, List<StudyReportAnalysisManagers> studyReportAnalysisManagers, Optional<String> sponsoringOrg, Optional<String> subjectMisCode, Optional<Integer> subjectType,
                             Optional<Long> certificateTime, Optional<Integer> certificateType,Optional<Integer>skinType,Optional<String> pcBanner,Optional<String> pcBannerPath) {
        CourseInfo c = dao.getOptional(id).orElse(null);
        if (c == null)
            return null;

        Integer oldSubjectType = c.getSubjectType();
        c.setPublishClient(publishClient);
        publishType.ifPresent(c::setPublishType);
        name.ifPresent(c::setName);
        c.setBeginDate(beginDate.orElse(null));
        c.setEndDate(endDate.orElse(null));
        c.setDescription(desc.orElse(null));
        //desc.ifPresent(c::setDescription);
        c.setDescriptionText(descText.orElse(null));
        //descText.ifPresent(c::setDescriptionText);
        c.setReleaseMemberId(releaseUser.orElse(null));
        c.setReleaseOrgId(releaseOrg.orElse(null));
        code.ifPresent(c::setCode);
        organizationId.ifPresent(c::setOrganizationId);
        learnSequence.ifPresent(c::setLearnSequence);
        studyDays.ifPresent(c::setStudyDays);
        cover.ifPresent(c::setCover);
        coverPath.ifPresent(c::setCoverPath);
        coverMaterialId.ifPresent(c::setCoverMaterialId);
        c.setDescriptionApp(descriptionApp.orElse(null));// updated by wangdongyan 简介--app加粗换行样式
        url.ifPresent(c::setUrl);
        shareSub.ifPresent(c::setShareSub);
        addType.ifPresent(c::setAddType);
        isPublic.ifPresent(c::setIsPublic);
        explicitLearningStatus.ifPresent(c::setExplicitLearningStatus);
        learningSituation.ifPresent(c::setLearningSituation);
        c.setSponsoringOrg(sponsoringOrg.orElse(null));
        skinType.ifPresent(c::setSkinType);
        pcBanner.ifPresent(c::setPcBanner);
        pcBannerPath.ifPresent(c::setPcBannerPath);

        c.setSubjectMisCode(subjectMisCode.orElse(null));
        subjectType.ifPresent(c::setSubjectType);

        styles.ifPresent(s -> {
            int judgeSwitchMentor = judgeSwitchMentor(s);
            if (!Objects.equals(CourseInfo.SWITCH_MENTOR_NULL, judgeSwitchMentor)){
                c.setSwitchMentor(judgeSwitchMentor);
            }
            c.setStyles(s);
            subjectAdvertisingService.save(c.getId(), c.getCreateMemberId(), advertisingList);
            this.savePhotos(c.getId(), photos);
            this.saveTextArea(c.getId(), textAreas);
        });
        // 如果专题配置证书，需添加业务关联关系表，反之则删除
        if (certificateId.isPresent()) {
            c.setCertificateType(certificateType.orElse(null));
            c.setCertificateTime(certificateTime.orElse(null));
            certificateRecordService.insert(c.getId(), certificateId.get(), BusinessCertificate.BUSINESS_TYPE_SUBJECT, c);
        } else {
            c.setCertificateType(null);
            c.setCertificateTime(null);
            certificateRecordService.delete(c.getId());
        }
        //专题是否需要报名
        isSign.ifPresent(c::setIsSign);

        // 下架状态编辑主题逻辑
        if(c.getStatus() != null && !c.getStatus().equals(CourseInfo.STATUS_SHELVES) && !c.getStatus().equals(CourseInfo.STATUS_THE_TEST)) {
            List<CourseChapter> courseChapterList = chapterDao.fetch(COURSE_CHAPTER.COURSE_ID.eq(id).and(COURSE_CHAPTER.VERSION_ID.isNull()));
            // updated by wangdongyan 2018-06-20 查询课程id对应的所有section的id集合，用于清除缓存
            logger.error("开始查询章节id时间：" + System.currentTimeMillis());
            List<String> sectionIds = courseCacheService.getSectionIds(id);
            logger.error("开始查询版本id时间：" + System.currentTimeMillis());
            List<String> versionIds = courseCacheService.getVersionIds(id);
            logger.error("结束查询版本id时间：" + System.currentTimeMillis());
            List<String> knowledgeIds = new ArrayList<String>();
            courseChapterList.stream().forEach(chapter -> {
                chapterDao.delete(COURSE_CHAPTER.ID.eq(chapter.getId()));
                dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourse(DeleteDataCourse.getTableName(COURSE_CHAPTER), chapter.getId(),""));
                List<CourseChapterSection> sections = chapterSectionDao.fetch(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(chapter.getId()));
                sections.stream().forEach(section -> {
                	if (section.getSectionType() == 15) {
                		knowledgeIds.add(section.getResourceId());
                	}
                });
                List<String> sectionIdListIds = sections.parallelStream().map(CourseChapterSection::getId).collect(Collectors.toList());
                List<String> referenceListIds = sections.parallelStream().map(CourseChapterSection::getReferenceId).collect(Collectors.toList());
                List<String> idList = chapterSectionDao.execute(dsl -> dsl.select(COURSE_CHAPTER_SECTION.ID).from(COURSE_CHAPTER_SECTION).where(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(chapter.getId())).fetch(COURSE_CHAPTER_SECTION.ID));
                chapterSectionDao.delete(idList);
                dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(DeleteDataCourse.getTableName(COURSE_CHAPTER_SECTION), idList,""));
                courseChapterQuestionnaireCommonDao.execute(x ->
                        x.update(COURSE_CHAPTER_QUESTIONNAIRE).set(COURSE_CHAPTER_QUESTIONNAIRE.STATUS, CourseChapterSection.QUESTIONNAIRE_FLAG_NO))
                        .where(COURSE_CHAPTER_QUESTIONNAIRE.TYPE.eq(CourseChapterQuestionnaire.COURSE_TYPE_ONLINE)
                                .and(COURSE_CHAPTER_QUESTIONNAIRE.REFERENCE_ID.in(sectionIdListIds).or(COURSE_CHAPTER_QUESTIONNAIRE.REFERENCE_ID.in(referenceListIds)))).execute();
            });
            logger.error("结束删除章节时间：" + System.currentTimeMillis());
         // updated by wangdongyan 10-15 知识使用量更新
            this.updateKnowledgeAsyncNum(c.getId(), knowledgeIds, UPDATE);
         // updated by wangdongyan 2018-06-20
            for (String sectionId : sectionIds) {
            	courseCacheService.clearChapterSection(sectionId);
			}
            logger.error("结束清除section缓存时间：" + System.currentTimeMillis());
            for (String versionId : versionIds) {
            	courseCacheService.clearCourseChapter(id, versionId);
			}
            logger.error("结束清除chapter缓存时间：" + System.currentTimeMillis());
        }else {
            // 发布状态下编辑，清除专题版本
            c.setVersionId(null);
        }
        // 删除附件时更新从知识库中选择的知识的使用量 updated by wangdongyan 2018-10-15
        deleteAttachmenct(id);

        logger.error("开始计算受众时间，currentTime={}", System.currentTimeMillis());
        // 重设课程和分享组织的受众对象,update受众项
        audienceKService.updateAudience(c.getId(),AudienceObject.BUSINESS_TYPE_SUBJECT,memberId, URI, audienceItems);
        logger.error("完成计算受众时间，currentTime={}", System.currentTimeMillis());

        c.setOpen(CourseInfo.OPEN_0);
        if (CollectionUtils.isNotEmpty(audienceItems)) {
            // 查询当前课程是否是全员发布（中国移动包含子节点）
            long rootCount = audienceItems.stream().filter(item -> "1".equals(item.getJoinId()) && item.getJoinType() == 2).count();
            // 查询当前课程是否是内部发布（内部组织包含子节点）
            long insideCount = audienceItems.stream().filter(item -> "10000001".equals(item.getJoinId()) && item.getJoinType() == 2).count();

            if (rootCount > 0) {
                c.setOpen(CourseInfo.OPEN_1);
            } else if (insideCount > 0) {
                c.setOpen(CourseInfo.OPEN_2);
            } else {
                c.setOpen(CourseInfo.OPEN_0);
            }
        }

        saveCourseChapters(id, courseChapters,memberId);
        courseService.saveCourseAttachs(id, courseAttachments);
        courseService.updateSubjectManager(id, managerItems);

        businessTopicService.delete(id, BusinessTopic.BUSINESS_TYPE_SUBJECT);//删除标签
        topicIds.ifPresent(ids -> businessTopicService.saveBusinessTopic(c.getId(), BusinessTopic.BUSINESS_TYPE_SUBJECT, ids.split(",")));

        // 上架(发布)
        courseShelves.ifPresent(s -> {
            c.setReleaseTime(System.currentTimeMillis());
            publishType.ifPresent(type -> {
                //xwolf 发布中 c.setStatus(CourseInfo.STATUS_THE_TEST);
                c.setStatus(CourseInfo.STATUS_IN_SHELVES);
                if (type == CourseInfo.PUBLISH_TYPE_FORMAL) {
                    //c.setStatus(CourseInfo.STATUS_SHELVES);
                    if (c.getShelveTime() == null) c.setShelveTime(System.currentTimeMillis());
                }
                // add by wdy 专题发布状态下编辑专题走上架逻辑时，将缓存设为发布中，防止连续点击
                courseCacheService.setSubjectPublishStatus(c.getId());
            });

            //专题培训班埋点-上架,关联的培训班数据同步
            messageSender.send(com.zxy.product.course.content.MessageTypeContent.IHR_TOPIC_TRAIN_SYNC,
                    MessageHeaderContent.OPERATION_STATE,CourseInfo.OPERATION_STATE_TOPIC_PLUBLISH,
                    MessageHeaderContent.TOPIC_ID, id);
        });
        c.setModifyDate(null);
        c.setUpdateDate(System.currentTimeMillis());
        dao.update(c);

        //todo 2025-08-03发版暂时不发消息， 注释，待后续功能完善打开
//        calculateConcentrateStudyHours(subjectType,oldSubjectType,id);

        if(!courseShelves.isPresent()) { // 此次修改不是发布动作
            int status = c.getStatus() == null ? 0 : c.getStatus();
            if(status == CourseInfo.STATUS_SHELVES || status == CourseInfo.STATUS_THE_TEST) { // 此次修改是在发布状态下修改
                // 发布状态下修改,重新走一遍上架逻辑
                CourseShelves shelves = insertCourseShelves(c);
                // add by wdy 专题发布状态下编辑专题走上架逻辑时，将缓存设为发布中，防止连续点击
                courseCacheService.setSubjectPublishStatus(c.getId());
                messageSender.send(MessageTypeContent.COURSE_SHELVES,
                    MessageHeaderContent.ID, shelves.getId());
                this.addReferenceId(courseChapters, UPDATE);
                this.syncKnowledge(courseAttachments, c);
            }
        }
        courseShelves.ifPresent(s -> {
            shelves(c.getId(), s,publishType.get());
            this.syncKnowledge(courseAttachments, c);
            this.addReferenceId(courseChapters, UPDATE);
        });
        messageSender.send(MessageTypeContent.COURSE_INFO_UPDATE, MessageHeaderContent.ID, c.getId(), MessageHeaderContent.NAME, c.getName());
        return c;
    }

    private void calculateConcentrateStudyHours(Optional<Integer> targetSubjectType, Integer oldSubjectType, String id) {
        if (!targetSubjectType.isPresent() || id == null || oldSubjectType == null) {
            return;
        }

        int newType = targetSubjectType.get();

        if (isConcentrateType(oldSubjectType) && isSelfStudyType(newType)) {
            sendConcentrateMessage(id, "CANCEL_CONCENTRATE");
        } else if (isSelfStudyType(oldSubjectType) && isConcentrateType(newType)) {
            sendConcentrateMessage(id, "RECALCULATE_CONCENTRATE");
        }
    }

    private boolean isConcentrateType(Integer subjectType) {
        return subjectType != null && (subjectType == CourseInfo.SUBJECT_TYPE_UNIFIED_ORGANIZATION
                || subjectType == CourseInfo.SUBJECT_TYPE_PROVINCE_ORGANIZATION);
    }

    private boolean isSelfStudyType(Integer subjectType) {
        return subjectType != null && subjectType == CourseInfo.SUBJECT_TYPE_SELF_STUDY_ONLINE;
    }

    private void sendConcentrateMessage(String id, String direction) {
        messageSender.send(
                MessageTypeContent.CONCENTRATE_STUDY_HOURS,
                MessageHeaderContent.ID, id,
                MessageHeaderContent.PARAMS, direction
        );
    }
    /**
     * 新增上架信息
     * @param c
     * @return
     */
    private CourseShelves insertCourseShelves(CourseInfo c) {
        shelvesDao.execute(d -> d.update(COURSE_SHELVES).set(COURSE_SHELVES.STATUS, CourseShelves.STATUS_FAILURE)
                .where(COURSE_SHELVES.COURSE_ID.eq(c.getId())).execute());
        CourseShelves shelves = new CourseShelves();
        shelves.forInsert();
        shelves.setCourseId(c.getId());
        shelves.setIsFirst(1);
        shelves.setNoticeUser(0);
        shelves.setNoticeManager(0);
        shelves.setStatus(1);
        shelves.setRule(2);
        shelvesDao.insert(shelves);
        return shelves;
    }

    @Override
    public int delete(String id, String currentUserId) {
        CourseInfo course = dao.get(id);
        int result = 0;
        Integer count = progressSevice.totalStudyMember(id);
        // 被人学过的则不能删除
        if (count != null && count > 0) {
            throw new UnprocessableException(ErrorCode.DataViolation);
        }
        // 加到推送的不能删除
        if(pushObjectDao.fetchOne(STUDY_PUSH_OBJECT.BUSINESS_ID.eq(id)).isPresent()) {
            throw new UnprocessableException(ErrorCode.DataViolation);
        }

        if (course.getStatus() != null && course.getStatus().equals(CourseInfo.STATUS_UNPUBLISHED)) {
            result = dao.delete(id);
            // updated by wangdongyan 2018-06-20 查询出课程对应所有section的id集合用于清除缓存
            List<String> sectionIds = courseCacheService.getSectionIds(id);
            List<String> versionIds = courseCacheService.getVersionIds(id);
            // 删除章节、附件
            chapterDao.delete(COURSE_CHAPTER.COURSE_ID.eq(id));
            dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourse(DeleteDataCourse.getTableName(COURSE_CHAPTER),id,""));
            List<String> idList = chapterSectionDao.execute(dsl -> dsl.select(COURSE_CHAPTER_SECTION.ID).from(COURSE_CHAPTER_SECTION).where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(id)).fetch(COURSE_CHAPTER_SECTION.ID));
            chapterSectionDao.delete(idList);
            dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(DeleteDataCourse.getTableName(COURSE_CHAPTER_SECTION),idList,""));
            attachmentDao.delete(COURSE_ATTACHMENT.COURSE_ID.eq(id));
            // 删除配置的证书记录
            certificateRecordService.delete(id);
            //businessTopicService.delete(id, BusinessTopic.BUSINESS_TYPE_SUBJECT);
            // updated by wangdongyan 2018-06-20
            for (String sectionId : sectionIds) {
            	courseCacheService.clearChapterSection(sectionId);
			}
            for (String versionId : versionIds) {
            	courseCacheService.clearCourseChapter(id, versionId);
			}
        } else {
            course.setDeleteFlag(CourseInfo.DELETE_FLAG_YES);
            course.setModifyDate(null);
            dao.update(course);
            result = 1;
        }

        businessTopicService.delete(id, BusinessTopic.BUSINESS_TYPE_SUBJECT);
        messageSender.send(MessageTypeContent.COURSE_INFO_DELETE, MessageHeaderContent.ID, id);
        //通知相关专题创建人
        messageSender.send(MessageTypeContent.COURSE_DISAPPEAR,MessageHeaderContent.ID,id,MessageHeaderContent.NAME,currentUserId);
        multidimensionalScoringService.deleteScore(id);
        return result;
    }

    @Override
    public List<CourseInfo> findBasicByIds(List<String> ids) {
        if (ids == null || ids.size() == 0) {
            return new ArrayList<>();
        }
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        return dao.execute(d -> d.select(Fields.start()
                .add(COURSE_INFO)
                .add(organizationTable)
                .end())
                .from(COURSE_INFO)
                .leftJoin(organizationTable).on(COURSE_INFO.ORGANIZATION_ID.eq(organizationTable.ID))
                .where(COURSE_INFO.ID.in(ids)).fetch(r -> {
                    CourseInfo subject = r.into(COURSE_INFO).into(CourseInfo.class);
                    Organization org = r.into(organizationTable).into(Organization.class);
                    subject.setOrganization(org);
                    return subject;
                })
        );
    }

    /**
     * 保存专题相册
     * @param subjectId
     * @param photos
     * @return
     */
    private List<CoursePhoto> savePhotos(String subjectId, List<CoursePhoto> photos) {
        photoDao.delete(COURSE_PHOTO.SUBJECT_ID.eq(subjectId));
        return photos.stream().map(photo -> {
            photo.forInsert();
            photo.setSubjectId(subjectId);
            return photoDao.insert(photo);
        }).collect(Collectors.toList());
    }

    /**
     * 保存专题文字区域
     *
     * @param subjectId
     * @param textAreas
     */
    private void saveTextArea(String subjectId, List<SubjectTextArea> textAreas) {
        textAreaDao.delete(Tables.SUBJECT_TEXT_AREA.SUBJECT_ID.eq(subjectId));
        textAreas.stream().forEach(obj -> {
            obj.forInsert();
            obj.setSubjectId(subjectId);
            textAreaDao.insert(obj);
        });
    }

    /**
     * 同步知识库
     * @param courseAttachments
     * @param course
     */
    private void syncKnowledge(List<CourseAttachment> courseAttachments, CourseInfo course) {
        if(courseAttachments != null && courseAttachments.size()>0) {
            messageSender.send(MessageTypeContent.KNOWLEDGE_INFO_SYNC, MessageHeaderContent.ID, course.getId(), MessageHeaderContent.BUSINESS_TYPE,  KnowledgeInfo.BUSINESS_TYPE_SUBJECT + "");
        }
    }

    /**
     * 给参考Id赋值
     * @param courseChapters
     */
    @Override
    public void addReferenceId(List<CourseChapter> courseChapters, int flag) {
        courseChapters.forEach(courseChapter -> {
            if (courseChapter.getCourseChapterSections() != null) {
                courseChapter.getCourseChapterSections().stream().forEach(x -> {
                    if (UPDATE == flag && x.getSectionType() == CourseChapterSection.SECTION_TYPE_COURSE) {
                        checkSectionCourse(x);
                    }

                    if (StringUtils.isEmpty(x.getReferenceId())) {
                        x.setReferenceId(x.getId());
                        x.setModifyDate(null);
                        chapterSectionDao.update(x);
                    }
                });
            }
        });
    }

    /**
     * 上架操作
     *
     * @param courseId
     * @param courseShelves
     */
    private void shelves(String courseId, CourseShelves courseShelves,Integer publishType) {
        courseShelves.setCourseId(courseId);
        courseShelves.setStatus(CourseShelves.STATUS_OK);
        courseShelves.forInsert();
        shelvesDao.insert(courseShelves);
        messageSender.send(MessageTypeContent.COURSE_SHELVES,
            MessageHeaderContent.ID, courseShelves.getId(),
            MessageHeaderContent.BUSINESS_ID, courseId,
            MessageHeaderContent.COURSE_PUBLISH_TYPE, String.valueOf(publishType));
    }


    /**
     * 生成课程编码 日期(例：170220)+部门编码(例：888)+三位自动增长序列号(001) 例:170220888001
     *
     * @param course
     * @return
     */

    private synchronized void updateCourseCode(CourseInfo course) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        String prelude = dateFormat.format(new Date()); // 初始化编码前缀
        Organization organization = organizationDao.get(course.getOrganizationId());
        String orgCode = organization.getCode() == null ? "000" : organization.getCode(); // 组织编码

        Optional<CourseSequence> csOptional = sequenceDao.fetchOne(COURSE_SEQUENCE.ORGANIZATION_ID
                .eq(course.getOrganizationId()).and(COURSE_SEQUENCE.BUSINESS_TYPE.eq(course.getBusinessType()))
                .and(COURSE_SEQUENCE.PRELUDE.eq(prelude)));

        int newSeq = 1;
        // 无则新增，有则更新，每天同一业务类型同一部门下只生成一条序列记录
        if (csOptional.isPresent()) {
            CourseSequence cs = csOptional.get();
            newSeq = cs.getSequence() + 1;
            cs.setSequence(newSeq);
            sequenceDao.update(cs);
        } else {
            // 初始化课程编码序列信息
            CourseSequence courseSequence = new CourseSequence();
            courseSequence.setSequence(1);
            courseSequence.setBusinessType(course.getBusinessType());
            courseSequence.setOrganizationId(course.getOrganizationId());
            courseSequence.setPrelude(prelude);
            courseSequence.forInsert();
            sequenceDao.insert(courseSequence);
        }
        String code = prelude + orgCode + String.format("%03d", newSeq);
        dao.execute(dao -> dao.update(COURSE_INFO).set(COURSE_INFO.CODE, code)
                .where(COURSE_INFO.ID.eq(course.getId())).execute());
    }

    private int saveCourseChapters(String courseId, List<CourseChapter> courseChapters,String memberId) {
        // 判断是否为重塑的url课程 是的话发消息 更新已经存在的并且完成的同名的课程
        boolean isRemodeling = trainService.findRemodelingRoleBySubjectId(courseId);

        courseChapters.forEach(courseChapter -> {
            chapterDao.delete(COURSE_CHAPTER.ID.eq(courseChapter.getId()).and(COURSE_CHAPTER.VERSION_ID.isNull()));
            dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourse(DeleteDataCourse.getTableName(COURSE_CHAPTER), courseChapter.getId(),""));
            String oldId = courseChapter.getId();
            courseChapter.forInsert();
            courseChapter.setCourseId(courseId);
            courseChapter.setVersionId(null);
            courseChapter.setModifyDate(null);
            chapterDao.insert(courseChapter);
            notificationScoreSheet(courseId,courseChapter.getScoringId(),memberId,Optional.ofNullable(oldId),Optional.ofNullable(courseChapter.getId()));
            List<CourseChapterSection> courseChapterSections = courseChapter.getCourseChapterSections();
            if (courseChapterSections != null) {
                courseChapterSectionCommonDao.insert(courseChapterSections.stream().map(x -> {
                    x.forInsert();
                    x.setChapterId(courseChapter.getId());
                    x.setCourseId(courseId);
                    x.setModifyTime(null);
                    x.setModifyDate(null);
                    if (x.getSectionType() !=null) {
                        if (x.getSectionType() == CourseChapterSection.SECTION_TYPE_TASK) {
                            saveStudyTask(x);
                        }
                        if (isRemodeling &&  CourseChapterSection.SECTION_TYPE_URL == x.getSectionType() && Optional.ofNullable(x.getAddType()).orElse(false)){
                            handleNewChapterProgressWithTheSameName(courseId,x.getName(),x.getId());
                        }
                    }
                    return x;
                }).collect(Collectors.toList()));

                List<String> referenceIds = courseChapterSections.parallelStream()
                        .filter(e -> !StringUtils.isBlank(e.getReferenceId()))
                        .map(CourseChapterSection::getReferenceId).collect(Collectors.toList());

                List<String> questionnaireReferenceIds = courseChapterSections.parallelStream()
                        .filter(e -> CourseChapterSection.QUESTIONNAIRE_FLAG_YES.equals(e.getQuestionnaireFlag())
                                && (CourseChapterSection.SECTION_TYPE_COURSE == e.getSectionType() || CourseChapterSection.SECTION_TYPE_URL == e.getSectionType())
                                && !StringUtils.isBlank(e.getReferenceId()))
                        .map(CourseChapterSection::getReferenceId).collect(Collectors.toList());

                List<String> questionnaireSectionIds = courseChapterSections.parallelStream()
                        .filter(e -> CourseChapterSection.QUESTIONNAIRE_FLAG_YES.equals(e.getQuestionnaireFlag())
                                && (CourseChapterSection.SECTION_TYPE_COURSE == e.getSectionType() || CourseChapterSection.SECTION_TYPE_URL == e.getSectionType())
                                && StringUtils.isBlank(e.getReferenceId()))
                        .map(CourseChapterSection::getId).collect(Collectors.toList());

                if (!referenceIds.isEmpty()) {
                    courseChapterQuestionnaireCommonDao.execute(x -> x.update(COURSE_CHAPTER_QUESTIONNAIRE).set(COURSE_CHAPTER_QUESTIONNAIRE.STATUS, CourseQuestionnaireRecord.STATUS_0)
                            .where(COURSE_CHAPTER_QUESTIONNAIRE.REFERENCE_ID.in(referenceIds))).execute();
                    if (!questionnaireReferenceIds.isEmpty()) {
                        courseChapterQuestionnaireCommonDao.execute(x -> x.update(COURSE_CHAPTER_QUESTIONNAIRE).set(COURSE_CHAPTER_QUESTIONNAIRE.STATUS, CourseQuestionnaireRecord.STATUS_1)
                                .where(COURSE_CHAPTER_QUESTIONNAIRE.REFERENCE_ID.in(questionnaireReferenceIds))).execute();
                        List<CourseChapterQuestionnaire> courseChapterQuestionnaires = courseChapterQuestionnaireCommonDao.fetch(COURSE_CHAPTER_QUESTIONNAIRE.REFERENCE_ID.in(questionnaireReferenceIds));
                        List<String> updatedReferenceIds = courseChapterQuestionnaires.parallelStream().map(CourseChapterQuestionnaire::getReferenceId).collect(Collectors.toList());
                        if (!updatedReferenceIds.isEmpty()) {
                            questionnaireReferenceIds.removeAll(updatedReferenceIds);
                        }
                    }
                }

                if (!questionnaireSectionIds.isEmpty() || !questionnaireReferenceIds.isEmpty()) {
                    questionnaireSectionIds.addAll(questionnaireReferenceIds);
                    //新增调查问卷逻辑
                    courseChapterQuestionnaireCommonDao.insert(questionnaireSectionIds.parallelStream()
                            .map(s -> {
                                CourseChapterQuestionnaire courseChapterQuestionnaire = new CourseChapterQuestionnaire();
                                courseChapterQuestionnaire.forInsert();
                                courseChapterQuestionnaire.setReferenceId(s);
                                courseChapterQuestionnaire.setType(CourseChapterQuestionnaire.COURSE_TYPE_ONLINE);
                                courseChapterQuestionnaire.setMouldId(String.valueOf(QuestionnaireMould.MOULD_TYPE_ONLINE));
                                return courseChapterQuestionnaire;
                            }).collect(Collectors.toList()));
                }
            }
        });
        return courseChapters.size();
    }

    /**
     * 统计专题热门标签
     */
    @Override
    public List<String> hotTopicIds() {
        return dao.execute(x->
                x.select(BUSINESS_TOPIC.TOPIC_ID).from(BUSINESS_TOPIC)
                        .leftJoin(TOPIC).on(BUSINESS_TOPIC.TOPIC_ID.eq(TOPIC.ID))
                        .where(BUSINESS_TOPIC.BUSINESS_TYPE.eq(BusinessTopic.BUSINESS_TYPE_SUBJECT))
                        .and(TOPIC.GROUP.eq(Topic.GROUP_STANDARD))
                        .groupBy(BUSINESS_TOPIC.TOPIC_ID)
                        .orderBy(BUSINESS_TOPIC.TOPIC_ID.count().desc())
                        .limit(8).fetch(BUSINESS_TOPIC.TOPIC_ID)
        );
    }

    /**
     * 专题章节保存作业
     * @param x
     */
    private void saveStudyTask(CourseChapterSection x) {
        if (x.getStudyTask() != null) {
            StudyTask task = x.getStudyTask();
            if (StringUtils.isEmpty(task.getId())) {
                task = studyTaskService.insert(x.getName(), task.getDescription(), task.getAuditType(),
                        Optional.ofNullable(task.getAuditMemberIds()),
                        Optional.ofNullable(task.getAttachments()));
                x.setResourceId(task.getId());
            } else {
                studyTaskService.update(task.getId(), x.getName(), task.getDescription(),
                        task.getAuditType(), Optional.ofNullable(task.getAuditMemberIds()),
                        Optional.ofNullable(task.getAttachments()));
            }
        } else {
            studyTaskService.updateName(x.getResourceId(), x.getName());
        }
    }

    /**
     * 检查专题节资源为课程的之前是否删除过?
     * 删除后再新增则引用之前版本的referenceId
     * @param section
     */
    private void checkSectionCourse(CourseChapterSection section) {
        String newReferenceId = section.getReferenceId() == null ? "" : section.getReferenceId();

        // 根据courseId,resourceId查询该专题下同一资源之前已存在的最早节信息
        Optional<CourseChapterSection> oldSection = courseChapterSectionCommonDao.execute(d ->
                d.select(COURSE_CHAPTER_SECTION.fields())
                .from(COURSE_CHAPTER_SECTION)
                .where(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(section.getResourceId())
                .and(COURSE_CHAPTER_SECTION.COURSE_ID.eq(section.getCourseId())))
                .orderBy(COURSE_CHAPTER_SECTION.CREATE_TIME.asc())
                .limit(1)
                .fetchOptionalInto(CourseChapterSection.class));

        oldSection.ifPresent(s -> {
            // 如果referenceId为空或者和以前的不一致，表示同一课程在专题中被删除过;
            if (!newReferenceId.equals(s.getReferenceId())) {
                section.setReferenceId(s.getReferenceId());
                section.setModifyDate(null);
                chapterSectionDao.update(section);
            }
        });
    }

    @DataSource(type= DataSourceEnum.SLAVE)
	@Override
	public PagedResult<CourseInfo> findSelect(Integer page, Integer pageSize, Optional<Integer> client,
                                              Optional<String> name, Optional<Integer> subjectType, Optional<Integer> isParty, List<String> grantOrganizationIds,
                                              Optional<List<String>> parentOrganizationIds, Optional<String[]> selectIds, Optional<Long> shelveBeginTime, Optional<Long> shelveEndTime, Optional<List<Integer>> courseStatus) {
		List<Condition> where = new ArrayList<>();


        where.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
        where.add(COURSE_INFO.BUSINESS_TYPE.eq(2));
        where.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));//选择器只查询已发布的课程
        where.add(selectIds.map(COURSE_INFO.ID::notIn).orElse(DSL.trueCondition()));
        subjectType.map(t -> where.add(t == 1 ? COURSE_INFO.URL.isNull() : COURSE_INFO.URL.isNotNull()));
        name.map(x->where.add(COURSE_INFO.NAME.contains(x)));
        where.add(courseStatus.map(COURSE_INFO.STATUS::in).orElse(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES)));
        shelveBeginTime.map(x -> where.add(COURSE_INFO.SHELVE_TIME.ge(x)));
        shelveEndTime.map(x -> where.add(COURSE_INFO.SHELVE_TIME.le(x)));
        //client.map(x -> where.add(COURSE_INFO.PUBLISH_CLIENT.eq(x)));
        assemblePartyClient(client, isParty, where);
        return dao.execute(x -> {
            SelectOrderByStep<Record> select = x
                    .selectDistinct(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
                            .add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
                    .from(COURSE_INFO)
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                    .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                    .where(where).and(COURSE_INFO.ORGANIZATION_ID.in(grantOrganizationIds));
            if (parentOrganizationIds.isPresent()) {

            	select = select.union(
            			x.select(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
            					.add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
            			.from(COURSE_INFO)
            			.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
            			.leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
            			.where(where)
            			.and(COURSE_INFO.SHARE_SUB.eq(1).and(COURSE_INFO.ORGANIZATION_ID.in(parentOrganizationIds.get()))
            					));
            }

            int count = x.fetchCount(select);
            Result<Record> record = select.orderBy(COURSE_INFO.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);

            List<Organization> organizationList = record.map(r -> {
                Organization org = new Organization();
                org.setName(r.getValue("oname", String.class));
                return org;
            });
            List<CourseCategory> courseCategoryList = record.map(r -> {
                CourseCategory cc = new CourseCategory();
                cc.setId(r.getValue("cid", String.class));
                cc.setName(r.getValue("cname", String.class));
                return cc;
            });

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
            });

            return PagedResult.create(count, courseInfoList);
        });
	}

	private void updateKnowledgeAsyncNum(String courseId, List<String> ids, int businessType) {
		if (businessType == INSERT) {
			messageSender.send(MessageTypeContent.KNOWLEDGE_INFO_SYNC,
					MessageHeaderContent.ID, courseId,
					MessageHeaderContent.BUSINESS_TYPE, 15+"");
		} else {
			messageSender.send(MessageTypeContent.KNOWLEDGE_INFO_SYNC_DELETE,
					MessageHeaderContent.ID, courseId,
					MessageHeaderContent.BUSINESS_TYPE, 15+"",
					MessageHeaderContent.IDS, String.join(",", ids));
		}
	}

	private void deleteAttachmenct(String courseId) {
		List<String> knowledgeIds = attachmentDao.fetch(COURSE_ATTACHMENT.COURSE_ID.eq(courseId),COURSE_ATTACHMENT.SOURCE.eq(CourseAttachment.ATTACHMENT_SOURCE_KNOWLEDGE)).stream().map(CourseAttachment::getKnowledgeId).collect(Collectors.toList());
		if (knowledgeIds != null && !knowledgeIds.isEmpty()) {
			messageSender.send(MessageTypeContent.KNOWLEDGE_INFO_SYNC_DELETE,
					MessageHeaderContent.ID, courseId,
					MessageHeaderContent.BUSINESS_TYPE, 2+"",
					MessageHeaderContent.IDS, String.join(",", knowledgeIds));
		}
		attachmentDao.delete(COURSE_ATTACHMENT.COURSE_ID.eq(courseId));
	}


	@Override
    public PagedResult<CourseInfo> findSelectorSubject(Integer page, Integer pageSize, Optional<Integer> client,
                                                       Optional<String> name, Optional<Integer> subjectType,
                                                       Optional<String[]> selectIds, Optional<Long> shelveBeginTime, Optional<Long> shelveEndTime, Optional<String> organizationId, Optional<List<Integer>> courseStatus) {
        List<Condition> where = new ArrayList<>();
        String path = null;
        if (organizationId.isPresent()) {
            path = dao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).where(ORGANIZATION.ID.eq(organizationId.get()))).fetchOne(ORGANIZATION.PATH);
        }
        Optional<String> pathOptioanl = Optional.ofNullable(path);

        where.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
        where.add(COURSE_INFO.BUSINESS_TYPE.eq(2));
        //todo 选择器查询已发布和测试中的课程
        where.add(courseStatus.map(COURSE_INFO.STATUS::in).orElse(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES)));
//        where.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));//选择器只查询已发布的课程
        where.add(selectIds.map(COURSE_INFO.ID::notIn).orElse(DSL.trueCondition()));
        where.add(COURSE_INFO.URL.isNull());
        subjectType.map(t -> where.add(t == 1 ? COURSE_INFO.URL.isNull() : COURSE_INFO.URL.isNotNull()));
        name.map(x->where.add(COURSE_INFO.NAME.contains(x)));
        client.map(x -> where.add(COURSE_INFO.PUBLISH_CLIENT.eq(x)));
        pathOptioanl.map(x -> where.add(ORGANIZATION.PATH.like(x + "%")));
        shelveBeginTime.map(x -> where.add(COURSE_INFO.SHELVE_TIME.ge(x)));
        shelveEndTime.map(x -> where.add(COURSE_INFO.SHELVE_TIME.le(x)));
        return dao.execute(x -> {
            SelectOrderByStep<Record> select = x
                    .selectDistinct(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
                            .end())
                    .from(COURSE_INFO)
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                    .where(where);

            SelectSelectStep<Record> selectCount = x.select(Fields.start().add(COURSE_INFO.ID.count()).end());
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> selectFields = a.from(COURSE_INFO)
                        .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .where(where);
                return selectFields;
            };

            int count = stepFunc.apply(selectCount).fetchOne().getValue(0, Integer.class);
            Result<Record> record = select.orderBy(COURSE_INFO.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);

            List<Organization> organizationList = record.map(r -> {
                Organization org = new Organization();
                org.setName(r.getValue("oname", String.class));
                return org;
            });


            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
            });

            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public List<String> findReferencedSubjectIds(Optional<String> subjectId){
        List<Condition> param = Stream.of(
                subjectId.map(s -> COURSE_INFO.ID.ne(s)))
                .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        subjectId.map(s -> COURSE_INFO.ID.ne(s));
//        HashSet<String> set = new HashSet<String>();
        Set<String> set = dao.execute(x -> {
            return x.select(Fields.start()
                    .add(COURSE_CHAPTER_SECTION.COURSE_ID)
                    .end()).from(COURSE_INFO)
                    .leftJoin(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID).or(COURSE_CHAPTER.VERSION_ID.isNull())))
                    .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                    .where(param).and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_SUBJECT))
                    .fetch(r -> r.into(CourseChapterSection.class));
        }).stream().filter(s -> !StringUtils.isEmpty(s.getCourseId())).map(s -> s.getCourseId()).collect(Collectors.toSet());
//        if (sectionList != null && !sectionList.isEmpty()) {
//            sectionList.forEach(r -> {
//                set.add(r.getResourceId());
//                set.add(r.getCourseId());
//            });
//        }
        return set != null && !set.isEmpty() ? set.stream().filter(r -> !StringUtils.isEmpty(r)).collect(Collectors.toList()) : null;
    }


    @Override
    public List<CourseInfo> findBySubjectIds(List<String> ids) {
        SelectHavingStep<Record> step = dao.execute(e -> e.select(Fields.start()
                .add(COURSE_INFO).end())
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.in(ids)))
                .groupBy(COURSE_INFO.ID);
        return step.fetch().map(r -> {
            CourseInfo courseInfo = r.into(COURSE_INFO).into(CourseInfo.class);
            return courseInfo;
        });
    }

    @Override
    public String findSubjectIsReferenced(String subjectId) {
        return dao.execute(x -> x.select(COURSE_INFO.ID).from(COURSE_INFO)
                .leftJoin(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID).or(COURSE_CHAPTER.VERSION_ID.isNull())))
                .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                .where(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(subjectId))
                .limit(1)
                .fetchOne(COURSE_INFO.ID)
        );
    }

    @Override
    public Optional<String> findResourceIdsBySubjectIds(List<String> subjectIds) {
        return dao.execute(x -> {
            return x.select(Fields.start()
                    .add(COURSE_CHAPTER_SECTION.COURSE_ID)
                    .end()).from(COURSE_INFO)
                    .leftJoin(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID).or(COURSE_CHAPTER.VERSION_ID.isNull())))
                    .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                    .where(COURSE_INFO.ID.in(subjectIds)).and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_SUBJECT))
                    .limit(1)
                    .fetchOptional(COURSE_CHAPTER_SECTION.COURSE_ID);
        });
    }

    @Override
    public CourseInfo findSubjectDetailByCode(String code, Optional<Long> sectionStartTime, Optional<Long> sectionEndTime) {
        // 获取专题信息
        CourseInfo courseInfo = dao.fetchOne(COURSE_INFO.CODE.eq(code))
                .orElseThrow(()->new UnprocessableException(ErrorCode.CourseInfoNotInShelvesOrIsNull));
        // 获取专题主题
        List<CourseChapter> chapters = chapterDao.fetch(COURSE_CHAPTER.COURSE_ID.eq(courseInfo.getId()),
                COURSE_CHAPTER.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_CHAPTER.DELETE_FLAG.isNull()),
                COURSE_CHAPTER.VERSION_ID.eq(courseInfo.getVersionId()));
        List<String> chapterIds = chapters.stream().map(CourseChapterEntity::getId).collect(Collectors.toList());
        // 获取专题章节
        Map<String,List<CourseChapterSection>> sectionMap = chapterSectionDao.fetch(COURSE_CHAPTER_SECTION.CHAPTER_ID.in(chapterIds),
                COURSE_CHAPTER_SECTION.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_CHAPTER_SECTION.DELETE_FLAG.isNull()),
                sectionStartTime.map(COURSE_CHAPTER_SECTION.CREATE_TIME::ge).orElse(DSL.trueCondition()),
                sectionEndTime.map(COURSE_CHAPTER_SECTION.CREATE_TIME::le).orElse(DSL.trueCondition()))
                .stream().collect(Collectors.groupingBy(CourseChapterSection::getChapterId));
        //查询专题章节包含有哪些类型
        List<Integer> typeList = new ArrayList<>();
        //专题章节的resourceIdList
        List<String> resourceIdList = new ArrayList<>();
        for (Map.Entry<String, List<CourseChapterSection>> m : sectionMap.entrySet()) {
            if (!ObjectUtils.isEmpty(m.getValue())) {
                typeList.addAll(m.getValue().stream().map(r -> r.getSectionType()).collect(Collectors.toList()));
                resourceIdList.addAll(m.getValue().stream().map(r -> r.getResourceId()).collect(Collectors.toList()));
            }
        }
        //课程map
        Map<String, CourseInfo> courseInfoMap = getCourseInfoMap(typeList, resourceIdList);
        //知识map
        Map<String, KnowledgeInfo> knowledgeInfoMap = getKnowledgeInfoMap(typeList, resourceIdList);
        //直播map
        Map<String, GenseeWebCast> genseeMap = getGenseeMap(typeList, resourceIdList);
        chapters.forEach(chapter->{
            List<CourseChapterSection> courseChapterSections = sectionMap.get(chapter.getId());
            if(!ObjectUtils.isEmpty(courseChapterSections)){
                courseChapterSections.forEach(courseChapterSection -> {
                    CourseInfo courseInfoFromMap = courseInfoMap.get(courseChapterSection.getResourceId());
                    KnowledgeInfo knowledgeInfoFromMap = knowledgeInfoMap.get(courseChapterSection.getResourceId());
                    GenseeWebCast genseeWebCastFromMap = genseeMap.get(courseChapterSection.getResourceId());
                    //类型是课程取课程表的coverPath和url
                    if(CourseChapterSection.SECTION_TYPE_COURSE == courseChapterSection.getSectionType() && courseInfoFromMap !=null) {
                        String url = courseInfoFromMap.getUrl();
                        if (StringUtils.isEmpty(url)) {
                            url = COURSE_PREFIX + courseChapterSection.getResourceId();
                        }
                        courseChapterSection.setUrl(url);
                        courseChapterSection.setCoverPath(StringUtils.isEmpty(courseInfoFromMap.getCoverPath())?
                                DEFAULT_IMG_URL : courseInfoFromMap.getCoverPath());
                    }
                    //类型是专题取课程表的coverPath和url
                    if (CourseChapterSection.SECTION_TYPE_SUBJECT.equals(courseChapterSection.getSectionType()) && courseInfoFromMap != null) {
                        String url = courseInfoFromMap.getUrl();
                        if(StringUtils.isEmpty(url)){
                            url = SUBJECT_PREFIX + courseChapterSection.getResourceId();
                        }
                        courseChapterSection.setUrl(url);
                        courseChapterSection.setCoverPath(StringUtils.isEmpty(courseInfoFromMap.getCoverPath())?
                                DEFAULT_IMG_URL : courseInfoFromMap.getCoverPath());
                    }
                    //类型是知识取知识表的coverPath和url
                    if(CourseChapterSection.SECTION_TYPE_KNOWLEDGE.equals(courseChapterSection.getSectionType()) &&
                            knowledgeInfoFromMap != null) {
                        String url = KNOWLEDGE_PREFIX + courseChapterSection.getResourceId();
                        courseChapterSection.setUrl(url);
                        courseChapterSection.setCoverPath(StringUtils.isEmpty(knowledgeInfoFromMap.getCover())?
                                DEFAULT_IMG_URL : knowledgeInfoFromMap.getCover());
                    }
                    //类型是直播取直播表的coverPath和url
                    if(CourseChapterSection.SECTION_TYPE_GENSEE == courseChapterSection.getSectionType() &&
                            genseeWebCastFromMap != null) {
                        String url = GENSEE_PREFIX + courseChapterSection.getResourceId();
                        courseChapterSection.setUrl(url);
                        courseChapterSection.setCoverPath(StringUtils.isEmpty(genseeWebCastFromMap.getCoverPath())?
                                DEFAULT_IMG_URL : genseeWebCastFromMap.getCoverPath());
                    }
                });
            }
            chapter.setCourseChapterSections(courseChapterSections);
        });
        courseInfo.setCourseChapters(chapters);
        return courseInfo;
    }

    @Override
    public String findMemberRole(List<String> courseIds, String currentUserId) {
        if (CollectionUtils.isEmpty(courseIds)) {
            return "";
        }
        for(int i=0; i<courseIds.size()-1; i++) {
            String id = courseIds.get(i);
            Integer count = dao.execute(e -> {
                return e.select(
                        Fields.start()
                                .add(AUDIENCE_OBJECT.ID.count())
                                .end())
                        .from(AUDIENCE_OBJECT)
                        .leftJoin(AUDIENCE_MEMBER).on(AUDIENCE_OBJECT.ITEM_ID.eq(AUDIENCE_MEMBER.ITEM_ID))
                        .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(id), AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                        .fetchOne().getValue(0, Integer.class);
            });
            if (count > 0) {
                return id;
            }
        }
        return courseIds.get(courseIds.size()-1);
    }

    /**
     *  若类型是课程,获取课程map
     * @param typeList  类型列表
     * @param resourceIdList  资源ids
     * @return
     */
    private Map<String, CourseInfo> getCourseInfoMap(List<Integer> typeList, List<String> resourceIdList) {
        Map<String, CourseInfo> courseInfoMap = new HashMap<>();
        if (typeList.contains(CourseChapterSection.SECTION_TYPE_COURSE)) {
            courseInfoMap = dao.fetch(COURSE_INFO.ID.in(resourceIdList),
                    COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()))
                    .stream().collect(Collectors.toMap(CourseInfo::getId, x -> x, (o, v) -> o));
        }
        return courseInfoMap;
    }

    /**
     *  若类型是知识,获取知识map
     * @param typeList  类型列表
     * @param resourceIdList  资源ids
     * @return
     */
    private Map<String, KnowledgeInfo> getKnowledgeInfoMap(List<Integer> typeList, List<String> resourceIdList) {
        Map<String,KnowledgeInfo> knowledgeInfoMap = new HashMap<>();
        if(typeList.contains(CourseChapterSection.SECTION_TYPE_KNOWLEDGE)){
            knowledgeInfoMap = knowledgeInfoCommonDao.fetch(KNOWLEDGE_INFO.ID.in(resourceIdList),
                    KNOWLEDGE_INFO.IS_DEL.eq(KnowledgeInfo.IS_DEL_NO).or(KNOWLEDGE_INFO.IS_DEL.isNull()))
                    .stream().collect(Collectors.toMap(KnowledgeInfo::getId, x -> x, (o, v) -> o));
        }
        return knowledgeInfoMap;
    }

    /**
     *  若类型是直播,获取直播map
     * @param typeList  类型列表
     * @param resourceIdList  资源ids
     * @return
     */
    private Map<String, GenseeWebCast> getGenseeMap(List<Integer> typeList, List<String> resourceIdList) {
        Map<String,GenseeWebCast> genseeMap = new HashMap<>();
        if(typeList.contains(CourseChapterSection.SECTION_TYPE_GENSEE)){
            genseeMap = genseeWebCastDao.fetch(GENSEE_WEB_CAST.ID.in(resourceIdList))
                    .stream().collect(Collectors.toMap(GenseeWebCast::getId, x -> x, (o, v) -> o));
        }
        return genseeMap;
    }

    public void notificationScoreSheet(String subjectId, String scoringId, String currentUserId, Optional<String> oldTopicId, Optional<String> newTopicId) {
        if (Objects.nonNull(scoringId) && Objects.nonNull(subjectId)) {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("subjectId", subjectId);
            jsonObject.put("scoringId", scoringId);
            jsonObject.put("currentUserId", currentUserId);
            jsonObject.put("oldTopicId", oldTopicId.orElse(null));
            jsonObject.put("newTopicId", newTopicId.orElse(null));
            messageSender.send(MessageTypeContent.SCORING_SHEET, MessageHeaderContent.CONTENT, jsonObject.toJSONString());

        }
    }

    /**
     * 当重塑的专题完成后 新增了 和已完成的章节名称相同的章节 用来更新章节进度
     * 并不会同步歌更新专题信息了
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleNewChapterProgressWithTheSameName(String courseId, String sectionName, String newSectionId) {
        Optional.ofNullable(sectionName).ifPresent(name ->{
            TableImpl<?> sectionTableName = tableConfigService.getTableName(courseId, ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS, ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
            List<CourseSectionStudyProgress> courseSectionStudyProgresses = chapterSectionDao.execute(r -> r.select(sectionTableName.fields())
                    .from(COURSE_CHAPTER_SECTION)
                    .join(sectionTableName).on(COURSE_CHAPTER_SECTION.ID.eq(sectionTableName.field("f_section_id", String.class)))
                    .where(sectionTableName.field("f_course_id", String.class).eq(courseId)
                            .and(sectionTableName.field("f_finish_status", Integer.class).in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS))
                            .and(COURSE_CHAPTER_SECTION.NAME.eq(name))).groupBy(sectionTableName.field("f_member_id",String.class))
                    .fetch(s -> new CourseSectionStudyProgress().fill(sectionTableName, s)));
            List<CourseSectionStudyProgress> courses = courseSectionStudyProgresses.stream().map(sec -> {
                long now = System.currentTimeMillis();
                int zero = 0, passTheExam = 1, completeSchedule = 100;
                CourseSectionStudyProgress courseSectionStudyProgress = new CourseSectionStudyProgress();
                courseSectionStudyProgress.forInsert();
                courseSectionStudyProgress.setMemberId(sec.getMemberId());
                courseSectionStudyProgress.setCourseId(sec.getCourseId());
                courseSectionStudyProgress.setSectionId(newSectionId);
                courseSectionStudyProgress.setBeginTime(now);
                courseSectionStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
                courseSectionStudyProgress.setFinishTime(now);
                courseSectionStudyProgress.setCompletedRate(completeSchedule);
                courseSectionStudyProgress.setStudyTotalTime(zero);
                courseSectionStudyProgress.setLastAccessTime(now);
                courseSectionStudyProgress.setExamStatus(passTheExam);
                courseSectionStudyProgress.setCommitTime(now);
                courseSectionStudyProgress.setScore(zero);
                return courseSectionStudyProgress;
            }).collect(Collectors.toList());
            chapterSectionDao.execute(dsl -> CourseSectionStudyProgress.batchInsert(sectionTableName, dsl,courses));
        });

    }



    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public PagedResult<CourseInfo> findSubjectVirtualSpace(
            Integer page,
            Integer pageSize,
            String currentUserId,
            Optional<String> name,
            Optional<String> organizationId,
            Optional<Integer> status,
            Optional<Long> shelveBeginDate,
            Optional<Long> shelveEndDate,
            List<String> grantOrganizationIds,
            String virtualSpacesId,
            String virtualSpacesOrganizationId,
            List<String> virtualSpacesGrantOrganizationIds,
            List<String> spacesStatus,
            List<String> spacesStatusAddTo,
            Optional<Integer> virtualSpacesStatus,
            Optional<Integer> subjectType) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门

        return dao.execute(x -> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(
                                    COURSE_INFO.ID,
                                    COURSE_INFO.NAME,
                                    COURSE_INFO.CODE,
                                    COURSE_INFO.PUBLISH_CLIENT,
                                    COURSE_INFO.STATUS,
                                    COURSE_INFO.URL,
                                    COURSE_INFO.SHELVE_TIME,
                                    COURSE_INFO.STYLES,
                                    COURSE_INFO.COVER,
                                    COURSE_INFO.COVER_PATH)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .end()); // 查询list

            SelectSelectStep<Record> selectCountField = x.select(Fields.start()
                    .add(COURSE_INFO.ID.countDistinct()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                            name.map(n -> {
                                String str = n.replace(" ", "");
                                return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                            }),
                            status.map(COURSE_INFO.STATUS::eq),
                            shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                            shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le)
                    )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());


            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
            param.add(COURSE_INFO.IS_PARTY.eq(CourseInfo.PARTY_BUILING_COURSE_FLASE).or(COURSE_INFO.IS_PARTY.isNull()));

            if (subjectType.isPresent() && Objects.equals(subjectType.get(),CourseInfo.PERSONALIZATION)){
                param.add(COURSE_INFO.URL.isNotNull());
            }else if (subjectType.isPresent() && Objects.equals(subjectType.get(),CourseInfo.GENERAL)){
                param.add(COURSE_INFO.URL.isNull());
            }

            //查询组织
            if(organizationId.isPresent()){
                //列表归属列表条件
                param.add(COURSE_INFO.ORGANIZATION_ID.in(grantOrganizationIds));
            }else {
                //默认查询当前归属空间
                param.add(COURSE_INFO.ORGANIZATION_ID.in(virtualSpacesGrantOrganizationIds));
            }


            if (!ObjectUtils.isEmpty(spacesStatus)) {
                //排除当前空间资源禁用的 and
                param.add(COURSE_INFO.ID.notIn(spacesStatus));
            }

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .where(param).or(COURSE_INFO.ID.in(spacesStatusAddTo));
                return select;
            };


            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public PagedResult<CourseInfo> findSuperiorSubject(List<String> byVirtualSpacesId,
                                                       Integer page, Integer pageSize,
                                                       Optional<String> name,
                                                       Optional<String> categoryId,
                                                       Optional<Long> shelveBeginDate,
                                                       Optional<Long> shelveEndDate,
                                                       Optional<Integer> allCategory,
                                                       Integer businessType,
                                                       String virtualSpacesId,
                                                       Optional<Integer> status,
                                                       String virtualSpacesOrganizationId,
                                                       Optional<Integer> subjectType) {

        //查询当前空间归属的上级部门分享的课程id
        List<String> subjectIds = findSuperiorSubjectIds(virtualSpacesOrganizationId,byVirtualSpacesId);

        if (ObjectUtils.isEmpty(subjectIds)){
            return PagedResult.create(0,new ArrayList<>());
        }



        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门

        return dao.execute(x -> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(
                                    COURSE_INFO.ID,
                                    COURSE_INFO.NAME,
                                    COURSE_INFO.CODE,
                                    COURSE_INFO.PUBLISH_CLIENT,
                                    COURSE_INFO.STATUS,
                                    COURSE_INFO.URL,
                                    COURSE_INFO.SHELVE_TIME,
                                    COURSE_INFO.STYLES)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .end()); // 查询list

            SelectSelectStep<Record> selectCountField = x.select(Fields.start()
                    .add(COURSE_INFO.ID.countDistinct()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                            name.map(n -> {
                                String str = n.replace(" ", "");
                                return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                            }),
                            status.map(COURSE_INFO.STATUS::eq),
                            shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                            shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le)
                    )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
            param.add(COURSE_INFO.ID.in(subjectIds));
            param.add(COURSE_INFO.IS_PARTY.eq(CourseInfo.PARTY_BUILING_COURSE_FLASE).or(COURSE_INFO.IS_PARTY.isNull()));

            if (subjectType.isPresent() && Objects.equals(subjectType.get(),CourseInfo.PERSONALIZATION)){
                param.add(COURSE_INFO.URL.isNotNull());
            }else if (subjectType.isPresent() && Objects.equals(subjectType.get(),CourseInfo.GENERAL)){
                param.add(COURSE_INFO.URL.isNull());
            }

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .where(param);
                return select;
            };


            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public PagedResult<CourseInfo> findSubjectVirtualSpaceForbidden(Integer page, Integer pageSize, String currentUserId, Optional<String> name, Optional<String> organizationId, Optional<Integer> status, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, String virtualSpacesId, List<String> spacesStatus, Optional<Integer> virtualSpacesStatus, List<String> grantOrganizationIds, Optional<Integer> subjectType) {

        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门

        return dao.execute(x -> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(
                                    COURSE_INFO.ID,
                                    COURSE_INFO.NAME,
                                    COURSE_INFO.CODE,
                                    COURSE_INFO.PUBLISH_CLIENT,
                                    COURSE_INFO.STATUS,
                                    COURSE_INFO.URL,
                                    COURSE_INFO.SHELVE_TIME,
                                    COURSE_INFO.STYLES,
                                    COURSE_INFO.COVER,
                                    COURSE_INFO.COVER_PATH)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .end()); // 查询list

            SelectSelectStep<Record> selectCountField = x.select(Fields.start()
                    .add(COURSE_INFO.ID.countDistinct()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                            name.map(n -> {
                                String str = n.replace(" ", "");
                                return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                            }),
                            status.map(COURSE_INFO.STATUS::eq),
                            shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                            shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le)
                    )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());


            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
            param.add(COURSE_INFO.ID.in(spacesStatus));

            if (subjectType.isPresent() && Objects.equals(subjectType.get(),CourseInfo.PERSONALIZATION)){
                param.add(COURSE_INFO.URL.isNotNull());
            }else if (subjectType.isPresent() && Objects.equals(subjectType.get(),CourseInfo.GENERAL)){
                param.add(COURSE_INFO.URL.isNull());
            }

            if (organizationId.isPresent()){
                param.add(COURSE_INFO.ORGANIZATION_ID.in(grantOrganizationIds));
            }

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .where(param);
                return select;
            };


            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setCourseVirtualSpacesStatus(CourseInfo.STATUS_FORBIDDEN);
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    /**
     *校验是否满足图谱专题更新条件
     *
     * @param subjectId 课程Id
     * @return 若满足图谱课程更新条件，则返回当前Id集合
     */
    @Override
    public List<String> checkSubjectSynchronous(String subjectId) {
        return dao.execute(ew1 ->
                ew1.select(COURSE_INFO.ID)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ID.eq(subjectId))
                        .and(COURSE_INFO.ORGANIZATION_ID.eq("1"))
                        .and(COURSE_INFO.STATUS.ne(0))
                        .fetch(COURSE_INFO.ID)
        );
    }

    /**
     * 示范培训添加专题时使用
     *
     * @param pageNum
     * @param pageSize
     * @param memberId
     * @param name
     * @param organizationId
     * @param code
     * @param status
     * @param releaseUserId
     * @param publishClient
     * @param subjectType
     * @param beginBeginDate
     * @param beginEndDate
     * @param endBeginDate
     * @param endEndDate
     * @param shelveBeginTime
     * @param shelveEndTime
     * @param selectIds
     * @param organizationIds
     * @return
     */
    @Override
    public PagedResult<CourseInfo> pageListForAddPartyTraining(int pageNum, int pageSize, String memberId, Optional<String> name, Optional<String> organizationId, Optional<String> code, Optional<Integer> status, Optional<String> releaseUserId, Optional<Integer> publishClient, Optional<Integer> subjectType, Optional<Long> beginBeginDate, Optional<Long> beginEndDate, Optional<Long> endBeginDate, Optional<Long> endEndDate, Optional<Long> shelveBeginTime, Optional<Long> shelveEndTime, Optional<String[]> selectIds, List<String> organizationIds) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门

        return dao.execute(x -> {

            //1.查询重点专题配置表的专题id集合
            List<String> courseInfoIds = partyTrainingConfigDao.execute(y -> {
                return y.select(PARTY_DEMONSTRATION_TRAINING_CONFIG.COURSE_INFO_ID)
                        .from(PARTY_DEMONSTRATION_TRAINING_CONFIG)
                        .fetch(PARTY_DEMONSTRATION_TRAINING_CONFIG.COURSE_INFO_ID);
            });

            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(
                            COURSE_INFO.ID,
                            COURSE_INFO.NAME,
                            COURSE_INFO.CODE,
                            COURSE_INFO.BEGIN_DATE,
                            COURSE_INFO.END_DATE,
                            COURSE_INFO.PUBLISH_CLIENT,
                            COURSE_INFO.STATUS,
                            COURSE_INFO.URL,
                            COURSE_INFO.SHELVE_TIME,
                            COURSE_INFO.STYLES)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .end()); // 查询list

            SelectSelectStep<Record> selectCountField = x.select(Fields.start()
                    .add(COURSE_INFO.ID.countDistinct()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                    name.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    code.map(COURSE_INFO.CODE::contains),
                    status.map(COURSE_INFO.STATUS::eq),
                    releaseUserId.map(COURSE_INFO.RELEASE_MEMBER_ID::eq),
                    //publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq),
                    beginBeginDate.map(COURSE_INFO.BEGIN_DATE::ge),
                    beginEndDate.map(COURSE_INFO.BEGIN_DATE::le),
                    endBeginDate.map(COURSE_INFO.END_DATE::ge),
                    endEndDate.map(COURSE_INFO.END_DATE::le),
                    shelveBeginTime.map(COURSE_INFO.SHELVE_TIME::ge),
                    shelveEndTime.map(COURSE_INFO.SHELVE_TIME::le),
                    Optional.ofNullable(organizationIds).map(COURSE_INFO.ORGANIZATION_ID::in),
                    subjectType.map(t -> t == 1 ? COURSE_INFO.URL.isNull() : COURSE_INFO.URL.isNotNull()))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(selectIds.map(COURSE_INFO.ID::notIn).orElse(DSL.trueCondition()));
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
            if (courseInfoIds.size() > 0 ){
                param.add(COURSE_INFO.ID.notIn(courseInfoIds));
            }

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .where(param);
                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((pageNum - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    /**
     * 重点专题添加专题时使用
     *
     * @param pageNum
     * @param pageSize
     * @param memberId
     * @param name
     * @param organizationId
     * @param code
     * @param status
     * @param releaseUserId
     * @param publishClient
     * @param subjectType
     * @param beginBeginDate
     * @param beginEndDate
     * @param endBeginDate
     * @param endEndDate
     * @param shelveBeginTime
     * @param shelveEndTime
     * @param selectIds
     * @param organizationIds
     * @return
     */
    @Override
    public PagedResult<CourseInfo> pageListForAddPartyFocus(int pageNum, int pageSize, String memberId, Optional<String> name, Optional<String> organizationId, Optional<String> code, Optional<Integer> status, Optional<String> releaseUserId, Optional<Integer> publishClient, Optional<Integer> subjectType, Optional<Long> beginBeginDate, Optional<Long> beginEndDate, Optional<Long> endBeginDate, Optional<Long> endEndDate, Optional<Long> shelveBeginTime, Optional<Long> shelveEndTime, Optional<String[]> selectIds, List<String> organizationIds) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门

        return dao.execute(x -> {

            //1.查询重点专题配置表的专题id集合
            List<String> courseInfoIds = partyFocusConfigDao.execute(y -> {
                return y.select(PARTY_FOCUS_CONFIG.COURSE_INFO_ID)
                        .from(PARTY_FOCUS_CONFIG)
                        .fetch(PARTY_FOCUS_CONFIG.COURSE_INFO_ID);
            });

            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(
                            COURSE_INFO.ID,
                            COURSE_INFO.NAME,
                            COURSE_INFO.CODE,
                            COURSE_INFO.BEGIN_DATE,
                            COURSE_INFO.END_DATE,
                            COURSE_INFO.PUBLISH_CLIENT,
                            COURSE_INFO.STATUS,
                            COURSE_INFO.URL,
                            COURSE_INFO.SHELVE_TIME,
                            COURSE_INFO.STYLES)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .end()); // 查询list

            SelectSelectStep<Record> selectCountField = x.select(Fields.start()
                    .add(COURSE_INFO.ID.countDistinct()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                    name.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    code.map(COURSE_INFO.CODE::contains),
                    status.map(COURSE_INFO.STATUS::eq),
                    releaseUserId.map(COURSE_INFO.RELEASE_MEMBER_ID::eq),
                    //publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq),
                    beginBeginDate.map(COURSE_INFO.BEGIN_DATE::ge),
                    beginEndDate.map(COURSE_INFO.BEGIN_DATE::le),
                    endBeginDate.map(COURSE_INFO.END_DATE::ge),
                    endEndDate.map(COURSE_INFO.END_DATE::le),
                    shelveBeginTime.map(COURSE_INFO.SHELVE_TIME::ge),
                    shelveEndTime.map(COURSE_INFO.SHELVE_TIME::le),
                    Optional.ofNullable(organizationIds).map(COURSE_INFO.ORGANIZATION_ID::in),
                    subjectType.map(t -> t == 1 ? COURSE_INFO.URL.isNull() : COURSE_INFO.URL.isNotNull()))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(selectIds.map(COURSE_INFO.ID::notIn).orElse(DSL.trueCondition()));
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
            if (courseInfoIds.size() > 0 ){
                param.add(COURSE_INFO.ID.notIn(courseInfoIds));
            }

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .where(param);
                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((pageNum - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public PagedResult<CourseInfo> findSubjectPage(int pageNum, int pageSize, Optional<String> name,
                                                   Optional<String> organizationId, Optional<Integer> status,
                                                   Optional<Integer> publishClient, Optional<Long> shelveBeginTime,
                                                   Optional<Long> shelveEndTime, List<String> organizationIds, String planId) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门

        return dao.execute(x -> {
            SelectOnConditionStep<Record> selectListField = x
                    .select(Fields.start().add(
                                          COURSE_INFO.ID,
                                          COURSE_INFO.NAME,
                                          COURSE_INFO.CODE,
                                          COURSE_INFO.BEGIN_DATE,
                                          COURSE_INFO.END_DATE,
                                          COURSE_INFO.PUBLISH_CLIENT,
                                          COURSE_INFO.STATUS,
                                          COURSE_INFO.URL,
                                          COURSE_INFO.SHELVE_TIME,
                                          COURSE_INFO.STYLES)
                                  .add(organizationTable.ID, organizationTable.NAME)
                                  .end()).from(COURSE_INFO)
                    .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID));

//            SelectSelectStep<Record> selectCountField = x.select(Fields.start()
//                                                                       .add(COURSE_INFO.ID.countDistinct()).end()); // 查询总条数
            // 选择计数
            SelectOnConditionStep<Record1<Integer>> selectCountField = x
                    .select(COURSE_INFO.ID.countDistinct())
                    .from(COURSE_INFO)
                    .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID));

            List<Condition> param = Stream.of(
                                                  name.map(n -> {
                                                      String str = n.replace(" ", "");
                                                      return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                                                  }),
                                                  status.map(COURSE_INFO.STATUS::eq),
                                                  shelveBeginTime.map(COURSE_INFO.SHELVE_TIME::ge),
                                                  shelveEndTime.map(COURSE_INFO.SHELVE_TIME::le),
                                                  Optional.ofNullable(organizationIds).map(COURSE_INFO.ORGANIZATION_ID::in))
                                          .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
            assemblePartyClient(publishClient, Optional.empty(), param);

            SelectConditionStep<Record> listQuery = selectListField
                    .where(param)
                    .andNotExists(
                            x.select(SUBJECT_PLAN_RELATED.SUBJECT_ID)
                             .from(SUBJECT_PLAN_RELATED)
                             .where(SUBJECT_PLAN_RELATED.PLAN_ID.eq(planId))
                             .and(SUBJECT_PLAN_RELATED.SUBJECT_ID.eq(COURSE_INFO.ID))
                    );

            SelectConditionStep<Record1<Integer>> countQuery = selectCountField
                    .where(param)
                    .andNotExists(
                            x.select(SUBJECT_PLAN_RELATED.SUBJECT_ID)
                             .from(SUBJECT_PLAN_RELATED)
                             .where(SUBJECT_PLAN_RELATED.PLAN_ID.eq(planId))
                             .and(SUBJECT_PLAN_RELATED.SUBJECT_ID.eq(COURSE_INFO.ID))
                    );


//            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
//                SelectConditionStep<Record> select = a.from(COURSE_INFO)
//                                                      .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
//                                                      .where(param).andNotExists(
//                                a.select(SUBJECT_PLAN_RELATED.SUBJECT_ID)
//                                 .from(SUBJECT_PLAN_RELATED).where(SUBJECT_PLAN_RELATED.PLAN_ID.eq(planId))
//                                 .and(SUBJECT_PLAN_RELATED.SUBJECT_ID.eq(COURSE_INFO.ID)));
//                return select;
//            };

//            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            // 获取总条数
            int count = countQuery.fetchOne(0, Integer.class);

            // 获取数据列表
            listQuery.orderBy(COURSE_INFO.SHELVE_TIME.desc());
//            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
//            listSetp.orderBy(COURSE_INFO.SHELVE_TIME.desc());
            Result<Record> record = listQuery.limit((pageNum - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public PagedResult<CourseInfo> findRelatedSubjectPage(int pageNum, int pageSize, Optional<String> name,
                                                          Optional<String> organizationId, Optional<String> code,
                                                          Optional<Integer> status, Optional<Integer> publishClient,
                                                          Optional<Long> beginBeginDate, Optional<Long> beginEndDate,
                                                          Optional<Long> endBeginDate, Optional<Long> endEndDate,
                                                          Optional<Long> shelveBeginTime, Optional<Long> shelveEndTime,
                                                          String planId) {

        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门

        return dao.execute(x -> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(
                                          COURSE_INFO.ID,
                                          COURSE_INFO.NAME,
                                          COURSE_INFO.CODE,
                                          COURSE_INFO.BEGIN_DATE,
                                          COURSE_INFO.END_DATE,
                                          COURSE_INFO.PUBLISH_CLIENT,
                                          COURSE_INFO.STATUS,
                                          COURSE_INFO.URL,
                                          COURSE_INFO.SHELVE_TIME,
                                          COURSE_INFO.STYLES)
                                  .add(organizationTable.ID,organizationTable.NAME)
                                  .end()); // 查询list

            SelectSelectStep<Record> selectCountField = x.select(Fields.start()
                                                                       .add(COURSE_INFO.ID.countDistinct()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                                                  name.map(n -> {
                                                      String str = n.replace(" ", "");
                                                      return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                                                  }),
                                                  code.map(COURSE_INFO.CODE::contains),
                                                  status.map(COURSE_INFO.STATUS::eq),
                                                  beginBeginDate.map(COURSE_INFO.BEGIN_DATE::ge),
                                                  beginEndDate.map(COURSE_INFO.BEGIN_DATE::le),
                                                  endBeginDate.map(COURSE_INFO.END_DATE::ge),
                                                  endEndDate.map(COURSE_INFO.END_DATE::le),
                                                  shelveBeginTime.map(COURSE_INFO.SHELVE_TIME::ge),
                                                  shelveEndTime.map(COURSE_INFO.SHELVE_TIME::le))
                                          .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT));
            assemblePartyClient(publishClient, Optional.empty(), param);

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                                                      .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                                                      .leftJoin(SUBJECT_PLAN_RELATED).on(SUBJECT_PLAN_RELATED.SUBJECT_ID.eq(COURSE_INFO.ID))
                                                      .where(param).and(SUBJECT_PLAN_RELATED.PLAN_ID.eq(planId));
                return select;
            };

            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.SHELVE_TIME.desc());
            Result<Record> record = listSetp.limit((pageNum - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    /**

     *  查询当前空间归属的上级部门分享的专题id
     * @param virtualSpacesOrganizationId
     * @param byVirtualSpacesId
     * @return
     */
    private List<String> findSuperiorSubjectIds(String virtualSpacesOrganizationId, List<String> byVirtualSpacesId) {

        List<String> orgIds = dao.execute(r->
                r.select(ORGANIZATION_DETAIL.ROOT)
                        .from(ORGANIZATION_DETAIL)
                        .where(ORGANIZATION_DETAIL.SUB.eq(virtualSpacesOrganizationId)
                                .and(ORGANIZATION_DETAIL.ROOT.ne(virtualSpacesOrganizationId)))
                        .fetch(ORGANIZATION_DETAIL.ROOT));

        return dao.execute(r->
                r.select(COURSE_INFO.ID)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ORGANIZATION_ID.in(orgIds)
                                .and(COURSE_INFO.SHARE_SUB.eq(CourseInfo.SHARE_SUB_YES))
                                .and(COURSE_INFO.ID.notIn(byVirtualSpacesId))
                                .and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT)))
                        .fetch(COURSE_INFO.ID));
    }

    @Override
    public Map<String, String> getSubjectText(List<String> subjectIds) {
        Map<String, String> map = new HashMap<>();
        dao.execute(w -> w.select(Fields.start().add(COURSE_INFO.ID, COURSE_INFO.DESCRIPTION_TEXT).end())
                .from(COURSE_INFO)
                .where(COURSE_INFO.ID.in(subjectIds))
                ).fetch(w -> map.put(w.getValue(COURSE_INFO.ID), w.getValue(COURSE_INFO.DESCRIPTION_TEXT)));
        return map;
    }

    @Override
    public Boolean isNetworkSubject(String subjectId) {
        return dao.execute(r -> r.fetchExists(
                r.selectOne()
                 .from(COURSE_INFO)
                 .where(COURSE_INFO.ID.eq(subjectId)
                                      .and(COURSE_INFO.SUBJECT_TYPE.in(
                                              CourseInfo.SUBJECT_TYPE_UNIFIED_ORGANIZATION,
                                              CourseInfo.SUBJECT_TYPE_PROVINCE_ORGANIZATION
                                      ).and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))))
        ));
    }

    @Override
    public List<CourseInfo> getDisableCourse(List<String> ids){
        com.zxy.product.course.jooq.tables.CourseInfo sectionCourse = COURSE_INFO.as("sectionCourse");
       return dao.execute(e->
                e.select(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.CREATE_MEMBER_ID,COURSE_INFO.BUSINESS_TYPE,sectionCourse.STATUS)
                        .from(COURSE_INFO)
                        .leftJoin(COURSE_CHAPTER)
                        .on(COURSE_CHAPTER.COURSE_ID.eq(COURSE_INFO.ID))
                        .and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID)
                                .or(COURSE_CHAPTER.VERSION_ID.isNull()))
                        .leftJoin(COURSE_CHAPTER_SECTION)
                        .on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                        .leftJoin(sectionCourse).on(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(sectionCourse.ID))
                        .where(COURSE_INFO.ID.in(ids),COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT),
                                sectionCourse.STATUS.in(CourseInfo.STATUS_FIVE_SHELVES, CourseInfo.STATUS_THE_SHELVES))
                        .fetch(r->{
                            CourseInfo into = r.into(CourseInfo.class);
                            into.setReturnFlag(r.getValue(sectionCourse.STATUS));
                            return into;
                        })
        );
    }
}
