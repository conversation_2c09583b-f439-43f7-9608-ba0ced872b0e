package com.zxy.product.course.service.support.archived;

import com.google.common.collect.Lists;
import com.zxy.common.base.helper.Pair;
import com.zxy.common.cache.Cache;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.archived.CourseStudyProgressArchivedService;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.jooq.tables.pojos.CourseRegisterEntity;
import com.zxy.product.course.util.CourseSectionStudyProgressUtil;
import com.zxy.product.course.util.CourseStudyProgressArchivedUtil;
import com.zxy.product.course.util.SplitTableName;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.Tables.*;
import static com.zxy.product.course.util.CourseStudyProgressArchivedUtil.*;

@Service
public class CourseStudyProgressArchivedSupport implements CourseStudyProgressArchivedService {

    public static final String CHECK_ARCHIVED = "check-archived#";
    @Resource
    private CommonDao<CourseStudyProgressArchived> dao;
    @Resource
    private CommonDao<CourseStudyProgress> courseStudyProgressDao;
    @Resource
    private CommonDao<CourseSectionStudyProgress> courseSectionStudyProgressDao;
    @Resource
    private CommonDao<CourseRegister> courseRegisterDao;
    @Resource
    private CommonDao<CourseSectionStudyLogAhDay> courseSectionStudyLogDay;
    @Resource
    private CommonDao<SubjectStudyLogAhDay> subjectStudyLogAhDay;
    @Resource
    private CommonDao<DeleteDataCourse> deleteDataCourseCommonDao;
    private Cache cache;
    @Autowired
    public void setCache(Cache cache) {
        this.cache = cache;
    }

    private MessageSender messageSender;

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    public Boolean checkAllStatusExistenceArchived(String memberId, String courseId) {
        return check(memberId, courseId);
    }

    public Boolean checkExistenceArchived(String memberId, String courseId) {
        return check(memberId, courseId,CourseStudyProgress.FINISH_STATUS_FINISH,CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
    }

    public Map<String, Boolean> checkExistenceArchived(List<String> memberIds, String courseId) {
        Map<String, List<String>> map = new HashMap<>();
        List<String> archivedMembers = new ArrayList<>();
        memberIds.forEach(memberId -> map.computeIfAbsent(CourseStudyProgressArchivedUtil.crc32TableName(memberId, courseId), k -> new ArrayList<>()).add(memberId));
        map.forEach((k,v)->{
            TableImpl<?> table = fileAwayTableMap.get(k);
            archivedMembers.addAll( dao.execute(ctx -> ctx.select(MEMBER_ID(table)).from(table).where(MEMBER_ID(table).in(v), COURSE_ID(table).eq(courseId),FINISH_STATUS(table).in(CourseStudyProgress.FINISH_STATUS_FINISH,CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)).fetch(Record1::value1)));
        });
        return archivedMembers.stream().collect(Collectors.toMap(k -> k, v -> false));
    }

    @Override
    public Map<String, Integer> getArchivedStatus(String memberId, List<String> courseIds) {
        Map<String, Integer> resMap = new HashMap<>();
        if (Objects.nonNull(courseIds) && !courseIds.isEmpty()) {
            Map<String, List<String>> map = new HashMap<>();

            courseIds.forEach(courseId -> map.computeIfAbsent(crc32TableName(memberId, courseId), k -> new ArrayList<>()).add(courseId));
            List<SelectOrderByStep<Record2<String, Integer>>> unionAllStep = new ArrayList<>();
            map.forEach((k, v) -> {
                TableImpl<?> table = fileAwayTableMap.get(k);
                SelectOrderByStep<Record2<String, Integer>> step = dao.execute(ctx -> ctx.select(COURSE_ID(table), FINISH_STATUS(table)).from(table).where(MEMBER_ID(table).eq(memberId), COURSE_ID(table).in(v)));
                unionAllStep.add(step);
            });
            SelectOrderByStep<Record2<String, Integer>> setp = unionAllStep.get(0);
            if (Objects.nonNull(setp)) {
                for (int i = 1; i < unionAllStep.size(); i++) {
                    setp = setp.unionAll(unionAllStep.get(i));
                }
                setp.fetch(r -> {
                    String courseId = r.get(0, String.class);
                    Integer status = r.get(1, Integer.class);
                    resMap.put(courseId, status);
                    return true;
                });

            }
            return resMap;
        }

        return Collections.emptyMap();
    }

    @Override
    public Integer deleteArchived(String memberId, String courseId) {
        return dao.execute(dsl -> {
            TableImpl<?> table = CourseStudyProgressArchivedUtil.getTable(memberId, courseId);
            return dsl.delete(table).where(CourseStudyProgressArchivedUtil.MEMBER_ID(table)
                    .eq(memberId).and(CourseStudyProgressArchivedUtil.COURSE_ID(table).eq(courseId))).execute();
        });
    }

    @Override
    public Map<String, String> findArchivedId(String memberId, String courseId) {
        TableImpl<?> table = CourseStudyProgressArchivedUtil.getTable(memberId, courseId);
        return new HashMap<String,String>() {{
            put(table.getName(), Optional.ofNullable(
                    dao.execute(dsl -> dsl
                            .select(CourseStudyProgressArchivedUtil.ID(table))
                            .from(table)
                            .where(
                                    CourseStudyProgressArchivedUtil.MEMBER_ID(table).eq(memberId)
                                            .and(CourseStudyProgressArchivedUtil.COURSE_ID(table).eq(courseId))
                            ).limit(1).fetchOne(Record1::value1)
                    )
            ).orElse(""));
        }};

    }

    @Override
    public Integer deleteArchived(Map<String, String> map) {
        if (Objects.nonNull(map) && !map.isEmpty()){
            Map.Entry<String, String> next = map.entrySet().iterator().next();
            TableImpl<?> table = CourseStudyProgressArchivedUtil.fileAwayTableMap.get(next.getKey());
            return dao.execute(dsl -> {
                Pair<String, String> archivedInfo = dsl.select(table.field("f_member_id", String.class),
                                table.field("f_course_id", String.class))
                        .from(table)
                        .where(ID(table).eq(next.getValue())).limit(1).fetchOne(r -> {
                            return Pair.create(r.getValue(table.field("f_member_id", String.class)), r.getValue(table.field("f_course_id", String.class)));
                        });
                cache.clear(CHECK_ARCHIVED +archivedInfo.getFirst()+"#"+archivedInfo.getSecond());
                return dsl
                        .delete(table)
                        .where(
                                ID(table).eq(next.getValue())
                        ).execute();
            });
        }
        return 0;
    }

    @Override
    public CourseInfo whetherPersonalizedTopicsAreArchived(String memberId, Optional<String> subjectUrl, Optional<String> id) {
        Condition condition = id.isPresent() ? COURSE_INFO.ID.eq(id.get()) : COURSE_INFO.URL.eq(subjectUrl.get());
        CourseInfo courseInfo = dao.execute(dsl -> dsl.select(COURSE_INFO.fields())
                .from(COURSE_INFO)
                .where(condition.and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_SUBJECT)))
                .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                .limit(1).fetchOne(record ->
                        record.into(COURSE_INFO).into(CourseInfo.class)
                ));
        if (Objects.nonNull(courseInfo) && !checkAllStatusExistenceArchived(memberId, Optional.ofNullable(courseInfo.getId()).orElse(""))) {
            courseInfo = null;
        }
        return courseInfo;
    }
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean courseBackToTheSource(boolean isSubject,
                                         String courseStudyTableName,
                                         String courseSectionProgressTableName,
                                         CourseStudyProgress c,
                                         List<CourseSectionStudyProgress> progress,
                                         List<CourseRegister> courseRegisters,
                                         String subjectDayTableName, String courseDayTableName,
                                         List<SubjectStudyLogAhDay> subjectStudyLogDays,
                                         List<CourseSectionStudyLogAhDay> courseSectionStudyLogDays) {
        insertCourseStudyProgress(c, courseStudyTableName);
        // 插入courseSectionStudyProgress进度表
        insertCourseSection(progress,courseSectionProgressTableName);
        // 插入courseRegister注册表
        insertCourseRegister(courseRegisters);
        // 插入章节和课程表
        insertSubjectLogDay(subjectStudyLogDays, subjectDayTableName);
        insertCourseLogDay(courseSectionStudyLogDays, courseDayTableName);
        return null;
    }

    @Override
    public Optional<CourseStudyProgressArchived> findByCourseIdAndMemberId(String courseId, String currentUserId) {
        TableImpl<?> table = getTable(currentUserId, courseId);
        return dao.execute(r ->
                 r.select()
                         .from(table)
                         .where(table.field("f_course_id", String.class).eq(courseId)
                                 .and(table.field("f_member_id", String.class).eq(currentUserId)))
                         .fetchOptionalInto(CourseStudyProgressArchived.class));

    }

    private void insertCourseStudyProgress(CourseStudyProgress c, String courseStudyTableName) {
        if (Objects.nonNull(c)) {
            // 插入courseStudyProgress分表
            TableImpl<?> table = SplitTableName.getTableNameByCode(courseStudyTableName);
            String spId = courseStudyProgressDao.execute(dsl ->
                    dsl.select(table.field("f_id", String.class))
                            .from(table)
                            .where(
                                    table.field("f_course_id",String.class).eq(c.getCourseId())
                                            .and(table.field("f_member_id",String.class).eq(c.getMemberId()))
                            )
            ).limit(1).fetchOne(Record1::value1);
            if (Objects.nonNull(spId) && !spId.isEmpty()) {
                courseStudyProgressDao.execute(dsl -> dsl.delete(table).where(table.field("f_id", String.class).eq(spId))).execute();
            }

            courseStudyProgressDao.execute(e -> e.insertInto(table, getCourseStudyProgressFields(table)).values(getCourseStudyProgressValues(c)).execute());
            // 插入courseStudyProgress总表
            List<CourseStudyProgress> courseStudyProgressList = Optional.ofNullable(
                    courseStudyProgressDao.fetch(COURSE_STUDY_PROGRESS.COURSE_ID.eq(c.getCourseId()).and(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(c.getMemberId())))
            ).orElse(new ArrayList<>());
            if (!courseStudyProgressList.isEmpty()) {
                courseStudyProgressDao.delete(courseStudyProgressList.get(0).getId());
            }
            courseStudyProgressDao.insert(c);

            //专题数据回源计算集中学时
            //todo 2025-08-03发版暂时不发消息， 注释，待后续功能完善打开
//            messageSender.send(MessageTypeContent.CONCENTRATE_STUDY_HOURS_MEMBER,
//                               MessageHeaderContent.BUSINESS_ID, c.getCourseId(),
//                               MessageHeaderContent.MEMBER_ID, c.getMemberId());
        }
    }
    private Field<?>[]  getCourseStudyProgressFields(TableImpl<?> table){
        return new Field<?>[]{
                        table.field("f_id", String.class),
                        table.field("f_member_id", String.class),
                        table.field("f_course_id", String.class),
                        table.field("f_begin_time", Long.class),
                        table.field("f_type", Integer.class),
                        table.field("f_is_required", Integer.class),
                        table.field("f_finish_status", Integer.class),
                        table.field("f_finish_time", Long.class),
                        table.field("f_study_total_time", Integer.class),
                        table.field("f_register_time", Long.class),
                        table.field("f_last_access_time", Long.class),
                        table.field("f_create_time", Long.class),
                        table.field("t_course_version_id", String.class),
                        table.field("f_mark_member_id", String.class),
                        table.field("f_mark_time", Long.class),
                        table.field("f_completed_rate", Integer.class),
                        table.field("f_current_chapter_id", String.class),
                        table.field("f_current_section_id", String.class),
                        table.field("f_push_id", String.class),
                        table.field("f_visits", Integer.class),
                        table.field("f_last_modify_time", Long.class)
                };
    }

    private  Object []  getCourseStudyProgressValues(CourseStudyProgress g){
        return new Object[]{
                g.getId(),
                g.getMemberId(),
                g.getCourseId(),
                g.getBeginTime(),
                g.getType(),
                g.getIsRequired(),
                g.getFinishStatus(),
                g.getFinishTime(),
                g.getStudyTotalTime(),
                g.getRegisterTime(),
                g.getLastAccessTime(),
                g.getCreateTime(),
                g.getCourseVersionId(),
                g.getMarkMemberId(),
                g.getMarkTime(),
                g.getCompletedRate(),
                g.getCurrentChapterId(),
                g.getCurrentSectionId(),
                g.getPushId(),
                g.getVisits(),
                g.getLastModifyTime()
        };
    }

    private void insertCourseSection(List<CourseSectionStudyProgress> modes, String courseSectionProgressTableName) {
        List<String> deleteIds = Lists.newArrayList();
        TableImpl<?> courseSectionProgressTable = CourseSectionStudyProgressUtil.getTableByCode(courseSectionProgressTableName);

        courseSectionStudyProgressDao.execute(dsl -> {
            InsertValuesStep20<?, String, String, String, String, Long, Integer, Long, Integer, Integer, Long, Integer, String, Long, Long, String, String, Integer, String, Integer, Integer> columns =
                    dsl.insertInto(courseSectionProgressTable)
                            .columns(
                                    courseSectionProgressTable.field("f_id", String.class),
                                    courseSectionProgressTable.field("f_member_id", String.class),
                                    courseSectionProgressTable.field("f_course_id", String.class),
                                    courseSectionProgressTable.field("f_section_id", String.class),
                                    courseSectionProgressTable.field("f_begin_time", Long.class),
                                    courseSectionProgressTable.field("f_finish_status", Integer.class),
                                    courseSectionProgressTable.field("f_finish_time", Long.class),
                                    courseSectionProgressTable.field("f_completed_rate", Integer.class),
                                    courseSectionProgressTable.field("f_study_total_time", Integer.class),
                                    courseSectionProgressTable.field("f_last_access_time", Long.class),
                                    courseSectionProgressTable.field("f_exam_status", Integer.class),
                                    courseSectionProgressTable.field("f_lesson_location", String.class),
                                    courseSectionProgressTable.field("f_create_time", Long.class),
                                    courseSectionProgressTable.field("f_commit_time", Long.class),
                                    courseSectionProgressTable.field("f_submit_text", String.class),
                                    courseSectionProgressTable.field("f_audit_member_id", String.class),
                                    courseSectionProgressTable.field("f_score", Integer.class),
                                    courseSectionProgressTable.field("f_comments", String.class),
                                    courseSectionProgressTable.field("f_audit_pass", Integer.class),
                                    courseSectionProgressTable.field("f_visits", Integer.class)
                            );
            for (CourseSectionStudyProgress mode : modes) {
                // 判断是否存在数据
                String id = dsl
                        .select(courseSectionProgressTable.field("f_id", String.class))
                        .from(courseSectionProgressTable)
                        .where(
                                courseSectionProgressTable.field("f_member_id", String.class).eq(mode.getMemberId())
                                        .and(courseSectionProgressTable.field("f_section_id", String.class).eq(mode.getSectionId()))
                        ).limit(1).fetchOne(Record1::value1);
                if (Objects.nonNull(id) && !id.isEmpty()) {
                    deleteIds.add(id);
                }
                 columns = columns.values(
                        mode.getId(),
                        mode.getMemberId(),
                        mode.getCourseId(),
                        mode.getSectionId(),
                        mode.getBeginTime(),
                        mode.getFinishStatus(),
                        mode.getFinishTime(),
                        mode.getCompletedRate(),
                        mode.getStudyTotalTime(),
                        mode.getLastAccessTime(),
                        mode.getExamStatus(),
                        mode.getLessonLocation(),
                        mode.getCreateTime(),
                        mode.getCommitTime(),
                        mode.getSubmitText(),
                        mode.getAuditMemberId(),
                        mode.getScore(),
                        mode.getComments(),
                        mode.getAuditPass(),
                        mode.getVisits());
            }
            dsl.delete(courseSectionProgressTable)
                .where(
                        courseSectionProgressTable.field("f_id", String.class).in(deleteIds)
                ).execute();
            deleteDataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(courseSectionProgressTable.getName(), deleteIds, ""));
            return columns.execute();
        });
    }

    private void insertCourseRegister(List<CourseRegister> modes) {
        if (modes.isEmpty()) {
            return;
        }
        // 存在数据就删除再插入
        CourseRegister courseRegister = modes.get(0);
        List<String> ids = courseRegisterDao.fetch(
                        COURSE_REGISTER.MEMBER_ID.eq(courseRegister.getMemberId())
                                .and(COURSE_REGISTER.COURSE_ID.eq(courseRegister.getCourseId()))
                )
                .stream()
                .map(CourseRegisterEntity::getId)
                .collect(Collectors.toList());
        if (!ids.isEmpty()){
            courseRegisterDao.delete(COURSE_REGISTER.ID.in(ids));
        }
        courseRegisterDao.insert(modes);
    }

    private void insertSubjectLogDay(List<SubjectStudyLogAhDay> modes, String subjectStudyLogDayTableName) {
        if (modes.isEmpty()) {
            return;
        }
        TableImpl<?> subjectStudyLogDayTable = SplitTableName.getTableNameByCode(subjectStudyLogDayTableName);
        SubjectStudyLogAhDay subjectDay = modes.get(0);
        subjectStudyLogAhDay.execute(dsl -> {
            // 判断是否存在数据
            List<String> deleteIds = dsl
                    .select(subjectStudyLogDayTable.field("f_id", String.class))
                    .from(subjectStudyLogDayTable)
                    .where(
                            subjectStudyLogDayTable.field("f_member_id", String.class).eq(subjectDay.getMemberId())
                                    .and(subjectStudyLogDayTable.field("f_subject_id", String.class).eq(subjectDay.getSubjectId()))
                    ).fetch(Record1::value1);

            InsertValuesStep8<?, String, String, String, Integer, Integer, Integer, Integer, Long> columns =
                    dsl.insertInto(subjectStudyLogDayTable).columns(
                            subjectStudyLogDayTable.field("f_id", String.class),
                            subjectStudyLogDayTable.field("f_member_id", String.class),
                            subjectStudyLogDayTable.field("f_subject_id", String.class),
                            subjectStudyLogDayTable.field("f_study_time", Integer.class),
                            subjectStudyLogDayTable.field("f_day", Integer.class),
                            subjectStudyLogDayTable.field("f_month", Integer.class),
                            subjectStudyLogDayTable.field("f_year", Integer.class),
                            subjectStudyLogDayTable.field("f_create_time", Long.class)
                    );

            for (SubjectStudyLogAhDay mode : modes) {
                columns = columns.values(
                        mode.getId(),
                        mode.getMemberId(),
                        mode.getSubjectId(),
                        mode.getStudyTime(),
                        mode.getDay(),
                        mode.getMonth(),
                        mode.getYear(),
                        mode.getCreateTime()
                );
            }
            if (Objects.nonNull(deleteIds) && !deleteIds.isEmpty()){
                dsl.delete(subjectStudyLogDayTable)
                        .where(
                                subjectStudyLogDayTable.field("f_id", String.class).in(deleteIds)
                        ).execute();
            }
            return columns.execute();
        });
    }

    public void insertCourseLogDay(List<CourseSectionStudyLogAhDay> modes, String courseSectionLogDayTableName) {
        if (modes.isEmpty()) {
            return;
        }
        TableImpl<?> courseSectionLogDayTable = SplitTableName.getTableNameByCode(courseSectionLogDayTableName);
        CourseSectionStudyLogAhDay courseDay = modes.get(0);
        courseSectionStudyLogDay.execute(dsl -> {
            // 判断是否存在数据
            List<String> deleteIds = dsl
                    .select(courseSectionLogDayTable.field("f_id", String.class))
                    .from(courseSectionLogDayTable)
                    .where(
                            courseSectionLogDayTable.field("f_member_id", String.class).eq(courseDay.getMemberId())
                                    .and(courseSectionLogDayTable.field("f_course_id", String.class).eq(courseDay.getCourseId()))
                    ).fetch(Record1::value1);

            InsertValuesStep10<?, String, String, String, Integer, Integer, Integer, Integer, Integer, Integer, Long> columns =
                    dsl.insertInto(courseSectionLogDayTable).columns(
                            courseSectionLogDayTable.field("f_id", String.class),
                            courseSectionLogDayTable.field("f_member_id", String.class),
                            courseSectionLogDayTable.field("f_course_id", String.class),
                            courseSectionLogDayTable.field("f_app_study_time", Integer.class),
                            courseSectionLogDayTable.field("f_pc_study_time", Integer.class),
                            courseSectionLogDayTable.field("f_study_time", Integer.class),
                            courseSectionLogDayTable.field("f_day", Integer.class),
                            courseSectionLogDayTable.field("f_month", Integer.class),
                            courseSectionLogDayTable.field("f_year", Integer.class),
                            courseSectionLogDayTable.field("f_create_time", Long.class)
                    );
            for (CourseSectionStudyLogAhDay mode : modes) {
                columns = columns.values(
                        mode.getId(),
                        mode.getMemberId(),
                        mode.getCourseId(),
                        mode.getAppStudyTime(),
                        mode.getPcStudyTime(),
                        mode.getStudyTime(),
                        mode.getDay(),
                        mode.getMonth(),
                        mode.getYear(),
                        mode.getCreateTime()
                );
            }
            if (Objects.nonNull(deleteIds) && !deleteIds.isEmpty()){
                dsl.delete(courseSectionLogDayTable)
                        .where(
                                courseSectionLogDayTable.field("f_id", String.class).in(deleteIds)
                        ).execute();
            }
            return columns.execute();
        });

    }
    private boolean check(String memberId, String courseId,Integer... finishStatus) {
        TableImpl<?> table = getTable(memberId, courseId);
        Condition condition;
        if (Objects.nonNull(finishStatus) && finishStatus.length != 0) {
            condition = FINISH_STATUS(table).in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
        } else {
            condition = DSL.trueCondition();
        }
        return dao.execute(ctx ->
                ctx.select(DSL.count()).from(table)
                        .where(MEMBER_ID(table).eq(memberId), COURSE_ID(table).eq(courseId), condition)
                        .limit(1).fetchOne(Record1::value1)) > 0;
    }
}
