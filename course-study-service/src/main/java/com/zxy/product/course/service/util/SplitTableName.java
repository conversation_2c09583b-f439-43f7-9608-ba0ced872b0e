package com.zxy.product.course.service.util;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.jooq.impl.TableImpl;
import static com.zxy.product.course.jooq.Tables.*;

public class SplitTableName {
	public final static Map<String, TableImpl<?>> tableNameMap = new HashMap<String, TableImpl<?>>();

	public final static List<TableImpl<?>> progressTalbeNameList = new ArrayList<TableImpl<?>>();

	static {
		// 课程log分表
	    tableNameMap.put("t_course_section_study_log_ah", COURSE_SECTION_STUDY_LOG_AH);
	    tableNameMap.put("t_course_section_study_log_bj", COURSE_SECTION_STUDY_LOG_BJ);
	    tableNameMap.put("t_course_section_study_log_cm", COURSE_SECTION_STUDY_LOG_CM);
	    tableNameMap.put("t_course_section_study_log_cq", COURSE_SECTION_STUDY_LOG_CQ);
	    tableNameMap.put("t_course_section_study_log_eb", COURSE_SECTION_STUDY_LOG_EB);
	    tableNameMap.put("t_course_section_study_log_fj", COURSE_SECTION_STUDY_LOG_FJ);
	    tableNameMap.put("t_course_section_study_log_gd", COURSE_SECTION_STUDY_LOG_GD);
	    tableNameMap.put("t_course_section_study_log_gs", COURSE_SECTION_STUDY_LOG_GS);
	    tableNameMap.put("t_course_section_study_log_gx", COURSE_SECTION_STUDY_LOG_GX);
	    tableNameMap.put("t_course_section_study_log_gz", COURSE_SECTION_STUDY_LOG_GZ);
	    tableNameMap.put("t_course_section_study_log_hb", COURSE_SECTION_STUDY_LOG_HB);
	    tableNameMap.put("t_course_section_study_log_hl", COURSE_SECTION_STUDY_LOG_HL);
	    tableNameMap.put("t_course_section_study_log_hn", COURSE_SECTION_STUDY_LOG_HN);
	    tableNameMap.put("t_course_section_study_log_jl", COURSE_SECTION_STUDY_LOG_JL);
	    tableNameMap.put("t_course_section_study_log_js", COURSE_SECTION_STUDY_LOG_JS);
	    tableNameMap.put("t_course_section_study_log_jx", COURSE_SECTION_STUDY_LOG_JX);
	    tableNameMap.put("t_course_section_study_log_ln", COURSE_SECTION_STUDY_LOG_LN);
	    tableNameMap.put("t_course_section_study_log_nm", COURSE_SECTION_STUDY_LOG_NM);
	    tableNameMap.put("t_course_section_study_log_nx", COURSE_SECTION_STUDY_LOG_NX);
	    tableNameMap.put("t_course_section_study_log_other", COURSE_SECTION_STUDY_LOG_OTHER);
	    tableNameMap.put("t_course_section_study_log_qh", COURSE_SECTION_STUDY_LOG_QH);
	    tableNameMap.put("t_course_section_study_log_qo", COURSE_SECTION_STUDY_LOG_QO);
	    tableNameMap.put("t_course_section_study_log_sc", COURSE_SECTION_STUDY_LOG_SC);
	    tableNameMap.put("t_course_section_study_log_sd", COURSE_SECTION_STUDY_LOG_SD);
	    tableNameMap.put("t_course_section_study_log_sh", COURSE_SECTION_STUDY_LOG_SH);
	    tableNameMap.put("t_course_section_study_log_sn", COURSE_SECTION_STUDY_LOG_SN);
	    tableNameMap.put("t_course_section_study_log_sx", COURSE_SECTION_STUDY_LOG_SX);
	    tableNameMap.put("t_course_section_study_log_tj", COURSE_SECTION_STUDY_LOG_TJ);
	    tableNameMap.put("t_course_section_study_log_xj", COURSE_SECTION_STUDY_LOG_XJ);
	    tableNameMap.put("t_course_section_study_log_xn", COURSE_SECTION_STUDY_LOG_XN);
	    tableNameMap.put("t_course_section_study_log_xz", COURSE_SECTION_STUDY_LOG_XZ);
	    tableNameMap.put("t_course_section_study_log_yn", COURSE_SECTION_STUDY_LOG_YN);
	    tableNameMap.put("t_course_section_study_log_zgtt", COURSE_SECTION_STUDY_LOG_ZGTT);
	    tableNameMap.put("t_course_section_study_log_zj", COURSE_SECTION_STUDY_LOG_ZJ);
	    tableNameMap.put("t_course_section_study_log_zx", COURSE_SECTION_STUDY_LOG_ZX);

	    // 课程人-课-天分表
	    tableNameMap.put("t_course_section_study_log_ah_day", COURSE_SECTION_STUDY_LOG_AH_DAY);
	    tableNameMap.put("t_course_section_study_log_bj_day", COURSE_SECTION_STUDY_LOG_BJ_DAY);
	    tableNameMap.put("t_course_section_study_log_cm_day", COURSE_SECTION_STUDY_LOG_CM_DAY);
	    tableNameMap.put("t_course_section_study_log_cq_day", COURSE_SECTION_STUDY_LOG_CQ_DAY);
	    tableNameMap.put("t_course_section_study_log_eb_day", COURSE_SECTION_STUDY_LOG_EB_DAY);
	    tableNameMap.put("t_course_section_study_log_fj_day", COURSE_SECTION_STUDY_LOG_FJ_DAY);
	    tableNameMap.put("t_course_section_study_log_gd_day", COURSE_SECTION_STUDY_LOG_GD_DAY);
	    tableNameMap.put("t_course_section_study_log_gs_day", COURSE_SECTION_STUDY_LOG_GS_DAY);
	    tableNameMap.put("t_course_section_study_log_gx_day", COURSE_SECTION_STUDY_LOG_GX_DAY);
	    tableNameMap.put("t_course_section_study_log_gz_day", COURSE_SECTION_STUDY_LOG_GZ_DAY);
	    tableNameMap.put("t_course_section_study_log_hb_day", COURSE_SECTION_STUDY_LOG_HB_DAY);
	    tableNameMap.put("t_course_section_study_log_hl_day", COURSE_SECTION_STUDY_LOG_HL_DAY);
	    tableNameMap.put("t_course_section_study_log_hn_day", COURSE_SECTION_STUDY_LOG_HN_DAY);
	    tableNameMap.put("t_course_section_study_log_jl_day", COURSE_SECTION_STUDY_LOG_JL_DAY);
	    tableNameMap.put("t_course_section_study_log_js_day", COURSE_SECTION_STUDY_LOG_JS_DAY);
	    tableNameMap.put("t_course_section_study_log_jx_day", COURSE_SECTION_STUDY_LOG_JX_DAY);
	    tableNameMap.put("t_course_section_study_log_ln_day", COURSE_SECTION_STUDY_LOG_LN_DAY);
	    tableNameMap.put("t_course_section_study_log_nm_day", COURSE_SECTION_STUDY_LOG_NM_DAY);
	    tableNameMap.put("t_course_section_study_log_nx_day", COURSE_SECTION_STUDY_LOG_NX_DAY);
	    tableNameMap.put("t_course_section_study_log_other_day", COURSE_SECTION_STUDY_LOG_OTHER_DAY);
	    tableNameMap.put("t_course_section_study_log_qh_day", COURSE_SECTION_STUDY_LOG_QH_DAY);
	    tableNameMap.put("t_course_section_study_log_qo_day", COURSE_SECTION_STUDY_LOG_QO_DAY);
	    tableNameMap.put("t_course_section_study_log_sc_day", COURSE_SECTION_STUDY_LOG_SC_DAY);
	    tableNameMap.put("t_course_section_study_log_sd_day", COURSE_SECTION_STUDY_LOG_SD_DAY);
	    tableNameMap.put("t_course_section_study_log_sh_day", COURSE_SECTION_STUDY_LOG_SH_DAY);
	    tableNameMap.put("t_course_section_study_log_sn_day", COURSE_SECTION_STUDY_LOG_SN_DAY);
	    tableNameMap.put("t_course_section_study_log_sx_day", COURSE_SECTION_STUDY_LOG_SX_DAY);
	    tableNameMap.put("t_course_section_study_log_tj_day", COURSE_SECTION_STUDY_LOG_TJ_DAY);
	    tableNameMap.put("t_course_section_study_log_xj_day", COURSE_SECTION_STUDY_LOG_XJ_DAY);
	    tableNameMap.put("t_course_section_study_log_xn_day", COURSE_SECTION_STUDY_LOG_XN_DAY);
	    tableNameMap.put("t_course_section_study_log_xz_day", COURSE_SECTION_STUDY_LOG_XZ_DAY);
	    tableNameMap.put("t_course_section_study_log_yn_day", COURSE_SECTION_STUDY_LOG_YN_DAY);
	    tableNameMap.put("t_course_section_study_log_zgtt_day", COURSE_SECTION_STUDY_LOG_ZGTT_DAY);
	    tableNameMap.put("t_course_section_study_log_zj_day", COURSE_SECTION_STUDY_LOG_ZJ_DAY);
	    tableNameMap.put("t_course_section_study_log_zx_day", COURSE_SECTION_STUDY_LOG_ZX_DAY);

	    // 专题log分表
	    tableNameMap.put("t_subject_section_study_log_ah", SUBJECT_SECTION_STUDY_LOG_AH);
	    tableNameMap.put("t_subject_section_study_log_bj", SUBJECT_SECTION_STUDY_LOG_BJ);
	    tableNameMap.put("t_subject_section_study_log_cm", SUBJECT_SECTION_STUDY_LOG_CM);
	    tableNameMap.put("t_subject_section_study_log_cq", SUBJECT_SECTION_STUDY_LOG_CQ);
	    tableNameMap.put("t_subject_section_study_log_eb", SUBJECT_SECTION_STUDY_LOG_EB);
	    tableNameMap.put("t_subject_section_study_log_fj", SUBJECT_SECTION_STUDY_LOG_FJ);
	    tableNameMap.put("t_subject_section_study_log_gd", SUBJECT_SECTION_STUDY_LOG_GD);
	    tableNameMap.put("t_subject_section_study_log_gs", SUBJECT_SECTION_STUDY_LOG_GS);
	    tableNameMap.put("t_subject_section_study_log_gx", SUBJECT_SECTION_STUDY_LOG_GX);
	    tableNameMap.put("t_subject_section_study_log_gz", SUBJECT_SECTION_STUDY_LOG_GZ);
	    tableNameMap.put("t_subject_section_study_log_hb", SUBJECT_SECTION_STUDY_LOG_HB);
	    tableNameMap.put("t_subject_section_study_log_hl", SUBJECT_SECTION_STUDY_LOG_HL);
	    tableNameMap.put("t_subject_section_study_log_hn", SUBJECT_SECTION_STUDY_LOG_HN);
	    tableNameMap.put("t_subject_section_study_log_jl", SUBJECT_SECTION_STUDY_LOG_JL);
	    tableNameMap.put("t_subject_section_study_log_js", SUBJECT_SECTION_STUDY_LOG_JS);
	    tableNameMap.put("t_subject_section_study_log_jx", SUBJECT_SECTION_STUDY_LOG_JX);
	    tableNameMap.put("t_subject_section_study_log_ln", SUBJECT_SECTION_STUDY_LOG_LN);
	    tableNameMap.put("t_subject_section_study_log_nm", SUBJECT_SECTION_STUDY_LOG_NM);
	    tableNameMap.put("t_subject_section_study_log_nx", SUBJECT_SECTION_STUDY_LOG_NX);
	    tableNameMap.put("t_subject_section_study_log_other", SUBJECT_SECTION_STUDY_LOG_OTHER);
	    tableNameMap.put("t_subject_section_study_log_qh", SUBJECT_SECTION_STUDY_LOG_QH);
	    tableNameMap.put("t_subject_section_study_log_qo", SUBJECT_SECTION_STUDY_LOG_QO);
	    tableNameMap.put("t_subject_section_study_log_sc", SUBJECT_SECTION_STUDY_LOG_SC);
	    tableNameMap.put("t_subject_section_study_log_sd", SUBJECT_SECTION_STUDY_LOG_SD);
	    tableNameMap.put("t_subject_section_study_log_sh", SUBJECT_SECTION_STUDY_LOG_SH);
	    tableNameMap.put("t_subject_section_study_log_sn", SUBJECT_SECTION_STUDY_LOG_SN);
	    tableNameMap.put("t_subject_section_study_log_sx", SUBJECT_SECTION_STUDY_LOG_SX);
	    tableNameMap.put("t_subject_section_study_log_tj", SUBJECT_SECTION_STUDY_LOG_TJ);
	    tableNameMap.put("t_subject_section_study_log_xj", SUBJECT_SECTION_STUDY_LOG_XJ);
	    tableNameMap.put("t_subject_section_study_log_xn", SUBJECT_SECTION_STUDY_LOG_XN);
	    tableNameMap.put("t_subject_section_study_log_xz", SUBJECT_SECTION_STUDY_LOG_XZ);
	    tableNameMap.put("t_subject_section_study_log_yn", SUBJECT_SECTION_STUDY_LOG_YN);
	    tableNameMap.put("t_subject_section_study_log_zgtt", SUBJECT_SECTION_STUDY_LOG_ZGTT);
	    tableNameMap.put("t_subject_section_study_log_zj", SUBJECT_SECTION_STUDY_LOG_ZJ);
	    tableNameMap.put("t_subject_section_study_log_zx", SUBJECT_SECTION_STUDY_LOG_ZX);

	    // 课程进度表分表

	    tableNameMap.put("t_course_study_progress_ah", COURSE_STUDY_PROGRESS_AH);
	    tableNameMap.put("t_course_study_progress_bj", COURSE_STUDY_PROGRESS_BJ);
	    tableNameMap.put("t_course_study_progress_cm", COURSE_STUDY_PROGRESS_CM);
	    tableNameMap.put("t_course_study_progress_cq", COURSE_STUDY_PROGRESS_CQ);
	    tableNameMap.put("t_course_study_progress_eb", COURSE_STUDY_PROGRESS_EB);
	    tableNameMap.put("t_course_study_progress_fj", COURSE_STUDY_PROGRESS_FJ);
	    tableNameMap.put("t_course_study_progress_gd", COURSE_STUDY_PROGRESS_GD);
	    tableNameMap.put("t_course_study_progress_gs", COURSE_STUDY_PROGRESS_GS);
	    tableNameMap.put("t_course_study_progress_gx", COURSE_STUDY_PROGRESS_GX);
	    tableNameMap.put("t_course_study_progress_gz", COURSE_STUDY_PROGRESS_GZ);
	    tableNameMap.put("t_course_study_progress_hb", COURSE_STUDY_PROGRESS_HB);
	    tableNameMap.put("t_course_study_progress_hl", COURSE_STUDY_PROGRESS_HL);
	    tableNameMap.put("t_course_study_progress_hn", COURSE_STUDY_PROGRESS_HN);
	    tableNameMap.put("t_course_study_progress_jl", COURSE_STUDY_PROGRESS_JL);
	    tableNameMap.put("t_course_study_progress_js", COURSE_STUDY_PROGRESS_JS);
	    tableNameMap.put("t_course_study_progress_jx", COURSE_STUDY_PROGRESS_JX);
	    tableNameMap.put("t_course_study_progress_ln", COURSE_STUDY_PROGRESS_LN);
	    tableNameMap.put("t_course_study_progress_nm", COURSE_STUDY_PROGRESS_NM);
	    tableNameMap.put("t_course_study_progress_nx", COURSE_STUDY_PROGRESS_NX);
	    tableNameMap.put("t_course_study_progress_other", COURSE_STUDY_PROGRESS_OTHER);
	    tableNameMap.put("t_course_study_progress_qh", COURSE_STUDY_PROGRESS_QH);
	    tableNameMap.put("t_course_study_progress_qo", COURSE_STUDY_PROGRESS_QO);
	    tableNameMap.put("t_course_study_progress_sc", COURSE_STUDY_PROGRESS_SC);
	    tableNameMap.put("t_course_study_progress_sd", COURSE_STUDY_PROGRESS_SD);
	    tableNameMap.put("t_course_study_progress_sh", COURSE_STUDY_PROGRESS_SH);
	    tableNameMap.put("t_course_study_progress_sn", COURSE_STUDY_PROGRESS_SN);
	    tableNameMap.put("t_course_study_progress_sx", COURSE_STUDY_PROGRESS_SX);
	    tableNameMap.put("t_course_study_progress_tj", COURSE_STUDY_PROGRESS_TJ);
	    tableNameMap.put("t_course_study_progress_xj", COURSE_STUDY_PROGRESS_XJ);
	    tableNameMap.put("t_course_study_progress_xn", COURSE_STUDY_PROGRESS_XN);
	    tableNameMap.put("t_course_study_progress_xz", COURSE_STUDY_PROGRESS_XZ);
	    tableNameMap.put("t_course_study_progress_yn", COURSE_STUDY_PROGRESS_YN);
	    tableNameMap.put("t_course_study_progress_zgtt", COURSE_STUDY_PROGRESS_ZGTT);
	    tableNameMap.put("t_course_study_progress_zj", COURSE_STUDY_PROGRESS_ZJ);
	    tableNameMap.put("t_course_study_progress_zx", COURSE_STUDY_PROGRESS_ZX);

	    // 人-专题-天分表

	    tableNameMap.put("t_subject_study_log_ah_day", SUBJECT_STUDY_LOG_AH_DAY);
	    tableNameMap.put("t_subject_study_log_bj_day", SUBJECT_STUDY_LOG_BJ_DAY);
	    tableNameMap.put("t_subject_study_log_cm_day", SUBJECT_STUDY_LOG_CM_DAY);
	    tableNameMap.put("t_subject_study_log_cq_day", SUBJECT_STUDY_LOG_CQ_DAY);
	    tableNameMap.put("t_subject_study_log_eb_day", SUBJECT_STUDY_LOG_EB_DAY);
	    tableNameMap.put("t_subject_study_log_fj_day", SUBJECT_STUDY_LOG_FJ_DAY);
	    tableNameMap.put("t_subject_study_log_gd_day", SUBJECT_STUDY_LOG_GD_DAY);
	    tableNameMap.put("t_subject_study_log_gs_day", SUBJECT_STUDY_LOG_GS_DAY);
	    tableNameMap.put("t_subject_study_log_gx_day", SUBJECT_STUDY_LOG_GX_DAY);
	    tableNameMap.put("t_subject_study_log_gz_day", SUBJECT_STUDY_LOG_GZ_DAY);
	    tableNameMap.put("t_subject_study_log_hb_day", SUBJECT_STUDY_LOG_HB_DAY);
	    tableNameMap.put("t_subject_study_log_hl_day", SUBJECT_STUDY_LOG_HL_DAY);
	    tableNameMap.put("t_subject_study_log_hn_day", SUBJECT_STUDY_LOG_HN_DAY);
	    tableNameMap.put("t_subject_study_log_jl_day", SUBJECT_STUDY_LOG_JL_DAY);
	    tableNameMap.put("t_subject_study_log_js_day", SUBJECT_STUDY_LOG_JS_DAY);
	    tableNameMap.put("t_subject_study_log_jx_day", SUBJECT_STUDY_LOG_JX_DAY);
	    tableNameMap.put("t_subject_study_log_ln_day", SUBJECT_STUDY_LOG_LN_DAY);
	    tableNameMap.put("t_subject_study_log_nm_day", SUBJECT_STUDY_LOG_NM_DAY);
	    tableNameMap.put("t_subject_study_log_nx_day", SUBJECT_STUDY_LOG_NX_DAY);
	    tableNameMap.put("t_subject_study_log_other_day", SUBJECT_STUDY_LOG_OTHER_DAY);
	    tableNameMap.put("t_subject_study_log_qh_day", SUBJECT_STUDY_LOG_QH_DAY);
	    tableNameMap.put("t_subject_study_log_qo_day", SUBJECT_STUDY_LOG_QO_DAY);
	    tableNameMap.put("t_subject_study_log_sc_day", SUBJECT_STUDY_LOG_SC_DAY);
	    tableNameMap.put("t_subject_study_log_sd_day", SUBJECT_STUDY_LOG_SD_DAY);
	    tableNameMap.put("t_subject_study_log_sh_day", SUBJECT_STUDY_LOG_SH_DAY);
	    tableNameMap.put("t_subject_study_log_sn_day", SUBJECT_STUDY_LOG_SN_DAY);
	    tableNameMap.put("t_subject_study_log_sx_day", SUBJECT_STUDY_LOG_SX_DAY);
	    tableNameMap.put("t_subject_study_log_tj_day", SUBJECT_STUDY_LOG_TJ_DAY);
	    tableNameMap.put("t_subject_study_log_xj_day", SUBJECT_STUDY_LOG_XJ_DAY);
	    tableNameMap.put("t_subject_study_log_xn_day", SUBJECT_STUDY_LOG_XN_DAY);
	    tableNameMap.put("t_subject_study_log_xz_day", SUBJECT_STUDY_LOG_XZ_DAY);
	    tableNameMap.put("t_subject_study_log_yn_day", SUBJECT_STUDY_LOG_YN_DAY);
	    tableNameMap.put("t_subject_study_log_zgtt_day", SUBJECT_STUDY_LOG_ZGTT_DAY);
	    tableNameMap.put("t_subject_study_log_zj_day", SUBJECT_STUDY_LOG_ZJ_DAY);
	    tableNameMap.put("t_subject_study_log_zx_day", SUBJECT_STUDY_LOG_ZX_DAY);

	    // 章节进度表
		tableNameMap.put("t_course_section_study_progress", COURSE_SECTION_STUDY_PROGRESS);
		tableNameMap.put("t_course_section_study_progress_xdnc", COURSE_SECTION_STUDY_PROGRESS_XDNC);
		tableNameMap.put("t_course_section_study_progress_xdns", COURSE_SECTION_STUDY_PROGRESS_XDNS);
		tableNameMap.put("t_course_section_study_progress_ffclc", COURSE_SECTION_STUDY_PROGRESS_FFCLC);
		tableNameMap.put("t_course_section_study_progress_ffclc_2023", COURSE_SECTION_STUDY_PROGRESS_FFCLC_2023);
		tableNameMap.put("t_course_section_study_progress_ffclc_2024", COURSE_SECTION_STUDY_PROGRESS_FFCLC_2024);
		tableNameMap.put("t_course_section_study_progress_ffcls", COURSE_SECTION_STUDY_PROGRESS_FFCLS);
		tableNameMap.put("t_course_section_study_progress_ffcls_2022", COURSE_SECTION_STUDY_PROGRESS_FFCLS_2022);
		tableNameMap.put("t_course_section_study_progress_ffcls_2023", COURSE_SECTION_STUDY_PROGRESS_FFCLS_2023);
		tableNameMap.put("t_course_section_study_progress_ffcls_2024", COURSE_SECTION_STUDY_PROGRESS_FFCLS_2024);
		tableNameMap.put("t_course_section_study_progress_rts", COURSE_SECTION_STUDY_PROGRESS_RTS);
		// CHBN活动增加对应课程和专题的章节进度表
		tableNameMap.put("t_course_section_study_progress_chbnc", COURSE_SECTION_STUDY_PROGRESS_CHBNC);
		tableNameMap.put("t_course_section_study_progress_chbns", COURSE_SECTION_STUDY_PROGRESS_CHBNS);
		// ZHZT活动增加对应课程和专题的章节进度表
		tableNameMap.put("t_course_section_study_progress_zhzts", COURSE_SECTION_STUDY_PROGRESS_ZHZTS);
		// 反腐倡廉课程对应的章节表
		tableNameMap.put("t_course_section_study_progress_ffclc_2022", COURSE_SECTION_STUDY_PROGRESS_FFCLC_2022);

			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_AH);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_BJ);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_CM);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_CQ);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_EB);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_FJ);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_GD);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_GS);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_GX);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_GZ);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_HB);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_HL);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_HN);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_JL);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_JS);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_JX);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_LN);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_NM);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_NX);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_OTHER);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_QH);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_QO);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_SC);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_SD);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_SH);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_SN);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_SX);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_TJ);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_XJ);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_XN);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_XZ);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_YN);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_ZGTT);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_ZJ);
			progressTalbeNameList.add(COURSE_STUDY_PROGRESS_ZX);

	}
	public static TableImpl<?> getTableNameByCode(String code){

        return tableNameMap.get(code);
    }

	public static List<TableImpl<?>> getProgressTalbeNameList(){
		return progressTalbeNameList;
	}
}
