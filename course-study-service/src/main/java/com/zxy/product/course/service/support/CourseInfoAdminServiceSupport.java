package com.zxy.product.course.service.support;

import com.alibaba.dubbo.common.utils.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.BusinessTopicService;
import com.zxy.product.course.api.CourseInfoCategoryService;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.audience.AudienceKService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.course.CourseInfoAdminService;
import com.zxy.product.course.content.*;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.jooq.tables.records.CourseInfoRecord;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;

import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.*;

/**
 * Created by keeley on 2017/9/8.
 */
@Service
public class CourseInfoAdminServiceSupport implements CourseInfoAdminService {
    public static final Logger logger = LoggerFactory.getLogger(CourseInfoAdminServiceSupport.class);
    private MessageSender messageSender;
    private AudienceKService audienceKService;
    private CourseInfoService courseInfoService;
    private BusinessTopicService businessTopicService;
    private CourseCacheService courseCacheService;
    private CourseInfoCategoryService courseInfoCategoryService;

    private CommonDao<CourseInfo> dao;
    private CommonDao<CourseTopic> topicCommonDao;
    private CommonDao<CourseShelves> shelvesDao;
    private CommonDao<CourseChapter> courseChapterCommonDao;
    private CommonDao<CourseChapterSection> courseChapterSectionCommonDao;
    private CommonDao<Organization> organizationDao;
    private CommonDao<CourseSequence> sequenceDao;
    private CommonDao<CourseChapterQuestionnaire> courseChapterQuestionnaireCommonDao;
    public static final Integer ZERO = 0;
    public static final Integer ONE = 1;

    private CommonDao<DeleteDataCourse> dataCourseCommonDao;

    @Autowired
    public void setDataCourseCommonDao(CommonDao<DeleteDataCourse> dataCourseCommonDao){
        this.dataCourseCommonDao = dataCourseCommonDao;
    }

    @Autowired
    public void setCourseChapterQuestionnaireCommonDao(CommonDao<CourseChapterQuestionnaire> courseChapterQuestionnaireCommonDao) {
        this.courseChapterQuestionnaireCommonDao = courseChapterQuestionnaireCommonDao;
    }

    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }
    @Autowired
    public void setCourseInfoService(CourseInfoService courseInfoService) {
        this.courseInfoService = courseInfoService;
    }
    @Autowired
    public void setBusinessTopicService(BusinessTopicService businessTopicService) {
        this.businessTopicService = businessTopicService;
    }

    @Autowired
    public void setCourseChapterCommonDao(CommonDao<CourseChapter> courseChapterCommonDao) {
        this.courseChapterCommonDao = courseChapterCommonDao;
    }
    @Autowired
    public void setCourseChapterSectionCommonDao(CommonDao<CourseChapterSection> courseChapterSectionCommonDao) {
        this.courseChapterSectionCommonDao = courseChapterSectionCommonDao;
    }

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setShelvesDao(CommonDao<CourseShelves> shelvesDao) {
        this.shelvesDao = shelvesDao;
    }

    @Autowired
    public void setDao(CommonDao<CourseInfo> dao) {
        this.dao = dao;
    }

    @Autowired
    public void setTopicCommonDao(CommonDao<CourseTopic> topicCommonDao) {
        this.topicCommonDao = topicCommonDao;
    }

    @Autowired
    public void setAudienceKService(AudienceKService audienceKService) {
        this.audienceKService = audienceKService;
    }

    @Autowired
    public void setOrganizationDao(CommonDao<Organization> organizationDao) {
		this.organizationDao = organizationDao;
	}
    @Autowired
	public void setSequenceDao(CommonDao<CourseSequence> sequenceDao) {
		this.sequenceDao = sequenceDao;
	}
    @Autowired
    public void setCourseInfoCategoryService(CourseInfoCategoryService courseInfoCategoryService) {
        this.courseInfoCategoryService = courseInfoCategoryService;
    }

    /**
     * 组装党校渠道字段
     */
    private void assemblePartyClient(Optional<Integer> publishClient, Optional<Integer> isParty, List<Condition> param) {
        isParty.map(t -> param.add(t == 0 ? COURSE_INFO.IS_PARTY.isNull().or(COURSE_INFO.IS_PARTY.eq(t)) : COURSE_INFO.IS_PARTY.eq(t)));
        if (isParty.isPresent() && CourseInfo.PARTY_BUILING_COURSE_TRUE.equals(isParty.get())) {
            if (publishClient.isPresent()) {
                param.add(COURSE_INFO.PUBLISH_CLIENT.eq(publishClient.get()));
            } else {
                param.add(COURSE_INFO.PUBLISH_CLIENT.in(CourseInfo.PUBLISH_CLIENT_ALL, CourseInfo.PUBLISH_CLIENT_PC));
            }
        } else {
            publishClient.ifPresent(t -> param.add(COURSE_INFO.PUBLISH_CLIENT.eq(t)));
        }
    }

	@Override
    public PagedResult<CourseInfo> find(Integer page, Integer pageSize, String currentUserId, List<String> grantOrganizationIds,
                                        int businessTypeCourse, Optional<String> name, Optional<String> organizationId,
                                        Optional<String> categoryId, Optional<String> code, Optional<Integer> status,
                                        Optional<Integer> source, Optional<String> releaseUserId, Optional<Integer> publishClient,
                                        Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, Optional<Long> releaseBeginDate,
                                        Optional<Long> releaseEndDate, Optional<Integer> isParty, Optional<Integer> allCategory,
                                        Optional<Integer> redAuditStatus, Optional<Integer> checkAuditStatus, Optional<Integer> captionOverallStatus,
                                        Optional<Integer> mentorState) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        com.zxy.product.course.jooq.tables.Organization releaseOrganizationTable = ORGANIZATION.as("releaseOrganization"); // 发布部门
        com.zxy.product.course.jooq.tables.Member createUserTable = MEMBER.as("createUser"); // 创建人
        com.zxy.product.course.jooq.tables.Member releaseUserTable = MEMBER.as("releaseUser"); // 发布人
        long now = System.currentTimeMillis();
        return dao.execute(x-> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(COURSE_INFO)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .add(releaseOrganizationTable.NAME,releaseOrganizationTable.ID)
                            .add(createUserTable.ID,createUserTable.NAME,createUserTable.FULL_NAME)
                            .add(releaseUserTable.ID,releaseUserTable.NAME,releaseUserTable.FULL_NAME)
                            .add(COURSE_RED_SHIP_AUDIT_DETAIL.ID, COURSE_RED_SHIP_AUDIT_DETAIL.SUBMIT_TIME,
                                    COURSE_RED_SHIP_AUDIT_DETAIL.RED_AUDIT_STATUS, COURSE_RED_SHIP_AUDIT_DETAIL.CHECK_AUDIT_STATUS,
                                    COURSE_RED_SHIP_AUDIT_DETAIL.RED_AUDIT_TIME)
                            .add(COURSE_CATEGORY.ID, COURSE_CATEGORY.NAME).end()); // 查询list
            SelectSelectStep<Record> selectCountField = x.select(Fields.start().add(COURSE_INFO.ID.count()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                    name.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    code.map(COURSE_INFO.CODE::contains),
                    categoryId.map(COURSE_CATEGORY.PATH::contains),
                    status.map(COURSE_INFO.STATUS::eq),
                    captionOverallStatus.map(COURSE_INFO.CAPTION_OVERALL_STATUS::eq),
                    source.map(COURSE_INFO.SOURCE::eq),
                    releaseUserId.map(COURSE_INFO.RELEASE_MEMBER_ID::eq),
                    //publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq),
                    shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                    shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le),
                    releaseBeginDate.map(COURSE_INFO.RELEASE_TIME::ge),
                    releaseEndDate.map(COURSE_INFO.RELEASE_TIME::le),
                    mentorState.map(COURSE_INFO.MENTOR_STATE::eq),
                    redAuditStatus.map(rs -> {
                        // 超时，查询红船审核状态为审核中 且 送审时间超过1.5h
                        if (rs == CourseRedShipAuditDetail.RED_AUDIT_STATUS_3) {
                            return (COURSE_RED_SHIP_AUDIT_DETAIL.RED_AUDIT_STATUS.eq(CourseRedShipAuditDetail.RED_AUDIT_STATUS_0)
                                    .and((COURSE_RED_SHIP_AUDIT_DETAIL.SUBMIT_TIME.plus(CourseRedShipAuditDetail.RED_AUDIT_THRESHOLD)).lt(now)));
                        }
                        if (rs == CourseRedShipAuditDetail.RED_AUDIT_STATUS_NEGATIVE_1) {
                            return COURSE_RED_SHIP_AUDIT_DETAIL.RED_AUDIT_STATUS.isNull();
                        }
                        if (rs == CourseRedShipAuditDetail.RED_AUDIT_STATUS_0) {
                            return (COURSE_RED_SHIP_AUDIT_DETAIL.RED_AUDIT_STATUS.eq(CourseRedShipAuditDetail.RED_AUDIT_STATUS_0)
                                    .and((COURSE_RED_SHIP_AUDIT_DETAIL.SUBMIT_TIME.plus(CourseRedShipAuditDetail.RED_AUDIT_THRESHOLD)).gt(now)));
                        }
                        return COURSE_RED_SHIP_AUDIT_DETAIL.RED_AUDIT_STATUS.eq(rs);
                    }),
                     checkAuditStatus.map(COURSE_RED_SHIP_AUDIT_DETAIL.CHECK_AUDIT_STATUS::eq)
            )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            // 查询红船审核数据（未发起过审核 + 最新审核数据）
            param.add(COURSE_RED_SHIP_AUDIT_VERSION.ID.isNull().or(COURSE_RED_SHIP_AUDIT_VERSION.IS_CURRENT.eq(CourseRedShipAuditVersion.IS_CURRENT_1)));
            //当allCategory不存在或者不为1时，只查询主序列
            if (!allCategory.isPresent()||!allCategory.get().equals(1)) param.add(COURSE_INFO_CATEGORY.IS_PRIME.eq(CourseInfoCategory.PRIME_CATEGORY));
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(businessTypeCourse));
            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(COURSE_INFO.ORGANIZATION_ID.in(grantOrganizationIds));
            param.add(organizationTable.ID.isNotNull());
            // 过滤掉工作室发布的内容
            param.add(COURSE_INFO.STATUS.ne(CourseInfo.STATUS_APPROVE));
            assemblePartyClient(publishClient, isParty, param);

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .leftJoin(createUserTable).on(createUserTable.ID.eq(COURSE_INFO.CREATE_MEMBER_ID))
                        .leftJoin(releaseUserTable).on(releaseUserTable.ID.eq(COURSE_INFO.RELEASE_MEMBER_ID))
                        .leftJoin(releaseOrganizationTable).on(releaseOrganizationTable.ID.eq(COURSE_INFO.RELEASE_ORG_ID))
                        .leftJoin(COURSE_INFO_CATEGORY).on(COURSE_INFO_CATEGORY.COURSE_INFO_ID.eq(COURSE_INFO.ID))
                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO_CATEGORY.COURSE_CATEGORY_ID))
                        .leftJoin(COURSE_RED_SHIP_AUDIT_VERSION).on(COURSE_INFO.ID.eq(COURSE_RED_SHIP_AUDIT_VERSION.COURSE_ID))
                        .leftJoin(COURSE_RED_SHIP_AUDIT_DETAIL).on(COURSE_RED_SHIP_AUDIT_VERSION.ID.eq(COURSE_RED_SHIP_AUDIT_DETAIL.VERSION_ID))
                        .where(param);
                return select;
            };
            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);
            List<Organization> releaseOrganizationList = record.into(releaseOrganizationTable).into(Organization.class);
            List<Member> createUserList = record.into(createUserTable).into(Member.class);
            List<Member> releaseUserList = record.into(releaseUserTable).into(Member.class);
            List<CourseCategory> courseCategoryList = record.into(COURSE_CATEGORY).into(CourseCategory.class);
            List<CourseRedShipAuditDetail> courseRedShipAuditDetailList = record.into(CourseRedShipAuditDetail.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setReleaseOrg(releaseOrganizationList.get(i));
                courseInfoList.get(i).setCreateUser(createUserList.get(i));
                courseInfoList.get(i).setReleaseUser(releaseUserList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
                // 超时，查询红船审核状态为审核中 且 送审时间超过1.5h
                CourseRedShipAuditDetail courseRedShipAuditDetail = courseRedShipAuditDetailList.get(i);
                if (courseRedShipAuditDetail != null
                        && courseRedShipAuditDetail.getRedAuditStatus() == CourseRedShipAuditDetail.RED_AUDIT_STATUS_0
                        && (courseRedShipAuditDetail.getSubmitTime() + CourseRedShipAuditDetail.RED_AUDIT_THRESHOLD) < now) {
                    courseRedShipAuditDetail.setRedAuditStatus(CourseRedShipAuditDetail.RED_AUDIT_STATUS_3);
                }
                courseInfoList.get(i).setCourseRedShipAuditDetail(courseRedShipAuditDetail);
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    @DataSource(type= DataSourceEnum.SLAVE)
    @Override
    public PagedResult<CourseInfo> findSelect(Integer page, Integer pageSize, Optional<Integer> client, Optional<String> name, Optional<String> category, Optional<Integer> isParty, List<String> grantOrganizationIds, Optional<List<String>> parentOrganizationIds, Optional<String[]> selectIds, Optional<List<Integer>> courseStatus,Optional<List<Integer>> captionOverallStatus) {
        List<Condition> where = new ArrayList<>();

        if(captionOverallStatus.isPresent()){
            where.add(captionOverallStatus.map(COURSE_INFO.CAPTION_OVERALL_STATUS::in).orElse(DSL.trueCondition()));
        }

        where.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
        where.add(COURSE_INFO.BUSINESS_TYPE.eq(0));
        //todo  选择器查询课程状态修改
        where.add(courseStatus.map(COURSE_INFO.STATUS::in).orElse(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES)));
//        where.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));//选择器只查询已发布的课程
        where.add(selectIds.map(COURSE_INFO.ID::notIn).orElse(DSL.trueCondition()));
        category.map(x -> where.add(COURSE_CATEGORY.PATH.contains(x)));
        name.map(x->where.add(COURSE_INFO.NAME.contains(x)));
        //client.map(x -> where.add(COURSE_INFO.PUBLISH_CLIENT.eq(x)));
        assemblePartyClient(client, isParty, where);
        return dao.execute(x -> {
            SelectOrderByStep<Record> select = x
                    .selectDistinct(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
                            .add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
                    .from(COURSE_INFO)
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                    .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                    .where(where).and(COURSE_INFO.ORGANIZATION_ID.in(grantOrganizationIds));
            if (parentOrganizationIds.isPresent()) {
            	select = select.union(
            			x.select(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
            					.add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
            			.from(COURSE_INFO)
            			.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
            			.leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
            			.where(where)
            			.and(COURSE_INFO.SHARE_SUB.eq(1).and(COURSE_INFO.ORGANIZATION_ID.in(parentOrganizationIds.get()))
            					));
            }

            int count = x.fetchCount(select);
            Result<Record> record = select.orderBy(COURSE_INFO.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);

            List<Organization> organizationList = record.map(r -> {
                Organization org = new Organization();
                org.setName(r.getValue("oname", String.class));
                return org;
            });
            List<CourseCategory> courseCategoryList = record.map(r -> {
                CourseCategory cc = new CourseCategory();
                cc.setId(r.getValue("cid", String.class));
                cc.setName(r.getValue("cname", String.class));
                return cc;
            });

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
            });

            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public int update(CourseInfo courseInfo,Integer mentorState) {
        //如果为上架中则更新状态
        if (Objects.equals(courseInfo.getStatus(),CourseInfo.STATUS_IN_SHELVES)){
            if(Objects.equals(courseInfo.getPublishType(),CourseInfo.PUBLISH_TYPE_FORMAL)) {
                courseInfo.setStatus(CourseInfo.STATUS_SHELVES);
            } else {
                courseInfo.setPublishType(CourseInfo.PUBLISH_TYPE_MEMBER);
                courseInfo.setStatus(CourseInfo.STATUS_THE_TEST);
            }
        }
        return dao.execute(dslContext ->{
            UpdateSetMoreStep<CourseInfoRecord> sql = dslContext.update(COURSE_INFO)
                    .set(COURSE_INFO.NAME, courseInfo.getName())
                    .set(COURSE_INFO.DESCRIPTION, courseInfo.getDescription())
                    .set(COURSE_INFO.CATEGORY_ID, courseInfo.getCategoryId())
                    .set(COURSE_INFO.LECTURER, courseInfo.getLecturer())
                    .set(COURSE_INFO.SOURCE, courseInfo.getSource())
                    .set(COURSE_INFO.SOURCE_DETAIL, courseInfo.getSourceDetail())
                    .set(COURSE_INFO.PUBLISH_CLIENT, courseInfo.getPublishClient())
                    .set(COURSE_INFO.ORGANIZATION_ID, courseInfo.getOrganizationId())
                    .set(COURSE_INFO.RELEASE_ORG_ID, courseInfo.getReleaseOrgId())
                    .set(COURSE_INFO.RELEASE_MEMBER_ID, courseInfo.getReleaseMemberId())
                    .set(COURSE_INFO.COVER, courseInfo.getCover())
                    .set(COURSE_INFO.COURSE_TIME, courseInfo.getCourseTime())
                    .set(COURSE_INFO.AUDIENCES, courseInfo.getAudiences())
                    .set(COURSE_INFO.COVER_PATH, courseInfo.getCoverPath())
                    .set(COURSE_INFO.IS_PUBLIC, courseInfo.getIsPublic())
                    .set(COURSE_INFO.LEARN_SEQUENCE, courseInfo.getLearnSequence())
                    .set(COURSE_INFO.SHARE_SUB, courseInfo.getShareSub())
                    .set(COURSE_INFO.ADD_TYPE, courseInfo.getAddType())
                    .set(COURSE_INFO.DESCRIPTION_TEXT, courseInfo.getDescriptionText())
                    .set(COURSE_INFO.DESCRIPTION_APP, courseInfo.getDescriptionApp())
                    .set(COURSE_INFO.INTEGRAL, courseInfo.getIntegral())
                    .set(COURSE_INFO.USE_VIDEO_SPEED, courseInfo.getUseVideoSpeed())
                    .set(COURSE_INFO.SWITCH_HIDE,courseInfo.getSwitchHide())
                    .set(COURSE_INFO.CAPTION_FOLLOW_RELEASE,courseInfo.getCaptionFollowRelease())
                    .set(COURSE_INFO.OPEN,courseInfo.getOpen())
                    .set(COURSE_INFO.MENTOR_STATE,Objects.isNull(courseInfo.getMentorState())?mentorState:courseInfo.getMentorState())
                    .set(COURSE_INFO.UPDATE_DATE, System.currentTimeMillis())
                    .set(COURSE_INFO.CONSTRUCTION_TYPE, courseInfo.getConstructionType());

            if(courseInfo.getShelveTime()!=null)
                sql.set(COURSE_INFO.SHELVE_TIME, courseInfo.getShelveTime());
            if(courseInfo.getReleaseTime()!=null)
                sql.set(COURSE_INFO.RELEASE_TIME, courseInfo.getReleaseTime());
            if(courseInfo.getPublishType()!=null)
                sql.set(COURSE_INFO.PUBLISH_TYPE, courseInfo.getPublishType());
            if(courseInfo.getStatus()!=null)
                sql.set(COURSE_INFO.STATUS, courseInfo.getStatus());
            if(courseInfo.getSource()!=null && courseInfo.getSource() == CourseInfo.SOURCE_SELF)
                sql.set(COURSE_INFO.RELATIVE_GENSEE_ID, courseInfo.getRelativeGenseeId());
            if(courseInfo.getCaptionFlag()!=null)
                sql.set(COURSE_INFO.CAPTION_FLAG, courseInfo.getCaptionFlag());
            if(courseInfo.getCaptionOverallStatus() != null)
                sql.set(COURSE_INFO.CAPTION_OVERALL_STATUS, courseInfo.getCaptionOverallStatus());
            if(courseInfo.getSwitchMentor()!=null)
                sql.set(COURSE_INFO.SWITCH_MENTOR,courseInfo.getSwitchMentor());
            if(courseInfo.getExplicitLearningStatus() != null)
                sql.set(COURSE_INFO.EXPLICIT_LEARNING_STATUS,courseInfo.getExplicitLearningStatus());
            return sql.where(COURSE_INFO.ID.eq(courseInfo.getId())).execute();
        });
    }

    @Override
    public int update(CourseInfo courseInfo, List<CourseChapter> courseChapters, List<CourseAttachment> courseAttachments,List<AudienceItem> audienceItems, CourseShelves courseShelves, String[] topicIds, List<String> deputyCategoryIds) {
        String courseId = courseInfo.getId();
        CourseInfo oldCourseInfo = dao.get(courseId);

        // 验证直播是否已被关联
        if (courseInfo.getRelativeGenseeId() != null
                && !courseInfo.getRelativeGenseeId().equals(oldCourseInfo.getRelativeGenseeId())
                && this.isRelatedGensee(courseInfo.getRelativeGenseeId())) {
            throw new UnprocessableException(ErrorCode.GenseeHasBeenRelatedToCourse);
        }
        if(!courseChapters.isEmpty() && hasEditchapter(courseId) ){
        	logger.error("开始执行删除章节时间--" + System.currentTimeMillis());
            this.deleteChapter(courseId);
            logger.error("开始执行保存章节时间--" + System.currentTimeMillis());
            courseInfoService.saveCourseChapters(courseId, courseChapters);
        }
        courseInfoService.deleteCourseAttachs(courseId);
        courseInfoService.saveCourseAttachs(courseId, courseAttachments);
        audienceKService.updateAudience(courseId, AudienceObject.BUSINESS_TYPE_COURSE,courseInfo.getCreateMemberId(),CourseInfo.URI,audienceItems);
        courseInfo.setOpen(CourseInfo.OPEN_0);
        if (CollectionUtils.isNotEmpty(audienceItems)) {
            // 查询当前课程是否是全员发布（中国移动包含子节点）
            long rootCount = audienceItems.stream().filter(item -> "1".equals(item.getJoinId()) && item.getJoinType() == 2).count();
            // 查询当前课程是否是内部发布（内部组织包含子节点）
            long insideCount = audienceItems.stream().filter(item -> "10000001".equals(item.getJoinId()) && item.getJoinType() == 2).count();

            if (rootCount > 0) {
                courseInfo.setOpen(CourseInfo.OPEN_1);
            } else if (insideCount > 0) {
                courseInfo.setOpen(CourseInfo.OPEN_2);
            } else {
                courseInfo.setOpen(CourseInfo.OPEN_0);
            }
        }


        if(courseShelves != null) {
            courseInfo.setReleaseTime(System.currentTimeMillis());
            if(Objects.equals(courseInfo.getPublishType(),CourseInfo.PUBLISH_TYPE_FORMAL)) {
                //courseInfo.setStatus(CourseInfo.STATUS_SHELVES);
                courseInfo.setStatus(CourseInfo.STATUS_IN_SHELVES);
                if(oldCourseInfo.getShelveTime() == null) courseInfo.setShelveTime(System.currentTimeMillis());
            } else {
                courseInfo.setPublishType(CourseInfo.PUBLISH_TYPE_MEMBER);
                //courseInfo.setStatus(CourseInfo.STATUS_THE_TEST);
                courseInfo.setStatus(CourseInfo.STATUS_IN_SHELVES);
            }
            if(!courseAttachments.isEmpty()) {
                messageSender.send(MessageTypeContent.KNOWLEDGE_INFO_SYNC,
                        MessageHeaderContent.ID, courseId,
                        MessageHeaderContent.BUSINESS_TYPE, KnowledgeInfo.BUSINESS_TYPE_COURSE+"");
            }
            this.addReferenceId(courseId);
            // 课程正在发布中，不支持取消发布动作
            courseCacheService.setSubjectPublishStatus(courseId);
        }
        businessTopicService.delete(courseId, CourseInfo.BUSINESS_TYPE_COURSE);//删除BusinessType
        if(topicIds!=null) {
            businessTopicService.saveBusinessTopic(courseId, CourseInfo.BUSINESS_TYPE_COURSE, topicIds);
        }
        //更新课程信息
        logger.error("即将更新课程，课程数据{}", JSON.toJSONString(courseInfo));
        this.update(courseInfo,oldCourseInfo.getMentorState());
        //更新序列关系
        courseInfoCategoryService.updateAllCategories(courseInfo,deputyCategoryIds);
        // 先更新课程信息后发消息
        if (courseShelves != null) {
            this.shelves(courseInfo, courseShelves);
        }
        messageSender.send(MessageTypeContent.COURSE_INFO_UPDATE, MessageHeaderContent.ID, courseId, MessageHeaderContent.NAME, courseInfo.getName());
        generateSubtitles(courseInfo);
        return 0;
    }


    @Override
    public CourseInfo insert(CourseInfo courseInfo) {
        logger.error("即将添加课程,课程信息{}",JSON.toJSONString(courseInfo));
    	CourseInfo insert = dao.insert(courseInfo);
    	updateCourseCode(courseInfo);
        return insert;
    }

    @Override
    public CourseInfo insert(CourseInfo courseInfo, List<CourseChapter> courseChapters, List<CourseAttachment> courseAttachments, List<AudienceItem> audienceItems, CourseShelves courseShelves, String[] topicIds,List<String> deputyCategoryIds) {
        courseInfo.forInsert();
        String courseId = courseInfo.getId();

        // 验证直播是否已被关联
        if (courseInfo.getRelativeGenseeId() != null && this.isRelatedGensee(courseInfo.getRelativeGenseeId())) {
            throw new UnprocessableException(ErrorCode.GenseeHasBeenRelatedToCourse);
        }

        if (!courseChapters.isEmpty()) {
            courseInfoService.saveCourseChapters(courseId, courseChapters);
        }

        if(!courseAttachments.isEmpty()) {
            courseInfoService.saveCourseAttachs(courseId, courseAttachments);
        }

        if(topicIds!=null) businessTopicService.saveBusinessTopic(courseId, CourseInfo.BUSINESS_TYPE_COURSE, topicIds);

        courseInfo.setOpen(CourseInfo.OPEN_0);
        if(!audienceItems.isEmpty()) {
            audienceKService.updateAudience(courseId, AudienceObject.BUSINESS_TYPE_COURSE,courseInfo.getCreateMemberId(),CourseInfo.URI,audienceItems);
            // 查询当前课程是否是全员发布（中国移动包含子节点）
            long rootCount = audienceItems.stream().filter(item -> "1".equals(item.getJoinId()) && item.getJoinType() == 2).count();
            // 查询当前课程是否是内部发布（内部组织包含子节点）
            long insideCount = audienceItems.stream().filter(item -> "10000001".equals(item.getJoinId()) && item.getJoinType() == 2).count();

            if (rootCount > 0) {
                courseInfo.setOpen(CourseInfo.OPEN_1);
            } else if (insideCount > 0) {
                courseInfo.setOpen(CourseInfo.OPEN_2);
            } else {
                courseInfo.setOpen(CourseInfo.OPEN_0);
            }
        }

        if(courseShelves != null) {

            courseInfo.setReleaseTime(System.currentTimeMillis());
            //xwolf -20190307 发布中
            if(courseInfo.getPublishType() == CourseInfo.PUBLISH_TYPE_FORMAL) {
                // courseInfo.setStatus(CourseInfo.STATUS_SHELVES);
                 courseInfo.setStatus(CourseInfo.STATUS_IN_SHELVES);
                if(courseInfo.getShelveTime() == null) courseInfo.setShelveTime(System.currentTimeMillis());
            } else {
                //courseInfo.setStatus(CourseInfo.STATUS_THE_TEST);
                courseInfo.setStatus(CourseInfo.STATUS_IN_SHELVES);
                courseInfo.setPublishType(CourseInfo.PUBLISH_TYPE_MEMBER);
            }
            courseInfo = this.insert(courseInfo);
            this.shelves(courseInfo, courseShelves);
            if(!courseAttachments.isEmpty()) {
                messageSender.send(MessageTypeContent.KNOWLEDGE_INFO_SYNC,
                        MessageHeaderContent.ID, courseId,
                        MessageHeaderContent.BUSINESS_TYPE, KnowledgeInfo.BUSINESS_TYPE_COURSE+"");
            }
            this.addReferenceId(courseId);
            // 课程正在发布，不支持取消发布动作
            courseCacheService.setSubjectPublishStatus(courseInfo.getId());

        } else {
            courseInfo =  this.insert(courseInfo);
        }
        generateSubtitles(courseInfo);
        //插入课程和目录关联关系
        courseInfoCategoryService.insertAllCategories(courseInfo,deputyCategoryIds);

        messageSender.send(MessageTypeContent.COURSE_INFO_INSERT, MessageHeaderContent.ID, courseId);
        // 新增课程后清除课程的缓存，并重新添加到缓存中
        courseCacheService.clearCacheCourseInfoMap(courseInfo.getBusinessType());

        return courseInfo;
    }

    private void generateSubtitles(CourseInfo courseInfo) {
        //是否生成字幕
        if (CourseInfo.OPEN_CAPTION.equals(courseInfo.getCaptionFlag())) {
            if (!ObjectUtils.isEmpty(courseInfo.getShowStatus())){
                messageSender.send(MessageTypeContent.CAPTION_INSERT,
                        MessageHeaderContent.COURSE_ID, courseInfo.getId(),
                        MessageHeaderContent.FLAG, courseInfo.getShowStatus().toString()
                );
            }else {
                messageSender.send(MessageTypeContent.CAPTION_INSERT,
                        MessageHeaderContent.COURSE_ID, courseInfo.getId()
                );  
            }
        }
    }


    @Override
    public CourseInfo get(String id) {
        return dao.get(id);
    }




    /**
     * 上架操作
     *
     * @param courseInfo
     * @param courseShelves
     */
    @Override
    public void shelves(CourseInfo courseInfo, CourseShelves courseShelves) {
        courseShelves.setCourseId(courseInfo.getId());
        courseShelves.setStatus(CourseShelves.STATUS_OK);
        courseShelves.forInsert();
        shelvesDao.insert(courseShelves);
        messageSender.send(MessageTypeContent.COURSE_SHELVES,
            MessageHeaderContent.ID, courseShelves.getId(),
            MessageHeaderContent.BUSINESS_ID, courseInfo.getId(),
            MessageHeaderContent.COURSE_PUBLISH_TYPE,String.valueOf(courseInfo.getPublishType()));
    }

    /**
     * 下架操作
     *
     * @param courseId
     */
    @Override
    public void theShelves(String courseId) {
        shelvesDao.execute(d -> d.update(COURSE_SHELVES).set(COURSE_SHELVES.STATUS, CourseShelves.STATUS_FAILURE)
                .where(COURSE_SHELVES.COURSE_ID.eq(courseId)).execute());
        messageSender.send(MessageTypeContent.COURSE_THE_SHELVES, MessageHeaderContent.ID, courseId);
        // 工作室内容下架
        messageSender.send(MessageTypeContent.STUDIO_COURSE_OFF_SHELVES, MessageHeaderContent.ID, courseId);
    }

    /**
     * 设置引用id
     * @param courseId
     */
    private void addReferenceId(String courseId) {
        dao.execute(dslContext ->
            dslContext.update(COURSE_CHAPTER_SECTION)
                    .set(COURSE_CHAPTER_SECTION.REFERENCE_ID, COURSE_CHAPTER_SECTION.ID)
                    .where(COURSE_CHAPTER_SECTION.REFERENCE_ID.isNull(), COURSE_CHAPTER_SECTION.COURSE_ID.eq(courseId)).execute()
        );
    }
    @Override
    public void deleteChapter(String courseId) {
        List<String> courseChapterIds = courseChapterCommonDao.execute(dslContext ->
                dslContext.select(COURSE_CHAPTER.ID).from(COURSE_CHAPTER)
                        .where(COURSE_CHAPTER.COURSE_ID.eq(courseId).and(COURSE_CHAPTER.VERSION_ID.isNull()))).fetch(COURSE_CHAPTER.ID);
     // updated by wangdongyan 2018-06-20 查询出课程对应所有section的id集合用于清除缓存
        List<String> sectionIds = courseCacheService.getSectionIds(courseId);
        List<String> versionIds = courseCacheService.getVersionIds(courseId);
        courseChapterCommonDao.delete(COURSE_CHAPTER.ID.in(courseChapterIds));
        dataCourseCommonDao.insert(DeleteDataCourse.getDeleteDataCourseList(DeleteDataCourse.getTableName(COURSE_CHAPTER), courseChapterIds,""));

        List<String> courseChapterSectionIds = courseChapterSectionCommonDao
                .execute(e -> e
                        .select(COURSE_CHAPTER_SECTION.ID)
                        .from(COURSE_CHAPTER_SECTION)
                        .where(COURSE_CHAPTER_SECTION.CHAPTER_ID.in(courseChapterIds))
                        .fetch(COURSE_CHAPTER_SECTION.ID));
        courseChapterSectionCommonDao.delete(COURSE_CHAPTER_SECTION.CHAPTER_ID.in(courseChapterIds));
        courseChapterSectionIds.forEach( courseChapter-> dataCourseCommonDao.insert(DeleteDataCourse
                .getDeleteDataCourse(DeleteDataCourse.COURSE_CHAPTER_SECTION,courseChapter,"")));
        logger.error("结束执行删除course_chapter_section数据：" + System.currentTimeMillis());
//        courseCacheService.clearCourseChapter(courseId);
     // updated by wangdongyan 2018-06-20
        for (String sectionId : sectionIds) {
        	courseCacheService.clearChapterSection(sectionId);
		}
        logger.error("结束执行清除course_chapter_section数据：" + System.currentTimeMillis());
        for (String versionId : versionIds) {
        	courseCacheService.clearCourseChapter(courseId, versionId);
		}
    }
    /**
     * 判断是否可以编辑课件
     * @param courseId
     * @return
     */
    private boolean hasEditchapter(String courseId) {
        CourseInfo oldCourse = this.get(courseId);
        return (oldCourse.getStatus() != null
                && !oldCourse.getStatus().equals(CourseInfo.STATUS_SHELVES)
                && !oldCourse.getStatus().equals(CourseInfo.STATUS_THE_TEST)
                && oldCourse.getVersionId() == null
        );
    }

    /**
     * 生成课程编码 日期(例：170220)+部门编码(例：888)+三位自动增长序列号(001) 例:170220888001
     *
     * @param course
     * @return
     */

    private synchronized void updateCourseCode(CourseInfo course) {
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyMMdd");
        String prelude = dateFormat.format(new Date()); // 初始化编码前缀
        Organization organization = organizationDao.get(course.getOrganizationId());
        String orgCode = organization.getCode() == null ? "000" : organization.getCode(); // 组织编码

        Optional<CourseSequence> csOptional = sequenceDao.fetchOne(COURSE_SEQUENCE.ORGANIZATION_ID
                .eq(course.getOrganizationId()).and(COURSE_SEQUENCE.BUSINESS_TYPE.eq(course.getBusinessType()))
                .and(COURSE_SEQUENCE.PRELUDE.eq(prelude)));

        int newSeq = 1;
        // 无则新增，有则更新，每天同一业务类型同一部门下只生成一条序列记录
        if (csOptional.isPresent()) {
            CourseSequence cs = csOptional.get();
            newSeq = cs.getSequence() + 1;
            cs.setSequence(newSeq);
            sequenceDao.update(cs);
        } else {
            // 初始化课程编码序列信息
            CourseSequence courseSequence = new CourseSequence();
            courseSequence.setSequence(1);
            courseSequence.setBusinessType(course.getBusinessType());
            courseSequence.setOrganizationId(course.getOrganizationId());
            courseSequence.setPrelude(prelude);
            courseSequence.forInsert();
            sequenceDao.insert(courseSequence);
        }
        String code = prelude + orgCode + String.format("%03d", newSeq);
        dao.execute(dao -> dao.update(COURSE_INFO).set(COURSE_INFO.CODE, code)
                .where(COURSE_INFO.ID.eq(course.getId())).execute());
    }

    @DataSource(type= DataSourceEnum.SLAVE)
	@Override
	public PagedResult<CourseInfo> findSelectNormal(Integer page, Integer pageSize, Optional<Integer> client,
                                                    Optional<String> name, Optional<String> category, Optional<Integer> isParty, List<String> grantOrganizationIds,
                                                    Optional<List<String>> parentIds, Optional<String[]> selectIds, Optional<List<Integer>> courseStatus,Optional<List<Integer>> captionOverallStatus) {
		List<Condition> where = new ArrayList<>();
        if(captionOverallStatus.isPresent()){
            where.add(captionOverallStatus.map(COURSE_INFO.CAPTION_OVERALL_STATUS::in).orElse(DSL.trueCondition()));
        }

        where.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
        where.add(COURSE_INFO.BUSINESS_TYPE.eq(0));
        //todo  选择器查询课程状态修改
        where.add(courseStatus.map(COURSE_INFO.STATUS::in).orElse(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES)));
//        where.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));//选择器只查询已发布的课程
        where.add(selectIds.map(COURSE_INFO.ID::notIn).orElse(DSL.trueCondition()));
        category.map(x -> where.add(COURSE_CATEGORY.PATH.contains(x)));
        name.map(x->where.add(COURSE_INFO.NAME.contains(x)));
        //client.map(x -> where.add(COURSE_INFO.PUBLISH_CLIENT.eq(x)));
        assemblePartyClient(client, isParty, where);
        return dao.execute(x -> {
            SelectOrderByStep<Record> select = x
                    .selectDistinct(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
                            .add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
                    .from(COURSE_INFO)
                    .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                    .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                    .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                    .where(where).and(ORGANIZATION_DETAIL.ROOT.in(grantOrganizationIds));
            if (parentIds.isPresent()) {
            	select = select.union(
            			x.select(Fields.start().add(COURSE_INFO).add(ORGANIZATION.NAME.as("oname"))
            					.add(COURSE_CATEGORY.ID.as("cid"), COURSE_CATEGORY.NAME.as("cname")).end())
            			.from(COURSE_INFO)
            			.leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(COURSE_INFO.ORGANIZATION_ID))
            			.leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
            			.where(where)
            			.and(COURSE_INFO.SHARE_SUB.eq(1).and(COURSE_INFO.ORGANIZATION_ID.in(parentIds.get()))
            					));
            }

            int count = x.fetchCount(select);
            Result<Record> record = select.orderBy(COURSE_INFO.CREATE_TIME.desc()).limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);

            List<Organization> organizationList = record.map(r -> {
                Organization org = new Organization();
                org.setName(r.getValue("oname", String.class));
                return org;
            });
            List<CourseCategory> courseCategoryList = record.map(r -> {
                CourseCategory cc = new CourseCategory();
                cc.setId(r.getValue("cid", String.class));
                cc.setName(r.getValue("cname", String.class));
                return cc;
            });

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
                // 查询章节和章节详情
                courseInfoList.get(i).setCourseChapters(courseCacheService.getCourseChapter(courseInfoList.get(i).getId(), courseInfoList.get(i).getVersionId()));
                //查询课程真实时长
                Integer time = 0;
                List<CourseChapterSection> sectionList = new ArrayList<CourseChapterSection>();
                courseInfoList.get(i).getCourseChapters().stream().filter(chapter -> chapter.getCourseChapterSections() != null && !chapter.getCourseChapterSections().isEmpty()).forEach(c -> sectionList.addAll(c.getCourseChapterSections()));
                time =sectionList.stream().filter(section -> section.getSectionType() != 8 && section.getSectionType() != 9 && section.getSectionType() != 13 && section.getSectionType() != 12).map(s -> s.getTimeSecond() + s.getTimeMinute() * 60).reduce(0, (a, b) -> a + b);
                courseInfoList.get(i).setCourseTime(time);
            });

            return PagedResult.create(count, courseInfoList);
        });
	}

    @Override
    public CourseInfo relateGensee(String id, String relativeGenseeId, String currentMemberId) {
        CourseInfo course = dao.getOptional(id).orElseThrow(() -> new UnprocessableException(ErrorCode.GetCourseInfoIsNull));
        // 已删除
        if (course.getDeleteFlag() != null && course.getDeleteFlag() == CourseInfo.DELETE_FLAG_YES) {
            throw new UnprocessableException(ErrorCode.GetCourseInfoIsNull);
        }
        // 验证课程是否已关联直播
        if (course.getRelativeGenseeId() != null) {
            throw new UnprocessableException(ErrorCode.CourseHasBeenRelateGensee);
        }
        // 验证直播是否已被关联
        if (this.isRelatedGensee(relativeGenseeId)) {
            throw new UnprocessableException(ErrorCode.GenseeHasBeenRelatedToCourse);
        }
        course.setRelativeGenseeId(relativeGenseeId);
        course.setModifyDate(null);
        return dao.update(course);
    }

    @Override
    public CourseInfo unrelateGensee(String id, String currentMemberId) {
        CourseInfo course = dao.getOptional(id).orElseThrow(() -> new UnprocessableException(ErrorCode.GetCourseInfoIsNull));
        // 已删除
        if (course.getDeleteFlag() != null && course.getDeleteFlag() == CourseInfo.DELETE_FLAG_YES) {
            throw new UnprocessableException(ErrorCode.GetCourseInfoIsNull);
        }
        // 验证课程是否已取消关联直播
        if (course.getRelativeGenseeId() == null) {
            throw new UnprocessableException(ErrorCode.CourseHasBeenUnrelateGensee);
        }
        course.setRelativeGenseeId(null);
        course.setModifyDate(null);
        return dao.update(course);
    }

    @Override
    public List<String> findRelatedGenseeIds() {
        return dao.execute(x->x.select(COURSE_INFO.RELATIVE_GENSEE_ID).from(COURSE_INFO)
                .where(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO)
                        .or(COURSE_INFO.DELETE_FLAG.isNull()))
                .and(COURSE_INFO.RELATIVE_GENSEE_ID.isNotNull())).fetch(COURSE_INFO.RELATIVE_GENSEE_ID);
    }

    @Override
    public List<CourseInfo> findStatus(Optional<String[]> selectIds) {
       return dao.execute(x -> x.select(Fields.start().add(COURSE_INFO.STATUS).add(COURSE_INFO.ID).end()).from(COURSE_INFO)
                .where(selectIds.map(COURSE_INFO.ID::in).orElse(DSL.trueCondition())).fetch().into(CourseInfo.class));
    }

    /**
     * 验证直播是否已被关联
     * @param relativeGenseeId 直播id
     * @return boolean
     */
    public boolean isRelatedGensee(String relativeGenseeId) {
        return dao.fetchOne(COURSE_INFO.RELATIVE_GENSEE_ID.eq(relativeGenseeId)).isPresent();
    }

    @Override
    public Integer updateSwitchHide(Integer hide, Optional<Integer> history) {

        if (!hide.equals(ZERO)) {//如果讨论区是打开的则讨论记录开关必然打开
            return dao.execute(w -> w.update(COURSE_INFO)
                    .set(COURSE_INFO.SWITCH_HIDE, hide)
                    .set(COURSE_INFO.HISTORY_HIDE, ONE)
                    .where(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                    .execute()
            );
        }

        //修改全部的历史记录and课程讨论区开关
        return history.map(integer -> dao.execute(w -> w.update(COURSE_INFO)
                .set(COURSE_INFO.SWITCH_HIDE, hide)
                .set(COURSE_INFO.HISTORY_HIDE, integer)
                .where(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                .execute()
        )).orElse(null);

    }

    @Override
    public CourseInfo updateSwitchHideOne(String id, Integer hide, Optional<Integer> history) {
        CourseInfo courseInfo = dao.get(id);
        if (!hide.equals(ZERO)) {//如果讨论区是打开的则讨论记录开关必然打开
            if (null != courseInfo){
                courseInfo.setSwitchHide(hide);
                courseInfo.setHistoryHide(ONE);
                courseInfo.setModifyDate(null);
                return dao.update(courseInfo);
            }
        }else {
            if (!history.isPresent()) {
                logger.error("history为null:{}", history);
                return null;
            }
            if (null != courseInfo){
                courseInfo.setSwitchHide(hide);
                courseInfo.setHistoryHide(history.get());
                courseInfo.setModifyDate(null);
                return dao.update(courseInfo);
            }
        }
        return null;
    }

    @Override
    public CourseInfo getSwitchHideAndHistoryHide(String id) {
        return dao.execute(w -> w.select(Fields.start()
                .add(COURSE_INFO.NAME)
                .add(COURSE_INFO.SWITCH_HIDE)
                .add(COURSE_INFO.HISTORY_HIDE)
                .end()).from(COURSE_INFO)
                .where(COURSE_INFO.ID.eq(id))
                .fetchOne()
        ).into(CourseInfo.class);
    }

    @Override
    public PagedResult<CourseInfo> findBy(Integer page, Integer pageSize, List<String> grantOrganizationIds, String currentUserId, int businessTypeCourse, Optional<String> name, Optional<String> categoryId, Optional<Integer> publishClient, Optional<Long> shelveBeginDate, Optional<Long> shelveEndDate, Optional<Integer> isParty) {

        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        return dao.execute(x-> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(COURSE_INFO)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .add(COURSE_CATEGORY.ID, COURSE_CATEGORY.NAME).end()); // 查询list
            SelectSelectStep<Record> selectCountField = x.select(Fields.start().add(COURSE_INFO.ID.count()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                    name.map(n -> {
                        String str = n.replace(" ", "");
                        return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                    }),
                    categoryId.map(COURSE_CATEGORY.PATH::contains),
                    publishClient.map(COURSE_INFO.PUBLISH_CLIENT::eq),
                    isParty.map(COURSE_INFO.IS_PARTY::eq),
                    shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                    shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(businessTypeCourse));
            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(organizationTable.ID.isNotNull());
            param.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));
            param.add(COURSE_INFO.ORGANIZATION_ID.in(grantOrganizationIds));
            param.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES));
            assemblePartyClient(publishClient, isParty, param);

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                        .where(param);
                return select;
            };
            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);
            List<CourseCategory> courseCategoryList = record.into(COURSE_CATEGORY).into(CourseCategory.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }


    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public PagedResult<CourseInfo> findCourseVirtualSpace(Integer page,
                                                          Integer pageSize,
                                                          String currentUserId,
                                                          List<String> grantOrganizationIds,
                                                          Integer businessType,
                                                          Optional<String> name,
                                                          Optional<String> organizationId,
                                                          Optional<String> categoryId,
                                                          Optional<Integer> status,
                                                          Optional<Long> shelveBeginDate,
                                                          Optional<Long> shelveEndDate,
                                                          String virtualSpacesId,
                                                          List<String> virtualSpacesOrganizationId,
                                                          Optional<String> OrgId,
                                                          List<String> spacesStatus,
                                                          Optional<Integer> virtualSpacesStatus,
                                                          List<String> spacesStatusAddTo) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        com.zxy.product.course.jooq.tables.Organization releaseOrganizationTable = ORGANIZATION.as("releaseOrganization"); // 发布部门
        return dao.execute(x-> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(COURSE_INFO.CODE,COURSE_INFO.SHELVE_TIME,COURSE_INFO.ID,COURSE_INFO.NAME,COURSE_INFO.PUBLISH_CLIENT,COURSE_INFO.SOURCE,COURSE_INFO.STATUS,COURSE_INFO.COVER,COURSE_INFO.COVER_PATH)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .add(releaseOrganizationTable.NAME,releaseOrganizationTable.ID)
                            .add(COURSE_CATEGORY.ID, COURSE_CATEGORY.NAME).end()); // 查询list
            SelectSelectStep<Record> selectCountField = x.select(Fields.start().add(COURSE_INFO.ID.count()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                            name.map(n -> {
                                String str = n.replace(" ", "");
                                return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                            }),
                            categoryId.map(COURSE_CATEGORY.PATH::contains),
                            status.map(COURSE_INFO.STATUS::eq),
                            shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                            shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le)
                    )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(businessType));
            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(COURSE_INFO.IS_PARTY.eq(CourseInfo.PARTY_BUILING_COURSE_FLASE).or(COURSE_INFO.IS_PARTY.isNull()));


            //查询组织
            if(OrgId.isPresent()){
                //列表归属列表条件
                param.add(COURSE_INFO.ORGANIZATION_ID.in(grantOrganizationIds));
            }else {
                //默认查询当前归属空间
                param.add(COURSE_INFO.ORGANIZATION_ID.in(virtualSpacesOrganizationId));
            }

            //查询启用
            if (!ObjectUtils.isEmpty(spacesStatus)) {
                //排除当前空间资源禁用的 and
                param.add(COURSE_INFO.ID.notIn(spacesStatus));
            }


            param.add(organizationTable.ID.isNotNull());
            // 过滤掉工作室发布的内容
            param.add(COURSE_INFO.STATUS.ne(CourseInfo.STATUS_APPROVE));

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .leftJoin(releaseOrganizationTable).on(releaseOrganizationTable.ID.eq(COURSE_INFO.RELEASE_ORG_ID))
                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                        .where(param).or(COURSE_INFO.ID.in(spacesStatusAddTo));
                return select;
            };
            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);
            List<Organization> releaseOrganizationList = record.into(releaseOrganizationTable).into(Organization.class);
            List<CourseCategory> courseCategoryList = record.into(COURSE_CATEGORY).into(CourseCategory.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setReleaseOrg(releaseOrganizationList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public PagedResult<CourseInfo> findSuperiorCourse(List<String> byVirtualSpacesId,
                                                      Integer page, Integer pageSize,
                                                      Optional<String> name,
                                                      Optional<String> categoryId,
                                                      Optional<Long> shelveBeginDate,
                                                      Optional<Long> shelveEndDate,
                                                      Integer businessType,
                                                      String virtualSpacesId,
                                                      Optional<Integer> status,
                                                      String virtualSpacesOrganizationId,
                                                      Optional<Integer> source) {

        //查询当前空间归属的上级部门分享的课程id
        List<String> courseIds = findSuperiorCourseIds(virtualSpacesOrganizationId,byVirtualSpacesId);

        if (ObjectUtils.isEmpty(courseIds)){
            return PagedResult.create(0,new ArrayList<>());
        }


        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        com.zxy.product.course.jooq.tables.Organization releaseOrganizationTable = ORGANIZATION.as("releaseOrganization"); // 发布部门
        return dao.execute(x-> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(COURSE_INFO.CODE,COURSE_INFO.CREATE_TIME,COURSE_INFO.ID,COURSE_INFO.NAME,COURSE_INFO.PUBLISH_CLIENT,COURSE_INFO.SOURCE,COURSE_INFO.STATUS)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .add(releaseOrganizationTable.NAME,releaseOrganizationTable.ID)
                            .add(COURSE_CATEGORY.ID, COURSE_CATEGORY.NAME).end()); // 查询list
            SelectSelectStep<Record> selectCountField = x.select(Fields.start().add(COURSE_INFO.ID.count()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                            name.map(n -> {
                                String str = n.replace(" ", "");
                                return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                            }),
                            categoryId.map(COURSE_CATEGORY.PATH::contains),
                            status.map(COURSE_INFO.STATUS::eq),
                            shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                            shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le),
                            source.map(COURSE_INFO.SOURCE::eq)
                    )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
            param.add(COURSE_INFO.BUSINESS_TYPE.eq(businessType));
            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));
            param.add(COURSE_INFO.IS_PARTY.eq(CourseInfo.PARTY_BUILING_COURSE_FLASE).or(COURSE_INFO.IS_PARTY.isNull()));

            param.add(organizationTable.ID.isNotNull());
            // 过滤掉工作室发布的内容
            param.add(COURSE_INFO.STATUS.ne(CourseInfo.STATUS_APPROVE));
            param.add(COURSE_INFO.ID.in(courseIds));



            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .leftJoin(releaseOrganizationTable).on(releaseOrganizationTable.ID.eq(COURSE_INFO.RELEASE_ORG_ID))
                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                        .where(param);
                return select;
            };
            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);
            List<Organization> releaseOrganizationList = record.into(releaseOrganizationTable).into(Organization.class);
            List<CourseCategory> courseCategoryList = record.into(COURSE_CATEGORY).into(CourseCategory.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setReleaseOrg(releaseOrganizationList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
            });
            return PagedResult.create(count, courseInfoList);
        });

    }


    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public PagedResult<CourseInfo> findCourseVirtualSpaceForbidden(Integer page,
                                                                   Integer pageSize,
                                                                   String currentUserId,
                                                                   Integer businessType,
                                                                   Optional<String> name,
                                                                   Optional<String> organizationId,
                                                                   Optional<String> categoryId,
                                                                   Optional<Integer> status,
                                                                   Optional<Long> shelveBeginDate,
                                                                   Optional<Long> shelveEndDate,
                                                                   String virtualSpacesId,
                                                                   List<String> spacesStatus,
                                                                   Optional<Integer> virtualSpacesStatus,
                                                                   List<String> grantOrganizationIds) {
        com.zxy.product.course.jooq.tables.Organization organizationTable = ORGANIZATION.as("organization"); // 所属部门
        com.zxy.product.course.jooq.tables.Organization releaseOrganizationTable = ORGANIZATION.as("releaseOrganization"); // 发布部门
        return dao.execute(x-> {
            SelectSelectStep<Record> selectListField = x
                    .select(Fields.start().add(COURSE_INFO.CODE,COURSE_INFO.SHELVE_TIME,COURSE_INFO.ID,COURSE_INFO.NAME,COURSE_INFO.PUBLISH_CLIENT,COURSE_INFO.SOURCE,COURSE_INFO.STATUS,COURSE_INFO.COVER,COURSE_INFO.COVER_PATH)
                            .add(organizationTable.ID,organizationTable.NAME)
                            .add(releaseOrganizationTable.NAME,releaseOrganizationTable.ID)
                            .add(COURSE_CATEGORY.ID, COURSE_CATEGORY.NAME).end()); // 查询list
            SelectSelectStep<Record> selectCountField = x.select(Fields.start().add(COURSE_INFO.ID.count()).end()); // 查询总条数

            List<Condition> param = Stream.of(
                            name.map(n -> {
                                String str = n.replace(" ", "");
                                return DSL.replace(COURSE_INFO.NAME, " ", "").contains(str);
                            }),
                            categoryId.map(COURSE_CATEGORY.PATH::contains),
                            status.map(COURSE_INFO.STATUS::eq),
                            shelveBeginDate.map(COURSE_INFO.SHELVE_TIME::ge),
                            shelveEndDate.map(COURSE_INFO.SHELVE_TIME::le)
                    )
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            param.add(COURSE_INFO.BUSINESS_TYPE.eq(businessType));
            param.add(COURSE_INFO.DELETE_FLAG.ne(CourseInfo.DELETE_FLAG_YES).or(COURSE_INFO.DELETE_FLAG.isNull()));


            param.add(organizationTable.ID.isNotNull());
            // 过滤掉工作室发布的内容
            param.add(COURSE_INFO.STATUS.ne(CourseInfo.STATUS_APPROVE));
            //只查询禁用的
            param.add(COURSE_INFO.ID.in(spacesStatus));

            if (organizationId.isPresent()){
                param.add(COURSE_INFO.ORGANIZATION_ID.in(grantOrganizationIds));
            }



            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(COURSE_INFO)
                        .leftJoin(organizationTable).on(organizationTable.ID.eq(COURSE_INFO.ORGANIZATION_ID))
                        .leftJoin(releaseOrganizationTable).on(releaseOrganizationTable.ID.eq(COURSE_INFO.RELEASE_ORG_ID))
                        .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO.CATEGORY_ID))
                        .where(param);
                return select;
            };
            int count = stepFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(COURSE_INFO.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();
            List<CourseInfo> courseInfoList = record.into(COURSE_INFO).into(CourseInfo.class);
            List<Organization> organizationList = record.into(organizationTable).into(Organization.class);
            List<Organization> releaseOrganizationList = record.into(releaseOrganizationTable).into(Organization.class);
            List<CourseCategory> courseCategoryList = record.into(COURSE_CATEGORY).into(CourseCategory.class);

            IntStream.range(0, courseInfoList.size()).forEach(i -> {
                courseInfoList.get(i).setOrganization(organizationList.get(i));
                courseInfoList.get(i).setReleaseOrg(releaseOrganizationList.get(i));
                courseInfoList.get(i).setCategory(courseCategoryList.get(i));
                courseInfoList.get(i).setCourseVirtualSpacesStatus(CourseInfo.STATUS_FORBIDDEN);
            });
            return PagedResult.create(count, courseInfoList);
        });
    }

    @Override
    public void updateConstructionType(String courseId, int constructionTypeYes, List<AudienceItem> audienceItems) {

        CourseInfo courseInfo = dao.get(courseId);
        if (ObjectUtils.isEmpty(courseInfo)){
            return;
        }
        int open = CourseInfo.OPEN_0;
        if (!audienceItems.isEmpty()) {
            audienceKService.updateAudience(
                    courseId,
                    AudienceObject.BUSINESS_TYPE_COURSE,
                    courseInfo.getCreateMemberId(),
                    CourseInfo.URI,
                    audienceItems
            );

            boolean hasRoot = false;
            boolean hasInside = false;

            for (AudienceItem item : audienceItems) {
                if (item.getJoinType() == 2) {
                    if ("1".equals(item.getJoinId())) {
                        hasRoot = true;
                        break;
                    } else if ("10000001".equals(item.getJoinId())) {
                        hasInside = true;
                        break;
                    }
                }
            }
            if (hasRoot) {
                open = CourseInfo.OPEN_1;
            } else if (hasInside) {
                open = CourseInfo.OPEN_2;
            }

            int finalOpen = open;
            dao.execute(x ->
                    x.update(COURSE_INFO)
                            .set(COURSE_INFO.CONSTRUCTION_TYPE, constructionTypeYes)
                            .set(COURSE_INFO.OPEN, finalOpen)
                            .where(COURSE_INFO.ID.eq(courseId)).execute());

            messageSender.send(MessageTypeContent.COURSE_INFO_UPDATE, MessageHeaderContent.ID, courseId, MessageHeaderContent.NAME, courseInfo.getName());
        }
    }

    /**
     * 查询当前空间归属的上级部门分享的课程id
     * @param virtualSpacesOrganizationId
     * @param byVirtualSpacesId
     * @return
     */
    private List<String> findSuperiorCourseIds(String virtualSpacesOrganizationId, List<String> byVirtualSpacesId) {

        List<String> orgIds = dao.execute(r->
                r.select(ORGANIZATION_DETAIL.ROOT)
                        .from(ORGANIZATION_DETAIL)
                        .where(ORGANIZATION_DETAIL.SUB.eq(virtualSpacesOrganizationId)
                                .and(ORGANIZATION_DETAIL.ROOT.ne(virtualSpacesOrganizationId)))
                        .fetch(ORGANIZATION_DETAIL.ROOT));

        return dao.execute(r->
                r.select(COURSE_INFO.ID)
                        .from(COURSE_INFO)
                        .where(COURSE_INFO.ORGANIZATION_ID.in(orgIds)
                                .and(COURSE_INFO.SHARE_SUB.eq(CourseInfo.SHARE_SUB_YES))
                                .and(COURSE_INFO.ID.notIn(byVirtualSpacesId))
                                .and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE)))
                        .fetch(COURSE_INFO.ID));
    }
}
