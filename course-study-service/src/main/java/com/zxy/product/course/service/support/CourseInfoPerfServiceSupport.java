package com.zxy.product.course.service.support;

import com.zxy.product.course.jooq.Tables;
import com.google.common.collect.Maps;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.CourseInfoPerfService;
import com.zxy.product.course.api.OrganizationService;
import com.zxy.product.course.content.DataSource;
import com.zxy.product.course.content.DataSourceEnum;
import com.zxy.product.course.entity.AudienceMember;
import com.zxy.product.course.entity.CourseCategory;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.Member;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.tables.AudienceMember.AUDIENCE_MEMBER;
import static com.zxy.product.course.jooq.tables.AudienceObject.AUDIENCE_OBJECT;
import static com.zxy.product.course.jooq.tables.BusinessTopic.BUSINESS_TOPIC;
import static com.zxy.product.course.jooq.tables.CourseCategory.COURSE_CATEGORY;
import static com.zxy.product.course.jooq.tables.CourseInfo.COURSE_INFO;
import static com.zxy.product.course.jooq.tables.CourseInfoCategory.COURSE_INFO_CATEGORY;
import static com.zxy.product.course.service.util.SecurePathCdnUtils.generateSecurePathCdn;

/**
 * 描述: 针对"课程/专题"性能优化服务实现类
 * 作者: mas
 * 创建日期: 2025/3/3
 */
@Service
public class CourseInfoPerfServiceSupport implements CourseInfoPerfService {

    /** 默认排序字段: 0=上架时间 */
    private static final int COURSE_QUERY_DEFAULT_ORDER_BY = 0;
    /** 默认排序方式: 2=降序(desc) */
    private static final int COURSE_QUERY_DEFAULT_ORDER = 2;
    /** 学员端展示标识: 1=展示 */
    private static final int COURSE_STUDENT_FRONT_SHOW = 1;
    /**假分页常量*/
    private static final int FAKE_PAGINATION=1;

    private CommonDao<CourseInfo> courseInfoCommonDao;
    private CommonDao<AudienceMember> audienceMemberCommonDao;
    private OrganizationService organizationService;

    @Autowired
    public void setCourseInfoCommonDao(CommonDao<CourseInfo> courseInfoCommonDao) {
        this.courseInfoCommonDao = courseInfoCommonDao;
    }

    @Autowired
    public void setAudienceMemberCommonDao(CommonDao<AudienceMember> audienceMemberCommonDao) {
        this.audienceMemberCommonDao = audienceMemberCommonDao;
    }

    @Autowired
    public void setOrganizationService(OrganizationService organizationService) {
        this.organizationService = organizationService;
    }

    @Override
    @Transactional(readOnly = true)
    @DataSource(type = DataSourceEnum.SLAVE)
    public Map<String, Object> findCourseOrSubjectInfoPage(String currentUserId, Integer page, Integer pageSize,
                                                           Optional<Integer> orderBy, Optional<Integer> order, Optional<String> searchContent, Optional<String> categoryId,
                                                           Optional<String> topicId, Optional<Integer> publishClient, Optional<Integer> companyType, Integer type,
                                                           boolean enableCacheFirstPageCourseIds, boolean enablePageCount, boolean enableSorting, Integer from) {
        // TODO 废弃enableCacheFirstPageCourseIds 缓存第一页课程id到临时表的方案, 后续考虑更优解

        int firstResult = (page - 1) * pageSize;

        // 查询当前用户的受众项ids
        List<String> audienceItemIds = audienceMemberCommonDao.execute(ctx ->
                ctx.selectDistinct(AUDIENCE_MEMBER.ITEM_ID)
                        .from(AUDIENCE_MEMBER)
                        .where(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                        .fetch(AUDIENCE_MEMBER.ITEM_ID));

        List<CourseInfo> courseItems = courseInfoCommonDao.execute(context -> {
            // 构建基础查询条件
            List<Condition> conditions = buildConditions(searchContent, categoryId, publishClient, type, audienceItemIds, from);

            SelectSelectStep<Record> selectStep = context.select(Fields.start()
                    .add(COURSE_INFO.ID)
                    .add(COURSE_INFO.NAME)
                    .add(COURSE_INFO.TYPE)
                    .add(COURSE_INFO.CREATE_TIME)
                    .add(COURSE_INFO.RELEASE_TIME)
                    .add(COURSE_INFO.SOURCE)
                    .add(COURSE_INFO.STATUS)
                    .add(COURSE_INFO.COVER)
                    .add(COURSE_INFO.COVER_PATH)
                    .add(COURSE_INFO.BEGIN_DATE)
                    .add(COURSE_INFO.END_DATE)
                    .add(COURSE_INFO.DESCRIPTION)
                    .add(COURSE_INFO.VISITS)
                    .add(COURSE_INFO.AVG_SCORE)
                    .add(COURSE_INFO.URL)
                    .add(COURSE_INFO.DESCRIPTION_TEXT)
                    .add(COURSE_INFO.INTEGRAL)
                    .end());

            // 动态关联课程目录表
            SelectJoinStep<Record> fromStep = applyCategoryJoin(categoryId, selectStep);
            SelectConditionStep<Record> query = fromStep.where(conditions);

            query = applyAdditionalFilters(query, context, topicId, companyType, currentUserId);

            SortField<?> sortField = getSortField(orderBy.orElse(COURSE_QUERY_DEFAULT_ORDER_BY),
                    order.orElse(COURSE_QUERY_DEFAULT_ORDER));
            SelectLimitStep<Record> queryStep = !enableSorting ? query.orderBy(sortField) : query;

            return queryStep.limit(firstResult, pageSize + 1).fetch(r->{
                CourseInfo info = r.into(COURSE_INFO).into(CourseInfo.class);
                info.setCoverPath(generateSecurePathCdn(r.getValue(Tables.COURSE_INFO.COVER_PATH)));
                return info;
            });
        });

        // 是否存在下一页
        int nextPage = (courseItems.size() > pageSize) ? 1 : 0;

        int count = 0;
        if (enablePageCount) {
            count = courseInfoCommonDao.execute(dsl -> {
                List<Condition> conditions = buildConditions(searchContent, categoryId, publishClient, type, audienceItemIds, from);

                SelectSelectStep<Record> countSelectStep = dsl.select(Fields.start().add(COURSE_INFO.ID.count()).end());
                SelectJoinStep<Record> fromStep = applyCategoryJoin(categoryId, countSelectStep);

                SelectConditionStep<Record> query = fromStep.where(conditions);
                query = applyAdditionalFilters(query, dsl, topicId, companyType, currentUserId);

                return query.fetchOne().getValue(0, Integer.class);
            });
        }

        // 封装返回数据
        if (nextPage == 1) {
            courseItems.remove(courseItems.size() - 1);
        }
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("items", courseItems);
        resultMap.put("recordCount", count);
        resultMap.put("more", nextPage);
        return resultMap;
    }

    @Override
    @Transactional(readOnly = true)
    @DataSource(type = DataSourceEnum.SLAVE)
    public Map<String, Object> findCourseOrSubjectInfoPageNew(String currentUserId, Integer page, Integer pageSize,
                                                           Optional<Integer> orderBy, Optional<Integer> order, Optional<String> searchContent, Optional<String> categoryId,
                                                           Optional<String> topicId, Optional<Integer> publishClient, Optional<Integer> companyType, Integer type,
                                                           boolean enableCacheFirstPageCourseIds, boolean enablePageCount, boolean enableSorting, Integer from) {
        // TODO 废弃enableCacheFirstPageCourseIds 缓存第一页课程id到临时表的方案, 后续考虑更优解

        int firstResult = (page - 1) * pageSize;

        // 查询当前用户的受众项ids
        List<String> audienceItemIds = audienceMemberCommonDao.execute(ctx ->
                ctx.selectDistinct(AUDIENCE_MEMBER.ITEM_ID)
                        .from(AUDIENCE_MEMBER)
                        .where(AUDIENCE_MEMBER.MEMBER_ID.eq(currentUserId))
                        .fetch(AUDIENCE_MEMBER.ITEM_ID));

        List<CourseInfo> courseItems = courseInfoCommonDao.execute(context -> {
            // 构建基础查询条件
            List<Condition> conditions = buildConditions(searchContent, categoryId, publishClient, type, audienceItemIds, from);

            SelectSelectStep<Record> selectStep = context.select(Fields.start()
                    .add(COURSE_INFO.ID)
                    .add(COURSE_INFO.NAME)
                    .add(COURSE_INFO.TYPE)
                    .add(COURSE_INFO.CREATE_TIME)
                    .add(COURSE_INFO.RELEASE_TIME)
                    .add(COURSE_INFO.SOURCE)
                    .add(COURSE_INFO.STATUS)
                    .add(COURSE_INFO.COVER)
                    .add(COURSE_INFO.COVER_PATH)
                    .add(COURSE_INFO.BEGIN_DATE)
                    .add(COURSE_INFO.END_DATE)
                    .add(COURSE_INFO.VISITS)
                    .add(COURSE_INFO.AVG_SCORE)
                    .add(COURSE_INFO.URL)
                    .add(COURSE_INFO.INTEGRAL)
                    .end());

            // 动态关联课程目录表
            SelectJoinStep<Record> fromStep = applyCategoryJoin(categoryId, selectStep);
            SelectConditionStep<Record> query = fromStep.where(conditions);

            query = applyAdditionalFilters(query, context, topicId, companyType, currentUserId);

            SortField<?> sortField = getSortField(orderBy.orElse(COURSE_QUERY_DEFAULT_ORDER_BY),
                    order.orElse(COURSE_QUERY_DEFAULT_ORDER));
            SelectLimitStep<Record> queryStep = !enableSorting ? query.orderBy(sortField) : query;

            return queryStep.limit(firstResult, pageSize + 1).fetchInto(CourseInfo.class);
        });

        // 是否存在下一页
        int nextPage = (courseItems.size() > pageSize) ? 1 : 0;

        int count = 0;
        if (enablePageCount) {
            count = courseInfoCommonDao.execute(dsl -> {
                List<Condition> conditions = buildConditions(searchContent, categoryId, publishClient, type, audienceItemIds, from);

                SelectSelectStep<Record> countSelectStep = dsl.select(Fields.start().add(COURSE_INFO.ID.count()).end());
                SelectJoinStep<Record> fromStep = applyCategoryJoin(categoryId, countSelectStep);

                SelectConditionStep<Record> query = fromStep.where(conditions);
                query = applyAdditionalFilters(query, dsl, topicId, companyType, currentUserId);

                return query.fetchOne().getValue(0, Integer.class);
            });
        }
        // 封装返回数据
        if (nextPage == FAKE_PAGINATION) {
            courseItems.remove(courseItems.size() - FAKE_PAGINATION );
        }
        courseItems.forEach(cs ->{
            cs.setCoverPath(generateSecurePathCdn(cs.getCoverPath()));
        });
        // 封装返回数据
        Map<String, Object> resultMap = Maps.newHashMap();
        resultMap.put("items", courseItems);
        resultMap.put("recordCount", count);
        resultMap.put("more", nextPage);
        return resultMap;
    }


    /**
     * 根据categoryId入参情况动态关联目录表
     */
    private <R extends Record> SelectJoinStep<R> applyCategoryJoin(Optional<String> categoryId, SelectSelectStep<R> selectStep) {
        if (categoryId.isPresent()) {
            return selectStep.from(COURSE_INFO)
                    .leftJoin(COURSE_INFO_CATEGORY).on(COURSE_INFO_CATEGORY.COURSE_INFO_ID.eq(COURSE_INFO.ID))
                    .leftJoin(COURSE_CATEGORY).on(COURSE_CATEGORY.ID.eq(COURSE_INFO_CATEGORY.COURSE_CATEGORY_ID));
        } else {
            return selectStep.from(COURSE_INFO);
        }
    }

    /**
     * 构建通用查询条件
     */
    private List<Condition> buildConditions(Optional<String> searchContent, Optional<String> categoryId,
                                            Optional<Integer> publishClient, Integer type, List<String> audienceItemIds, Integer from) {
        List<Condition> conditions = Stream.of(
                        searchContent.map(content -> {
                            String trimmed = content.replace(" ", "");
                            return DSL.replace(COURSE_INFO.NAME, " ", "").contains(trimmed);
                        }),
                        categoryId.map(COURSE_CATEGORY.PATH::contains),
                        publishClient.map(p -> COURSE_INFO.PUBLISH_CLIENT.eq(p)
                                .or(COURSE_INFO.PUBLISH_CLIENT.eq(0)))
                ).filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());

        conditions.add(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull()));
        conditions.add(COURSE_INFO.VERSION_ID.isNotNull());
        conditions.add(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES).or(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_THE_TEST)));
        conditions.add(COURSE_INFO.IS_PUBLIC.isNull().or(COURSE_INFO.IS_PUBLIC.eq(CourseInfo.IS_PUBLIC_YES)));

        // 若提供categoryId，关联COURSE_CATEGORY时需添加状态条件
        if (categoryId.isPresent()) {
            conditions.add(COURSE_CATEGORY.STATE.eq(CourseCategory.STATE_ENABLE)
                    .or(COURSE_CATEGORY.STATE.isNull()));
        }

        // 如果当前学员是内部组织的，按照课程受众字段进行过滤
        if (Objects.equals(from, Member.FROM_INSIDE)) {
            conditions.add(
                    COURSE_INFO.BUSINESS_TYPE.eq(type)
                            .and(COURSE_INFO.OPEN.eq(CourseInfo.OPEN_2)
                            .or(DSL.exists(
                                    DSL.select(AUDIENCE_OBJECT.ID)
                                            .from(AUDIENCE_OBJECT)
                                            .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID)
                                                    .and(AUDIENCE_OBJECT.ITEM_ID.in(audienceItemIds)))
                                            .limit(1)
                            )))
            );
        } else {
            conditions.add(
                    COURSE_INFO.BUSINESS_TYPE.eq(type)
                            .andExists(
                                    DSL.select(AUDIENCE_OBJECT.ID)
                                            .from(AUDIENCE_OBJECT)
                                            .where(AUDIENCE_OBJECT.BUSINESS_ID.eq(COURSE_INFO.ID)
                                                    .and(AUDIENCE_OBJECT.ITEM_ID.in(audienceItemIds)))
                                            .limit(1)
                            )
            );
        }
        return conditions;
    }

    private SelectConditionStep<Record> applyAdditionalFilters(SelectConditionStep<Record> query, DSLContext dsl,
                                                               Optional<String> topicId, Optional<Integer> companyType, String currentUserId) {
        if (topicId.isPresent()) {
            query = query.andExists(
                    dsl.select(BUSINESS_TOPIC.ID)
                            .from(BUSINESS_TOPIC)
                            .where(BUSINESS_TOPIC.BUSINESS_ID.eq(COURSE_INFO.ID)
                                    .and(BUSINESS_TOPIC.TOPIC_ID.eq(topicId.get()))
                            )
            );
        }
        if (companyType.isPresent() && companyType.get() != 0) {
            query = applyCompanyFilter(query, currentUserId, companyType.get());
        }
        return query;
    }

    private SelectConditionStep<Record> applyCompanyFilter(SelectConditionStep<Record> query, String currentUserId, int companyType) {
        Optional<String> groupId = organizationService.getLever(currentUserId, 2);
        Optional<String> companyId = organizationService.getLever(currentUserId, 3);
        // 若当前公司不存在，则使用集团ID
        companyId = companyId.isPresent() ? companyId : groupId;
        List<String> organizationIds = Stream.of(groupId, companyId)
                .filter(Optional::isPresent)
                .map(Optional::get)
                .collect(Collectors.toList());
        if (companyType == 1) {
            query = query.and(COURSE_INFO.ORGANIZATION_ID.eq(groupId.orElse("")));
        } else if (companyType == 2) {
            query = query.and(COURSE_INFO.ORGANIZATION_ID.eq(companyId.orElse("")));
        } else if (companyType == 3 && !organizationIds.isEmpty()) {
            query = query.and(COURSE_INFO.ORGANIZATION_ID.notIn(organizationIds));
        }
        return query;
    }

    /**
     * 根据排序参数获取对应的排序字段
     */
    private SortField<?> getSortField(int orderBy, int order) {
        TableField<?, ?>[] sortFields = {COURSE_INFO.RELEASE_TIME, COURSE_INFO.VISITS, COURSE_INFO.AVG_SCORE};
        return order == 1 ? sortFields[orderBy].asc() : sortFields[orderBy].desc();
    }
}