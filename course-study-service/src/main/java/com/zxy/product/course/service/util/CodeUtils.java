package com.zxy.product.course.service.util;

import org.jooq.Condition;
import org.jooq.impl.DSL;
import org.springframework.util.CollectionUtils;

import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

import static com.zxy.product.system.jooq.Tables.ORGANIZATION;

/**
 * Created by <PERSON><PERSON><PERSON> on 17/6/26.
 */
public class CodeUtils {


    public static String getModuleCode(String prefix){
        String code = getRandomCode();
        if(code.indexOf("[")!=-1){
            code = code.replace("[","_");
        }
        if(code.contains("@")){
            code = code.replace("@","A");
        }
        return prefix + code;
    }

    public static String getRandomCode() {
        //Always use a SecureRandom generator
        SecureRandom sr = null;
        try {
            sr = SecureRandom.getInstance("SHA1PRNG");
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        //Create array for salt
        byte[] salt = new byte[16];
        //Get a random salt
        sr.nextBytes(salt);
        //return salt
        return salt.toString();
    }

    /**
     * 拼装组织条件
     *
     * @param grantOrganizationMap 组织Map
     * @param conditions return 组织条件
     */
    public static void generateOrganizationConditions(Map<String, Set<String>> grantOrganizationMap, List<Condition> conditions) {
        Set<String> organizationIdSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.NOT_INCLUDE_KEY);
        Set<String> pathSet = grantOrganizationMap.get(com.zxy.product.system.entity.Organization.INCLUDE_KEY);
        if (!CollectionUtils.isEmpty(pathSet) || !CollectionUtils.isEmpty(organizationIdSet)) {
            Condition condition;
            if (pathSet.isEmpty()) {
                condition = Optional.of(organizationIdSet).map(ORGANIZATION.ID::in).orElse(DSL.trueCondition());
            } else {
                condition = pathSet.stream().map(ORGANIZATION.PATH::startsWith).reduce(DSL::or)
                                   .orElse(DSL.trueCondition());
                if (!organizationIdSet.isEmpty()) {
                    condition = condition.or(ORGANIZATION.ID.in(organizationIdSet));
                }
            }
            conditions.add(condition);
        }
    }
}
