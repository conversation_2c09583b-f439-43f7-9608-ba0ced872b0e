package com.zxy.product.course.service.helper;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.dto.model.mentor.FileRuleDto;
import com.zxy.product.course.entity.CourseKnowledge;
import com.zxy.product.course.util.HttpClientUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.File;
import java.util.HashMap;
import java.util.Objects;
import java.util.Optional;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/14:13
 * @Description:
 */
@Component
public class CourseKnowledgeHelper {

    @Value("${course.model.url}")
    private String url;
    @Value("${course.model.dataset-id}")
    private String datasetId;
    @Value("${course.model.app-api-key}")
    private String appApiKey;
    @Value("${course.model.dataset-api-key}")
    private String datasetApiKey;
    @Value("${course.model.interface.create-file}")
    private String createFile;
    @Value("${course.model.interface.delete-file}")
    private String deleteFile;

    private static Logger logger = LoggerFactory.getLogger(CourseKnowledgeHelper.class);

    public void sendFile(File file, CourseKnowledge courseKnowledge, Optional<String> documemntId){
        FileRuleDto data = setRule();
        if(documemntId.isPresent()){
            data.setOriginal_document_id(documemntId.get());
        }
        HashMap<String, String> map = new HashMap();
//        map.put("file", file);
        HashMap<String, String> heard = new HashMap();
        heard.put("Authorization", datasetApiKey);
        String urls = url + String.format(createFile, datasetId);
        logger.info("params: urls = {}, file={}, data ={}", urls, file, data);
        String response = HttpClientUtil.uploadFile(urls,file, heard, JSON.toJSONString(data));
        JSONObject jsonObject = JSON.parseObject(response);
        JSONObject document = jsonObject.getJSONObject("document");
        Boolean enabled = document.getBoolean("enabled");
        if(Objects.nonNull(document) && Objects.nonNull(enabled) && enabled){
            courseKnowledge.setAttachmentUpId(document.getString("id"));
            courseKnowledge.setAttachmentBatch(document.getString("batch"));
            if(Objects.nonNull(document.getString("indexing_status")) && Objects.equals(document.getString("indexing_status"), CourseKnowledge.INDEXING_STATUS_COMPLETED)){
                courseKnowledge.setFinshStatus(CourseKnowledge.FEEDBACK_STATUS_PUBLISH);
            }
        } else {
            String message = jsonObject.getString("message");
            String code = jsonObject.getString("code");
            logger.warn("上传文件错误：错误code={}，message={}", code, message);
            throw new UnprocessableException(ErrorCode.uploadFileFail);
        }
    }


    private FileRuleDto setRule(){
        FileRuleDto data = new FileRuleDto();
        data.setDoc_form("text_model");
        data.setIndexing_technique("high_quality");
        return data;

    }

    public boolean deleteFile(String documemntId){
        HashMap<String, String> heard = new HashMap();
        heard.put("Authorization", datasetApiKey);
        logger.info("heard={}, documemntId ={}", heard,documemntId);
        return HttpClientUtil.httpDeleteCustomizeV2(url + String.format(deleteFile,datasetId, documemntId), heard);
    }

}
