package com.zxy.product.course.service.support;

import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.ApplicationConfigService;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.entity.ApplicationConfig;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import static com.zxy.product.course.jooq.Tables.APPLICATION_CONFIG;

/**
 * 学习助手应用管理
 */
@Service
public class ApplicationConfigServiceSupport implements ApplicationConfigService {

    private CommonDao<ApplicationConfig> applicationConfigDao;

    @Autowired
    public void setApplicationConfigDao(CommonDao<ApplicationConfig> applicationConfigDao) {
        this.applicationConfigDao = applicationConfigDao;
    }

    @Override
    public List<ApplicationConfig> find() {
        return applicationConfigDao.execute(d -> d.select(APPLICATION_CONFIG.fields())
                                                  .from(APPLICATION_CONFIG)
                                                  .orderBy(APPLICATION_CONFIG.ORDER)
                                                  .fetchInto(ApplicationConfig.class));
    }

    @Override
    public List<ApplicationConfig> findByIds(Integer status) {
        return applicationConfigDao.execute(d -> d.select(APPLICATION_CONFIG.ID, APPLICATION_CONFIG.NAME)
                .from(APPLICATION_CONFIG)
                .where(APPLICATION_CONFIG.STATUS.eq(status))
                .fetchInto(ApplicationConfig.class));
    }

    @Override
    public boolean insert(String name, Integer type, String uri, Optional<String> icon, Optional<Integer> orderOptional) {
        ApplicationConfig applicationConfig = new ApplicationConfig();
        applicationConfig.forInsert();
        applicationConfig.setName(name);
        applicationConfig.setType(type);
        applicationConfig.setUri(uri);
        applicationConfig.setInit(ApplicationConfig.INIT_NO);
        icon.ifPresent(applicationConfig::setIcon);
        Integer maxOrder = applicationConfigDao.execute(d -> d.select(DSL.max(APPLICATION_CONFIG.ORDER))
                                                              .from(APPLICATION_CONFIG))
                                               .fetchOne(DSL.max(APPLICATION_CONFIG.ORDER));
        Integer order = orderOptional.orElse(maxOrder + 1);
        applicationConfig.setOrder(order > maxOrder + 1 ? maxOrder + 1 : order);
        updateOrder(applicationConfig.getOrder(), maxOrder + 2, 1);
        applicationConfigDao.insert(applicationConfig);
        return true;
    }

    @Override
    public boolean update(String id, String name, Integer type, String uri, Optional<String> icon, Integer order) {
        ApplicationConfig applicationConfig = applicationConfigDao.get(id);
        applicationConfig.setName(name);
        applicationConfig.setType(type);
        applicationConfig.setUri(uri);
        applicationConfig.setIcon(icon.orElse(null));
        if (!Objects.equals(applicationConfig.getOrder(), order)) {
            if (applicationConfig.getOrder() > order) {
                updateOrder(order, applicationConfig.getOrder() + 1, 1);
            } else {
                updateOrder(applicationConfig.getOrder(), order, -1);
            }
            Integer maxOrder = applicationConfigDao.execute(d -> d.select(DSL.max(APPLICATION_CONFIG.ORDER))
                                                                  .from(APPLICATION_CONFIG))
                                                   .fetchOne(DSL.max(APPLICATION_CONFIG.ORDER));
            applicationConfig.setOrder(order > maxOrder ? maxOrder : order);
        }
        applicationConfigDao.update(applicationConfig);
        return true;
    }

    private void updateOrder(int leftOrder, int rightOrder, int addBase) {
        if (addBase > 0) {
            applicationConfigDao.execute(d -> d.update(APPLICATION_CONFIG).set(APPLICATION_CONFIG.ORDER, APPLICATION_CONFIG.ORDER.add(addBase))
                                          .where(APPLICATION_CONFIG.ORDER.ge(leftOrder), APPLICATION_CONFIG.ORDER.lt(rightOrder)).execute());
        } else {
            applicationConfigDao.execute(d -> d.update(APPLICATION_CONFIG).set(APPLICATION_CONFIG.ORDER, APPLICATION_CONFIG.ORDER.add(addBase))
                                          .where(APPLICATION_CONFIG.ORDER.gt(leftOrder), APPLICATION_CONFIG.ORDER.le(rightOrder)).execute());
        }
    }

    @Override
    public boolean delete(String id) {
        ApplicationConfig applicationConfig = applicationConfigDao.get(id);
        if (Objects.equals(applicationConfig.getInit(), ApplicationConfig.INIT_YES)) {
            return false;
        }
        applicationConfigDao.delete(id);
        applicationConfigDao.execute(d -> d.update(APPLICATION_CONFIG).set(APPLICATION_CONFIG.ORDER, APPLICATION_CONFIG.ORDER.minus(1))
                                           .where(APPLICATION_CONFIG.ORDER.gt(applicationConfig.getOrder())).execute());
        return true;
    }

    @Override
    public boolean updateStatus(String id, Integer status) {
        List<ApplicationConfig> list = this.find();
        // 最多可激活12个
        if (Objects.equals(status, ApplicationConfig.STATUS_ON)) {
            long onCount = list.stream().filter(s -> Objects.equals(s.getStatus(), ApplicationConfig.STATUS_ON)).count();
            if (onCount >= 12) {
                throw new UnprocessableException(ErrorCode.APPLICATION_CONFIG_ON_EXCEED_MAX);
            }
        }
        if (Objects.equals(status, ApplicationConfig.STATUS_OFF)) {
            long onCount = list.stream().filter(s -> Objects.equals(s.getStatus(), ApplicationConfig.STATUS_ON)).count();
            if (onCount <= 2) {
                throw new UnprocessableException(ErrorCode.APPLICATION_CONFIG_OFF_EXCEED_MAX);
            }
        }
        applicationConfigDao.execute(e -> e.update(APPLICATION_CONFIG)
                                           .set(APPLICATION_CONFIG.STATUS, status)
                                           .where(APPLICATION_CONFIG.ID.eq(id)).execute());
        return true;
    }
}