package com.zxy.product.course.service.support.remodeling;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.encrypt.SM4.SM4Utils;
import com.zxy.product.course.api.remodeling.CourseChapterQuestionnaireService;
import com.zxy.product.course.api.remodeling.OfflineCourseQuestionnaireChapterService;
import com.zxy.product.course.api.remodeling.OfflineCourseQuestionnaireService;
import com.zxy.product.course.entity.*;
import com.zxy.product.course.service.util.CopyList;
import org.jooq.*;
import org.jooq.impl.DSL;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.text.DecimalFormat;
import java.util.Comparator;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.*;
import static java.util.stream.Collectors.groupingBy;

@Service
public class OfflineCourseQuestionnaireServiceSupport implements OfflineCourseQuestionnaireService {
    public static final Logger logger = LoggerFactory.getLogger(OfflineCourseQuestionnaireServiceSupport.class);
    private CommonDao<OfflineCourseQuestionnaire> offlineCourseQuestionnaireDao;

    private OfflineCourseQuestionnaireChapterService offlineCourseQuestionnaireChapterService;
    private CourseChapterQuestionnaireService courseChapterQuestionnaireService;

    private CommonDao<OfflineCourseQuestionnaireChapter> offlineCourseQuestionnaireChapterDao;
    private CommonDao<OfflineQuestionnaireAnswer> offlineQuestionnaireAnswerDao;
    private CommonDao<QuestionnaireMouldQuestion> questionnaireMouldQuestionDao;
    private CommonDao<CourseQuestionnaireRecord> courseQuestionnaireRecordDao;


    @Autowired
    public void setCourseQuestionnaireRecordDao(CommonDao<CourseQuestionnaireRecord> courseQuestionnaireRecordDao) {
        this.courseQuestionnaireRecordDao = courseQuestionnaireRecordDao;
    }

    @Autowired
    public void setQuestionnaireMouldQuestionDao(CommonDao<QuestionnaireMouldQuestion> questionnaireMouldQuestionDao) {
        this.questionnaireMouldQuestionDao = questionnaireMouldQuestionDao;
    }

    @Autowired
    public void setOfflineQuestionnaireAnswerDao(CommonDao<OfflineQuestionnaireAnswer> offlineQuestionnaireAnswerDao) {
        this.offlineQuestionnaireAnswerDao = offlineQuestionnaireAnswerDao;
    }

    @Autowired
    public void setOfflineCourseQuestionnaireChapterDao(CommonDao<OfflineCourseQuestionnaireChapter> offlineCourseQuestionnaireChapterDao) {
        this.offlineCourseQuestionnaireChapterDao = offlineCourseQuestionnaireChapterDao;
    }

    @Autowired
    public void setOfflineCourseQuestionnaireDao(CommonDao<OfflineCourseQuestionnaire> offlineCourseQuestionnaireDao) {
        this.offlineCourseQuestionnaireDao = offlineCourseQuestionnaireDao;
    }

    @Autowired
    public void setOfflineCourseQuestionnaireChapterService(OfflineCourseQuestionnaireChapterService offlineCourseQuestionnaireChapterService) {
        this.offlineCourseQuestionnaireChapterService = offlineCourseQuestionnaireChapterService;
    }

    @Autowired
    public void setCourseChapterQuestionnaireService(CourseChapterQuestionnaireService courseChapterQuestionnaireService) {
        this.courseChapterQuestionnaireService = courseChapterQuestionnaireService;
    }



    @Override
    public PagedResult<OfflineCourseQuestionnaire> findPagedResult(Integer page, Integer pageSize,
                                                                   Optional<String> name, Optional<String> memberId,
                                                                   Map<String, Set<String>> grantOrganizationPathMap) {
        return offlineCourseQuestionnaireDao.execute(e -> {
            SelectSelectStep<Record> selectListField = e.select(
                    Fields.start().add(
                            OFFLINE_COURSE_QUESTIONNAIRE.ID,
                            OFFLINE_COURSE_QUESTIONNAIRE.NAME,
                            OFFLINE_COURSE_QUESTIONNAIRE.CREATE_TIME,
                            MEMBER.FULL_NAME,
                            ORGANIZATION.NAME
                    ).end()
            );
            List<Condition> conditions = Stream.of(
                    name.map(OFFLINE_COURSE_QUESTIONNAIRE.NAME::contains),
                    memberId.map(MEMBER.ID::eq)
            ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            generateOrganizationConditions(grantOrganizationPathMap,conditions);

            SelectSelectStep<Record> selectIdFiedld = e.selectDistinct(
                    Fields.start()
                            .add(OFFLINE_COURSE_QUESTIONNAIRE.ID)
                            .end()
            );

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                SelectConditionStep<Record> select = a.from(OFFLINE_COURSE_QUESTIONNAIRE)
                        .leftJoin(MEMBER).on(OFFLINE_COURSE_QUESTIONNAIRE.CREATE_MEMBER_ID.eq(MEMBER.ID))
                        .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                        .where(conditions);
                return select;
            };

            int count = stepFunc.apply(selectIdFiedld).fetch().size();

            SelectConditionStep<Record> listSetp = stepFunc.apply(selectListField);
            listSetp.orderBy(OFFLINE_COURSE_QUESTIONNAIRE.CREATE_TIME.desc());
            Result<Record> record = listSetp.limit((page - 1) * pageSize, pageSize).fetch();

            return PagedResult.create(count, record.stream().map(r -> {
                OfflineCourseQuestionnaire ofq = new OfflineCourseQuestionnaire();
                ofq.setId(r.getValue(OFFLINE_COURSE_QUESTIONNAIRE.ID));
                ofq.setName(r.getValue(OFFLINE_COURSE_QUESTIONNAIRE.NAME));
                ofq.setCreateTime(r.getValue(OFFLINE_COURSE_QUESTIONNAIRE.CREATE_TIME));
                Member m = new Member();
                m.setFullName(r.getValue(MEMBER.FULL_NAME));
                Organization o = new Organization();
                o.setName(r.getValue(ORGANIZATION.NAME));
                m.setOrganization(o);
                ofq.setMember(m);
                return ofq;
            }).collect(Collectors.toList()));
        });
    }

    @Override
    public OfflineCourseQuestionnaire insertOfflineCourseQuestionnaire(OfflineCourseQuestionnaire offlineCourseQuestionnaire) {
        return offlineCourseQuestionnaireDao.insert(offlineCourseQuestionnaire);
    }

    @Override
    public void changeExamName(String id, String name) {
        offlineCourseQuestionnaireDao.execute(e -> e.update(OFFLINE_COURSE_QUESTIONNAIRE)
                .set(OFFLINE_COURSE_QUESTIONNAIRE.NAME, name)
                .where(OFFLINE_COURSE_QUESTIONNAIRE.ID.eq(id)).execute());

    }


    @Override
    public void saveOfflineQuestionnaire(OfflineCourseQuestionnaire offlineCourseQuestionnaire,
                                         List<OfflineCourseQuestionnaireChapter> correctInsertList,
                                         CourseChapterQuestionnaire courseChapterQuestionnaire) {
        this.insertOfflineCourseQuestionnaire(offlineCourseQuestionnaire);
        offlineCourseQuestionnaireChapterService.insertOfflineCourseQuestionnaireChapters(correctInsertList);
        courseChapterQuestionnaireService.insertCourseChapterQuestionnaire(courseChapterQuestionnaire);
    }

    /**
     * 线下满意度问卷查询统计-总体统计情况and课程师资统计
     * @param questionnaireId
     * @return
     */
    @Override
    public OfflineCourseQuestionnaire getStatisticsPopulation(String questionnaireId) {
        OfflineCourseQuestionnaire offlineCourseQuestionnaire = offlineCourseQuestionnaireDao.get(questionnaireId);

        // 查询已提交的问卷记录
        List<CourseQuestionnaireRecord> records = courseQuestionnaireRecordDao.fetch(COURSE_QUESTIONNAIRE_RECORD.COURSE_QUESTIONARY_ID.eq(questionnaireId).and(COURSE_QUESTIONNAIRE_RECORD.STATUS.eq(CourseQuestionnaireRecord.STATUS_1)));
        // 参与人数
        int count = records.size();

        // 查询作答记录信息
        List<OfflineQuestionnaireAnswer> answerList = offlineQuestionnaireAnswerDao.fetch(OFFLINE_QUESTIONNAIRE_ANSWER.QUESTIONNAIRE_RECORD_ID.in(records.stream().map(CourseQuestionnaireRecord::getId).collect(Collectors.toList())));

        // 查询章节信息
        List<OfflineCourseQuestionnaireChapter> chapterList = offlineCourseQuestionnaireChapterDao.fetch(OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER.OFFLINE_COURSE_QUESTIONNAIRE_ID.eq(questionnaireId)).stream().sorted(Comparator.comparing(OfflineCourseQuestionnaireChapter::getSequence)).collect(Collectors.toList());

        // 查询线下满意度问题数据
        List<QuestionnaireMouldQuestion> questionnaireMouldQuestionList = findQuestionnaireMouldQuestionList(QuestionnaireMould.OFFLINE_MOULD_ID);

        // 需要循环的单选题
        List<QuestionnaireMouldQuestion> circleQuestionList = questionnaireMouldQuestionList.stream().filter(q -> QuestionnaireMouldQuestion.QUESTION_CIRCLE_FLAG_YES.equals(q.getCircleFlag())
                && QuestionnaireQuestion.QUESTION_TYPE_SINGLE_CHOICE.equals(q.getQuestionnaireQuestion().getType())).collect(Collectors.toList());
        // 不需要循环的单选题
        List<QuestionnaireMouldQuestion> otherQuestionList = questionnaireMouldQuestionList.stream().filter(q -> QuestionnaireMouldQuestion.QUESTION_CIRCLE_FLAG_NO.equals(q.getCircleFlag())
        && QuestionnaireQuestion.QUESTION_TYPE_SINGLE_CHOICE.equals(q.getQuestionnaireQuestion().getType())).collect(Collectors.toList());

        Map<Integer, List<QuestionnaireMouldQuestion>> groups = circleQuestionList.stream().collect(groupingBy(k -> k.getQuestionnaireQuestion().getGroup()));

        chapterList.forEach(offlineCourseQuestionnaireChapter -> {
            List<String> list = new ArrayList<>();
            groups.forEach((Integer k, List<QuestionnaireMouldQuestion> v) -> {
                for (QuestionnaireMouldQuestion questionnaireMouldQuestion : v) {
                    // 统计数据
                    List<OfflineQuestionnaireAnswer> answers = answerList.stream().filter(answer -> offlineCourseQuestionnaireChapter.getId().equals(answer.getQuestionnaireChapterId())
                            && questionnaireMouldQuestion.getQuestionId().equals(answer.getQuestionId())).collect(Collectors.toList());
                    Statistics statistics = statisticsData(answers, count);
                    list.add(statistics.getRowSatisfaction());
                    questionnaireMouldQuestion.setStatistics(statistics);
                }
                // 分组总计数据
                List<Statistics> statisticsList = v.stream().map(QuestionnaireMouldQuestion::getStatistics).collect(Collectors.toList());
                statisticsTotal(k,offlineCourseQuestionnaireChapter,statisticsList);
            });

            //深度copy
            List<QuestionnaireMouldQuestion> questionnaireMouldQuestionListForCourse= new ArrayList<>();
            List<QuestionnaireMouldQuestion> questionnaireMouldQuestionListForTeacher= new ArrayList<>();
            try {
                questionnaireMouldQuestionListForCourse = CopyList.deepCopy(groups.get(QuestionnaireQuestion.QUESTION_GROUP_COURSE_CONTENT));
                questionnaireMouldQuestionListForTeacher = CopyList.deepCopy(groups.get(QuestionnaireQuestion.QUESTION_GROUP_INSTRUCTOR_LEVEL));
            } catch (Exception e) {
                logger.error("统计查询-问卷id={}",questionnaireId);
            }

            offlineCourseQuestionnaireChapter.setQuestionnaireMouldQuestionListForCourse(questionnaireMouldQuestionListForCourse);
            offlineCourseQuestionnaireChapter.setQuestionnaireMouldQuestionListForTeacher(questionnaireMouldQuestionListForTeacher);

            // 章节总体满意度（所有分项满意度的平均值）
            double sum = list.stream().mapToDouble(s -> Double.parseDouble(s.replace("%", "")) * 0.01).sum();
            DecimalFormat df   = new DecimalFormat("######0.00");
            offlineCourseQuestionnaireChapter.setChapterStatisticsNum(df.format(sum/((float)(list.size()))*100 )+ "%");
        });
        offlineCourseQuestionnaire.setChapterList(chapterList);

        // 培训组织
        // 修改sort(前端根据sort当作序号...)
        for (int i=0; i<otherQuestionList.size(); i++) {
            QuestionnaireMouldQuestion questionnaireMouldQuestion = otherQuestionList.get(i);
            questionnaireMouldQuestion.setSort(i+1);
            List<OfflineQuestionnaireAnswer> answers = answerList.stream().filter(answer -> questionnaireId.equals(answer.getQuestionnaireChapterId())
                    && questionnaireMouldQuestion.getQuestionId().equals(answer.getQuestionId())).collect(Collectors.toList());
            Statistics statistics = statisticsData(answers, count);
            questionnaireMouldQuestion.setStatistics(statistics);
        }

        // 培训组织总计数据
        List<Statistics> statisticsList = otherQuestionList.stream().map(QuestionnaireMouldQuestion::getStatistics).collect(Collectors.toList());
        Statistics statistics = statisticsTotalData(statisticsList);
        offlineCourseQuestionnaire.setOtherStatistics(statistics);
        offlineCourseQuestionnaire.setOtherQuestionList(otherQuestionList);
        offlineCourseQuestionnaire.setCount(count);

        return offlineCourseQuestionnaire;
    }

    /**
     * 线下满意度问卷查询统计-总体主观意见统计情况
     * @param questionnaireId
     * @return
     */
    @Override
    public QuestionnaireMouldQuestion getStatisticsSubjectiveOpinion(String questionnaireId) {
        OfflineCourseQuestionnaire offlineCourseQuestionnaire = offlineCourseQuestionnaireDao.get(questionnaireId);
        // 查询线下满意度问题数据
        List<QuestionnaireMouldQuestion> questionnaireMouldQuestionList = findQuestionnaireMouldQuestionList(QuestionnaireMould.OFFLINE_MOULD_ID);
        // 查询总体主观意见题目
        List<QuestionnaireMouldQuestion> list = questionnaireMouldQuestionList.stream().filter(q -> QuestionnaireMouldQuestion.QUESTION_CIRCLE_FLAG_NO.equals(q.getCircleFlag())
                && QuestionnaireQuestion.QUESTION_TYPE_QUESTION.equals(q.getQuestionnaireQuestion().getType())).collect(Collectors.toList());
        QuestionnaireMouldQuestion question;
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }else {
            question = list.get(0);
        }
        // 查询作答记录信息
        List<OfflineQuestionnaireAnswer> answers = getSomeAnswers(questionnaireId, Optional.of(questionnaireId), Arrays.asList(question.getQuestionId()));
        question.setOfflineQuestionnaireAnswerList(answers);
        question.setOfflineCourseQuestionnaire(offlineCourseQuestionnaire);
        question.setSort(QuestionnaireMouldQuestion.QUESTION_SORT_4);
        return question;
    }

    private List<OfflineQuestionnaireAnswer> getSomeAnswers(String questionnaireId, Optional<String> chapterId, List<String> questionIds) {
        return offlineQuestionnaireAnswerDao.execute(e -> e.select(
                Fields.start()
                        .add(OFFLINE_QUESTIONNAIRE_ANSWER.ANSWER)
                        .add(OFFLINE_QUESTIONNAIRE_ANSWER.QUESTION_ID)
                        .add(OFFLINE_QUESTIONNAIRE_ANSWER.QUESTIONNAIRE_CHAPTER_ID)
                        .add(MEMBER.NAME)
                        .add(MEMBER.FULL_NAME)
                        .add(MEMBER.PHONE_NUMBER)
                        .add(ORGANIZATION.NAME)
                        .end())
                .from(OFFLINE_QUESTIONNAIRE_ANSWER)
                .leftJoin(MEMBER).on(MEMBER.ID.eq(OFFLINE_QUESTIONNAIRE_ANSWER.MEMBER_ID))
                .leftJoin(ORGANIZATION).on(ORGANIZATION.ID.eq(MEMBER.ORGANIZATION_ID))
                .where(OFFLINE_QUESTIONNAIRE_ANSWER.REFERENCE_ID.eq(questionnaireId))
                .and(chapterId.map(id -> OFFLINE_QUESTIONNAIRE_ANSWER.QUESTIONNAIRE_CHAPTER_ID.eq(id)).orElse(DSL.trueCondition()))
                .and(OFFLINE_QUESTIONNAIRE_ANSWER.QUESTION_ID.in(questionIds))
                .fetch().stream().map(r -> {
                    OfflineQuestionnaireAnswer answer = new OfflineQuestionnaireAnswer();
                    answer.setAnswer(r.getValue(OFFLINE_QUESTIONNAIRE_ANSWER.ANSWER));
                    answer.setQuestionId(r.getValue(OFFLINE_QUESTIONNAIRE_ANSWER.QUESTION_ID));
                    answer.setQuestionnaireChapterId(r.getValue(OFFLINE_QUESTIONNAIRE_ANSWER.QUESTIONNAIRE_CHAPTER_ID));
                    Member member = new Member();
                    member.setName(r.getValue(MEMBER.NAME));
                    member.setFullName(r.getValue(MEMBER.FULL_NAME));
                    member.setPhoneNumber(SM4Utils.decryptDataCBC(r.getValue(MEMBER.PHONE_NUMBER)));
                    Organization organization = new Organization();
                    organization.setName(r.getValue(ORGANIZATION.NAME));
                    member.setOrganization(organization);
                    answer.setMember(member);
                    return answer;
                }).filter(o-> StringUtils.isNotEmpty(o.getAnswer())).collect(Collectors.toList()));
    }

    /**
     * 线下满意度问卷查询统计-课程师资主观意见统计情况
     * @param questionnaireId
     */
    @Override
    public OfflineCourseQuestionnaire getStatisticsCourseOpinion(String questionnaireId) {
        OfflineCourseQuestionnaire offlineCourseQuestionnaire = offlineCourseQuestionnaireDao.get(questionnaireId);
        // 查询线下满意度问题数据
        List<QuestionnaireMouldQuestion> questionnaireMouldQuestionList = findQuestionnaireMouldQuestionList(QuestionnaireMould.OFFLINE_MOULD_ID);
        // 查询课程师资主观意见题目
        List<QuestionnaireMouldQuestion> mouldQuestionList = questionnaireMouldQuestionList.stream().filter(q -> QuestionnaireMouldQuestion.QUESTION_CIRCLE_FLAG_YES.equals(q.getCircleFlag())
                && QuestionnaireQuestion.QUESTION_TYPE_QUESTION.equals(q.getQuestionnaireQuestion().getType())).collect(Collectors.toList());

        List<String> questionIds = mouldQuestionList.stream().map(QuestionnaireMouldQuestion::getQuestionId).collect(Collectors.toList());

        // 查询课程师资主观意见题目作答记录信息
        List<OfflineQuestionnaireAnswer> answers = getSomeAnswers(questionnaireId, Optional.empty(), questionIds);

        // 查询章节信息
        List<OfflineCourseQuestionnaireChapter> chapterList = offlineCourseQuestionnaireChapterDao.fetch(OFFLINE_COURSE_QUESTIONNAIRE_CHAPTER.OFFLINE_COURSE_QUESTIONNAIRE_ID.eq(questionnaireId)).stream().sorted(Comparator.comparing(OfflineCourseQuestionnaireChapter::getSequence)).collect(Collectors.toList());;

        AtomicInteger index = new AtomicInteger();
        chapterList.forEach(offlineCourseQuestionnaireChapter -> {
            int rowIndex = 0;
            Integer size = 0;
            //深度copy
            List<QuestionnaireMouldQuestion> questionnaireMouldQuestions= new ArrayList<>();
            try {
                questionnaireMouldQuestions = CopyList.deepCopy(mouldQuestionList);
            } catch (Exception e) {
                logger.error("统计查询-问卷id={}",questionnaireId);
            }
            for (QuestionnaireMouldQuestion questionnaireMouldQuestion : questionnaireMouldQuestions) {
                List<OfflineQuestionnaireAnswer> answerList = answers.stream().filter(a -> offlineCourseQuestionnaireChapter.getId().equals(a.getQuestionnaireChapterId())
                        && questionnaireMouldQuestion.getQuestionId().equals(a.getQuestionId())).collect(Collectors.toList());
                questionnaireMouldQuestion.setOfflineQuestionnaireAnswerList(answerList);
                index.addAndGet(answerList.size());
                rowIndex = index.get();
                size+=answerList.size();
            }
            offlineCourseQuestionnaireChapter.setQuestionnaireMouldQuestionList(questionnaireMouldQuestions);
            offlineCourseQuestionnaireChapter.setRowIndex(rowIndex);
            offlineCourseQuestionnaireChapter.setSize(size);
        });
        offlineCourseQuestionnaire.setChapterList(chapterList);

        return offlineCourseQuestionnaire;
    }




    private void statisticsTotal(Integer k, OfflineCourseQuestionnaireChapter offlineCourseQuestionnaireChapter, List<Statistics> list) {
        Statistics statistics = statisticsTotalData(list);
        if (QuestionnaireQuestion.QUESTION_GROUP_COURSE_CONTENT.equals(k)) {
            // 课程内容总计
            offlineCourseQuestionnaireChapter.setCourseContentStatistics(statistics);
        } else if (QuestionnaireQuestion.QUESTION_GROUP_INSTRUCTOR_LEVEL.equals(k)) {
            // 讲师水平总计
            offlineCourseQuestionnaireChapter.setInstructorLevelStatistics(statistics);
        }
    }

    private Statistics statisticsTotalData(List<Statistics> list) {
        Statistics statistics = new Statistics();
        statistics.setAnswerFor1(list.stream().mapToInt(Statistics::getAnswerFor1).sum());
        statistics.setAnswerFor2(list.stream().mapToInt(Statistics::getAnswerFor2).sum());
        statistics.setAnswerFor3(list.stream().mapToInt(Statistics::getAnswerFor3).sum());
        statistics.setAnswerFor4(list.stream().mapToInt(Statistics::getAnswerFor4).sum());
        statistics.setAnswerFor5(list.stream().mapToInt(Statistics::getAnswerFor5).sum());
        statistics.setAnswerFor6(list.stream().mapToInt(Statistics::getAnswerFor6).sum());
        statistics.setAnswerFor7(list.stream().mapToInt(Statistics::getAnswerFor7).sum());
        statistics.setAnswerFor8(list.stream().mapToInt(Statistics::getAnswerFor8).sum());
        statistics.setAnswerFor9(list.stream().mapToInt(Statistics::getAnswerFor9).sum());
        statistics.setAnswerFor10(list.stream().mapToInt(Statistics::getAnswerFor10).sum());

        double sum = list.stream().filter(s->s.getRowSatisfaction()!=null).mapToDouble(s -> Double.parseDouble(s.getRowSatisfaction().replace("%", "")) * 0.01).sum();
        DecimalFormat df   = new DecimalFormat("######0.00");
        statistics.setRowSatisfaction(df.format((sum/(float)(list.size()))*100 )+ "%");
        return statistics;
    }


    private Statistics statisticsData(List<OfflineQuestionnaireAnswer> answers, Integer count) {
        Map<String, Long> map = answers.stream().collect(groupingBy(a -> a.getAnswer(), Collectors.counting()));
        Statistics statistics = new Statistics();
        statistics.setAnswerFor1(filterNull(map.get("1")));
        statistics.setAnswerFor2(filterNull(map.get("2")));
        statistics.setAnswerFor3(filterNull(map.get("3")));
        statistics.setAnswerFor4(filterNull(map.get("4")));
        statistics.setAnswerFor5(filterNull(map.get("5")));
        statistics.setAnswerFor6(filterNull(map.get("6")));
        statistics.setAnswerFor7(filterNull(map.get("7")));
        statistics.setAnswerFor8(filterNull(map.get("8")));
        statistics.setAnswerFor9(filterNull(map.get("9")));
        statistics.setAnswerFor10(filterNull(map.get("10")));
        statistics.setRowSatisfaction(getRowStatistics(statistics,count));
        return statistics;
    }


    /**
     * 计算分项满意度
     * @param statistics
     * @param count
     * @return
     */
    private String getRowStatistics(Statistics statistics, Integer count) {
        if (count==0) {
            return "0.00%";
        }
         Integer sum = statistics.getAnswerFor10()*10 + statistics.getAnswerFor9()*9 +
                statistics.getAnswerFor8()*8 + statistics.getAnswerFor7()*7 +
                statistics.getAnswerFor6()*6 + statistics.getAnswerFor5()*5 +
                statistics.getAnswerFor4()*4 + statistics.getAnswerFor3()*3 +
                statistics.getAnswerFor2()*2 + statistics.getAnswerFor1()*1;
        DecimalFormat df   = new DecimalFormat("######0.00");
        return df.format(((float)sum/(float)(count*10))*100 )+ "%";
    }

    private Integer filterNull(Long o) { return o==null?0:o.intValue(); }

    /**
     * 查询满意度问题数据
     * @return
     */
    private List<QuestionnaireMouldQuestion> findQuestionnaireMouldQuestionList (String mouldId) {
        return questionnaireMouldQuestionDao.execute(e -> {
            return e.selectDistinct(
                    Fields.start()
                            .add(QUESTIONNAIRE_MOULD_QUESTION)
                            .add(QUESTIONNAIRE_QUESTION)
                            .end())
                    .from(QUESTIONNAIRE_MOULD_QUESTION)
                    .leftJoin(QUESTIONNAIRE_QUESTION).on(QUESTIONNAIRE_MOULD_QUESTION.QUESTION_ID.eq(QUESTIONNAIRE_QUESTION.ID))
                    .where(QUESTIONNAIRE_MOULD_QUESTION.MOULD_ID.eq(mouldId))
                    .orderBy(QUESTIONNAIRE_MOULD_QUESTION.SORT)
                    .fetch().stream().map(r -> {
                        QuestionnaireMouldQuestion qmq = r.into(QuestionnaireMouldQuestion.class);
                        QuestionnaireQuestion qq = r.into(QuestionnaireQuestion.class);
                        qmq.setQuestionnaireQuestion(qq);
                        return qmq;
                    }).collect(Collectors.toList());
        });
    }

    /**
     * 拼装组织条件
     *
     * @param grantOrganizationMap 组织Map
     * @param conditions
     * return 组织条件
     */
    private void generateOrganizationConditions(Map<String, Set<String>> grantOrganizationMap, List<Condition> conditions) {
        Set<String> organizationIdSet = grantOrganizationMap.get(Organization.NOT_INCLUDE_KEY);
        Set<String> pathSet = grantOrganizationMap.get(Organization.INCLUDE_KEY);
        if (!CollectionUtils.isEmpty(pathSet) || !CollectionUtils.isEmpty(organizationIdSet)) {
            Condition condition;
            if (pathSet.isEmpty()) {
                condition = Optional.of(organizationIdSet).map(ORGANIZATION.ID::in).orElse(DSL.trueCondition());
            } else {
                condition = pathSet.stream().map(ORGANIZATION.PATH::startsWith).reduce(DSL::or)
                        .orElse(DSL.trueCondition());
                if (!organizationIdSet.isEmpty()) {
                    condition = condition.or(ORGANIZATION.ID.in(organizationIdSet));
                }
            }
            conditions.add(condition);
        }
    }



}
