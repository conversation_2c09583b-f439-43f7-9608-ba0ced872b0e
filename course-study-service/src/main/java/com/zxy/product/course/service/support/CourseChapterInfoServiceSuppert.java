package com.zxy.product.course.service.support;

import com.alibaba.fastjson.JSON;
import com.zxy.common.cache.redis.Redis;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.CourseChapterInfoService;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.GenseeWebCastService;
import com.zxy.product.course.api.KnowledgeService;
import com.zxy.product.course.content.DataSource;
import com.zxy.product.course.content.DataSourceEnum;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;

import com.zxy.product.course.jooq.tables.pojos.CourseChapterSectionEntity;
import org.jooq.Record1;
import org.jooq.impl.DSL;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.zxy.product.course.jooq.Tables.*;
import static com.zxy.product.system.content.CacheKeyConstant.CERTIFY_OUTSIDE_CERTIFICATION_COURSE_COUNT_REDIS_KEY;
import static com.zxy.product.system.content.CacheKeyConstant.CERTIFY_OUTSIDE_CERTIFICATION_REDIS_KEY;
import static java.util.Comparator.comparingInt;

@Service
public class CourseChapterInfoServiceSuppert implements CourseChapterInfoService {
    @Resource
    private CommonDao<CourseChapterSection> courseChapterSectionCommonDao;
    @Resource
    private KnowledgeService knowledgeService;
    @Resource
    private CourseInfoService courseInfoService;
    @Resource
    private GenseeWebCastService genseeWebCastService;
    @Resource
    private Redis redis;

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public Map<String, List<CourseChapterSection>> findBySubjectId(List<String> subjectIds) {
        return courseChapterSectionCommonDao.execute(dslContext -> {

            Map<String, List<CourseChapterSection>> containsSubSubjects = Optional.of(dslContext
                    .select(COURSE_INFO.ID, COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.SECTION_TYPE,COURSE_CHAPTER_SECTION.NAME,COURSE_CHAPTER_SECTION.SEQUENCE)
                    .from(COURSE_INFO)
                    .join(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID)))
                    .join(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                    .where(COURSE_INFO.ID.in(subjectIds)
                            .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                            .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_SUBJECT)))
                    .fetch(r -> {
                        CourseChapterSection courseChapterSection = new CourseChapterSection();
                        courseChapterSection.setCourseId(r.getValue(COURSE_INFO.ID));
                        courseChapterSection.setResourceId(r.getValue(COURSE_CHAPTER_SECTION.RESOURCE_ID));
                        courseChapterSection.setSectionType(r.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                        courseChapterSection.setName(r.getValue(COURSE_CHAPTER_SECTION.NAME));
                        courseChapterSection.setSequence(r.getValue(COURSE_CHAPTER_SECTION.SEQUENCE));
                        return courseChapterSection;
                    }).stream().sorted(comparingInt(CourseChapterSection::getSequence)).collect(Collectors.groupingBy(CourseChapterSectionEntity::getCourseId))).orElse(new HashMap<>());
            Set<String> subjectTypeIds = containsSubSubjects.keySet();
            ArrayList<String> countSubjectIds = new ArrayList<>(subjectTypeIds);


            subjectIds.removeAll(subjectTypeIds);

            Map<String, List<CourseChapterSection>> otherResource = dslContext
                    .select(COURSE_INFO.ID, COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.SECTION_TYPE,COURSE_CHAPTER_SECTION.SEQUENCE ,COURSE_CHAPTER_SECTION.TIME_MINUTE, COURSE_CHAPTER_SECTION.TIME_SECOND, COURSE_CHAPTER_SECTION.URL, COURSE_CHAPTER_SECTION.COVER_PATH,COURSE_CHAPTER_SECTION.NAME,COURSE_CHAPTER_SECTION.ID)
                    .from(COURSE_INFO)
                    .join(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID)))
                    .join(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                    .where(COURSE_INFO.ID.in(subjectIds)
                            .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES)))
                    .fetch(r -> {
                        CourseChapterSection courseChapterSection = new CourseChapterSection();
                        courseChapterSection.setCourseId(r.getValue(COURSE_INFO.ID));
                        courseChapterSection.setResourceId(r.getValue(COURSE_CHAPTER_SECTION.RESOURCE_ID));
                        courseChapterSection.setSectionType(r.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                        courseChapterSection.setTimeMinute(r.getValue(COURSE_CHAPTER_SECTION.TIME_MINUTE));
                        courseChapterSection.setTimeSecond(r.getValue(COURSE_CHAPTER_SECTION.TIME_SECOND));
                        courseChapterSection.setUrl(r.getValue(COURSE_CHAPTER_SECTION.URL));
                        courseChapterSection.setCoverPath(r.getValue(COURSE_CHAPTER_SECTION.COVER_PATH));
                        courseChapterSection.setName(r.getValue(COURSE_CHAPTER_SECTION.NAME));
                        courseChapterSection.setSequence(r.getValue(COURSE_CHAPTER_SECTION.SEQUENCE));
                        if (courseChapterSection.getSectionType().equals(CourseChapterSection.SECTION_TYPE_TASK)){
                            courseChapterSection.setResourceId(r.getValue(COURSE_CHAPTER_SECTION.ID));
                        }
                        return courseChapterSection;
                    }).stream().sorted(comparingInt(CourseChapterSection::getSequence)).collect(Collectors.groupingBy(CourseChapterSectionEntity::getCourseId));

            containsSubSubjects.putAll(otherResource);

            Collection<List<CourseChapterSection>> values = containsSubSubjects.values();
            Map<Integer, List<CourseChapterSection>> sectionTypeMap = values.stream()
                    .flatMap(List::stream)
                    .collect(Collectors.groupingBy(CourseChapterSection::getSectionType));
            // 查询知识
            List<String> knowledgeIds = sectionTypeMap.getOrDefault(CourseChapterSection.SECTION_TYPE_KNOWLEDGE, new ArrayList<>()).stream().map(CourseChapterSectionEntity::getResourceId).collect(Collectors.toList());
            Map<String, String> knowledgeMap = knowledgeService.findCoverByIds(knowledgeIds);
            // 查询课程
            List<CourseChapterSection> courseInfos = sectionTypeMap.getOrDefault(CourseChapterSection.SECTION_TYPE_SUBJECT, new ArrayList<>());
            courseInfos.addAll(sectionTypeMap.getOrDefault(CourseChapterSection.SECTION_TYPE_COURSE, new ArrayList<>()));
            List<String>  courseInfoIds = courseInfos.stream().map(CourseChapterSectionEntity::getResourceId).collect(Collectors.toList());
            Map<String, String> courseInfoMap = courseInfoService.getMapCoverPaths(courseInfoIds);
            //region 填充课程类型的数量
            Map<String, Integer> courseCount = dslContext
                    .select(COURSE_INFO.ID, DSL.count().as("count"))
                    .from(COURSE_INFO)
                    .join(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID)))
                    .join(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                    .where(COURSE_INFO.ID.in(countSubjectIds)
                            .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_COURSE)))
                    .groupBy(COURSE_CHAPTER_SECTION.COURSE_ID)
                    .fetchMap(COURSE_INFO.ID, DSL.count().as("count"));
            containsSubSubjects.forEach((k, v) -> v.forEach(v1 -> {
                if (v1.getSectionType().equals(CourseChapterSection.SECTION_TYPE_COURSE)) {
                    courseCount.compute(k, (k2, v2) -> (v2 == null) ? 1 : v2 + 1);
                }
            }));
            redis.process(redis -> redis.set(CERTIFY_OUTSIDE_CERTIFICATION_COURSE_COUNT_REDIS_KEY, JSON.toJSONString(courseCount)));
            //endregion

            // 查询直播
            List<String> genseeIds = sectionTypeMap.getOrDefault(CourseChapterSection.SECTION_TYPE_GENSEE, new ArrayList<>()).stream().map(CourseChapterSectionEntity::getResourceId).collect(Collectors.toList());
            Map<String, String> genseeInfoMap = genseeWebCastService.getMapCoverPaths(genseeIds);
            //  填充数据
            values.forEach(v -> v.forEach(v1 -> {
                if (Objects.isNull(v1.getCoverPath()) || v1.getCoverPath().isEmpty()){
                    v1.setCoverPath(genseeInfoMap.getOrDefault(v1.getResourceId(), courseInfoMap.getOrDefault(v1.getResourceId(), knowledgeMap.getOrDefault(v1.getResourceId(), ""))));
                }
            }));
            return containsSubSubjects;
        });
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public List<String> initHomeCertifyCacheBySubjectId(List<String> subjectIds) {
        return courseChapterSectionCommonDao.execute(dslContext -> {

            List<String> ids = Optional.ofNullable(dslContext
                    .select(COURSE_CHAPTER_SECTION.RESOURCE_ID)
                    .from(COURSE_INFO)
                    .join(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID)))
                    .join(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                    .where(COURSE_INFO.ID.in(subjectIds)
                            .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES))
                            .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(CourseChapterSection.SECTION_TYPE_SUBJECT)))
                    .fetch(Record1::value1)).orElse(new ArrayList<>());

            subjectIds.removeAll(ids);

            ids.addAll(Optional.ofNullable(dslContext
                    .select(COURSE_CHAPTER_SECTION.RESOURCE_ID)
                    .from(COURSE_INFO)
                    .join(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID)))
                    .join(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                    .where(COURSE_INFO.ID.in(subjectIds)
                            .and(COURSE_INFO.STATUS.eq(CourseInfo.STATUS_SHELVES)))
                    .fetch(Record1::value1)).orElse(new ArrayList<>()));
            String value = "1";
            Map<String, String> map = ids.stream().collect(Collectors.toMap(String::toString, v -> value, (v1, v2) -> v1));
            redis.process(r -> r.hmset(CERTIFY_OUTSIDE_CERTIFICATION_REDIS_KEY, map));
            return ids;
        });
    }

    @Override
    /*
     * 查询专题下，所有课程名
     */
    public List<CourseChapterSection> getCourseNames(String courseId) {
        return courseChapterSectionCommonDao.execute(r ->
                r.select(COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.SECTION_TYPE)
                        .from(COURSE_INFO)
                        .leftJoin(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID).and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID)))
                        .leftJoin(COURSE_CHAPTER_SECTION).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                        .where(COURSE_INFO.ID.eq(courseId))
                        .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.in(CourseChapterSection.SECTION_TYPE_COURSE, CourseChapterSection.SECTION_TYPE_SUBJECT))
                        .fetch(o -> {
                                    CourseChapterSection courseChapterSection = new CourseChapterSection();
                                    courseChapterSection.setResourceId(o.getValue(COURSE_CHAPTER_SECTION.RESOURCE_ID));
                                    courseChapterSection.setSectionType(o.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                                    return courseChapterSection;
                                }
                        ));
    }

    @Override
    public List<CourseChapterSection> getRequiredCourseByCourseId(List<String> courseIds) {

        return courseChapterSectionCommonDao.execute(r ->
                                                             r.select(COURSE_CHAPTER_SECTION.ID,
                                                                      COURSE_CHAPTER_SECTION.RESOURCE_ID,
                                                                      COURSE_CHAPTER_SECTION.SECTION_TYPE,
                                                                      COURSE_CHAPTER_SECTION.TIME_SECOND)
                                                              .from(COURSE_INFO)
                                                              .leftJoin(COURSE_CHAPTER).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID)
                                                                                                         .and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID)))
                                                              .leftJoin(COURSE_CHAPTER_SECTION)
                                                              .on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                                                              .where(COURSE_INFO.ID.in(courseIds))
                                                              .and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED))
                                                              .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.in(CourseChapterSection.SECTION_TYPE_COURSE,
                                                                                                          CourseChapterSection.SECTION_TYPE_SUBJECT))
                                                              .fetch(o -> {
                                                                         CourseChapterSection courseChapterSection = new CourseChapterSection();
                                                                         courseChapterSection.setResourceId(o.getValue(COURSE_CHAPTER_SECTION.RESOURCE_ID));
                                                                         courseChapterSection.setSectionType(o.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                                                                         courseChapterSection.setTimeSecond(o.getValue(COURSE_CHAPTER_SECTION.TIME_SECOND));
                                                                         courseChapterSection.setId(o.getValue(COURSE_CHAPTER_SECTION.ID));
                                                                         return courseChapterSection;
                                                                     }
                                                              ));

    }
}
