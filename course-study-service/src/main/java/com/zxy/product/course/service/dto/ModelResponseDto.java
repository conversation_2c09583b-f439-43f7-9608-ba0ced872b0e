package com.zxy.product.course.service.dto;

/**
 * 大模型响应DTO
 */
public class ModelResponseDto {
    
    /**
     * 状态码：成功-0
     */
    private String code;
    
    /**
     * 返回信息：操作成功
     */
    private String msg;
    
    /**
     * 每次请求的唯一标识
     */
    private String msgId;
    
    /**
     * 应用返回内容
     */
    private String answer;
    
    public ModelResponseDto() {
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getMsg() {
        return msg;
    }
    
    public void setMsg(String msg) {
        this.msg = msg;
    }
    
    public String getMsgId() {
        return msgId;
    }
    
    public void setMsgId(String msgId) {
        this.msgId = msgId;
    }
    
    public String getAnswer() {
        return answer;
    }
    
    public void setAnswer(String answer) {
        this.answer = answer;
    }
    
    @Override
    public String toString() {
        return "ModelResponseDto{" +
                "code='" + code + '\'' +
                ", msg='" + msg + '\'' +
                ", msgId='" + msgId + '\'' +
                ", answer='" + answer + '\'' +
                '}';
    }
}
