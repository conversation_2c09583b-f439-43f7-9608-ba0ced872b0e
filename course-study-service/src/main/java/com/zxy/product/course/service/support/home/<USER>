package com.zxy.product.course.service.support.home;


import com.alibaba.fastjson.JSON;
import com.google.common.base.Strings;
import com.zxy.product.course.api.home.CoursePolymerService;
import com.zxy.product.course.vo.*;
import com.zxy.product.course.vo.extend.ExtendVO;
import org.jooq.DSLContext;
import org.jooq.Record;
import org.jooq.SelectField;
import org.jooq.SelectSelectStep;
import org.jooq.tools.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

import static com.zxy.product.course.content.ExtendExtEnum.All;
import static com.zxy.product.course.content.ExtendIndexEnum.TextOnly;
import static com.zxy.product.course.jooq.Tables.*;
import static com.zxy.product.course.util.DatasetProcessing.*;
import static org.jooq.impl.DSL.substring;

/**
 * 十万——首页学习模块聚合Service实现类
 * <AUTHOR>
 * @date 2024年11月08日 16:14
 */
@Service
public class CoursePolymerSupport implements CoursePolymerService {
    private static final Logger logger= LoggerFactory.getLogger(CoursePolymerSupport.class);

    /**扩展简介默认截断字符串长度*/
    private static final Integer DEFAULT_TRUNCATED=10;
    private DSLContext context;

    @Autowired
    public void setContext(DSLContext context){ this.context=context; }

    /**
     * 课程|专题的find-by-ids接口数据
     * @param transmitMap 课程|专题 集合数据
     * @param clientType 客户端类型 1App 2PC
     * @return 课程|专题的find-by-ids接口数据
     */
    @Override
    public List<? extends CustomizeParentVO> doCustomizeCourse(Map<String, SpecialVO> transmitMap, Integer clientType){
        Collection<SelectField<?>> select = doSelect(COURSE_INFO.COVER_PATH, COURSE_INFO.VISITS, COURSE_INFO.ID, COURSE_INFO.CONSTRUCTION_TYPE, ORGANIZATION.NAME);
        List<CustomizeParentVO> parentCollect = context.select(select)
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(COURSE_INFO.ID.in(transmitMap.keySet()))
                .fetch(ew1 -> this.doBuildConvertCourse(clientType,ew1,transmitMap));
        logger.info("查询完成，查询的课程|专题数据集合体为{}", JSON.toJSONString(parentCollect));
        return parentCollect;
    }

    /**
     * 执行构建转化自定义模块返回值（课程|专题）
     * @param clientType 客户端类型 1PC 2App
     * @param record JOOQ记录流
     * @return 自定义查询数据
     */
    private CustomizeParentVO doBuildConvertCourse(Integer clientType, Record record,Map<String,SpecialVO> transmitMap){
        String id = record.getValue(COURSE_INFO.ID);
        return Optional.ofNullable(clientType)
                .filter(ew1 -> Objects.equals(1, ew1))
                .map(ew2 -> {
                    CustomizePcVO customizePcVO = new CustomizePcVO();
                    customizePcVO.setId(id);
                    String path = this.doSelectCoursePath(id, transmitMap, record);
                    customizePcVO.setImagePath(path);
                    customizePcVO.setBrowseCount(record.getValue(COURSE_INFO.VISITS));
                    customizePcVO.setConstructionType(record.getValue(COURSE_INFO.CONSTRUCTION_TYPE));
                    customizePcVO.setOrganizationName(record.getValue(ORGANIZATION.NAME));
                    return (CustomizeParentVO) customizePcVO;
                }).orElseGet(() -> {
                    CustomizeAppVO customizeAppVO = new CustomizeAppVO();
                    customizeAppVO.setId(id);
                    String path = this.doSelectCoursePath(id, transmitMap, record);
                    customizeAppVO.setCoverPath(path);
                    return customizeAppVO;
                });
    }

    /**
     * 查询自定义模块（课程|专题）的图片地址
     * @param id 资源主键
     * @param transmitMap 首页自定义模块配置的数据Map
     * @param record JOOQ的记录流
     * @return 资源图片地址
     */
    private String doSelectCoursePath(String id, Map<String,SpecialVO> transmitMap, Record record){
        SpecialVO specialVO = transmitMap.get(id);
        return Optional.ofNullable(specialVO)
                .filter(ew1->!Strings.isNullOrEmpty(ew1.getImgPath()))
                .map(SpecialVO::getImgPath)
                .orElseGet(()->record.getValue(COURSE_INFO.COVER_PATH));
    }

    /**
     * 直播的find-by-ids接口数据
     * @param map  直播集合数据
     * @param clientType 客户端类型 1App 2PC
     * @return 直播的find-by-ids接口数据
     */
    @Override
    public List<? extends CustomizeParentVO> doCustomizeBroadcast(Map<String, SpecialVO> map, Integer clientType) {
        Collection<SelectField<?>> select = doSelect(GENSEE_WEB_CAST.COVER_PATH, GENSEE_WEB_CAST.VIEW_NUMBER, GENSEE_WEB_CAST.ID);
        List<CustomizeParentVO> parentCollect = context.select(select)
                .from(GENSEE_WEB_CAST)
                .where(GENSEE_WEB_CAST.ID.in(map.keySet()))
                .fetch(ew1 -> this.doBuildConvertGensee(clientType, ew1,map));
        logger.info("查询完成，查询的班级数据集合体为{}", JSON.toJSONString(parentCollect));
        return parentCollect;
    }

    /**
     * 执行构建转化自定义返回值（直播）
     * @param clientType 客户端类型 1PC 2App
     * @param record JOOQ记录流
     * @return 自定义查询数据
     */
    private CustomizeParentVO doBuildConvertGensee(Integer clientType, Record record,Map<String, SpecialVO> transmitMap){
        String id = record.getValue(GENSEE_WEB_CAST.ID);
        return Optional.ofNullable(clientType)
                .filter(ew1 -> Objects.equals(1, ew1))
                .map(ew2 -> {
                    CustomizePcVO customizePcVO = new CustomizePcVO();
                    customizePcVO.setId(id);
                    String path = this.doSelectBroadcastPath(id, transmitMap, record);
                    customizePcVO.setImagePath(path);
                    customizePcVO.setBrowseCount(record.getValue(GENSEE_WEB_CAST.VIEW_NUMBER));
                    return (CustomizeParentVO) customizePcVO;
                }).orElseGet(() -> {
                    CustomizeAppVO customizeAppVO = new CustomizeAppVO();
                    customizeAppVO.setId(id);
                    String path = this.doSelectBroadcastPath(id, transmitMap, record);
                    customizeAppVO.setCoverPath(path);
                    return customizeAppVO;
                });
    }

    /**
     * 查询自定义模块（直播）的图片地址
     * @param id 资源Id
     * @param transmitMap 首页自定义模块传输的数据Map
     * @param record JOOQ记录流
     * @return 直播图片地址
     */
    private String doSelectBroadcastPath(String id, Map<String,SpecialVO> transmitMap, Record record){
        SpecialVO specialVO = transmitMap.get(id);
        return Optional.ofNullable(specialVO)
                .filter(ew1->!Strings.isNullOrEmpty(ew1.getImgPath()))
                .map(SpecialVO::getImgPath)
                .orElseGet(()->record.getValue(GENSEE_WEB_CAST.COVER_PATH));
    }

    /**
     * 扩展查询资源Ext
     * @param selectType 查询类型 1全部字数简介 2固定字数
     * @param selectIndex 查询索引 1纯文字 2富文本 3PC简介 4App简介
     * @param sizeOpt 若selectType为2时，此字段必填，若无，默认10
     * @param customizeIdCollect 需要查询的自定义资源属性集合
     * @return 填充资源Ext（简介）
     */
    @Override
    public Map<String, String> extendCourseExt(Integer selectType, Integer selectIndex,Optional<Integer> sizeOpt, List<String> customizeIdCollect) {
        Integer size = sizeOpt.orElse(DEFAULT_TRUNCATED);
        Collection<SelectField<?>> select = this.selectCourseIndex(selectType, selectIndex, size);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        List<ExtendVO> extendCollect = doSingleRecord(COURSE_INFO, selectStep)
                .where(COURSE_INFO.ID.in(customizeIdCollect))
                .fetch(ew1->{
                    ExtendVO extendVO = new ExtendVO();
                    extendVO.setId(ew1.getValue(COURSE_INFO.ID));
                    String subExt = ew1.get("subExt", String.class);
                    extendVO.setExt(Strings.isNullOrEmpty(subExt)? StringUtils.EMPTY:subExt);
                    return extendVO;
                });
        return extendCollect.parallelStream().collect(Collectors.toMap(ExtendVO::getId, ExtendVO::getExt));
    }

    /**
     * 课程查询索引且封装JOOQ查询属性集合
     * @param selectType 查询类型 1全部  2截取部分字段
     * @param selectIndex 查询索引 1纯文字 2富文本 3PC简介 4App简介
     * @param size 截取长度
     * @return JOOQ查询属性集合
     */
    private Collection<SelectField<?>> selectCourseIndex(Integer selectType, Integer selectIndex, Integer size){
        Map<Integer, Supplier<Collection<SelectField<?>>>> indexMap = new HashMap<>(2);
        indexMap.put(TextOnly.getCode(), () -> Optional.ofNullable(selectType)
                .filter(ew1 -> Objects.equals(All.getCode(), ew1))
                .map(ew2 -> doSelect(COURSE_INFO.DESCRIPTION_TEXT.as("subExt"), COURSE_INFO.ID))
                .orElseGet(() -> doSelect(substring(COURSE_INFO.DESCRIPTION_TEXT, 1, size).as("subExt"), COURSE_INFO.ID))
        );
        return indexMap.get(selectIndex).get();
    }


    /**
     * 扩展查询资源Ext（直播）
     * @param selectType 查询类型 1全部字数简介 2固定字数
     * @param sizeOpt 若selectType为2时，此字段必填，若无，默认10
     * @param customizeIdCollect 需要查询的自定义资源属性集合
     * @return 填充资源Ext（简介）
     */
    @Override
    public Map<String, String> extendBroadcastExt(Integer selectType,Integer selectIndex,Optional<Integer> sizeOpt, List<String> customizeIdCollect) {
        Integer size = sizeOpt.orElse(DEFAULT_TRUNCATED);
        Collection<SelectField<?>> select = this.selectBroadcastIndex(selectType, selectIndex, size);
        SelectSelectStep<Record> selectStep = doSelectStep(1, context, select);
        List<ExtendVO> extendCollect = doSingleRecord(GENSEE_WEB_CAST, selectStep)
                .where(GENSEE_WEB_CAST.ID.in(customizeIdCollect))
                .fetch(ew1 -> {
                    ExtendVO extendVO = new ExtendVO();
                    extendVO.setId(ew1.getValue(GENSEE_WEB_CAST.ID));
                    String subExt = ew1.get("subExt", String.class);
                    extendVO.setExt(Strings.isNullOrEmpty(subExt)? StringUtils.EMPTY:subExt);
                    return extendVO;
                });
        return extendCollect.parallelStream().collect(Collectors.toMap(ExtendVO::getId, ExtendVO::getExt));
    }

    @Override
    public List<ClassifiedCourseVo> doClassifiedCoursesCourse(List<String> ids) {
        Collection<SelectField<?>> select = doSelect(COURSE_INFO.ID, COURSE_INFO.VISITS, COURSE_INFO.CONSTRUCTION_TYPE, ORGANIZATION.NAME);
        return context.select(select)
                .from(COURSE_INFO)
                .leftJoin(ORGANIZATION).on(COURSE_INFO.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(COURSE_INFO.ID.in(ids))
                .fetch(ew1 -> {
                    ClassifiedCourseVo classifiedCourseVo = new ClassifiedCourseVo();
                    classifiedCourseVo.setId(ew1.getValue(COURSE_INFO.ID));
                    classifiedCourseVo.setVisits(ew1.getValue(COURSE_INFO.VISITS));
                    classifiedCourseVo.setConstructionType(ew1.getValue(COURSE_INFO.CONSTRUCTION_TYPE));
                    classifiedCourseVo.setOrganizationName(ew1.getValue(ORGANIZATION.NAME));
                    return classifiedCourseVo;
                });
    }

    /**
     * 直播查询索引且封装JOOQ查询属性集合
     * @param selectType 查询类型 1全部  2截取部分字段
     * @param selectIndex 查询索引 1纯文字 2富文本 3PC简介 4App简介
     * @param size 截取长度
     * @return JOOQ查询属性集合
     */
    private Collection<SelectField<?>>selectBroadcastIndex(Integer selectType, Integer selectIndex, Integer size){
        Map<Integer, Supplier<Collection<SelectField<?>>>> indexMap = new HashMap<>(2);
        indexMap.put(TextOnly.getCode(), () -> Optional.ofNullable(selectType)
                .filter(ew1 -> Objects.equals(All.getCode(), ew1))
                .map(ew2 -> doSelect(GENSEE_WEB_CAST.ID, GENSEE_WEB_CAST.GENSEE_DESC_TEXT.as("subExt")))
                .orElseGet(() -> doSelect(GENSEE_WEB_CAST.ID, substring(GENSEE_WEB_CAST.GENSEE_DESC_TEXT, 1, size).as("subExt")))
        );
        return indexMap.get(selectIndex).get();
    }
}