package com.zxy.product.course.service.support.ai;

import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.product.course.api.course.CourseQuestionCommendService;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.entity.CourseQuestionRecommend;
import org.jooq.Condition;
import org.jooq.Record;
import org.jooq.SelectJoinStep;
import org.jooq.impl.DSL;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.tables.CourseQuestionRecommend.COURSE_QUESTION_RECOMMEND;
import static com.zxy.product.system.jooq.Tables.ORGANIZATION;

/**
 * Created with IntelliJ IDEA.
 *
 * @Author: xxh
 * @Date: 2025/05/27/18:02
 * @Description:
 */
@Service
public class CourseQuestionCommendServiceSupport implements CourseQuestionCommendService {

    private static Integer LIMIT = 10;
    private static Integer FOUR = 4;
    private CommonDao<CourseQuestionRecommend> courseQuestionRecommendCommonDao;

    @Autowired
    public void setCourseQuestionRecommendCommonDao(CommonDao<CourseQuestionRecommend> courseQuestionRecommendCommonDao) {
        this.courseQuestionRecommendCommonDao = courseQuestionRecommendCommonDao;
    }



    @Override
    public void insert(CourseQuestionRecommend recommend){
        courseQuestionRecommendCommonDao.insert(recommend);
        orderSort(recommend);
    }

    //重新排序
    private void orderSort(CourseQuestionRecommend recommend){
        updateRecommend(recommend.getId(), recommend.getRecommend());
    }

    private List<CourseQuestionRecommend> findList(Integer page, Integer pageSize){
       return courseQuestionRecommendCommonDao.execute(e->e.select(COURSE_QUESTION_RECOMMEND.fields())
                .from(COURSE_QUESTION_RECOMMEND)
                .where(COURSE_QUESTION_RECOMMEND.RECOMMEND.eq(CourseQuestionRecommend.RECOMMEND_YES))
                .orderBy(COURSE_QUESTION_RECOMMEND.SORT.asc(),COURSE_QUESTION_RECOMMEND.MODIFY_DATE.desc())
                .limit((page-1)*pageSize, pageSize)
                .fetchInto(CourseQuestionRecommend.class)
        );
    }


    @Override
    public void update(CourseQuestionRecommend recommend){
        courseQuestionRecommendCommonDao.update(recommend);
    }


    private List<String> findSort(Integer order, Optional<Integer> maxOrder){
        return maxOrder.map(max -> {
            return courseQuestionRecommendCommonDao.execute(d -> d.select(COURSE_QUESTION_RECOMMEND.ID)
                    .from(COURSE_QUESTION_RECOMMEND).where(COURSE_QUESTION_RECOMMEND.RECOMMEND.eq(CourseQuestionRecommend.RECOMMEND_YES)
                            .and(COURSE_QUESTION_RECOMMEND.SORT.between(order, max))).fetch(ORGANIZATION.ID));
        }).orElseGet(() -> {
            return courseQuestionRecommendCommonDao.execute(d -> d.select(COURSE_QUESTION_RECOMMEND.ID).from(COURSE_QUESTION_RECOMMEND).where(COURSE_QUESTION_RECOMMEND.RECOMMEND.eq(CourseQuestionRecommend.RECOMMEND_YES).and(COURSE_QUESTION_RECOMMEND.SORT.ge(order))).fetch(COURSE_QUESTION_RECOMMEND.ID));
        });
    }

    @Override
    public CourseQuestionRecommend get(String id){
        return courseQuestionRecommendCommonDao.get(id);
    }
    /**
     * 更新组织顺序
     */
    private void updateOrgOrder(List<String> recommendIds, int addBase) {
        if (!recommendIds.isEmpty() && !Objects.equals(addBase, 0)) {
            if (addBase > 0) {
                courseQuestionRecommendCommonDao.execute(d -> d.update(COURSE_QUESTION_RECOMMEND).set(COURSE_QUESTION_RECOMMEND.SORT, COURSE_QUESTION_RECOMMEND.SORT.add(addBase)).where(COURSE_QUESTION_RECOMMEND.ID.in(recommendIds)).execute());
            } else {
                courseQuestionRecommendCommonDao.execute(d -> d.update(COURSE_QUESTION_RECOMMEND).set(COURSE_QUESTION_RECOMMEND.SORT, COURSE_QUESTION_RECOMMEND.SORT.add(addBase)).where(COURSE_QUESTION_RECOMMEND.ID.in(recommendIds), COURSE_QUESTION_RECOMMEND.SORT.gt(0)).execute());
            }
        }
    }

    /**
     * 获取组织顺序
     */
    private Integer getOrgOrder(Optional<Integer> order) {
        // 获取最大order
        Integer maxOrder = courseQuestionRecommendCommonDao.execute(d -> {
            return d.select(DSL.max(COURSE_QUESTION_RECOMMEND.SORT)).from(COURSE_QUESTION_RECOMMEND).where(COURSE_QUESTION_RECOMMEND.RECOMMEND.eq(CourseQuestionRecommend.RECOMMEND_YES)).fetchOne(DSL.max(COURSE_QUESTION_RECOMMEND.SORT));
        });
        if (maxOrder == null) {
            return 1;
        } else {
            return order.map(o -> {
                    return o > maxOrder ? maxOrder : o;
            }).orElseGet(() -> maxOrder + 1);
        }
    }

    @Override
    public void sort(String id, Integer order){
        CourseQuestionRecommend o = get(id);
        Integer older = o.getSort();
        o.setSort(getOrgOrder(Optional.of(order)));
        courseQuestionRecommendCommonDao.update(o);
        int addBase = Integer.compare(older, o.getSort());
        if (!Objects.equals(addBase,0)) {
            int max = older - o.getSort() > 0 ? older : o.getSort();
            int min = older - o.getSort() < 0 ? older : o.getSort();
            List<String> recommendIds = findSort(min, Optional.of(max));
            recommendIds.remove(id);
            // 更新被影响的组织顺序
            updateOrgOrder(recommendIds, addBase);
        }
    }

    private Optional<Integer> findMaxSort(){
       return courseQuestionRecommendCommonDao.execute(d -> d.select(COURSE_QUESTION_RECOMMEND.SORT.max()).
               from(COURSE_QUESTION_RECOMMEND).where(COURSE_QUESTION_RECOMMEND.RECOMMEND.eq(CourseQuestionRecommend.RECOMMEND_YES))
               .fetchOptional(COURSE_QUESTION_RECOMMEND.SORT.max()));

    }

    @Override
    public void updateRecommend(String id, Integer recommend){
        if(Objects.equals(recommend, CourseQuestionRecommend.RECOMMEND_YES)){
            //获取推荐数据的最大值
            Integer maxSort = findMaxSort().orElse(0);
            if(maxSort < 5){
                courseQuestionRecommendCommonDao.execute(e->e.update(COURSE_QUESTION_RECOMMEND)
                        .set(COURSE_QUESTION_RECOMMEND.RECOMMEND,recommend)
                        .set(COURSE_QUESTION_RECOMMEND.SORT, maxSort +1)
                        .where(COURSE_QUESTION_RECOMMEND.ID.eq(id))
                .execute());
            } else if(Objects.equals(maxSort, 5)){
                //第5个改为非推荐
                courseQuestionRecommendCommonDao.execute(e->e.update(COURSE_QUESTION_RECOMMEND).set(COURSE_QUESTION_RECOMMEND.SORT,6)
                        .set(COURSE_QUESTION_RECOMMEND.RECOMMEND,CourseQuestionRecommend.RECOMMEND_NO)
                        .where(COURSE_QUESTION_RECOMMEND.SORT.eq(maxSort)).execute());
                //顶替原来的第5个排序
                courseQuestionRecommendCommonDao.execute(e->e.update(COURSE_QUESTION_RECOMMEND).set(COURSE_QUESTION_RECOMMEND.SORT,5)
                        .set(COURSE_QUESTION_RECOMMEND.RECOMMEND,recommend)
                        .where(COURSE_QUESTION_RECOMMEND.ID.eq(id)).execute());

            }
            return;
        }
        //点非推荐，需要重新排序
        sort(id, CommonConstant.SIX);
        courseQuestionRecommendCommonDao.execute(e->e.update(COURSE_QUESTION_RECOMMEND).set(COURSE_QUESTION_RECOMMEND.RECOMMEND,recommend)
                .set(COURSE_QUESTION_RECOMMEND.SORT, CommonConstant.NINE)
                .where(COURSE_QUESTION_RECOMMEND.ID.eq(id)).execute());
    }



    @Override
    public PagedResult<CourseQuestionRecommend> findPage(Integer page, Integer pageSize, Optional<String> question, Optional<Integer> recommend){
        List<Condition> conditions = Stream.of(question.map(COURSE_QUESTION_RECOMMEND.QUESTION::contains),
                recommend.map(COURSE_QUESTION_RECOMMEND.RECOMMEND::eq)
        ).filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

        return courseQuestionRecommendCommonDao.fetchPage(page,pageSize,e->{
            SelectJoinStep<Record> step =
                    e.select(Fields.start().add(COURSE_QUESTION_RECOMMEND.ID,COURSE_QUESTION_RECOMMEND.QUESTION,COURSE_QUESTION_RECOMMEND.ANSWER,COURSE_QUESTION_RECOMMEND.RECOMMEND,
                                    COURSE_QUESTION_RECOMMEND.CREATE_TIME,COURSE_QUESTION_RECOMMEND.SORT,COURSE_QUESTION_RECOMMEND.STATUS,COURSE_QUESTION_RECOMMEND.MODIFY_DATE,COURSE_QUESTION_RECOMMEND.ANSWER_TEXT).end())
                            .from(COURSE_QUESTION_RECOMMEND);
            return step.where(conditions).orderBy(COURSE_QUESTION_RECOMMEND.SORT.asc(), COURSE_QUESTION_RECOMMEND.MODIFY_DATE.desc());
        }, r->{
            return r.into(CourseQuestionRecommend.class);
        });
    }

}
