package com.zxy.product.course.service.support;

import com.alibaba.dubbo.common.utils.StringUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.zxy.common.base.exception.UnprocessableException;
import com.zxy.common.base.helper.PagedResult;
import com.zxy.common.cache.Cache;
import com.zxy.common.dao.Fields;
import com.zxy.common.dao.support.CommonDao;
import com.zxy.common.message.provider.MessageSender;
import com.zxy.product.course.api.CourseInfoService;
import com.zxy.product.course.api.CourseStudyProgressService;
import com.zxy.product.course.api.archived.CourseStudyProgressArchivedService;
import com.zxy.product.course.api.course.CourseCacheService;
import com.zxy.product.course.api.course.CourseProcessService;
import com.zxy.product.course.api.remodeling.RemodelingTrainService;
import com.zxy.product.course.api.sharding.ShardingConfigService;
import com.zxy.product.course.content.CacheKeyConstant;
import com.zxy.product.course.content.CommonConstant;
import com.zxy.product.course.content.DataSource;
import com.zxy.product.course.content.DataSourceEnum;
import com.zxy.product.course.content.ErrorCode;
import com.zxy.product.course.content.MessageHeaderContent;
import com.zxy.product.course.content.MessageTypeContent;
import com.zxy.product.course.entity.Ability;
import com.zxy.product.course.entity.CourseChapter;
import com.zxy.product.course.entity.CourseChapterSection;
import com.zxy.product.course.entity.CourseInfo;
import com.zxy.product.course.entity.CourseRecord;
import com.zxy.product.course.entity.CourseRegister;
import com.zxy.product.course.entity.CourseSectionProgressAttachment;
import com.zxy.product.course.entity.CourseSectionStudyLog;
import com.zxy.product.course.entity.CourseSectionStudyProgress;
import com.zxy.product.course.entity.CourseStudyProgress;
import com.zxy.product.course.entity.Member;
import com.zxy.product.course.entity.MemberStatistics;
import com.zxy.product.course.entity.MemberStatisticsArchives;
import com.zxy.product.course.entity.Organization;
import com.zxy.product.course.entity.ShardingConfig;
import com.zxy.product.course.entity.SplitTableConfig;
import com.zxy.product.course.entity.StudyPushInfo;
import com.zxy.product.course.entity.StudyRank;
import com.zxy.product.course.entity.StudyRecord_2017;
import com.zxy.product.course.entity.SubjectSectionStudyLog;
import com.zxy.product.course.entity.WeekInfo;
import com.zxy.product.course.jooq.tables.pojos.CourseChapterSectionEntity;
import com.zxy.product.course.mongodb.CourseStudyLog;
import com.zxy.product.course.service.support.course.CourseCacheServiceSupport;
import com.zxy.product.course.service.util.DateUtil;
import com.zxy.product.course.service.util.SplitTableName;
import com.zxy.product.course.util.CourseSectionStudyProgressUtil;
import org.jooq.Condition;
import org.jooq.DSLContext;
import org.jooq.Field;
import org.jooq.Record;
import org.jooq.Record1;
import org.jooq.Record2;
import org.jooq.Record4;
import org.jooq.Result;
import org.jooq.SelectConditionStep;
import org.jooq.SelectForUpdateStep;
import org.jooq.SelectJoinStep;
import org.jooq.SelectOnConditionStep;
import org.jooq.SelectOrderByStep;
import org.jooq.SelectSelectStep;
import org.jooq.SortField;
import org.jooq.Table;
import org.jooq.UpdateConditionStep;
import org.jooq.impl.DSL;
import org.jooq.impl.TableImpl;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Date;
import java.text.SimpleDateFormat;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoField;
import java.time.temporal.TemporalAdjusters;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.IntStream;
import java.util.stream.Stream;

import static com.zxy.product.course.jooq.Tables.ABILITY;
import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER;
import static com.zxy.product.course.jooq.Tables.COURSE_CHAPTER_SECTION;
import static com.zxy.product.course.jooq.Tables.COURSE_INFO;
import static com.zxy.product.course.jooq.Tables.COURSE_REGISTER;
import static com.zxy.product.course.jooq.Tables.COURSE_SECTION_PROGRESS_ATTACHMENT;
import static com.zxy.product.course.jooq.Tables.COURSE_SECTION_STUDY_LOG;
import static com.zxy.product.course.jooq.Tables.COURSE_SECTION_STUDY_PROGRESS;
import static com.zxy.product.course.jooq.Tables.COURSE_STUDY_PROGRESS;
import static com.zxy.product.course.jooq.Tables.MEMBER;
import static com.zxy.product.course.jooq.Tables.ORGANIZATION;
import static com.zxy.product.course.jooq.Tables.ORGANIZATION_DETAIL;
import static com.zxy.product.course.jooq.Tables.SPLIT_TABLE_CONFIG;
import static com.zxy.product.course.jooq.Tables.STUDY_PUSH_INFO;
import static com.zxy.product.course.jooq.Tables.STUDY_RECORD_2017;
import static com.zxy.product.course.jooq.Tables.STUDY_TASK_AUDIT_MEMBER;

/**
 * Created by keeley on 16/11/2.
 */
@Service
public class CourseStudyProgressServiceSupport implements CourseStudyProgressService {
    private static Logger log = LoggerFactory.getLogger(CourseStudyProgressServiceSupport.class);
    private CommonDao<CourseStudyProgress> courseStudyProgressCommonDao;
    private CommonDao<CourseSectionStudyProgress> courseSectionStudyProgressCommonDao;
    private CommonDao<CourseRegister> courseRegisterCommonDao;
    private CommonDao<CourseSectionProgressAttachment> attachmentDao;
    private CommonDao<CourseSectionStudyLog> logDao;
    private CommonDao<CourseSectionProgressAttachment> sectionProgressAttachmentDao;
    private CommonDao<CourseChapterSection> sectionDao;
    private CommonDao<CourseInfo> courseInfoDao;
    private CommonDao<StudyRecord_2017> StudyRecord_2017Dao;
    private CommonDao<CourseRecord> recordDao;
    private CommonDao<Member> memberDao;
    private CourseCacheServiceSupport courseCacheServiceSupport;
    private CommonDao<MemberStatistics> memberStatisticsDao;
    private CommonDao<MemberStatisticsArchives> memberStatisticsArchivesDao;
    private CourseProcessService courseProcessService;
    private CommonDao<SubjectSectionStudyLog> subjectLogDao;
    private Cache cache;
    private CommonDao<Organization> orgDao;
    private ShardingConfigService shardingConfigService;
    private RemodelingTrainService trainService;
    private CommonDao<SplitTableConfig> splitTableConfigDao;
    private CommonDao<CourseSectionStudyLog> logCommonDao;
    private CourseCacheService courseCacheService;

    @Resource
    private CourseStudyProgressArchivedService archivedService;
    @Resource
    private CourseInfoService courseInfoService;
    private static String TENDENCYDAY = "day";
    private static String TENDENCYWEEK = "week";
    private static String TENDENCYMONTH = "month";
//    //学习卡片优化
    private static final String STUDY_PROGRESS_CACHE_KEY = "study-progress-cache-key-";
    private static final int MAX_COURSE = 5;
    private static final int APP_COURSE = 1;
    private static final Integer DEFAULT_COMPLETE_TIMES = 0;
    private CommonDao<Ability> abilityDao;

    @Autowired
    public void setAbilityDao(CommonDao<Ability> abilityDao) {
        this.abilityDao = abilityDao;
    }

    @Autowired
    public void setLogCommonDao(CommonDao<CourseSectionStudyLog> logCommonDao) {
        this.logCommonDao = logCommonDao;
    }

    @Autowired
    public void setSplitTableConfigDao(CommonDao<SplitTableConfig> splitTableConfigDao) {
        this.splitTableConfigDao = splitTableConfigDao;
    }

    @Autowired
    public void setShardingConfigService(ShardingConfigService shardingConfigService) {
        this.shardingConfigService = shardingConfigService;
    }

    @Autowired
    public void setCache(Cache cache) {
        this.cache = cache;
    }


    @Autowired
    public void setStudyRecord_2017Dao(CommonDao<StudyRecord_2017> studyRecord_2017Dao) {
        StudyRecord_2017Dao = studyRecord_2017Dao;
    }

    @Autowired
    public void setRecordDao(CommonDao<CourseRecord> recordDao) {
        this.recordDao = recordDao;
    }

    @Autowired
    public void setCourseCacheServiceSupport(CourseCacheServiceSupport courseCacheServiceSupport) {
        this.courseCacheServiceSupport = courseCacheServiceSupport;
    }

    @Autowired
    public void setSectionDao(CommonDao<CourseChapterSection> sectionDao) {
        this.sectionDao = sectionDao;
    }

    private MessageSender messageSender;

    @Autowired
    public void setMessageSender(MessageSender messageSender) {
        this.messageSender = messageSender;
    }

    @Autowired
    public void setCourseRegisterCommonDao(CommonDao<CourseRegister> courseRegisterCommonDao) {
        this.courseRegisterCommonDao = courseRegisterCommonDao;
    }

    @Autowired
    public void setCourseStudyProgressCommonDao(CommonDao<CourseStudyProgress> courseStudyProgressCommonDao) {
        this.courseStudyProgressCommonDao = courseStudyProgressCommonDao;
    }

    @Autowired
    public void setCourseSectionStudyProgressCommonDao(
            CommonDao<CourseSectionStudyProgress> courseSectionStudyProgressCommonDao) {
        this.courseSectionStudyProgressCommonDao = courseSectionStudyProgressCommonDao;
    }

    @Autowired
    public void setAttachmentDao(CommonDao<CourseSectionProgressAttachment> attachmentDao) {
        this.attachmentDao = attachmentDao;
    }

    @Autowired
    public void setLogDao(CommonDao<CourseSectionStudyLog> logDao) {
        this.logDao = logDao;
    }

    @Autowired
    public void setSectionProgressAttachmentDao(
            CommonDao<CourseSectionProgressAttachment> sectionProgressAttachmentDao) {
        this.sectionProgressAttachmentDao = sectionProgressAttachmentDao;
    }

    @Autowired
    public void setCourseInfoDao(CommonDao<CourseInfo> courseInfoDao) {
        this.courseInfoDao = courseInfoDao;
    }

    @Autowired
    public void setMemberDao(CommonDao<Member> memberDao) {
        this.memberDao = memberDao;
    }

    @Autowired
    public void setMemberStatisticsDao(CommonDao<MemberStatistics> memberStatisticsDao) {
        this.memberStatisticsDao = memberStatisticsDao;
    }

    @Autowired
    public void setMemberStatisticsArchivesDao(CommonDao<MemberStatisticsArchives> memberStatisticsArchivesDao) {
        this.memberStatisticsArchivesDao = memberStatisticsArchivesDao;
    }

    @Autowired
    public void setCourseProcessService(CourseProcessService courseProcessService) {
        this.courseProcessService = courseProcessService;
    }

    @Autowired
    public void setSubjectLogDao(CommonDao<SubjectSectionStudyLog> subjectLogDao) {
		this.subjectLogDao = subjectLogDao;
	}

    @Autowired
	public void setOrgDao(CommonDao<Organization> orgDao) {
		this.orgDao = orgDao;
	}

	@Autowired
    public void setTrainService(RemodelingTrainService trainService) {
        this.trainService = trainService;
    }
    @Autowired
    public void setCourseCacheService(CourseCacheService courseCacheService) {
        this.courseCacheService = courseCacheService;
    }
    @DataSource(type= DataSourceEnum.SLAVE)
	@Override
    public PagedResult<CourseStudyProgress> page(int pageNum, int pageSize, String courseId,
            Optional<String> memberName, Optional<String> memberReadName, Optional<String> organizationId,
            Optional<Integer> finishStatus, Optional<Integer> requiredStatus, Optional<Long> beginBeginDate, Optional<Long> beginEndDate,
            Optional<Long> beginRegisterDate, Optional<Long> endRegisterDate) {

        com.zxy.product.course.jooq.tables.Member markMemberTable = MEMBER.as("markMember"); // 标记人
        String path = null;
        if (organizationId.isPresent()) {
        	path = courseStudyProgressCommonDao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).where(ORGANIZATION.ID.eq(organizationId.get()))).fetchOne(ORGANIZATION.PATH);
        }
        Optional<String> pathOptioanl = Optional.ofNullable(path);
        // 来源列表
        return courseStudyProgressCommonDao.execute(x -> {

            SelectSelectStep<Record> selectListField = x.select(Fields.start()
            		.add(COURSE_STUDY_PROGRESS.ID, COURSE_STUDY_PROGRESS.FINISH_STATUS, COURSE_STUDY_PROGRESS.FINISH_TIME,
            				COURSE_STUDY_PROGRESS.IS_REQUIRED, COURSE_STUDY_PROGRESS.TYPE,
            				COURSE_STUDY_PROGRESS.REGISTER_TIME, COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME,
            				COURSE_STUDY_PROGRESS.COURSE_ID, COURSE_STUDY_PROGRESS.MARK_TIME)
                    .add(MEMBER.ID, MEMBER.NAME, MEMBER.FULL_NAME) // 按需查询
                    .add(ORGANIZATION.ID, ORGANIZATION.NAME)
                    .add(markMemberTable.ID, markMemberTable.NAME, markMemberTable.FULL_NAME).end()); // 查询list


            List<Condition> param = Stream
                    .of(memberName.map(MEMBER.NAME::startsWith), memberReadName.map(MEMBER.FULL_NAME::startsWith),
                            finishStatus.map(f -> {
                                Condition c = COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(f);
                                if(f == CourseStudyProgress.FINISH_STATUS_FINISH)
                                    return c.or(COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS));
                                return c;
                            }),
                            requiredStatus.map(COURSE_STUDY_PROGRESS.IS_REQUIRED::eq),
                            beginBeginDate.map(COURSE_STUDY_PROGRESS.FINISH_TIME::ge),
                            beginEndDate.map(COURSE_STUDY_PROGRESS.FINISH_TIME::le),
                            beginRegisterDate.map(COURSE_STUDY_PROGRESS.REGISTER_TIME::ge),
                            endRegisterDate.map(COURSE_STUDY_PROGRESS.REGISTER_TIME::le),
                            pathOptioanl.map(p -> ORGANIZATION.PATH.like(p + "%")))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> a
                    .from(COURSE_STUDY_PROGRESS).leftJoin(MEMBER).on(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(MEMBER.ID))
                    .leftJoin(markMemberTable).on(markMemberTable.ID.eq(COURSE_STUDY_PROGRESS.MARK_MEMBER_ID))
                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(param)
                    .and(COURSE_STUDY_PROGRESS.COURSE_ID.eq(courseId));
            // 没有条件输入时不查询count，直接使用课程总学习人数
            int count = 0;
            if (!memberName.isPresent() && !memberReadName.isPresent() && !finishStatus.isPresent()
                && !requiredStatus.isPresent() && !beginBeginDate.isPresent() && !beginEndDate.isPresent()
                    && !beginRegisterDate.isPresent() && !endRegisterDate.isPresent()
                    && !pathOptioanl.isPresent()) {
                count = x.select(COURSE_INFO.STUDY_MEMBER_COUNT).from(COURSE_INFO).where(COURSE_INFO.ID.eq(courseId)).fetchOptional(COURSE_INFO.STUDY_MEMBER_COUNT).orElse(0);

            } else {
                SelectSelectStep<Record> selectCountField = x
                        .select(Fields.start().add(COURSE_STUDY_PROGRESS.ID.count()).end()); // 查询总条数
                Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepCountFunc = a -> a
                        .from(COURSE_STUDY_PROGRESS).leftJoin(MEMBER).on(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(MEMBER.ID))
                        .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(param)
                        .and(COURSE_STUDY_PROGRESS.COURSE_ID.eq(courseId));
                count = stepCountFunc.apply(selectCountField).fetchOne().getValue(0, Integer.class);
            }
            SelectConditionStep<Record> listStep = stepFunc.apply(selectListField);
//            listStep.orderBy(COURSE_STUDY_PROGRESS.CREATE_TIME.desc());
            Result<Record> record = listStep.limit((pageNum - 1) * pageSize, pageSize).fetch();

            List<CourseStudyProgress> courseStudyProgresses = record.into(CourseStudyProgress.class);
            List<Member> members = record.into(MEMBER).into(Member.class);
            List<Member> markMembers = record.into(markMemberTable).into(Member.class);
            List<Organization> organizations = record.into(Organization.class);

            IntStream.range(0, courseStudyProgresses.size()).forEach(i -> {
                CourseStudyProgress cs = courseStudyProgresses.get(i);
                members.get(i).setOrganization(organizations.get(i));
                cs.setMember(members.get(i));
                cs.setMarkMember(markMembers.get(i));
                cs.setOrganization(organizations.get(i));
            });
            return PagedResult.create(count, courseStudyProgresses);
        });
    }

    @Override
    public List<CourseStudyProgress> findList(String courseId, Optional<String> memberName, Optional<String> memberReadName, Optional<String> organizationId, Optional<Integer> finishStatus, Optional<Integer> requiredStatus, Optional<Long> studyTime, Optional<Long> registerTime,Optional<Long> beginBeginDate, Optional<Long> beginEndDate, Optional<Long> registerBeginDate, Optional<Long> registerEndDate) {
        com.zxy.product.course.jooq.tables.Member markMemberTable = MEMBER.as("markMember"); // 标记人
        String path = null;
        if (organizationId.isPresent()) {
        	path = courseStudyProgressCommonDao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).where(ORGANIZATION.ID.eq(organizationId.get()))).fetchOne(ORGANIZATION.PATH);
        }
        Optional<String> pathOptioanl = Optional.ofNullable(path);
        // 来源列表
        return courseStudyProgressCommonDao.execute(x -> {
            SelectSelectStep<Record> selectListField = x.select(Fields.start()
            		.add(COURSE_STUDY_PROGRESS.ID, COURSE_STUDY_PROGRESS.FINISH_STATUS, COURSE_STUDY_PROGRESS.FINISH_TIME,
            				COURSE_STUDY_PROGRESS.IS_REQUIRED, COURSE_STUDY_PROGRESS.TYPE,
            				COURSE_STUDY_PROGRESS.REGISTER_TIME, COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME)
                    .add(MEMBER.ID, MEMBER.NAME, MEMBER.FULL_NAME) // 按需查询
                    .add(ORGANIZATION.ID, ORGANIZATION.NAME).add(markMemberTable.ID, markMemberTable.NAME).end()); // 查询list
            List<Condition> param = Stream
                    .of(memberName.map(MEMBER.NAME::contains), memberReadName.map(MEMBER.FULL_NAME::contains),
                            finishStatus.map(COURSE_STUDY_PROGRESS.FINISH_STATUS::eq),
                            requiredStatus.map(COURSE_STUDY_PROGRESS.IS_REQUIRED::eq),
                            studyTime.map(COURSE_STUDY_PROGRESS.BEGIN_TIME::lt),
                            registerTime.map(COURSE_STUDY_PROGRESS.REGISTER_TIME::lt),
                            beginBeginDate.map(COURSE_STUDY_PROGRESS.FINISH_TIME::ge),
                            beginEndDate.map(COURSE_STUDY_PROGRESS.FINISH_TIME::le),
                            registerBeginDate.map(COURSE_STUDY_PROGRESS.REGISTER_TIME::ge),
                            registerEndDate.map(COURSE_STUDY_PROGRESS.REGISTER_TIME::le),
                            pathOptioanl.map(p -> ORGANIZATION.PATH.like(p + "%")))
                    .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());

            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> a
                    .from(COURSE_STUDY_PROGRESS).leftJoin(MEMBER).on(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(MEMBER.ID))
                    .leftJoin(markMemberTable).on(markMemberTable.ID.eq(COURSE_STUDY_PROGRESS.MARK_MEMBER_ID))
                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(param)
                    .and(COURSE_STUDY_PROGRESS.COURSE_ID.eq(courseId));

            Result<Record> record = stepFunc.apply(selectListField).orderBy(COURSE_STUDY_PROGRESS.CREATE_TIME.desc()).limit(0, 5000).fetch();

            List<CourseStudyProgress> courseStudyProgresses = record.into(COURSE_STUDY_PROGRESS)
                    .into(CourseStudyProgress.class);
            List<Member> members = record.into(MEMBER).into(Member.class);
            List<Member> markMembers = record.into(markMemberTable).into(Member.class);
            List<Organization> organizations = record.into(ORGANIZATION).into(Organization.class);

            IntStream.range(0, courseStudyProgresses.size()).forEach(i -> {
                CourseStudyProgress cs = courseStudyProgresses.get(i);
                members.get(i).setOrganization(organizations.get(i));
                cs.setMember(members.get(i));
                cs.setMarkMember(markMembers.get(i));
                cs.setOrganization(organizations.get(i));
            });
            return courseStudyProgresses;
        });
    }

    /**
     * 所有记录
     *
     * @param memberId
     * @param courseId
     * @return
     */
    @Override
    public List<CourseRegister> findRegisters(String memberId, String courseId) {
        Stream<Condition> where = Stream.of(COURSE_REGISTER.MEMBER_ID.eq(memberId),
                COURSE_REGISTER.COURSE_ID.eq(courseId));
        return courseRegisterCommonDao.fetch(where, COURSE_REGISTER.REGISTER_TIME.desc());
    }

    @Override
    public Optional<CourseStudyProgress> getByMemberIdAndCourseId(String memberId, String courseId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return Optional.ofNullable(courseStudyProgressCommonDao.execute(dslContext -> dslContext
                .select(cacheTable.fields())
                .from(cacheTable)
                .where(cacheTable.field("f_member_id", String.class).eq(memberId).and(cacheTable.field("f_course_id", String.class).eq(courseId))).limit(1)
                .fetchOne(r -> r.into(CourseStudyProgress.class))
        ));
    }

    @SuppressWarnings("unchecked")
    @Override
    public PagedResult<CourseSectionStudyProgress> findCourseSectionStudyList(int page, int pageSize,
            String referenceId, Integer sectionType, Optional<String> memberName, Optional<String> loginId,
            Optional<Long> startTime, Optional<Long> endTime,String organizationId) {
        Field<String> orgName = ORGANIZATION.NAME.as("organization_name");
        Field<String> orgId = ORGANIZATION.ID.as("organization_id");
        Field<String> memberId = MEMBER.ID.as("member_id");
        Optional<String> pathOptional = Optional.ofNullable(courseStudyProgressCommonDao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).where(ORGANIZATION.ID.eq(organizationId))).fetchOne(ORGANIZATION.PATH));;

        // update for xdn 作业详情
        CourseInfo courseInfo = courseCacheServiceSupport.getCourseByReferenceId(referenceId);
//        TableImpl<?> csspTable = shardingConfigService.getTableName(courseInfo.getId(),
//                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil
                .getTableByOrgId(organizationId,courseInfo.getId());
        SelectConditionStep<?> step = courseSectionStudyProgressCommonDao.execute(c -> c
                .selectDistinct(Fields.start()
                        .add(csspTable.field("f_id"), csspTable.field("f_member_id"),
                                csspTable.field("f_commit_time"), csspTable.field("f_score"),
                                csspTable.field("f_finish_status"), csspTable.field("f_section_id"))
                        .add(memberId, MEMBER.NAME, MEMBER.FULL_NAME, MEMBER.ORGANIZATION_ID).add(orgId, orgName)
                        .add(COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.SECTION_TYPE).end())
                .from(csspTable).leftJoin(MEMBER)
                .on(csspTable.field("f_member_id", String.class).eq(MEMBER.ID)).leftJoin(ORGANIZATION)
                .on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).leftJoin(COURSE_CHAPTER_SECTION)
                .on(csspTable.field("f_section_id", String.class).eq(COURSE_CHAPTER_SECTION.REFERENCE_ID)
                        .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(sectionType)))
                .where(COURSE_CHAPTER_SECTION.REFERENCE_ID.eq(referenceId)
                        .and(csspTable.field("f_finish_status", Integer.class).in(CourseSectionStudyProgress.FINISH_STATUS_AUDIT,
                        CourseSectionStudyProgress.FINISH_STATUS_FINISH,CourseSectionStudyProgress.FINISH_STATUS_NOT_THROUGH))));

        Stream<Optional<Condition>> conditions = Stream.of(memberName.map(MEMBER.FULL_NAME::contains),
                loginId.map(MEMBER.NAME::contains),
                startTime.map(csspTable.field("f_commit_time", Long.class)::greaterOrEqual),
                endTime.map(csspTable.field("f_commit_time", Long.class)::lessOrEqual),
                pathOptional.map(p -> ORGANIZATION.PATH.like(p + "%")));

        Condition c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
                .orElse(DSL.trueCondition());
        Integer count = courseSectionStudyProgressCommonDao.execute(e -> e.fetchCount(step.and(c)));

        int firstResult = (page - 1) * pageSize;
        List<Record> items = (List<Record>) step.and(c).orderBy(csspTable.field("f_commit_time").desc())
                .limit(firstResult, pageSize).fetch();

        List<CourseSectionStudyProgress> progressList = items.stream().map(o -> {
            CourseSectionStudyProgress progress = o.into(csspTable)
                    .into(CourseSectionStudyProgress.class);
            Organization organization = new Organization();
            organization.setId(o.getValue(orgId));
            organization.setName(o.getValue(orgName));
            Member member = new Member();
            member.setId(o.getValue(memberId));
            member.setName(o.getValue(MEMBER.NAME));
            member.setFullName(o.getValue(MEMBER.FULL_NAME));
            progress.setOrganization(organization);
            progress.setMember(member);
            CourseChapterSection section = new CourseChapterSection();
            section.setResourceId(o.getValue(COURSE_CHAPTER_SECTION.RESOURCE_ID));
            section.setSectionType(o.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
            progress.setCourseChapterSection(section);
            return progress;
        }).collect(Collectors.toList());

        return PagedResult.create(count, progressList);
    }

    @Override
    public List<CourseSectionStudyProgress> findCourseSectionStudyList(String referenceId, Integer sectionType, Optional<String> memberName, Optional<String> loginId, Optional<Long> startTime, Optional<Long> endTime , String organizationId) {
        Field<String> orgName = ORGANIZATION.NAME.as("organization_name");
        Field<String> orgId = ORGANIZATION.ID.as("organization_id");
        Field<String> memberId = MEMBER.ID.as("member_id");

        Optional<String> pathOptional = Optional.ofNullable(courseStudyProgressCommonDao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).where(ORGANIZATION.ID.eq(organizationId))).fetchOne(ORGANIZATION.PATH));
        // update for xdn 作业详情导出
        CourseInfo courseInfo = courseCacheServiceSupport.getCourseByReferenceId(referenceId);
//        TableImpl<?> csspTable = shardingConfigService.getTableName(courseInfo.getId(),
//                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil
                .getTableByOrgId(organizationId, courseInfo.getId());
        SelectConditionStep<?> step = courseSectionStudyProgressCommonDao.execute(c -> c
                .selectDistinct(Fields.start()
                        .add(csspTable.field("f_id"), csspTable.field("f_member_id"),
                                csspTable.field("f_commit_time"), csspTable.field("f_score"),
                                csspTable.field("f_finish_status"), csspTable.field("f_section_id"))
                        .add(memberId, MEMBER.NAME, MEMBER.FULL_NAME, MEMBER.ORGANIZATION_ID).add(orgId, orgName)
                        .add(COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.SECTION_TYPE).end())
                .from(csspTable).leftJoin(MEMBER)
                .on(csspTable.field("f_member_id", String.class).eq(MEMBER.ID)).leftJoin(ORGANIZATION)
                .on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).leftJoin(COURSE_CHAPTER_SECTION)
                .on(csspTable.field("f_section_id", String.class).eq(COURSE_CHAPTER_SECTION.REFERENCE_ID)
                        .and(COURSE_CHAPTER_SECTION.SECTION_TYPE.eq(sectionType)))
                .where(COURSE_CHAPTER_SECTION.REFERENCE_ID.eq(referenceId)
                        .and(csspTable.field("f_finish_status", Integer.class).in(CourseSectionStudyProgress.FINISH_STATUS_AUDIT,
                        CourseSectionStudyProgress.FINISH_STATUS_FINISH,CourseSectionStudyProgress.FINISH_STATUS_NOT_THROUGH))));

        Stream<Optional<Condition>> conditions = Stream.of(memberName.map(MEMBER.FULL_NAME::contains),
                loginId.map(MEMBER.NAME::contains),
                startTime.map(csspTable.field("f_commit_time", Long.class)::greaterOrEqual),
                endTime.map(csspTable.field("f_commit_time", Long.class)::lessOrEqual),
                pathOptional.map(p -> ORGANIZATION.PATH.like(p + "%")));

        Condition c = conditions.filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item))
                .orElse(DSL.trueCondition());

        List<Record> items = (List<Record>) step.and(c).orderBy(csspTable.field("f_commit_time").desc()).fetch();

        return items.stream().map(o -> {
            CourseSectionStudyProgress progress = o.into(csspTable)
                    .into(CourseSectionStudyProgress.class);
            Organization organization = new Organization();
            organization.setId(o.getValue(orgId));
            organization.setName(o.getValue(orgName));
            Member member = new Member();
            member.setId(o.getValue(memberId));
            member.setName(o.getValue(MEMBER.NAME));
            member.setFullName(o.getValue(MEMBER.FULL_NAME));
            progress.setOrganization(organization);
            progress.setMember(member);
            CourseChapterSection section = new CourseChapterSection();
            section.setResourceId(o.getValue(COURSE_CHAPTER_SECTION.RESOURCE_ID));
            section.setSectionType(o.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
            progress.setCourseChapterSection(section);
            return progress;
        }).collect(Collectors.toList());
    }

    @Override
    public CourseSectionStudyProgress auditSectionStudyProgress(String auditMemberId, String sectionProgressId,
                                                                Integer auditPass, Optional<String> courseId, Optional<String> comments, Optional<Integer> score) {
       return auditSectionStudyProgress(auditMemberId,sectionProgressId,auditPass,courseId,comments,score, Optional.empty());
    }
    @Override
    public CourseSectionStudyProgress auditSectionStudyProgress(String auditMemberId, String sectionProgressId,
                                                                Integer auditPass, Optional<String> courseId, Optional<String> comments, Optional<Integer> score,
                                                                Optional<String> beAuditedId) {
        // update for xdn 作业审核
        TableImpl<?> csspTable = courseId.isPresent() && beAuditedId.isPresent() ? CourseSectionStudyProgressUtil.getTable(beAuditedId.get(), courseId.get()) : COURSE_SECTION_STUDY_PROGRESS;

        CourseSectionStudyProgress courseSectionStudyProgress = courseSectionStudyProgressCommonDao.execute(dslContext -> dslContext.select(csspTable.fields())
                .from(csspTable).where(csspTable.field("f_id", String.class).eq(sectionProgressId)))
                .fetchOne(r-> new CourseSectionStudyProgress().fill(csspTable, r));

        int finishStatus = courseSectionStudyProgress.getFinishStatus() != null ? courseSectionStudyProgress.getFinishStatus() : CourseSectionStudyProgress.FINISH_STATUS_UNSTART;
        if (finishStatus != CourseSectionStudyProgress.FINISH_STATUS_AUDIT) throw new UnprocessableException(ErrorCode.HaveAuditAssignmentsYouSubmit);
        courseSectionStudyProgress.setAuditMemberId(auditMemberId);
        score.ifPresent(courseSectionStudyProgress::setScore);
        courseSectionStudyProgress.setAuditPass(auditPass);
        courseSectionStudyProgress.setLastAccessTime(System.currentTimeMillis());
        comments.ifPresent(courseSectionStudyProgress::setComments);

        CourseChapterSection section = sectionDao.getOptional(courseSectionStudyProgress.getSectionId()).orElse(null);
        if(section == null ) return null;

        // 状态为审核通过时，该节进度为已完成
        if (auditPass.equals(AUDIT_PASS)) {
            courseSectionStudyProgress.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_FINISH);
            courseSectionStudyProgress.setCompletedRate(100);
        } else {
            courseSectionStudyProgress.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_NOT_THROUGH);
        }

        // 更新进度
        courseCacheServiceSupport.updateProgress(courseSectionStudyProgress);
        // 只有任务完成了才更新进度
        if (courseSectionStudyProgress.getFinishStatus() == CourseSectionStudyProgress.FINISH_STATUS_FINISH) {
            messageSender.send(MessageTypeContent.COURSE_PROGRESS_UPDATE, MessageHeaderContent.ID, sectionProgressId,
                    MessageHeaderContent.COURSE_ID, courseSectionStudyProgress.getCourseId(),
                    MessageHeaderContent.STUDYTIME, String.valueOf(0), MessageHeaderContent.STUDYCLIENTTYPE,
                    String.valueOf(CourseSectionStudyLog.CLIENT_TYPE_PC),
                    MessageHeaderContent.FINISHSTIME, System.currentTimeMillis() + "",
                    MessageHeaderContent.MEMBER_ID , beAuditedId.orElse("1"));
        }

        if(finishStatus > CourseSectionStudyProgress.FINISH_STATUS_STUDY) {
            messageSender.send(MessageTypeContent.STUDY_TASK_SUBMIT_AUDIT, MessageHeaderContent.ID, courseSectionStudyProgress.getId(),
                    MessageHeaderContent.COURSE_ID, section.getCourseId(), MessageHeaderContent.BUSINESS_ID, section.getResourceId());
        }

        return courseSectionStudyProgress;
    }
    // 点击的时候 生成进度（已弃用）
    @Deprecated
    public CourseSectionStudyLog createSectionLog(String sectionId, Optional<Integer> clientType) {
        CourseChapterSection section = courseCacheServiceSupport.getSection(sectionId);
        CourseSectionStudyLog log = new CourseSectionStudyLog();
        log.forInsert();
        log.setSectionId(section.getReferenceId());// 关联引用id
        log.setCourseId(section.getCourseId());
        log.setClientType(clientType.orElse(CourseSectionStudyLog.CLIENT_TYPE_APP));
        log.setStudyTime( 5*60); // 默认五分钟
        CourseSectionStudyLog result = logDao.insert(log);

        // add by Acong 发送log分表同步消息
        messageSender.send(MessageTypeContent.SPLIT_COURSE_SECTION_STUDY_LOG_INSERT, MessageHeaderContent.ID, log.getId());
        return result;
    }


    @Override
    public CourseSectionStudyProgress getSectionStudyProgressById(String id, Optional<String> courseId, String memberId) {
        com.zxy.product.course.jooq.tables.Member commitMember = MEMBER.as("commitMember"); // 提交人
        com.zxy.product.course.jooq.tables.Member auditMember = MEMBER.as("auditMember"); // 审核人
        Field<String> commitMemberName = commitMember.FULL_NAME.as("commitMemberName");
        Field<String> auditMemberName = auditMember.FULL_NAME.as("auditMemberName");
        // update for xdn 单个学员作业详情
        TableImpl<?> csspTable = courseId.isPresent() ? CourseSectionStudyProgressUtil.getTable(memberId, courseId.get()) : COURSE_SECTION_STUDY_PROGRESS;
        CourseSectionStudyProgress courseSectionStudyProgress = courseSectionStudyProgressCommonDao.execute(dao -> dao
                .select(Fields.start().add(csspTable)
                        .add(commitMemberName, auditMemberName)
                        .add(COURSE_INFO.BUSINESS_TYPE)
                        .add(COURSE_CHAPTER_SECTION.RESOURCE_ID).end())
                .from(csspTable)
                .leftJoin(COURSE_CHAPTER_SECTION).on(csspTable.field("f_section_id", String.class).eq(COURSE_CHAPTER_SECTION.ID))
                .leftJoin(commitMember).on(csspTable.field("f_member_id", String.class).eq(commitMember.ID))
                .leftJoin(auditMember).on(csspTable.field("f_audit_member_id", String.class).eq(auditMember.ID))
                .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(csspTable.field("f_course_id", String.class)))
                .where(csspTable.field("f_id", String.class).eq(id)).fetchOne().map(record -> {
                    CourseSectionStudyProgress progress = new CourseSectionStudyProgress().fill(csspTable, record);
                    progress.setCommitMemberName(record.getValue(commitMemberName));
                    progress.setAuditMemberName(record.getValue(auditMemberName));
                    CourseInfo courseInfo = new CourseInfo();
                    courseInfo.setBusinessType(record.getValue(COURSE_INFO.BUSINESS_TYPE));
                    progress.setCourseInfo(courseInfo);
                    CourseChapterSection section = record.into(COURSE_CHAPTER_SECTION).into(CourseChapterSection.class);
                    progress.setCourseChapterSection(section);
                    return progress;
                }));
        courseSectionStudyProgress.setSectionAttachments(this.getProgressAttachment(id));
        return courseSectionStudyProgress;
    }

    @Override
    public List<CourseChapterSection> findCourseSectionStudyProgress(String courseId, String memberId) {
        // update for xdn 学员管理章节详情
/*        TableImpl<?> csspTable = shardingConfigService.getTableName(courseId,
                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);*/
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, courseId);
        Result<Record> record = courseSectionStudyProgressCommonDao.execute(
                x -> x.selectDistinct(Fields.start().add(COURSE_CHAPTER_SECTION).add(csspTable).end())
                        .from(csspTable).leftJoin(COURSE_CHAPTER_SECTION)
                        .on(csspTable.field("f_section_id", String.class).eq(COURSE_CHAPTER_SECTION.ID))
                        .where(csspTable.field("f_course_id", String.class).eq(courseId))
                        .and(csspTable.field("f_member_id", String.class).eq(memberId))
                        .orderBy(COURSE_CHAPTER_SECTION.SEQUENCE.asc())
                        .fetch());

        List<CourseChapterSection> chapterSectionList = record.into(COURSE_CHAPTER_SECTION)
                .into(CourseChapterSection.class);
        List<CourseSectionStudyProgress> sectionStudyProgressList = new ArrayList<>();
        record.forEach(r-> sectionStudyProgressList.add(new CourseSectionStudyProgress().fill(csspTable, r)));
//        List<CourseSectionStudyProgress> sectionStudyProgressList = record.into(COURSE_SECTION_STUDY_PROGRESS)
//                .into(CourseSectionStudyProgress.class);
        IntStream.range(0, chapterSectionList.size())
                .forEach(i -> chapterSectionList.get(i).setProgress(sectionStudyProgressList.get(i)));
        // 增加归档的数据的查询展示 Begin
        List<CourseChapter> courseChapters = courseInfoService.findCourseChapterByCourseId(courseId);
        List<String> resourceIds = Lists.newArrayList();
        List<CourseChapterSection> archivedCourseChapterSection = Lists.newArrayList();
        courseChapters
                .stream()
                .map(CourseChapter::getCourseChapterSections)
                .filter(Objects::nonNull)
                .peek(archivedCourseChapterSection::addAll)
                .map(sections -> sections
                        .stream()
                        .map(CourseChapterSectionEntity::getResourceId)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList())
                )
                .distinct()
                .peek(resourceIds::addAll)
                .count();
        Map<String, CourseChapterSection> courseChapterSectionMap = archivedCourseChapterSection
                .stream()
                .filter(x -> Objects.nonNull(x.getResourceId()) && !StringUtils.isEmpty(x.getResourceId()))
                .collect(Collectors.toMap(CourseChapterSectionEntity::getResourceId, x -> x,(v1,v2) -> v1));
        // 去重学习中的resourceId
        List<String> collect = chapterSectionList
                .stream()
                .map(CourseChapterSectionEntity::getResourceId)
                .distinct()
                .collect(Collectors.toList());
        resourceIds.removeIf(collect::contains);

        Map<String, Integer> archivedStatus = archivedService.getArchivedStatus(memberId, resourceIds);
        for (String resourceId : resourceIds) {
            if (archivedStatus.containsKey(resourceId)) {
                Optional.ofNullable(courseChapterSectionMap.get(resourceId)).ifPresent(courseChapterSection -> {
                            CourseSectionStudyProgress courseSectionStudyProgress = new CourseSectionStudyProgress();
                            Integer status = archivedStatus.get(resourceId);
                            if (Objects.nonNull(status) && (CourseStudyProgress.FINISH_STATUS_FINISH == status || CourseStudyProgress.FINISH_STATUS_MARKSUCCESS == status)) {
                                courseSectionStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
                            } else {
                                courseSectionStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_STUDY);
                            }
                            courseSectionStudyProgress.setCourseId(courseId);
                            courseSectionStudyProgress.setMemberId(memberId);
                            courseChapterSection.setProgress(courseSectionStudyProgress);
                            courseChapterSection.setId(courseChapterSection.getReferenceId());
                            chapterSectionList.add(courseChapterSection);
                        }
                );
            }
        }
        // 增加归档的数据的查询展示 End

        return chapterSectionList;
    }

    /**
     * 改接口暂时没有地方使用（如果启用改接口需将异步更新也加上）
     * @param id
     * @return
     */
    @Override
    public int delete(String id) {
        messageSender.send(MessageTypeContent.COURSE_MANAGER_STUDY_DELETE, MessageHeaderContent.ID, id);
        return courseStudyProgressCommonDao.delete(id);
    }

    @Override
    public CourseStudyProgress markCompleted(String id, String memberId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        CourseStudyProgress courseStudyProgre = courseStudyProgressCommonDao.execute(dslContext -> dslContext.select(cacheTable.fields()).from(cacheTable).where(cacheTable.field("f_id", String.class).eq(id)).fetchOne(r->r.into(CourseStudyProgress.class)));
        courseStudyProgre.setFinishStatus(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
        courseStudyProgre.setMarkTime(System.currentTimeMillis());
        courseStudyProgre.setMarkMemberId(memberId);
        courseStudyProgre.setFinishTime(System.currentTimeMillis());
        // add by wangdongyan 分表使用，最后一次修改时间
        courseStudyProgre.setLastModifyTime(System.currentTimeMillis());
        messageSender.send(MessageTypeContent.COURSE_MANAGER_STUDY_MARK_FINASH, MessageHeaderContent.ID, id,MessageHeaderContent.MEMBER_ID,memberId);
        courseStudyProgressCommonDao.execute(dsl -> dsl.update(cacheTable).set(CourseStudyProgress.getUpdateMap(cacheTable,courseStudyProgre)).where(cacheTable.field("f_id", String.class).eq(courseStudyProgre.getId())).execute());
        // add 2020-04-26 courseStudyProgress分表更新改为异步
        messageSender.send(MessageTypeContent.SPLIT_COURSE_SECTION_STUDY_LOG_UPDATE, MessageHeaderContent.ID, id);
        return courseStudyProgre;
    }

    @Override
    public int markCompleted(Map<String, String> map, String markMemberId) {

        int num = 0;
        Map<TableImpl<?>, List<String>> tableListMap = new HashMap<>();

        map.keySet().forEach(memberId -> {
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
            tableListMap.computeIfAbsent(cacheTable, c -> new ArrayList<>()).add(memberId);
        });

        for (Map.Entry<TableImpl<?>, List<String>> entry : tableListMap.entrySet()) {
            TableImpl<?> cacheTable = entry.getKey();
            List<String> ids = entry.getValue().stream().map(map::get).collect(Collectors.toList());

            Map<String, String> realMap = courseStudyProgressCommonDao.execute(x -> x.select(cacheTable.field("f_id", String.class), cacheTable.field("f_member_id", String.class))
                    .from(cacheTable).where(cacheTable.field("f_id", String.class).in(ids))
                    .and(cacheTable.field("f_finish_status", Integer.class).in(
                            CourseStudyProgress.FINISH_STATUS_DEFAULT,
                            CourseStudyProgress.FINISH_STATUS_STUDY))
                    .fetchMap(cacheTable.field("f_id", String.class), cacheTable.field("f_member_id", String.class)));

            num += courseStudyProgressCommonDao.execute(x ->
                            x.update(cacheTable).set(cacheTable.field("f_finish_status", Integer.class), CourseStudyProgress.FINISH_STATUS_MARKSUCCESS))
                    .set(cacheTable.field("f_mark_time", Long.class), System.currentTimeMillis())
                    .set(cacheTable.field("f_finish_time", Long.class), System.currentTimeMillis())
                    .set(cacheTable.field("f_mark_member_id", String.class), markMemberId)
                    .where(cacheTable.field("f_id", String.class).in(realMap.keySet())).execute();

            realMap.forEach((id, memberId) -> {
                messageSender.send(MessageTypeContent.COURSE_MANAGER_STUDY_MARK_FINASH, MessageHeaderContent.ID, id, MessageHeaderContent.MEMBER_ID, memberId);
                messageSender.send(MessageTypeContent.SUBJECT_PROGRESS_UPDATE_NEW,
                        MessageHeaderContent.BUSINESS_ID, id, MessageHeaderContent.STUDYTIME, 0 + "",
                        MessageHeaderContent.FINISHSTIME, System.currentTimeMillis() + "", MessageHeaderContent.MEMBER_ID, memberId);
                // add 2020-04-26 courseStudyProgress分表更新改为异步
                messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE, MessageHeaderContent.ID, id, MessageHeaderContent.MEMBER_ID, memberId);
            });
        }

        return num;
    }
    public List<CourseSectionProgressAttachment> getProgressAttachment(String sectionProgressId) {
        return attachmentDao.fetch(COURSE_SECTION_PROGRESS_ATTACHMENT.SECTION_PROGRESS_ID.eq(sectionProgressId));
    }

    @Override
    public Map<String, List<CourseStudyProgress>> getFinishStatus(TableImpl<?> table, Set<String> subjectIds, List<String> memberIds) {

        return courseStudyProgressCommonDao.execute(e ->
                                                            e.select(Fields.start()
                                                                           .add(
                                                                                   table.field("f_course_id", String.class).as("course_id"),
                                                                                   table.field("f_member_id", String.class).as("member_id"),
                                                                                   table.field("f_finish_status", Integer.class).as("finish_status")
                                                                           ).end())
                                                             .from(table)
                                                             .where(table.field("f_member_id", String.class).in(memberIds))
                                                             .and(table.field("f_course_id", String.class).in(subjectIds))).fetch().map(r -> {
            CourseStudyProgress progress = new CourseStudyProgress();
            progress.setCourseId(r.getValue("course_id").toString());
            progress.setMemberId(r.getValue("member_id").toString());
            progress.setFinishStatus(r.getValue("finish_status") != null ? Integer.parseInt(r.getValue("finish_status").toString()) : 0);
            return progress;
        }).stream().collect(Collectors.groupingBy(CourseStudyProgress::getCourseId));
    }

    @Override
    public int insertList(List<CourseStudyProgress> courseProgresses) {
        courseProgresses.forEach(p -> {
        	p.forInsert();
        	// add by wangdongyan 分表使用，最后一次修改时间
        	p.setLastModifyTime(System.currentTimeMillis());
        });
        // add 2020-4-24 异步更新studyProgress分表数据
        courseProgresses.forEach(p -> {
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(p.getMemberId(), SplitTableConfig.COURSE_STUDY_PROGRESS);
            courseStudyProgressCommonDao.execute(dsl->dsl.insertInto(cacheTable,CourseStudyProgress.getFields(cacheTable)).values(CourseStudyProgress.getValues(p)).execute());
            messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_INSERT,
                    MessageHeaderContent.ID, p.getId(),MessageHeaderContent.MEMBER_ID,p.getMemberId());
        });
        return courseProgresses.size();
    }

    @Override
    public int insertCourseSectionStudyLogList(List<CourseSectionStudyLog> logList) {
        logList.forEach(CourseSectionStudyLog::forInsert);
        logDao.insert(logList);
        return logList.size();
    }

    @Override
    public int updateList(List<CourseStudyProgress> courseProgresses) {
        // add by wangdongyan 分表使用，最后一次修改时间
        Map<String, List<CourseStudyProgress>> tableMemberIds = new HashMap<>();
        Map<String, TableImpl<?>> tableMap = new HashMap<>();

        courseProgresses.forEach(r -> r.setLastModifyTime(System.currentTimeMillis()));
        for (CourseStudyProgress courseProgress : courseProgresses) {
            String memberId = courseProgress.getMemberId();
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
            tableMemberIds.computeIfAbsent(cacheTable.getName(), c -> new ArrayList<>()).add(courseProgress);
            tableMap.put(cacheTable.getName(),cacheTable);

        }
        for (Map.Entry<String, List<CourseStudyProgress>> entry : tableMemberIds.entrySet()) {
            TableImpl<?> cacheTable = tableMap.get(entry.getKey());
            List<CourseStudyProgress> value = entry.getValue();

            courseStudyProgressCommonDao.execute(dsl -> {
                List<UpdateConditionStep<?>> collect = value.stream().map(c ->
                        dsl.update(cacheTable).set(CourseStudyProgress.getUpdateMap(cacheTable,c)).where(cacheTable.field("f_id", String.class).eq(c.getId()))
                ).collect(Collectors.toList());
                return dsl.batch(collect).execute();
            });
        }

        // add 2020-4-24 异步更新studyProgress分表数据
        courseProgresses.forEach(p -> {
            messageSender.send(MessageTypeContent.SPLIT_COURSE_SECTION_STUDY_LOG_UPDATE,
                    MessageHeaderContent.ID, p.getId());

        });
        return courseProgresses.size();
    }

    @Override
    public int insertSectionProgressList(List<CourseSectionStudyProgress> sectionProgresses) {
        // update for xdn
        if (sectionProgresses == null || sectionProgresses.isEmpty())
            return 0;

        CourseInfo courseInfo = courseCacheServiceSupport.getCourseByReferenceId(sectionProgresses.get(0).getCourseId());
//        TableImpl<?> csspTable = shardingConfigService.getTableName(courseInfo.getId(),
//                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil
                .getTable(sectionProgresses.get(0).getMemberId(),courseInfo.getId());
        sectionProgresses.forEach(p -> {
            p.forInsert();
            courseSectionStudyProgressCommonDao.execute(dslContext -> p.insert(csspTable, dslContext)).execute();
        });
//        sectionProgresses.forEach(p -> p.forInsert());
//        courseSectionStudyProgressCommonDao.insert(sectionProgresses);
        return sectionProgresses.size();
    }

    @Override
    public Optional<CourseStudyProgress> findByMemberIdAndCourseId(String memberId, String courseId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return Optional.ofNullable(courseStudyProgressCommonDao.execute(dslContext -> dslContext.select(cacheTable.fields()).from(cacheTable).where(cacheTable.field("f_course_id", String.class).eq(courseId), cacheTable.field("f_member_id", String.class).eq(memberId)).fetchOne(r->r.into(CourseStudyProgress.class))));
    }

    @Override
    public List<CourseStudyProgress> findStudyProgressList(String businessId, int limitCount,List<String> exceludeIds) {
        List<Record> records = courseStudyProgressCommonDao.execute(dao -> dao
                .select(Fields.start()
                        .add(COURSE_STUDY_PROGRESS.ID, COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME)
                        .add(MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.ORGANIZATION_ID)
                        .add(ORGANIZATION.NAME).end())
                .from(COURSE_STUDY_PROGRESS).leftJoin(MEMBER).on(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(MEMBER.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(businessId).and(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.gt(60)).and(COURSE_STUDY_PROGRESS.MEMBER_ID.notIn(exceludeIds)))
                .orderBy(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.desc())
                .limit(limitCount).fetch());
        return records.stream().map(r -> {
            CourseStudyProgress progress = r.into(COURSE_STUDY_PROGRESS).into(CourseStudyProgress.class);
            Member member = new Member();
            member.setFullName(r.getValue(MEMBER.FULL_NAME));
            member.setHeadPortrait(r.getValue(MEMBER.HEAD_PORTRAIT));
            Organization org = new Organization();
            org.setName(r.getValue(ORGANIZATION.NAME));
            member.setOrganization(org);
            progress.setMember(member);
            return progress;
        }).collect(Collectors.toList());
    }

    @Override
    public PagedResult<CourseStudyProgress> findStudyProgressPageList(int page, int pageSize, String businessId,List<String> exceludeIds) {
    	// 缓存加1天
    	return cache.get("page-list-ranking" + businessId + page, () -> {
    		List<CourseStudyProgress> list = courseStudyProgressCommonDao.execute(x -> {
        		return x.select(Fields.start().add(COURSE_STUDY_PROGRESS.ID, COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME)
                        .add(MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.ORGANIZATION_ID).add(ORGANIZATION.NAME)
                        .end())
                .from(COURSE_STUDY_PROGRESS).leftJoin(MEMBER).on(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(MEMBER.ID))
                .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(businessId).and(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.gt(0))).and(COURSE_STUDY_PROGRESS.MEMBER_ID.notIn(exceludeIds))
                .orderBy(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.desc())
                .limit((page-1)*pageSize, pageSize).fetch( r -> {
                	CourseStudyProgress progress = r.into(CourseStudyProgress.class);
                    Member member = new Member();
                    member.setFullName(r.getValue(MEMBER.FULL_NAME));
                    Organization org = new Organization();
                    org.setName(r.getValue(ORGANIZATION.NAME));
                    member.setOrganization(org);
                    progress.setMember(member);
                    return progress;
                });
        	});
        	//  查询专题中排行榜的总条数 updated by wangdongyan
        	Integer count = courseStudyProgressCommonDao.execute(x -> {
        		return x.select(COURSE_STUDY_PROGRESS.ID.count()).from(COURSE_STUDY_PROGRESS)
                .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(businessId).and(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.gt(0))).and(COURSE_STUDY_PROGRESS.MEMBER_ID.notIn(exceludeIds))
                .fetchOptional(COURSE_STUDY_PROGRESS.ID.count()).orElse(0);
        	});
        	return  PagedResult.create(count, list);
    	}, 60 * 60 * 24);
//    	return courseStudyProgressCommonDao.fetchPage(page, pageSize, dao -> {
//            return dao
//                    .selectDistinct(Fields.start().add(COURSE_STUDY_PROGRESS.ID, COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME)
//                            .add(MEMBER.FULL_NAME, MEMBER.HEAD_PORTRAIT, MEMBER.ORGANIZATION_ID).add(ORGANIZATION.NAME)
//                            .end())
//                    .from(COURSE_STUDY_PROGRESS).leftJoin(MEMBER).on(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(MEMBER.ID))
//                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
//                    .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(businessId).and(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.gt(0))).and(COURSE_STUDY_PROGRESS.MEMBER_ID.notIn(exceludeIds))
//                    .orderBy(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.desc());
//        }, r -> {
//            CourseStudyProgress progress = r.into(CourseStudyProgress.class);
//            Member member = new Member();
//            member.setFullName(r.getValue(MEMBER.FULL_NAME));
//            Organization org = new Organization();
//            org.setName(r.getValue(ORGANIZATION.NAME));
//            member.setOrganization(org);
//            progress.setMember(member);
//            return progress;
//        });
    }

    // 更新课程进度（已弃用）
    @Deprecated
    @Override
    public CourseSectionStudyProgress updateVideoStudyProgress(String memberId, String sectionId, int clientType,
            int studyTime, int videoTotalTime, Long beginTime, Optional<Integer> lessonLocation) {
        // 学习状态为已完成时，记录完成时间
        if( studyTime > videoTotalTime*10)  studyTime = videoTotalTime* 10;// 不超过10倍总时长
        final int tempTime = studyTime;
        return defaultCourseSectionStudyProgress(memberId, sectionId, clientType, tempTime, beginTime,
                lessonLocation.map(String::valueOf), Optional.empty(), progress -> {
                    int total = Optional.of(progress.getStudyTotalTime()).orElse(0);
                    int completedRate = Optional.ofNullable(progress.getCompletedRate()).orElse(0);
                    if (videoTotalTime == 0)
                        return;

                    if (progress.getFinishStatus() == CourseSectionStudyLog.FINISH_STATUS_STUDY) {
                        int processRate = (total + tempTime) * 100 / videoTotalTime; // 学习时长百分比
                        int localRate = lessonLocation.orElse(0) * 100 / videoTotalTime; // 最后播放时间占百分比
                        if (processRate > 80 && localRate > 80) {
                            progress.setFinishStatus(CourseSectionStudyLog.FINISH_STATUS_FINISH);
                            progress.setCompletedRate(100);
                        } else {
                            completedRate = localRate > completedRate ? localRate : completedRate;
                            if (completedRate == 100) completedRate = 99;
                            progress.setCompletedRate(completedRate);
                        }
                    }
                },System.currentTimeMillis());
    }

    /**
     * 已弃用
     * @param memberId
     * @param sectionId
     * @param clientType
     * @param lessonLocation
     * @param beginTime
     * @return
     */
    @Deprecated
    @Override
    public CourseSectionStudyProgress updateDocStudyProgress(String memberId, String sectionId, int clientType,Optional<Integer> lessonLocation,
            Long beginTime) {
        int studyTime = (int) ((System.currentTimeMillis() - beginTime)/1000);

        if(studyTime > 60*60) studyTime = 3600;// 单次时长上限60;

        CourseChapterSection section = sectionDao.getOptional(sectionId).orElse(null);
        if(section == null ) return null;

        int sectionTime = section.getTimeSecond() * 60 + section.getTimeMinute();
        if(sectionTime!= 0 && studyTime > sectionTime * 10){ // 单次时长超过课件总时长的10倍
            studyTime = sectionTime* 10;
        }

        final int tempTime = studyTime;

        return defaultCourseSectionStudyProgress(memberId, sectionId, clientType, studyTime, beginTime,
                lessonLocation.map(String::valueOf), Optional.empty(), p -> {
                    if (tempTime > 10) { // 点开十秒完成
                        p.setCompletedRate(100);
                        p.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_FINISH);
                    }
                }, System.currentTimeMillis());
    }

    @Override
    public CourseSectionStudyProgress updateUrlStudyProgress(String memberId, String sectionId, int clientType, Optional<Integer> lessonLocation, Long beginTime, List<String> domains) {
        CourseChapterSection section = sectionDao.getOptional(sectionId).orElse(null);
        //是否包含重塑专区的域名，若包含重塑专区，状态不能置为已完成
        boolean hasThirdPartDomain = false;
        for(String domain :domains){
            if(section.getUrl().contains(domain)){
                hasThirdPartDomain = true;
            }
        }
        // 重塑专区并且是内部课程链接，状态也不能设置为已完成
        boolean hasRemodelingAndInternalCourseUrl = false;
        boolean isRemodeling = trainService.findRemodelingRoleBySubjectId(section.getCourseId());
        if (isRemodeling && (section.getUrl().contains("wangda.chinamobile.com/")
                // 重塑专区并且是面授课程 不自动完成的这样不会影响除了重塑外的其他使用了此url的课 但是更改得发版本
                || section.getUrl().contains("default/M00/0E/9D/ChhDAWAP7M6ADLmbAAByefv9nnw504.png")))
        {
            hasRemodelingAndInternalCourseUrl = true;
        }
        boolean finalHasThirdPartDomain = hasThirdPartDomain;
        boolean finalHasRemodelingAndInternalCourseUrl = hasRemodelingAndInternalCourseUrl;
        return defaultCourseSectionStudyProgress(memberId, sectionId, clientType, 0, beginTime,
                lessonLocation.map(String::valueOf), Optional.empty(), p -> {
                    p.setCompletedRate(100);
                    if(!finalHasThirdPartDomain && !finalHasRemodelingAndInternalCourseUrl){
                        p.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_FINISH);
                    }
                }, System.currentTimeMillis());
    }


    @Override
    public CourseSectionStudyProgress submitProgress(String sectionId, String memberId, int clientType,
            Optional<List<CourseSectionProgressAttachment>> sectionAttachments) {
        long currentTime = System.currentTimeMillis();
        return defaultCourseSectionStudyProgress(memberId, sectionId, clientType, 0, currentTime, Optional.of("0"), sectionAttachments,
                process -> {
                    process.setCommitTime(currentTime);
                    process.setCompletedRate(50);
                    process.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_AUDIT);
                    process.setComments(null);
                }, currentTime);
    }

    @Override
    public CourseSectionStudyProgress startStudy(String sectionId, String memberId, int clientType) {
        long currentTime = System.currentTimeMillis();
        // update for xdn 学员端打开作业
        CourseInfo courseInfo = courseCacheServiceSupport.getCourseByReferenceId(sectionId);
//        TableImpl<?> csspTable = shardingConfigService.getTableName(courseInfo.getId(),
//                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil
                .getTable(memberId,courseInfo.getId());
        Optional<CourseSectionStudyProgress> progress = courseSectionStudyProgressCommonDao.execute(e->e.select(csspTable.fields()).from(csspTable)
                .where(csspTable.field("f_member_id", String.class).eq(memberId),
                        csspTable.field("f_section_id", String.class).eq(sectionId)))
                .fetchOptional(r-> new CourseSectionStudyProgress().fill(csspTable, r));

//       Optional<CourseSectionStudyProgress> progress =  courseSectionStudyProgressCommonDao.fetchOne(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId)
//                .and(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.eq(sectionId)));

       if(progress.isPresent()) {
           CourseSectionStudyProgress p = new CourseSectionStudyProgress();
           BeanUtils.copyProperties(progress.get(), p);
           p.setLastAccessTime(currentTime);
           courseCacheServiceSupport.updateProgress(p);

           messageSender.send(MessageTypeContent.COURSE_PROGRESS_UPDATE, MessageHeaderContent.ID, p.getId(),
                   MessageHeaderContent.COURSE_ID, p.getCourseId(),
                   MessageHeaderContent.STUDYTIME, "0", MessageHeaderContent.STUDYCLIENTTYPE,
                   String.valueOf(clientType), MessageHeaderContent.FINISHSTIME, System.currentTimeMillis()+"",
                   MessageHeaderContent.MEMBER_ID , memberId);
           return p;
       } else {
           return defaultCourseSectionStudyProgress(memberId, sectionId, clientType, 0, currentTime, Optional.of("0"), Optional.empty(),
                   process -> {
                       process.setCompletedRate(0);
                       process.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_STUDY);
                   }, System.currentTimeMillis());
       }
    }

    @Override
    public List<CourseSectionStudyLog> getSectionStudyLogs(String memberId, String sectionId) {
        return logDao.fetch(
                Stream.of(COURSE_SECTION_STUDY_LOG.MEMBER_ID.eq(memberId),
                        COURSE_SECTION_STUDY_LOG.SECTION_ID.eq(sectionId)),
                COURSE_SECTION_STUDY_LOG.CREATE_TIME.desc());
    }

    /**
     * =======================================================学习进度代码开始，勿动==================================================
     */

    /**
     *
     * 更新章节进度。 业务不同的地方用consumer处理
     *
     * @return
     */
    private CourseSectionStudyProgress defaultCourseSectionStudyProgress(String memberId, String sectionId,
            Integer clientType, int studyTime, Long beginTime, Optional<String> lessonLocation, Optional<List<CourseSectionProgressAttachment>> sectionAttachments,
            Consumer<CourseSectionStudyProgress> consumer, Long createTime) {
        long now = System.currentTimeMillis();
//        int oldTime;
        CourseChapterSection section = sectionDao.getOptional(sectionId).orElse(null);
        if(section == null ) return null;
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        // add 2019-11-25 修改考试，调研，评估，作业后更新当前学习sectionId
        Optional<CourseStudyProgress> progressOptional = courseStudyProgressCommonDao.execute(x -> x.select(cacheTable.field("f_id",String.class),
                        cacheTable.field("f_current_section_id",String.class), cacheTable.field("f_last_access_time",Long.class)).from(cacheTable)
                .where(cacheTable.field("f_member_id",String.class).eq(memberId).and(cacheTable.field("f_course_id",String.class).eq(section.getCourseId())))
                .fetchOptional(r -> {
                    CourseStudyProgress csp = new CourseStudyProgress();
                    csp.setId(r.getValue(cacheTable.field("f_id",String.class)));
                    csp.setCurrentSectionId(r.getValue(cacheTable.field("f_current_section_id",String.class)));
                    csp.setLastAccessTime(r.getValue(cacheTable.field("f_last_access_time",Long.class)));
                    return csp;
                })
        );

        CourseSectionStudyProgress progress = courseCacheServiceSupport.getProgress(memberId, section.getReferenceId());
        if(progress ==null) {
            log.info("找不到节进度，开始插入memberId={},sectionId={}", memberId, sectionId);
            progress = new CourseSectionStudyProgress();
            progress.setMemberId(memberId);
            progress.setSectionId(section.getReferenceId());
            progress.setCourseId(section.getCourseId());
            progress.setFinishStatus(CourseSectionStudyLog.FINISH_STATUS_UNSTART);
            progress.setBeginTime(beginTime);
        };
        Integer previousState = progress.getFinishStatus();

        if(progress.getBeginTime() == null) {
            progress.setBeginTime(beginTime);
        }
        progress.setCommitTime(createTime);
        progress.setStudyTotalTime(Optional.ofNullable(progress.getStudyTotalTime()).orElse(0) + studyTime);
        progress.setLastAccessTime(createTime);
        lessonLocation.ifPresent(progress::setLessonLocation);
        if (progress.getFinishStatus() == CourseSectionStudyLog.FINISH_STATUS_UNSTART) {
            progress.setFinishStatus(CourseSectionStudyLog.FINISH_STATUS_STUDY);
            progress.setBeginTime(beginTime);
        }


        //lambda表达式组装参数，不同的业务调用组装不同的对象
        if (consumer != null)
            consumer.accept(progress);
        Integer currentState = progress.getFinishStatus();

        //查询的数据状态不等于已完成 且 当前入参即将修改的数据状态等于已完成，设置完成时间
        if(CourseSectionStudyLog.FINISH_STATUS_FINISH==currentState
                && (CourseSectionStudyLog.FINISH_STATUS_FINISH !=previousState)){
            progress.setFinishTime(createTime);
        }

//        // 更新专题的章节进度时需要获取该章节的log流水时长 updated by wangdongyan
//        if (section.getSectionType() == CourseChapterSection.SECTION_TYPE_COURSE) {
//        	BigDecimal oldStudyTime = subjectLogDao.execute(x -> x.select(SUBJECT_SECTION_STUDY_LOG.STUDY_TIME.sum()).from(SUBJECT_SECTION_STUDY_LOG)
//            		.where(SUBJECT_SECTION_STUDY_LOG.MEMBER_ID.eq(memberId).and(SUBJECT_SECTION_STUDY_LOG.SECTION_ID.eq(section.getReferenceId())))
//            		.fetchOne(SUBJECT_SECTION_STUDY_LOG.STUDY_TIME.sum()));
//            oldTime = oldStudyTime == null ? 0 : oldStudyTime.intValue();
//            studyTime = progress.getStudyTotalTime() - oldTime;
//            System.err.println("课程章节的时长：" + progress.getStudyTotalTime() + "课程章节对应的log时长" + oldTime);
//        }

        //记录专题log updated 2019-11-20 log异步更新
        messageSender.send(MessageTypeContent.SUBJECT_INSERT_SECTION_STUDY_LOG_AND_DAY,
                MessageHeaderContent.MEMBER_ID, memberId,
                MessageHeaderContent.COURSE_ID, section.getCourseId(),
                MessageHeaderContent.BUSINESS_ID, section.getReferenceId(),
                MessageHeaderContent.PARAMS, progress.getFinishStatus()+"",
                MessageHeaderContent.STUDYTIME, studyTime+"",
                MessageHeaderContent.FINISHSTIME, createTime+""
                );
//        courseProcessService.insertSubjectLog(memberId, section.getCourseId(), section.getReferenceId(), progress.getFinishStatus(), studyTime);

        if (progress.getId() == null) {
            progress.forInsert();
            // update for xdn
//            TableImpl<?> csspTable = shardingConfigService.getTableName(progress.getCourseId(),
//                    ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                    ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
            TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId,progress.getCourseId());
            CourseSectionStudyProgress cssp = progress;
            courseSectionStudyProgressCommonDao.execute(dslContext -> cssp.insert(csspTable, dslContext)).execute();
            // courseSectionStudyProgressCommonDao.insert(progress);
        } else {
            courseCacheServiceSupport.updateProgress(progress);
        }

        String progressId = progress.getId();

        sectionProgressAttachmentDao
                .delete(COURSE_SECTION_PROGRESS_ATTACHMENT.SECTION_PROGRESS_ID.eq(progress.getId()));
        sectionAttachments.ifPresent(attachments -> attachments.stream().forEach(att -> {
            att.forInsert();
            att.setSectionProgressId(progressId);
            sectionProgressAttachmentDao.insert(att);
        }));

        messageSender.send(MessageTypeContent.COURSE_PROGRESS_UPDATE,
                MessageHeaderContent.ID, progress.getId(),
                MessageHeaderContent.COURSE_ID, progress.getCourseId(),
                MessageHeaderContent.STUDYTIME, String.valueOf(studyTime),
                MessageHeaderContent.STUDYCLIENTTYPE, String.valueOf(clientType),
                MessageHeaderContent.FINISHSTIME, createTime+"",
                MessageHeaderContent.MEMBER_ID , memberId);

        int sectionType = section.getSectionType() != null ? section.getSectionType() : 0;
        int finishStatus = progress.getFinishStatus() != null ? progress.getFinishStatus() : 0;
        if(sectionType == CourseChapterSection.SECTION_TYPE_TASK && finishStatus > CourseSectionStudyProgress.FINISH_STATUS_STUDY) {
            messageSender.send(MessageTypeContent.STUDY_TASK_SUBMIT_AUDIT, MessageHeaderContent.ID, progress.getId(),
                    MessageHeaderContent.COURSE_ID, section.getCourseId(), MessageHeaderContent.BUSINESS_ID, section.getResourceId());
        }
        Optional<Member> commitMember = memberDao.execute(x -> x.select(MEMBER.FULL_NAME).from(MEMBER).where(MEMBER.ID.eq(memberId)).fetchOptional(r -> r.into(Member.class)));
        if(commitMember.isPresent()){
            progress.setCommitMemberName(commitMember.get().getFullName());
        }

        // add 2019-11-25 考试调研评估作业提交后更新当前学习章节id
        String referenceId = progress.getSectionId();
        progressOptional.ifPresent(p -> {
            if (ObjectUtils.isEmpty(p.getCurrentSectionId()) || !p.getCurrentSectionId().equals(referenceId)) {
                Long lastTime = p.getLastAccessTime() == null ? now : p.getLastAccessTime();
                courseStudyProgressCommonDao.execute(x -> x.update(cacheTable)
                        .set(cacheTable.field("f_current_section_id",String.class), referenceId)
                        .set(cacheTable.field("f_last_access_time",Long.class), lastTime > createTime ? lastTime : createTime)
                        .where(cacheTable.field("f_id",String.class).eq(p.getId()))
                        .execute()
                );

                //更新最近学习课程  --  LJY
                if(!section.getCourseId().isEmpty()) {
                    //查询是否有courseId
                    CourseInfo courseInfo = courseInfoDao.execute(x -> x.select(COURSE_INFO.ID,COURSE_INFO.NAME,COURSE_INFO.COVER_PATH,COURSE_INFO.BUSINESS_TYPE).from(COURSE_INFO)
                            .where(COURSE_INFO.ID.eq(section.getCourseId()))).
                            fetchOptional(r -> {
                                CourseInfo info = new CourseInfo();
                                info.setId(r.getValue(COURSE_INFO.ID));
                                info.setName(r.getValue(COURSE_INFO.NAME));
                                info.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH));
                                info.setBusinessType(r.getValue(COURSE_INFO.BUSINESS_TYPE));
                                return info;
                            }).orElse(null);
                    //有此课程并且类型为课程，专题不更新
                    if(courseInfo !=null && courseInfo.getBusinessType() == 0){
                        messageSender.send(MessageTypeContent.STUDY_CARD_UPDATE,
                                MessageHeaderContent.MEMBER_ID, memberId,
                                MessageHeaderContent.COURSE_ID, section.getCourseId());
                    }
                }

  /*              CourseInfo courseInfo =courseInfoDao.get(p.getCourseId());
                if(courseInfo.getBusinessType() == 0){
                    messageSender.send(MessageTypeContent.STUDY_CARD_UPDATE,
                            MessageHeaderContent.MEMBER_ID, p.getMemberId(),
                            MessageHeaderContent.COURSE_ID, p.getCourseId());
                }*/
            }
            // add 2020-4-24 异步更新studyProgress分表数据
            messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE, MessageHeaderContent.ID, p.getId(),MessageHeaderContent.MEMBER_ID,memberId);
        });


        return progress;
    }

    /**
     * MessageTypeContent.COURSE_PROGRESS_UPDATE 消息调用的方法，记录流水，计算进度
     *
     * @param sectionProgressId
     * @param clientType
     * @param studentTime
     * @return
     */
    @Override
    public CourseSectionStudyProgress updateSectionProgressAsync(String sectionProgressId, String courseId, Optional<Integer> clientType,
            Optional<Integer> studentTime, Long commitTime,String memberId) {
        // update for xdn
//        TableImpl<?> csspTable = shardingConfigService.getTableName(courseId,
//                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId,courseId);
        Optional<CourseSectionStudyProgress> courseSectionStudyProgressOptional =
                courseSectionStudyProgressCommonDao.execute(dslContext -> dslContext.select(csspTable.fields())
                    .from(csspTable).where(csspTable.field("f_id", String.class).eq(sectionProgressId)))
                    .fetchOptional(r-> new CourseSectionStudyProgress().fill(csspTable, r));

//        Optional<CourseSectionStudyProgress> courseSectionStudyProgressOptional = courseSectionStudyProgressCommonDao
//                .getOptional(sectionProgressId);
        // 记录流水
        log.info("开始异步插入章节学习日志,sectionProgressId = {}", sectionProgressId);
        courseSectionStudyProgressOptional.ifPresent(courseSectionStudyProgress -> {
            // 需查询当前更新的是否为课程，如果为课程再更新课程的log
            Optional<CourseInfo> courseInfo = courseInfoDao.execute(x -> x.select(COURSE_INFO.BUSINESS_TYPE, COURSE_INFO.ID).from(COURSE_INFO).where(COURSE_INFO.ID.eq(courseSectionStudyProgress.getCourseId()))).fetchOptional(r -> {
                CourseInfo c = new CourseInfo();
                c.setId(r.getValue(COURSE_INFO.ID));
                c.setBusinessType(r.getValue(COURSE_INFO.BUSINESS_TYPE));
                return c;
            });
            if (courseInfo.isPresent() && CourseInfo.BUSINESS_TYPE_COURSE == courseInfo.get().getBusinessType()) {
                insertCourseSectionStudyLog(courseSectionStudyProgress.getMemberId(),
                        courseSectionStudyProgress.getCourseId(), courseSectionStudyProgress.getSectionId(),
                        clientType.orElse(CourseSectionStudyLog.CLIENT_TYPE_PC),
                        courseSectionStudyProgress.getCompletedRate(), courseSectionStudyProgress.getFinishStatus(),
                        studentTime.orElse(0), courseSectionStudyProgress.getCreateTime(),
                        Optional.ofNullable(courseSectionStudyProgress.getSubmitText()),
                        Optional.ofNullable(courseSectionStudyProgress.getAuditMemberId()),
                        Optional.ofNullable(courseSectionStudyProgress.getScore()),
                        Optional.ofNullable(courseSectionStudyProgress.getComments()));
            }
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(courseSectionStudyProgress.getMemberId(), SplitTableConfig.COURSE_STUDY_PROGRESS);
            //旧的课程进度表
            Integer oldTime = courseStudyProgressCommonDao.execute(x -> x.select(cacheTable.field("f_study_total_time",Integer.class)).from(cacheTable)
            .where(cacheTable.field("f_member_id",String.class).eq(courseSectionStudyProgress.getMemberId())
            .and(cacheTable.field("f_course_id",String.class).eq(courseSectionStudyProgress.getCourseId())))
            .fetchOne(cacheTable.field("f_study_total_time",Integer.class)));
            // 更新课程学习进度
            CourseInfo course = courseInfoDao.get(courseSectionStudyProgress.getCourseId());
            CourseStudyProgress progress = updateCourseStudyProgress(courseSectionStudyProgress.getMemberId(),
                    courseSectionStudyProgress.getCourseId(), commitTime);
            // 获取课程基本信息
            // 当前更新进度为课程进度时，同步更新包含该课程的专题进度，否则至此结束
            if (progress != null && course.getBusinessType() != null
                    && course.getBusinessType().equals(CourseInfo.BUSINESS_TYPE_COURSE)){
            	oldTime = oldTime == null ? 0 : oldTime;
            	int studyTotalTime = progress.getStudyTotalTime() == null ? 0 : progress.getStudyTotalTime();
            	int studyTime = studyTotalTime - oldTime < 0 ? 0 : studyTotalTime - oldTime;
                updateSubjectByCourse(progress.getId(), clientType.orElse(CourseSectionStudyLog.CLIENT_TYPE_PC), studyTime, commitTime,progress.getMemberId());
            }
        });
        return courseSectionStudyProgressOptional.orElse(null);
    }

    @Override
    public CourseSectionStudyProgress updateSectionProgressAsync(String sectionId, String memberId, int finishStatus, Integer clientType, Optional<Integer> score, Long beginTime, Long commitTime) {
        CourseChapterSection section = sectionDao.getOptional(sectionId).orElse(null);
        if(section == null ) return null;
        CourseSectionStudyProgress progress = courseCacheServiceSupport.getProgress(memberId, section.getReferenceId());
        return defaultCourseSectionStudyProgress(memberId, sectionId, clientType, 0, beginTime,
                Optional.empty(), Optional.empty(), p -> {
                    p.setFinishStatus(finishStatus);
                    score.ifPresent(p::setScore);
                    if(
                            finishStatus == CourseSectionStudyProgress.FINISH_STATUS_AUDIT
                            || finishStatus == CourseSectionStudyProgress.FINISH_STATUS_STUDY
                            || finishStatus == CourseSectionStudyProgress.FINISH_STATUS_NOT_THROUGH) { // 待评卷/学习中／／审核未通过
                        p.setCompletedRate(50);
                    } else {
                        p.setCompletedRate(100);

                        if (progress == null){
                            p.setFinishTime(commitTime);
                        }else {
                            p.setFinishTime(Optional.ofNullable(progress.getFinishTime()).orElse(commitTime));
                        }
                    }
                }, commitTime);
    }

    /**
     * 新增学习流水；注册、单个用户单次产生的学习动作都需要记录
     *
     * @return
     */
    private CourseStudyLog insertCourseSectionStudyLog(String memberId, String courseId, String sectionId,
            Integer clientType, Integer completedRate, Integer finishType, Integer studyTime, Long commitTime,
            Optional<String> submitText, Optional<String> auditMemberId, Optional<Integer> score,
            Optional<String> comments) {
        //CourseChapterSection section = courseCacheServiceSupport.getSection(sectionId);
    	CourseChapterSection section = sectionDao.getOptional(sectionId).orElse(null);
    	if(section == null || section.getSectionType() == CourseChapterSection.SECTION_TYPE_COURSE) return null;

        String path = orgDao.execute(x -> x.select(ORGANIZATION.PATH).from(ORGANIZATION).leftJoin(MEMBER).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID)).where(MEMBER.ID.eq(memberId)).fetchOne(ORGANIZATION.PATH));
        // updated by wangdongyan 2018-12-11 优化后时长流水分表仍然在使用需要更新数据
        CourseStudyLog log = courseProcessService.insertSubLog(memberId, clientType, section, path);
        if (studyTime > 0) {
//        	// 新增log分表数据的同时新增人-课-天分表的数据
        	String currentDate = this.simpleDateFormateForMonth(log.getCreateTime(), "yyyyMMdd");
//        	courseProcessService.insertOrUpdateCourseSectionLogDay(memberId, section.getCourseId(), currentDate, clientType, studyTime);
            String flag = Optional.ofNullable(cache.get(CacheKeyConstant.CORSE_MEMBER_DAY_STUDY_UPDATE_MESSAGE_TRIGGER, String.class)).orElse("1");
            //如果缓存中是1则发消息，反之不发
            if (flag.equals(CacheKeyConstant.CORSE_MEMBER_DAY_STUDY_UPDATE_MESSAGE_TRIGGER_TRUE)) {
                // add by wangdongyan 异步更新人-课-天数据
                messageSender.send(MessageTypeContent.COURSE_STUDY_LOG_DAY_SPLIT_TIME_UPDATE,
                        MessageHeaderContent.MEMBER_ID, log.getMemberId(),
                        MessageHeaderContent.COURSE_ID, log.getCourseId(),
                        MessageHeaderContent.STUDYDATE, currentDate,
                        MessageHeaderContent.STUDYCLIENTTYPE, log.getClientType() + "",
                        MessageHeaderContent.STUDYTIME, studyTime + "");
            }
        }
        return log;
    }

    /**
     * 更新课程进度
     *
     * @param memberId
     * @param courseId
     * @return
     */
    @Override
    public CourseStudyProgress updateCourseStudyProgress(String memberId, String courseId, Long commitTime) {
        /**
         * 目的 修改时长，修改状态
         */
        long nowTime = System.currentTimeMillis();
        // 查询出用户注册记录
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        log.info("更新课程进度-1116，memberId = {}, courseId = {} , cacheTable={}", memberId, courseId,
                cacheTable);
        List<CourseStudyProgress> courseStudyProgresses = courseStudyProgressCommonDao.execute(dslContext ->
                dslContext
                        .select(cacheTable.fields())
                        .from(cacheTable)
                        .where(cacheTable.field("f_course_id", String.class).eq(courseId), cacheTable.field("f_member_id", String.class).eq(memberId))
                        .fetchInto(CourseStudyProgress.class));
        if(CollectionUtils.isEmpty(courseStudyProgresses) || courseStudyProgresses.get(0) == null) {
            log.error("课程进度找不到 未开始更新,memberId = {}, courseId = {} ", memberId, courseId);
            return null;
        }
        CourseStudyProgress courseStudyProgress = courseStudyProgresses.get(0);
        log.info("开始异步更新课程进,memberId = {}, courseId = {} , courseStudeyProcess[id={}]", memberId, courseId,
                courseStudyProgress.getId());
        // 学习时长 = 所有版本的章节的总学习的时长(不过滤以前删除的章节的学习时长)

//        BigDecimal studentTimes = courseSectionStudyProgressCommonDao.execute(
//                    x -> x.select(COURSE_SECTION_STUDY_PROGRESS.STUDY_TOTAL_TIME.sum()).from(COURSE_SECTION_STUDY_PROGRESS)
//                            .where(COURSE_SECTION_STUDY_PROGRESS.COURSE_ID.eq(courseId)
//                                    .and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId)))
//                            .fetchOne(COURSE_SECTION_STUDY_PROGRESS.STUDY_TOTAL_TIME.sum()));
     // updated by wangdongyan 直接sum求和比较慢

        // update for xdn 更新课程进度,通过section汇总时长
//        TableImpl<?> csspTable = shardingConfigService.getTableName(courseId,
//                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil
                .getTable(memberId,courseId);
        Integer studentTimes = courseSectionStudyProgressCommonDao.execute(
                x -> x.select(csspTable.field("f_study_total_time")).from(csspTable)
                        .where(csspTable.field("f_course_id", String.class).eq(courseId),
                                csspTable.field("f_member_id", String.class).eq(memberId)))
                        .fetch(csspTable.field("f_study_total_time", Integer.class))
        .stream().filter(Objects::nonNull).reduce(0, (a, b) -> a+b);
        int currentStudyTime = studentTimes == null ? 0 : studentTimes;
        if (courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_DEFAULT || courseStudyProgress.getFinishStatus() == CourseStudyProgress.FINISH_STATUS_GIVEUP) {
            courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_STUDY);
        }
    	courseStudyProgress.setStudyTotalTime(currentStudyTime);

        if (!courseStudyProgress.isFinish()) {
            // 1 用户需要必修的进度 = 必修的数量*100
            List<String> referenceIds = sectionDao.execute(x ->
                    x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID).from(COURSE_CHAPTER_SECTION)
                    .innerJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
//                    .leftJoin(COURSE_INFO).on(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(COURSE_INFO.ID))
                    .where(COURSE_CHAPTER.COURSE_ID.eq(courseId)
                            .and(COURSE_CHAPTER.VERSION_ID.eq(courseStudyProgress.getCourseVersionId()))
                            .and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED))
//                            .and(COURSE_INFO.STATUS.notEqual(CourseInfo.STATUS_FIVE_SHELVES)) //过滤退库数据
                    )
                    .fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID);
            if (referenceIds.size() == 0) {
                log.error("需要必修的课程是0，请检查 courseStudyProgress id= {}", courseStudyProgress.getId());
                return courseStudyProgress;
            }
            // 2 用户当前学习版本必修的章节学习进度百分比
            // update for xdn
            Map<String, String> finishSectionIdsMap = courseSectionStudyProgressCommonDao.execute(x ->
                    x.select(csspTable.field("f_section_id")).from(csspTable)
                            .where(csspTable.field("f_member_id", String.class).eq(memberId))
                            .and(csspTable.field("f_course_id", String.class).eq(courseId))
                            .and(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
                                    .or(csspTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                            .fetch(csspTable.field("f_section_id", String.class)))
                    .stream().collect(Collectors.toMap(r -> r, r -> r));
            List<String> finishList = new ArrayList<>();
            referenceIds.forEach(r -> {
                if (!org.jooq.tools.StringUtils.isEmpty(finishSectionIdsMap.get(r))) {
                    finishList.add(r);
                }
            });

//            int finishNum = courseSectionStudyProgressCommonDao.execute(x ->
//                    x.select(COURSE_SECTION_STUDY_PROGRESS.ID.count()).from(COURSE_SECTION_STUDY_PROGRESS)
//                            .where(COURSE_SECTION_STUDY_PROGRESS.SECTION_ID.in(referenceIds))
//                            .and(COURSE_SECTION_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
//                            .and(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH)
//                                    .or(COURSE_SECTION_STUDY_PROGRESS.FINISH_STATUS.eq(CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS)))
//                            .fetchOptional(COURSE_SECTION_STUDY_PROGRESS.ID.count()).orElse(0));

            int finishNum = finishList != null && finishList.size() > 0 ? finishList.size() : 0;
            int completeRate;
            if (referenceIds.size() == finishNum) {
                courseStudyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
                courseStudyProgress.setFinishTime(commitTime == null ? nowTime : commitTime);
                courseStudyProgress.setCompletedRate(100);
                completeRate= 100;
            } else {
                courseStudyProgress.setCompletedRate(50);
                //学习计划完成率
                BigDecimal allCount = BigDecimal.valueOf(referenceIds.size());
                BigDecimal finishCount = BigDecimal.valueOf(finishNum);
                completeRate = finishCount.divide(allCount, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).intValue();//完成率
            }
            // 首次开始学习时间为空，代表当前更新动作为首次学习
            if (courseStudyProgress.getBeginTime() == null) {
                courseStudyProgress.setBeginTime(courseStudyProgress.getCreateTime());
                // 更新学习人数
//                updateStudyMemberCount(courseId);
            }
            //更新学习计划完成率
            messageSender.send(MessageTypeContent.STUDY_PLAN_FINISH_RATE,MessageHeaderContent.MEMBER_ID,memberId,MessageHeaderContent.COURSE_ID,courseId,MessageHeaderContent.RATE,String.valueOf(completeRate));
        }

        // update 2019-11-25 考试调研评估作业提交后当前学习sectionId不在这里更新
        // update for xdn
//        CourseSectionStudyProgress sectionProgress = courseSectionStudyProgressCommonDao.execute(
//                x -> x.select(csspTable.field("f_section_id"), csspTable.field("f_last_access_time"))
//                        .from(csspTable)
//                        .where(csspTable.field("f_course_id", String.class).eq(courseId)
//                                .and(csspTable.field("f_member_id", String.class).eq(memberId)))
//                        .orderBy(csspTable.field("f_last_access_time").desc()).limit(1)
//        ).fetchOne(r->{
//            CourseSectionStudyProgress cssp = new CourseSectionStudyProgress();
//            cssp.setSectionId(r.getValue("f_section_id", String.class));
//            cssp.setLastAccessTime(r.getValue("f_last_access_time", Long.class));
//            return cssp;
//        });
//
//        courseStudyProgress.setCurrentSectionId(sectionProgress.getSectionId()); // 设置最后访问的章节
//        courseStudyProgress.setLastAccessTime(sectionProgress.getLastAccessTime() == null ? System.currentTimeMillis() : sectionProgress.getLastAccessTime());  // 设置最后访问时间
        // add by wangdongyan 分表使用，最后一次修改时间
        courseStudyProgress.setLastModifyTime(System.currentTimeMillis());
        // 3 判断是否修改状态
        courseStudyProgressCommonDao.execute(dsl -> dsl
                        .update(cacheTable)
                        .set(CourseStudyProgress.getUpdateMap(cacheTable,courseStudyProgress))
                        .where(cacheTable.field("f_id",String.class).eq(courseStudyProgress.getId()))
                        .execute()
        );
        // 课程为完成状态时同步课程进度到专题班
        if (courseStudyProgress.getFinishStatus() != null &&
                CourseStudyProgress.FINISH_STATUS_FINISH == courseStudyProgress.getFinishStatus()) {
            messageSender.send(MessageTypeContent.THEMATIC_CLASS_COURSE,
                    MessageHeaderContent.MEMBER_ID,memberId,
                    MessageHeaderContent.COURSE_ID,courseId);
        }

    	messageSender.send(MessageTypeContent.COURSE_STUDY_PROGRESS_UPDATE,
    			MessageHeaderContent.ID, courseStudyProgress.getId(),
    			MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId()
                ,MessageHeaderContent.MEMBER_ID,memberId);


        // 专题完成后颁发证书
        if (courseStudyProgress.getFinishStatus() != null &&
                CourseStudyProgress.FINISH_STATUS_FINISH == courseStudyProgress.getFinishStatus() || CourseStudyProgress.FINISH_STATUS_MARKSUCCESS == courseStudyProgress.getFinishStatus()) {
            messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD,
                    MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId(),
                    MessageHeaderContent.MEMBER_ID, courseStudyProgress.getMemberId());

            // add 2020-6-10 CHBN活动颁证书
            messageSender.send(MessageTypeContent.ISSUE_CERTIFICATE_RECORD_CHBN,
                    MessageHeaderContent.ID, courseStudyProgress.getId(),
                    MessageHeaderContent.MEMBER_ID,courseStudyProgress.getMemberId()
                    );
        }

        // 专题更新完成后，更新引用该专题的父专题
        Long finishTime = commitTime == null ? nowTime : commitTime;
        messageSender.send(MessageTypeContent.SUBJECT_REFERENCE_SUBJECT_PROGRESS_UPDATE,
                MessageHeaderContent.ID, courseStudyProgress.getId(),
                MessageHeaderContent.FINISHSTIME, finishTime + "",MessageHeaderContent.MEMBER_ID,memberId
        );

        // 专题更新后，发消息更新重塑培训进度
        messageSender.send(MessageTypeContent.REMODELING_TRAIN_PLAN_STUDENT_SING_UP,
                MessageHeaderContent.BUSINESS_ID, courseStudyProgress.getCourseId(),
                MessageHeaderContent.MEMBER_ID, courseStudyProgress.getMemberId()
        );

        // add 2020-4-24 异步更新studyProgress分表数据
        messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE,
                MessageHeaderContent.ID, courseStudyProgress.getId(),
                MessageHeaderContent.MEMBER_ID,memberId);

        return courseStudyProgress;
    }

    /**
     * 专题包含此课程的进度直接取课程进度表，课程更新之后 专题进度异步更新。
     *
     * @param studyTime
     * @param memberId
     */
    private void updateSubjectByCourse(String courseStudyProcessId, int clientType, int studyTime, Long commitTime, String memberId) {
        log.info("开始异步更新包含该课程[courseStudyProcessId={}]的章节", courseStudyProcessId);
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        Optional.ofNullable(courseStudyProgressCommonDao.execute(dslContext -> dslContext.select(cacheTable.fields()).from(cacheTable).where(cacheTable.field("f_id", String.class).eq(courseStudyProcessId)).fetchOne(r->r.into(CourseStudyProgress.class))))
        .ifPresent(courseProcess -> {
            // 查询包含该课程的专题的章节id列表
            List<String> sectionIdList = sectionDao
                    .execute(dao -> dao.selectDistinct(COURSE_CHAPTER_SECTION.REFERENCE_ID).from(COURSE_CHAPTER_SECTION)
                            .leftJoin(cacheTable).on(COURSE_CHAPTER_SECTION.COURSE_ID.eq(cacheTable.field("f_course_id",String.class)))
                            .leftJoin(COURSE_CHAPTER).on(COURSE_CHAPTER.ID.eq(COURSE_CHAPTER_SECTION.CHAPTER_ID))
                            .leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID))
                            .where(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(courseProcess.getCourseId())
                                    .and(cacheTable.field("f_member_id",String.class).eq(courseProcess.getMemberId()))
                                    .and(COURSE_CHAPTER.VERSION_ID.eq(COURSE_INFO.VERSION_ID)))
                                .fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID)
                    );
            for (String sectionId : sectionIdList) {
                defaultCourseSectionStudyProgress(courseProcess.getMemberId(),
                        sectionId, clientType, studyTime, courseProcess.getBeginTime(),
                        Optional.ofNullable(courseProcess.getCurrentSectionId()), Optional.empty(), x -> {
                            x.setCompletedRate(courseProcess.getCompletedRate());
                            x.setFinishStatus(courseProcess.getFinishStatus());
                            x.setCommitTime(courseProcess.getLastAccessTime());
                            x.setLastAccessTime(System.currentTimeMillis());
                        }, commitTime);

            }

        });
    }

    /**
     * 更新学习人数
     *
     */
//    private void updateStudyMemberCount(String courseId) {
//        // 统计课程学习人数
//        Integer studyMemberCount = courseStudyProgressCommonDao
//                .execute(dao -> dao.select(Fields.start().add(COURSE_STUDY_PROGRESS.MEMBER_ID.countDistinct()).end())
//                        .from(COURSE_STUDY_PROGRESS)
//                        .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(courseId)
//                                .and(COURSE_STUDY_PROGRESS.FINISH_STATUS.ne(CourseStudyProgress.FINISH_STATUS_DEFAULT)))
//                        .fetchOne().getValue(0, Integer.class));
//        courseInfoDao.execute(dao -> dao.update(COURSE_INFO).set(COURSE_INFO.STUDY_MEMBER_COUNT, studyMemberCount + 1)
//                .where(COURSE_INFO.ID.eq(courseId)).execute());
//    }
    // =========================================核心代码结束=====================================================================================

    @DataSource(type= DataSourceEnum.SLAVE)
    @Override
    public Map<String, Integer> getStudyTimeStatistics(List<String> memberIds, Optional<Long> startTime,
            Optional<Long> endTime) {
    	//  add by wangdongyan 直接使用log表中的sum查询较慢， 查询list集合后java代码累加
    	List<CourseStudyLog> logs = logDao.execute(d -> {
    		// 替换年月
    		Optional<Integer> startOp = Optional.ofNullable(null);
    		Optional<Integer> endOp = Optional.ofNullable(null);
    		if (startTime.isPresent()) {
    			String start = simpleDateFormateForMonth(startTime.get(), "yyyyMM");
    			startOp = Optional.of(Integer.valueOf(start));
    		}
    		if (endTime.isPresent()) {
    			// endTime中都是选择值的第二天0分0秒，减一换算出来正确月份
    			String end = simpleDateFormateForMonth(endTime.get() - 1, "yyyyMM");
    			endOp = Optional.of(Integer.valueOf(end));
    		}
    		// 兼岗多个分表结合查询学习时长
            if (!memberIds.isEmpty() && memberIds.size() == 1) {
                TableImpl<?> table = courseCacheServiceSupport.getCacheTable(memberIds.get(0), SplitTableConfig.COURSE_SECTION_STUDY_LOG_DAY);
                return d.select(table.field("f_app_study_time", Integer.class), table.field("f_pc_study_time", Integer.class))
                        .from(table)
                        .where(table.field("f_member_id", String.class).eq(memberIds.get(0))
                                .and(startOp.map(table.field("f_month", Integer.class)::ge).orElse(DSL.trueCondition()))
                                .and(endOp.map(table.field("f_month", Integer.class)::le).orElse(DSL.trueCondition())))
                .fetch(r -> {
                    CourseStudyLog log = new CourseStudyLog();
                    log.setAppStudyTime(Integer.valueOf(null == r.getValue("f_app_study_time") ? "0" : r.getValue("f_app_study_time").toString()));
                    log.setPcStudyTime(Integer.valueOf(null == r.getValue("f_pc_study_time") ? "0" : r.getValue("f_pc_study_time").toString()));
                    return log;
                });
            }
    		SelectOrderByStep<Record2<Integer, Integer>> tableSumLog = null;
    		for (String id : memberIds) {
    			TableImpl<?> table = courseCacheServiceSupport.getCacheTable(id, SplitTableConfig.COURSE_SECTION_STUDY_LOG_DAY);
    			if (tableSumLog ==  null) {
    				tableSumLog = d.select(table.field("f_app_study_time", Integer.class), table.field("f_pc_study_time", Integer.class))
    						.from(table)
    						.where(table.field("f_member_id", String.class).eq(id)
    								.and(startOp.map(table.field("f_month", Integer.class)::ge).orElse(DSL.trueCondition()))
	                    			.and(endOp.map(table.field("f_month", Integer.class)::le).orElse(DSL.trueCondition())));
    			} else {
    				tableSumLog = tableSumLog.unionAll(d.select(table.field("f_app_study_time", Integer.class), table.field("f_pc_study_time", Integer.class))
    						.from(table)
    						.where(table.field("f_member_id", String.class).eq(id)
    								.and(startOp.map(table.field("f_month", Integer.class)::ge).orElse(DSL.trueCondition()))
	                    			.and(endOp.map(table.field("f_month", Integer.class)::le).orElse(DSL.trueCondition()))));
    			}
			}
    		return d.select(Fields.start().add(tableSumLog.field("f_app_study_time"), tableSumLog.field("f_pc_study_time")).end())
    		.from(tableSumLog).fetch(r -> {
    			CourseStudyLog log = new CourseStudyLog();
	        	log.setAppStudyTime(Integer.valueOf(null == r.getValue("f_app_study_time") ? "0" : r.getValue("f_app_study_time").toString()));
	        	log.setPcStudyTime(Integer.valueOf(null == r.getValue("f_pc_study_time") ? "0" : r.getValue("f_pc_study_time").toString()));
	        	return log;
    		});
    	});

    	Integer appStudyTime = logs.stream().filter(l -> l.getAppStudyTime() > 0 ).map(CourseStudyLog::getAppStudyTime).reduce(0, (a, b) -> a+b);
    	Integer pcStudyTime = logs.stream().filter(l -> l.getPcStudyTime() > 0).map(CourseStudyLog::getPcStudyTime).reduce(0, (a, b) -> a+b);
    	Map<String, Integer> timeMap = new HashMap<>();
    	timeMap.put("0", pcStudyTime);
    	timeMap.put("1", appStudyTime);

//        Map<String, Integer> execute1 = logDao.execute(d -> {
//            Map<String, Integer> map = new HashMap<>();
//            d.select(COURSE_SECTION_STUDY_LOG.CLIENT_TYPE, DSL.sum(COURSE_SECTION_STUDY_LOG.STUDY_TIME))
//                    .from(COURSE_SECTION_STUDY_LOG).where(COURSE_SECTION_STUDY_LOG.MEMBER_ID.in(memberIds))
//                    .and(startTime.map(COURSE_SECTION_STUDY_LOG.CREATE_TIME::ge).orElse(DSL.trueCondition()))
//                    .and(endTime.map(COURSE_SECTION_STUDY_LOG.CREATE_TIME::lt).orElse(DSL.trueCondition()))
//                    .groupBy(COURSE_SECTION_STUDY_LOG.CLIENT_TYPE).fetch().forEach(r -> {
//                        map.put(r.getValue(COURSE_SECTION_STUDY_LOG.CLIENT_TYPE, String.class),
//                                r.getValue(DSL.sum(COURSE_SECTION_STUDY_LOG.STUDY_TIME), Integer.class));
//                    });
//            return map;
//        });

        // 去掉查询2017年的时长接口，目前系统只有2018和2019年的
        // 根据人员id查询员工编码
//        List<String> memberNameList = memberDao.execute(d -> d.select(MEMBER.NAME).from(MEMBER).where(MEMBER.ID.in(memberIds))).fetch(MEMBER.NAME);

//        Map<String, Integer> execute2 = StudyRecord_2017Dao.execute(d -> {
//            Map<String, Integer> map = new HashMap<String, Integer>();
//            d.select(STUDY_RECORD_2017.CLIENT, DSL.sum(STUDY_RECORD_2017.DURATION))
//            .from(STUDY_RECORD_2017).where(STUDY_RECORD_2017.PERSON_CODE.in(memberNameList))
//            .and(startTime.map(this.datetime2Long(STUDY_RECORD_2017.START_TIME)::ge).orElse(DSL.trueCondition()))
//            .and(endTime.map(this.datetime2Long(STUDY_RECORD_2017.START_TIME)::lt).orElse(DSL.trueCondition()))
//            .groupBy(STUDY_RECORD_2017.CLIENT).fetch().forEach(r -> {
//                map.put(r.getValue(STUDY_RECORD_2017.CLIENT, String.class),
//                        r.getValue(DSL.sum(STUDY_RECORD_2017.DURATION), Integer.class));
//            });
//            return map;
//        });
//        Map<String, Integer> map = new HashMap<String, Integer>();
//        map = addTo(map,timeMap);
//        map = addTo(map,execute2);
        return timeMap;
    }



	/**
     * 合并map
     * @param target
     * @param plus
     * @return
     */
    public static Map<String,Integer> addTo(Map<String,Integer> target, Map<String,Integer> plus) {
        if (Objects.nonNull(plus) && !plus.isEmpty()){
            plus.entrySet().forEach( e -> {
                String key = e.getKey();
                Integer value = Optional.ofNullable(e.getValue()).orElse(0);
                if (target.containsKey(key)){
                    target.put(key, Optional.ofNullable(target.get(key)).orElse(0) + value);
                } else {
                    target.put(key, value);
                }
            });
        }
        return target;
    }

    // 已弃用
    @Deprecated
    @Override
    public Integer getPersonStudyTotalTime(String memberId) {
       return courseStudyProgressCommonDao.execute(d -> d.select(DSL.sum(COURSE_SECTION_STUDY_LOG.STUDY_TIME))
                    .from(COURSE_SECTION_STUDY_LOG)
                    .where(COURSE_SECTION_STUDY_LOG.MEMBER_ID.eq(memberId))
                    .fetchOne().getValue(0, Integer.class));
    }

    @Override
    public Integer totalStudyMember(String courseId) {
        return cache.get("course-total-study-number" + courseId, () -> {
            return courseStudyProgressCommonDao.execute(dslContext ->
                    dslContext.select(COURSE_STUDY_PROGRESS.MEMBER_ID.count())
                            .from(COURSE_STUDY_PROGRESS).where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(courseId))
                            .fetchOne(COURSE_STUDY_PROGRESS.MEMBER_ID.count())
            );
        }, 5 * 60);

    }

    /**
     * 个人中心我的档案:课程、专题学习记录，分页查询
     */
    @Override
    public PagedResult<CourseStudyProgress> personPage(int page, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy, Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired,
                                                       Optional<Long> startTime, Optional<Long> endTime) {
        return courseStudyProgressCommonDao.execute(dslContext -> {
            SortField<?> sf = COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME.desc().nullsLast();

//
//            Condition c = Stream.of(
//                    name.map(COURSE_INFO.NAME::contains),
//                    finishStatus.map(s -> {
//                        if (s == CourseStudyProgress.FINISH_STATUS_FINISH) {
//                            return COURSE_STUDY_PROGRESS.FINISH_STATUS.in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
//                        }
//                        return COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(s);
//                    }),
//                    isRequired.map(COURSE_STUDY_PROGRESS.IS_REQUIRED::eq),
//                    findStudy.map(study -> COURSE_STUDY_PROGRESS.FINISH_STATUS.ne(CourseStudyProgress.FINISH_STATUS_GIVEUP))
//            ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());

            int firstResult = (page - 1) * pageSize;

            // 若为课程，则关联过程数据联合查询
            if (businessType == 0) {
                Table<Record> table1 = dslContext.select(
                        Fields.start()
                                .add(COURSE_STUDY_PROGRESS.ID.as("id"), COURSE_STUDY_PROGRESS.FINISH_STATUS.as("finish_status"),
                                        COURSE_STUDY_PROGRESS.FINISH_TIME.as("finish_time"),
                                        COURSE_STUDY_PROGRESS.REGISTER_TIME.as("register_time"),
                                        COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.as("study_total_time"),
                                        COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME.as("last_access_time"),
                                        COURSE_STUDY_PROGRESS.COURSE_ID.as("course_id"),
                                        COURSE_STUDY_PROGRESS.IS_REQUIRED.as("is_required")
                                        , COURSE_STUDY_PROGRESS.BEGIN_TIME.as("begin_time"), COURSE_STUDY_PROGRESS.TYPE.as("type"),
                                        COURSE_STUDY_PROGRESS.CREATE_TIME.as("create_time"))
                                .add(COURSE_INFO.NAME.as("name"), COURSE_INFO.COVER.as("cover"), COURSE_INFO.COVER_PATH.as("cover_path"),
                                        COURSE_INFO.URL.as("url"), COURSE_INFO.VERSION_ID.as("version_id"),
                                        COURSE_INFO.STATUS.as("status"), COURSE_INFO.PUBLISH_CLIENT.as("publish_client"))
                                .end())
                        .from(COURSE_STUDY_PROGRESS).leftJoin(COURSE_INFO).on(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID))
                        .where(COURSE_INFO.BUSINESS_TYPE.eq(businessType))
                        .and(COURSE_STUDY_PROGRESS.MEMBER_ID.in(currentMemberId)).asTable("table1");

                // 学习过程表数据
                Field<Integer> asId = STUDY_RECORD_2017.COURSE_ID.as("id");
                Field<Integer> asStatus = STUDY_RECORD_2017.STATUS.as("finish_status");
                Field<Long> asFinishTime = this.datetime2Long(STUDY_RECORD_2017.END_TIME).as("finish_time");
                Field<String> asRegisterTime = STUDY_RECORD_2017.PLACEHOLDER.as("register_time");
                Field<Long> asDuration = STUDY_RECORD_2017.DURATION.as("study_total_time");
//                Field<String> asLastAccessTime = STUDY_RECORD_2017.PLACEHOLDER.as("last_access_time");
                Field<String> asIsRequired = STUDY_RECORD_2017.PLACEHOLDER.as("is_required");
                Field<Long> asBeginTime = this.datetime2Long(STUDY_RECORD_2017.START_TIME).as("begin_time");
                Field<String> asType = STUDY_RECORD_2017.PLACEHOLDER.as("type");
                Field<String> asCover = STUDY_RECORD_2017.PLACEHOLDER.as("cover");
                Field<String> asCoverPath = STUDY_RECORD_2017.PLACEHOLDER.as("cover_path");
                Field<String> asUrl = STUDY_RECORD_2017.PLACEHOLDER.as("url");
                Field<String> asVersionId = STUDY_RECORD_2017.PLACEHOLDER.as("version_id");
                Field<String> asPublishClient = STUDY_RECORD_2017.PLACEHOLDER.as("publish_client");
                Field<Long> asLastAccessTime = DSL.nvl(this.datetime2Long(STUDY_RECORD_2017.END_TIME),this.datetime2Long(STUDY_RECORD_2017.START_TIME)).as("last_access_time");
                //年度的改为按照创建时间来查询数据
                Field<Long> asCreateTime = DSL.nvl(this.datetime2Long(STUDY_RECORD_2017.END_TIME),this.datetime2Long(STUDY_RECORD_2017.START_TIME)).as("create_time");

                Table<Record> table2 = dslContext.select(
                        Fields.start()
                                .add(asId, asStatus, asFinishTime, asRegisterTime, asDuration,asLastAccessTime,
                                        STUDY_RECORD_2017.COURSE_ID.as("course_id"), asIsRequired,
                                        asBeginTime, asType, STUDY_RECORD_2017.COURSE_NAME.as("name"), asCover, asCoverPath,
                                        asUrl, asVersionId, STUDY_RECORD_2017.STATUS.as("status"), asPublishClient, asCreateTime)
                                .end())
                        .from(STUDY_RECORD_2017).leftJoin(MEMBER).on(STUDY_RECORD_2017.PERSON_CODE.eq(MEMBER.NAME))
                        .where(MEMBER.ID.in(currentMemberId)).asTable("table2");


                Table<Record> unionTable = dslContext.select(Fields.start().add(table1.field("id"))
                        .add(table1.field("finish_status")).add(table1.field("finish_time")).add(table1.field("register_time"))
                        .add(table1.field("study_total_time")).add(table1.field("last_access_time")).add(table1.field("course_id"))
                        .add(table1.field("is_required")).add(table1.field("begin_time")).add(table1.field("type"))
                        .add(table1.field("name")).add(table1.field("cover")).add(table1.field("cover_path"))
                        .add(table1.field("url")).add(table1.field("version_id")).add(table1.field("status"))
                        .add(table1.field("publish_client")).add(table1.field("create_time")).end()).from(table1)
                        .unionAll(dslContext.select(Fields.start().add(table2.field("id"))
                                .add(table2.field("finish_status")).add(table2.field("finish_time")).add(table2.field("register_time"))
                                .add(table2.field("study_total_time")).add(table2.field("last_access_time")).add(table2.field("course_id"))
                                .add(table2.field("is_required")).add(table2.field("begin_time")).add(table2.field("type"))
                                .add(table2.field("name")).add(table2.field("cover")).add(table2.field("cover_path"))
                                .add(table2.field("url")).add(table2.field("version_id")).add(table2.field("status"))
                                .add(table2.field("publish_client")).add(table2.field("create_time")).end()).from(table2)).asTable("unionTable");
                Condition processCondition = Stream.of(
                        name.map(x-> unionTable.field("name", String.class).contains(x)),
                        startTime.map(unionTable.field("create_time", Long.class)::ge),
                        endTime.map(unionTable.field("create_time", Long.class)::le)
                ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());

                List<CourseStudyProgress> progressList = dslContext.select(Fields.start().add(unionTable.field("id"))
                        .add(unionTable.field("finish_status")).add(unionTable.field("finish_time")).add(unionTable.field("register_time"))
                        .add(unionTable.field("study_total_time")).add(unionTable.field("last_access_time")).add(unionTable.field("course_id"))
                        .add(unionTable.field("is_required")).add(unionTable.field("begin_time")).add(unionTable.field("type"))
                        .add(unionTable.field("name")).add(unionTable.field("cover")).add(unionTable.field("cover_path"))
                        .add(unionTable.field("url")).add(unionTable.field("version_id")).add(unionTable.field("status"))
                        .add(unionTable.field("publish_client")).end()).from(unionTable)
                        .where(processCondition)
                        .orderBy(unionTable.field("last_access_time").desc().nullsLast(), unionTable.field("create_time").desc()).limit(firstResult, pageSize)
                .fetch(r -> {
                    CourseStudyProgress csp = new CourseStudyProgress();
                    csp.setId(this.getStringOfObject(r.getValue("id")));

                    CourseInfo ci = new CourseInfo();
                    ci.setId(this.getStringOfObject(r.getValue("course_id")));
                    ci.setName(this.getStringOfObject(r.getValue("name")));
                    csp.setCourseInfo(ci);

                    csp.setBeginTime(Long.valueOf(null != r.getValue("begin_time") ? r.getValue("begin_time").toString() : "0"));
                    csp.setFinishTime(Long.valueOf(null != r.getValue("finish_time") ? r.getValue("finish_time").toString() : "0"));
                    csp.setStudyTotalTime(Integer.valueOf(null != r.getValue("study_total_time") ? r.getValue("study_total_time").toString() : "0"));
                    csp.setFinishStatus(Integer.valueOf(null != r.getValue("finish_status") ? r.getValue("finish_status").toString() : "0"));
                    csp.setLastAccessTime(Optional.ofNullable(r.getValue("last_access_time")).map(x->Long.valueOf(x.toString())).orElse(0L));
                    csp.setType(Integer.valueOf(null != r.getValue("type") ? r.getValue("type").toString() : "1"));
                    csp.setIsRequired(Integer.valueOf(null != r.getValue("is_required") ? r.getValue("is_required").toString() : "1"));
                    return csp;
                });

                Integer count = dslContext.selectCount().from(unionTable).where(processCondition).fetchOne(0, Integer.class);

                return PagedResult.create(count, progressList);
            } else {
                SelectSelectStep<Record> listSelect = dslContext.select(
                        Fields.start()
                                .add(COURSE_STUDY_PROGRESS.ID,COURSE_STUDY_PROGRESS.FINISH_STATUS,COURSE_STUDY_PROGRESS.FINISH_TIME,COURSE_STUDY_PROGRESS.REGISTER_TIME,
                                        COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME,COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME,COURSE_STUDY_PROGRESS.COURSE_ID,COURSE_STUDY_PROGRESS.IS_REQUIRED
                                        ,COURSE_STUDY_PROGRESS.BEGIN_TIME, COURSE_STUDY_PROGRESS.TYPE)
                                .add(COURSE_INFO.NAME, COURSE_INFO.COVER, COURSE_INFO.COVER_PATH, COURSE_INFO.URL, COURSE_INFO.VERSION_ID, COURSE_INFO.STATUS, COURSE_INFO.PUBLISH_CLIENT)
                                .end());
                SelectSelectStep<Record> countSelect = dslContext.select(Fields.start().add(COURSE_STUDY_PROGRESS.ID.count()).end());

                Condition condition = Stream.of(
                        name.map(x-> COURSE_INFO.NAME.contains(x)),
                        startTime.map(COURSE_STUDY_PROGRESS.CREATE_TIME::ge),
                        endTime.map(COURSE_STUDY_PROGRESS.CREATE_TIME::le)
                ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());

                Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a -> {
                    SelectConditionStep<Record> ret = a.from(COURSE_STUDY_PROGRESS)
                            .leftJoin(COURSE_INFO).on(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID))
                            .where(condition).and(COURSE_INFO.BUSINESS_TYPE.eq(businessType))
                            .and(COURSE_STUDY_PROGRESS.MEMBER_ID.in(currentMemberId));

                    return ret;
                };


                Result<Record> result =stepFunc.apply(listSelect).orderBy(sf, COURSE_STUDY_PROGRESS.CREATE_TIME.desc()).limit(firstResult, pageSize).fetch();
                List<CourseStudyProgress> progressList = result.into(COURSE_STUDY_PROGRESS).into(CourseStudyProgress.class);
                List<CourseInfo> infos = result.into(COURSE_INFO).into(CourseInfo.class);

                Integer count = stepFunc.apply(countSelect).fetchOne(0, Integer.class);

                IntStream.range(0, progressList.size()).forEach(index->
                        progressList.get(index).setCourseInfo(infos.get(index))
                );
                return PagedResult.create(count, progressList);
            }
        });
    }

    /**
     * 个人中心我的档案:课程、专题学习记录，查所有记录，用于导出（已弃用）
     */
    @Deprecated
    @Override
    public List<CourseStudyProgress> personAll(Integer businessType, List<String> currentMemberId) {
        Map<String, List<String>> tableMemberIdMap = new HashMap<>();
        Map<String, TableImpl<?>> tableMap = new HashMap<>();
        for (String id : currentMemberId) {
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(id, SplitTableConfig.COURSE_STUDY_PROGRESS);
            tableMemberIdMap.computeIfAbsent(cacheTable.getName(), c-> new ArrayList<>()).add(id);
            tableMap.put(cacheTable.getName(),cacheTable);

        }
        List<CourseStudyProgress> items = new ArrayList<>();
        for (Map.Entry<String, List<String>> stringListEntry : tableMemberIdMap.entrySet()) {
            TableImpl<?> table = tableMap.get(stringListEntry.getKey());
            List<String> ids = stringListEntry.getValue();
            Field<String> courseName = COURSE_INFO.NAME.as("course_name");
            SelectOnConditionStep<?> step = courseStudyProgressCommonDao.execute(e -> e
                    .selectDistinct(Fields.start().add(table.fields()).add(courseName).end()).from(table)
                    .leftJoin(COURSE_INFO).on(table.field("f_course_id",String.class).eq(COURSE_INFO.ID)));
            Condition c = COURSE_INFO.BUSINESS_TYPE.eq(businessType).and(table.field("f_member_id",String.class).in(ids));
            // .orderBy(COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME.desc())
            List<CourseStudyProgress> fetch = step.where(c).fetch(item -> {
                CourseStudyProgress progress = item.into(CourseStudyProgress.class);
                CourseInfo course = new CourseInfo();
                course.setName(item.getValue(courseName));
                progress.setCourseInfo(course);
                return progress;
            });
            items.addAll(fetch);
        }
        items = items.stream().sorted(Comparator.comparingLong(CourseStudyProgress::getLastAccessTime).reversed()).collect(Collectors.toList());
        return items;
    }

    @Override
    public Integer getCurrentMemberStudyTimeRank(String memberId , String organizationId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return courseStudyProgressCommonDao.execute(e -> {
            Field<BigDecimal> sumStudyTotalTime = cacheTable.field("f_study_total_time",Integer.class).sum().as("sum_study_total_time");
            Field<String> tMemberId = cacheTable.field("f_member_id",String.class).as("member_id");

            // 按照人分组统计学习时长
            Table<Record2<String, BigDecimal>> totalStudyGroupByMember = e.select(tMemberId, sumStudyTotalTime)
                    .from(cacheTable)
                    .leftJoin(COURSE_INFO).on(cacheTable.field("f_course_id",String.class).eq(COURSE_INFO.ID))
                    .where(COURSE_INFO.field("f_business_type",Integer.class).eq(CourseInfo.BUSINESS_TYPE_COURSE))
                    .and(cacheTable.field("f_study_total_time",Integer.class).gt(0))
                    .groupBy(tMemberId).asTable("total_study_group_by_member");

            Integer count = e.select(totalStudyGroupByMember.field(sumStudyTotalTime).count())
                    .from(totalStudyGroupByMember)
                    .leftJoin(MEMBER).on(totalStudyGroupByMember.field(tMemberId).eq(MEMBER.ID))
                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                    .where(ORGANIZATION_DETAIL.ROOT.eq(organizationId))
                    .and(totalStudyGroupByMember.field(sumStudyTotalTime).greaterOrEqual(
                            e.select(cacheTable.field("f_study_total_time",Integer.class).sum())
                                    .from(cacheTable)
                                    .leftJoin(COURSE_INFO).on(cacheTable.field("f_course_id",String.class).eq(COURSE_INFO.ID))
                                    .where(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                                    .and(cacheTable.field("f_study_total_time",Integer.class).gt(0))
                                    .and(cacheTable.field("f_member_id",String.class).eq(memberId))
                                    .groupBy(cacheTable.field("f_member_id",String.class))
                                    .fetchOptional(r -> r.getValue(0, BigDecimal.class)).orElse(null)
                    )).fetchOne()
                    .getValue(0, Integer.class);
            return count;
        });
    }

    @Override
    public Integer getListByCurrentMemberId(String currentMemberId, String businessId, List<String>exceludeIds) {
        return cache.get("current-ranking" + businessId + currentMemberId, () -> {
        	return courseStudyProgressCommonDao.execute(dao -> {
            Field<String> courseId = COURSE_STUDY_PROGRESS.COURSE_ID.as("courseId");
            Field<String> progressId = COURSE_STUDY_PROGRESS.ID.as("progressId");
            Field<Integer> studyTotalTime = COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.as("studyTotalTime");

            Table<Record> progressTable = dao.select(Fields.start().add(
                    progressId,
                    courseId,
                    COURSE_STUDY_PROGRESS.MEMBER_ID.as("memberId"),
                    COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.as("studyTotalTime"))
                    .end())
                    .from(COURSE_STUDY_PROGRESS)
                    .where(COURSE_STUDY_PROGRESS.COURSE_ID.eq(businessId).and(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.gt(60)).and(COURSE_STUDY_PROGRESS.MEMBER_ID.notIn(exceludeIds)))
                    .orderBy(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.desc()).asTable("progress");

            Condition condition = dao.select(Fields.start()
                    .add(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME,COURSE_STUDY_PROGRESS.REGISTER_TIME).end())
                    .from(COURSE_STUDY_PROGRESS)
                    .where(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(currentMemberId)
                            .and(COURSE_STUDY_PROGRESS.COURSE_ID.eq(businessId)))
                    .fetchOptional(r -> r.into(CourseStudyProgress.class))
                    .map(p -> {
                        return progressTable.field(studyTotalTime).ge(p.getStudyTotalTime());
                    }).orElse(DSL.trueCondition());


            return dao.select(progressTable.field(progressId).count())
                .from(progressTable)
                    .where(condition).and(progressTable.field(courseId).eq(businessId))
                    .fetchOptional(COURSE_STUDY_PROGRESS.ID.count()).orElse(1);
        });
        }, 2 * 60);
    }

    @Override
    public List<StudyRank> rankForMemberStudyTime(String organizationId, int size) {

        return courseStudyProgressCommonDao.execute(e -> {
            Field<BigDecimal> sumStudyTotalTime = COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.sum().as("sum_study_total_time");
            Field<String> memberId = COURSE_STUDY_PROGRESS.MEMBER_ID.as("member_id");

            // 按照人分组统计学习时长
            Table<Record2<String, BigDecimal>> totalStudyGroupByMember = e.select(memberId, sumStudyTotalTime)
                    .from(COURSE_STUDY_PROGRESS)
                    .leftJoin(COURSE_INFO).on(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID))
                    .where(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                    .and(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.gt(0))
                    .groupBy(memberId).asTable("total_study_group_by_member");

            List<StudyRank> list = e.select(Fields.start().add(MEMBER.ID).add(MEMBER.FULL_NAME).add(MEMBER.HEAD_PORTRAIT).add(ORGANIZATION.NAME).add(totalStudyGroupByMember.field(sumStudyTotalTime)).end())
                    .from(totalStudyGroupByMember).leftJoin(MEMBER).on(totalStudyGroupByMember.field(memberId).eq(MEMBER.ID))
                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                    .where(ORGANIZATION_DETAIL.ROOT.eq(organizationId))
                    .orderBy(totalStudyGroupByMember.field(sumStudyTotalTime).desc())
                    .limit(0, size).fetch(r -> {
                        StudyRank studyRank = new StudyRank();
                        studyRank.setMemberId(r.getValue(MEMBER.ID));
                        studyRank.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                        studyRank.setMemberName(r.getValue(MEMBER.FULL_NAME));
                        studyRank.setHeadPortrait(r.getValue(MEMBER.HEAD_PORTRAIT));
                        studyRank.setStudyTotalTime(Optional.ofNullable(r.getValue(totalStudyGroupByMember.field(sumStudyTotalTime)).intValue()).orElse(0));
                        return studyRank;
                    });
            return list;
        });

    }

    @Override
    public Integer getCurrentMemberCourseRank(String memberId,String organizationId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return courseStudyProgressCommonDao.execute(e -> {
                Field<String> tMemberId = cacheTable.field("f_member_id",String.class).as("member_id");
                Field<Integer> courseCount = cacheTable.field("f_id",String.class).count().as("course_count");
                // 按照人分组统计完成的课程数据
                Table<Record2<String, Integer>> courseCountGroupByMember = e.select(tMemberId,courseCount)
                        .from(cacheTable)
                        .leftJoin(COURSE_INFO).on(cacheTable.field("f_course_id",String.class).eq(COURSE_INFO.ID))
                        .where(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                        .and(cacheTable.field("f_finish_status",Integer.class).eq(CourseStudyProgress.FINISH_STATUS_FINISH)
                                .or(cacheTable.field("f_finish_status",Integer.class).eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                        .groupBy(tMemberId).asTable("course_count_group_by_member");

                Integer count = e.select(courseCountGroupByMember.field(courseCount).count())
                        .from(courseCountGroupByMember)
                            .leftJoin(MEMBER).on(courseCountGroupByMember.field(tMemberId).eq(MEMBER.ID))
                            .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                            .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                            .where(ORGANIZATION_DETAIL.ROOT.eq(organizationId))
                            .and(courseCountGroupByMember.field(courseCount).greaterOrEqual(
                                    e.select(cacheTable.field("f_id",String.class).count())
                                            .from(cacheTable)
                                            .leftJoin(COURSE_INFO).on(cacheTable.field("f_course_id",String.class).eq(COURSE_INFO.ID))
                                            .where(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                                            .and(cacheTable.field("f_member_id",String.class).eq(memberId))
                                            .and(cacheTable.field("f_finish_status",Integer.class).eq(CourseStudyProgress.FINISH_STATUS_FINISH)
                                                    .or(cacheTable.field("f_finish_status",Integer.class).eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                                            .groupBy(cacheTable.field("f_member_id",String.class))
                                            .fetchOptional(r -> r.getValue(0, Integer.class)).orElse(0)
                            )).fetchOne()
                                    .getValue(0, Integer.class);
            return count;
        });
    }


    @Override
    public Map<String, Integer> getSectionStudyMemberCount(List<String> ids,String memberId) {
        // update for xdn 修改课程专题时会查询
        CourseInfo courseInfo = courseCacheServiceSupport.getCourseByReferenceId(ids.get(0));
//        TableImpl<?> csspTable = shardingConfigService.getTableName(courseInfo.getId(),
//                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
//                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil
                .getTable(memberId,courseInfo.getId());
        // ids不会太长,且有覆盖索引,性能很好,可以遍历查询
        Map<String, Integer> map = new HashMap<>();
        ids.forEach(id -> {
            Optional<String> optional = courseSectionStudyProgressCommonDao.execute(dao ->
                    dao.select(csspTable.field("f_section_id")).from(csspTable)
                    .where(csspTable.field("f_section_id", String.class).eq(id)).limit(1))
                    .fetchOptional(csspTable.field("f_section_id", String.class));
            optional.ifPresent(sectionId -> map.put(sectionId, 1));
        });
        return map;

//        return courseSectionStudyProgressCommonDao.execute(dao -> dao.select(Fields.start()
//                .add(csspTable.field("f_section_id"), csspTable.field("f_member_id", String.class).countDistinct())
//                .end())
//                .from(csspTable)
//                .where(csspTable.field("f_section_id", String.class).in(ids))
//                .groupBy(csspTable.field("f_section_id", String.class))
//                .fetchMap(csspTable.field("f_section_id", String.class),
//                        csspTable.field("f_member_id", String.class).countDistinct()));
    }

    @Override
    public CourseSectionStudyProgress insertSectionProgress(CourseSectionStudyProgress progress) {
        // update for xdn
        TableImpl<?> csspTable = shardingConfigService.getTableName(progress.getCourseId(),
                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
        courseSectionStudyProgressCommonDao.execute(dslContext -> progress.insert(csspTable, dslContext)).execute();
        return progress;
        // return courseSectionStudyProgressCommonDao.insert(progress);
    }

    @Override
    public List<CourseSectionStudyProgress> findSectionProgressByCourseId(String courseId, String memberId) {
        // update for xdn
        /*TableImpl<?> csspTable = shardingConfigService.getTableName(courseId,
                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
       */
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, courseId);
        return courseSectionStudyProgressCommonDao.execute(dao ->
                dao.select(Fields.start()
                        .add(csspTable.field("f_id"),
                                csspTable.field("f_member_id"),
                                csspTable.field("f_course_id"),
                                csspTable.field("f_section_id"),
                                csspTable.field("f_begin_time"),
                                csspTable.field("f_finish_status"),
                                csspTable.field("f_finish_time"),
                                csspTable.field("f_study_total_time"),
                                csspTable.field("f_score")
                        )
                        .end()).from(csspTable)
                        .where(csspTable.field("f_course_id", String.class).eq(courseId)
                                .and(csspTable.field("f_member_id", String.class).eq(memberId)))
                        .fetch(r->{
                            CourseSectionStudyProgress cssp = new CourseSectionStudyProgress();
                            cssp.setId(r.get("f_id", String.class));
                            cssp.setMemberId(r.get("f_member_id", String.class));
                            cssp.setCourseId(r.get("f_course_id", String.class));
                            cssp.setSectionId(r.get("f_section_id", String.class));
                            cssp.setBeginTime(r.get("f_begin_time", Long.class));
                            cssp.setFinishStatus(r.get("f_finish_status", Integer.class));
                            cssp.setFinishTime(r.get("f_finish_time", Long.class));
                            cssp.setStudyTotalTime(r.get("f_study_total_time", Integer.class));
                            cssp.setScore(r.get("f_score", Integer.class));
                            return cssp;
                        }));

    }

    @Override
    public List<StudyRank> rankForMemberCourse(String organizationId, int size) {
        return courseStudyProgressCommonDao.execute(e -> {
            Field<String> memberId = COURSE_STUDY_PROGRESS.MEMBER_ID.as("member_id");
            Field<Integer> courseCount = COURSE_STUDY_PROGRESS.ID.count().as("course_count");
            // 按照人分组统计完成的课程数据
            Table<Record2<String, Integer>> courseCountGroupByMember = e.select(memberId,courseCount)
                    .from(COURSE_STUDY_PROGRESS)
                    .leftJoin(COURSE_INFO).on(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID))
                    .where(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                    .and(COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(CourseStudyProgress.FINISH_STATUS_FINISH)
                            .or(COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                    .groupBy(memberId).asTable("course_count_group_by_member");

            List<StudyRank> list = e.select(Fields.start().add(MEMBER.ID).add(MEMBER.FULL_NAME).add(MEMBER.HEAD_PORTRAIT).add(ORGANIZATION.NAME).add(courseCountGroupByMember.field(courseCount)).end())
                    .from(courseCountGroupByMember).leftJoin(MEMBER).on(courseCountGroupByMember.field(memberId).eq(MEMBER.ID))
                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID))
                    .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION.ID.eq(ORGANIZATION_DETAIL.SUB))
                    .where(ORGANIZATION_DETAIL.ROOT.eq(organizationId))
                    .orderBy(courseCountGroupByMember.field(courseCount).desc())
                    .limit(0,size).fetch(r -> {
                        StudyRank studyRank = new StudyRank();
                        studyRank.setMemberId(r.getValue(MEMBER.ID));
                        studyRank.setOrganizationName(r.getValue(ORGANIZATION.NAME));
                        studyRank.setMemberName(r.getValue(MEMBER.FULL_NAME));
                        studyRank.setHeadPortrait(r.getValue(MEMBER.HEAD_PORTRAIT));
                        studyRank.setCourseCount(Optional.ofNullable(r.getValue(courseCountGroupByMember.field(courseCount))).orElse(0));
                        return studyRank;
                    });
            return list;
        });
    }

    @Override
    public CourseStudyProgress giveUpStudyProgress(String id) {
        CourseStudyProgress courseStudyProgre = courseStudyProgressCommonDao.get(id);
        courseStudyProgre.setFinishStatus(CourseStudyProgress.FINISH_STATUS_GIVEUP);
        courseStudyProgre.setLastAccessTime(System.currentTimeMillis());
        // add by wangdongyan 分表使用，最后一次修改时间
        courseStudyProgre.setLastModifyTime(System.currentTimeMillis());
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(courseStudyProgre.getMemberId(), SplitTableConfig.COURSE_STUDY_PROGRESS);

        Field<?>[] fields = CourseStudyProgress.getFields(cacheTable);
        Object[] values = CourseStudyProgress.getValues(courseStudyProgre);
        Map<Field<?>,Object> map = new HashMap<>();
        for (int i = 0; i < fields.length; i++) {
            map.put(fields[i], values[i]);
        }
        courseStudyProgressCommonDao.execute(e-> e.update(cacheTable).set(map).where(cacheTable.field("f_id", String.class).eq(courseStudyProgre.getId())).execute());


        messageSender.send(MessageTypeContent.COURSE_STUDY_PROGRESS_GIVE_UP, MessageHeaderContent.ID, id);
        // add 2020-04-26 courseStudyProgress分表更新改为异步
        messageSender.send(MessageTypeContent.SPLIT_COURSE_STUDY_PROGRESS_UPDATE, MessageHeaderContent.ID, courseStudyProgre.getId(),MessageHeaderContent.MEMBER_ID,courseStudyProgre.getMemberId());
        return courseStudyProgre;
    }

    // 已弃用
    @Deprecated
    @Override
    public CourseStudyProgress againStudy(CourseStudyProgress studyProgress) {
        if(studyProgress.getFinishTime() != null){ // 完成时间不为空，回滚到已完成
            if(StringUtils.isNotEmpty(studyProgress.getMarkMemberId())){ // 标记完成
                studyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
            } else {
                studyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_FINISH);
            }
        } else if(studyProgress.getStudyTotalTime() == null || studyProgress.getStudyTotalTime().equals(0)) { // 无学习时长，回滚到未开始
            studyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_DEFAULT);
        } else { // 学习中
            studyProgress.setFinishStatus(CourseStudyProgress.FINISH_STATUS_STUDY);
        }
        // add by wangdongyan 分表使用，最后一次修改 时间
        studyProgress.setLastModifyTime(System.currentTimeMillis());
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(studyProgress.getMemberId(), SplitTableConfig.COURSE_STUDY_PROGRESS);
        courseStudyProgressCommonDao.execute(dsl -> dsl.update(cacheTable).set(CourseStudyProgress.getUpdateMap(cacheTable,studyProgress)).where(cacheTable.field("f_id",String.class).eq(studyProgress.getId())).execute());
        messageSender.send(MessageTypeContent.COURSE_STUDY_PROGRESS_UPDATE, MessageHeaderContent.ID, studyProgress.getId(),
                MessageHeaderContent.BUSINESS_ID, studyProgress.getCourseId(),MessageHeaderContent.MEMBER_ID,studyProgress.getMemberId());
        return studyProgress;

    }

    @Override
    public Optional<CourseStudyProgress> getOptional(String id) {
        return courseStudyProgressCommonDao.getOptional(id);
    }

    @Override
    public List<CourseRegister> findProgressByPushId(String pushId, String memberId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
//        return courseRegisterCommonDao.execute(dao -> dao.selectDistinct(Fields.start().add(COURSE_REGISTER).add(COURSE_STUDY_PROGRESS.FINISH_STATUS).end())
//                        .from(COURSE_REGISTER)
//                        .leftJoin(COURSE_STUDY_PROGRESS).on(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(COURSE_REGISTER.MEMBER_ID)
//                                .and(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_REGISTER.COURSE_ID)))
//                        .where(COURSE_REGISTER.MEMBER_ID.eq(memberId).and(COURSE_REGISTER.PUSH_ID.eq(pushId)))
//                        .fetch(record -> {
//                            CourseRegister register = record.into(COURSE_REGISTER).into(CourseRegister.class);
//                            register.setCourseStudyProgress(record.into(COURSE_STUDY_PROGRESS).into(CourseStudyProgress.class));
//                            return register;
//                        })
//        );
        List<CourseRegister> registerList = courseRegisterCommonDao.execute(dao -> dao.select(Fields.start().add(COURSE_REGISTER).end())
                .from(COURSE_REGISTER)
                .where(COURSE_REGISTER.MEMBER_ID.eq(memberId).and(COURSE_REGISTER.PUSH_ID.eq(pushId))).fetch(record -> {
                    CourseRegister register = record.into(COURSE_REGISTER).into(CourseRegister.class);
                    return register;
                }));
        List<String> ids = registerList.stream().map(CourseRegister::getCourseId).collect(Collectors.toList());
        if(registerList != null && registerList.size() > 0) {
            List<CourseStudyProgress> cspList = courseStudyProgressCommonDao.execute(dao -> dao.select(Fields.start().add(cacheTable.fields()).end())
                    .from(cacheTable)
                    .where(cacheTable.field("f_member_id",String.class).eq(memberId).and(cacheTable.field("f_course_id",String.class).in(ids))).fetch(record -> {
                        CourseStudyProgress courseStudyProgress = record.into(cacheTable).into(CourseStudyProgress.class);
                        return courseStudyProgress;
                    }));
            registerList.stream().forEach(x -> {
                List<CourseStudyProgress> cp = cspList.stream().filter(csp -> csp.getMemberId().equals(x.getMemberId()) && csp.getCourseId().equals(x.getCourseId())).collect(Collectors.toList());
                x.setCourseStudyProgress(cp !=null && cp.size()>0 ? cp.get(0):null);
            });
        }
        return registerList;
    }

    @Override
    public List<Map<String, Object>> totalStudyMember(String courseIds, String memberIds) {
        Map<String, List<String>> tableMemberIdMap = new HashMap<>();
        Map<String, TableImpl<?>> tableMap = new HashMap<>();
        String[] courseIdss = courseIds.split(",");
        for (String id : memberIds.split(",")) {
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(id, SplitTableConfig.COURSE_STUDY_PROGRESS);
            tableMemberIdMap.computeIfAbsent(cacheTable.getName(), c-> new ArrayList<>()).add(id);
            tableMap.put(cacheTable.getName(),cacheTable);
        }
        List<Map<String, Object>> res = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : tableMemberIdMap.entrySet()) {
            TableImpl<?> table = tableMap.get(entry.getKey());
            List<String> ids = entry.getValue();
            List<Map<String, Object>> execute = courseInfoDao.execute(dslContext ->
                    dslContext.select(table.field("f_member_id",String.class),
                                    table.field("f_study_total_time",Integer.class).sum().as("times"),
                                    table.field("f_member_id",String.class).as("memberId"))
                            .from(table)
                            .where(table.field("f_course_id",String.class).in(courseIdss))
                            .and(table.field("f_member_id",String.class).in(ids))
                            .groupBy(table.field("f_member_id",String.class)).fetchMaps());
            res.addAll(execute);
        }

        return res;
    }
    @Override
    public List<CourseStudyProgress> findProgressByCourseIds(String courseIds, String memberId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return courseStudyProgressCommonDao.execute(
                dslContext -> {
                    SelectConditionStep<Record> where = dslContext.select(Fields.start().add(cacheTable.fields())
                            .add(COURSE_INFO.ID, COURSE_INFO.NAME).end())
                            .from(cacheTable)
                            .innerJoin(COURSE_INFO).on(cacheTable.field("f_course_id",String.class).eq(COURSE_INFO.ID))
                            .where(cacheTable.field("f_course_id",String.class).in(courseIds.split(",")))
                            .and(cacheTable.field("f_member_id",String.class).eq(memberId));
                    List <CourseStudyProgress> progresses = where.fetchInto(cacheTable).into(CourseStudyProgress.class);
                    List<CourseInfo> infos = where.fetchInto(COURSE_INFO).into(CourseInfo.class);
                    IntStream.range(0,progresses.size()).forEach(i-> progresses.get(i).setCourseInfo(infos.get(i)));
                    return progresses;
                }

        );
    }
    @Override
    public List<CourseStudyProgress> findProgressByCourseIdsAndMemberIds(List<String> courseIds, List<String> memberIds) {
        Map<String, CourseInfo> courseMap = courseInfoDao.execute(x -> x.select(COURSE_INFO.ID, COURSE_INFO.NAME).from(COURSE_INFO).where(COURSE_INFO.ID.in(courseIds)))
                .fetch(r -> {
                    CourseInfo c = new CourseInfo();
                    c.setId(r.getValue(COURSE_INFO.ID));
                    c.setName(r.getValue(COURSE_INFO.NAME));
                    return c;
                }).stream().filter(c -> !StringUtils.isEmpty(c.getId())).collect(Collectors.toMap(c -> c.getId(), c -> c));

        Map<String, List<String>> tableMemberIdMap = new HashMap<>();
        Map<String, TableImpl<?>> tableMap = new HashMap<>();
        for (String id : memberIds) {
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(id, SplitTableConfig.COURSE_STUDY_PROGRESS);
            tableMemberIdMap.computeIfAbsent(cacheTable.getName(), c-> new ArrayList<>()).add(id);
            tableMap.put(cacheTable.getName(),cacheTable);
        }
        List<CourseStudyProgress> progressList = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : tableMemberIdMap.entrySet()) {
            TableImpl<?> table = tableMap.get(entry.getKey());
            List<String> ids = entry.getValue();
            List<CourseStudyProgress> list = courseStudyProgressCommonDao.execute(x ->
                    x.select(table.field("f_study_total_time",Integer.class),
                                    table.field("f_member_id",String.class),
                                    table.field("f_course_id",String.class),
                                    table.field("f_begin_time",Long.class),
                                    table.field("f_finish_status",Integer.class))
                            .from(table)
                            .where(table.field("f_course_id",String.class).in(courseIds)
                                    .and(table.field("f_member_id",String.class).in(ids)))
                            .fetch(r -> {
                                CourseStudyProgress p = new CourseStudyProgress();
                                p.setStudyTotalTime(r.getValue(table.field("f_study_total_time",Integer.class)));
                                p.setCourseId(r.getValue(table.field("f_course_id",String.class)));
                                p.setMemberId(r.getValue(table.field("f_member_id",String.class)));
                                p.setBeginTime(r.getValue(table.field("f_begin_time",Long.class)));
                                p.setFinishStatus(r.getValue(table.field("f_finish_status",Integer.class)));
                                return p;
                            })
            );
            progressList.addAll(list);
        }
        progressList.forEach(p -> {
            if (courseMap.get(p.getCourseId()) != null) {
                p.setCourseInfo(courseMap.get(p.getCourseId()));
            }
        });
        return progressList;
//        return courseStudyProgressCommonDao.execute(
//                dao -> dao.select(Fields.start().add(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME,COURSE_STUDY_PROGRESS.MEMBER_ID)
//                        .add(COURSE_INFO.ID, COURSE_INFO.NAME).end())
//                        .from(COURSE_STUDY_PROGRESS)
//                        .innerJoin(COURSE_INFO).on(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID))
//                        .where(COURSE_STUDY_PROGRESS.COURSE_ID.in(courseIds))
//                        .and(COURSE_STUDY_PROGRESS.MEMBER_ID.in(memberIds))).fetch(r->{
//            CourseStudyProgress courseStudyProgress = r.into(COURSE_STUDY_PROGRESS).into(CourseStudyProgress.class);
//            courseStudyProgress.setCourseInfo(r.into(COURSE_INFO).into(CourseInfo.class));
//            return  courseStudyProgress;
//        });
    }

	@Override
    public List<CourseStudyProgress> findRequiredUnfinished(Integer businessType) {
        return courseStudyProgressCommonDao.execute(dao -> dao.selectDistinct(
                Fields.start().add(COURSE_STUDY_PROGRESS.ID,
                        COURSE_STUDY_PROGRESS.IS_REQUIRED,
                        COURSE_STUDY_PROGRESS.COURSE_ID,
                        COURSE_STUDY_PROGRESS.FINISH_STATUS,
                        COURSE_STUDY_PROGRESS.MEMBER_ID,
                        COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME,
                        COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME,
                        COURSE_STUDY_PROGRESS.REGISTER_TIME)
                        .add(COURSE_INFO)
                        .add(STUDY_PUSH_INFO.ID,STUDY_PUSH_INFO.START_TIME,STUDY_PUSH_INFO.FINISH_TIME).end()
                ).from(COURSE_STUDY_PROGRESS)
                .leftJoin(COURSE_INFO).on(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID))
                .leftJoin(STUDY_PUSH_INFO).on(COURSE_STUDY_PROGRESS.PUSH_ID.eq(STUDY_PUSH_INFO.ID))
                .where(COURSE_STUDY_PROGRESS.FINISH_STATUS.notIn(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS, CourseStudyProgress.FINISH_STATUS_FINISH)
                .and(COURSE_STUDY_PROGRESS.IS_REQUIRED.eq(CourseStudyProgress.IS_REQUIRED_YES))
                .and(STUDY_PUSH_INFO.FINISH_TIME.gt(System.currentTimeMillis()))
                .and(COURSE_INFO.DELETE_FLAG.eq(CourseInfo.DELETE_FLAG_NO).or(COURSE_INFO.DELETE_FLAG.isNull())))
                .fetch(record -> {
                    CourseStudyProgress progress = record.into(COURSE_STUDY_PROGRESS).into(CourseStudyProgress.class);
                    progress.setCourseInfo(record.into(COURSE_INFO).into(CourseInfo.class));
                    progress.setStudyPushInfo(record.into(STUDY_PUSH_INFO).into(StudyPushInfo.class));
                    return progress;
                })
        );
    }

    @Override
    public PagedResult<CourseSectionStudyProgress> findWorksByAuditMemberId(int page, int pageSize, String memberId, Optional<Integer> finishStatus) {

        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, CourseSectionStudyProgressUtil.BUSINESS_ID);
        CourseSectionStudyProgressUtil.TableField tableField = CourseSectionStudyProgressUtil.initTableField(csspTable);
        // update for xdn 待办批改作业
        return courseSectionStudyProgressCommonDao.execute(dao -> {
            Field orgName = ORGANIZATION.NAME.as("orgName");
            Field memberName = MEMBER.FULL_NAME.as("memberName");
            SelectOnConditionStep<Record> step = dao.selectDistinct(Fields.start()
                    .add(csspTable.fields())
                    .add(COURSE_CHAPTER_SECTION.NAME, COURSE_CHAPTER_SECTION.RESOURCE_ID)
                    .add(COURSE_INFO.BUSINESS_TYPE)
                    .add(memberName)
                    .add(orgName)
                    .end())
                    .from(csspTable)
                    .leftJoin(COURSE_CHAPTER_SECTION).on(tableField.SECTION_ID().eq(COURSE_CHAPTER_SECTION.ID))
                    .leftJoin(STUDY_TASK_AUDIT_MEMBER).on(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(STUDY_TASK_AUDIT_MEMBER.TASK_ID))
                    .leftJoin(COURSE_INFO).on(COURSE_CHAPTER_SECTION.COURSE_ID.eq(COURSE_INFO.ID))
                    .leftJoin(MEMBER).on(tableField.MEMBER_ID().eq(MEMBER.ID))
                    .leftJoin(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID));

            SelectConditionStep selectConditionStep = step.where(finishStatus.map(tableField.FINISH_STATUS()::eq).orElse(DSL.trueCondition())
                    .and(STUDY_TASK_AUDIT_MEMBER.MEMBER_ID.eq(memberId)
                            .and(tableField.FINISH_STATUS().notIn(CourseSectionStudyProgress.FINISH_STATUS_UNSTART,
                                    CourseSectionStudyProgress.FINISH_STATUS_STUDY))));

            int firstResult = (page - 1) * pageSize;
            Integer count = dao.fetchCount(selectConditionStep);

            List<CourseSectionStudyProgress> progressList = selectConditionStep.orderBy(tableField.LAST_ACCESS_TIME().desc())
                    .limit(firstResult, pageSize).fetch(record -> {
                        CourseSectionStudyProgress progress = record.into(csspTable).into(CourseSectionStudyProgress.class);
                        Member member = new Member();
                        member.setFullName((String)record.getValue(memberName));
                        Organization org = new Organization();
                        org.setName((String)record.getValue(orgName));
                        member.setOrganization(org);
                        progress.setMember(member);
                        progress.setCourseChapterSection(record.into(COURSE_CHAPTER_SECTION).into(CourseChapterSection.class));
                        CourseInfo course = new CourseInfo();
                        course.setBusinessType(record.getValue(COURSE_INFO.BUSINESS_TYPE));
                        progress.setCourseInfo(course);
                        return progress;
                    });
            return PagedResult.create(count, progressList);
        });
    }

    /**
     * 转换数据库中 datetime 为 Long
     * <AUTHOR>
     * @param time
     * @return
     */
    private Field<Long> datetime2Long(Field<Date> time) {
        return DSL.field("UNIX_TIMESTAMP({0}) * 1000", Long.class, time);
    }

    public String getStringOfObject(Object obj) {
        return null != obj ? obj.toString() : "";
    }

    /**
     * 个人中心-我的课程及我的专题
     */
    @Override
    public PagedResult<CourseStudyProgress> personCourse(int pageNum, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy, Optional<String> name,
            Optional<Integer> finishStatus, Optional<Integer> isRequired, Optional<String> studyTimeOrder) {
        return courseStudyProgressCommonDao.execute(dslContext -> {

            //排序字段
            SortField<?> sf = studyTimeOrder.map(order ->
                    "asc".equals(order) ? COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME.asc() : COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME.desc())
                    .orElse(COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME.desc());
            //查询条件
            Condition c = Stream.of(
                    name.map(COURSE_INFO.NAME::contains),
                    finishStatus.map(s -> {
                        if (s == CourseStudyProgress.FINISH_STATUS_FINISH) {
                            return COURSE_STUDY_PROGRESS.FINISH_STATUS.in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
                        }
                        return COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(s);
                    }),
                    isRequired.map(COURSE_STUDY_PROGRESS.IS_REQUIRED::eq),
                    findStudy.map(study -> COURSE_STUDY_PROGRESS.FINISH_STATUS.ne(CourseStudyProgress.FINISH_STATUS_GIVEUP))
            ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());

            //查询语句
            SelectSelectStep<Record> listSelect = dslContext.select(
                    Fields.start()
                            .add(COURSE_STUDY_PROGRESS.ID,COURSE_STUDY_PROGRESS.FINISH_STATUS,COURSE_STUDY_PROGRESS.FINISH_TIME,COURSE_STUDY_PROGRESS.REGISTER_TIME,
                            COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME,COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME,COURSE_STUDY_PROGRESS.COURSE_ID,COURSE_STUDY_PROGRESS.IS_REQUIRED
                                    ,COURSE_STUDY_PROGRESS.BEGIN_TIME, COURSE_STUDY_PROGRESS.TYPE)
                            .add(COURSE_INFO.NAME, COURSE_INFO.COVER, COURSE_INFO.COVER_PATH, COURSE_INFO.URL, COURSE_INFO.VERSION_ID, COURSE_INFO.STATUS, COURSE_INFO.PUBLISH_CLIENT)
                            .end());
            //计算总数
            SelectSelectStep<Record> countSelect = dslContext.select(Fields.start().add(COURSE_STUDY_PROGRESS.ID.count()).end());
            Function<SelectSelectStep<Record>, SelectConditionStep<Record>> stepFunc = a ->
                a.from(COURSE_STUDY_PROGRESS)
                        .leftJoin(COURSE_INFO).on(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID))
                        .where(c).and(COURSE_INFO.BUSINESS_TYPE.eq(businessType))
                        .and(COURSE_STUDY_PROGRESS.MEMBER_ID.in(currentMemberId));
            int firstResult = (pageNum - 1) * pageSize;
            Result<Record> result =stepFunc.apply(listSelect).orderBy(sf).limit(firstResult, pageSize).fetch();
            List<CourseStudyProgress> progressList = result.into(COURSE_STUDY_PROGRESS).into(CourseStudyProgress.class);
            List<CourseInfo> infos = result.into(COURSE_INFO).into(CourseInfo.class);

            Integer count = stepFunc.apply(countSelect).fetchOne(0, Integer.class);

            IntStream.range(0, progressList.size()).forEach(index->
                progressList.get(index).setCourseInfo(infos.get(index))
            );
            return PagedResult.create(count, progressList);

            });
    }

    @DataSource(type=DataSourceEnum.SLAVE)
    @Override
    public PagedResult<CourseStudyProgress> personCourseUnionHistory(int pageNum, int pageSize, Integer businessType, String currentMemberId, Optional<Integer> findStudy, Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired, Optional<String> studyTimeOrder) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(currentMemberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        //查询条件  修改sql
        Condition studyProgressCondition = Stream.of(
                name.map(COURSE_INFO.NAME::contains),
                finishStatus.map(s -> {
                    if (s == CourseStudyProgress.FINISH_STATUS_FINISH) {
                        return cacheTable.field("f_finish_status",Integer.class).in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
                    }
                    return cacheTable.field("f_finish_status",Integer.class).eq(s);
                }),
                isRequired.map(cacheTable.field("f_is_required",Integer.class)::eq),
                findStudy.map(study -> cacheTable.field("f_finish_status",Integer.class).ne(CourseStudyProgress.FINISH_STATUS_GIVEUP))
        ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());

        SelectConditionStep<Record> studyProgressStep = courseStudyProgressCommonDao.execute(e ->
                e.select(Fields.start()
                        .add(
                                cacheTable.field("f_id",String.class).as("id"),
                                cacheTable.field("f_finish_status",Integer.class).as("finish_status"),
                                cacheTable.field("f_finish_time",Long.class).as("finish_time"),
                                cacheTable.field("f_register_time",Long.class).as("register_time"),
                                cacheTable.field("f_study_total_time",Integer.class).as("study_total_time"),
                                cacheTable.field("f_last_access_time",Long.class).as("last_access_time"),
                                cacheTable.field("f_course_id",String.class).as("course_id"),
                                cacheTable.field("f_is_required",Integer.class).as("is_required"),
                                cacheTable.field("f_begin_time",Long.class).as("begin_time"),
                                cacheTable.field("f_type",Integer.class).as("type"),
                                cacheTable.field("f_member_id",String.class).as("member_id")
                        )
                        .add(
                                COURSE_INFO.NAME,
                                COURSE_INFO.COVER,
                                COURSE_INFO.COVER_PATH,
                                COURSE_INFO.URL,
                                COURSE_INFO.VERSION_ID,
                                COURSE_INFO.STATUS,
                                COURSE_INFO.PUBLISH_CLIENT)
                        .end())
                        .from(cacheTable)
                        .leftJoin(COURSE_INFO).on(cacheTable.field("f_course_id",String.class).eq(COURSE_INFO.ID))
                        .where(studyProgressCondition).and(cacheTable.field("f_member_id",String.class).in(currentMemberId))
                        .and(COURSE_INFO.BUSINESS_TYPE.eq(businessType)));

        final SelectOrderByStep<Record> stepFinal = studyProgressStep;
        Integer count = courseStudyProgressCommonDao.execute(e -> e.select(cacheTable.field("f_id",String.class).count()).from(cacheTable)
                .leftJoin(COURSE_INFO).on(cacheTable.field("f_course_id",String.class).eq(COURSE_INFO.ID))
                .where(studyProgressCondition).and(cacheTable.field("f_member_id",String.class).in(currentMemberId))
                .and(COURSE_INFO.BUSINESS_TYPE.eq(businessType))).fetchOne(cacheTable.field("f_id",String.class).count(), Integer.class);
        int firstResult = (pageNum - 1) * pageSize;


        //排序字段
        SortField<?> sf = studyTimeOrder.map(order ->
                "asc".equals(order) ? DSL.val(6).asc() : DSL.val(6).desc())
                .orElse(DSL.val(6).desc());

        Result<Record> result =stepFinal.orderBy(sf).limit(firstResult, pageSize).fetch();

        List<CourseStudyProgress> progressList = result.map(r -> {
            CourseStudyProgress progress = new CourseStudyProgress();
            progress.setId(r.getValue("id") != null ? r.getValue("id").toString() : "");
            progress.setFinishStatus(r.getValue("finish_status") != null ? Integer.parseInt(r.getValue("finish_status").toString()) : 0);
            progress.setFinishTime(r.getValue("finish_time") != null ? Long.parseLong(r.getValue("finish_time").toString()) : null);
            progress.setRegisterTime(r.getValue("register_time") != null ? Long.parseLong(r.getValue("register_time").toString()) : null);
            progress.setStudyTotalTime(r.getValue("study_total_time") != null ? Integer.parseInt(r.getValue("study_total_time").toString()) : 0);
            progress.setLastAccessTime(r.getValue("last_access_time") != null ? Long.parseLong(r.getValue("last_access_time").toString()) : null);
            progress.setCourseId(r.getValue("course_id") != null ? r.getValue("course_id").toString() : "");
            progress.setIsRequired(r.getValue("is_required") != null ? Integer.parseInt(r.getValue("is_required").toString().toString()) : 0);
            progress.setBeginTime(r.getValue("begin_time") != null ? Long.parseLong(r.getValue("begin_time").toString()) : null);
            progress.setType(r.getValue("type") != null ? Integer.parseInt(r.getValue("type").toString().toString()) : 0);
            progress.setMemberId(r.getValue("member_id") != null ? r.getValue("member_id").toString() : "");
            return progress;
        });
        List<CourseInfo> infos = result.into(COURSE_INFO).into(CourseInfo.class);

        IntStream.range(0, progressList.size()).forEach(index->
                progressList.get(index).setCourseInfo(infos.get(index))
        );
        return PagedResult.create(count, progressList);


    }

    @DataSource(type = DataSourceEnum.SLAVE)
    @Override
    public Map<String, Object> personCourseUnionHistoryMap(int pageNum, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy,
                                                           Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired, Optional<String> studyTimeOrder,
                                                           boolean pageSwitch, Optional<Long> startTime, Optional<Long> endTime, Optional<List<String>> courseIds) {
        TableImpl<?> table = courseCacheServiceSupport.getCacheTable(Optional.ofNullable(currentMemberId).orElse(new ArrayList<String>(){{add("0");}}).get(0), SplitTableConfig.COURSE_STUDY_PROGRESS);

        Condition studyProgressCondition = Stream.of(
                courseIds.map(COURSE_INFO.ID::in),
                name.map(COURSE_INFO.NAME::contains),
                finishStatus.map(s -> {
                    if (s == CourseStudyProgress.FINISH_STATUS_FINISH) {
                        return table.field("f_finish_status", Integer.class).in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
                    }
                    return table.field("f_finish_status", Integer.class).eq(s);
                }),
                isRequired.map(table.field("f_is_required", Integer.class)::eq),
                startTime.map(table.field("f_begin_time", Long.class)::ge),
                endTime.map(table.field("f_begin_time", Long.class)::le),
                findStudy.map(study -> table.field("f_finish_status", Integer.class).ne(CourseStudyProgress.FINISH_STATUS_GIVEUP))
        ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());

        SelectConditionStep<Record> studyProgressStep = courseStudyProgressCommonDao.execute(e ->
                e.select(Fields.start()
                        .add(
                                table.field("f_id", String.class).as("id"),
                                table.field("f_finish_status", Integer.class).as("finish_status"),
                                table.field("f_finish_time", Long.class).as("finish_time"),
                                table.field("f_register_time", Long.class).as("register_time"),
                                table.field("f_study_total_time", Integer.class).as("study_total_time"),
                                table.field("f_last_access_time", Long.class).as("last_access_time"),
                                table.field("f_course_id", String.class).as("course_id"),
                                table.field("f_is_required", Integer.class).as("is_required"),
                                table.field("f_begin_time", Long.class).as("begin_time"),
                                table.field("f_type", Integer.class).as("type"),
                                table.field("f_completed_rate", Integer.class).as("completed_rate"),
                                table.field("f_member_id", String.class).as("member_id")
                        )
                        .add(
                                COURSE_INFO.NAME,
                                COURSE_INFO.COVER,
                                COURSE_INFO.COVER_PATH,
                                COURSE_INFO.DESCRIPTION,
                                COURSE_INFO.URL,
                                COURSE_INFO.VERSION_ID,
                                COURSE_INFO.STATUS,
                                COURSE_INFO.STYLES,
                                COURSE_INFO.LEARN_SEQUENCE,
                                COURSE_INFO.PUBLISH_CLIENT,
                                COURSE_INFO.CREATE_TIME)
                        .end())
                        .from(table)
                        .leftJoin(COURSE_INFO).on(table.field("f_course_id", String.class).eq(COURSE_INFO.ID))
                        .where(studyProgressCondition).and(table.field("f_member_id" ,String.class).in(currentMemberId))
                        .and(COURSE_INFO.BUSINESS_TYPE.eq(businessType))
                        .and(COURSE_INFO.STATUS.ne(CourseInfo.STATUS_FIVE_SHELVES)));


        // 改成查询分表
//        SelectOrderByStep<Record> studyProgressStep = null;
//        for (String memberId: currentMemberId) {
//            TableImpl<?> cacheTable = courseCacheServiceSupport.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
//            if (studyProgressStep == null) {
//                studyProgressStep = courseStudyProgressCommonDao.execute(e ->
//                        e.select(Fields.start()
//                                .add(
//                                        cacheTable.field("f_id", String.class).as("id"),
//                                        cacheTable.field("f_finish_status", Integer.class).as("finish_status"),
//                                        cacheTable.field("f_finish_time", Long.class).as("finish_time"),
//                                        cacheTable.field("f_register_time", Long.class).as("register_time"),
//                                        cacheTable.field("f_study_total_time", Integer.class).as("study_total_time"),
//                                        cacheTable.field("f_last_access_time", Long.class).as("last_access_time"),
//                                        cacheTable.field("f_course_id", String.class).as("course_id"),
//                                        cacheTable.field("f_is_required", Integer.class).as("is_required"),
//                                        cacheTable.field("f_begin_time", Long.class).as("begin_time"),
//                                        cacheTable.field("f_type", Integer.class).as("type"),
//                                        cacheTable.field("f_member_id", String.class).as("member_id")
//                                )
//                                .add(
//                                        COURSE_INFO.NAME,
//                                        COURSE_INFO.COVER,
//                                        COURSE_INFO.COVER_PATH,
//                                        COURSE_INFO.URL,
//                                        COURSE_INFO.VERSION_ID,
//                                        COURSE_INFO.STATUS,
//                                        COURSE_INFO.PUBLISH_CLIENT)
//                                .end())
//                                .from(cacheTable)
//                                .leftJoin(COURSE_INFO).on(cacheTable.field("f_course_id", String.class).eq(COURSE_INFO.ID))
//                                .where(name.map(COURSE_INFO.NAME::contains).orElse(DSL.trueCondition())
//                                        .and(finishStatus.map(s -> {
//                                            if (s == CourseStudyProgress.FINISH_STATUS_FINISH) {
//                                                return cacheTable.field("f_finish_status", Integer.class).in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
//                                            }
//                                            return cacheTable.field("f_finish_status", Integer.class).eq(s);
//                                        }).orElse(DSL.trueCondition()))
//                                        .and(isRequired.map(cacheTable.field("f_is_required", Integer.class)::eq).orElse(DSL.trueCondition()))).and(cacheTable.field("f_member_id", String.class).eq(memberId))
//                                .and(findStudy.isPresent() ? cacheTable.field("f_finish_status", Integer.class).ne(CourseStudyProgress.FINISH_STATUS_GIVEUP) : DSL.trueCondition())
//                                .and(COURSE_INFO.BUSINESS_TYPE.eq(businessType)));
//            } else {
//                studyProgressStep = studyProgressStep.unionAll(courseStudyProgressCommonDao.execute(e ->
//                        e.select(Fields.start()
//                                .add(
//                                        cacheTable.field("f_id", String.class).as("id"),
//                                        cacheTable.field("f_finish_status", Integer.class).as("finish_status"),
//                                        cacheTable.field("f_finish_time", Long.class).as("finish_time"),
//                                        cacheTable.field("f_register_time", Long.class).as("register_time"),
//                                        cacheTable.field("f_study_total_time", Integer.class).as("study_total_time"),
//                                        cacheTable.field("f_last_access_time", Long.class).as("last_access_time"),
//                                        cacheTable.field("f_course_id", String.class).as("course_id"),
//                                        cacheTable.field("f_is_required", Integer.class).as("is_required"),
//                                        cacheTable.field("f_begin_time", Long.class).as("begin_time"),
//                                        cacheTable.field("f_type", Integer.class).as("type"),
//                                        cacheTable.field("f_member_id", String.class).as("member_id")
//                                )
//                                .add(
//                                        COURSE_INFO.NAME,
//                                        COURSE_INFO.COVER,
//                                        COURSE_INFO.COVER_PATH,
//                                        COURSE_INFO.URL,
//                                        COURSE_INFO.VERSION_ID,
//                                        COURSE_INFO.STATUS,
//                                        COURSE_INFO.PUBLISH_CLIENT)
//                                .end())
//                                .from(cacheTable)
//                                .leftJoin(COURSE_INFO).on(cacheTable.field("f_course_id", String.class).eq(COURSE_INFO.ID))
//                                .where(name.map(COURSE_INFO.NAME::contains).orElse(DSL.trueCondition())
//                                        .and(finishStatus.map(s -> {
//                                            if (s == CourseStudyProgress.FINISH_STATUS_FINISH) {
//                                                return cacheTable.field("f_finish_status", Integer.class).in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
//                                            }
//                                            return cacheTable.field("f_finish_status", Integer.class).eq(s);
//                                        }).orElse(DSL.trueCondition()))
//                                        .and(isRequired.map(cacheTable.field("f_is_required", Integer.class)::eq).orElse(DSL.trueCondition()))).and(cacheTable.field("f_member_id", String.class).eq(memberId))
//                                .and(findStudy.isPresent() ? cacheTable.field("f_finish_status", Integer.class).ne(CourseStudyProgress.FINISH_STATUS_GIVEUP) : DSL.trueCondition())
//                                .and(COURSE_INFO.BUSINESS_TYPE.eq(businessType))));
//            }
//
//        }

        final SelectOrderByStep<Record> stepFinal = studyProgressStep;

        long count = 0L;
        if (pageSwitch){
            count =  stepFinal.fetch().stream().count();
        }

//        //查询数量
//        Integer count = dao.execute(x -> {
//            return x.select(KNOWLEDGE_INFO.ID.count()).from(KNOWLEDGE_INFO)
//                    .where(c)
//                    .fetchOne(KNOWLEDGE_INFO.ID.count());
//        });
        int firstResult = (pageNum - 1) * pageSize;

        //排序字段
        SortField<?> sf = studyTimeOrder.map(order ->
                "asc".equals(order) ? DSL.val(6).asc() : DSL.val(6).desc())
                .orElse(DSL.val(23).desc());


        Result<Record> result =stepFinal.orderBy(sf).limit(firstResult, pageSize + 1).fetch();

        List<CourseStudyProgress> progressList = result.map(r -> {
            CourseStudyProgress progress = new CourseStudyProgress();
            progress.setId(r.getValue("id") != null ? r.getValue("id").toString() : "");
            progress.setFinishStatus(r.getValue("finish_status") != null ? Integer.parseInt(r.getValue("finish_status").toString()) : 0);
            progress.setFinishTime(r.getValue("finish_time") != null ? Long.parseLong(r.getValue("finish_time").toString()) : null);
            progress.setRegisterTime(r.getValue("register_time") != null ? Long.parseLong(r.getValue("register_time").toString()) : null);
            progress.setStudyTotalTime(r.getValue("study_total_time") != null ? Integer.parseInt(r.getValue("study_total_time").toString()) : 0);
            progress.setLastAccessTime(r.getValue("last_access_time") != null ? Long.parseLong(r.getValue("last_access_time").toString()) : null);
            progress.setCourseId(r.getValue("course_id") != null ? r.getValue("course_id").toString() : "");
            progress.setIsRequired(r.getValue("is_required") != null ? Integer.parseInt(r.getValue("is_required").toString().toString()) : 0);
            progress.setBeginTime(r.getValue("begin_time") != null ? Long.parseLong(r.getValue("begin_time").toString()) : null);
            progress.setType(r.getValue("type") != null ? Integer.parseInt(r.getValue("type").toString().toString()) : 0);
            progress.setCompletedRate(r.getValue("completed_rate") != null ? Integer.parseInt(r.getValue("completed_rate").toString().toString()) : 0);
            progress.setMemberId(r.getValue("member_id") != null ? r.getValue("member_id").toString() : "");
            return progress;
        });
        List<CourseInfo> infos = result.into(COURSE_INFO).into(CourseInfo.class);

        IntStream.range(0, progressList.size()).forEach(index->
                progressList.get(index).setCourseInfo(infos.get(index))
        );

        Integer more = 0;
        if(Objects.nonNull(progressList)) {
            if(pageSize < progressList.size()) {
                more = 1;
                progressList.remove(pageSize);
            }
        }

        Map<String, Object> map = new HashMap<>();

        map.put("items", progressList);

        if (pageSwitch){
            map.put("recordCount", count);
        }
        map.put("more",more);

        log.info("GET INTO###personCourseUnionHistoryMap,studyTimeOrder={}  -- LJY", studyTimeOrder);
        log.info("GET INTO###personCourseUnionHistoryMap,studyTimeOrder.get()={}  -- LJY", studyTimeOrder.orElse(""));
        //可在此进行更新缓存
        if(pageNum == 1) {
            if(studyTimeOrder.isPresent() && "desc".equals(studyTimeOrder.get()) && !courseIds.isPresent()) {
                String cacheKey = STUDY_PROGRESS_CACHE_KEY + currentMemberId.get(0) + "-" + MAX_COURSE;
                String cacheAPPKey = STUDY_PROGRESS_CACHE_KEY + currentMemberId.get(0) + "-" + APP_COURSE;
                if (progressList.size() > 0) {
                    Map<String, List<CourseInfo>> studyRecords = Maps.newHashMap();
                    Map<String, List<CourseInfo>> studyAPPRecords = Maps.newHashMap();
                    List<CourseInfo> newInfo = infos;
                    List<CourseStudyProgress> newProgressList = progressList;
                    //如果大于5个课程，只截取前五，否则就不做改动
                    if (newInfo.size() > MAX_COURSE) {
                        newInfo = newInfo.subList(0, MAX_COURSE);
                        newProgressList = newProgressList.subList(0, MAX_COURSE);
                    }
                    //处理课程相关内容
                    for (int i = 0; i < newInfo.size(); i++) {
                        newInfo.get(i).setCompletedSectionCount(1);
                        newInfo.get(i).setTotalSectionCount(1);
                        newInfo.get(i).setId(newProgressList.get(i).getCourseId());
                    }
                    studyRecords.put("unfinished", newInfo);
                    studyAPPRecords.put("unfinished", newInfo.subList(0, 1));
                    studyRecords.put("finished", Arrays.asList(new CourseInfo()));
                    studyAPPRecords.put("finished", Arrays.asList(new CourseInfo()));
                    if (!studyRecords.equals(null)) {
                        setCacheStudyCard(cacheKey, studyRecords, timeLeftInSeconds());
                    }
                    if (!studyAPPRecords.equals(null)) {
                        setCacheStudyCard(cacheAPPKey, studyAPPRecords, timeLeftInSeconds());
                    }
                } else {
                    cache.clear(cacheKey);
                    cache.clear(cacheAPPKey);
                }
            }
        }
        //优化结束

        return map;


    }


    @Override
    public CourseInfo findCourseInfoByProgressId(String progressId) {
        // TODO: 2019/10/30 作业审核审计记录
        return courseInfoDao.execute(x ->
        x.select(COURSE_INFO.NAME)
        .from(COURSE_INFO)
        .leftJoin(COURSE_SECTION_STUDY_PROGRESS).on(COURSE_INFO.ID.eq(COURSE_SECTION_STUDY_PROGRESS.COURSE_ID )))
        .where(COURSE_SECTION_STUDY_PROGRESS.ID.eq(progressId))
        .fetchOne(r -> r.into(COURSE_INFO).into(CourseInfo.class));
    }

    @Override
    public Map<String, Integer> getHistoryAndNowDuration(List<String> memberIds) {
//        Map<String, Integer> nowTime = memberStatisticsDao.execute(x -> x.select(DSL.sum(MEMBER_STATISTICS.APP_TOTAL_TIME), DSL.sum(MEMBER_STATISTICS.PC_TOTAL_TIME)).from(MEMBER_STATISTICS)).where(MEMBER_STATISTICS.MEMBER_ID.in(memberIds)).fetchOne(r -> {
//            Map<String, Integer> map = new HashMap<String, Integer>();
//            map.put("0", r.getValue(DSL.sum(MEMBER_STATISTICS.PC_TOTAL_TIME), Integer.class));
//            map.put("1", r.getValue(DSL.sum(MEMBER_STATISTICS.APP_TOTAL_TIME), Integer.class));
//            return map;
//        });
        // updated by wdy sql执行较慢，去掉关联课程
//    	Integer nowTime = courseStudyProgressCommonDao.execute(x -> {
//    		SelectOrderByStep<Record1<Integer>> tableSum = null;
//    		for (String memberId : memberIds) {
//    			TableImpl<?> table = courseCacheServiceSupport.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
//    			if (tableSum == null) {
//    				tableSum = x.select(table.field("f_study_total_time", Integer.class))
//    						.from(table)
//    						.leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(table.field("f_course_id", String.class)))
//    						.where(table.field("f_study_total_time", Integer.class).gt(0)
//    								.and(table.field("f_member_id", String.class).eq(memberId))
//    								.and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE)));
//    			} else {
//    				tableSum = tableSum.unionAll(x.select(table.field("f_study_total_time", Integer.class))
//    						.from(table)
//    						.leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(table.field("f_course_id", String.class)))
//    						.where(table.field("f_study_total_time", Integer.class).gt(0)
//    								.and(table.field("f_member_id", String.class).eq(memberId))
//    								.and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))));
//    			}
//    		}
//    		return x.select(Fields.start().add(tableSum.field("f_study_total_time",Integer.class)).end())
//    				.from(tableSum)
//    				.fetch(r -> {
//    					CourseStudyProgress p = new CourseStudyProgress();
//    					p.setStudyTotalTime(Integer.valueOf(r.getValue("f_study_total_time").toString()));
//    					return p;
//    				}).stream().map(CourseStudyProgress::getStudyTotalTime).reduce(0, (a, b) -> a+b);
//    	});
        // 2020-1-7 改为查询当年学习数据
//        Integer nowTime = 0;
//        // 查询所有的进度表数据
//        List<CourseStudyProgress> progressList = courseStudyProgressCommonDao.execute(x -> {
//            SelectOrderByStep<Record2<Integer, String>> tableSum = null;
//            for (String memberId : memberIds) {
//                TableImpl<?> table = courseCacheServiceSupport.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
//                if (tableSum == null) {
//                    tableSum = x.select(table.field("f_study_total_time", Integer.class), table.field("f_course_id", String.class))
//                            .from(table)
//                            .where(table.field("f_study_total_time", Integer.class).gt(0)
//                                    .and(table.field("f_member_id", String.class).eq(memberId)));
//                } else {
//                    tableSum = tableSum.unionAll(x.select(table.field("f_study_total_time", Integer.class), table.field("f_course_id", String.class))
//                            .from(table)
//                            .where(table.field("f_study_total_time", Integer.class).gt(0)
//                                    .and(table.field("f_member_id", String.class).eq(memberId))));
//                }
//            }
//            return x.select(Fields.start().add(tableSum.field("f_study_total_time", Integer.class), tableSum.field("f_course_id", String.class)).end())
//                    .from(tableSum)
//                    .fetch(r -> {
//                        CourseStudyProgress p = new CourseStudyProgress();
//                        p.setStudyTotalTime(Integer.valueOf(r.getValue("f_study_total_time").toString()));
//                        p.setCourseId(r.getValue("f_course_id", String.class));
//                        return p;
//                    });
//        });
//        //查询目前库中存在的所有课程
//        Map<String, CourseInfo> courseMap = courseCacheServiceSupport.getCourseInfoMapByBusinessType(CourseInfo.BUSINESS_TYPE_COURSE);
//        // 遍历progressList找出课程的总时长
//        List<Integer> timeList = new ArrayList<>();
//        progressList.forEach(p ->{
//            if(courseMap.get(p.getCourseId()) != null && p.getStudyTotalTime() != null) {
//                timeList.add(p.getStudyTotalTime());
//            }
//        });
//        if (!timeList.isEmpty()) {
//            nowTime = timeList.stream().reduce(0, (a, b) -> a+b);
//        }
// --------------
//    	Integer nowTime = courseStudyProgressCommonDao.execute(r -> {
//    		return r.select(Fields.start().add(COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME).end())
//    		.from(COURSE_STUDY_PROGRESS)
//    		.leftJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_STUDY_PROGRESS.COURSE_ID))
//    		.where(COURSE_STUDY_PROGRESS.MEMBER_ID.in(memberIds).and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE)))
//    		.fetch(s -> s.into(CourseStudyProgress.class));
//    	}).stream().map(x -> x.getStudyTotalTime()).reduce(0, (a, b) -> a+b);
        // 2020-1-7 修改改为查询当年学习时长
//        Map<String, Integer> timeMap = new HashMap<String, Integer>();
//        timeMap.put("0", nowTime);
//
//        // 查询历史迁移表中的数据
//        Map<String, Integer> historyTime = memberStatisticsArchivesDao.execute(x -> x.select(DSL.sum(MEMBER_STATISTICS_ARCHIVES.PC_TOTAL_TIME),DSL.sum(MEMBER_STATISTICS_ARCHIVES.APP_TOTAL_TIME), DSL.sum(MEMBER_STATISTICS_ARCHIVES.STUDY_TOTAL_TIME))).from(MEMBER_STATISTICS_ARCHIVES).where(MEMBER_STATISTICS_ARCHIVES.MEMBER_ID.in(memberIds)).fetchOne(r -> {
//            Map<String, Integer> map = new HashMap<String, Integer>();
//            Integer value = r.getValue(DSL.sum(MEMBER_STATISTICS_ARCHIVES.STUDY_TOTAL_TIME), Integer.class);
//            Integer pc = r.getValue(DSL.sum(MEMBER_STATISTICS_ARCHIVES.PC_TOTAL_TIME), Integer.class);
//            Integer app = r.getValue(DSL.sum(MEMBER_STATISTICS_ARCHIVES.APP_TOTAL_TIME), Integer.class);
//            Integer history = 0;
//            if (null != value && null != pc && null != app) {
//                history = value - pc - app;
//            }
//            map.put("0", pc);
//            map.put("1", app);
//            map.put("2", history);
//            return map;
//        });
//        Map<String, Integer> map = new HashMap<String, Integer>();
//        map = addTo(map,timeMap);
//        map = addTo(map,historyTime);
// ------------------------
        // 2020-1-7 个人中心总时长改为查询当年学习时长
        // 获取当前年
        int year = LocalDate.now().getYear();
        // day表中查询学员当前年的数据
        List<CourseStudyLog> progressList = courseStudyProgressCommonDao.execute(x -> {
            SelectOrderByStep<Record2<Integer, Integer>> tableSum = null;
            for (String memberId : memberIds) {
                TableImpl<?> table = courseCacheServiceSupport.getCacheTable(memberId, SplitTableConfig.COURSE_SECTION_STUDY_LOG_DAY);
                if (tableSum == null) {
                    tableSum = x.select(table.field("f_pc_study_time", Integer.class), table.field("f_app_study_time", Integer.class))
                            .from(table)
                            .where(table.field("f_member_id", String.class).eq(memberId)
                                    .and(table.field("f_year", Integer.class).eq(year))
                            );
                } else {
                    tableSum = tableSum.unionAll(x.select(table.field("f_pc_study_time", Integer.class), table.field("f_app_study_time", Integer.class))
                            .from(table)
                            .where(table.field("f_member_id", String.class).eq(memberId)
                                    .and(table.field("f_year", Integer.class).eq(year))
                            ));
                }
            }
            return x.select(Fields.start().add(tableSum.field("f_pc_study_time", Integer.class), tableSum.field("f_app_study_time", Integer.class)).end())
                    .from(tableSum)
                    .fetch(r -> {
                        CourseStudyLog p = new CourseStudyLog();
                        p.setPcStudyTime(ObjectUtils.isEmpty(r.getValue("f_pc_study_time")) ? 0 : Integer.valueOf(r.getValue("f_pc_study_time").toString()));
                        p.setAppStudyTime(ObjectUtils.isEmpty(r.getValue("f_app_study_time")) ? 0 : Integer.valueOf(r.getValue("f_app_study_time").toString()));
                        return p;
                    });
        });
        Integer pcStudyTime = progressList.stream().filter(p -> p.getPcStudyTime() != null).map(CourseStudyLog::getPcStudyTime).reduce(0, (a, b) -> a + b);
        Integer AppStudyTime = progressList.stream().filter(p -> p.getAppStudyTime() != null).map(CourseStudyLog::getAppStudyTime).reduce(0, (a, b) -> a + b);
        Map<String, Integer> map = new HashMap<String, Integer>();
        map.put("0", pcStudyTime);
        map.put("1", AppStudyTime);
        return map;
    }

    @Override
	public Integer findMonthCourseStudyProgressCount(Optional<Long> lastMonthFirstDay, Optional<Long> monthFirstDay) {
		return logDao.execute(d ->{
//			Field<BigDecimal> sumStudyTotalTime = COURSE_SECTION_STUDY_LOG.STUDY_TIME.sum().as("sum_study_total_time");
	        Field<String> tMemberId = COURSE_SECTION_STUDY_LOG.MEMBER_ID.as("member_id");
			Table<Record1<String>> totalStudyGroupByMember = d.select(tMemberId)
	            .from(COURSE_SECTION_STUDY_LOG)
//	            .leftJoin(MEMBER).on(MEMBER.ID.eq(COURSE_SECTION_STUDY_LOG.MEMBER_ID))
	            .where(lastMonthFirstDay.map(COURSE_SECTION_STUDY_LOG.CREATE_TIME::ge).orElse(DSL.trueCondition()))
	            .and(monthFirstDay.map(COURSE_SECTION_STUDY_LOG.CREATE_TIME::lt).orElse(DSL.trueCondition()))
	            .groupBy(COURSE_SECTION_STUDY_LOG.MEMBER_ID)
	            .asTable("total_study_group_by_member");

			return d.select(totalStudyGroupByMember.field(tMemberId).count()).from(totalStudyGroupByMember)
					.fetchOne() .getValue(0, Integer.class);
		});
	}


    @Override
	public List<CourseStudyProgress> findMonthCourseStudyProgress(Integer page, Integer pageSize, Optional<Long> lastMonthFirstDay, Optional<Long> monthFirstDay) {
		List<CourseStudyProgress> execute1 = logDao.execute(d -> {
	    	List<CourseStudyProgress> list = new ArrayList<CourseStudyProgress>();
            d.select(COURSE_SECTION_STUDY_LOG.MEMBER_ID,
            		 	MEMBER.NAME,
            		 	MEMBER.FULL_NAME,
            		 	DSL.sum(COURSE_SECTION_STUDY_LOG.STUDY_TIME))
                    .from(COURSE_SECTION_STUDY_LOG)
                    .leftJoin(MEMBER).on(MEMBER.ID.eq(COURSE_SECTION_STUDY_LOG.MEMBER_ID))
                    .where(lastMonthFirstDay.map(COURSE_SECTION_STUDY_LOG.CREATE_TIME::ge).orElse(DSL.trueCondition()))
                    .and(monthFirstDay.map(COURSE_SECTION_STUDY_LOG.CREATE_TIME::lt).orElse(DSL.trueCondition()))
                    .groupBy(COURSE_SECTION_STUDY_LOG.MEMBER_ID)
                    .limit((page - 1) * pageSize, pageSize)
                    .fetch().forEach(r -> {
                    	CourseStudyProgress courseStudyProgress = new CourseStudyProgress();
                    	Member member = new Member();
                    	member.setName(r.getValue(MEMBER.NAME, String.class));
                    	member.setFullName(r.getValue(MEMBER.FULL_NAME, String.class));
                    	courseStudyProgress.setMember(member);
                    	courseStudyProgress.setStudyTotalTime(r.getValue(DSL.sum(COURSE_SECTION_STUDY_LOG.STUDY_TIME), Integer.class));
                    	list.add(courseStudyProgress);
                    });
	            return list;
	        });
		return execute1;
	}

	@Override
	public void updateKnowledgeStudyProgressAsync(String memberId, String resourceId, Long beginTime) {
		messageSender.send(MessageTypeContent.SUBJECT_KNOWLEDGE_UPDATE,
                MessageHeaderContent.MEMBER_ID, memberId,
                MessageHeaderContent.ID, resourceId,
                MessageHeaderContent.PARAMS, beginTime.toString());
	}

    @Override
    public List<CourseStudyProgress> findList(List<String> memberIds, List<String> courseIds) {
        Map<String, List<String>> tableMemberIdMap = new HashMap<>();
        Map<String, TableImpl<?>> tableMap = new HashMap<>();
        for (String id : memberIds) {
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(id, SplitTableConfig.COURSE_STUDY_PROGRESS);
            tableMemberIdMap.computeIfAbsent(cacheTable.getName(), c-> new ArrayList<>()).add(id);
            tableMap.put(cacheTable.getName(),cacheTable);
        }
        List<CourseStudyProgress> courseStudyProgress = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : tableMemberIdMap.entrySet()) {
            TableImpl<?> table = tableMap.get(entry.getKey());
            List<String> ids = entry.getValue();

            List<CourseStudyProgress> list = courseStudyProgressCommonDao.execute(x -> x.select(Fields.start().add(
                    table.field("f_member_id", String.class), table.field("f_course_id", String.class)
            ).end()).from(table).where(table.field("f_member_id", String.class).in(ids)
                    .and(table.field("f_course_id", String.class).in(courseIds).and(table.field("f_finish_status", Integer.class).eq(CourseStudyProgress.FINISH_STATUS_FINISH).or(table.field("f_finish_status", Integer.class).eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                    ))).fetch(r -> {
                CourseStudyProgress c = new CourseStudyProgress();
                c.setMemberId(r.getValue(table.field("f_member_id", String.class)));
                c.setCourseId(r.getValue(table.field("f_course_id", String.class)));
                return c;
            });
            courseStudyProgress.addAll(list);
        }
        return courseStudyProgress;
    }

    @Override
    public List<CourseStudyProgress> getStatus(String memberId, List<String> courseIds) {
        TableImpl<?> table = courseCacheServiceSupport.getCacheTable(memberId,SplitTableConfig.COURSE_STUDY_PROGRESS);
        List<CourseStudyProgress> courseStudyProgress = courseStudyProgressCommonDao.execute(x->x.select(Fields.start().add(
                table.field("f_member_id", String.class),table.field("f_course_id", String.class)
        ).end()).from(table).where(table.field("f_member_id", String.class).eq(memberId)
                .and(table.field("f_course_id", String.class).in(courseIds).and(table.field("f_finish_status", Integer.class).eq(CourseStudyProgress.FINISH_STATUS_FINISH).or(table.field("f_finish_status", Integer.class).eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                ))).fetch(r->{
            CourseStudyProgress c =  new CourseStudyProgress();
            c.setCourseId(r.getValue(table.field("f_course_id", String.class)));
            return c;
        });
        return courseStudyProgress;
    }
    @Override
    public List<String> getStatusNe(String memberId, List<String> courseIds) {
        TableImpl<?> table = courseCacheServiceSupport.getCacheTable(memberId,SplitTableConfig.COURSE_STUDY_PROGRESS);
        return courseStudyProgressCommonDao.execute(x->x.select(Fields.start().add(
                table.field("f_member_id", String.class),table.field("f_course_id", String.class)
        ).end()).from(table).where(table.field("f_member_id", String.class).eq(memberId)
                .and(table.field("f_course_id", String.class).in(courseIds).and(table.field("f_finish_status", Integer.class)
                        .ne(CourseStudyProgress.FINISH_STATUS_DEFAULT))
                ))).fetch(r-> r.getValue(table.field("f_course_id", String.class)));
    }

    @Override
    public Integer countStatus(List<String> courseIds, String memberId) {
        TableImpl<?> table = courseCacheServiceSupport.getCacheTable(memberId,SplitTableConfig.COURSE_STUDY_PROGRESS);
        return courseStudyProgressCommonDao.execute(x->x.select(Fields.start().add(
                table.field("f_member_id", String.class),table.field("f_id", String.class).count()
        ).end()).from(table).where(table.field("f_member_id", String.class).eq(memberId)
                .and(table.field("f_course_id",String.class).in(courseIds).and(table.field("f_finish_status",Integer.class).eq(CourseStudyProgress.FINISH_STATUS_FINISH).or(table.field("f_finish_status", Integer.class).eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                ))).fetchOne(r->{
                return r.getValue(table.field("f_id", String.class).count());
        });
    }

    private String simpleDateFormateForMonth(Long date, String style) {
        return new SimpleDateFormat(style).format(new Date(date));
	}

    @Override
    public Boolean finishAllCourse(String memberId, List<String> courseIds) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        Integer count = courseStudyProgressCommonDao.execute(e -> e.select(DSL.count(cacheTable.field("f_id",String.class)))
                .from(cacheTable)
                .where(cacheTable.field("f_member_id",String.class).eq(memberId))
                .and(cacheTable.field("f_course_id",String.class).in(courseIds))
                .and(cacheTable.field("f_finish_status",Integer.class).eq(CourseStudyProgress.FINISH_STATUS_FINISH)
                    .or(cacheTable.field("f_finish_status",Integer.class).eq(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS)))
                .fetchOne(DSL.count(cacheTable.field("f_id",String.class))));
        return count.equals(courseIds.size());
    }

    @Override
    public Map<String, BigDecimal> findStudyTimeTendency(String memberId, String organizationId, Integer size, String type) {
        // organizationId补充
        if (StringUtils.isEmpty(organizationId)) {
            organizationId = memberDao.execute(dslContext -> dslContext
                    .select(MEMBER.ORGANIZATION_ID)
                    .from(MEMBER)
                    .where(MEMBER.ID.eq(memberId))
                    .limit(1)
                    .fetchOne(MEMBER.ORGANIZATION_ID));
        }
        String finalOrganizationId = organizationId;

        // 查询分表名
        String tableName = splitTableConfigDao.execute(x -> x.select(SPLIT_TABLE_CONFIG.TARGET_TABLE).from(SPLIT_TABLE_CONFIG)
                .leftJoin(ORGANIZATION_DETAIL).on(ORGANIZATION_DETAIL.ROOT.eq(SPLIT_TABLE_CONFIG.ORGANIZATION_ID))
                .where(ORGANIZATION_DETAIL.SUB.eq(finalOrganizationId).and(SPLIT_TABLE_CONFIG.SOURCE.eq(SplitTableConfig.COURSE_SECTION_STUDY_LOG_DAY)))
                .limit(1)
                .fetchOptional(SPLIT_TABLE_CONFIG.TARGET_TABLE).orElse("t_course_section_study_log_other_day")
        );
        Map<String, BigDecimal> resultMap = new HashMap<>();
        // 日
        if (TENDENCYDAY.equals(type)) {
            resultMap = this.findStudyTimeTendencyByDay(memberId, type, size, tableName);
        }
        // 周
        if (TENDENCYWEEK.equals(type)) {
            resultMap = this.findStudyTimeTendencyByWeek(memberId, size, tableName);
        }
        // 月
        if (TENDENCYMONTH.equals(type)) {
            resultMap = this.findStudyTimeTendencyByMonth(memberId, type, size, tableName);
        }
        /*if (!CollectionUtils.isEmpty(resultMap)){
            BigDecimal avg = new BigDecimal(this.calculateStudyTimeAvg(resultMap).toPlainString());
            BigDecimal qoq = this.calculateStudyTimeQOQ(resultMap);
            resultMap.put("avg", avg);
            resultMap.put("qoq", qoq);
        }*/
        return resultMap;
    }

    private BigDecimal calculateStudyTimeQOQ(Map<String, BigDecimal> dataMap) {
        String[] arr = new String[]{};
        String[] keyarr = dataMap.keySet().toArray(arr);

        String lastkey = keyarr[dataMap.size() - 1];
        String lastSecondKye = keyarr[dataMap.size() - 2];
        if (dataMap.get(lastkey).compareTo(BigDecimal.ZERO)== 0 && dataMap.get(lastSecondKye).equals(BigDecimal.ZERO)) return BigDecimal.ZERO;
        //如果上期学习时长为0，则环比增长为0
        if (dataMap.get(lastSecondKye).compareTo(BigDecimal.ZERO)== 0) return BigDecimal.ZERO;

        return BigDecimal.valueOf((dataMap.get(lastkey).doubleValue() - dataMap.get(lastSecondKye).doubleValue())
                / dataMap.get(lastSecondKye).doubleValue() * 100).setScale(0, BigDecimal.ROUND_UP);
    }

    private BigDecimal calculateStudyTimeAvg(Map<String, BigDecimal> dataMap) {
        BigDecimal totalStudyTime =  BigDecimal.ZERO;
        for (Map.Entry<String,BigDecimal> entry :dataMap.entrySet()){
            totalStudyTime = totalStudyTime.add(entry.getValue());
        }
        return totalStudyTime.divide(BigDecimal.valueOf(dataMap.size()),0,RoundingMode.HALF_UP).stripTrailingZeros();
    }

    private Map<String, BigDecimal> findStudyTimeTendencyByDay(String memberId, String type, Integer size, String tableName) {
        // 当前日期
        LocalDate today = LocalDate.now();
        LocalDate minusDay = today.minusDays(size);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMdd");
        String todayDayStr = today.format(fmt);//今天日期
        String minusDayStr = minusDay.format(fmt);//size天

        TableImpl<?> table = SplitTableName.getTableNameByCode(tableName);
        Map<Integer, BigDecimal> result = logCommonDao.execute(dao -> {
            return dao.select(table.field("f_day", Integer.class), DSL.sum(table.field("f_study_time", BigDecimal.class)))
                    .from(table)
                    .where(table.field("f_year", Integer.class).eq(today.getYear())
                            .and(table.field("f_member_id", String.class).eq(memberId))
                            .and(table.field("f_day", Integer.class).between(Integer.parseInt(minusDayStr), Integer.parseInt(todayDayStr)))
                    ).groupBy(table.field("f_day"))
                    .orderBy(table.field("f_day"))
                    .fetchMap(table.field("f_day", Integer.class), DSL.sum(table.field("f_study_time", BigDecimal.class)));
        });

        // 转换成分钟
        result = this.convertResultMap(result);

        // 封装数据
        return this.encapsulationResultMap(result, today, size, fmt, DateTimeFormatter.ofPattern("MMdd"), type);

    }


    private Map<String, BigDecimal> findStudyTimeTendencyByMonth(String memberId, String type, Integer size, String tableName) {
        // 当前日期
        LocalDate today = LocalDate.now();
        LocalDate minusMonth = today.minusMonths(size);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMM");
        String todayMonthStr = today.format(fmt);//今天月份
        String minusMonthStr = minusMonth.format(fmt);//size月份

        TableImpl<?> table = SplitTableName.getTableNameByCode(tableName);
        Map<Integer, BigDecimal> result = logCommonDao.execute(dao -> {
            return dao.select(table.field("f_month", Integer.class), DSL.sum(table.field("f_study_time", BigDecimal.class)))
                    .from(table)
                    .where(table.field("f_year", Integer.class).eq(today.getYear())
                            .and(table.field("f_member_id", String.class).eq(memberId))
                            .and(table.field("f_month", Integer.class).between(Integer.parseInt(minusMonthStr), Integer.parseInt(todayMonthStr)))
                    ).groupBy(table.field("f_month"))
                    .orderBy(table.field("f_month"))
                    .fetchMap(table.field("f_month", Integer.class), DSL.sum(table.field("f_study_time", BigDecimal.class)));
        });

        // 转换成分钟
        result = this.convertResultMap(result);

        // 封装数据
        return this.encapsulationResultMap(result, today, size, fmt, DateTimeFormatter.ofPattern("yyyy-MM"), type);
    }


    private Map<String, BigDecimal> findStudyTimeTendencyByWeek(String memberId, Integer size, String tableName) {
        // 当前日期
        LocalDate today = LocalDate.now();
        // 获取本周一
        LocalDate monday = today.with(TemporalAdjusters.previousOrSame(DayOfWeek.MONDAY));
        // 获取n周前 最开始一周的日期
        LocalDate minusDay = monday.minusDays((size - 1) * 7);
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMdd");
        String todayDayStr = today.format(fmt);// 今天日期
        String minusDayStr = minusDay.format(fmt);// n周前日期

        // 查询最近N周 每天汇总的数据map
        TableImpl<?> table = SplitTableName.getTableNameByCode(tableName);
        Map<Integer, BigDecimal> map = logCommonDao.execute(dao -> {
            return dao.select(table.field("f_day", Integer.class), DSL.sum(table.field("f_study_time", BigDecimal.class)))
                    .from(table)
                    .where(table.field("f_year", Integer.class).eq(today.getYear())
                            .and(table.field("f_member_id", String.class).eq(memberId))
                            .and(table.field("f_day", Integer.class).between(Integer.parseInt(minusDayStr), Integer.parseInt(todayDayStr)))
                    ).groupBy(table.field("f_day"))
                    .orderBy(table.field("f_day"))
                    .fetchMap(table.field("f_day", Integer.class), DSL.sum(table.field("f_study_time", BigDecimal.class)));
        });

        // 处理N周 每周的数据结果
        return this.getWeekMap(today, size, map);

    }

    /**
     * 通过当前时间 获取前N周 每周一的日期
     */
    private Map<String, BigDecimal> getWeekMap(LocalDate today, Integer size, Map<Integer, BigDecimal> map) {

        List<WeekInfo> weekInfoList = new ArrayList<>();
        DateTimeFormatter fmt = DateTimeFormatter.ofPattern("yyyyMMdd");
        DateTimeFormatter mondayfmt = DateTimeFormatter.ofPattern("MM-dd");
        // 获取本周一
        LocalDate monday = today.with(TemporalAdjusters.previous(DayOfWeek.MONDAY));
        // 获取本周日
        LocalDate sunday = today.with(TemporalAdjusters.next(DayOfWeek.SUNDAY));

        Integer weekStart = Integer.parseInt(monday.format(fmt));
        Integer weekEnd = Integer.parseInt(sunday.format(fmt));
        WeekInfo week = new WeekInfo();
        week.setMondayDate(weekStart);
        week.setMondayStr(monday.format(mondayfmt));
        week.setSundayDate(weekEnd);
        weekInfoList.add(week);

        // 获取当年第一天时间
        LocalDate yearStart = today.with(TemporalAdjusters.firstDayOfYear());
        // 循环size-1次 查出最近N周 的日期
        for (int i = 1; i < size; i++) {
            // 获取n周前 每周 周一日期
            LocalDate mondayDate = monday.minusDays(i * 7);
            // 获取n周前 每周 周日日期
            LocalDate sundayDate = sunday.minusDays(i * 7);
            Integer startDay = Integer.parseInt(mondayDate.format(fmt));
            Integer endDay = Integer.parseInt(sundayDate.format(fmt));

            WeekInfo wk = new WeekInfo();
            // 当年第一天为起始周
            if (mondayDate.isEqual(yearStart)) {
                wk.setMondayDate(startDay);
                wk.setMondayStr(mondayDate.format(mondayfmt));
                wk.setSundayDate(endDay);
                weekInfoList.add(wk);
                break;
            } else if (mondayDate.isBefore(yearStart)) { // 当前周 周一 在去年
                // 周一 为当年第一天
                wk.setMondayDate(Integer.parseInt(yearStart.format(fmt)));
                wk.setMondayStr(yearStart.format(mondayfmt));
                // 周日 为当年第一天的周日
                wk.setSundayDate(Integer.parseInt(yearStart.with(TemporalAdjusters.previous(DayOfWeek.SUNDAY)).format(fmt)));
                weekInfoList.add(wk);
                break;
            } else {
                wk.setMondayDate(startDay);
                wk.setMondayStr(mondayDate.format(mondayfmt));
                wk.setSundayDate(endDay);
                weekInfoList.add(wk);
            }

        }
        Map<String, BigDecimal> resultMap = new LinkedHashMap<>();

        // 数据处理（按每周分组数据）
        weekInfoList.forEach(weekInfo -> {
            // 每周学习数据时长
            BigDecimal studyTime = new BigDecimal(0);
            for (Integer key : map.keySet()) {
                // 判断是否在 某一周内
                if (rangeInDefined(key, weekInfo.getMondayDate(), weekInfo.getSundayDate())) {
                    BigDecimal dayTime = map.get(key);
                    if (null != dayTime) {
                        studyTime = studyTime.add(dayTime);
                    }
                }
            }
            int weekOfYear = DateUtil.weekOfYear(Calendar.getInstance().get(Calendar.YEAR) + "-" + weekInfo.getMondayStr());
            resultMap.put(String.valueOf(weekOfYear), studyTime);
        });

        // 时长转换为分钟
        Map<String, BigDecimal> collect = resultMap.entrySet().stream()
                .collect(Collectors.toMap(
                        m -> m.getKey(),
                        m -> m.getValue().divide(new BigDecimal(60), 0, BigDecimal.ROUND_HALF_UP)));

        return this.sortResultMap(collect);
    }

    /**
     * 将结果封装 （当年数据且key类型为ofPattern）
     *
     * @param result
     * @param today
     * @param size
     * @param dbOfPattern
     * @param ofPattern
     * @return
     */
    private Map<String, BigDecimal> encapsulationResultMap(Map<Integer, BigDecimal> result, LocalDate today, Integer size,
                                                           DateTimeFormatter dbOfPattern, DateTimeFormatter ofPattern, String type) {
        Map<String, BigDecimal> resultMap = new LinkedHashMap();
        // 获取当年第一天时间
        LocalDate yearStart = today.with(TemporalAdjusters.firstDayOfYear());
        for (int i = 0; i < size; i++) {
            LocalDate minusDate = null;
            if (TENDENCYDAY.equals(type)) {
                minusDate = today.minusDays(i);
            }
            if (TENDENCYMONTH.equals(type)) {
                minusDate = today.minusMonths(i);
            }
            // 日期必须大于年初日期
            if (minusDate.isAfter(yearStart)) {
                int dbKey = Integer.parseInt(minusDate.format(dbOfPattern));
                String key = null;
                if (TENDENCYDAY.equals(type)) {
                    key = minusDate.format(ofPattern);
                }
                if (TENDENCYMONTH.equals(type)) {
                    key = String.valueOf(minusDate.get(ChronoField.MONTH_OF_YEAR));
                }
                if (result.containsKey(dbKey)) {
                    resultMap.put(key, result.get(dbKey));
                } else {
                    resultMap.put(key, BigDecimal.valueOf(0));
                }
            }
        }
        return sortResultMap(resultMap);
    }

    /**
     * 转换成分钟（不足0.1分钟的 == 0）
     */
    private Map<Integer, BigDecimal> convertResultMap(Map<Integer, BigDecimal> resultMap) {
        return resultMap.entrySet().stream()
                .collect(Collectors.toMap(
                        map -> map.getKey(),
                        map -> map.getValue().divide(new BigDecimal(60), 1, BigDecimal.ROUND_HALF_UP)));
    }

    /**
     * sort
     */
    private Map<String, BigDecimal> sortResultMap(Map<String, BigDecimal> resultMap) {
        Map<String, BigDecimal> result = new LinkedHashMap<>();
        resultMap.entrySet().stream()
                .sorted(Map.Entry.comparingByKey())
                .forEachOrdered(x -> result.put(x.getKey(), x.getValue()));
        return result;
    }


    public static boolean rangeInDefined(int current, int min, int max) {
        return Math.max(min, current) == Math.min(current, max);
    }



    private List<CourseInfo> getTopNUnfinishedRecords(String memberId, Integer size, TableImpl<?> courseStudyProgressTable) {
        return courseInfoDao.execute(e ->
                getStudyCourseInfo(memberId,size,Arrays.asList(CourseStudyProgress.FINISH_STATUS_STUDY), true, courseStudyProgressTable, e).map(r -> {
                    CourseInfo record = new CourseInfo();
                    record.setId(r.get(COURSE_INFO.ID));
                    record.setName(r.get(COURSE_INFO.NAME));
                    record.setCoverPath(r.get(COURSE_INFO.COVER_PATH));
                    record.setVersionId(r.get(COURSE_INFO.VERSION_ID));
                    computeSectionCount(memberId, e, record);
                    return record;
                }));
    }

    /**
     * 优化课程，从未完成，学习中，已完成中挑选最近的数据
     * @param memberId
     * @param size
     * @param courseStudyProgressTable
     */
    private List<CourseInfo> getTopNRecords(String memberId, Integer size, TableImpl<?> courseStudyProgressTable) {
        return courseInfoDao.execute(e ->
                getStudyCourseInfo(memberId,size,
                    Arrays.asList(CourseStudyProgress.FINISH_STATUS_STUDY,CourseStudyProgress.FINISH_STATUS_DEFAULT,CourseStudyProgress.FINISH_STATUS_FINISH), true, courseStudyProgressTable, e).map(r -> {
                    CourseInfo record = new CourseInfo();
                    record.setId(r.get(COURSE_INFO.ID));
                    record.setName(r.get(COURSE_INFO.NAME));
                    record.setCoverPath(r.get(COURSE_INFO.COVER_PATH));
                    record.setVersionId(r.get(COURSE_INFO.VERSION_ID));
                    sectionPadding(record);
                    return record;
                }));
    }

//    /**
//     * 优化课程，从未完成，学习中，已完成中挑选最近的数据
//     * @param businessType
//     * @param currentMemberId
//     * @param findStudy
//     * @param name
//     * @param finishStatus
//     * @param isRequired
//     * @param studyTimeOrder
//     */
//    private List<CourseInfo> getTop5Records(Integer businessType, String currentMemberId, Optional<Integer> findStudy, Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired, Optional<String> studyTimeOrder) {
//        Condition studyProgressCondition = Stream.of(
//                name.map(COURSE_INFO.NAME::contains),
//                finishStatus.map(s -> {
//                    if (s == CourseStudyProgress.FINISH_STATUS_FINISH) {
//                        return COURSE_STUDY_PROGRESS.FINISH_STATUS.in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
//                    }
//                    return COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(s);
//                }),
//                isRequired.map(COURSE_STUDY_PROGRESS.IS_REQUIRED::eq),
//                findStudy.map(study -> COURSE_STUDY_PROGRESS.FINISH_STATUS.ne(CourseStudyProgress.FINISH_STATUS_GIVEUP))
//        ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());
//
//        SelectConditionStep<Record> studyProgressStep = courseStudyProgressCommonDao.execute(e ->
//                e.select(Fields.start()
//                        .add(
//                                COURSE_STUDY_PROGRESS.ID.as("id"),
//                                COURSE_STUDY_PROGRESS.FINISH_STATUS.as("finish_status"),
//                                COURSE_STUDY_PROGRESS.FINISH_TIME.as("finish_time"),
//                                COURSE_STUDY_PROGRESS.REGISTER_TIME.as("register_time"),
//                                COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.as("study_total_time"),
//                                COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME.as("last_access_time"),
//                                COURSE_STUDY_PROGRESS.COURSE_ID.as("course_id"),
//                                COURSE_STUDY_PROGRESS.IS_REQUIRED.as("is_required"),
//                                COURSE_STUDY_PROGRESS.BEGIN_TIME.as("begin_time"),
//                                COURSE_STUDY_PROGRESS.TYPE.as("type"),
//                                COURSE_STUDY_PROGRESS.MEMBER_ID.as("member_id")
//                        )
//                        .add(
//                                COURSE_INFO.NAME,
//                                COURSE_INFO.COVER,
//                                COURSE_INFO.COVER_PATH,
//                                COURSE_INFO.URL,
//                                COURSE_INFO.VERSION_ID,
//                                COURSE_INFO.STATUS,
//                                COURSE_INFO.PUBLISH_CLIENT)
//                        .end())
//                        .from(COURSE_STUDY_PROGRESS)
//                        .leftJoin(COURSE_INFO).on(COURSE_STUDY_PROGRESS.COURSE_ID.eq(COURSE_INFO.ID))
//                        .where(studyProgressCondition).and(COURSE_STUDY_PROGRESS.MEMBER_ID.in(currentMemberId))
//                        .and(COURSE_INFO.BUSINESS_TYPE.eq(businessType))
//                        .and(COURSE_INFO.STATUS.ne(CourseInfo.STATUS_FIVE_SHELVES)));
//
//        final SelectOrderByStep<Record> stepFinal = studyProgressStep;
//
//        //排序字段
//        SortField<?> sf = studyTimeOrder.map(order ->
//                "asc".equals(order) ? DSL.val(6).asc() : DSL.val(6).desc())
//                .orElse(DSL.val(6).desc());
//
//        Result<Record> result = stepFinal.orderBy(sf).limit(5).fetch();
//
//        return result.map(r -> {
//            CourseInfo record = new CourseInfo();
//            record.setId(r.getValue("course_id") != null ? r.getValue("course_id").toString() : "");
//            record.setName(r.getValue(COURSE_INFO.NAME) != null ? r.getValue(COURSE_INFO.NAME).toString() : "");
//            record.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH) != null ? r.getValue(COURSE_INFO.COVER_PATH).toString() : "");
//            record.setVersionId(r.getValue(COURSE_INFO.VERSION_ID) != null ? r.getValue(COURSE_INFO.VERSION_ID).toString() : "");
//            sectionPadding(record);
//            return record;
//        });
//    }

    /**
     * 去除相关内容，填充完成节数和总节数为1
     * @param record
     */
    private void sectionPadding(CourseInfo record) {
        record.setTotalSectionCount(1);
        record.setCompletedSectionCount(1);
    }

    private List<CourseInfo> getOneFinishRecord(String memberIds, TableImpl<?> courseStudyProgressTable) {
        return courseInfoDao.execute(e ->
                getStudyCourseInfo(memberIds, 1, Arrays.asList(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS,
                        CourseStudyProgress.FINISH_STATUS_FINISH), false,
                        courseStudyProgressTable, e).map(r -> {
                    CourseInfo record = new CourseInfo();
                    record.setId(r.get(COURSE_INFO.ID));
                    record.setName(r.get(COURSE_INFO.NAME));
                    record.setVersionId(r.get(COURSE_INFO.VERSION_ID));
                    return record;
                }));
    }

    /**
     * 根据完成状态 查询对应学员的学习记录
     * @param memberId
     * @param size
     * @param finishStatuses
     * @param courseStudyProgressTable
     * @param e
     * @return
     */
    private Result<Record4<String, String, String, String>> getStudyCourseInfo(String memberId, Integer size, List<Integer> finishStatuses,
                                                                               boolean sort, TableImpl<?> courseStudyProgressTable, DSLContext e) {
        SelectConditionStep<Record4<String, String, String, String>> conditionStep = e.select(COURSE_INFO.ID, COURSE_INFO.NAME, COURSE_INFO.COVER_PATH,
                DSL.ifnull(courseStudyProgressTable.field("t_course_version_id", String.class), COURSE_INFO.VERSION_ID).as(COURSE_INFO.VERSION_ID))
                .from(courseStudyProgressTable)
                .leftJoin(COURSE_INFO)
                .on(courseStudyProgressTable.field("f_course_id", String.class).eq(COURSE_INFO.ID))
                .where(courseStudyProgressTable.field("f_member_id", String.class).eq(memberId))
                .and(courseStudyProgressTable.field("f_finish_status", Integer.class).in(finishStatuses))
                .and(COURSE_INFO.BUSINESS_TYPE.eq(CourseInfo.BUSINESS_TYPE_COURSE))
                .and(COURSE_INFO.STATUS.ne(CourseInfo.STATUS_FIVE_SHELVES));
        if (sort) {
            conditionStep
                    .orderBy(courseStudyProgressTable.field("f_last_access_time", Long.class).desc());
        }
        return conditionStep.limit(size).fetch();
    }

    /**
     * 根据学员ID和课程ID获取对应课程的总章节数和已完成章节数用于计算学习进度
     * 学习进度计算方式：已完成章节数/课程总章节数
     * @param memberIds
     * @param e
     * @param record
     */
    private void computeSectionCount(String memberIds, DSLContext e, CourseInfo record) {
        int totalSectionCount = courseCacheServiceSupport.getCourseTotalSectionCount(record.getId(), record.getVersionId());
        record.setTotalSectionCount(totalSectionCount);

        int completedSectionCount = getCompletedSectionCount(memberIds, record, e);
        record.setCompletedSectionCount(completedSectionCount);
    }



    /**
     * 查询某门课程已完成章节数
     * @param memberId
     * @param record
     * @param e
     * @return
     */
    private int getCompletedSectionCount(String memberId, CourseInfo record, DSLContext e) {
        TableImpl<?> courseSectionStudyProgressTable = CourseSectionStudyProgressUtil.getTable(memberId, record.getId());


        return e.select(DSL.count(courseSectionStudyProgressTable.field("f_id")))
                .from(courseSectionStudyProgressTable)
                .where(courseSectionStudyProgressTable.field("f_member_id", String.class).eq(memberId))
                .and(courseSectionStudyProgressTable.field("f_course_id", String.class).eq(record.getId()))
                .and(courseSectionStudyProgressTable.field("f_finish_status", Integer.class).eq(CourseSectionStudyProgress.FINISH_STATUS_FINISH))
                .fetchOne(DSL.count(courseSectionStudyProgressTable.field("f_id")));
    }

    /**
     * 根据课程id 获取其对应的章节学习记录分表
     * @param courseId
     * @return
     */
    //没有用好像
    private TableImpl<?> getCourseSectionStudyProgressShardingTable(String courseId) {
        return shardingConfigService.getTableName(courseId,
                ShardingConfig.LOGIC_TABLE_COURSE_SECTION_STUDY_PROGRESS,
                ShardingConfig.DEFAULT_TABLE_COURSE_SECTION_STUDY_PROGRESS);
    }

    @Override
    public int timeLeftInSeconds() {
        long now = System.currentTimeMillis();
        long today24 = DateUtil.getTimesnightByLong(now);
        return (int)(today24 - now) / 1000;
    }

//    @Override
//    public Map<String, List<CourseInfo>> updateStudyCard(String memberId,String courseId,Integer size) {
//        //此处studyCard一定不会为空
//        Map<String, List<CourseInfo>> studyRecords = Maps.newHashMap();
////        新需求改造，未完成，已完成，学习中
////        List<CourseInfo> unfinishedRecords = getTopNRecords(memberId, 5, courseStudyProgressTableNew);
////        查找未完成的课程
////        List<CourseInfo> unfinishedRecords = getTopNUnfinishedRecords(memberId, 5, courseStudyProgressTableNew);
////        List<CourseInfo> unfinishedRecords = getTop5Records(0,memberId,Optional.of(0),Optional.empty(),Optional.empty(),Optional.empty(),Optional.of("desc"));
//        String cacheKey = STUDY_PROGRESS_CACHE_KEY + memberId + "-" + size;
//
//        Map<String, List<CourseInfo>> studyCard = getCacheStudyCard(cacheKey);
//        if(studyCard == null){return null;}
//        //LJY 缓存优化
//        List<CourseInfo> unfinishedRecords = studyCard.get("unfinished");
//        //不查数据库，先判断这个课程是否在缓存里
//        CourseInfo newCourse = new CourseInfo();
//        //最新表格
//        List<CourseInfo> newUnfinished = Lists.newArrayList();
//        if(unfinishedRecords.get(0).getId().equals(courseId)){
//            //情况一：课程在第一个位置,不做改动
//            return null;
////            newUnfinished = unfinishedRecords;
//        } else {
//            for(int j = 0; j < unfinishedRecords.size();j++){
//                //如果课程已经存在，让其提到第一个并删除原有的列表
//                if (unfinishedRecords.get(j).getId().equals(courseId)){
//                    //情况二：不在第一位
//                    newCourse = unfinishedRecords.get(j);
//                    unfinishedRecords.remove(j);
//                    newUnfinished.add(newCourse);
//                    newUnfinished.addAll(unfinishedRecords);
//                    break;
//                 }
//            }
//            //此课程不在缓存中
//            if (null == newCourse.getId()) {
//                //情况三：不在列表里
//                newCourse = courseInfoDao.get(courseId);
//                newCourse.setCompletedSectionCount(1);
//                newCourse.setTotalSectionCount(1);
//                if(newUnfinished.size() == 0){
//                    newUnfinished.add(newCourse);
//                    newUnfinished.addAll(unfinishedRecords);
//                }
//            }
//        }
//        //只截取前五
//        if (newUnfinished.size()> size) {
//            newUnfinished = newUnfinished.subList(0, size);
//        }
//        studyRecords.put("unfinished", newUnfinished);
//        studyRecords.put("finished", Arrays.asList(new CourseInfo()));
//        return studyRecords;
//    }

    @Override
    public Map<String, List<CourseInfo>> getCacheStudyCard(String cacheKey) {
        return cache.get(cacheKey, Map.class);
    }

    @Override
    public void setCacheStudyCard(String key, Map<String, List<CourseInfo>> studyCard,int time){
        cache.set(key,studyCard,time);
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public Integer calculateRequiredRate(String courseId, String memberId) {
        Optional<CourseStudyProgress> courseProgress = getCourseStudyProgress(courseId, memberId);

        if (!courseProgress.isPresent()) return 0;
        if (courseProgress.get().isFinish()) return 100;

        // 用户需要必修的章节
        List<String> referenceIds = getRequiredSectionIds(courseId, courseProgress.get().getCourseVersionId());
        if (CollectionUtils.isEmpty(referenceIds)) return 0;

        // 用户已完成数量的章节
        Set<String> finishSectionIds = getRequiredFinishSectionIds(courseId, memberId);
        // 计算完成百分比 并返回
        return calculateCompletionPercentage(referenceIds, finishSectionIds);
    }

    @Override
    public CourseStudyProgress calculateRequiredRateToMap(String courseId, String memberId) {
        // 必要字段: isFinish(通过FinishStatus计算得出)、CourseVersionId、FinishTime
        Optional<CourseStudyProgress> courseProgress = getCourseStudyProgress(courseId, memberId);

        CourseStudyProgress course;
        if (!courseProgress.isPresent()) {
            course = new CourseStudyProgress();
            course.setCompletedRate(0);
            course.setFinishTime(0L);
            return course;
        } else {
            course = courseProgress.get();
            if (course.getFinishNum() == null) course.setFinishTime(0L);
        }

        if (courseProgress.get().isFinish()) {
            course.setCompletedRate(100);
            return course;
        }

        // 用户需要必修的章节(课程节资源Id)
        List<String> referenceIds = getRequiredSectionIds(courseId, courseProgress.get().getCourseVersionId());
        if (CollectionUtils.isEmpty(referenceIds)) {
            course.setCompletedRate(100);
            course.setFinishTime(0L);
            return course;
        }

        // 用户已完成数量的章节
        Set<String> finishSectionIds = getRequiredFinishSectionIds(courseId, referenceIds, memberId);
        // 计算完成百分比
        int percentage = calculateCompletionPercentage(referenceIds, finishSectionIds);
        course.setCompletedRate(percentage);
        return course;
    }

    @DataSource(type = DataSourceEnum.SLAVE)
    private Optional<CourseStudyProgress> getCourseStudyProgress(String courseId, String memberId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return Optional.ofNullable(courseStudyProgressCommonDao.execute(dslContext -> dslContext
                .select(cacheTable.field("f_id", String.class), cacheTable.field("t_course_version_id", String.class),
                        cacheTable.field("f_finish_status", Integer.class), cacheTable.field("f_finish_time", Long.class)
                )
                .from(cacheTable)
                .where(cacheTable.field("f_member_id", String.class).eq(memberId),
                        cacheTable.field("f_course_id", String.class).eq(courseId))
                .limit(1)
                .fetchOne(r -> r.into(CourseStudyProgress.class))
        ));
    }

    private int calculateCompletionPercentage(List<String> referenceIds, Set<String> finishSectionIds) {
        long count = referenceIds.stream().filter(finishSectionIds::contains).count();
        BigDecimal allCount = BigDecimal.valueOf(referenceIds.size());
        BigDecimal finishCount = BigDecimal.valueOf(count);
        return finishCount.divide(allCount, 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100))
                .intValue();
    }


    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public Map<String,Integer> calculateRequiredRate(String courseId,List<String> memberIds) {

        Map<String, List<String>> tableMemberIdMap = new HashMap<>();
        Map<String, TableImpl<?>> tableMap = new HashMap<>();
        for (String id : memberIds) {
            TableImpl<?> cacheTable = courseCacheService.getCacheTable(id, SplitTableConfig.COURSE_STUDY_PROGRESS);
            tableMemberIdMap.computeIfAbsent(cacheTable.getName(), c-> new ArrayList<>()).add(id);
            tableMap.put(cacheTable.getName(), cacheTable);
        }
        List<CourseStudyProgress> courseStudyProgress = new ArrayList<>();
        for (Map.Entry<String, List<String>> entry : tableMemberIdMap.entrySet()) {
            TableImpl<?> table = tableMap.get(entry.getKey());
            List<String> ids = entry.getValue();
            //查询完成状态
            List<CourseStudyProgress> list = courseStudyProgressCommonDao.execute(dslContext -> dslContext
                    .select(Fields.start().add(table.fields()).end())
                    .from(table)
                    .where(table.field("f_member_id",String.class).in(ids), table.field("f_course_id",String.class).eq(courseId))
                    .fetchInto(CourseStudyProgress.class));
            courseStudyProgress.addAll(list);
        }

        //没有值，返回空
        if (CollectionUtils.isEmpty(courseStudyProgress)) return null;
        //计算已经完成的人员，将完成率设置成100
        Map<String, Integer> memberIdsMap = courseStudyProgress.stream().filter(CourseStudyProgress::isFinish).collect(Collectors.toMap(CourseStudyProgress::getMemberId, progress -> {
            return 100;
        }));
        // 用户需要必修的章节
        List<String> referenceIds = getRequiredSectionIds(courseId, courseStudyProgress.stream().findFirst().get().getCourseVersionId());

        //去除已经完成的人员
        List<String> memberIdsfilter = memberIds.stream().filter(m -> {
            return !memberIdsMap.keySet().contains(m);
        }).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(referenceIds)) {
            //如果没有必修，所有的完成度为0 处理垃圾数据
            Map<String, Integer> collect = memberIdsfilter.stream().collect(Collectors.toMap(
                    memberid -> memberid,
                    memberid -> {
                        return 0;
                    }));
            memberIdsMap.putAll(collect);
            return memberIdsMap;
        }
        //用户已完成数量的章节
        Map<String, Integer> requiredFinishSectionIds = getRequiredFinishSectionIds(courseId, memberIdsfilter);
        //计算完成率
        Map<String, Integer> collect = memberIdsfilter.stream().collect(Collectors.toMap(
                memberid -> memberid,
                memberid -> {
                    Integer finishNum = requiredFinishSectionIds.get(memberid) == null ? 0 : requiredFinishSectionIds.get(memberid);
                    //学习计划完成率
                    BigDecimal allCount = BigDecimal.valueOf(referenceIds.size());
                    BigDecimal finishCount = BigDecimal.valueOf(finishNum);
                    int completeRate = finishCount.divide(allCount, 2, RoundingMode.HALF_UP).multiply(BigDecimal.valueOf(100)).intValue();//完成率
                    return completeRate;
                }));
        //添加已完成的完成率
        collect.putAll(memberIdsMap);
        return collect;
    }

    @Override
    public Map<String, String> findCourseVersionIds(String memberId, List<String> courseIds) {
        //当前最新版本
        Map<String, String> versionMap = courseInfoDao.execute(dls -> dls.select(COURSE_INFO.ID, COURSE_INFO.VERSION_ID).from(COURSE_INFO).where(COURSE_INFO.ID.in(courseIds)).fetchMap(COURSE_INFO.ID, COURSE_INFO.VERSION_ID));
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        //人员对应的版本
        Map<String, String> existedMap = courseStudyProgressCommonDao.execute(dls ->
                dls.select(cacheTable.field("f_course_id",String.class), cacheTable.field("t_course_version_id",String.class))
                        .from(cacheTable)
                        .where(cacheTable.field("f_member_id",String.class).eq(memberId))
                        .and(cacheTable.field("f_course_id",String.class).in(courseIds))
                        .fetchMap(cacheTable.field("f_course_id",String.class), cacheTable.field("t_course_version_id",String.class))
        );
        //替换
        versionMap.putAll(existedMap);
        return versionMap;
    }

    @Override
    @DataSource(type = DataSourceEnum.SLAVE)
    public Long getCourseFinishTime(String courseId, String memberId) {
        TableImpl<?> cacheTable = courseCacheService.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);
        return courseStudyProgressCommonDao.execute(dsl->
                dsl.select(cacheTable.field("f_finish_time",Long.class))
                        .from(cacheTable)
                        .where(cacheTable.field("f_member_id",String.class).eq(memberId))
                        .and(cacheTable.field("f_course_id",String.class).eq(courseId))
                        .fetchOne(cacheTable.field("f_finish_time",Long.class))
                );
    }

    private Set<String> getRequiredFinishSectionIds(String courseId, List<String> referenceIds, String memberId) {
        TableImpl<?> sectionTable = CourseSectionStudyProgressUtil.getTable(memberId, courseId);
        CourseSectionStudyProgressUtil.TableField tableField = CourseSectionStudyProgressUtil.initTableField(sectionTable);

        Condition condition = tableField.MEMBER_ID().eq(memberId)
                .and(tableField.COURSE_ID().eq(courseId))
                .and(tableField.FINISH_STATUS().in(CourseSectionStudyProgress.FINISH_STATUS_FINISH,CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS));

        if(CollectionUtils.isEmpty(referenceIds)) {
            condition.and(tableField.SECTION_ID().in(referenceIds));
        }

        return sectionDao.execute(x ->
                x.select(tableField.SECTION_ID()).from(sectionTable)
                        .where(condition)
                        .fetchSet(tableField.SECTION_ID()));
    }

    private Set<String> getRequiredFinishSectionIds(String courseId, String memberId) {
        return getRequiredFinishSectionIds(courseId, Collections.emptyList(), memberId);
    }

    private Map<String,Integer> getRequiredFinishSectionIds(String courseId, List<String> memberIds) {
        Map<String, Integer> resourceMap = new HashMap<>();

        for(String memberId : memberIds) {
            TableImpl<?> sectionTable = CourseSectionStudyProgressUtil.getTable(memberId, courseId);
            CourseSectionStudyProgressUtil.TableField tableField = CourseSectionStudyProgressUtil.initTableField(sectionTable);
            Map<String, Integer> execute = sectionDao.execute(x ->
                    x.select(tableField.MEMBER_ID(), tableField.SECTION_ID().countDistinct().as("count"))
                            .from(sectionTable)
                            .where(tableField.MEMBER_ID().in(memberId))
                            .and(tableField.COURSE_ID().eq(courseId))
                            .and(tableField.FINISH_STATUS().in(CourseSectionStudyProgress.FINISH_STATUS_FINISH, CourseSectionStudyProgress.FINISH_STATUS_MARKSUCCESS))
                            .groupBy(tableField.MEMBER_ID())
                            .fetchMap(tableField.MEMBER_ID(), DSL.field("count", Integer.class)));
            if (Objects.nonNull(execute) && Objects.nonNull(execute.get(memberId))){
                resourceMap.put(memberId, execute.get(memberId));
            }
        }
        return resourceMap;
    }

    private List<String> getRequiredSectionIds(String courseId, String versionId) {
        return sectionDao.execute(x -> x.select(COURSE_CHAPTER_SECTION.REFERENCE_ID).from(COURSE_CHAPTER_SECTION)
                        .innerJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID)))
//                .leftJoin(COURSE_INFO).on(COURSE_CHAPTER_SECTION.RESOURCE_ID.eq(COURSE_INFO.ID))
                .where(COURSE_CHAPTER.COURSE_ID.eq(courseId)
                        .and(COURSE_CHAPTER.VERSION_ID.eq(versionId))
//                        .and(COURSE_INFO.STATUS.notEqual(CourseInfo.STATUS_FIVE_SHELVES)) //过滤退库数据
                        .and(COURSE_CHAPTER_SECTION.REQUIRED.eq(CourseChapterSection.IS_REQUIRED)))
                .fetch(COURSE_CHAPTER_SECTION.REFERENCE_ID);
    }

    @Override
    public Map<String, List<CourseInfo>> updateStudyCard(String memberId,String courseId,Integer size) {
        String cacheKey = STUDY_PROGRESS_CACHE_KEY + memberId + "-" + size;
        Map<String, List<CourseInfo>> studyCard = getCacheStudyCard(cacheKey);
        log.info("GET INTO###updateStudyCard,cacheKey={}  -- LJY", cacheKey);
        log.info("###updateStudyCard,studyCard={} -- LJY", studyCard);
        //未初始化，需要初始化
        if(studyCard == null){return null;}
        //LJY 缓存优化
        List<CourseInfo> unfinishedRecords = studyCard.get("unfinished");
        if(unfinishedRecords == null){return null;}
        if(unfinishedRecords.size()==0){
//            CourseInfo newCourse = courseInfoDao.get(courseId);
            CourseInfo newCourse = courseInfoDao.execute(x -> x.select(COURSE_INFO.ID,COURSE_INFO.NAME,COURSE_INFO.COVER_PATH).
                    from(COURSE_INFO).
                    where(COURSE_INFO.ID.eq(courseId))).
                    fetchOptional(r -> {
                        CourseInfo info = new CourseInfo();
                        info.setId(r.getValue(COURSE_INFO.ID));
                        info.setName(r.getValue(COURSE_INFO.NAME));
                        info.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH));
                        return info;
                    }).orElse(null);
            // 查不到对应数据，不进行处理,修复逻辑缺陷，防止攻击
            if (newCourse == null || newCourse.getId() == null) return null;
            newCourse.setCompletedSectionCount(1);
            newCourse.setTotalSectionCount(1);
            unfinishedRecords.add(newCourse);
            studyCard.put("unfinished", unfinishedRecords);
            //studyCard.put("finished", Arrays.asList(new CourseInfo()));
            log.info("###updateStudyCard -- unfinishedRecords.size()==0时,studyCard={}", studyCard);
            return studyCard;
        }
        //不查数据库，先判断这个课程是否在缓存里
        CourseInfo newCourse = new CourseInfo();
        List<CourseInfo> newUnfinished = Lists.newArrayList();
        if(unfinishedRecords.get(0).getId().equals(courseId)){
            log.info("###updateStudyCard -- 有值时,情况1:,unfinishedRecords={}", unfinishedRecords);
            //情况一：课程在第一个位置,不做改动,缓存也无需更新
            return null;
        } else {
            for(int j = 1; j < unfinishedRecords.size();j++){
                //如果课程已经存在，让其提到第一个并删除原有的列表
                if (unfinishedRecords.get(j).getId().equals(courseId)){
                    //情况二：不在第一位，更新最新课程的courseID属性，不为空
                    newCourse = unfinishedRecords.get(j);
                    unfinishedRecords.remove(j);
                    newUnfinished.add(newCourse);
                    newUnfinished.addAll(unfinishedRecords);
                    log.info("###updateStudyCard -- 有值时,情况2:,newUnfinished={}", newUnfinished);
                    break;
                }
            }
            //此课程不在缓存中，此属性为空
            if (null == newCourse.getId()) {
                //情况三：不在列表里
//                newCourse = courseInfoDao.get(courseId);
                newCourse = courseInfoDao.execute(x -> x.select(COURSE_INFO.ID,COURSE_INFO.NAME,COURSE_INFO.COVER_PATH).
                        from(COURSE_INFO).
                        where(COURSE_INFO.ID.eq(courseId))).
                        fetchOptional(r -> {
                            CourseInfo info = new CourseInfo();
                            info.setId(r.getValue(COURSE_INFO.ID));
                            info.setName(r.getValue(COURSE_INFO.NAME));
                            info.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH));
                            return info;
                        }).orElse(null);
                // 查不到对应数据，不进行处理,修复逻辑缺陷，防止攻击
                if (newCourse == null || newCourse.getId() == null) return null;
                newCourse.setCompletedSectionCount(1);
                newCourse.setTotalSectionCount(1);
                if(newUnfinished.size() == 0){
                    newUnfinished.add(newCourse);
                    newUnfinished.addAll(unfinishedRecords);
                }
                log.info("###updateStudyCard -- 有值时,情况3:,newUnfinished={}", newUnfinished);
            }
        }
        //只截取前五
        if (newUnfinished.size()> size) {
            newUnfinished = newUnfinished.subList(0, size);
            log.info("###updateStudyCard -- 有值时,情况3:,newUnfinished={},size={}", newUnfinished,size);
        }
        Map<String, List<CourseInfo>> studyRecords = Maps.newHashMap();
        studyRecords.put("unfinished", newUnfinished);
        studyRecords.put("finished", Arrays.asList(new CourseInfo()));
        log.info("###updateStudyCard -- 更新:,studyRecords={}", studyRecords);
        return studyRecords;
    }

    /**
     * 优化课程，从未完成，学习中，已完成中挑选最近的数据
     * @param businessType
     * @param currentMemberId
     * @param findStudy
     * @param name
     * @param finishStatus
     * @param isRequired
     * @param studyTimeOrder
     */
    private List<CourseInfo> getTopRecords(Integer businessType, String currentMemberId, Optional<Integer> findStudy, Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired, Optional<String> studyTimeOrder,Integer size) {

        TableImpl<?> table = courseCacheServiceSupport.getCacheTable(currentMemberId, SplitTableConfig.COURSE_STUDY_PROGRESS);

        Condition studyProgressCondition = Stream.of(
                name.map(COURSE_INFO.NAME::contains),
                finishStatus.map(s -> {
                    if (s == CourseStudyProgress.FINISH_STATUS_FINISH) {
                        return table.field("f_finish_status", Integer.class).in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
                    }
                    return table.field("f_finish_status", Integer.class).eq(s);
                }),
                isRequired.map(table.field("f_is_required", Integer.class)::eq),
                findStudy.map(study -> table.field("f_finish_status", Integer.class).ne(CourseStudyProgress.FINISH_STATUS_GIVEUP))
        ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());

        SelectConditionStep<Record> studyProgressStep = courseStudyProgressCommonDao.execute(e ->
                e.select(Fields.start()
                        .add(
                                table.field("f_id", String.class).as("id"),
                                table.field("f_finish_status", Integer.class).as("finish_status"),
                                table.field("f_finish_time", Long.class).as("finish_time"),
                                table.field("f_register_time", Long.class).as("register_time"),
                                table.field("f_study_total_time", Integer.class).as("study_total_time"),
                                table.field("f_last_access_time", Long.class).as("last_access_time"),
                                table.field("f_course_id", String.class).as("course_id"),
                                table.field("f_is_required", Integer.class).as("is_required"),
                                table.field("f_begin_time", Long.class).as("begin_time"),
                                table.field("f_type", Integer.class).as("type"),
                                table.field("f_member_id", String.class).as("member_id")
                        )
                        .add(
                                COURSE_INFO.NAME,
                                COURSE_INFO.COVER,
                                COURSE_INFO.COVER_PATH,
                                COURSE_INFO.URL,
                                COURSE_INFO.VERSION_ID,
                                COURSE_INFO.STATUS,
                                COURSE_INFO.PUBLISH_CLIENT)
                        .end())
                        .from(table)
                        .leftJoin(COURSE_INFO).on(table.field("f_course_id", String.class).eq(COURSE_INFO.ID))
                        .where(studyProgressCondition).and(table.field("f_member_id", String.class).in(currentMemberId))
                        .and(COURSE_INFO.BUSINESS_TYPE.eq(businessType))
                        .and(COURSE_INFO.STATUS.ne(CourseInfo.STATUS_FIVE_SHELVES)));

        final SelectOrderByStep<Record> stepFinal = studyProgressStep;

        //排序字段
        SortField<?> sf = studyTimeOrder.map(order ->
                "asc".equals(order) ? DSL.val(6).asc() : DSL.val(6).desc())
                .orElse(DSL.val(6).desc());

//        Result<Record> result = stepFinal.orderBy(sf).limit(5).fetch();
        Result<Record> result = stepFinal.orderBy(sf).limit(size).fetch();

        return result.map(r -> {
            CourseInfo record = new CourseInfo();
            record.setId(r.getValue("course_id") != null ? r.getValue("course_id").toString() : null);
            record.setName(r.getValue(COURSE_INFO.NAME) != null ? r.getValue(COURSE_INFO.NAME).toString() : null);
            record.setCoverPath(r.getValue(COURSE_INFO.COVER_PATH) != null ? r.getValue(COURSE_INFO.COVER_PATH).toString() : null);
            record.setVersionId(r.getValue(COURSE_INFO.VERSION_ID) != null ? r.getValue(COURSE_INFO.VERSION_ID).toString() : null);
            sectionPadding(record);
            return record;
        });
    }

    @Override
    public CourseSectionStudyProgress updateBroadcastStudyProgress(String currentUserId, String sectionId, Integer clientType, Optional<Integer> lessonLocation, Long beginTime) {
        CourseChapterSection section = sectionDao.getOptional(sectionId).orElse(null);
        return defaultCourseSectionStudyProgress(currentUserId, sectionId, clientType, 0, beginTime,
                lessonLocation.map(String::valueOf), Optional.empty(), p -> {
                    p.setCompletedRate(100);
                    p.setFinishStatus(CourseSectionStudyProgress.FINISH_STATUS_FINISH);
                }, System.currentTimeMillis());
    }

    @Override
    public CourseSectionStudyProgress updateCoachTestsStudyProgress(String sectionId, String memberId, Integer finishStatus, Long finishTime) {
        return defaultCourseSectionStudyProgress(memberId, sectionId, 0, 0, 0L, Optional.of("0"), Optional.empty(),
                process -> {
                    process.setCommitTime(finishTime);
                    process.setCompletedRate(100);
                    process.setFinishStatus(finishStatus.equals(1) ? CourseSectionStudyProgress.FINISH_STATUS_FINISH : CourseSectionStudyProgress.FINISH_STATUS_UNSTART);
                    process.setComments(null);
                }, System.currentTimeMillis());
    }

    private Integer getTestsFinishStatus(Integer finishStatus) {
        if (null == finishStatus){
            return CourseSectionStudyProgress.FINISH_STATUS_STUDY;
        }
        if (finishStatus.equals(0)){
            return CourseSectionStudyProgress.FINISH_STATUS_NOT_THROUGH;
        }
        if (finishStatus.equals(1)){
            return CourseSectionStudyProgress.FINISH_STATUS_FINISH;
        }
        return CourseSectionStudyProgress.FINISH_STATUS_UNSTART;
    }

    @Override
    public PagedResult<CourseStudyProgress> findPage(int pageNum, int pageSize, String courseId,
                                                     Optional<String> memberName, Optional<String> memberReadName, Optional<String> organizationPath,
                                                     Optional<Integer> finishStatus, Optional<Integer> requiredStatus, Optional<Integer> isDisable, Optional<Long> beginBeginDate, Optional<Long> beginEndDate,
                                                     Optional<Long> beginLearnDate, Optional<Long> endLearnDate,Optional<Long> beginRegisterDate, Optional<Long> endRegisterDate) {
        // 查询的字段
        List<Field<?>> selectFields = Lists.newArrayList(COURSE_STUDY_PROGRESS.ID
            ,COURSE_STUDY_PROGRESS.COURSE_ID
            ,COURSE_STUDY_PROGRESS.FINISH_STATUS
            ,COURSE_STUDY_PROGRESS.BEGIN_TIME
            ,COURSE_STUDY_PROGRESS.FINISH_TIME
            ,COURSE_STUDY_PROGRESS.TYPE
            ,COURSE_STUDY_PROGRESS.IS_REQUIRED
            ,COURSE_STUDY_PROGRESS.MARK_TIME
            ,COURSE_STUDY_PROGRESS.REGISTER_TIME
            ,COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME
            ,COURSE_STUDY_PROGRESS.MEMBER_ID
            ,COURSE_STUDY_PROGRESS.MARK_MEMBER_ID
            ,COURSE_STUDY_PROGRESS.CREATE_TIME
            ,COURSE_STUDY_PROGRESS.COMPLETED_RATE
        );
        List<Field<?>> memberFields = Arrays.asList(
            MEMBER.ID, MEMBER.NAME, MEMBER.FULL_NAME, MEMBER.ORGANIZATION_ID, MEMBER.STATUS, MEMBER.MAJOR_POSITION_ID
        );
        List<Field<?>> organizationFields = Arrays.asList(
            ORGANIZATION.ID, ORGANIZATION.NAME
        );

        List<Condition> progressParams = Stream.of(finishStatus.map(f -> {
                                                       Condition c = COURSE_STUDY_PROGRESS.FINISH_STATUS.eq(f);
                                                       if (Objects.equals(CourseStudyProgress.FINISH_STATUS_FINISH, f)) {
                                                           c = COURSE_STUDY_PROGRESS.FINISH_STATUS.in(CourseStudyProgress.FINISH_STATUS_FINISH,CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
                                                       }
                                                       return c;
                                                   }),
                                                   requiredStatus.map(COURSE_STUDY_PROGRESS.IS_REQUIRED::eq),
                                                   beginBeginDate.map(COURSE_STUDY_PROGRESS.FINISH_TIME::ge),
                                                   beginEndDate.map(COURSE_STUDY_PROGRESS.FINISH_TIME::le),
                                                   beginLearnDate.map(COURSE_STUDY_PROGRESS.BEGIN_TIME::ge),
                                                   endLearnDate.map(COURSE_STUDY_PROGRESS.BEGIN_TIME::le),
                                                   beginRegisterDate.map(COURSE_STUDY_PROGRESS.REGISTER_TIME::ge),
                                                   endRegisterDate.map(COURSE_STUDY_PROGRESS.REGISTER_TIME::le))
                                               .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        progressParams.add(COURSE_STUDY_PROGRESS.COURSE_ID.eq(courseId));

        List<Condition> memberParams = Stream.of(memberName.map(MEMBER.NAME::contains),
                                                 memberReadName.map(MEMBER.FULL_NAME::contains),
                                                 isDisable.map(MEMBER.STATUS::ne),
                                                 organizationPath.map(ORGANIZATION.PATH::startsWith))
                                             .filter(Optional::isPresent).map(Optional::get).collect(Collectors.toList());
        boolean hasMemberParams = !CollectionUtils.isEmpty(memberParams);

        PagedResult<CourseStudyProgress> pagedResult = courseStudyProgressCommonDao.execute(e -> {
            //拼接查询条件参数
            if (hasMemberParams) {
                memberParams.add(COURSE_STUDY_PROGRESS.MEMBER_ID.eq(MEMBER.ID));
                SelectJoinStep<Record1<String>> memberFrom = e.select(MEMBER.ID).from(MEMBER);
                if (organizationPath.isPresent()) {
                    memberFrom.join(ORGANIZATION).on(MEMBER.ORGANIZATION_ID.eq(ORGANIZATION.ID));
                }
                Condition exists = DSL.exists(memberFrom.where(memberParams));
                progressParams.add(exists);
            }

            //查询count总数
            SelectConditionStep<Record1<Integer>> countStep = e.select(DSL.count(COURSE_STUDY_PROGRESS.ID))
                                                               .from(COURSE_STUDY_PROGRESS).where(progressParams);
            int count = countStep.fetchOptional(CommonConstant.ZERO, Integer.class).orElse(CommonConstant.ZERO);
            if (CommonConstant.ZERO == count) {
                return PagedResult.create(count, new ArrayList<>());
            }

            //只查ID，这里是为了避免慢SQL，导致接口超时
            SelectConditionStep<Record1<String>> listStep = e.select(COURSE_STUDY_PROGRESS.ID)
                                                             .from(COURSE_STUDY_PROGRESS).where(progressParams);
            SelectForUpdateStep<Record1<String>> select = listStep.orderBy(COURSE_STUDY_PROGRESS.CREATE_TIME.desc(), COURSE_STUDY_PROGRESS.ID.desc())
                                                                 .limit((pageNum - 1) * pageSize, pageSize);
            log.info("CourseStudyProgressServiceSupport.findPage ==> SQL:{}", select.getSQL(true));

            List<String> progressIds = select.fetch(COURSE_STUDY_PROGRESS.ID);

            //再根据ID查询详细信息
            List<CourseStudyProgress> courseStudyProgressList = e.select(selectFields)
                                                                 .from(COURSE_STUDY_PROGRESS)
                                                                 .where(COURSE_STUDY_PROGRESS.ID.in(progressIds))
                                                                 .fetch().into(COURSE_STUDY_PROGRESS).into(CourseStudyProgress.class);
            List<CourseStudyProgress> sortedProgressList = courseStudyProgressList.stream().sorted(Comparator.comparing(CourseStudyProgress::getCreateTime).reversed()).collect(Collectors.toList());
            return PagedResult.create(count, sortedProgressList);
        });

        List<CourseStudyProgress> courseStudyProgressList = pagedResult.getItems();

        if (!CollectionUtils.isEmpty(courseStudyProgressList)) {
            Set<String> memberIds = new HashSet<>();
            Set<String> markMemberIds = new HashSet<>();
            for (CourseStudyProgress progress : courseStudyProgressList) {
                String memberId = progress.getMemberId();
                String markMemberId = progress.getMarkMemberId();
                if (org.springframework.util.StringUtils.hasText(memberId)) {
                    memberIds.add(memberId);
                }
                if (org.springframework.util.StringUtils.hasText(markMemberId)) {
                    memberIds.add(markMemberId);
                    markMemberIds.add(markMemberId);
                }
            }
            //单表查人员信息，再往CourseStudyProgress拼装
            List<Member> memberList = memberDao.execute(e -> e.select(memberFields)
                                                              .from(MEMBER)
                                                              .where(MEMBER.ID.in(memberIds)))
                                               .fetch().into(MEMBER).into(Member.class);
            if (!CollectionUtils.isEmpty(memberList)) {
                Map<String, Member> memberMap = memberList.stream().collect(Collectors.toMap(Member::getId, Function.identity()));

                //单表查组织信息，再往member拼装
                List<String> organizationIds = memberList.stream().map(Member::getOrganizationId).collect(Collectors.toList());
                List<Organization> organizationList = orgDao.execute(e -> e.select(organizationFields)
                                                                                          .from(ORGANIZATION)
                                                                                          .where(ORGANIZATION.ID.in(organizationIds)))
                                                                           .fetch().into(ORGANIZATION).into(Organization.class);
                if (!CollectionUtils.isEmpty(organizationIds)) {
                    Map<String, Organization> organizationMap = organizationList.stream().collect(Collectors.toMap(Organization::getId, Function.identity()));
                    for (Member member : memberList) {
                        member.setOrganization(organizationMap.get(member.getOrganizationId()));
                    }
                }
                for (CourseStudyProgress progress : courseStudyProgressList) {
                    String memberId = progress.getMemberId();
                    Member member = memberMap.get(memberId);
                    progress.setMember(member);
                    if (markMemberIds.contains(memberId)) {
                        progress.setMarkMember(member);
                    }
                }
            }
        }
        return pagedResult;
    }

    @Override
    public Map<String, Integer> sumStudyTimeByIdAndMemberIds(String id, List<String> memberIds) {
        return courseStudyProgressCommonDao.execute(dslContext -> dslContext
            .select(COURSE_STUDY_PROGRESS.MEMBER_ID.as("memberId"),
                COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME.as("studyTotalTime")).from(COURSE_STUDY_PROGRESS)
            .where(COURSE_STUDY_PROGRESS.MEMBER_ID.in(memberIds)).and(COURSE_STUDY_PROGRESS.COURSE_ID.eq(id))
            .fetchStream()
            .collect(Collectors.toMap(Record2::value1, Record2::value2)));
    }


    @Override
    public Map<String, Integer> sumFinishSectionByIdAndMemberIds(String id, List<String> memberIds) {
        // 查询专题对应的课程或资源ID
        Map<Integer, List<String>> resourceIdMap = sectionDao.execute(dls ->
            dls.select(COURSE_CHAPTER_SECTION.SECTION_TYPE, COURSE_CHAPTER_SECTION.RESOURCE_ID)
               .from(COURSE_CHAPTER_SECTION)
               .where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(id))
               .fetch().intoGroups(COURSE_CHAPTER_SECTION.SECTION_TYPE, COURSE_CHAPTER_SECTION.RESOURCE_ID));

        // 查询这个专题下课程的完成数量
        Map<String, Integer> m1 = new HashMap<>();
        List<String> courseTypeIds = resourceIdMap.get(CourseChapterSection.SECTION_TYPE_COURSE);
        if(!CollectionUtils.isEmpty(courseTypeIds)) {
            m1 =courseStudyProgressCommonDao.execute(dslContext -> dslContext
                .select(COURSE_STUDY_PROGRESS.MEMBER_ID, DSL.count(COURSE_STUDY_PROGRESS.MEMBER_ID))
                .from(COURSE_STUDY_PROGRESS)
                .where(COURSE_STUDY_PROGRESS.MEMBER_ID.in(memberIds))
                .and(COURSE_STUDY_PROGRESS.COURSE_ID.in(courseTypeIds))
                .and(COURSE_STUDY_PROGRESS.FINISH_STATUS.in(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS,CourseStudyProgress.FINISH_STATUS_FINISH))
                .groupBy(COURSE_STUDY_PROGRESS.MEMBER_ID).fetchStream()
                .collect(Collectors.toMap(Record2::value1, Record2::value2)));
        }
        return m1;
    }

    @Override
    public List<CourseSectionStudyProgress> subjectSectionProgress(String memberId, String subjectId) {
        //查询章节信息
        List<CourseChapterSection> sectionList = sectionDao.execute(e -> e.select(Fields.start().add(COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.SECTION_TYPE,
                                                                              COURSE_CHAPTER_SECTION.CHAPTER_ID,
                                                                              COURSE_CHAPTER_SECTION.NAME, COURSE_CHAPTER_SECTION.SEQUENCE,
                                                                              COURSE_CHAPTER_SECTION.REQUIRED).end())
                                                                          .from(COURSE_CHAPTER_SECTION)
                                                                          .innerJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                                                                          .innerJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID), COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID))
                                                                          .where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(subjectId))
                                                                          .fetch().into(COURSE_CHAPTER_SECTION).into(CourseChapterSection.class));
        log.info("SubjectProcessServiceSupport.subjectSectionProgress ===> memberId:{}, subjectId:{}， sectionList:{}", memberId, subjectId, sectionList);
        if (CollectionUtils.isEmpty(sectionList)) {
            return Lists.newArrayList();
        }

        List<String> resourceIds = new ArrayList<>(sectionList.size());
        Map<String, CourseChapterSection> sectionMap = new HashMap<>(sectionList.size());
        for (CourseChapterSection courseChapterSection : sectionList) {
            String resourceId = courseChapterSection.getResourceId();
            if (!StringUtils.isBlank(resourceId)) {
                resourceIds.add(resourceId);
                sectionMap.put(resourceId, courseChapterSection);
            }
        }

        List<CourseSectionStudyProgress> allProgressList = new ArrayList<>();
        //查询当前人的章节学习记录（不包含课程进度，因为课程进度不会写入到这张表）
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, CourseSectionStudyProgressUtil.BUSINESS_ID);
        List<CourseSectionStudyProgress> progressWithoutCourseList = courseSectionStudyProgressCommonDao.execute(e -> e.select(Fields.start()
                                                                                                                               .add(csspTable.fields()).end())
                                                                                                                 .from(csspTable)
                                                                                                                 .where(csspTable.field("f_course_id", String.class).eq(subjectId),csspTable.field("f_member_id", String.class).eq(memberId))
                                                                                                                 .fetch(r-> new CourseSectionStudyProgress().fill(csspTable,r)));
        log.info("SubjectProcessServiceSupport.subjectSectionProgress ===> progressWithoutCourseList:{}", progressWithoutCourseList);

        for (CourseSectionStudyProgress progress : progressWithoutCourseList) {
            //正常来说，课程的进度不会进到COURSE_SECTION_STUDY_PROGRESS表，这里再做个兼容处理处理，fix：zxy-2812
            if (!Objects.equals(CourseChapterSection.SECTION_TYPE_COURSE, progress.getSectionType())) {
                allProgressList.add(progress);
            }
        }

        //查询当前人的章节学习记录（只查课程进度）
        List<CourseStudyProgress> courseStudyProgressList = courseStudyProgressCommonDao.execute(e -> e.select(Fields.start()
                                                                                                                     .add(COURSE_STUDY_PROGRESS.ID,COURSE_STUDY_PROGRESS.FINISH_STATUS,COURSE_STUDY_PROGRESS.FINISH_TIME,COURSE_STUDY_PROGRESS.BEGIN_TIME,
                                                                                                                         COURSE_STUDY_PROGRESS.COURSE_ID,COURSE_STUDY_PROGRESS.MEMBER_ID, COURSE_STUDY_PROGRESS.COMPLETED_RATE,
                                                                                                                         COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME,COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME).end())
                                                                                                       .from(COURSE_STUDY_PROGRESS)
                                                                                                       .where(COURSE_STUDY_PROGRESS.COURSE_ID.in(resourceIds), COURSE_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
                                                                                                       .fetch().into(COURSE_STUDY_PROGRESS).into(CourseStudyProgress.class));
        log.info("SubjectProcessServiceSupport.subjectSectionProgress ===> courseStudyProgressList:{}", courseStudyProgressList);

        for (CourseStudyProgress x : courseStudyProgressList) {
            CourseSectionStudyProgress progress = new CourseSectionStudyProgress();

            progress.setId(x.getId());
            progress.setFinishStatus(x.getFinishStatus());
            progress.setSectionType(CourseChapterSection.SECTION_TYPE_COURSE);
            progress.setStudyTotalTime(x.getStudyTotalTime());
            progress.setBeginTime(x.getBeginTime());
            progress.setResourceId(x.getCourseId());
            progress.setCourseId(x.getCourseId());
            progress.setMemberId(x.getMemberId());
            progress.setFinishTime(x.getFinishTime());
            if (x.getFinishStatus() != null && Integer.valueOf(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS).equals(x.getFinishStatus())) {
                progress.setCompletedRate(CommonConstant.ONE_HUNDRED);
            } else {
                progress.setCompletedRate(x.getCompletedRate());
            }
            progress.setLastAccessTime(x.getLastAccessTime());

            allProgressList.add(progress);
        }

        Map<String, CourseSectionStudyProgress> progressMap = allProgressList.stream().collect(Collectors.toMap(CourseSectionStudyProgress::getResourceId,x->x,(a,b)->a));
        Map<String, String> abilityMap = abilityDao.execute(x -> x.select(COURSE_CHAPTER.ID, ABILITY.NAME)
                                                                  .from(COURSE_CHAPTER)
                                                                  .innerJoin(ABILITY).on(COURSE_CHAPTER.ABILITY_ID.eq(ABILITY.ID))
                                                                  .where(COURSE_CHAPTER.ID.in(sectionList.stream().map(CourseChapterSection::getChapterId).filter(Objects::nonNull)
                                                                                                  .distinct().collect(Collectors.toList())))
                                                                  .fetchMap(COURSE_CHAPTER.ID, ABILITY.NAME));


        return sectionList.stream().map(x -> {
            CourseSectionStudyProgress courseSectionStudyProgress = progressMap.getOrDefault(x.getResourceId(), new CourseSectionStudyProgress());
            courseSectionStudyProgress.setFinishStatus(
                Optional.ofNullable(courseSectionStudyProgress.getFinishStatus())
                        .orElse(CourseSectionStudyProgress.FINISH_STATUS_UNSTART)
            );
            courseSectionStudyProgress.setSectionType(x.getSectionType());
            courseSectionStudyProgress.setSectionName(x.getName());
            courseSectionStudyProgress.setRequired(x.getRequired());
            courseSectionStudyProgress.setAbilityName(abilityMap.get(x.getChapterId()));
            return courseSectionStudyProgress;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> personCourseListMapTutor(int pageNum, int pageSize, Integer businessType, List<String> currentMemberId, Optional<Integer> findStudy, Optional<String> name, Optional<Integer> finishStatus, Optional<Integer> isRequired, Optional<String> studyTimeOrder, boolean pageSwitch, Optional<Long> startTime, Optional<Long> endTime, Optional<List<String>> courseIds) {
        TableImpl<?> table = courseCacheServiceSupport.getCacheTable(Optional.ofNullable(currentMemberId).orElse(new ArrayList<String>(){{add("0");}}).get(0), SplitTableConfig.COURSE_STUDY_PROGRESS);

        Condition studyProgressCondition = Stream.of(
                courseIds.map(COURSE_INFO.ID::in),
                name.map(COURSE_INFO.NAME::contains),
                finishStatus.map(s -> {
                    if (s == CourseStudyProgress.FINISH_STATUS_FINISH) {
                        return table.field("f_finish_status", Integer.class).in(CourseStudyProgress.FINISH_STATUS_FINISH, CourseStudyProgress.FINISH_STATUS_MARKSUCCESS);
                    }
                    return table.field("f_finish_status", Integer.class).eq(s);
                }),
                isRequired.map(table.field("f_is_required", Integer.class)::eq),
                startTime.map(table.field("f_begin_time", Long.class)::ge),
                endTime.map(table.field("f_begin_time", Long.class)::le),
                findStudy.map(study -> table.field("f_finish_status", Integer.class).ne(CourseStudyProgress.FINISH_STATUS_GIVEUP))
        ).filter(Optional::isPresent).map(Optional::get).reduce((acc, item) -> acc.and(item)).orElse(DSL.trueCondition());

        SelectConditionStep<Record> studyProgressStep = courseStudyProgressCommonDao.execute(e ->
                e.select(Fields.start()
                                .add(
                                        table.field("f_id", String.class).as("id"),
                                        table.field("f_finish_status", Integer.class).as("finish_status"),
                                        table.field("f_finish_time", Long.class).as("finish_time"),
                                        table.field("f_register_time", Long.class).as("register_time"),
                                        table.field("f_study_total_time", Integer.class).as("study_total_time"),
                                        table.field("f_last_access_time", Long.class).as("last_access_time"),
                                        table.field("f_course_id", String.class).as("course_id"),
                                        table.field("f_is_required", Integer.class).as("is_required"),
                                        table.field("f_begin_time", Long.class).as("begin_time"),
                                        table.field("f_type", Integer.class).as("type"),
                                        table.field("f_member_id", String.class).as("member_id")
                                )
                                .add(
                                        COURSE_INFO.ID,
                                        COURSE_INFO.NAME,
                                        COURSE_INFO.COVER,
                                        COURSE_INFO.COVER_PATH,
                                        COURSE_INFO.URL,
                                        COURSE_INFO.VERSION_ID,
                                        COURSE_INFO.STATUS,
                                        COURSE_INFO.STYLES,
                                        COURSE_INFO.LEARN_SEQUENCE,
                                        COURSE_INFO.PUBLISH_CLIENT,
                                        COURSE_INFO.EXPLICIT_LEARNING_STATUS)
                                .end())
                        .from(table)
                        .leftJoin(COURSE_INFO).on(table.field("f_course_id", String.class).eq(COURSE_INFO.ID))
                        .where(studyProgressCondition).and(table.field("f_member_id" ,String.class).in(currentMemberId))
                        .and(COURSE_INFO.BUSINESS_TYPE.eq(businessType))
                        .and(COURSE_INFO.STATUS.ne(CourseInfo.STATUS_FIVE_SHELVES)));

        final SelectOrderByStep<Record> stepFinal = studyProgressStep;
        long count = 0L;
        if (pageSwitch){
            count =  stepFinal.fetch().stream().count();
        }
        int firstResult = (pageNum - 1) * pageSize;
        //排序字段
        SortField<?> sf = studyTimeOrder.map(order ->
                        "asc".equals(order) ? DSL.val(6).asc() : DSL.val(6).desc())
                .orElse(DSL.val(6).desc());


        Result<Record> result =stepFinal.orderBy(sf).limit(firstResult, pageSize + 1).fetch();

        List<CourseStudyProgress> progressList = result.map(r -> {
            CourseStudyProgress progress = new CourseStudyProgress();
            progress.setId(r.getValue("id") != null ? r.getValue("id").toString() : "");
            progress.setFinishStatus(r.getValue("finish_status") != null ? Integer.parseInt(r.getValue("finish_status").toString()) : 0);
            progress.setFinishTime(r.getValue("finish_time") != null ? Long.parseLong(r.getValue("finish_time").toString()) : null);
            progress.setRegisterTime(r.getValue("register_time") != null ? Long.parseLong(r.getValue("register_time").toString()) : null);
            progress.setStudyTotalTime(r.getValue("study_total_time") != null ? Integer.parseInt(r.getValue("study_total_time").toString()) : 0);
            progress.setLastAccessTime(r.getValue("last_access_time") != null ? Long.parseLong(r.getValue("last_access_time").toString()) : null);
            progress.setCourseId(r.getValue("course_id") != null ? r.getValue("course_id").toString() : "");
            progress.setIsRequired(r.getValue("is_required") != null ? Integer.parseInt(r.getValue("is_required").toString().toString()) : 0);
            progress.setBeginTime(r.getValue("begin_time") != null ? Long.parseLong(r.getValue("begin_time").toString()) : null);
            progress.setType(r.getValue("type") != null ? Integer.parseInt(r.getValue("type").toString().toString()) : 0);
            progress.setMemberId(r.getValue("member_id") != null ? r.getValue("member_id").toString() : "");
            return progress;
        });
        List<CourseInfo> infos = result.into(COURSE_INFO).into(CourseInfo.class);
        IntStream.range(0, progressList.size()).forEach(index->
                progressList.get(index).setCourseInfo(infos.get(index))
        );

        Integer more = 0;
        if(Objects.nonNull(progressList)) {
            if(pageSize < progressList.size()) {
                more = 1;
                progressList.remove(pageSize);
            }
        }
        Map<String, Object> map = new HashMap<>();
        map.put("items", progressList);
        if (pageSwitch){
            map.put("recordCount", count);
        }
        map.put("more",more);
        return map;
    }



    @Override
    public PagedResult<CourseSectionStudyProgress> subjectSectionProgress(String memberId, String subjectId, Integer page, Integer pageSize) {
        //查询章节信息
        Integer count = sectionDao.execute(e -> e.select(DSL.count(COURSE_CHAPTER_SECTION.ID))
                .from(COURSE_CHAPTER_SECTION)
                .innerJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                .innerJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID), COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID))
                .where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(subjectId))
                .fetchOne(DSL.count(COURSE_CHAPTER_SECTION.ID)));
        List<CourseChapterSection> sectionList = sectionDao.execute(e -> e.select(Fields.start().add(COURSE_CHAPTER_SECTION.ID,COURSE_CHAPTER_SECTION.RESOURCE_ID, COURSE_CHAPTER_SECTION.SECTION_TYPE,
                        COURSE_CHAPTER_SECTION.CHAPTER_ID,
                        COURSE_CHAPTER_SECTION.NAME, COURSE_CHAPTER_SECTION.SEQUENCE,
                        COURSE_CHAPTER_SECTION.REQUIRED).end())
                .from(COURSE_CHAPTER_SECTION)
                .innerJoin(COURSE_CHAPTER).on(COURSE_CHAPTER_SECTION.CHAPTER_ID.eq(COURSE_CHAPTER.ID))
                .innerJoin(COURSE_INFO).on(COURSE_INFO.ID.eq(COURSE_CHAPTER.COURSE_ID), COURSE_INFO.VERSION_ID.eq(COURSE_CHAPTER.VERSION_ID))
                .where(COURSE_CHAPTER_SECTION.COURSE_ID.eq(subjectId))
                .limit((page - 1) * pageSize, pageSize)
                .fetch().into(COURSE_CHAPTER_SECTION).into(CourseChapterSection.class));
        log.info("SubjectProcessServiceSupport.subjectSectionProgress ===> memberId:{}, subjectId:{}， sectionList:{}", memberId, subjectId, sectionList);
        if (CollectionUtils.isEmpty(sectionList)) {
            return PagedResult.create(0, Collections.emptyList());
        }

        List<String> resourceIds = new ArrayList<>(sectionList.size());
        Map<String, CourseChapterSection> sectionMap = new HashMap<>(sectionList.size());
        for (CourseChapterSection courseChapterSection : sectionList) {
            String resourceId = courseChapterSection.getResourceId();
            if (!StringUtils.isBlank(resourceId)) {
                resourceIds.add(resourceId);
                sectionMap.put(resourceId, courseChapterSection);
            }
        }

        List<CourseSectionStudyProgress> allProgressList = new ArrayList<>();
        //查询当前人的章节学习记录（不包含课程进度，因为课程进度不会写入到这张表）
        TableImpl<?> csspTable = CourseSectionStudyProgressUtil.getTable(memberId, subjectId);
        List<CourseSectionStudyProgress> progressWithoutCourseList = courseSectionStudyProgressCommonDao.execute(e -> e.select(Fields.start()
                        .add(csspTable.fields()).add(COURSE_CHAPTER_SECTION.SECTION_TYPE).add(COURSE_CHAPTER_SECTION.ID).add(COURSE_CHAPTER_SECTION.RESOURCE_ID).end())
                .from(csspTable)
                .leftJoin(COURSE_CHAPTER_SECTION).on(csspTable.field("f_section_id", String.class).eq(COURSE_CHAPTER_SECTION.ID))
                .where(csspTable.field("f_course_id", String.class).eq(subjectId),csspTable.field("f_member_id", String.class).eq(memberId))
                .fetch(r-> {
                    CourseSectionStudyProgress cc= new CourseSectionStudyProgress();
                    cc.setId(r.getValue(csspTable.field("f_id",String.class)));
                    cc.setCourseId(r.getValue(csspTable.field("f_course_id",String.class)));
                    cc.setMemberId(r.getValue(csspTable.field("f_member_id",String.class)));
                    cc.setBeginTime(r.getValue(csspTable.field("f_begin_time",Long.class)));
                    cc.setFinishStatus(r.getValue(csspTable.field("f_finish_status",Integer.class)));
                    cc.setFinishTime(r.getValue(csspTable.field("f_finish_time",Long.class)));
                    cc.setStudyTotalTime(r.getValue(csspTable.field("f_study_total_time",Integer.class)));
                    cc.setScore(r.getValue(csspTable.field("f_score",Integer.class)));

                    cc.setSectionType(r.getValue(COURSE_CHAPTER_SECTION.SECTION_TYPE));
                    cc.setSectionId(r.getValue(COURSE_CHAPTER_SECTION.ID));
                    cc.setResourceId(r.getValue(COURSE_CHAPTER_SECTION.RESOURCE_ID));
                    return cc;
                }));
       log.info("SubjectProcessServiceSupport.subjectSectionProgress ===> progressWithoutCourseList:{}", progressWithoutCourseList);

        for (CourseSectionStudyProgress progress : progressWithoutCourseList) {
            //正常来说，课程的进度不会进到COURSE_SECTION_STUDY_PROGRESS表，这里再做个兼容处理处理，fix：zxy-2812
            if (!Objects.equals(CourseChapterSection.SECTION_TYPE_COURSE, progress.getSectionType())) {
                allProgressList.add(progress);
            }
        }

        //查询当前人的章节学习记录（只查课程进度）
        List<CourseStudyProgress> courseStudyProgressList = courseStudyProgressCommonDao.execute(e -> e.select(Fields.start()
                        .add(COURSE_STUDY_PROGRESS.ID,COURSE_STUDY_PROGRESS.FINISH_STATUS,COURSE_STUDY_PROGRESS.FINISH_TIME,COURSE_STUDY_PROGRESS.BEGIN_TIME,
                                COURSE_STUDY_PROGRESS.COURSE_ID,COURSE_STUDY_PROGRESS.MEMBER_ID, COURSE_STUDY_PROGRESS.COMPLETED_RATE,
                                COURSE_STUDY_PROGRESS.STUDY_TOTAL_TIME,COURSE_STUDY_PROGRESS.LAST_ACCESS_TIME).end())
                .from(COURSE_STUDY_PROGRESS)
                .where(COURSE_STUDY_PROGRESS.COURSE_ID.in(resourceIds), COURSE_STUDY_PROGRESS.MEMBER_ID.eq(memberId))
                .fetch().into(COURSE_STUDY_PROGRESS).into(CourseStudyProgress.class));
        log.info("SubjectProcessServiceSupport.subjectSectionProgress ===> courseStudyProgressList:{}", courseStudyProgressList);

        for (CourseStudyProgress x : courseStudyProgressList) {
            CourseSectionStudyProgress progress = new CourseSectionStudyProgress();

            progress.setId(x.getId());
            progress.setFinishStatus(x.getFinishStatus());
            progress.setSectionType(CourseChapterSection.SECTION_TYPE_COURSE);
            progress.setStudyTotalTime(x.getStudyTotalTime());
            progress.setBeginTime(x.getBeginTime());
            progress.setResourceId(x.getCourseId());
            progress.setCourseId(x.getCourseId());
            progress.setMemberId(x.getMemberId());
            progress.setFinishTime(x.getFinishTime());
            if (x.getFinishStatus() != null && Integer.valueOf(CourseStudyProgress.FINISH_STATUS_MARKSUCCESS).equals(x.getFinishStatus())) {
                progress.setCompletedRate(CommonConstant.ONE_HUNDRED);
            } else {
                progress.setCompletedRate(x.getCompletedRate());
            }
            progress.setLastAccessTime(x.getLastAccessTime());

            allProgressList.add(progress);
        }

        Map<String, CourseSectionStudyProgress> progressMap = allProgressList.stream().collect(Collectors.toMap(CourseSectionStudyProgress::getResourceId,x->x,(a,b)->a));
        Map<String, String> abilityMap = abilityDao.execute(x -> x.select(COURSE_CHAPTER.ID, ABILITY.NAME)
                .from(COURSE_CHAPTER)
                .innerJoin(ABILITY).on(COURSE_CHAPTER.ABILITY_ID.eq(ABILITY.ID))
                .where(COURSE_CHAPTER.ID.in(sectionList.stream().map(CourseChapterSection::getChapterId).filter(Objects::nonNull)
                        .distinct().collect(Collectors.toList())))
                .fetchMap(COURSE_CHAPTER.ID, ABILITY.NAME));


        return PagedResult.create(count,sectionList.stream().map(x -> {
            CourseSectionStudyProgress courseSectionStudyProgress = progressMap.getOrDefault(x.getResourceId(), new CourseSectionStudyProgress());
            courseSectionStudyProgress.setFinishStatus(
                    Optional.ofNullable(courseSectionStudyProgress.getFinishStatus())
                            .orElse(CourseSectionStudyProgress.FINISH_STATUS_UNSTART)
            );
            courseSectionStudyProgress.setSectionType(x.getSectionType());
            courseSectionStudyProgress.setSectionName(x.getName());
            courseSectionStudyProgress.setRequired(x.getRequired());
            courseSectionStudyProgress.setAbilityName(abilityMap.get(x.getChapterId()));
            return courseSectionStudyProgress;
        }).collect(Collectors.toList()));
    }

    @Override
    public void updateConcentrateStudyHours(List<String> courseIds, String subjectId, Map<String, Integer> sectionIdAndConcentrateHoursMap) {

        if (paramVerify(courseIds, subjectId)) return;

        sectionIdAndConcentrateHoursMap = Optional.ofNullable(sectionIdAndConcentrateHoursMap).orElse(Collections.emptyMap());

        List<TableImpl<?>> progressTalbeNameList = SplitTableName.getProgressTalbeNameList();

        for (TableImpl<?> table : progressTalbeNameList) {

            log.info("更新专题的集中学时 - " + table.getName());

            // 查询专题在分表中有哪些人学习
            List<String> memberIds = getMemberIdsBySubjectId(subjectId, table);

            if (memberIds == null || memberIds.isEmpty()) {
                log.info("专题下没有学习人员 - " + table.getName());
                continue;
            }

            // 查询分表中人员-专题下课程的学习情况
            List<CourseStudyProgress> progressList = getProgressList(memberIds, courseIds, table);

            if (progressList == null || progressList.isEmpty()) {
                log.info("分表 {} 中未找到指定专题的学习进度数据，跳过。", table.getName());
                continue;
            }

            Map<String, List<CourseStudyProgress>> courseIdAndCourseStudyProgressList = filterByStatus(progressList);

            batchHandleCourseProgress(sectionIdAndConcentrateHoursMap, table, courseIdAndCourseStudyProgressList);
        };
    }

    private void batchHandleCourseProgress(Map<String, Integer> sectionIdAndConcentrateHoursMap, TableImpl<?> table, Map<String, List<CourseStudyProgress>> courseIdAndCourseStudyProgressList) {
        for (Map.Entry<String, List<CourseStudyProgress>> entry : courseIdAndCourseStudyProgressList.entrySet()) {
            String courseId = entry.getKey();

            if (courseId == null) {
                continue;
            }

            Integer concentrate = sectionIdAndConcentrateHoursMap.getOrDefault(courseId, 0);

            List<String> progressIds = filterAndMap(entry);

            if (progressIds.isEmpty()) {
                log.info("专题 {} 在分表 {} 中无有效进度记录，跳过。", courseId, table.getName());
                continue;
            }

            log.info("更新专题 [{}] 在分表 [{}] 中共 {} 条记录的集中学时为 {}。", courseId, table.getName(), progressIds.size(), concentrate);
            batchUpdateConcentrateStudyHours(table, progressIds, concentrate);
        }
        ;
    }

    private List<String> filterAndMap(Map.Entry<String, List<CourseStudyProgress>> entry) {
        return entry.getValue().stream()
                    .filter(Objects::nonNull)
                    .map(CourseStudyProgress::getId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
    }

    private boolean paramVerify(List<String> courseIds, String subjectId) {
        if (courseIds == null || courseIds.isEmpty()) {
            log.error("集中学时 - 课程ID列表为空，跳过更新。");
            return true;
        }
        if (subjectId == null || subjectId.isEmpty()) {
            log.error("集中学时 -  subjectId为空，跳过更新。");
            return true;
        }
        return false;
    }

    @Override
    public void updateConcentrateStudyHoursByMember(String memberId, List<String> courseIds,
                                                    String subjectId, Map<String, Integer> sectionIdAndConcentrateHoursMap) {

        if (paramVerify(courseIds, subjectId)) return;

        if (memberId == null || memberId.isEmpty()){
            return;
        }

        sectionIdAndConcentrateHoursMap = Optional.ofNullable(sectionIdAndConcentrateHoursMap).orElse(Collections.emptyMap());

        TableImpl<?> table = courseCacheServiceSupport.getCacheTable(memberId, SplitTableConfig.COURSE_STUDY_PROGRESS);

        // 查询分表中人员-专题下课程的学习情况
        List<CourseStudyProgress> progressList = getProgressList(Collections.singletonList(memberId), courseIds, table);

        if (progressList == null || progressList.isEmpty()) {
            log.info("分表 {} 中未找到指定课程的学习进度数据，跳过。", table.getName());
            return;
        }

        Map<String, List<CourseStudyProgress>> courseIdAndCourseStudyProgressList = filterByStatus(progressList);

        batchHandleCourseProgress(sectionIdAndConcentrateHoursMap, table, courseIdAndCourseStudyProgressList);
    }

    private void batchUpdateConcentrateStudyHours(TableImpl<?> table, List<String> ids, int concentrate) {
        if (ids == null || ids.isEmpty()){
            return;
        }
        final int BATCH_SIZE = 50;
        for (int i = 0; i < ids.size(); i += BATCH_SIZE) {
            List<String> batch = ids.subList(i, Math.min(i + BATCH_SIZE, ids.size()));
            try {
                int updated = courseStudyProgressCommonDao.execute(dsl ->
                                                                           dsl.update(table)
                                                                              .set(table.field("f_study_hours_record_time", Long.class), System.currentTimeMillis())
                                                                              .set(table.field("f_concentrate_study_hours", Integer.class), concentrate)
                                                                              .where(table.field("f_id", String.class).in(batch))
                                                                              .execute()
                );
                log.debug("更新表 {}：批次 {} ~ {}，更新成功 {} 条记录。", table.getName(), i, i + batch.size() - 1, updated);
            } catch (Exception e) {
                log.error("更新表 {} 批次 {} 时发生异常：{}", table.getName(), i, e.getMessage(), e);
            }
        }
    }

    private List<String> getMemberIdsBySubjectId(String subjectId, TableImpl<?> table) {
        return courseStudyProgressCommonDao.execute(x -> x.select(Fields.start()
                                                                        .add(table.field("f_member_id", String.class))
                                                                        .end())
                                                          .from(table)
                                                          .where(table.field("f_course_id", String.class).eq(subjectId)))
                                           .fetch(r -> r.getValue(table.field("f_member_id", String.class)));
    }

    private List<CourseStudyProgress> getProgressList(List<String> memberIds, List<String> courseIds, TableImpl<?> table) {
        return courseStudyProgressCommonDao.execute(x -> x.select(Fields.start()
                                                                        .add(table.field("f_member_id", String.class))
                                                                        .add(table.field("f_course_id", String.class))
                                                                        .add(table.field("f_id", String.class))
                                                                        .add(table.field("f_finish_status", Integer.class))
                                                                        .end())
                                                          .from(table)
                                                          .where(table.field("f_course_id", String.class).in(courseIds)
                                                                      .and(table.field("f_member_id", String.class).in(memberIds))))
                                           .fetch(r -> {
                                               CourseStudyProgress progress = new CourseStudyProgress();
                                               progress.setId(r.getValue("f_id", String.class));
                                               progress.setFinishStatus(r.getValue("f_finish_status", Integer.class));
                                               progress.setCourseId(r.getValue("f_course_id", String.class));
                                               progress.setMemberId(r.getValue("f_member_id", String.class));
                                               return progress;
                                           });
    }

    private Map<String, List<CourseStudyProgress>> filterByStatus(List<CourseStudyProgress> progressList) {
        return progressList.stream()
                           .filter(Objects::nonNull)
                           .filter(progress ->
                                           CourseStudyProgress.FINISH_STATUS_FINISH == progress.getFinishStatus() ||
                                                   CourseStudyProgress.FINISH_STATUS_MARKSUCCESS == progress.getFinishStatus())
                           .collect(Collectors.groupingBy(CourseStudyProgress::getCourseId));

    }


}
