spring.application.name=course-study-service
spring.profiles.include = log-level

#spring.datasource.url=**************************************************************
#spring.datasource.url=**************************************************************
#spring.datasource.username=cmudevuser
#spring.datasource.password=DreamtechIT%9
spring.datasource.master.url=***************************************************************
spring.datasource.master.username=root
spring.datasource.master.password=dreamtech%9


#spring.datasource.url=***********************************************************
#spring.datasource.username=root
#spring.datasource.password=
spring.datasource.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.validation-query=SELECT 1
spring.datasource.initial-size=5
spring.datasource.max-active=10
spring.datasource.max-idle=5
spring.datasource.min-idle=1
spring.datasource.test-while-idle=true
spring.datasource.test-on-borrow=true
spring.datasource.time-between-eviction-runs-millis=5000
spring.datasource.min-evictable-idle-time-millis=60000

app.secretKey.sm4=e83d7a1c9b046f25d2c5e789a0b4f67d


spring.datasource.master.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.master.test-while-idle=true
spring.datasource.master.test-on-borrow=true
spring.datasource.master.time-between-eviction-runs-millis=5000
spring.datasource.master.min-evictable-idle-time-millis=60000
spring.datasource.master.validation-query=SELECT 1


spring.datasource.slave.driver-class-name=com.mysql.jdbc.Driver
spring.datasource.slave.test-while-idle=true
spring.datasource.slave.test-on-borrow=true
spring.datasource.slave.time-between-eviction-runs-millis=5000
spring.datasource.slave.min-evictable-idle-time-millis=60000
spring.datasource.slave.validation-query=SELECT 1



spring.jooq.sql-dialect = mysql


dubbo.application.name=course-study-service
#dubbo.registry.address=zookeeper://**************:10501
dubbo.registry.address=zookeeper://127.0.0.1:2181
dubbo.application.version=1
dubbo.registry.username=zk_user
dubbo.registry.password=dreamtech
dubbo.registry.client=curator


spring.rabbitmq.host=*************
spring.rabbitmq.port=30419
spring.rabbitmq.username=guest
spring.rabbitmq.password=guest
spring.rabbitmq.virtual-host=/
spring.rabbitmq.default-exchange=amq.direct
spring.rabbitmq.listener.simple.prefetch = 1
spring.rabbitmq.listener.simple.concurrency = 1
spring.rabbitmq.listener.simple.max-concurrency = 1

#graphite.server=**************
graphite.server=**************
graphite.port=30004

dubbo.protocol.port = 20883

# redis
#spring.redis.cluster.nodes = mw9.zhixueyun.com:10301
spring.redis.cluster = false
spring.redis.timeout = 10000
spring.redis.cluster.nodes = *************:30016
spring.redis.password = TA6sMiuSRqtTrHB7Amdg
spring.redis.connection-timeout = 2000
spring.redis.max-attempts = 3

spring.data.mongodb.host = *************
spring.data.mongodb.port = 27017
spring.data.mongodb.dbname = cmu_homecfg
spring.data.mongodb.connectTimeout = 60000
spring.data.mongodb.socketTimeout = 120000
spring.data.mongodb.socketKeepAlive = true
spring.data.mongodb.maxConnectionIdleTime = 120000
spring.data.mongodb.maxConnectionLifeTime = 120000


# jedis pool
spring.jedis.max-total=8
spring.jedis.max-idle=8
spring.jedis.block-when-exhausted=true
spring.jedis.max-wait-millis=-1

org.releated.tables=t_course_info,f_organization_id,false,40601|t_gensee_web_cast,f_organization_id,false,40602|\
t_study_push_info,f_organization_id,false,40603|t_course_category,f_organization_id,false,40604

course.time.limit=00:00:00-23:59:59
subject.time.limit=00:00:00-23:59:59
#\u5B81\u590F 1012774
course.organization.id =
# \u6D59\u6C5F
subject.organization.id = 80307
subject.log.day.repair.time.limit=00:00:00-20:50:00

organization.province.ids=96493,1105092,1567690,1011203,98209,1093623,1061451,1073360,44261,1754249,1148390,80307,552512,1093072,567498,1180849,55836,1018307,105408,1073923,993478,1013550,1095592,1495996,1495590,1094794,1133482,1012970,1322085,1100282,1012774,1062701,284519
#organization.province.ids=96493,1105092,1567690,1011203,98209,1093623,1061451,1073360,44261,1754249,1148390,80307,552512,1093072,567498,1180849,55836,1018307,105408,1073923,993478,1013550,1095592,1495996,1495590,1094794,1133482,1012970,1322085,1100282,1012774,1062701,5955a059-3e16-44ca-a3c9-3aabfeb8024b
organization.other.ids=184091188,45229480,110918047,155132038,152603244,172712787,156426232,156426227,172762630,168524524,170630482,7347731c-139b-443b-9a89-14a1c20a0421,d72f2b95-7e7f-402f-a10c-0f64197f5352,79ee93e8-ff47-435c-b950-7728275d9cfa,a55b7b32-ed0c-47fe-8e8c-c799caa4adb5,173586065,96674,96597,79495171,140075687,155242868,3770796
#organization.other.ids=184091188,45229480,52b75271-c33e-4f3f-9470-a8a11bfc0c1e,155132038,152603244,172712787,156426232,156426227,172762630,168524524,170630482,9acf51aa-a12f-4108-8952-5ba936d2fbf6,af94df99-dc42-4361-b51c-1f4cbb64fa99,79ee93e8-ff47-435c-b950-7728275d9cfa,a55b7b32-ed0c-47fe-8e8c-c799caa4adb5,173586065,96674,96597,79495171,140075687,155242868,3770796
hot.visit.rank.start.timestamp=1577808000000
# ????aiapi.wangda.chinamobile.com      ?? video.wangda.chinamobile.com
short.video.domain=aiapi.wangda.chinamobile.com

#ishow
ishow.http.host = https://gxpt.ge.chinamobile.com:18980/ishow/prod-api/wangda/gethistory
ishow.user.secret = a19de0eb80013fb468fd2ee0cc672211

course.model.url: http://exam.cmiivip.com
course.model.dataset-id:c2e4f95b-0518-4fb9-bea9-d4d8c1dceefb
course.model.app-api-key: Bearer app-9KIvY4ycYBL7PtIhdhKYk9fi
course.model.dataset-api-key:dataset-wyJ5dGtiGiMsqk7nrg0Etve2
course.model.interface.create-file = /v1/datasets/%s/document/create_by_file
course.model.interface.delete-file = /v1/datasets/%s/documents/%s


