<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>com.zxy.product</groupId>
		<artifactId>course-study</artifactId>
		<version>cmu-9.6.0</version>
	</parent>
	<artifactId>course-study-service</artifactId>
	<dependencies>
		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>service-parent</artifactId>
			<type>pom</type>
			<exclusions>
				<exclusion>
					<artifactId>curator-framework</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<artifactId>curator-client</artifactId>
					<groupId>org.apache.curator</groupId>
				</exclusion>
				<exclusion>
					<groupId>redis.clients</groupId>
					<artifactId>jedis</artifactId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>2.9.3</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>course-study-api</artifactId>
			<version>${version}</version>
		</dependency>
		<!--		<dependency>-->
		<!--			<groupId>com.zxy.product</groupId>-->
		<!--			<artifactId>zxy-log-api</artifactId>-->
		<!--			<version>${version}</version>-->
		<!--		</dependency>-->
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>history-api</artifactId>
			<version>${version}</version>
		</dependency>
		<dependency>
			<artifactId>spring-boot-starter</artifactId>
			<groupId>org.springframework.boot</groupId>
		</dependency>
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>1.2.17</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-test</artifactId>
			<scope>test</scope>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-test</artifactId>
		</dependency>
		<dependency>
			<groupId>org.aspectj</groupId>
			<artifactId>aspectjweaver</artifactId>
			<version>1.8.13</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>common-distributed</artifactId>
			<version>0.0.3</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>
        <dependency>
            <groupId>com.zxy.product</groupId>
            <artifactId>system-api</artifactId>
            <version>${version}</version>
            <scope>compile</scope>
        </dependency>
		<dependency>
			<groupId>com.zxy.product</groupId>
			<artifactId>exam-api</artifactId>
			<version>${version}</version>
		</dependency>

		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.15</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-scratchpad</artifactId>
			<version>3.15</version>
		</dependency>
		<dependency>
			<groupId>com.zxy.common</groupId>
			<artifactId>common-encrypt</artifactId>
			<version>CMU-0.1.0-SNAPSHOT</version>
		</dependency>
    </dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<dependencies>
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>springloaded</artifactId>
						<version>1.2.3.RELEASE</version>
					</dependency>
				</dependencies>
			</plugin>
		</plugins>
	</build>
</project>