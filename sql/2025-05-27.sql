CREATE TABLE `course-study`.`t_course_knowledge` (
                                                     `f_id` VARCHAR(40) NOT NULL,
                                                     `f_name` VARCHAR(200) DEFAULT NULL COMMENT '知识名称',
                                                     `f_attachment_id` VARCHAR(40) DEFAULT NULL COMMENT '附件id',
                                                     `f_attachment_name` VARCHAR(100) DEFAULT NULL COMMENT '附件名称',
                                                     `f_attachment_type` TINYINT(4) DEFAULT NULL COMMENT '0 word 1 pdf',
                                                     `f_attachment_up_id` VARCHAR(40) DEFAULT NULL COMMENT '上传到成研的附件id',
                                                     `f_attachment_batch` VARCHAR(40) DEFAULT NULL COMMENT '上传到成研的附件批次号',
                                                     `f_status` TINYINT(4) DEFAULT 0 COMMENT '状态 0 未发布 1 已发布',
                                                     `f_delete` TINYINT(4) DEFAULT 0 COMMENT '是否删除 0 未删除 1 已删除',
                                                     `f_finsh_status` TINYINT(4) DEFAULT 0 COMMENT '向量化状态 0 未完成 1已完成',
                                                     `f_knowledge` VARCHAR(1000) DEFAULT NULL COMMENT '知识简介',
                                                     `f_knowledge_text` VARCHAR(1000) DEFAULT NULL COMMENT '知识简介文本',
                                                     `f_shelve_time` BIGINT(20) DEFAULT NULL COMMENT '首次发布时间',
                                                     `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                                     `f_modify_date` BIGINT(20) DEFAULT NULL COMMENT '更新时间',
                                                     PRIMARY KEY (`f_id`),
                                                     KEY `idx_f_attachment_id` (`f_attachment_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='学习助手知识表';
CREATE TABLE `course-study`.`t_course_question_recommend` (
                                                              `f_id` VARCHAR(40) NOT NULL,
                                                              `f_question` VARCHAR(100) DEFAULT NULL COMMENT '问题',
                                                              `f_answer` VARCHAR(1000) DEFAULT NULL COMMENT '答案',
                                                              `f_question_third_id` VARCHAR(1000) DEFAULT NULL COMMENT '第三方问题ID',
                                                              `f_answer_text` VARCHAR(1000) DEFAULT NULL COMMENT '答案文本',
                                                              `f_recommend` TINYINT(4) DEFAULT 0 COMMENT '是否为推荐问题 0 否 1 是',
                                                              `f_status` TINYINT(4) DEFAULT 0 COMMENT '状态 0 未发布 1 已发布',
                                                              `f_sort` INT(11) DEFAULT NULL COMMENT '排序',
                                                              `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                                              `f_modify_date` BIGINT(20) DEFAULT NULL COMMENT '更新时间',
                                                              PRIMARY KEY (`f_id`),
                                                              KEY `idx_f_question_third_id` (`f_question_third_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='推荐问题管理表';
CREATE TABLE `course-study`.`t_course_feedback` (
                                     `f_id` varchar(40) NOT NULL,
                                     `f_question_id` varchar(100) DEFAULT NULL COMMENT '问题id',
                                     `f_question_content` varchar(500) DEFAULT NULL COMMENT '问题内容',
                                     `f_answer_content` text DEFAULT NULL,
                                     `f_feedback_content` varchar(300) DEFAULT NULL COMMENT '反馈内容',
                                     `f_feedback_type` tinyint(4) DEFAULT 0 COMMENT '反馈类型 0 内容不全面、1观点有错误、2其他',
                                     `f_like` tinyint(4) DEFAULT NULL COMMENT '0 点踩 1 点赞',
                                     `f_create_member_id` varchar(40) DEFAULT NULL COMMENT '创建人',
                                     `f_create_time` bigint(20) DEFAULT NULL COMMENT '创建时间',
                                     `f_modify_date` bigint(20) DEFAULT NULL COMMENT '更新时间',
                                     PRIMARY KEY (`f_id`),
                                     KEY `idx_f_question_id` (`f_question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='意见反馈';


CREATE TABLE `course-study`.`t_chat_time_record` (
                                                     `f_id` VARCHAR(40) NOT NULL,
                                                     `f_answer_id` VARCHAR(100) DEFAULT NULL COMMENT '回答id，第三方主键ID',
                                                     `f_time` BIGINT(20) DEFAULT NULL COMMENT '思考时间 时间毫秒',
                                                     `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                                     `f_modify_date` BIGINT(20) DEFAULT NULL COMMENT '更新时间',
                                                     PRIMARY KEY (`f_id`),
                                                     KEY `idx_f_f_answer_id` (`f_answer_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='回答问题时间差';
CREATE TABLE `system`.`t_chat_detail` (
                                          `f_id` VARCHAR(40) NOT NULL COMMENT 'ID',
                                          `f_member_id` VARCHAR(40) DEFAULT NULL COMMENT '用户id',
                                          `f_result_id` VARCHAR(40) DEFAULT NULL COMMENT '用户对话总和表id',
                                          `f_business_type` TINYINT(4) DEFAULT NULL COMMENT '业务类型 0对话 1智能应用 3 推荐问题 4 跟随对话',
                                          `f_from_id` VARCHAR(40) NULL COMMENT '智能应用来源id',
                                          `f_business_id` VARCHAR(40) DEFAULT NULL COMMENT '业务对象id',
                                          `f_text_size` INT(11) DEFAULT 0 COMMENT '对话字数',
                                          `f_detail` VARCHAR(500) DEFAULT NULL COMMENT '详细记录',
                                          `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                          `f_modify_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '修改时间',
                                          PRIMARY KEY (`f_id`),
                                          KEY `idx_chat_detail_member_id` (`f_member_id`),
                                          KEY `idx_chat_detail_mcs` (`f_member_id`,`f_create_time`,`f_text_size`),
                                          KEY `idx_modify_date` (`f_modify_date`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='用户对话详情表';

CREATE TABLE `system`.`t_chat_rank_day` (
                                            `f_id` VARCHAR(40) NOT NULL COMMENT '主键id',
                                            `f_member_id` VARCHAR(40) DEFAULT NULL COMMENT '人员id',
                                            `f_times` BIGINT(20) DEFAULT NULL COMMENT '次数',
                                            `f_number` BIGINT(20) DEFAULT NULL COMMENT '人数',
                                            `f_text_size` BIGINT(20) DEFAULT NULL COMMENT '字数',
                                            `f_app_times` BIGINT(20) DEFAULT NULL COMMENT '智能应用次数',
                                            `f_app_number` BIGINT(20) DEFAULT NULL COMMENT '智能应用人数',
                                            `f_recommend_times` BIGINT(20) DEFAULT NULL COMMENT '推荐问题点击次数',
                                            `f_follow_conversation_times` BIGINT(20) DEFAULT NULL COMMENT '跟随对话点击次数',
                                            `f_root_organization_id` VARCHAR(40) DEFAULT NULL COMMENT '根组织ID',
                                            `f_day` INT(11) DEFAULT NULL COMMENT '天',
                                            `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                            `f_modify_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '修改时间',
                                            PRIMARY KEY (`f_id`),
                                            KEY `idx_chat_rank_day_member_id` (`f_member_id`,`f_day`) USING BTREE,
                                            KEY `idx_modify_date` (`f_modify_date`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='用户对话天表';
CREATE TABLE `system`.`t_chat_rank_month` (
                                              `f_id` VARCHAR(40) NOT NULL COMMENT '主键id',
                                              `f_member_id` VARCHAR(40) DEFAULT NULL COMMENT '人员id',
                                              `f_times` BIGINT(20) DEFAULT NULL COMMENT '次数',
                                              `f_number` BIGINT(20) DEFAULT NULL COMMENT '人数',
                                              `f_text_size` BIGINT(20) DEFAULT NULL COMMENT '字数',
                                              `f_app_times` BIGINT(20) DEFAULT NULL COMMENT '智能应用次数',
                                              `f_app_number` BIGINT(20) DEFAULT NULL COMMENT '智能应用人数',
                                              `f_recommend_times` BIGINT(20) DEFAULT NULL COMMENT '推荐问题点击次数',
                                              `f_follow_conversation_times` BIGINT(20) DEFAULT NULL COMMENT '跟随对话点击次数',
                                              `f_root_organization_id` VARCHAR(40) DEFAULT NULL COMMENT '根组织ID',
                                              `f_month` INT(11) DEFAULT NULL COMMENT '月',
                                              `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                              `f_modify_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '修改时间',
                                              PRIMARY KEY (`f_id`),
                                              KEY `idx_chat_rank_month_month` (`f_month`) USING BTREE,
                                              KEY `idx_chat_rank_month_member_id` (`f_member_id`,`f_month`,`f_create_time`) USING BTREE,
                                              KEY `idx_modify_date` (`f_modify_date`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='用户对话月表';
CREATE TABLE `system`.`t_chat_rank_week` (
                                             `f_id` VARCHAR(40) NOT NULL COMMENT '主键id',
                                             `f_member_id` VARCHAR(40) DEFAULT NULL COMMENT '人员id',
                                             `f_times` BIGINT(20) DEFAULT NULL COMMENT '次数',
                                             `f_number` BIGINT(20) DEFAULT NULL COMMENT '人数',
                                             `f_text_size` BIGINT(20) DEFAULT NULL COMMENT '字数',
                                             `f_app_times` BIGINT(20) DEFAULT NULL COMMENT '智能应用次数',
                                             `f_app_number` BIGINT(20) DEFAULT NULL COMMENT '智能应用人数',
                                             `f_recommend_times` BIGINT(20) DEFAULT NULL COMMENT '推荐问题点击次数',
                                             `f_follow_conversation_times` BIGINT(20) DEFAULT NULL COMMENT '跟随对话点击次数',
                                             `f_root_organization_id` VARCHAR(40) DEFAULT NULL COMMENT '根组织ID',
                                             `f_week` INT(11) DEFAULT NULL COMMENT '周',
                                             `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                             `f_modify_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '修改时间',
                                             PRIMARY KEY (`f_id`),
                                             KEY `idx_chat_rank_week_year` (`f_work`) USING BTREE,
                                             KEY `idx_modify_date` (`f_modify_date`),
                                             KEY `idx_chat_rank_week_create_time` (`f_work`,`f_create_time`),
                                             KEY `idx_chat_rank_week_mys` (`f_member_id`,`f_work`),
                                             KEY `idx_chat_rank_week_member_id` (`f_member_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='用户对话周表';

CREATE TABLE `system`.`t_app_rank_month` (
                                             `f_id` VARCHAR(40) NOT NULL COMMENT '主键id',
                                             `f_from` TINYINT(4) NOT NULL COMMENT '智能应用来源 0 智能标签，1岗位画像 2 学情分析 3岗位图谱 4 学习风格 5智能推荐',
                                             `f_app_times` BIGINT(20) DEFAULT NULL COMMENT '智能应用次数',
                                             `f_app_number` BIGINT(20) DEFAULT NULL COMMENT '智能应用人数',
                                             `f_month` INT(11) DEFAULT NULL COMMENT '月',
                                             `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                             `f_modify_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() ON UPDATE CURRENT_TIMESTAMP() COMMENT '修改时间',
                                             PRIMARY KEY (`f_id`),
                                             KEY `idx_app_rank_month_month` (`f_month`) USING BTREE,
                                             KEY `idx_app_rank_month_member_id` (`f_month`,`f_app_times`, `f_app_number`,`f_create_time`) USING BTREE,
                                             KEY `idx_modify_date` (`f_modify_date`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='用户应用月表';

ALTER TABLE `system`.`t_app_rank_month`
    CHANGE `f_from` `f_from_id` VARCHAR(40) NOT NULL COMMENT '智能应用来源Id';
ALTER TABLE `course-study`.`t_course_question_recommend`
    CHANGE `f_answer_text` `f_answer_text` TEXT CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '答案文本';
ALTER TABLE `course-study`.`t_course_question_recommend`
    CHANGE `f_answer` `f_answer` TEXT CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '答案',
    CHANGE `f_question_third_id` `f_question_third_id` VARCHAR(100) CHARSET utf8 COLLATE utf8_general_ci NULL COMMENT '第三方问题ID';


ALTER TABLE `course-study`.`t_course_feedback`
  ADD  KEY `idx_modify_time` (`f_modify_date`) ,
  ADD  KEY `idx_modify_time_content` (`f_modify_date` , `f_question_content`) ,
  ADD  KEY `idx_context_member_create` (`f_question_content` , `f_create_member_id` , `f_create_time`);

UPDATE `system`.t_rule_config SET f_value = '{"isOpen":"1",
"data":[
{"name":"AI助手","color":"","link":"","backgroundImage":"","isShow":TRUE,"type": "PC"},
{"name":"AI助手","color":"","link":"","backgroundImage":"","isShow":TRUE,"type": "APP"},
{"name":"图书","color":"#333333","link":"","backgroundImage":"default/M00/01/E1/Cg4XD2L995yAEiTYAAQUxrFHtIE980.jpg","isShow":TRUE,"type": "PC"},
{"name":"网大宣传片","type": "PC","color":"#ef1313","link":"https://uat.wangda.chinamobile.com/#/study/course/detail/f8d2656e-4bfc-41ae-9308-c146f86f47a0","backgroundImage":"default/M00/00/C7/CuFUjmetUT2AQeaQAAMJ7VDeaTM610.jpg","isShow":TRUE,"title":"添加内容标识"},
{"name":"宝藏功能","type": "PC","color":"#f31616","link":"https://wangda.chinamobile.com/#/ask/question/detail/271915f0-20c4-40e4-937b-29aaac4ed90c","backgroundImage":"default/M00/00/C7/CuFUjmetUUOATas_AABD8iHXjVg911.jpg","isShow":TRUE,"title":"添加内容标识"},
{"name":"免费领流量","type": "PC","color":"#2526e8","link":"https://dev.coc.10086.cn/coc/canvas/package-h5-canvas/online/zhongyiwangda?pageId=1696418506034348032&channelId=P00000010578","backgroundImage":"default/M00/00/4B/CuFUjmc_9FSAC5EwAABJPkp9WvM966.jpg","isShow":TRUE,"title":"添加内容标识"},
{"name":"课程共建","type": "PC","color":"#333333","link":"https://uat.wangda.chinamobile.com/admin/#/course-study/course-info","backgroundImage":"default/M00/01/36/CuFUjmfjWUyAO02bAAALlAmHOwI131.jpg","isShow":TRUE,"title":"添加内容标识"}]}'
WHERE f_key ='BAY_WINDOW_CONFIGURATION';
