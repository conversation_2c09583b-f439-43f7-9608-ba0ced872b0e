ALTER TABLE `course-study`.`t_sub_authenticated`
    ADD COLUMN `f_check_course_process` TINYINT(4) DEFAULT 0 NULL COMMENT '是否校验课程进度 0 是 1否' AFTER `f_check_certificate`;
CREATE TABLE `course-study`.`t_sub_authenticated_tmp` (
                                           `f_id` VARCHAR(40) NOT NULL COMMENT 'id',
                                           `f_name` VARCHAR(100) DEFAULT NULL COMMENT '姓名',
                                           `f_code` VARCHAR(100) DEFAULT NULL COMMENT '编号',
                                           `f_file_id` VARCHAR(40) DEFAULT NULL COMMENT '文件id',
                                           `f_sub_authenticated_id` VARCHAR(40) DEFAULT NULL COMMENT '子认证id',
                                           `f_delete_flag` TINYINT(4) DEFAULT 0 COMMENT '是否删除 0 否 1是',
                                           `f_creator` VARCHAR(40) DEFAULT NULL COMMENT '创建人',
                                           `f_create_time` BIGINT(20) DEFAULT NULL COMMENT '创建时间',
                                           `f_modify_date` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP() COMMENT '修改时间',
                                           PRIMARY KEY (`f_id`)
) ENGINE=INNODB DEFAULT CHARSET=utf8 COMMENT='子认证-维度导入表-临时表';
ALTER TABLE `course-study`.`t_sub_authenticated_certificate_record`
    ADD COLUMN `f_import_time` BIGINT(20) NULL COMMENT '导入时间' AFTER `f_delete_flag`,
	ADD COLUMN `f_status` TINYINT(4) DEFAULT NULL COMMENT '发证状态 0 未发 1已发' AFTER `f_import_time`;

UPDATE `course-study`.t_sub_authenticated_certificate_record SET f_import_time = f_create_time WHERE f_import_time IS NULL;
