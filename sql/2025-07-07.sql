INSERT INTO `system`.`t_message_template` (`f_id`, `f_organization_id`, `f_name`, `f_subject`, `f_type`, `f_module`, `f_content`,
                                  `f_content_desc`, `f_param_num`, `f_param_desc`, `f_status`, `f_create_time`, `f_create_member_id`, `f_last_modify_time`,
                                  `f_last_modify_member_id`, `f_text_content`, `f_code`, `f_seq`, `f_url`, `f_is_default`)
VALUES(UUID(),'1','专题内容修改通知','【专题内容修改通知】','1','2','您好，您创建的专题【{0}】内课程【{1}】，根据网大资源管理要求，于【{2}】已进行退库处理，请知悉。',
       NULL,'1','0专题名称','1',NULL,'1',NULL,NULL,'您好，您创建的专题【{0}】内课程【{1}】，根据网大资源管理要求，于【{2}】已进行退库处理，请知悉。',
       'course_disappear_inner','2399',NULL,'0');
ALTER TABLE `course-study`.`t_course_info`
    ADD COLUMN `f_certificate_time` BIGINT(20) NULL COMMENT '证书日期' AFTER `f_certificate_id`;
ALTER TABLE `course-study`.`t_course_info`
    ADD COLUMN `f_certificate_type` TINYINT(4) NULL COMMENT '0 默认 1自定义时间' AFTER `f_certificate_id`;
ALTER TABLE `course-study`.`t_course_info`
    ADD COLUMN `f_certificate_update_time` BIGINT(20) NULL COMMENT '更新证书时间' AFTER `f_certificate_time`;


-- /api/v1/course-study/certificate-record/manual-fix 修复证书日期
